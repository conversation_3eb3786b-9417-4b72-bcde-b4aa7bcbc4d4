// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateDatabaseReq struct {
	InstanceId       string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	DBName           string `thrift:"DBName,2,required" frugal:"2,required,string" validate:"required,min=2,max=64"`
	CharacterSetName string `thrift:"CharacterSetName,3,required" frugal:"3,required,string" validate:"required"`
	DBDesc           string `thrift:"DBDesc,4" frugal:"4,default,string" json:"DBDesc"`
}

func NewCreateDatabaseReq() *CreateDatabaseReq {
	return &CreateDatabaseReq{}
}

func (p *CreateDatabaseReq) InitDefault() {
}

func (p *CreateDatabaseReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDatabaseReq) GetDBName() (v string) {
	return p.DBName
}

func (p *CreateDatabaseReq) GetCharacterSetName() (v string) {
	return p.CharacterSetName
}

func (p *CreateDatabaseReq) GetDBDesc() (v string) {
	return p.DBDesc
}
func (p *CreateDatabaseReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDatabaseReq) SetDBName(val string) {
	p.DBName = val
}
func (p *CreateDatabaseReq) SetCharacterSetName(val string) {
	p.CharacterSetName = val
}
func (p *CreateDatabaseReq) SetDBDesc(val string) {
	p.DBDesc = val
}

var fieldIDToName_CreateDatabaseReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
	3: "CharacterSetName",
	4: "DBDesc",
}

func (p *CreateDatabaseReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false
	var issetCharacterSetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCharacterSetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCharacterSetName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDatabaseReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDatabaseReq[fieldId]))
}

func (p *CreateDatabaseReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDatabaseReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *CreateDatabaseReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CharacterSetName = _field
	return nil
}
func (p *CreateDatabaseReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBDesc = _field
	return nil
}

func (p *CreateDatabaseReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDatabaseReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CharacterSetName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CharacterSetName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBDesc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateDatabaseReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDatabaseReq(%+v)", *p)

}

func (p *CreateDatabaseReq) DeepEqual(ano *CreateDatabaseReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field3DeepEqual(ano.CharacterSetName) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBDesc) {
		return false
	}
	return true
}

func (p *CreateDatabaseReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDatabaseReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDatabaseReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CharacterSetName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDatabaseReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DBDesc, src) != 0 {
		return false
	}
	return true
}

type DeleteDatabaseReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	DBName     string `thrift:"DBName,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteDatabaseReq() *DeleteDatabaseReq {
	return &DeleteDatabaseReq{}
}

func (p *DeleteDatabaseReq) InitDefault() {
}

func (p *DeleteDatabaseReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDatabaseReq) GetDBName() (v string) {
	return p.DBName
}
func (p *DeleteDatabaseReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDatabaseReq) SetDBName(val string) {
	p.DBName = val
}

var fieldIDToName_DeleteDatabaseReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
}

func (p *DeleteDatabaseReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDatabaseReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDatabaseReq[fieldId]))
}

func (p *DeleteDatabaseReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDatabaseReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}

func (p *DeleteDatabaseReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDatabaseReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDatabaseReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDatabaseReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDatabaseReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDatabaseReq(%+v)", *p)

}

func (p *DeleteDatabaseReq) DeepEqual(ano *DeleteDatabaseReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	return true
}

func (p *DeleteDatabaseReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDatabaseReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}

type ListDatabasesReq struct {
	Offset     int32     `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit      int32     `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId string    `thrift:"InstanceId,3" frugal:"3,default,string" json:"InstanceId"`
	DBName     string    `thrift:"DBName,4" frugal:"4,default,string" json:"DBName"`
	DBStatus   *DBStatus `thrift:"DBStatus,5,optional" frugal:"5,optional,DBStatus" json:"DBStatus,omitempty"`
}

func NewListDatabasesReq() *ListDatabasesReq {
	return &ListDatabasesReq{}
}

func (p *ListDatabasesReq) InitDefault() {
}

func (p *ListDatabasesReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListDatabasesReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListDatabasesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListDatabasesReq) GetDBName() (v string) {
	return p.DBName
}

var ListDatabasesReq_DBStatus_DEFAULT DBStatus

func (p *ListDatabasesReq) GetDBStatus() (v DBStatus) {
	if !p.IsSetDBStatus() {
		return ListDatabasesReq_DBStatus_DEFAULT
	}
	return *p.DBStatus
}
func (p *ListDatabasesReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListDatabasesReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListDatabasesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListDatabasesReq) SetDBName(val string) {
	p.DBName = val
}
func (p *ListDatabasesReq) SetDBStatus(val *DBStatus) {
	p.DBStatus = val
}

var fieldIDToName_ListDatabasesReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "DBName",
	5: "DBStatus",
}

func (p *ListDatabasesReq) IsSetDBStatus() bool {
	return p.DBStatus != nil
}

func (p *ListDatabasesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDatabasesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListDatabasesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListDatabasesReq[fieldId]))
}

func (p *ListDatabasesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListDatabasesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListDatabasesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListDatabasesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *ListDatabasesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *DBStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBStatus(v)
		_field = &tmp
	}
	p.DBStatus = _field
	return nil
}

func (p *ListDatabasesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDatabasesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabasesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListDatabasesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListDatabasesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListDatabasesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListDatabasesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListDatabasesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBStatus() {
		if err = oprot.WriteFieldBegin("DBStatus", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DBStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListDatabasesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDatabasesReq(%+v)", *p)

}

func (p *ListDatabasesReq) DeepEqual(ano *ListDatabasesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field5DeepEqual(ano.DBStatus) {
		return false
	}
	return true
}

func (p *ListDatabasesReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListDatabasesReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListDatabasesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListDatabasesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *ListDatabasesReq) Field5DeepEqual(src *DBStatus) bool {

	if p.DBStatus == src {
		return true
	} else if p.DBStatus == nil || src == nil {
		return false
	}
	if *p.DBStatus != *src {
		return false
	}
	return true
}

type ListDatabasesResp struct {
	Total int32     `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*DBInfo `thrift:"Datas,2,required" frugal:"2,required,list<DBInfo>" json:"Datas"`
}

func NewListDatabasesResp() *ListDatabasesResp {
	return &ListDatabasesResp{}
}

func (p *ListDatabasesResp) InitDefault() {
}

func (p *ListDatabasesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListDatabasesResp) GetDatas() (v []*DBInfo) {
	return p.Datas
}
func (p *ListDatabasesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListDatabasesResp) SetDatas(val []*DBInfo) {
	p.Datas = val
}

var fieldIDToName_ListDatabasesResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListDatabasesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDatabasesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListDatabasesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListDatabasesResp[fieldId]))
}

func (p *ListDatabasesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListDatabasesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBInfo, 0, size)
	values := make([]DBInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListDatabasesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDatabasesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabasesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListDatabasesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListDatabasesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListDatabasesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDatabasesResp(%+v)", *p)

}

func (p *ListDatabasesResp) DeepEqual(ano *ListDatabasesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListDatabasesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListDatabasesResp) Field2DeepEqual(src []*DBInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListTablesReq struct {
	Offset     int32  `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit      int32  `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId string `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	DBName     string `thrift:"DBName,4" frugal:"4,default,string" json:"DBName"`
	TableName  string `thrift:"TableName,5" frugal:"5,default,string" json:"TableName"`
}

func NewListTablesReq() *ListTablesReq {
	return &ListTablesReq{}
}

func (p *ListTablesReq) InitDefault() {
}

func (p *ListTablesReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListTablesReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListTablesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListTablesReq) GetDBName() (v string) {
	return p.DBName
}

func (p *ListTablesReq) GetTableName() (v string) {
	return p.TableName
}
func (p *ListTablesReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListTablesReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListTablesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListTablesReq) SetDBName(val string) {
	p.DBName = val
}
func (p *ListTablesReq) SetTableName(val string) {
	p.TableName = val
}

var fieldIDToName_ListTablesReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "DBName",
	5: "TableName",
}

func (p *ListTablesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListTablesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListTablesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListTablesReq[fieldId]))
}

func (p *ListTablesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListTablesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListTablesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListTablesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *ListTablesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}

func (p *ListTablesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListTablesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListTablesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListTablesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListTablesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListTablesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListTablesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListTablesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListTablesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTablesReq(%+v)", *p)

}

func (p *ListTablesReq) DeepEqual(ano *ListTablesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field5DeepEqual(ano.TableName) {
		return false
	}
	return true
}

func (p *ListTablesReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListTablesReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListTablesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListTablesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *ListTablesReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}

type ListTablesResp struct {
	Total int32    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []string `thrift:"Datas,2,required" frugal:"2,required,list<string>" json:"Datas"`
}

func NewListTablesResp() *ListTablesResp {
	return &ListTablesResp{}
}

func (p *ListTablesResp) InitDefault() {
}

func (p *ListTablesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListTablesResp) GetDatas() (v []string) {
	return p.Datas
}
func (p *ListTablesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListTablesResp) SetDatas(val []string) {
	p.Datas = val
}

var fieldIDToName_ListTablesResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListTablesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListTablesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListTablesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListTablesResp[fieldId]))
}

func (p *ListTablesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListTablesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListTablesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListTablesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListTablesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListTablesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListTablesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListTablesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTablesResp(%+v)", *p)

}

func (p *ListTablesResp) DeepEqual(ano *ListTablesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListTablesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListTablesResp) Field2DeepEqual(src []string) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ListHostDatabasesReq struct {
	InstanceId string `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	Host       string `thrift:"Host,2" frugal:"2,default,string" json:"Host"`
	Port       string `thrift:"Port,3" frugal:"3,default,string" json:"Port"`
	Username   string `thrift:"Username,4" frugal:"4,default,string" json:"Username"`
	Password   string `thrift:"Password,5" frugal:"5,default,string" json:"Password"`
}

func NewListHostDatabasesReq() *ListHostDatabasesReq {
	return &ListHostDatabasesReq{}
}

func (p *ListHostDatabasesReq) InitDefault() {
}

func (p *ListHostDatabasesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListHostDatabasesReq) GetHost() (v string) {
	return p.Host
}

func (p *ListHostDatabasesReq) GetPort() (v string) {
	return p.Port
}

func (p *ListHostDatabasesReq) GetUsername() (v string) {
	return p.Username
}

func (p *ListHostDatabasesReq) GetPassword() (v string) {
	return p.Password
}
func (p *ListHostDatabasesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListHostDatabasesReq) SetHost(val string) {
	p.Host = val
}
func (p *ListHostDatabasesReq) SetPort(val string) {
	p.Port = val
}
func (p *ListHostDatabasesReq) SetUsername(val string) {
	p.Username = val
}
func (p *ListHostDatabasesReq) SetPassword(val string) {
	p.Password = val
}

var fieldIDToName_ListHostDatabasesReq = map[int16]string{
	1: "InstanceId",
	2: "Host",
	3: "Port",
	4: "Username",
	5: "Password",
}

func (p *ListHostDatabasesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostDatabasesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHostDatabasesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListHostDatabasesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListHostDatabasesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Host = _field
	return nil
}
func (p *ListHostDatabasesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Port = _field
	return nil
}
func (p *ListHostDatabasesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Username = _field
	return nil
}
func (p *ListHostDatabasesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}

func (p *ListHostDatabasesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostDatabasesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHostDatabasesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHostDatabasesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHostDatabasesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Host", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Host); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListHostDatabasesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Port", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Port); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListHostDatabasesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Username", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Username); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListHostDatabasesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListHostDatabasesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHostDatabasesReq(%+v)", *p)

}

func (p *ListHostDatabasesReq) DeepEqual(ano *ListHostDatabasesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Host) {
		return false
	}
	if !p.Field3DeepEqual(ano.Port) {
		return false
	}
	if !p.Field4DeepEqual(ano.Username) {
		return false
	}
	if !p.Field5DeepEqual(ano.Password) {
		return false
	}
	return true
}

func (p *ListHostDatabasesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostDatabasesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Host, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostDatabasesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Port, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostDatabasesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Username, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostDatabasesReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}

type ListHostDatabasesResp struct {
	Datas []string `thrift:"Datas,1,required" frugal:"1,required,list<string>" json:"Datas"`
}

func NewListHostDatabasesResp() *ListHostDatabasesResp {
	return &ListHostDatabasesResp{}
}

func (p *ListHostDatabasesResp) InitDefault() {
}

func (p *ListHostDatabasesResp) GetDatas() (v []string) {
	return p.Datas
}
func (p *ListHostDatabasesResp) SetDatas(val []string) {
	p.Datas = val
}

var fieldIDToName_ListHostDatabasesResp = map[int16]string{
	1: "Datas",
}

func (p *ListHostDatabasesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostDatabasesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatas {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHostDatabasesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListHostDatabasesResp[fieldId]))
}

func (p *ListHostDatabasesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListHostDatabasesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostDatabasesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHostDatabasesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHostDatabasesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHostDatabasesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHostDatabasesResp(%+v)", *p)

}

func (p *ListHostDatabasesResp) DeepEqual(ano *ListHostDatabasesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListHostDatabasesResp) Field1DeepEqual(src []string) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ListHostTablesReq struct {
	DBName     string `thrift:"DBName,1,required" frugal:"1,required,string" validate:"required"`
	InstanceId string `thrift:"InstanceId,2" frugal:"2,default,string" json:"InstanceId"`
	Host       string `thrift:"Host,3" frugal:"3,default,string" json:"Host"`
	Port       string `thrift:"Port,4" frugal:"4,default,string" json:"Port"`
	Username   string `thrift:"Username,5" frugal:"5,default,string" json:"Username"`
	Password   string `thrift:"Password,6" frugal:"6,default,string" json:"Password"`
}

func NewListHostTablesReq() *ListHostTablesReq {
	return &ListHostTablesReq{}
}

func (p *ListHostTablesReq) InitDefault() {
}

func (p *ListHostTablesReq) GetDBName() (v string) {
	return p.DBName
}

func (p *ListHostTablesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListHostTablesReq) GetHost() (v string) {
	return p.Host
}

func (p *ListHostTablesReq) GetPort() (v string) {
	return p.Port
}

func (p *ListHostTablesReq) GetUsername() (v string) {
	return p.Username
}

func (p *ListHostTablesReq) GetPassword() (v string) {
	return p.Password
}
func (p *ListHostTablesReq) SetDBName(val string) {
	p.DBName = val
}
func (p *ListHostTablesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListHostTablesReq) SetHost(val string) {
	p.Host = val
}
func (p *ListHostTablesReq) SetPort(val string) {
	p.Port = val
}
func (p *ListHostTablesReq) SetUsername(val string) {
	p.Username = val
}
func (p *ListHostTablesReq) SetPassword(val string) {
	p.Password = val
}

var fieldIDToName_ListHostTablesReq = map[int16]string{
	1: "DBName",
	2: "InstanceId",
	3: "Host",
	4: "Port",
	5: "Username",
	6: "Password",
}

func (p *ListHostTablesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostTablesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHostTablesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListHostTablesReq[fieldId]))
}

func (p *ListHostTablesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *ListHostTablesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListHostTablesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Host = _field
	return nil
}
func (p *ListHostTablesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Port = _field
	return nil
}
func (p *ListHostTablesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Username = _field
	return nil
}
func (p *ListHostTablesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}

func (p *ListHostTablesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostTablesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHostTablesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHostTablesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHostTablesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListHostTablesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Host", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Host); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListHostTablesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Port", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Port); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListHostTablesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Username", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Username); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListHostTablesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListHostTablesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHostTablesReq(%+v)", *p)

}

func (p *ListHostTablesReq) DeepEqual(ano *ListHostTablesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Host) {
		return false
	}
	if !p.Field4DeepEqual(ano.Port) {
		return false
	}
	if !p.Field5DeepEqual(ano.Username) {
		return false
	}
	if !p.Field6DeepEqual(ano.Password) {
		return false
	}
	return true
}

func (p *ListHostTablesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostTablesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostTablesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Host, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostTablesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Port, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostTablesReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Username, src) != 0 {
		return false
	}
	return true
}
func (p *ListHostTablesReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}

type ListHostTablesResp struct {
	Datas []string `thrift:"Datas,1,required" frugal:"1,required,list<string>" json:"Datas"`
}

func NewListHostTablesResp() *ListHostTablesResp {
	return &ListHostTablesResp{}
}

func (p *ListHostTablesResp) InitDefault() {
}

func (p *ListHostTablesResp) GetDatas() (v []string) {
	return p.Datas
}
func (p *ListHostTablesResp) SetDatas(val []string) {
	p.Datas = val
}

var fieldIDToName_ListHostTablesResp = map[int16]string{
	1: "Datas",
}

func (p *ListHostTablesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostTablesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatas {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHostTablesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListHostTablesResp[fieldId]))
}

func (p *ListHostTablesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListHostTablesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHostTablesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHostTablesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHostTablesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHostTablesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHostTablesResp(%+v)", *p)

}

func (p *ListHostTablesResp) DeepEqual(ano *ListHostTablesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListHostTablesResp) Field1DeepEqual(src []string) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type CreateUserDataImportTaskReq struct {
	DataFileUrl         string         `thrift:"DataFileUrl,1,required" frugal:"1,required,string" json:"DataFileUrl"`
	FileType            ImportFileType `thrift:"FileType,2,required" frugal:"2,required,ImportFileType" json:"FileType"`
	InstanceId          string         `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	Database            *string        `thrift:"Database,4,optional" frugal:"4,optional,string" json:"Database,omitempty"`
	Table               *string        `thrift:"Table,5,optional" frugal:"5,optional,string" json:"Table,omitempty"`
	FirstLineColumnName *bool          `thrift:"FirstLineColumnName,6,optional" frugal:"6,optional,bool" json:"FirstLineColumnName,omitempty"`
	EnableReplace       *bool          `thrift:"EnableReplace,7,optional" frugal:"7,optional,bool" json:"EnableReplace,omitempty"`
	FieldTerminator     *string        `thrift:"FieldTerminator,8,optional" frugal:"8,optional,string" json:"FieldTerminator,omitempty"`
	FieldEnclose        *string        `thrift:"FieldEnclose,9,optional" frugal:"9,optional,string" json:"FieldEnclose,omitempty"`
	FieldEscape         *string        `thrift:"FieldEscape,10,optional" frugal:"10,optional,string" json:"FieldEscape,omitempty"`
	LineTerminator      *string        `thrift:"LineTerminator,11,optional" frugal:"11,optional,string" json:"LineTerminator,omitempty"`
}

func NewCreateUserDataImportTaskReq() *CreateUserDataImportTaskReq {
	return &CreateUserDataImportTaskReq{}
}

func (p *CreateUserDataImportTaskReq) InitDefault() {
}

func (p *CreateUserDataImportTaskReq) GetDataFileUrl() (v string) {
	return p.DataFileUrl
}

func (p *CreateUserDataImportTaskReq) GetFileType() (v ImportFileType) {
	return p.FileType
}

func (p *CreateUserDataImportTaskReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateUserDataImportTaskReq_Database_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetDatabase() (v string) {
	if !p.IsSetDatabase() {
		return CreateUserDataImportTaskReq_Database_DEFAULT
	}
	return *p.Database
}

var CreateUserDataImportTaskReq_Table_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetTable() (v string) {
	if !p.IsSetTable() {
		return CreateUserDataImportTaskReq_Table_DEFAULT
	}
	return *p.Table
}

var CreateUserDataImportTaskReq_FirstLineColumnName_DEFAULT bool

func (p *CreateUserDataImportTaskReq) GetFirstLineColumnName() (v bool) {
	if !p.IsSetFirstLineColumnName() {
		return CreateUserDataImportTaskReq_FirstLineColumnName_DEFAULT
	}
	return *p.FirstLineColumnName
}

var CreateUserDataImportTaskReq_EnableReplace_DEFAULT bool

func (p *CreateUserDataImportTaskReq) GetEnableReplace() (v bool) {
	if !p.IsSetEnableReplace() {
		return CreateUserDataImportTaskReq_EnableReplace_DEFAULT
	}
	return *p.EnableReplace
}

var CreateUserDataImportTaskReq_FieldTerminator_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetFieldTerminator() (v string) {
	if !p.IsSetFieldTerminator() {
		return CreateUserDataImportTaskReq_FieldTerminator_DEFAULT
	}
	return *p.FieldTerminator
}

var CreateUserDataImportTaskReq_FieldEnclose_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetFieldEnclose() (v string) {
	if !p.IsSetFieldEnclose() {
		return CreateUserDataImportTaskReq_FieldEnclose_DEFAULT
	}
	return *p.FieldEnclose
}

var CreateUserDataImportTaskReq_FieldEscape_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetFieldEscape() (v string) {
	if !p.IsSetFieldEscape() {
		return CreateUserDataImportTaskReq_FieldEscape_DEFAULT
	}
	return *p.FieldEscape
}

var CreateUserDataImportTaskReq_LineTerminator_DEFAULT string

func (p *CreateUserDataImportTaskReq) GetLineTerminator() (v string) {
	if !p.IsSetLineTerminator() {
		return CreateUserDataImportTaskReq_LineTerminator_DEFAULT
	}
	return *p.LineTerminator
}
func (p *CreateUserDataImportTaskReq) SetDataFileUrl(val string) {
	p.DataFileUrl = val
}
func (p *CreateUserDataImportTaskReq) SetFileType(val ImportFileType) {
	p.FileType = val
}
func (p *CreateUserDataImportTaskReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateUserDataImportTaskReq) SetDatabase(val *string) {
	p.Database = val
}
func (p *CreateUserDataImportTaskReq) SetTable(val *string) {
	p.Table = val
}
func (p *CreateUserDataImportTaskReq) SetFirstLineColumnName(val *bool) {
	p.FirstLineColumnName = val
}
func (p *CreateUserDataImportTaskReq) SetEnableReplace(val *bool) {
	p.EnableReplace = val
}
func (p *CreateUserDataImportTaskReq) SetFieldTerminator(val *string) {
	p.FieldTerminator = val
}
func (p *CreateUserDataImportTaskReq) SetFieldEnclose(val *string) {
	p.FieldEnclose = val
}
func (p *CreateUserDataImportTaskReq) SetFieldEscape(val *string) {
	p.FieldEscape = val
}
func (p *CreateUserDataImportTaskReq) SetLineTerminator(val *string) {
	p.LineTerminator = val
}

var fieldIDToName_CreateUserDataImportTaskReq = map[int16]string{
	1:  "DataFileUrl",
	2:  "FileType",
	3:  "InstanceId",
	4:  "Database",
	5:  "Table",
	6:  "FirstLineColumnName",
	7:  "EnableReplace",
	8:  "FieldTerminator",
	9:  "FieldEnclose",
	10: "FieldEscape",
	11: "LineTerminator",
}

func (p *CreateUserDataImportTaskReq) IsSetDatabase() bool {
	return p.Database != nil
}

func (p *CreateUserDataImportTaskReq) IsSetTable() bool {
	return p.Table != nil
}

func (p *CreateUserDataImportTaskReq) IsSetFirstLineColumnName() bool {
	return p.FirstLineColumnName != nil
}

func (p *CreateUserDataImportTaskReq) IsSetEnableReplace() bool {
	return p.EnableReplace != nil
}

func (p *CreateUserDataImportTaskReq) IsSetFieldTerminator() bool {
	return p.FieldTerminator != nil
}

func (p *CreateUserDataImportTaskReq) IsSetFieldEnclose() bool {
	return p.FieldEnclose != nil
}

func (p *CreateUserDataImportTaskReq) IsSetFieldEscape() bool {
	return p.FieldEscape != nil
}

func (p *CreateUserDataImportTaskReq) IsSetLineTerminator() bool {
	return p.LineTerminator != nil
}

func (p *CreateUserDataImportTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserDataImportTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataFileUrl bool = false
	var issetFileType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFileUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataFileUrl {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetFileType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateUserDataImportTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateUserDataImportTaskReq[fieldId]))
}

func (p *CreateUserDataImportTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFileUrl = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field ImportFileType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ImportFileType(v)
	}
	p.FileType = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Database = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Table = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FirstLineColumnName = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableReplace = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldTerminator = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldEnclose = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldEscape = _field
	return nil
}
func (p *CreateUserDataImportTaskReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LineTerminator = _field
	return nil
}

func (p *CreateUserDataImportTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserDataImportTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateUserDataImportTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFileUrl", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFileUrl); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FileType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabase() {
		if err = oprot.WriteFieldBegin("Database", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Database); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTable() {
		if err = oprot.WriteFieldBegin("Table", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Table); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFirstLineColumnName() {
		if err = oprot.WriteFieldBegin("FirstLineColumnName", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.FirstLineColumnName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableReplace() {
		if err = oprot.WriteFieldBegin("EnableReplace", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableReplace); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldTerminator() {
		if err = oprot.WriteFieldBegin("FieldTerminator", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldTerminator); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldEnclose() {
		if err = oprot.WriteFieldBegin("FieldEnclose", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldEnclose); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldEscape() {
		if err = oprot.WriteFieldBegin("FieldEscape", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldEscape); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetLineTerminator() {
		if err = oprot.WriteFieldBegin("LineTerminator", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LineTerminator); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateUserDataImportTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUserDataImportTaskReq(%+v)", *p)

}

func (p *CreateUserDataImportTaskReq) DeepEqual(ano *CreateUserDataImportTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataFileUrl) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileType) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Database) {
		return false
	}
	if !p.Field5DeepEqual(ano.Table) {
		return false
	}
	if !p.Field6DeepEqual(ano.FirstLineColumnName) {
		return false
	}
	if !p.Field7DeepEqual(ano.EnableReplace) {
		return false
	}
	if !p.Field8DeepEqual(ano.FieldTerminator) {
		return false
	}
	if !p.Field9DeepEqual(ano.FieldEnclose) {
		return false
	}
	if !p.Field10DeepEqual(ano.FieldEscape) {
		return false
	}
	if !p.Field11DeepEqual(ano.LineTerminator) {
		return false
	}
	return true
}

func (p *CreateUserDataImportTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DataFileUrl, src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field2DeepEqual(src ImportFileType) bool {

	if p.FileType != src {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field4DeepEqual(src *string) bool {

	if p.Database == src {
		return true
	} else if p.Database == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Database, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field5DeepEqual(src *string) bool {

	if p.Table == src {
		return true
	} else if p.Table == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Table, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field6DeepEqual(src *bool) bool {

	if p.FirstLineColumnName == src {
		return true
	} else if p.FirstLineColumnName == nil || src == nil {
		return false
	}
	if *p.FirstLineColumnName != *src {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field7DeepEqual(src *bool) bool {

	if p.EnableReplace == src {
		return true
	} else if p.EnableReplace == nil || src == nil {
		return false
	}
	if *p.EnableReplace != *src {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field8DeepEqual(src *string) bool {

	if p.FieldTerminator == src {
		return true
	} else if p.FieldTerminator == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldTerminator, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field9DeepEqual(src *string) bool {

	if p.FieldEnclose == src {
		return true
	} else if p.FieldEnclose == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldEnclose, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field10DeepEqual(src *string) bool {

	if p.FieldEscape == src {
		return true
	} else if p.FieldEscape == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldEscape, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserDataImportTaskReq) Field11DeepEqual(src *string) bool {

	if p.LineTerminator == src {
		return true
	} else if p.LineTerminator == nil || src == nil {
		return false
	}
	if strings.Compare(*p.LineTerminator, *src) != 0 {
		return false
	}
	return true
}

type CreateUserDataImportTaskResp struct {
	TaskID string `thrift:"TaskID,1,required" frugal:"1,required,string" json:"TaskID"`
}

func NewCreateUserDataImportTaskResp() *CreateUserDataImportTaskResp {
	return &CreateUserDataImportTaskResp{}
}

func (p *CreateUserDataImportTaskResp) InitDefault() {
}

func (p *CreateUserDataImportTaskResp) GetTaskID() (v string) {
	return p.TaskID
}
func (p *CreateUserDataImportTaskResp) SetTaskID(val string) {
	p.TaskID = val
}

var fieldIDToName_CreateUserDataImportTaskResp = map[int16]string{
	1: "TaskID",
}

func (p *CreateUserDataImportTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserDataImportTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateUserDataImportTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateUserDataImportTaskResp[fieldId]))
}

func (p *CreateUserDataImportTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}

func (p *CreateUserDataImportTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserDataImportTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateUserDataImportTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateUserDataImportTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateUserDataImportTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUserDataImportTaskResp(%+v)", *p)

}

func (p *CreateUserDataImportTaskResp) DeepEqual(ano *CreateUserDataImportTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskID) {
		return false
	}
	return true
}

func (p *CreateUserDataImportTaskResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskID, src) != 0 {
		return false
	}
	return true
}

type ListUserDataImportTasksReq struct {
	Offset     int32                     `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit      int32                     `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId string                    `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	Status     *UserDataImportTaskStatus `thrift:"Status,4,optional" frugal:"4,optional,UserDataImportTaskStatus" json:"Status,omitempty"`
	Type       *ImportFileType           `thrift:"Type,5,optional" frugal:"5,optional,ImportFileType" json:"Type,omitempty"`
}

func NewListUserDataImportTasksReq() *ListUserDataImportTasksReq {
	return &ListUserDataImportTasksReq{}
}

func (p *ListUserDataImportTasksReq) InitDefault() {
}

func (p *ListUserDataImportTasksReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListUserDataImportTasksReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListUserDataImportTasksReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ListUserDataImportTasksReq_Status_DEFAULT UserDataImportTaskStatus

func (p *ListUserDataImportTasksReq) GetStatus() (v UserDataImportTaskStatus) {
	if !p.IsSetStatus() {
		return ListUserDataImportTasksReq_Status_DEFAULT
	}
	return *p.Status
}

var ListUserDataImportTasksReq_Type_DEFAULT ImportFileType

func (p *ListUserDataImportTasksReq) GetType() (v ImportFileType) {
	if !p.IsSetType() {
		return ListUserDataImportTasksReq_Type_DEFAULT
	}
	return *p.Type
}
func (p *ListUserDataImportTasksReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListUserDataImportTasksReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListUserDataImportTasksReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListUserDataImportTasksReq) SetStatus(val *UserDataImportTaskStatus) {
	p.Status = val
}
func (p *ListUserDataImportTasksReq) SetType(val *ImportFileType) {
	p.Type = val
}

var fieldIDToName_ListUserDataImportTasksReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "Status",
	5: "Type",
}

func (p *ListUserDataImportTasksReq) IsSetStatus() bool {
	return p.Status != nil
}

func (p *ListUserDataImportTasksReq) IsSetType() bool {
	return p.Type != nil
}

func (p *ListUserDataImportTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListUserDataImportTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListUserDataImportTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListUserDataImportTasksReq[fieldId]))
}

func (p *ListUserDataImportTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListUserDataImportTasksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListUserDataImportTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListUserDataImportTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *UserDataImportTaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := UserDataImportTaskStatus(v)
		_field = &tmp
	}
	p.Status = _field
	return nil
}
func (p *ListUserDataImportTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *ImportFileType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ImportFileType(v)
		_field = &tmp
	}
	p.Type = _field
	return nil
}

func (p *ListUserDataImportTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListUserDataImportTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListUserDataImportTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("Status", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Status)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetType() {
		if err = oprot.WriteFieldBegin("Type", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Type)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListUserDataImportTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserDataImportTasksReq(%+v)", *p)

}

func (p *ListUserDataImportTasksReq) DeepEqual(ano *ListUserDataImportTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Status) {
		return false
	}
	if !p.Field5DeepEqual(ano.Type) {
		return false
	}
	return true
}

func (p *ListUserDataImportTasksReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListUserDataImportTasksReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListUserDataImportTasksReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListUserDataImportTasksReq) Field4DeepEqual(src *UserDataImportTaskStatus) bool {

	if p.Status == src {
		return true
	} else if p.Status == nil || src == nil {
		return false
	}
	if *p.Status != *src {
		return false
	}
	return true
}
func (p *ListUserDataImportTasksReq) Field5DeepEqual(src *ImportFileType) bool {

	if p.Type == src {
		return true
	} else if p.Type == nil || src == nil {
		return false
	}
	if *p.Type != *src {
		return false
	}
	return true
}

type ListUserDataImportTasksResp struct {
	Total int32                 `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*UserDataImportTask `thrift:"Datas,2,required" frugal:"2,required,list<UserDataImportTask>" json:"Datas"`
}

func NewListUserDataImportTasksResp() *ListUserDataImportTasksResp {
	return &ListUserDataImportTasksResp{}
}

func (p *ListUserDataImportTasksResp) InitDefault() {
}

func (p *ListUserDataImportTasksResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListUserDataImportTasksResp) GetDatas() (v []*UserDataImportTask) {
	return p.Datas
}
func (p *ListUserDataImportTasksResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListUserDataImportTasksResp) SetDatas(val []*UserDataImportTask) {
	p.Datas = val
}

var fieldIDToName_ListUserDataImportTasksResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListUserDataImportTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListUserDataImportTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListUserDataImportTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListUserDataImportTasksResp[fieldId]))
}

func (p *ListUserDataImportTasksResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListUserDataImportTasksResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UserDataImportTask, 0, size)
	values := make([]UserDataImportTask, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListUserDataImportTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListUserDataImportTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListUserDataImportTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListUserDataImportTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListUserDataImportTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListUserDataImportTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserDataImportTasksResp(%+v)", *p)

}

func (p *ListUserDataImportTasksResp) DeepEqual(ano *ListUserDataImportTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListUserDataImportTasksResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListUserDataImportTasksResp) Field2DeepEqual(src []*UserDataImportTask) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

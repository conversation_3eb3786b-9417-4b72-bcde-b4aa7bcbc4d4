// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ListInstanceParamsReq) IsValid() error {
	return nil
}
func (p *ListInstanceParamsResp) IsValid() error {
	return nil
}
func (p *ModifyInstanceParamsReq) IsValid() error {
	return nil
}
func (p *ListInstanceParamsHistoryReq) IsValid() error {
	return nil
}
func (p *ListInstanceParamsHistoryResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceConfigReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceConfigResp) IsValid() error {
	if p.TosConfig != nil {
		if err := p.TosConfig.IsValid(); err != nil {
			return fmt.Errorf("field TosConfig not valid, %w", err)
		}
	}
	if p.TlsConfig != nil {
		if err := p.TlsConfig.IsValid(); err != nil {
			return fmt.Errorf("field TlsConfig not valid, %w", err)
		}
	}
	if p.DbwConfig != nil {
		if err := p.DbwConfig.IsValid(); err != nil {
			return fmt.Errorf("field DbwConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyInstanceConfigReq) IsValid() error {
	if p.TosConfig != nil {
		if err := p.TosConfig.IsValid(); err != nil {
			return fmt.Errorf("field TosConfig not valid, %w", err)
		}
	}
	if p.TlsConfig != nil {
		if err := p.TlsConfig.IsValid(); err != nil {
			return fmt.Errorf("field TlsConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *ListParameterTemplatesReq) IsValid() error {
	return nil
}
func (p *ListParameterTemplatesResp) IsValid() error {
	return nil
}
func (p *DescribeParameterTemplateReq) IsValid() error {
	return nil
}
func (p *DescribeParameterTemplateResp) IsValid() error {
	if p.TemplateInfo != nil {
		if err := p.TemplateInfo.IsValid(); err != nil {
			return fmt.Errorf("field TemplateInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteParameterTemplateReq) IsValid() error {
	return nil
}
func (p *CopyParameterTemplateReq) IsValid() error {
	return nil
}
func (p *CreateParameterTemplateReq) IsValid() error {
	return nil
}
func (p *ModifyParameterTemplateReq) IsValid() error {
	return nil
}
func (p *DescribeApplyParameterTemplateReq) IsValid() error {
	return nil
}
func (p *DescribeApplyParameterTemplateResp) IsValid() error {
	return nil
}
func (p *SaveAsParameterTemplateReq) IsValid() error {
	return nil
}
func (p *ModifyCustomMySQLParamsReq) IsValid() error {
	return nil
}

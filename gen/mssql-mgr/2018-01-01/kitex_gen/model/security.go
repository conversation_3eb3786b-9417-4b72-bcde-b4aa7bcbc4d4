// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateDBInstanceIPListReq struct {
	InstanceId string   `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	GroupName  string   `thrift:"GroupName,2,required" frugal:"2,required,string" validate:"required,min=2,max=120"`
	IPList     []string `thrift:"IPList,3,required" frugal:"3,required,list<string>" json:"IPList"`
}

func NewCreateDBInstanceIPListReq() *CreateDBInstanceIPListReq {
	return &CreateDBInstanceIPListReq{}
}

func (p *CreateDBInstanceIPListReq) InitDefault() {
}

func (p *CreateDBInstanceIPListReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBInstanceIPListReq) GetGroupName() (v string) {
	return p.GroupName
}

func (p *CreateDBInstanceIPListReq) GetIPList() (v []string) {
	return p.IPList
}
func (p *CreateDBInstanceIPListReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBInstanceIPListReq) SetGroupName(val string) {
	p.GroupName = val
}
func (p *CreateDBInstanceIPListReq) SetIPList(val []string) {
	p.IPList = val
}

var fieldIDToName_CreateDBInstanceIPListReq = map[int16]string{
	1: "InstanceId",
	2: "GroupName",
	3: "IPList",
}

func (p *CreateDBInstanceIPListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBInstanceIPListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetGroupName bool = false
	var issetIPList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroupName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIPList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetGroupName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIPList {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBInstanceIPListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBInstanceIPListReq[fieldId]))
}

func (p *CreateDBInstanceIPListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBInstanceIPListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupName = _field
	return nil
}
func (p *CreateDBInstanceIPListReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IPList = _field
	return nil
}

func (p *CreateDBInstanceIPListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBInstanceIPListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBInstanceIPListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBInstanceIPListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBInstanceIPListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBInstanceIPListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IPList", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.IPList)); err != nil {
		return err
	}
	for _, v := range p.IPList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDBInstanceIPListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBInstanceIPListReq(%+v)", *p)

}

func (p *CreateDBInstanceIPListReq) DeepEqual(ano *CreateDBInstanceIPListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.GroupName) {
		return false
	}
	if !p.Field3DeepEqual(ano.IPList) {
		return false
	}
	return true
}

func (p *CreateDBInstanceIPListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBInstanceIPListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.GroupName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBInstanceIPListReq) Field3DeepEqual(src []string) bool {

	if len(p.IPList) != len(src) {
		return false
	}
	for i, v := range p.IPList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ModifyDBInstanceIPListReq struct {
	InstanceId string   `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	GroupName  string   `thrift:"GroupName,2,required" frugal:"2,required,string" validate:"required"`
	IPList     []string `thrift:"IPList,3,required" frugal:"3,required,list<string>" json:"IPList"`
}

func NewModifyDBInstanceIPListReq() *ModifyDBInstanceIPListReq {
	return &ModifyDBInstanceIPListReq{}
}

func (p *ModifyDBInstanceIPListReq) InitDefault() {
}

func (p *ModifyDBInstanceIPListReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceIPListReq) GetGroupName() (v string) {
	return p.GroupName
}

func (p *ModifyDBInstanceIPListReq) GetIPList() (v []string) {
	return p.IPList
}
func (p *ModifyDBInstanceIPListReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceIPListReq) SetGroupName(val string) {
	p.GroupName = val
}
func (p *ModifyDBInstanceIPListReq) SetIPList(val []string) {
	p.IPList = val
}

var fieldIDToName_ModifyDBInstanceIPListReq = map[int16]string{
	1: "InstanceId",
	2: "GroupName",
	3: "IPList",
}

func (p *ModifyDBInstanceIPListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceIPListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetGroupName bool = false
	var issetIPList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroupName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIPList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetGroupName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIPList {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceIPListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceIPListReq[fieldId]))
}

func (p *ModifyDBInstanceIPListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceIPListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupName = _field
	return nil
}
func (p *ModifyDBInstanceIPListReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IPList = _field
	return nil
}

func (p *ModifyDBInstanceIPListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceIPListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceIPListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceIPListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceIPListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceIPListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IPList", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.IPList)); err != nil {
		return err
	}
	for _, v := range p.IPList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceIPListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceIPListReq(%+v)", *p)

}

func (p *ModifyDBInstanceIPListReq) DeepEqual(ano *ModifyDBInstanceIPListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.GroupName) {
		return false
	}
	if !p.Field3DeepEqual(ano.IPList) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceIPListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceIPListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.GroupName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceIPListReq) Field3DeepEqual(src []string) bool {

	if len(p.IPList) != len(src) {
		return false
	}
	for i, v := range p.IPList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DeleteDBInstanceIPListReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	GroupName  string `thrift:"GroupName,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteDBInstanceIPListReq() *DeleteDBInstanceIPListReq {
	return &DeleteDBInstanceIPListReq{}
}

func (p *DeleteDBInstanceIPListReq) InitDefault() {
}

func (p *DeleteDBInstanceIPListReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBInstanceIPListReq) GetGroupName() (v string) {
	return p.GroupName
}
func (p *DeleteDBInstanceIPListReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBInstanceIPListReq) SetGroupName(val string) {
	p.GroupName = val
}

var fieldIDToName_DeleteDBInstanceIPListReq = map[int16]string{
	1: "InstanceId",
	2: "GroupName",
}

func (p *DeleteDBInstanceIPListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceIPListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetGroupName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroupName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetGroupName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBInstanceIPListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBInstanceIPListReq[fieldId]))
}

func (p *DeleteDBInstanceIPListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBInstanceIPListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupName = _field
	return nil
}

func (p *DeleteDBInstanceIPListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceIPListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBInstanceIPListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBInstanceIPListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBInstanceIPListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBInstanceIPListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBInstanceIPListReq(%+v)", *p)

}

func (p *DeleteDBInstanceIPListReq) DeepEqual(ano *DeleteDBInstanceIPListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.GroupName) {
		return false
	}
	return true
}

func (p *DeleteDBInstanceIPListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBInstanceIPListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.GroupName, src) != 0 {
		return false
	}
	return true
}

type ListDBInstanceIPListsReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewListDBInstanceIPListsReq() *ListDBInstanceIPListsReq {
	return &ListDBInstanceIPListsReq{}
}

func (p *ListDBInstanceIPListsReq) InitDefault() {
}

func (p *ListDBInstanceIPListsReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ListDBInstanceIPListsReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ListDBInstanceIPListsReq = map[int16]string{
	1: "InstanceId",
}

func (p *ListDBInstanceIPListsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDBInstanceIPListsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListDBInstanceIPListsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListDBInstanceIPListsReq[fieldId]))
}

func (p *ListDBInstanceIPListsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ListDBInstanceIPListsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDBInstanceIPListsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListDBInstanceIPListsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListDBInstanceIPListsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListDBInstanceIPListsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDBInstanceIPListsReq(%+v)", *p)

}

func (p *ListDBInstanceIPListsReq) DeepEqual(ano *ListDBInstanceIPListsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ListDBInstanceIPListsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type ListDBInstanceIPListsResp struct {
	Total int32           `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*WhiteIPGroup `thrift:"Datas,2,required" frugal:"2,required,list<WhiteIPGroup>" json:"Datas"`
}

func NewListDBInstanceIPListsResp() *ListDBInstanceIPListsResp {
	return &ListDBInstanceIPListsResp{}
}

func (p *ListDBInstanceIPListsResp) InitDefault() {
}

func (p *ListDBInstanceIPListsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListDBInstanceIPListsResp) GetDatas() (v []*WhiteIPGroup) {
	return p.Datas
}
func (p *ListDBInstanceIPListsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListDBInstanceIPListsResp) SetDatas(val []*WhiteIPGroup) {
	p.Datas = val
}

var fieldIDToName_ListDBInstanceIPListsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListDBInstanceIPListsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDBInstanceIPListsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListDBInstanceIPListsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListDBInstanceIPListsResp[fieldId]))
}

func (p *ListDBInstanceIPListsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListDBInstanceIPListsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WhiteIPGroup, 0, size)
	values := make([]WhiteIPGroup, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListDBInstanceIPListsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListDBInstanceIPListsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListDBInstanceIPListsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListDBInstanceIPListsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListDBInstanceIPListsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListDBInstanceIPListsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDBInstanceIPListsResp(%+v)", *p)

}

func (p *ListDBInstanceIPListsResp) DeepEqual(ano *ListDBInstanceIPListsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListDBInstanceIPListsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListDBInstanceIPListsResp) Field2DeepEqual(src []*WhiteIPGroup) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDBInstanceTDEReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeDBInstanceTDEReq() *DescribeDBInstanceTDEReq {
	return &DescribeDBInstanceTDEReq{}
}

func (p *DescribeDBInstanceTDEReq) InitDefault() {
}

func (p *DescribeDBInstanceTDEReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBInstanceTDEReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBInstanceTDEReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBInstanceTDEReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceTDEReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceTDEReq[fieldId]))
}

func (p *DescribeDBInstanceTDEReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBInstanceTDEReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceTDEReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceTDEReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceTDEReq(%+v)", *p)

}

func (p *DescribeDBInstanceTDEReq) DeepEqual(ano *DescribeDBInstanceTDEReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceTDEReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceTDEResp struct {
	TDEStatus EnableSwitch `thrift:"TDEStatus,1,required" frugal:"1,required,EnableSwitch" json:"TDEStatus"`
}

func NewDescribeDBInstanceTDEResp() *DescribeDBInstanceTDEResp {
	return &DescribeDBInstanceTDEResp{}
}

func (p *DescribeDBInstanceTDEResp) InitDefault() {
}

func (p *DescribeDBInstanceTDEResp) GetTDEStatus() (v EnableSwitch) {
	return p.TDEStatus
}
func (p *DescribeDBInstanceTDEResp) SetTDEStatus(val EnableSwitch) {
	p.TDEStatus = val
}

var fieldIDToName_DescribeDBInstanceTDEResp = map[int16]string{
	1: "TDEStatus",
}

func (p *DescribeDBInstanceTDEResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTDEStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTDEStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTDEStatus {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceTDEResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceTDEResp[fieldId]))
}

func (p *DescribeDBInstanceTDEResp) ReadField1(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.TDEStatus = _field
	return nil
}

func (p *DescribeDBInstanceTDEResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceTDEResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEStatus", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceTDEResp(%+v)", *p)

}

func (p *DescribeDBInstanceTDEResp) DeepEqual(ano *DescribeDBInstanceTDEResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TDEStatus) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceTDEResp) Field1DeepEqual(src EnableSwitch) bool {

	if p.TDEStatus != src {
		return false
	}
	return true
}

type ModifyDBInstanceTDEReq struct {
	InstanceId    string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	TDEStatus     EnableSwitch `thrift:"TDEStatus,2,required" frugal:"2,required,EnableSwitch" json:"TDEStatus"`
	EncryptionKey string       `thrift:"EncryptionKey,3" frugal:"3,default,string" json:"EncryptionKey"`
	RoleArn       string       `thrift:"RoleArn,4" frugal:"4,default,string" json:"RoleArn"`
}

func NewModifyDBInstanceTDEReq() *ModifyDBInstanceTDEReq {
	return &ModifyDBInstanceTDEReq{}
}

func (p *ModifyDBInstanceTDEReq) InitDefault() {
}

func (p *ModifyDBInstanceTDEReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceTDEReq) GetTDEStatus() (v EnableSwitch) {
	return p.TDEStatus
}

func (p *ModifyDBInstanceTDEReq) GetEncryptionKey() (v string) {
	return p.EncryptionKey
}

func (p *ModifyDBInstanceTDEReq) GetRoleArn() (v string) {
	return p.RoleArn
}
func (p *ModifyDBInstanceTDEReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceTDEReq) SetTDEStatus(val EnableSwitch) {
	p.TDEStatus = val
}
func (p *ModifyDBInstanceTDEReq) SetEncryptionKey(val string) {
	p.EncryptionKey = val
}
func (p *ModifyDBInstanceTDEReq) SetRoleArn(val string) {
	p.RoleArn = val
}

var fieldIDToName_ModifyDBInstanceTDEReq = map[int16]string{
	1: "InstanceId",
	2: "TDEStatus",
	3: "EncryptionKey",
	4: "RoleArn",
}

func (p *ModifyDBInstanceTDEReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTDEStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTDEStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTDEStatus {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceTDEReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceTDEReq[fieldId]))
}

func (p *ModifyDBInstanceTDEReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.TDEStatus = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EncryptionKey = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RoleArn = _field
	return nil
}

func (p *ModifyDBInstanceTDEReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceTDEReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EncryptionKey", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EncryptionKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RoleArn", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RoleArn); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceTDEReq(%+v)", *p)

}

func (p *ModifyDBInstanceTDEReq) DeepEqual(ano *ModifyDBInstanceTDEReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TDEStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.EncryptionKey) {
		return false
	}
	if !p.Field4DeepEqual(ano.RoleArn) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceTDEReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field2DeepEqual(src EnableSwitch) bool {

	if p.TDEStatus != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EncryptionKey, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RoleArn, src) != 0 {
		return false
	}
	return true
}

type UpgradeAllowListVersionReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewUpgradeAllowListVersionReq() *UpgradeAllowListVersionReq {
	return &UpgradeAllowListVersionReq{}
}

func (p *UpgradeAllowListVersionReq) InitDefault() {
}

func (p *UpgradeAllowListVersionReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *UpgradeAllowListVersionReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_UpgradeAllowListVersionReq = map[int16]string{
	1: "InstanceId",
}

func (p *UpgradeAllowListVersionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpgradeAllowListVersionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpgradeAllowListVersionReq[fieldId]))
}

func (p *UpgradeAllowListVersionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *UpgradeAllowListVersionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpgradeAllowListVersionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpgradeAllowListVersionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpgradeAllowListVersionReq(%+v)", *p)

}

func (p *UpgradeAllowListVersionReq) DeepEqual(ano *UpgradeAllowListVersionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *UpgradeAllowListVersionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type UpgradeAllowListVersionResp struct {
}

func NewUpgradeAllowListVersionResp() *UpgradeAllowListVersionResp {
	return &UpgradeAllowListVersionResp{}
}

func (p *UpgradeAllowListVersionResp) InitDefault() {
}

var fieldIDToName_UpgradeAllowListVersionResp = map[int16]string{}

func (p *UpgradeAllowListVersionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionResp")

	if err = oprot.WriteStructBegin("UpgradeAllowListVersionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpgradeAllowListVersionResp(%+v)", *p)

}

func (p *UpgradeAllowListVersionResp) DeepEqual(ano *UpgradeAllowListVersionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeAllowListsReq struct {
	RegionId   string  `thrift:"RegionId,1,required" frugal:"1,required,string" validate:"required"`
	InstanceId *string `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
}

func NewDescribeAllowListsReq() *DescribeAllowListsReq {
	return &DescribeAllowListsReq{}
}

func (p *DescribeAllowListsReq) InitDefault() {
}

func (p *DescribeAllowListsReq) GetRegionId() (v string) {
	return p.RegionId
}

var DescribeAllowListsReq_InstanceId_DEFAULT string

func (p *DescribeAllowListsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeAllowListsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *DescribeAllowListsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAllowListsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeAllowListsReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
}

func (p *DescribeAllowListsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeAllowListsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListsReq[fieldId]))
}

func (p *DescribeAllowListsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAllowListsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeAllowListsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAllowListsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListsReq(%+v)", *p)

}

func (p *DescribeAllowListsReq) DeepEqual(ano *DescribeAllowListsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeAllowListsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListsReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type AllowListObject struct {
	AllowListId           string `thrift:"AllowListId,1" frugal:"1,default,string" json:"AllowListId"`
	AllowListName         string `thrift:"AllowListName,2" frugal:"2,default,string" json:"AllowListName"`
	AllowListDesc         string `thrift:"AllowListDesc,3" frugal:"3,default,string" json:"AllowListDesc"`
	AllowListType         string `thrift:"AllowListType,4" frugal:"4,default,string" json:"AllowListType"`
	AllowListIPNum        int32  `thrift:"AllowListIPNum,5" frugal:"5,default,i32" json:"AllowListIPNum"`
	AssociatedInstanceNum int32  `thrift:"AssociatedInstanceNum,6" frugal:"6,default,i32" json:"AssociatedInstanceNum"`
}

func NewAllowListObject() *AllowListObject {
	return &AllowListObject{}
}

func (p *AllowListObject) InitDefault() {
}

func (p *AllowListObject) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *AllowListObject) GetAllowListName() (v string) {
	return p.AllowListName
}

func (p *AllowListObject) GetAllowListDesc() (v string) {
	return p.AllowListDesc
}

func (p *AllowListObject) GetAllowListType() (v string) {
	return p.AllowListType
}

func (p *AllowListObject) GetAllowListIPNum() (v int32) {
	return p.AllowListIPNum
}

func (p *AllowListObject) GetAssociatedInstanceNum() (v int32) {
	return p.AssociatedInstanceNum
}
func (p *AllowListObject) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *AllowListObject) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *AllowListObject) SetAllowListDesc(val string) {
	p.AllowListDesc = val
}
func (p *AllowListObject) SetAllowListType(val string) {
	p.AllowListType = val
}
func (p *AllowListObject) SetAllowListIPNum(val int32) {
	p.AllowListIPNum = val
}
func (p *AllowListObject) SetAssociatedInstanceNum(val int32) {
	p.AssociatedInstanceNum = val
}

var fieldIDToName_AllowListObject = map[int16]string{
	1: "AllowListId",
	2: "AllowListName",
	3: "AllowListDesc",
	4: "AllowListType",
	5: "AllowListIPNum",
	6: "AssociatedInstanceNum",
}

func (p *AllowListObject) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllowListObject")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllowListObject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AllowListObject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *AllowListObject) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *AllowListObject) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *AllowListObject) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListType = _field
	return nil
}
func (p *AllowListObject) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListIPNum = _field
	return nil
}
func (p *AllowListObject) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AssociatedInstanceNum = _field
	return nil
}

func (p *AllowListObject) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllowListObject")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllowListObject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllowListObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllowListObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllowListObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AllowListObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AllowListObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIPNum", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AllowListIPNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AllowListObject) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AssociatedInstanceNum", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AssociatedInstanceNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AllowListObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllowListObject(%+v)", *p)

}

func (p *AllowListObject) DeepEqual(ano *AllowListObject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AllowListIPNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.AssociatedInstanceNum) {
		return false
	}
	return true
}

func (p *AllowListObject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AllowListDesc, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AllowListType, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field5DeepEqual(src int32) bool {

	if p.AllowListIPNum != src {
		return false
	}
	return true
}
func (p *AllowListObject) Field6DeepEqual(src int32) bool {

	if p.AssociatedInstanceNum != src {
		return false
	}
	return true
}

type DescribeAllowListsResp struct {
	AllowLists []*AllowListObject `thrift:"AllowLists,1" frugal:"1,default,list<AllowListObject>" json:"AllowLists"`
}

func NewDescribeAllowListsResp() *DescribeAllowListsResp {
	return &DescribeAllowListsResp{}
}

func (p *DescribeAllowListsResp) InitDefault() {
}

func (p *DescribeAllowListsResp) GetAllowLists() (v []*AllowListObject) {
	return p.AllowLists
}
func (p *DescribeAllowListsResp) SetAllowLists(val []*AllowListObject) {
	p.AllowLists = val
}

var fieldIDToName_DescribeAllowListsResp = map[int16]string{
	1: "AllowLists",
}

func (p *DescribeAllowListsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAllowListsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AllowListObject, 0, size)
	values := make([]AllowListObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowLists = _field
	return nil
}

func (p *DescribeAllowListsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowLists", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AllowLists)); err != nil {
		return err
	}
	for _, v := range p.AllowLists {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListsResp(%+v)", *p)

}

func (p *DescribeAllowListsResp) DeepEqual(ano *DescribeAllowListsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowLists) {
		return false
	}
	return true
}

func (p *DescribeAllowListsResp) Field1DeepEqual(src []*AllowListObject) bool {

	if len(p.AllowLists) != len(src) {
		return false
	}
	for i, v := range p.AllowLists {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeAllowListDetailReq struct {
	AllowListId string `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeAllowListDetailReq() *DescribeAllowListDetailReq {
	return &DescribeAllowListDetailReq{}
}

func (p *DescribeAllowListDetailReq) InitDefault() {
}

func (p *DescribeAllowListDetailReq) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *DescribeAllowListDetailReq) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_DescribeAllowListDetailReq = map[int16]string{
	1: "AllowListId",
}

func (p *DescribeAllowListDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListDetailReq[fieldId]))
}

func (p *DescribeAllowListDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *DescribeAllowListDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListDetailReq(%+v)", *p)

}

func (p *DescribeAllowListDetailReq) DeepEqual(ano *DescribeAllowListDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *DescribeAllowListDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type AssociatedInstanceObject struct {
	InstanceId   string `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	InstanceName string `thrift:"InstanceName,2" frugal:"2,default,string" json:"InstanceName"`
	VPC          string `thrift:"VPC,3" frugal:"3,default,string" json:"VPC"`
}

func NewAssociatedInstanceObject() *AssociatedInstanceObject {
	return &AssociatedInstanceObject{}
}

func (p *AssociatedInstanceObject) InitDefault() {
}

func (p *AssociatedInstanceObject) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AssociatedInstanceObject) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *AssociatedInstanceObject) GetVPC() (v string) {
	return p.VPC
}
func (p *AssociatedInstanceObject) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AssociatedInstanceObject) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *AssociatedInstanceObject) SetVPC(val string) {
	p.VPC = val
}

var fieldIDToName_AssociatedInstanceObject = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "VPC",
}

func (p *AssociatedInstanceObject) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociatedInstanceObject")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssociatedInstanceObject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssociatedInstanceObject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VPC = _field
	return nil
}

func (p *AssociatedInstanceObject) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociatedInstanceObject")

	var fieldId int16
	if err = oprot.WriteStructBegin("AssociatedInstanceObject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VPC", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VPC); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AssociatedInstanceObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociatedInstanceObject(%+v)", *p)

}

func (p *AssociatedInstanceObject) DeepEqual(ano *AssociatedInstanceObject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.VPC) {
		return false
	}
	return true
}

func (p *AssociatedInstanceObject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field3DeepEqual(src string) bool {

	if strings.Compare(p.VPC, src) != 0 {
		return false
	}
	return true
}

type DescribeAllowListDetailResp struct {
	AllowListId         string                      `thrift:"AllowListId,1" frugal:"1,default,string" json:"AllowListId"`
	AllowListName       string                      `thrift:"AllowListName,2" frugal:"2,default,string" json:"AllowListName"`
	AllowListDesc       string                      `thrift:"AllowListDesc,3" frugal:"3,default,string" json:"AllowListDesc"`
	AllowListType       string                      `thrift:"AllowListType,4" frugal:"4,default,string" json:"AllowListType"`
	AllowList           string                      `thrift:"AllowList,5" frugal:"5,default,string" json:"AllowList"`
	AssociatedInstances []*AssociatedInstanceObject `thrift:"AssociatedInstances,6" frugal:"6,default,list<AssociatedInstanceObject>" json:"AssociatedInstances"`
}

func NewDescribeAllowListDetailResp() *DescribeAllowListDetailResp {
	return &DescribeAllowListDetailResp{}
}

func (p *DescribeAllowListDetailResp) InitDefault() {
}

func (p *DescribeAllowListDetailResp) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *DescribeAllowListDetailResp) GetAllowListName() (v string) {
	return p.AllowListName
}

func (p *DescribeAllowListDetailResp) GetAllowListDesc() (v string) {
	return p.AllowListDesc
}

func (p *DescribeAllowListDetailResp) GetAllowListType() (v string) {
	return p.AllowListType
}

func (p *DescribeAllowListDetailResp) GetAllowList() (v string) {
	return p.AllowList
}

func (p *DescribeAllowListDetailResp) GetAssociatedInstances() (v []*AssociatedInstanceObject) {
	return p.AssociatedInstances
}
func (p *DescribeAllowListDetailResp) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *DescribeAllowListDetailResp) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *DescribeAllowListDetailResp) SetAllowListDesc(val string) {
	p.AllowListDesc = val
}
func (p *DescribeAllowListDetailResp) SetAllowListType(val string) {
	p.AllowListType = val
}
func (p *DescribeAllowListDetailResp) SetAllowList(val string) {
	p.AllowList = val
}
func (p *DescribeAllowListDetailResp) SetAssociatedInstances(val []*AssociatedInstanceObject) {
	p.AssociatedInstances = val
}

var fieldIDToName_DescribeAllowListDetailResp = map[int16]string{
	1: "AllowListId",
	2: "AllowListName",
	3: "AllowListDesc",
	4: "AllowListType",
	5: "AllowList",
	6: "AssociatedInstances",
}

func (p *DescribeAllowListDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListType = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowList = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AssociatedInstanceObject, 0, size)
	values := make([]AssociatedInstanceObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AssociatedInstances = _field
	return nil
}

func (p *DescribeAllowListDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowList); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AssociatedInstances", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AssociatedInstances)); err != nil {
		return err
	}
	for _, v := range p.AssociatedInstances {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListDetailResp(%+v)", *p)

}

func (p *DescribeAllowListDetailResp) DeepEqual(ano *DescribeAllowListDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AllowList) {
		return false
	}
	if !p.Field6DeepEqual(ano.AssociatedInstances) {
		return false
	}
	return true
}

func (p *DescribeAllowListDetailResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AllowListDesc, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AllowListType, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.AllowList, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field6DeepEqual(src []*AssociatedInstanceObject) bool {

	if len(p.AssociatedInstances) != len(src) {
		return false
	}
	for i, v := range p.AssociatedInstances {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateAllowListReq struct {
	AllowListName string  `thrift:"AllowListName,1,required" frugal:"1,required,string" validate:"required"`
	AllowListDesc *string `thrift:"AllowListDesc,2,optional" frugal:"2,optional,string" json:"AllowListDesc,omitempty"`
	AllowListType *string `thrift:"AllowListType,3,optional" frugal:"3,optional,string" json:"AllowListType,omitempty"`
	AllowList     string  `thrift:"AllowList,4,required" frugal:"4,required,string" validate:"required"`
}

func NewCreateAllowListReq() *CreateAllowListReq {
	return &CreateAllowListReq{}
}

func (p *CreateAllowListReq) InitDefault() {
}

func (p *CreateAllowListReq) GetAllowListName() (v string) {
	return p.AllowListName
}

var CreateAllowListReq_AllowListDesc_DEFAULT string

func (p *CreateAllowListReq) GetAllowListDesc() (v string) {
	if !p.IsSetAllowListDesc() {
		return CreateAllowListReq_AllowListDesc_DEFAULT
	}
	return *p.AllowListDesc
}

var CreateAllowListReq_AllowListType_DEFAULT string

func (p *CreateAllowListReq) GetAllowListType() (v string) {
	if !p.IsSetAllowListType() {
		return CreateAllowListReq_AllowListType_DEFAULT
	}
	return *p.AllowListType
}

func (p *CreateAllowListReq) GetAllowList() (v string) {
	return p.AllowList
}
func (p *CreateAllowListReq) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *CreateAllowListReq) SetAllowListDesc(val *string) {
	p.AllowListDesc = val
}
func (p *CreateAllowListReq) SetAllowListType(val *string) {
	p.AllowListType = val
}
func (p *CreateAllowListReq) SetAllowList(val string) {
	p.AllowList = val
}

var fieldIDToName_CreateAllowListReq = map[int16]string{
	1: "AllowListName",
	2: "AllowListDesc",
	3: "AllowListType",
	4: "AllowList",
}

func (p *CreateAllowListReq) IsSetAllowListDesc() bool {
	return p.AllowListDesc != nil
}

func (p *CreateAllowListReq) IsSetAllowListType() bool {
	return p.AllowListType != nil
}

func (p *CreateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListName bool = false
	var issetAllowList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowList {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAllowListReq[fieldId]))
}

func (p *CreateAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *CreateAllowListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *CreateAllowListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowListType = _field
	return nil
}
func (p *CreateAllowListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowList = _field
	return nil
}

func (p *CreateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListDesc() {
		if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowListDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListType() {
		if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowListType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowList); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAllowListReq(%+v)", *p)

}

func (p *CreateAllowListReq) DeepEqual(ano *CreateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowList) {
		return false
	}
	return true
}

func (p *CreateAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field2DeepEqual(src *string) bool {

	if p.AllowListDesc == src {
		return true
	} else if p.AllowListDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowListDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field3DeepEqual(src *string) bool {

	if p.AllowListType == src {
		return true
	} else if p.AllowListType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowListType, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AllowList, src) != 0 {
		return false
	}
	return true
}

type CreateAllowListResp struct {
	AllowListId string `thrift:"AllowListId,1" frugal:"1,default,string" json:"AllowListId"`
}

func NewCreateAllowListResp() *CreateAllowListResp {
	return &CreateAllowListResp{}
}

func (p *CreateAllowListResp) InitDefault() {
}

func (p *CreateAllowListResp) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *CreateAllowListResp) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_CreateAllowListResp = map[int16]string{
	1: "AllowListId",
}

func (p *CreateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAllowListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateAllowListResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *CreateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAllowListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAllowListResp(%+v)", *p)

}

func (p *CreateAllowListResp) DeepEqual(ano *CreateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *CreateAllowListResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type ModifyAllowListReq struct {
	AllowListId      string  `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
	AllowListName    string  `thrift:"AllowListName,2,required" frugal:"2,required,string" validate:"required"`
	AllowListDesc    *string `thrift:"AllowListDesc,3,optional" frugal:"3,optional,string" json:"AllowListDesc,omitempty"`
	AllowList        *string `thrift:"AllowList,4,optional" frugal:"4,optional,string" json:"AllowList,omitempty"`
	ModifyMode       *string `thrift:"ModifyMode,5,optional" frugal:"5,optional,string" json:"ModifyMode,omitempty"`
	ApplyInstanceNum *int32  `thrift:"ApplyInstanceNum,6,optional" frugal:"6,optional,i32" json:"ApplyInstanceNum,omitempty"`
}

func NewModifyAllowListReq() *ModifyAllowListReq {
	return &ModifyAllowListReq{}
}

func (p *ModifyAllowListReq) InitDefault() {
}

func (p *ModifyAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *ModifyAllowListReq) GetAllowListName() (v string) {
	return p.AllowListName
}

var ModifyAllowListReq_AllowListDesc_DEFAULT string

func (p *ModifyAllowListReq) GetAllowListDesc() (v string) {
	if !p.IsSetAllowListDesc() {
		return ModifyAllowListReq_AllowListDesc_DEFAULT
	}
	return *p.AllowListDesc
}

var ModifyAllowListReq_AllowList_DEFAULT string

func (p *ModifyAllowListReq) GetAllowList() (v string) {
	if !p.IsSetAllowList() {
		return ModifyAllowListReq_AllowList_DEFAULT
	}
	return *p.AllowList
}

var ModifyAllowListReq_ModifyMode_DEFAULT string

func (p *ModifyAllowListReq) GetModifyMode() (v string) {
	if !p.IsSetModifyMode() {
		return ModifyAllowListReq_ModifyMode_DEFAULT
	}
	return *p.ModifyMode
}

var ModifyAllowListReq_ApplyInstanceNum_DEFAULT int32

func (p *ModifyAllowListReq) GetApplyInstanceNum() (v int32) {
	if !p.IsSetApplyInstanceNum() {
		return ModifyAllowListReq_ApplyInstanceNum_DEFAULT
	}
	return *p.ApplyInstanceNum
}
func (p *ModifyAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *ModifyAllowListReq) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *ModifyAllowListReq) SetAllowListDesc(val *string) {
	p.AllowListDesc = val
}
func (p *ModifyAllowListReq) SetAllowList(val *string) {
	p.AllowList = val
}
func (p *ModifyAllowListReq) SetModifyMode(val *string) {
	p.ModifyMode = val
}
func (p *ModifyAllowListReq) SetApplyInstanceNum(val *int32) {
	p.ApplyInstanceNum = val
}

var fieldIDToName_ModifyAllowListReq = map[int16]string{
	1: "AllowListId",
	2: "AllowListName",
	3: "AllowListDesc",
	4: "AllowList",
	5: "ModifyMode",
	6: "ApplyInstanceNum",
}

func (p *ModifyAllowListReq) IsSetAllowListDesc() bool {
	return p.AllowListDesc != nil
}

func (p *ModifyAllowListReq) IsSetAllowList() bool {
	return p.AllowList != nil
}

func (p *ModifyAllowListReq) IsSetModifyMode() bool {
	return p.ModifyMode != nil
}

func (p *ModifyAllowListReq) IsSetApplyInstanceNum() bool {
	return p.ApplyInstanceNum != nil
}

func (p *ModifyAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false
	var issetAllowListName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyAllowListReq[fieldId]))
}

func (p *ModifyAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowList = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModifyMode = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApplyInstanceNum = _field
	return nil
}

func (p *ModifyAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListDesc() {
		if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowListDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowList() {
		if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetModifyMode() {
		if err = oprot.WriteFieldBegin("ModifyMode", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ModifyMode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetApplyInstanceNum() {
		if err = oprot.WriteFieldBegin("ApplyInstanceNum", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ApplyInstanceNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAllowListReq(%+v)", *p)

}

func (p *ModifyAllowListReq) DeepEqual(ano *ModifyAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowList) {
		return false
	}
	if !p.Field5DeepEqual(ano.ModifyMode) {
		return false
	}
	if !p.Field6DeepEqual(ano.ApplyInstanceNum) {
		return false
	}
	return true
}

func (p *ModifyAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field3DeepEqual(src *string) bool {

	if p.AllowListDesc == src {
		return true
	} else if p.AllowListDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowListDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field4DeepEqual(src *string) bool {

	if p.AllowList == src {
		return true
	} else if p.AllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field5DeepEqual(src *string) bool {

	if p.ModifyMode == src {
		return true
	} else if p.ModifyMode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ModifyMode, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field6DeepEqual(src *int32) bool {

	if p.ApplyInstanceNum == src {
		return true
	} else if p.ApplyInstanceNum == nil || src == nil {
		return false
	}
	if *p.ApplyInstanceNum != *src {
		return false
	}
	return true
}

type ModifyAllowListResp struct {
}

func NewModifyAllowListResp() *ModifyAllowListResp {
	return &ModifyAllowListResp{}
}

func (p *ModifyAllowListResp) InitDefault() {
}

var fieldIDToName_ModifyAllowListResp = map[int16]string{}

func (p *ModifyAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListResp")

	if err = oprot.WriteStructBegin("ModifyAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAllowListResp(%+v)", *p)

}

func (p *ModifyAllowListResp) DeepEqual(ano *ModifyAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteAllowListReq struct {
	AllowListId string `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDeleteAllowListReq() *DeleteAllowListReq {
	return &DeleteAllowListReq{}
}

func (p *DeleteAllowListReq) InitDefault() {
}

func (p *DeleteAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *DeleteAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_DeleteAllowListReq = map[int16]string{
	1: "AllowListId",
}

func (p *DeleteAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteAllowListReq[fieldId]))
}

func (p *DeleteAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *DeleteAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAllowListReq(%+v)", *p)

}

func (p *DeleteAllowListReq) DeepEqual(ano *DeleteAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *DeleteAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type DeleteAllowListResp struct {
}

func NewDeleteAllowListResp() *DeleteAllowListResp {
	return &DeleteAllowListResp{}
}

func (p *DeleteAllowListResp) InitDefault() {
}

var fieldIDToName_DeleteAllowListResp = map[int16]string{}

func (p *DeleteAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListResp")

	if err = oprot.WriteStructBegin("DeleteAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAllowListResp(%+v)", *p)

}

func (p *DeleteAllowListResp) DeepEqual(ano *DeleteAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type AssociateAllowListReq struct {
	InstanceIds  []string `thrift:"InstanceIds,1,required" frugal:"1,required,list<string>" validate:"required"`
	AllowListIds []string `thrift:"AllowListIds,2,required" frugal:"2,required,list<string>" validate:"required"`
}

func NewAssociateAllowListReq() *AssociateAllowListReq {
	return &AssociateAllowListReq{}
}

func (p *AssociateAllowListReq) InitDefault() {
}

func (p *AssociateAllowListReq) GetInstanceIds() (v []string) {
	return p.InstanceIds
}

func (p *AssociateAllowListReq) GetAllowListIds() (v []string) {
	return p.AllowListIds
}
func (p *AssociateAllowListReq) SetInstanceIds(val []string) {
	p.InstanceIds = val
}
func (p *AssociateAllowListReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_AssociateAllowListReq = map[int16]string{
	1: "InstanceIds",
	2: "AllowListIds",
}

func (p *AssociateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceIds bool = false
	var issetAllowListIds bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceIds {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListIds {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssociateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AssociateAllowListReq[fieldId]))
}

func (p *AssociateAllowListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceIds = _field
	return nil
}
func (p *AssociateAllowListReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *AssociateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AssociateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceIds", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceIds)); err != nil {
		return err
	}
	for _, v := range p.InstanceIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssociateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
		return err
	}
	for _, v := range p.AllowListIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AssociateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociateAllowListReq(%+v)", *p)

}

func (p *AssociateAllowListReq) DeepEqual(ano *AssociateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceIds) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *AssociateAllowListReq) Field1DeepEqual(src []string) bool {

	if len(p.InstanceIds) != len(src) {
		return false
	}
	for i, v := range p.InstanceIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *AssociateAllowListReq) Field2DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type AssociateAllowListResp struct {
}

func NewAssociateAllowListResp() *AssociateAllowListResp {
	return &AssociateAllowListResp{}
}

func (p *AssociateAllowListResp) InitDefault() {
}

var fieldIDToName_AssociateAllowListResp = map[int16]string{}

func (p *AssociateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssociateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListResp")

	if err = oprot.WriteStructBegin("AssociateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociateAllowListResp(%+v)", *p)

}

func (p *AssociateAllowListResp) DeepEqual(ano *AssociateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DisassociateAllowListReq struct {
	InstanceIds  []string `thrift:"InstanceIds,1,required" frugal:"1,required,list<string>" validate:"required"`
	AllowListIds []string `thrift:"AllowListIds,2,required" frugal:"2,required,list<string>" validate:"required"`
}

func NewDisassociateAllowListReq() *DisassociateAllowListReq {
	return &DisassociateAllowListReq{}
}

func (p *DisassociateAllowListReq) InitDefault() {
}

func (p *DisassociateAllowListReq) GetInstanceIds() (v []string) {
	return p.InstanceIds
}

func (p *DisassociateAllowListReq) GetAllowListIds() (v []string) {
	return p.AllowListIds
}
func (p *DisassociateAllowListReq) SetInstanceIds(val []string) {
	p.InstanceIds = val
}
func (p *DisassociateAllowListReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_DisassociateAllowListReq = map[int16]string{
	1: "InstanceIds",
	2: "AllowListIds",
}

func (p *DisassociateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceIds bool = false
	var issetAllowListIds bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceIds {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListIds {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DisassociateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DisassociateAllowListReq[fieldId]))
}

func (p *DisassociateAllowListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceIds = _field
	return nil
}
func (p *DisassociateAllowListReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *DisassociateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DisassociateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisassociateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceIds", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceIds)); err != nil {
		return err
	}
	for _, v := range p.InstanceIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DisassociateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
		return err
	}
	for _, v := range p.AllowListIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DisassociateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisassociateAllowListReq(%+v)", *p)

}

func (p *DisassociateAllowListReq) DeepEqual(ano *DisassociateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceIds) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *DisassociateAllowListReq) Field1DeepEqual(src []string) bool {

	if len(p.InstanceIds) != len(src) {
		return false
	}
	for i, v := range p.InstanceIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DisassociateAllowListReq) Field2DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DisassociateAllowListResp struct {
}

func NewDisassociateAllowListResp() *DisassociateAllowListResp {
	return &DisassociateAllowListResp{}
}

func (p *DisassociateAllowListResp) InitDefault() {
}

var fieldIDToName_DisassociateAllowListResp = map[int16]string{}

func (p *DisassociateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisassociateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListResp")

	if err = oprot.WriteStructBegin("DisassociateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisassociateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisassociateAllowListResp(%+v)", *p)

}

func (p *DisassociateAllowListResp) DeepEqual(ano *DisassociateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

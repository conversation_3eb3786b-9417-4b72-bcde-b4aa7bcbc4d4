// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ListEventsReq) IsValid() error {
	return nil
}
func (p *ListEventsResp) IsValid() error {
	return nil
}
func (p *ListHAChangeLogsReq) IsValid() error {
	return nil
}
func (p *ListHAChangeLogsResp) IsValid() error {
	return nil
}
func (p *ListLogsReq) IsValid() error {
	return nil
}
func (p *ListLogsResp) IsValid() error {
	return nil
}
func (p *DownloadLogReq) IsValid() error {
	return nil
}
func (p *DescribeAuditLogConfigReq) IsValid() error {
	return nil
}
func (p *DescribeAuditLogConfigResp) IsValid() error {
	return nil
}
func (p *ModifyAuditLogConfigReq) IsValid() error {
	return nil
}
func (p *ApplyDownloadLogUrlReq) IsValid() error {
	return nil
}
func (p *ApplyDownloadLogUrlResp) IsValid() error {
	return nil
}
func (p *ModifyEventStatusReq) IsValid() error {
	return nil
}
func (p *DeleteEventReq) IsValid() error {
	return nil
}

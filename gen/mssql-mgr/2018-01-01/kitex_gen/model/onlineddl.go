// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateFreeLockCorrectOrderReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	TableName   string `thrift:"TableName,2" frugal:"2,default,string" json:"TableName"`
	DBName      string `thrift:"DBName,3,required" frugal:"3,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,4" frugal:"4,default,string" json:"AccountName"`
	ExecSQL     string `thrift:"ExecSQL,5,required" frugal:"5,required,string" validate:"required"`
	Comment     string `thrift:"Comment,6" frugal:"6,default,string" json:"Comment"`
}

func NewCreateFreeLockCorrectOrderReq() *CreateFreeLockCorrectOrderReq {
	return &CreateFreeLockCorrectOrderReq{}
}

func (p *CreateFreeLockCorrectOrderReq) InitDefault() {
}

func (p *CreateFreeLockCorrectOrderReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateFreeLockCorrectOrderReq) GetTableName() (v string) {
	return p.TableName
}

func (p *CreateFreeLockCorrectOrderReq) GetDBName() (v string) {
	return p.DBName
}

func (p *CreateFreeLockCorrectOrderReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *CreateFreeLockCorrectOrderReq) GetExecSQL() (v string) {
	return p.ExecSQL
}

func (p *CreateFreeLockCorrectOrderReq) GetComment() (v string) {
	return p.Comment
}
func (p *CreateFreeLockCorrectOrderReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateFreeLockCorrectOrderReq) SetTableName(val string) {
	p.TableName = val
}
func (p *CreateFreeLockCorrectOrderReq) SetDBName(val string) {
	p.DBName = val
}
func (p *CreateFreeLockCorrectOrderReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *CreateFreeLockCorrectOrderReq) SetExecSQL(val string) {
	p.ExecSQL = val
}
func (p *CreateFreeLockCorrectOrderReq) SetComment(val string) {
	p.Comment = val
}

var fieldIDToName_CreateFreeLockCorrectOrderReq = map[int16]string{
	1: "InstanceId",
	2: "TableName",
	3: "DBName",
	4: "AccountName",
	5: "ExecSQL",
	6: "Comment",
}

func (p *CreateFreeLockCorrectOrderReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFreeLockCorrectOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false
	var issetExecSQL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecSQL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetExecSQL {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateFreeLockCorrectOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateFreeLockCorrectOrderReq[fieldId]))
}

func (p *CreateFreeLockCorrectOrderReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateFreeLockCorrectOrderReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *CreateFreeLockCorrectOrderReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *CreateFreeLockCorrectOrderReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *CreateFreeLockCorrectOrderReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecSQL = _field
	return nil
}
func (p *CreateFreeLockCorrectOrderReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Comment = _field
	return nil
}

func (p *CreateFreeLockCorrectOrderReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFreeLockCorrectOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateFreeLockCorrectOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecSQL", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecSQL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Comment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFreeLockCorrectOrderReq(%+v)", *p)

}

func (p *CreateFreeLockCorrectOrderReq) DeepEqual(ano *CreateFreeLockCorrectOrderReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExecSQL) {
		return false
	}
	if !p.Field6DeepEqual(ano.Comment) {
		return false
	}
	return true
}

func (p *CreateFreeLockCorrectOrderReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFreeLockCorrectOrderReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFreeLockCorrectOrderReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFreeLockCorrectOrderReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFreeLockCorrectOrderReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ExecSQL, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFreeLockCorrectOrderReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Comment, src) != 0 {
		return false
	}
	return true
}

type CreateFreeLockCorrectOrderResp struct {
	OrderId string `thrift:"OrderId,1,required" frugal:"1,required,string" json:"OrderId"`
}

func NewCreateFreeLockCorrectOrderResp() *CreateFreeLockCorrectOrderResp {
	return &CreateFreeLockCorrectOrderResp{}
}

func (p *CreateFreeLockCorrectOrderResp) InitDefault() {
}

func (p *CreateFreeLockCorrectOrderResp) GetOrderId() (v string) {
	return p.OrderId
}
func (p *CreateFreeLockCorrectOrderResp) SetOrderId(val string) {
	p.OrderId = val
}

var fieldIDToName_CreateFreeLockCorrectOrderResp = map[int16]string{
	1: "OrderId",
}

func (p *CreateFreeLockCorrectOrderResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFreeLockCorrectOrderResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateFreeLockCorrectOrderResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateFreeLockCorrectOrderResp[fieldId]))
}

func (p *CreateFreeLockCorrectOrderResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}

func (p *CreateFreeLockCorrectOrderResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFreeLockCorrectOrderResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateFreeLockCorrectOrderResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateFreeLockCorrectOrderResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFreeLockCorrectOrderResp(%+v)", *p)

}

func (p *CreateFreeLockCorrectOrderResp) DeepEqual(ano *CreateFreeLockCorrectOrderResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.OrderId) {
		return false
	}
	return true
}

func (p *CreateFreeLockCorrectOrderResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}

type ListFreeLockCorrectOrdersReq struct {
	InstanceId      string       `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	OrderId         string       `thrift:"OrderId,2" frugal:"2,default,string" json:"OrderId"`
	OrderStatus     *OrderStatus `thrift:"OrderStatus,3,optional" frugal:"3,optional,OrderStatus" json:"OrderStatus,omitempty"`
	CreateTimeStart string       `thrift:"CreateTimeStart,4" frugal:"4,default,string" json:"CreateTimeStart"`
	CreateTimeEnd   string       `thrift:"CreateTimeEnd,5" frugal:"5,default,string" json:"CreateTimeEnd"`
	Offset          int32        `thrift:"Offset,6,required" frugal:"6,required,i32" json:"Offset"`
	Limit           int32        `thrift:"Limit,7,required" frugal:"7,required,i32" json:"Limit"`
}

func NewListFreeLockCorrectOrdersReq() *ListFreeLockCorrectOrdersReq {
	return &ListFreeLockCorrectOrdersReq{}
}

func (p *ListFreeLockCorrectOrdersReq) InitDefault() {
}

func (p *ListFreeLockCorrectOrdersReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListFreeLockCorrectOrdersReq) GetOrderId() (v string) {
	return p.OrderId
}

var ListFreeLockCorrectOrdersReq_OrderStatus_DEFAULT OrderStatus

func (p *ListFreeLockCorrectOrdersReq) GetOrderStatus() (v OrderStatus) {
	if !p.IsSetOrderStatus() {
		return ListFreeLockCorrectOrdersReq_OrderStatus_DEFAULT
	}
	return *p.OrderStatus
}

func (p *ListFreeLockCorrectOrdersReq) GetCreateTimeStart() (v string) {
	return p.CreateTimeStart
}

func (p *ListFreeLockCorrectOrdersReq) GetCreateTimeEnd() (v string) {
	return p.CreateTimeEnd
}

func (p *ListFreeLockCorrectOrdersReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListFreeLockCorrectOrdersReq) GetLimit() (v int32) {
	return p.Limit
}
func (p *ListFreeLockCorrectOrdersReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListFreeLockCorrectOrdersReq) SetOrderId(val string) {
	p.OrderId = val
}
func (p *ListFreeLockCorrectOrdersReq) SetOrderStatus(val *OrderStatus) {
	p.OrderStatus = val
}
func (p *ListFreeLockCorrectOrdersReq) SetCreateTimeStart(val string) {
	p.CreateTimeStart = val
}
func (p *ListFreeLockCorrectOrdersReq) SetCreateTimeEnd(val string) {
	p.CreateTimeEnd = val
}
func (p *ListFreeLockCorrectOrdersReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListFreeLockCorrectOrdersReq) SetLimit(val int32) {
	p.Limit = val
}

var fieldIDToName_ListFreeLockCorrectOrdersReq = map[int16]string{
	1: "InstanceId",
	2: "OrderId",
	3: "OrderStatus",
	4: "CreateTimeStart",
	5: "CreateTimeEnd",
	6: "Offset",
	7: "Limit",
}

func (p *ListFreeLockCorrectOrdersReq) IsSetOrderStatus() bool {
	return p.OrderStatus != nil
}

func (p *ListFreeLockCorrectOrdersReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListFreeLockCorrectOrdersReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListFreeLockCorrectOrdersReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListFreeLockCorrectOrdersReq[fieldId]))
}

func (p *ListFreeLockCorrectOrdersReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *OrderStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderStatus(v)
		_field = &tmp
	}
	p.OrderStatus = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTimeStart = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTimeEnd = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}

func (p *ListFreeLockCorrectOrdersReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListFreeLockCorrectOrdersReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListFreeLockCorrectOrdersReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderStatus() {
		if err = oprot.WriteFieldBegin("OrderStatus", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTimeStart", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTimeStart); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTimeEnd", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTimeEnd); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListFreeLockCorrectOrdersReq(%+v)", *p)

}

func (p *ListFreeLockCorrectOrdersReq) DeepEqual(ano *ListFreeLockCorrectOrdersReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderId) {
		return false
	}
	if !p.Field3DeepEqual(ano.OrderStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateTimeStart) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateTimeEnd) {
		return false
	}
	if !p.Field6DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field7DeepEqual(ano.Limit) {
		return false
	}
	return true
}

func (p *ListFreeLockCorrectOrdersReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field3DeepEqual(src *OrderStatus) bool {

	if p.OrderStatus == src {
		return true
	} else if p.OrderStatus == nil || src == nil {
		return false
	}
	if *p.OrderStatus != *src {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CreateTimeStart, src) != 0 {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CreateTimeEnd, src) != 0 {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field6DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersReq) Field7DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}

type ListFreeLockCorrectOrdersResp struct {
	Total int32                   `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*FreeLockCorrectOrder `thrift:"Datas,2,required" frugal:"2,required,list<FreeLockCorrectOrder>" json:"Datas"`
}

func NewListFreeLockCorrectOrdersResp() *ListFreeLockCorrectOrdersResp {
	return &ListFreeLockCorrectOrdersResp{}
}

func (p *ListFreeLockCorrectOrdersResp) InitDefault() {
}

func (p *ListFreeLockCorrectOrdersResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListFreeLockCorrectOrdersResp) GetDatas() (v []*FreeLockCorrectOrder) {
	return p.Datas
}
func (p *ListFreeLockCorrectOrdersResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListFreeLockCorrectOrdersResp) SetDatas(val []*FreeLockCorrectOrder) {
	p.Datas = val
}

var fieldIDToName_ListFreeLockCorrectOrdersResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListFreeLockCorrectOrdersResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListFreeLockCorrectOrdersResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListFreeLockCorrectOrdersResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListFreeLockCorrectOrdersResp[fieldId]))
}

func (p *ListFreeLockCorrectOrdersResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListFreeLockCorrectOrdersResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FreeLockCorrectOrder, 0, size)
	values := make([]FreeLockCorrectOrder, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListFreeLockCorrectOrdersResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListFreeLockCorrectOrdersResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListFreeLockCorrectOrdersResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListFreeLockCorrectOrdersResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListFreeLockCorrectOrdersResp(%+v)", *p)

}

func (p *ListFreeLockCorrectOrdersResp) DeepEqual(ano *ListFreeLockCorrectOrdersResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListFreeLockCorrectOrdersResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListFreeLockCorrectOrdersResp) Field2DeepEqual(src []*FreeLockCorrectOrder) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type StopFreeLockCorrectOrderReq struct {
	InstanceId string `thrift:"InstanceId,1" frugal:"1,default,string" validate:"required"`
	OrderId    string `thrift:"OrderId,2" frugal:"2,default,string" validate:"required"`
}

func NewStopFreeLockCorrectOrderReq() *StopFreeLockCorrectOrderReq {
	return &StopFreeLockCorrectOrderReq{}
}

func (p *StopFreeLockCorrectOrderReq) InitDefault() {
}

func (p *StopFreeLockCorrectOrderReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *StopFreeLockCorrectOrderReq) GetOrderId() (v string) {
	return p.OrderId
}
func (p *StopFreeLockCorrectOrderReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *StopFreeLockCorrectOrderReq) SetOrderId(val string) {
	p.OrderId = val
}

var fieldIDToName_StopFreeLockCorrectOrderReq = map[int16]string{
	1: "InstanceId",
	2: "OrderId",
}

func (p *StopFreeLockCorrectOrderReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopFreeLockCorrectOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopFreeLockCorrectOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StopFreeLockCorrectOrderReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *StopFreeLockCorrectOrderReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}

func (p *StopFreeLockCorrectOrderReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopFreeLockCorrectOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopFreeLockCorrectOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopFreeLockCorrectOrderReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopFreeLockCorrectOrderReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StopFreeLockCorrectOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopFreeLockCorrectOrderReq(%+v)", *p)

}

func (p *StopFreeLockCorrectOrderReq) DeepEqual(ano *StopFreeLockCorrectOrderReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderId) {
		return false
	}
	return true
}

func (p *StopFreeLockCorrectOrderReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *StopFreeLockCorrectOrderReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}

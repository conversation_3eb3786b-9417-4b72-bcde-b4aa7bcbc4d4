// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateAccountReq struct {
	InstanceId      string      `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName     string      `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required,min=2,max=32"`
	AccountPassword string      `thrift:"AccountPassword,3,required" frugal:"3,required,string" validate:"required,min=8,max=32"`
	AccountDesc     string      `thrift:"AccountDesc,4" frugal:"4,default,string" json:"AccountDesc"`
	AccountType     AccountType `thrift:"AccountType,5,required" frugal:"5,required,AccountType" json:"AccountType"`
}

func NewCreateAccountReq() *CreateAccountReq {
	return &CreateAccountReq{}
}

func (p *CreateAccountReq) InitDefault() {
}

func (p *CreateAccountReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateAccountReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *CreateAccountReq) GetAccountPassword() (v string) {
	return p.AccountPassword
}

func (p *CreateAccountReq) GetAccountDesc() (v string) {
	return p.AccountDesc
}

func (p *CreateAccountReq) GetAccountType() (v AccountType) {
	return p.AccountType
}
func (p *CreateAccountReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateAccountReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *CreateAccountReq) SetAccountPassword(val string) {
	p.AccountPassword = val
}
func (p *CreateAccountReq) SetAccountDesc(val string) {
	p.AccountDesc = val
}
func (p *CreateAccountReq) SetAccountType(val AccountType) {
	p.AccountType = val
}

var fieldIDToName_CreateAccountReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccountPassword",
	4: "AccountDesc",
	5: "AccountType",
}

func (p *CreateAccountReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAccountReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetAccountPassword bool = false
	var issetAccountType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountPassword {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAccountType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAccountReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAccountReq[fieldId]))
}

func (p *CreateAccountReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateAccountReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *CreateAccountReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountPassword = _field
	return nil
}
func (p *CreateAccountReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountDesc = _field
	return nil
}
func (p *CreateAccountReq) ReadField5(iprot thrift.TProtocol) error {

	var _field AccountType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AccountType(v)
	}
	p.AccountType = _field
	return nil
}

func (p *CreateAccountReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAccountReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAccountReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAccountReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAccountReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateAccountReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPassword", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountPassword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateAccountReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountDesc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateAccountReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AccountType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateAccountReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAccountReq(%+v)", *p)

}

func (p *CreateAccountReq) DeepEqual(ano *CreateAccountReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountPassword) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountDesc) {
		return false
	}
	if !p.Field5DeepEqual(ano.AccountType) {
		return false
	}
	return true
}

func (p *CreateAccountReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAccountReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAccountReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountPassword, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAccountReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AccountDesc, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAccountReq) Field5DeepEqual(src AccountType) bool {

	if p.AccountType != src {
		return false
	}
	return true
}

type DeleteAccount struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteAccount() *DeleteAccount {
	return &DeleteAccount{}
}

func (p *DeleteAccount) InitDefault() {
}

func (p *DeleteAccount) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteAccount) GetAccountName() (v string) {
	return p.AccountName
}
func (p *DeleteAccount) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteAccount) SetAccountName(val string) {
	p.AccountName = val
}

var fieldIDToName_DeleteAccount = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
}

func (p *DeleteAccount) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAccount")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteAccount[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteAccount[fieldId]))
}

func (p *DeleteAccount) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteAccount) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}

func (p *DeleteAccount) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAccount")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteAccount"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAccount(%+v)", *p)

}

func (p *DeleteAccount) DeepEqual(ano *DeleteAccount) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	return true
}

func (p *DeleteAccount) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteAccount) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}

type GrantAccountPrivilegeReq struct {
	InstanceId          string           `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName         string           `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	DBName              string           `thrift:"DBName,3,required" frugal:"3,required,string" validate:"required"`
	AccountPrivilege    AccountPrivilege `thrift:"AccountPrivilege,4,required" frugal:"4,required,AccountPrivilege" json:"AccountPrivilege"`
	AccountPrivilegeStr string           `thrift:"AccountPrivilegeStr,5" frugal:"5,default,string" json:"AccountPrivilegeStr"`
	AccessIP            string           `thrift:"AccessIP,6" frugal:"6,default,string" json:"AccessIP"`
	TableNames          string           `thrift:"TableNames,7" frugal:"7,default,string" json:"TableNames"`
}

func NewGrantAccountPrivilegeReq() *GrantAccountPrivilegeReq {
	return &GrantAccountPrivilegeReq{}
}

func (p *GrantAccountPrivilegeReq) InitDefault() {
}

func (p *GrantAccountPrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GrantAccountPrivilegeReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *GrantAccountPrivilegeReq) GetDBName() (v string) {
	return p.DBName
}

func (p *GrantAccountPrivilegeReq) GetAccountPrivilege() (v AccountPrivilege) {
	return p.AccountPrivilege
}

func (p *GrantAccountPrivilegeReq) GetAccountPrivilegeStr() (v string) {
	return p.AccountPrivilegeStr
}

func (p *GrantAccountPrivilegeReq) GetAccessIP() (v string) {
	return p.AccessIP
}

func (p *GrantAccountPrivilegeReq) GetTableNames() (v string) {
	return p.TableNames
}
func (p *GrantAccountPrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GrantAccountPrivilegeReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *GrantAccountPrivilegeReq) SetDBName(val string) {
	p.DBName = val
}
func (p *GrantAccountPrivilegeReq) SetAccountPrivilege(val AccountPrivilege) {
	p.AccountPrivilege = val
}
func (p *GrantAccountPrivilegeReq) SetAccountPrivilegeStr(val string) {
	p.AccountPrivilegeStr = val
}
func (p *GrantAccountPrivilegeReq) SetAccessIP(val string) {
	p.AccessIP = val
}
func (p *GrantAccountPrivilegeReq) SetTableNames(val string) {
	p.TableNames = val
}

var fieldIDToName_GrantAccountPrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "DBName",
	4: "AccountPrivilege",
	5: "AccountPrivilegeStr",
	6: "AccessIP",
	7: "TableNames",
}

func (p *GrantAccountPrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantAccountPrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetDBName bool = false
	var issetAccountPrivilege bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPrivilege = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAccountPrivilege {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GrantAccountPrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GrantAccountPrivilegeReq[fieldId]))
}

func (p *GrantAccountPrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AccountPrivilege
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AccountPrivilege(v)
	}
	p.AccountPrivilege = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountPrivilegeStr = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccessIP = _field
	return nil
}
func (p *GrantAccountPrivilegeReq) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableNames = _field
	return nil
}

func (p *GrantAccountPrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantAccountPrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GrantAccountPrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPrivilege", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AccountPrivilege)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPrivilegeStr", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountPrivilegeStr); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccessIP", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccessIP); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableNames", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableNames); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *GrantAccountPrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GrantAccountPrivilegeReq(%+v)", *p)

}

func (p *GrantAccountPrivilegeReq) DeepEqual(ano *GrantAccountPrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountPrivilege) {
		return false
	}
	if !p.Field5DeepEqual(ano.AccountPrivilegeStr) {
		return false
	}
	if !p.Field6DeepEqual(ano.AccessIP) {
		return false
	}
	if !p.Field7DeepEqual(ano.TableNames) {
		return false
	}
	return true
}

func (p *GrantAccountPrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field4DeepEqual(src AccountPrivilege) bool {

	if p.AccountPrivilege != src {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.AccountPrivilegeStr, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.AccessIP, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegeReq) Field7DeepEqual(src string) bool {

	if strings.Compare(p.TableNames, src) != 0 {
		return false
	}
	return true
}

type GrantAccountPrivilegesReq struct {
	InstanceId  string                      `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string                      `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	AccessIP    string                      `thrift:"AccessIP,3" frugal:"3,default,string" json:"AccessIP"`
	Privileges  []*GrantAccountPrivilegeReq `thrift:"Privileges,4,required" frugal:"4,required,list<GrantAccountPrivilegeReq>" json:"Privileges"`
}

func NewGrantAccountPrivilegesReq() *GrantAccountPrivilegesReq {
	return &GrantAccountPrivilegesReq{}
}

func (p *GrantAccountPrivilegesReq) InitDefault() {
}

func (p *GrantAccountPrivilegesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GrantAccountPrivilegesReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *GrantAccountPrivilegesReq) GetAccessIP() (v string) {
	return p.AccessIP
}

func (p *GrantAccountPrivilegesReq) GetPrivileges() (v []*GrantAccountPrivilegeReq) {
	return p.Privileges
}
func (p *GrantAccountPrivilegesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GrantAccountPrivilegesReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *GrantAccountPrivilegesReq) SetAccessIP(val string) {
	p.AccessIP = val
}
func (p *GrantAccountPrivilegesReq) SetPrivileges(val []*GrantAccountPrivilegeReq) {
	p.Privileges = val
}

var fieldIDToName_GrantAccountPrivilegesReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccessIP",
	4: "Privileges",
}

func (p *GrantAccountPrivilegesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantAccountPrivilegesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetPrivileges bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrivileges = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPrivileges {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GrantAccountPrivilegesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GrantAccountPrivilegesReq[fieldId]))
}

func (p *GrantAccountPrivilegesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GrantAccountPrivilegesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *GrantAccountPrivilegesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccessIP = _field
	return nil
}
func (p *GrantAccountPrivilegesReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*GrantAccountPrivilegeReq, 0, size)
	values := make([]GrantAccountPrivilegeReq, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Privileges = _field
	return nil
}

func (p *GrantAccountPrivilegesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantAccountPrivilegesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GrantAccountPrivilegesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GrantAccountPrivilegesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GrantAccountPrivilegesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GrantAccountPrivilegesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccessIP", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccessIP); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GrantAccountPrivilegesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Privileges", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Privileges)); err != nil {
		return err
	}
	for _, v := range p.Privileges {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GrantAccountPrivilegesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GrantAccountPrivilegesReq(%+v)", *p)

}

func (p *GrantAccountPrivilegesReq) DeepEqual(ano *GrantAccountPrivilegesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccessIP) {
		return false
	}
	if !p.Field4DeepEqual(ano.Privileges) {
		return false
	}
	return true
}

func (p *GrantAccountPrivilegesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccessIP, src) != 0 {
		return false
	}
	return true
}
func (p *GrantAccountPrivilegesReq) Field4DeepEqual(src []*GrantAccountPrivilegeReq) bool {

	if len(p.Privileges) != len(src) {
		return false
	}
	for i, v := range p.Privileges {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RevokeAccountPrivilegeReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	DBNames     string `thrift:"DBNames,3,required" frugal:"3,required,string" validate:"required"`
}

func NewRevokeAccountPrivilegeReq() *RevokeAccountPrivilegeReq {
	return &RevokeAccountPrivilegeReq{}
}

func (p *RevokeAccountPrivilegeReq) InitDefault() {
}

func (p *RevokeAccountPrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RevokeAccountPrivilegeReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *RevokeAccountPrivilegeReq) GetDBNames() (v string) {
	return p.DBNames
}
func (p *RevokeAccountPrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RevokeAccountPrivilegeReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *RevokeAccountPrivilegeReq) SetDBNames(val string) {
	p.DBNames = val
}

var fieldIDToName_RevokeAccountPrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "DBNames",
}

func (p *RevokeAccountPrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeAccountPrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetDBNames bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBNames = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBNames {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RevokeAccountPrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RevokeAccountPrivilegeReq[fieldId]))
}

func (p *RevokeAccountPrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RevokeAccountPrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *RevokeAccountPrivilegeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBNames = _field
	return nil
}

func (p *RevokeAccountPrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeAccountPrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RevokeAccountPrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RevokeAccountPrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RevokeAccountPrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RevokeAccountPrivilegeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBNames", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBNames); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RevokeAccountPrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RevokeAccountPrivilegeReq(%+v)", *p)

}

func (p *RevokeAccountPrivilegeReq) DeepEqual(ano *RevokeAccountPrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBNames) {
		return false
	}
	return true
}

func (p *RevokeAccountPrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RevokeAccountPrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *RevokeAccountPrivilegeReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBNames, src) != 0 {
		return false
	}
	return true
}

type ResetAccountPasswordReq struct {
	InstanceId      string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName     string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	AccountPassword string `thrift:"AccountPassword,3,required" frugal:"3,required,string" validate:"required,min=8,max=32"`
}

func NewResetAccountPasswordReq() *ResetAccountPasswordReq {
	return &ResetAccountPasswordReq{}
}

func (p *ResetAccountPasswordReq) InitDefault() {
}

func (p *ResetAccountPasswordReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ResetAccountPasswordReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *ResetAccountPasswordReq) GetAccountPassword() (v string) {
	return p.AccountPassword
}
func (p *ResetAccountPasswordReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ResetAccountPasswordReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *ResetAccountPasswordReq) SetAccountPassword(val string) {
	p.AccountPassword = val
}

var fieldIDToName_ResetAccountPasswordReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccountPassword",
}

func (p *ResetAccountPasswordReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetAccountPasswordReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetAccountPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountPassword {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetAccountPasswordReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResetAccountPasswordReq[fieldId]))
}

func (p *ResetAccountPasswordReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ResetAccountPasswordReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *ResetAccountPasswordReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountPassword = _field
	return nil
}

func (p *ResetAccountPasswordReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetAccountPasswordReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetAccountPasswordReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResetAccountPasswordReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResetAccountPasswordReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ResetAccountPasswordReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPassword", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountPassword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ResetAccountPasswordReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetAccountPasswordReq(%+v)", *p)

}

func (p *ResetAccountPasswordReq) DeepEqual(ano *ResetAccountPasswordReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountPassword) {
		return false
	}
	return true
}

func (p *ResetAccountPasswordReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ResetAccountPasswordReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *ResetAccountPasswordReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountPassword, src) != 0 {
		return false
	}
	return true
}

type ListAccountsReq struct {
	Offset           int32  `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit            int32  `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId       string `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	AccountName      string `thrift:"AccountName,4" frugal:"4,default,string" json:"AccountName"`
	ExcludeSuperUser bool   `thrift:"ExcludeSuperUser,5" frugal:"5,default,bool" json:"ExcludeSuperUser"`
}

func NewListAccountsReq() *ListAccountsReq {
	return &ListAccountsReq{}
}

func (p *ListAccountsReq) InitDefault() {
}

func (p *ListAccountsReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListAccountsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListAccountsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListAccountsReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *ListAccountsReq) GetExcludeSuperUser() (v bool) {
	return p.ExcludeSuperUser
}
func (p *ListAccountsReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListAccountsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListAccountsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListAccountsReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *ListAccountsReq) SetExcludeSuperUser(val bool) {
	p.ExcludeSuperUser = val
}

var fieldIDToName_ListAccountsReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "AccountName",
	5: "ExcludeSuperUser",
}

func (p *ListAccountsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAccountsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAccountsReq[fieldId]))
}

func (p *ListAccountsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListAccountsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListAccountsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListAccountsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *ListAccountsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExcludeSuperUser = _field
	return nil
}

func (p *ListAccountsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAccountsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAccountsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAccountsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAccountsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListAccountsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListAccountsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExcludeSuperUser", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ExcludeSuperUser); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListAccountsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAccountsReq(%+v)", *p)

}

func (p *ListAccountsReq) DeepEqual(ano *ListAccountsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExcludeSuperUser) {
		return false
	}
	return true
}

func (p *ListAccountsReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListAccountsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListAccountsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListAccountsReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *ListAccountsReq) Field5DeepEqual(src bool) bool {

	if p.ExcludeSuperUser != src {
		return false
	}
	return true
}

type ListAccountsResp struct {
	Total int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*AccountInfo `thrift:"Datas,2,required" frugal:"2,required,list<AccountInfo>" json:"Datas"`
}

func NewListAccountsResp() *ListAccountsResp {
	return &ListAccountsResp{}
}

func (p *ListAccountsResp) InitDefault() {
}

func (p *ListAccountsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListAccountsResp) GetDatas() (v []*AccountInfo) {
	return p.Datas
}
func (p *ListAccountsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListAccountsResp) SetDatas(val []*AccountInfo) {
	p.Datas = val
}

var fieldIDToName_ListAccountsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListAccountsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAccountsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAccountsResp[fieldId]))
}

func (p *ListAccountsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListAccountsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AccountInfo, 0, size)
	values := make([]AccountInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListAccountsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAccountsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAccountsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAccountsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAccountsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAccountsResp(%+v)", *p)

}

func (p *ListAccountsResp) DeepEqual(ano *ListAccountsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListAccountsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListAccountsResp) Field2DeepEqual(src []*AccountInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListAccountDBPrivilegeReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
}

func NewListAccountDBPrivilegeReq() *ListAccountDBPrivilegeReq {
	return &ListAccountDBPrivilegeReq{}
}

func (p *ListAccountDBPrivilegeReq) InitDefault() {
}

func (p *ListAccountDBPrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListAccountDBPrivilegeReq) GetAccountName() (v string) {
	return p.AccountName
}
func (p *ListAccountDBPrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListAccountDBPrivilegeReq) SetAccountName(val string) {
	p.AccountName = val
}

var fieldIDToName_ListAccountDBPrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
}

func (p *ListAccountDBPrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountDBPrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAccountDBPrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAccountDBPrivilegeReq[fieldId]))
}

func (p *ListAccountDBPrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListAccountDBPrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}

func (p *ListAccountDBPrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountDBPrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAccountDBPrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAccountDBPrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAccountDBPrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAccountDBPrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAccountDBPrivilegeReq(%+v)", *p)

}

func (p *ListAccountDBPrivilegeReq) DeepEqual(ano *ListAccountDBPrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	return true
}

func (p *ListAccountDBPrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListAccountDBPrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}

type ListAccountDBPrivilegeResp struct {
	Total int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*DBPrivilege `thrift:"Datas,2,required" frugal:"2,required,list<DBPrivilege>" json:"Datas"`
}

func NewListAccountDBPrivilegeResp() *ListAccountDBPrivilegeResp {
	return &ListAccountDBPrivilegeResp{}
}

func (p *ListAccountDBPrivilegeResp) InitDefault() {
}

func (p *ListAccountDBPrivilegeResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListAccountDBPrivilegeResp) GetDatas() (v []*DBPrivilege) {
	return p.Datas
}
func (p *ListAccountDBPrivilegeResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListAccountDBPrivilegeResp) SetDatas(val []*DBPrivilege) {
	p.Datas = val
}

var fieldIDToName_ListAccountDBPrivilegeResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListAccountDBPrivilegeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountDBPrivilegeResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAccountDBPrivilegeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAccountDBPrivilegeResp[fieldId]))
}

func (p *ListAccountDBPrivilegeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListAccountDBPrivilegeResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBPrivilege, 0, size)
	values := make([]DBPrivilege, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListAccountDBPrivilegeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAccountDBPrivilegeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAccountDBPrivilegeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAccountDBPrivilegeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAccountDBPrivilegeResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAccountDBPrivilegeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAccountDBPrivilegeResp(%+v)", *p)

}

func (p *ListAccountDBPrivilegeResp) DeepEqual(ano *ListAccountDBPrivilegeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListAccountDBPrivilegeResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListAccountDBPrivilegeResp) Field2DeepEqual(src []*DBPrivilege) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

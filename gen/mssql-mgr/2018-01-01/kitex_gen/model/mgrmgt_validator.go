// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ListMgrParamsReq) IsValid() error {
	return nil
}
func (p *ListMgrParamsResp) IsValid() error {
	return nil
}
func (p *ModifyMgrParamsReq) IsValid() error {
	return nil
}
func (p *RefreshMgrParamsReq) IsValid() error {
	return nil
}
func (p *RefreshMgrParamsResp) IsValid() error {
	return nil
}
func (p *ListMgrInstanceReq) IsValid() error {
	return nil
}
func (p *ListMgrInstanceResp) IsValid() error {
	return nil
}
func (p *RestartMgrInstanceReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceStatusReq) IsValid() error {
	return nil
}
func (p *ListInstancePodsReq) IsValid() error {
	return nil
}
func (p *ListInstancePodsResp) IsValid() error {
	return nil
}
func (p *ModifyInstanceReplicaNumReq) IsValid() error {
	return nil
}
func (p *ListDeployRulesReq) IsValid() error {
	return nil
}
func (p *ListDeployRulesResp) IsValid() error {
	return nil
}
func (p *DeleteDeployRuleReq) IsValid() error {
	return nil
}
func (p *CreateDeployRuleReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceReplicaNumReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceReplicaNumResp) IsValid() error {
	return nil
}
func (p *ExecuteOperationCommandReq) IsValid() error {
	return nil
}
func (p *ExecuteOperationCommandResp) IsValid() error {
	return nil
}
func (p *ListOperationCommandsReq) IsValid() error {
	return nil
}
func (p *ListOperationCommandsResp) IsValid() error {
	return nil
}

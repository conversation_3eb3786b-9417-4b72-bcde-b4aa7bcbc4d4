// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *InnerInspectDBInstanceReq) IsValid() error {
	if p.InspectOption != nil {
		if err := p.InspectOption.IsValid(); err != nil {
			return fmt.Errorf("field InspectOption not valid, %w", err)
		}
	}
	return nil
}
func (p *InnerInspectDBInstanceResp) IsValid() error {
	if p.Summary != nil {
		if err := p.Summary.IsValid(); err != nil {
			return fmt.Errorf("field Summary not valid, %w", err)
		}
	}
	return nil
}
func (p *InnerDescribeInspectItemsReq) IsValid() error {
	return nil
}
func (p *InnerDescribeInspectItemsResp) IsValid() error {
	return nil
}

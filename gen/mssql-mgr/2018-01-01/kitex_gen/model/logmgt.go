// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ListEventsReq struct {
	Offset          int64         `thrift:"Offset,1,required" frugal:"1,required,i64" json:"Offset"`
	Limit           int32         `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	EventId         string        `thrift:"EventId,3" frugal:"3,default,string" json:"EventId"`
	InstanceId      string        `thrift:"InstanceId,4" frugal:"4,default,string" json:"InstanceId"`
	StartTime       string        `thrift:"StartTime,5" frugal:"5,default,string" json:"StartTime"`
	EndTime         string        `thrift:"EndTime,6" frugal:"6,default,string" json:"EndTime"`
	EventType       *EventType    `thrift:"EventType,7,optional" frugal:"7,optional,EventType" json:"EventType,omitempty"`
	EventAction     string        `thrift:"EventAction,8" frugal:"8,default,string" json:"EventAction"`
	EventSource     *EventSource  `thrift:"EventSource,9,optional" frugal:"9,optional,EventSource" json:"EventSource,omitempty"`
	EventResult_    *EventResult_ `thrift:"EventResult,10,optional" frugal:"10,optional,EventResult_" json:"EventResult,omitempty"`
	FinishStartTime *string       `thrift:"FinishStartTime,11,optional" frugal:"11,optional,string" json:"FinishStartTime,omitempty"`
	FinishEndTime   *string       `thrift:"FinishEndTime,12,optional" frugal:"12,optional,string" json:"FinishEndTime,omitempty"`
	ProjectName     *string       `thrift:"ProjectName,13,optional" frugal:"13,optional,string" json:"ProjectName,omitempty"`
}

func NewListEventsReq() *ListEventsReq {
	return &ListEventsReq{}
}

func (p *ListEventsReq) InitDefault() {
}

func (p *ListEventsReq) GetOffset() (v int64) {
	return p.Offset
}

func (p *ListEventsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListEventsReq) GetEventId() (v string) {
	return p.EventId
}

func (p *ListEventsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListEventsReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListEventsReq) GetEndTime() (v string) {
	return p.EndTime
}

var ListEventsReq_EventType_DEFAULT EventType

func (p *ListEventsReq) GetEventType() (v EventType) {
	if !p.IsSetEventType() {
		return ListEventsReq_EventType_DEFAULT
	}
	return *p.EventType
}

func (p *ListEventsReq) GetEventAction() (v string) {
	return p.EventAction
}

var ListEventsReq_EventSource_DEFAULT EventSource

func (p *ListEventsReq) GetEventSource() (v EventSource) {
	if !p.IsSetEventSource() {
		return ListEventsReq_EventSource_DEFAULT
	}
	return *p.EventSource
}

var ListEventsReq_EventResult__DEFAULT EventResult_

func (p *ListEventsReq) GetEventResult_() (v EventResult_) {
	if !p.IsSetEventResult_() {
		return ListEventsReq_EventResult__DEFAULT
	}
	return *p.EventResult_
}

var ListEventsReq_FinishStartTime_DEFAULT string

func (p *ListEventsReq) GetFinishStartTime() (v string) {
	if !p.IsSetFinishStartTime() {
		return ListEventsReq_FinishStartTime_DEFAULT
	}
	return *p.FinishStartTime
}

var ListEventsReq_FinishEndTime_DEFAULT string

func (p *ListEventsReq) GetFinishEndTime() (v string) {
	if !p.IsSetFinishEndTime() {
		return ListEventsReq_FinishEndTime_DEFAULT
	}
	return *p.FinishEndTime
}

var ListEventsReq_ProjectName_DEFAULT string

func (p *ListEventsReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return ListEventsReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *ListEventsReq) SetOffset(val int64) {
	p.Offset = val
}
func (p *ListEventsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListEventsReq) SetEventId(val string) {
	p.EventId = val
}
func (p *ListEventsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListEventsReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListEventsReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *ListEventsReq) SetEventType(val *EventType) {
	p.EventType = val
}
func (p *ListEventsReq) SetEventAction(val string) {
	p.EventAction = val
}
func (p *ListEventsReq) SetEventSource(val *EventSource) {
	p.EventSource = val
}
func (p *ListEventsReq) SetEventResult_(val *EventResult_) {
	p.EventResult_ = val
}
func (p *ListEventsReq) SetFinishStartTime(val *string) {
	p.FinishStartTime = val
}
func (p *ListEventsReq) SetFinishEndTime(val *string) {
	p.FinishEndTime = val
}
func (p *ListEventsReq) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_ListEventsReq = map[int16]string{
	1:  "Offset",
	2:  "Limit",
	3:  "EventId",
	4:  "InstanceId",
	5:  "StartTime",
	6:  "EndTime",
	7:  "EventType",
	8:  "EventAction",
	9:  "EventSource",
	10: "EventResult",
	11: "FinishStartTime",
	12: "FinishEndTime",
	13: "ProjectName",
}

func (p *ListEventsReq) IsSetEventType() bool {
	return p.EventType != nil
}

func (p *ListEventsReq) IsSetEventSource() bool {
	return p.EventSource != nil
}

func (p *ListEventsReq) IsSetEventResult_() bool {
	return p.EventResult_ != nil
}

func (p *ListEventsReq) IsSetFinishStartTime() bool {
	return p.FinishStartTime != nil
}

func (p *ListEventsReq) IsSetFinishEndTime() bool {
	return p.FinishEndTime != nil
}

func (p *ListEventsReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *ListEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListEventsReq[fieldId]))
}

func (p *ListEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventId = _field
	return nil
}
func (p *ListEventsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListEventsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListEventsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListEventsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *EventType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventType(v)
		_field = &tmp
	}
	p.EventType = _field
	return nil
}
func (p *ListEventsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventAction = _field
	return nil
}
func (p *ListEventsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *EventSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventSource(v)
		_field = &tmp
	}
	p.EventSource = _field
	return nil
}
func (p *ListEventsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *EventResult_
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventResult_(v)
		_field = &tmp
	}
	p.EventResult_ = _field
	return nil
}
func (p *ListEventsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FinishStartTime = _field
	return nil
}
func (p *ListEventsReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FinishEndTime = _field
	return nil
}
func (p *ListEventsReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *ListEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListEventsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListEventsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListEventsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventType() {
		if err = oprot.WriteFieldBegin("EventType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListEventsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventAction", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListEventsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventSource() {
		if err = oprot.WriteFieldBegin("EventSource", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListEventsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventResult_() {
		if err = oprot.WriteFieldBegin("EventResult", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventResult_)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ListEventsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetFinishStartTime() {
		if err = oprot.WriteFieldBegin("FinishStartTime", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FinishStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ListEventsReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetFinishEndTime() {
		if err = oprot.WriteFieldBegin("FinishEndTime", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FinishEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ListEventsReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ListEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListEventsReq(%+v)", *p)

}

func (p *ListEventsReq) DeepEqual(ano *ListEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.EventType) {
		return false
	}
	if !p.Field8DeepEqual(ano.EventAction) {
		return false
	}
	if !p.Field9DeepEqual(ano.EventSource) {
		return false
	}
	if !p.Field10DeepEqual(ano.EventResult_) {
		return false
	}
	if !p.Field11DeepEqual(ano.FinishStartTime) {
		return false
	}
	if !p.Field12DeepEqual(ano.FinishEndTime) {
		return false
	}
	if !p.Field13DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *ListEventsReq) Field1DeepEqual(src int64) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListEventsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListEventsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EventId, src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field7DeepEqual(src *EventType) bool {

	if p.EventType == src {
		return true
	} else if p.EventType == nil || src == nil {
		return false
	}
	if *p.EventType != *src {
		return false
	}
	return true
}
func (p *ListEventsReq) Field8DeepEqual(src string) bool {

	if strings.Compare(p.EventAction, src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field9DeepEqual(src *EventSource) bool {

	if p.EventSource == src {
		return true
	} else if p.EventSource == nil || src == nil {
		return false
	}
	if *p.EventSource != *src {
		return false
	}
	return true
}
func (p *ListEventsReq) Field10DeepEqual(src *EventResult_) bool {

	if p.EventResult_ == src {
		return true
	} else if p.EventResult_ == nil || src == nil {
		return false
	}
	if *p.EventResult_ != *src {
		return false
	}
	return true
}
func (p *ListEventsReq) Field11DeepEqual(src *string) bool {

	if p.FinishStartTime == src {
		return true
	} else if p.FinishStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FinishStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field12DeepEqual(src *string) bool {

	if p.FinishEndTime == src {
		return true
	} else if p.FinishEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FinishEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ListEventsReq) Field13DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type ListEventsResp struct {
	Total int32        `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*EventInfo `thrift:"Datas,2,required" frugal:"2,required,list<EventInfo>" json:"Datas"`
}

func NewListEventsResp() *ListEventsResp {
	return &ListEventsResp{}
}

func (p *ListEventsResp) InitDefault() {
}

func (p *ListEventsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListEventsResp) GetDatas() (v []*EventInfo) {
	return p.Datas
}
func (p *ListEventsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListEventsResp) SetDatas(val []*EventInfo) {
	p.Datas = val
}

var fieldIDToName_ListEventsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListEventsResp[fieldId]))
}

func (p *ListEventsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListEventsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*EventInfo, 0, size)
	values := make([]EventInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListEventsResp(%+v)", *p)

}

func (p *ListEventsResp) DeepEqual(ano *ListEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListEventsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListEventsResp) Field2DeepEqual(src []*EventInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListHAChangeLogsReq struct {
	Offset     int32  `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit      int32  `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId string `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime  string `thrift:"StartTime,4" frugal:"4,default,string" json:"StartTime"`
	EndTime    string `thrift:"EndTime,5" frugal:"5,default,string" json:"EndTime"`
}

func NewListHAChangeLogsReq() *ListHAChangeLogsReq {
	return &ListHAChangeLogsReq{}
}

func (p *ListHAChangeLogsReq) InitDefault() {
}

func (p *ListHAChangeLogsReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListHAChangeLogsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListHAChangeLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListHAChangeLogsReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListHAChangeLogsReq) GetEndTime() (v string) {
	return p.EndTime
}
func (p *ListHAChangeLogsReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListHAChangeLogsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListHAChangeLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListHAChangeLogsReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListHAChangeLogsReq) SetEndTime(val string) {
	p.EndTime = val
}

var fieldIDToName_ListHAChangeLogsReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "StartTime",
	5: "EndTime",
}

func (p *ListHAChangeLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHAChangeLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHAChangeLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListHAChangeLogsReq[fieldId]))
}

func (p *ListHAChangeLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListHAChangeLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListHAChangeLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListHAChangeLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListHAChangeLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *ListHAChangeLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHAChangeLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHAChangeLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHAChangeLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHAChangeLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListHAChangeLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListHAChangeLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListHAChangeLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListHAChangeLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHAChangeLogsReq(%+v)", *p)

}

func (p *ListHAChangeLogsReq) DeepEqual(ano *ListHAChangeLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *ListHAChangeLogsReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListHAChangeLogsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListHAChangeLogsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListHAChangeLogsReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListHAChangeLogsReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}

type ListHAChangeLogsResp struct {
	Total int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*HAChangeLog `thrift:"Datas,2,required" frugal:"2,required,list<HAChangeLog>" json:"Datas"`
}

func NewListHAChangeLogsResp() *ListHAChangeLogsResp {
	return &ListHAChangeLogsResp{}
}

func (p *ListHAChangeLogsResp) InitDefault() {
}

func (p *ListHAChangeLogsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListHAChangeLogsResp) GetDatas() (v []*HAChangeLog) {
	return p.Datas
}
func (p *ListHAChangeLogsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListHAChangeLogsResp) SetDatas(val []*HAChangeLog) {
	p.Datas = val
}

var fieldIDToName_ListHAChangeLogsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListHAChangeLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHAChangeLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListHAChangeLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListHAChangeLogsResp[fieldId]))
}

func (p *ListHAChangeLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListHAChangeLogsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HAChangeLog, 0, size)
	values := make([]HAChangeLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListHAChangeLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListHAChangeLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListHAChangeLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListHAChangeLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListHAChangeLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListHAChangeLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHAChangeLogsResp(%+v)", *p)

}

func (p *ListHAChangeLogsResp) DeepEqual(ano *ListHAChangeLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListHAChangeLogsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListHAChangeLogsResp) Field2DeepEqual(src []*HAChangeLog) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListLogsReq struct {
	Offset     int32   `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit      int32   `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	LogType    LogType `thrift:"LogType,3,required" frugal:"3,required,LogType" json:"LogType"`
	InstanceId string  `thrift:"InstanceId,4,required" frugal:"4,required,string" validate:"required"`
	StartTime  string  `thrift:"StartTime,5" frugal:"5,default,string" json:"StartTime"`
	EndTime    string  `thrift:"EndTime,6" frugal:"6,default,string" json:"EndTime"`
}

func NewListLogsReq() *ListLogsReq {
	return &ListLogsReq{}
}

func (p *ListLogsReq) InitDefault() {
}

func (p *ListLogsReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListLogsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListLogsReq) GetLogType() (v LogType) {
	return p.LogType
}

func (p *ListLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListLogsReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListLogsReq) GetEndTime() (v string) {
	return p.EndTime
}
func (p *ListLogsReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListLogsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListLogsReq) SetLogType(val LogType) {
	p.LogType = val
}
func (p *ListLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListLogsReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListLogsReq) SetEndTime(val string) {
	p.EndTime = val
}

var fieldIDToName_ListLogsReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "LogType",
	4: "InstanceId",
	5: "StartTime",
	6: "EndTime",
}

func (p *ListLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetLogType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListLogsReq[fieldId]))
}

func (p *ListLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field LogType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LogType(v)
	}
	p.LogType = _field
	return nil
}
func (p *ListLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListLogsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *ListLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LogType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListLogsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLogsReq(%+v)", *p)

}

func (p *ListLogsReq) DeepEqual(ano *ListLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogType) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *ListLogsReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListLogsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListLogsReq) Field3DeepEqual(src LogType) bool {

	if p.LogType != src {
		return false
	}
	return true
}
func (p *ListLogsReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListLogsReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListLogsReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}

type ListLogsResp struct {
	Total int32    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*DBLog `thrift:"Datas,2,required" frugal:"2,required,list<DBLog>" json:"Datas"`
}

func NewListLogsResp() *ListLogsResp {
	return &ListLogsResp{}
}

func (p *ListLogsResp) InitDefault() {
}

func (p *ListLogsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListLogsResp) GetDatas() (v []*DBLog) {
	return p.Datas
}
func (p *ListLogsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListLogsResp) SetDatas(val []*DBLog) {
	p.Datas = val
}

var fieldIDToName_ListLogsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListLogsResp[fieldId]))
}

func (p *ListLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListLogsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBLog, 0, size)
	values := make([]DBLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLogsResp(%+v)", *p)

}

func (p *ListLogsResp) DeepEqual(ano *ListLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListLogsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListLogsResp) Field2DeepEqual(src []*DBLog) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DownloadLogReq struct {
	InstanceId string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	LogName    string  `thrift:"LogName,2,required" frugal:"2,required,string" validate:"required"`
	LogType    LogType `thrift:"LogType,3,required" frugal:"3,required,LogType" json:"LogType"`
	NodeName   string  `thrift:"NodeName,4,required" frugal:"4,required,string" validate:"required"`
}

func NewDownloadLogReq() *DownloadLogReq {
	return &DownloadLogReq{}
}

func (p *DownloadLogReq) InitDefault() {
}

func (p *DownloadLogReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DownloadLogReq) GetLogName() (v string) {
	return p.LogName
}

func (p *DownloadLogReq) GetLogType() (v LogType) {
	return p.LogType
}

func (p *DownloadLogReq) GetNodeName() (v string) {
	return p.NodeName
}
func (p *DownloadLogReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DownloadLogReq) SetLogName(val string) {
	p.LogName = val
}
func (p *DownloadLogReq) SetLogType(val LogType) {
	p.LogType = val
}
func (p *DownloadLogReq) SetNodeName(val string) {
	p.NodeName = val
}

var fieldIDToName_DownloadLogReq = map[int16]string{
	1: "InstanceId",
	2: "LogName",
	3: "LogType",
	4: "NodeName",
}

func (p *DownloadLogReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadLogReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetLogName bool = false
	var issetLogType bool = false
	var issetNodeName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLogName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNodeName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadLogReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadLogReq[fieldId]))
}

func (p *DownloadLogReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DownloadLogReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogName = _field
	return nil
}
func (p *DownloadLogReq) ReadField3(iprot thrift.TProtocol) error {

	var _field LogType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LogType(v)
	}
	p.LogType = _field
	return nil
}
func (p *DownloadLogReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeName = _field
	return nil
}

func (p *DownloadLogReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadLogReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadLogReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadLogReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DownloadLogReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DownloadLogReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LogType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DownloadLogReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DownloadLogReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadLogReq(%+v)", *p)

}

func (p *DownloadLogReq) DeepEqual(ano *DownloadLogReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.LogName) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogType) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeName) {
		return false
	}
	return true
}

func (p *DownloadLogReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadLogReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.LogName, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadLogReq) Field3DeepEqual(src LogType) bool {

	if p.LogType != src {
		return false
	}
	return true
}
func (p *DownloadLogReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.NodeName, src) != 0 {
		return false
	}
	return true
}

type DescribeAuditLogConfigReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeAuditLogConfigReq() *DescribeAuditLogConfigReq {
	return &DescribeAuditLogConfigReq{}
}

func (p *DescribeAuditLogConfigReq) InitDefault() {
}

func (p *DescribeAuditLogConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeAuditLogConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeAuditLogConfigReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeAuditLogConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAuditLogConfigReq[fieldId]))
}

func (p *DescribeAuditLogConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeAuditLogConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogConfigReq(%+v)", *p)

}

func (p *DescribeAuditLogConfigReq) DeepEqual(ano *DescribeAuditLogConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeAuditLogConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeAuditLogConfigResp struct {
	EnableAuditLog EnableSwitch `thrift:"EnableAuditLog,1,required" frugal:"1,required,EnableSwitch" json:"EnableAuditLog"`
	TTL            int32        `thrift:"TTL,2,required" frugal:"2,required,i32" json:"TTL"`
}

func NewDescribeAuditLogConfigResp() *DescribeAuditLogConfigResp {
	return &DescribeAuditLogConfigResp{}
}

func (p *DescribeAuditLogConfigResp) InitDefault() {
}

func (p *DescribeAuditLogConfigResp) GetEnableAuditLog() (v EnableSwitch) {
	return p.EnableAuditLog
}

func (p *DescribeAuditLogConfigResp) GetTTL() (v int32) {
	return p.TTL
}
func (p *DescribeAuditLogConfigResp) SetEnableAuditLog(val EnableSwitch) {
	p.EnableAuditLog = val
}
func (p *DescribeAuditLogConfigResp) SetTTL(val int32) {
	p.TTL = val
}

var fieldIDToName_DescribeAuditLogConfigResp = map[int16]string{
	1: "EnableAuditLog",
	2: "TTL",
}

func (p *DescribeAuditLogConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEnableAuditLog bool = false
	var issetTTL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableAuditLog = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTTL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEnableAuditLog {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTTL {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAuditLogConfigResp[fieldId]))
}

func (p *DescribeAuditLogConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableAuditLog = _field
	return nil
}
func (p *DescribeAuditLogConfigResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TTL = _field
	return nil
}

func (p *DescribeAuditLogConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableAuditLog", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableAuditLog)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAuditLogConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TTL", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TTL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAuditLogConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogConfigResp(%+v)", *p)

}

func (p *DescribeAuditLogConfigResp) DeepEqual(ano *DescribeAuditLogConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EnableAuditLog) {
		return false
	}
	if !p.Field2DeepEqual(ano.TTL) {
		return false
	}
	return true
}

func (p *DescribeAuditLogConfigResp) Field1DeepEqual(src EnableSwitch) bool {

	if p.EnableAuditLog != src {
		return false
	}
	return true
}
func (p *DescribeAuditLogConfigResp) Field2DeepEqual(src int32) bool {

	if p.TTL != src {
		return false
	}
	return true
}

type ModifyAuditLogConfigReq struct {
	InstanceId     string        `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EnableAuditLog *EnableSwitch `thrift:"EnableAuditLog,2,optional" frugal:"2,optional,EnableSwitch" json:"EnableAuditLog,omitempty"`
	TTL            *int32        `thrift:"TTL,3,optional" frugal:"3,optional,i32" json:"TTL,omitempty"`
}

func NewModifyAuditLogConfigReq() *ModifyAuditLogConfigReq {
	return &ModifyAuditLogConfigReq{}
}

func (p *ModifyAuditLogConfigReq) InitDefault() {
}

func (p *ModifyAuditLogConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyAuditLogConfigReq_EnableAuditLog_DEFAULT EnableSwitch

func (p *ModifyAuditLogConfigReq) GetEnableAuditLog() (v EnableSwitch) {
	if !p.IsSetEnableAuditLog() {
		return ModifyAuditLogConfigReq_EnableAuditLog_DEFAULT
	}
	return *p.EnableAuditLog
}

var ModifyAuditLogConfigReq_TTL_DEFAULT int32

func (p *ModifyAuditLogConfigReq) GetTTL() (v int32) {
	if !p.IsSetTTL() {
		return ModifyAuditLogConfigReq_TTL_DEFAULT
	}
	return *p.TTL
}
func (p *ModifyAuditLogConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyAuditLogConfigReq) SetEnableAuditLog(val *EnableSwitch) {
	p.EnableAuditLog = val
}
func (p *ModifyAuditLogConfigReq) SetTTL(val *int32) {
	p.TTL = val
}

var fieldIDToName_ModifyAuditLogConfigReq = map[int16]string{
	1: "InstanceId",
	2: "EnableAuditLog",
	3: "TTL",
}

func (p *ModifyAuditLogConfigReq) IsSetEnableAuditLog() bool {
	return p.EnableAuditLog != nil
}

func (p *ModifyAuditLogConfigReq) IsSetTTL() bool {
	return p.TTL != nil
}

func (p *ModifyAuditLogConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAuditLogConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyAuditLogConfigReq[fieldId]))
}

func (p *ModifyAuditLogConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyAuditLogConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EnableSwitch(v)
		_field = &tmp
	}
	p.EnableAuditLog = _field
	return nil
}
func (p *ModifyAuditLogConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TTL = _field
	return nil
}

func (p *ModifyAuditLogConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAuditLogConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableAuditLog() {
		if err = oprot.WriteFieldBegin("EnableAuditLog", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EnableAuditLog)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTTL() {
		if err = oprot.WriteFieldBegin("TTL", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.TTL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAuditLogConfigReq(%+v)", *p)

}

func (p *ModifyAuditLogConfigReq) DeepEqual(ano *ModifyAuditLogConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableAuditLog) {
		return false
	}
	if !p.Field3DeepEqual(ano.TTL) {
		return false
	}
	return true
}

func (p *ModifyAuditLogConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAuditLogConfigReq) Field2DeepEqual(src *EnableSwitch) bool {

	if p.EnableAuditLog == src {
		return true
	} else if p.EnableAuditLog == nil || src == nil {
		return false
	}
	if *p.EnableAuditLog != *src {
		return false
	}
	return true
}
func (p *ModifyAuditLogConfigReq) Field3DeepEqual(src *int32) bool {

	if p.TTL == src {
		return true
	} else if p.TTL == nil || src == nil {
		return false
	}
	if *p.TTL != *src {
		return false
	}
	return true
}

type ApplyDownloadLogUrlReq struct {
	LogFileID  string  `thrift:"LogFileID,1,required" frugal:"1,required,string" validate:"required"`
	InstanceId string  `thrift:"InstanceId,2,required" frugal:"2,required,string" validate:"required"`
	Type       LogType `thrift:"Type,3,required" frugal:"3,required,LogType" validate:"required"`
}

func NewApplyDownloadLogUrlReq() *ApplyDownloadLogUrlReq {
	return &ApplyDownloadLogUrlReq{}
}

func (p *ApplyDownloadLogUrlReq) InitDefault() {
}

func (p *ApplyDownloadLogUrlReq) GetLogFileID() (v string) {
	return p.LogFileID
}

func (p *ApplyDownloadLogUrlReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ApplyDownloadLogUrlReq) GetType() (v LogType) {
	return p.Type
}
func (p *ApplyDownloadLogUrlReq) SetLogFileID(val string) {
	p.LogFileID = val
}
func (p *ApplyDownloadLogUrlReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ApplyDownloadLogUrlReq) SetType(val LogType) {
	p.Type = val
}

var fieldIDToName_ApplyDownloadLogUrlReq = map[int16]string{
	1: "LogFileID",
	2: "InstanceId",
	3: "Type",
}

func (p *ApplyDownloadLogUrlReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadLogUrlReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLogFileID bool = false
	var issetInstanceId bool = false
	var issetType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogFileID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLogFileID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyDownloadLogUrlReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ApplyDownloadLogUrlReq[fieldId]))
}

func (p *ApplyDownloadLogUrlReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogFileID = _field
	return nil
}
func (p *ApplyDownloadLogUrlReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ApplyDownloadLogUrlReq) ReadField3(iprot thrift.TProtocol) error {

	var _field LogType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LogType(v)
	}
	p.Type = _field
	return nil
}

func (p *ApplyDownloadLogUrlReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadLogUrlReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyDownloadLogUrlReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyDownloadLogUrlReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogFileID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogFileID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ApplyDownloadLogUrlReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ApplyDownloadLogUrlReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ApplyDownloadLogUrlReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyDownloadLogUrlReq(%+v)", *p)

}

func (p *ApplyDownloadLogUrlReq) DeepEqual(ano *ApplyDownloadLogUrlReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.LogFileID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Type) {
		return false
	}
	return true
}

func (p *ApplyDownloadLogUrlReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.LogFileID, src) != 0 {
		return false
	}
	return true
}
func (p *ApplyDownloadLogUrlReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ApplyDownloadLogUrlReq) Field3DeepEqual(src LogType) bool {

	if p.Type != src {
		return false
	}
	return true
}

type ApplyDownloadLogUrlResp struct {
	DownloadUrl string `thrift:"DownloadUrl,1,required" frugal:"1,required,string" json:"DownloadUrl"`
}

func NewApplyDownloadLogUrlResp() *ApplyDownloadLogUrlResp {
	return &ApplyDownloadLogUrlResp{}
}

func (p *ApplyDownloadLogUrlResp) InitDefault() {
}

func (p *ApplyDownloadLogUrlResp) GetDownloadUrl() (v string) {
	return p.DownloadUrl
}
func (p *ApplyDownloadLogUrlResp) SetDownloadUrl(val string) {
	p.DownloadUrl = val
}

var fieldIDToName_ApplyDownloadLogUrlResp = map[int16]string{
	1: "DownloadUrl",
}

func (p *ApplyDownloadLogUrlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadLogUrlResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDownloadUrl bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDownloadUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDownloadUrl {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyDownloadLogUrlResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ApplyDownloadLogUrlResp[fieldId]))
}

func (p *ApplyDownloadLogUrlResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DownloadUrl = _field
	return nil
}

func (p *ApplyDownloadLogUrlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadLogUrlResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyDownloadLogUrlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyDownloadLogUrlResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DownloadUrl", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DownloadUrl); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ApplyDownloadLogUrlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyDownloadLogUrlResp(%+v)", *p)

}

func (p *ApplyDownloadLogUrlResp) DeepEqual(ano *ApplyDownloadLogUrlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DownloadUrl) {
		return false
	}
	return true
}

func (p *ApplyDownloadLogUrlResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DownloadUrl, src) != 0 {
		return false
	}
	return true
}

type ModifyEventStatusReq struct {
	EventId      string       `thrift:"EventId,1,required" frugal:"1,required,string" json:"EventId"`
	EventResult_ EventResult_ `thrift:"EventResult,2,required" frugal:"2,required,EventResult_" json:"EventResult"`
}

func NewModifyEventStatusReq() *ModifyEventStatusReq {
	return &ModifyEventStatusReq{}
}

func (p *ModifyEventStatusReq) InitDefault() {
}

func (p *ModifyEventStatusReq) GetEventId() (v string) {
	return p.EventId
}

func (p *ModifyEventStatusReq) GetEventResult_() (v EventResult_) {
	return p.EventResult_
}
func (p *ModifyEventStatusReq) SetEventId(val string) {
	p.EventId = val
}
func (p *ModifyEventStatusReq) SetEventResult_(val EventResult_) {
	p.EventResult_ = val
}

var fieldIDToName_ModifyEventStatusReq = map[int16]string{
	1: "EventId",
	2: "EventResult",
}

func (p *ModifyEventStatusReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyEventStatusReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEventId bool = false
	var issetEventResult_ bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventResult_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEventId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEventResult_ {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyEventStatusReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyEventStatusReq[fieldId]))
}

func (p *ModifyEventStatusReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventId = _field
	return nil
}
func (p *ModifyEventStatusReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EventResult_
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EventResult_(v)
	}
	p.EventResult_ = _field
	return nil
}

func (p *ModifyEventStatusReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyEventStatusReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyEventStatusReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyEventStatusReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyEventStatusReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventResult", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EventResult_)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyEventStatusReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyEventStatusReq(%+v)", *p)

}

func (p *ModifyEventStatusReq) DeepEqual(ano *ModifyEventStatusReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EventResult_) {
		return false
	}
	return true
}

func (p *ModifyEventStatusReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EventId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyEventStatusReq) Field2DeepEqual(src EventResult_) bool {

	if p.EventResult_ != src {
		return false
	}
	return true
}

type DeleteEventReq struct {
	EventId string `thrift:"EventId,1,required" frugal:"1,required,string" json:"EventId"`
}

func NewDeleteEventReq() *DeleteEventReq {
	return &DeleteEventReq{}
}

func (p *DeleteEventReq) InitDefault() {
}

func (p *DeleteEventReq) GetEventId() (v string) {
	return p.EventId
}
func (p *DeleteEventReq) SetEventId(val string) {
	p.EventId = val
}

var fieldIDToName_DeleteEventReq = map[int16]string{
	1: "EventId",
}

func (p *DeleteEventReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteEventReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEventId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEventId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteEventReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteEventReq[fieldId]))
}

func (p *DeleteEventReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventId = _field
	return nil
}

func (p *DeleteEventReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteEventReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteEventReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteEventReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteEventReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteEventReq(%+v)", *p)

}

func (p *DeleteEventReq) DeepEqual(ano *DeleteEventReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventId) {
		return false
	}
	return true
}

func (p *DeleteEventReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EventId, src) != 0 {
		return false
	}
	return true
}

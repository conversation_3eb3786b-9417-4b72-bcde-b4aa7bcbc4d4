// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateFreeLockCorrectOrderReq) IsValid() error {
	return nil
}
func (p *CreateFreeLockCorrectOrderResp) IsValid() error {
	return nil
}
func (p *ListFreeLockCorrectOrdersReq) IsValid() error {
	return nil
}
func (p *ListFreeLockCorrectOrdersResp) IsValid() error {
	return nil
}
func (p *StopFreeLockCorrectOrderReq) IsValid() error {
	return nil
}

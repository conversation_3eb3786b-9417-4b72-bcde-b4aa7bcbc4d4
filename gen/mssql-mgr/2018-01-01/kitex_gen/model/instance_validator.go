// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDBInstanceReq) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateDBInstanceResp) IsValid() error {
	return nil
}
func (p *CreateReadOnlyDBInstanceReq) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteDBInstanceReq) IsValid() error {
	return nil
}
func (p *DestroyDBInstanceReq) IsValid() error {
	return nil
}
func (p *RestartDBInstanceReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceReq) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyDBInstanceResp) IsValid() error {
	return nil
}
func (p *ListDBInstancesReq) IsValid() error {
	return nil
}
func (p *ListDBInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceResp) IsValid() error {
	if p.BasicInfo != nil {
		if err := p.BasicInfo.IsValid(); err != nil {
			return fmt.Errorf("field BasicInfo not valid, %w", err)
		}
	}
	if p.ConnectionInfo != nil {
		if err := p.ConnectionInfo.IsValid(); err != nil {
			return fmt.Errorf("field ConnectionInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyDBInstanceMaintainTimeReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceUnchangeableTimeReq) IsValid() error {
	return nil
}
func (p *ChangeDBInstanceHAMasterReq) IsValid() error {
	return nil
}
func (p *ModifyInstanceSyncModeReq) IsValid() error {
	return nil
}
func (p *ListDataItemsResp) IsValid() error {
	return nil
}
func (p *ListZonesReq) IsValid() error {
	return nil
}
func (p *ListStorageTypesReq) IsValid() error {
	return nil
}
func (p *ListInstanceSpecsReq) IsValid() error {
	return nil
}
func (p *ListInstanceSpecsResp) IsValid() error {
	return nil
}
func (p *ListVpcsReq) IsValid() error {
	return nil
}
func (p *ListInstanceNodesReq) IsValid() error {
	return nil
}
func (p *ListInstanceNodesResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceNameReq) IsValid() error {
	return nil
}
func (p *DescribeResourceUsageReq) IsValid() error {
	return nil
}
func (p *DescribeResourceUsageResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePerformanceReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePerformanceResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceVersionReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceVersionResp) IsValid() error {
	return nil
}
func (p *UpgradeDBInstanceVersionReq) IsValid() error {
	return nil
}
func (p *ListDBInstanceUpgradeableVersionReq) IsValid() error {
	return nil
}
func (p *ListDBInstanceUpgradeableVersionResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceDistributionResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceStatisticsReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceStatisticsResp) IsValid() error {
	return nil
}
func (p *DescribeReadOnlyDBInstanceDelayReq) IsValid() error {
	return nil
}
func (p *DescribeReadOnlyDBInstanceDelayResp) IsValid() error {
	return nil
}
func (p *ModifyReadonlyInstanceDelayReplicationTimeReq) IsValid() error {
	return nil
}
func (p *DescribeReadOnlyInstanceDelayReplicationTimeReq) IsValid() error {
	return nil
}
func (p *DescribeReadOnlyInstanceDelayReplicationTimeResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePriceReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePriceResp) IsValid() error {
	return nil
}
func (p *DescribeDBProxyInstancePriceReq) IsValid() error {
	return nil
}
func (p *CallbackInstanceTradeChangeReq) IsValid() error {
	return nil
}
func (p *CallbackInstanceTradeChangeResp) IsValid() error {
	return nil
}
func (p *RebuildDBInstanceReq) IsValid() error {
	return nil
}
func (p *CreateChargeOrderReq) IsValid() error {
	return nil
}
func (p *CreateChargeOrderResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceChargeTypeReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceChargeTypeResp) IsValid() error {
	return nil
}
func (p *ListCloudMonitorDBInstancesReq) IsValid() error {
	return nil
}
func (p *ListCloudMonitorDBInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeCloudMonitorDimensionValuesReq) IsValid() error {
	return nil
}
func (p *DescribeCloudMonitorDimensionValuesResp) IsValid() error {
	return nil
}
func (p *TagFilterObject) IsValid() error {
	return nil
}

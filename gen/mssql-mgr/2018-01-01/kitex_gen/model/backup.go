// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateBackupReq struct {
	InstanceId     string         `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupStrategy BackupStrategy `thrift:"BackupStrategy,2,required" frugal:"2,required,BackupStrategy" json:"BackupStrategy"`
	BackupType     BackupType     `thrift:"BackupType,3,required" frugal:"3,required,BackupType" json:"BackupType"`
	BackupMode     BackupMode     `thrift:"BackupMode,4,required" frugal:"4,required,BackupMode" json:"BackupMode"`
	BackupDataType BackupDataType `thrift:"BackupDataType,5,required" frugal:"5,required,BackupDataType" json:"BackupDataType"`
	DBNames        *string        `thrift:"DBNames,6,optional" frugal:"6,optional,string" json:"DBNames,omitempty"`
}

func NewCreateBackupReq() *CreateBackupReq {
	return &CreateBackupReq{}
}

func (p *CreateBackupReq) InitDefault() {
}

func (p *CreateBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateBackupReq) GetBackupStrategy() (v BackupStrategy) {
	return p.BackupStrategy
}

func (p *CreateBackupReq) GetBackupType() (v BackupType) {
	return p.BackupType
}

func (p *CreateBackupReq) GetBackupMode() (v BackupMode) {
	return p.BackupMode
}

func (p *CreateBackupReq) GetBackupDataType() (v BackupDataType) {
	return p.BackupDataType
}

var CreateBackupReq_DBNames_DEFAULT string

func (p *CreateBackupReq) GetDBNames() (v string) {
	if !p.IsSetDBNames() {
		return CreateBackupReq_DBNames_DEFAULT
	}
	return *p.DBNames
}
func (p *CreateBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateBackupReq) SetBackupStrategy(val BackupStrategy) {
	p.BackupStrategy = val
}
func (p *CreateBackupReq) SetBackupType(val BackupType) {
	p.BackupType = val
}
func (p *CreateBackupReq) SetBackupMode(val BackupMode) {
	p.BackupMode = val
}
func (p *CreateBackupReq) SetBackupDataType(val BackupDataType) {
	p.BackupDataType = val
}
func (p *CreateBackupReq) SetDBNames(val *string) {
	p.DBNames = val
}

var fieldIDToName_CreateBackupReq = map[int16]string{
	1: "InstanceId",
	2: "BackupStrategy",
	3: "BackupType",
	4: "BackupMode",
	5: "BackupDataType",
	6: "DBNames",
}

func (p *CreateBackupReq) IsSetDBNames() bool {
	return p.DBNames != nil
}

func (p *CreateBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupStrategy bool = false
	var issetBackupType bool = false
	var issetBackupMode bool = false
	var issetBackupDataType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupStrategy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupStrategy {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBackupType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBackupMode {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupDataType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBackupReq[fieldId]))
}

func (p *CreateBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateBackupReq) ReadField2(iprot thrift.TProtocol) error {

	var _field BackupStrategy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupStrategy(v)
	}
	p.BackupStrategy = _field
	return nil
}
func (p *CreateBackupReq) ReadField3(iprot thrift.TProtocol) error {

	var _field BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupType(v)
	}
	p.BackupType = _field
	return nil
}
func (p *CreateBackupReq) ReadField4(iprot thrift.TProtocol) error {

	var _field BackupMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupMode(v)
	}
	p.BackupMode = _field
	return nil
}
func (p *CreateBackupReq) ReadField5(iprot thrift.TProtocol) error {

	var _field BackupDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupDataType(v)
	}
	p.BackupDataType = _field
	return nil
}
func (p *CreateBackupReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBNames = _field
	return nil
}

func (p *CreateBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBackupReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupStrategy", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupStrategy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateBackupReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateBackupReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupMode", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateBackupReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupDataType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupDataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateBackupReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBNames() {
		if err = oprot.WriteFieldBegin("DBNames", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBNames); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBackupReq(%+v)", *p)

}

func (p *CreateBackupReq) DeepEqual(ano *CreateBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupStrategy) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupMode) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupDataType) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBNames) {
		return false
	}
	return true
}

func (p *CreateBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field2DeepEqual(src BackupStrategy) bool {

	if p.BackupStrategy != src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field3DeepEqual(src BackupType) bool {

	if p.BackupType != src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field4DeepEqual(src BackupMode) bool {

	if p.BackupMode != src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field5DeepEqual(src BackupDataType) bool {

	if p.BackupDataType != src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field6DeepEqual(src *string) bool {

	if p.DBNames == src {
		return true
	} else if p.DBNames == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBNames, *src) != 0 {
		return false
	}
	return true
}

type CreateBackupResp struct {
	BackupId string `thrift:"BackupId,1,required" frugal:"1,required,string" json:"BackupId"`
}

func NewCreateBackupResp() *CreateBackupResp {
	return &CreateBackupResp{}
}

func (p *CreateBackupResp) InitDefault() {
}

func (p *CreateBackupResp) GetBackupId() (v string) {
	return p.BackupId
}
func (p *CreateBackupResp) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_CreateBackupResp = map[int16]string{
	1: "BackupId",
}

func (p *CreateBackupResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBackupId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBackupResp[fieldId]))
}

func (p *CreateBackupResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *CreateBackupResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBackupResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBackupResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBackupResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBackupResp(%+v)", *p)

}

func (p *CreateBackupResp) DeepEqual(ano *CreateBackupResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *CreateBackupResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type ApplyDownloadBackupUrlReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" validate:"required"`
}

func NewApplyDownloadBackupUrlReq() *ApplyDownloadBackupUrlReq {
	return &ApplyDownloadBackupUrlReq{}
}

func (p *ApplyDownloadBackupUrlReq) InitDefault() {
}

func (p *ApplyDownloadBackupUrlReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ApplyDownloadBackupUrlReq) GetBackupId() (v string) {
	return p.BackupId
}
func (p *ApplyDownloadBackupUrlReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ApplyDownloadBackupUrlReq) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_ApplyDownloadBackupUrlReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *ApplyDownloadBackupUrlReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadBackupUrlReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyDownloadBackupUrlReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ApplyDownloadBackupUrlReq[fieldId]))
}

func (p *ApplyDownloadBackupUrlReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ApplyDownloadBackupUrlReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *ApplyDownloadBackupUrlReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadBackupUrlReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyDownloadBackupUrlReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyDownloadBackupUrlReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ApplyDownloadBackupUrlReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ApplyDownloadBackupUrlReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyDownloadBackupUrlReq(%+v)", *p)

}

func (p *ApplyDownloadBackupUrlReq) DeepEqual(ano *ApplyDownloadBackupUrlReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *ApplyDownloadBackupUrlReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ApplyDownloadBackupUrlReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type ApplyDownloadBackupUrlResp struct {
	DownloadUrl string `thrift:"DownloadUrl,1,required" frugal:"1,required,string" json:"DownloadUrl"`
}

func NewApplyDownloadBackupUrlResp() *ApplyDownloadBackupUrlResp {
	return &ApplyDownloadBackupUrlResp{}
}

func (p *ApplyDownloadBackupUrlResp) InitDefault() {
}

func (p *ApplyDownloadBackupUrlResp) GetDownloadUrl() (v string) {
	return p.DownloadUrl
}
func (p *ApplyDownloadBackupUrlResp) SetDownloadUrl(val string) {
	p.DownloadUrl = val
}

var fieldIDToName_ApplyDownloadBackupUrlResp = map[int16]string{
	1: "DownloadUrl",
}

func (p *ApplyDownloadBackupUrlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadBackupUrlResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDownloadUrl bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDownloadUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDownloadUrl {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyDownloadBackupUrlResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ApplyDownloadBackupUrlResp[fieldId]))
}

func (p *ApplyDownloadBackupUrlResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DownloadUrl = _field
	return nil
}

func (p *ApplyDownloadBackupUrlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ApplyDownloadBackupUrlResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyDownloadBackupUrlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyDownloadBackupUrlResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DownloadUrl", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DownloadUrl); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ApplyDownloadBackupUrlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyDownloadBackupUrlResp(%+v)", *p)

}

func (p *ApplyDownloadBackupUrlResp) DeepEqual(ano *ApplyDownloadBackupUrlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DownloadUrl) {
		return false
	}
	return true
}

func (p *ApplyDownloadBackupUrlResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DownloadUrl, src) != 0 {
		return false
	}
	return true
}

type ListBackupsReq struct {
	Offset         int64           `thrift:"Offset,1,required" frugal:"1,required,i64" json:"Offset"`
	Limit          int32           `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId     string          `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime      string          `thrift:"StartTime,4" frugal:"4,default,string" json:"StartTime"`
	EndTime        string          `thrift:"EndTime,5" frugal:"5,default,string" json:"EndTime"`
	BackupStrategy *BackupStrategy `thrift:"BackupStrategy,6,optional" frugal:"6,optional,BackupStrategy" json:"BackupStrategy,omitempty"`
	BackupType     *BackupType     `thrift:"BackupType,7,optional" frugal:"7,optional,BackupType" json:"BackupType,omitempty"`
	BackupMode     *BackupMode     `thrift:"BackupMode,8,optional" frugal:"8,optional,BackupMode" json:"BackupMode,omitempty"`
	BackupStatus   *BackupStatus   `thrift:"BackupStatus,9,optional" frugal:"9,optional,BackupStatus" json:"BackupStatus,omitempty"`
	BackupId       string          `thrift:"BackupId,10" frugal:"10,default,string" json:"BackupId"`
	BackupDataType BackupDataType  `thrift:"BackupDataType,11,required" frugal:"11,required,BackupDataType" json:"BackupDataType"`
	QueryKeyWord   *string         `thrift:"QueryKeyWord,12,optional" frugal:"12,optional,string" json:"QueryKeyWord,omitempty"`
	CreateType     *CreateType     `thrift:"CreateType,13,optional" frugal:"13,optional,CreateType" json:"CreateType,omitempty"`
}

func NewListBackupsReq() *ListBackupsReq {
	return &ListBackupsReq{}
}

func (p *ListBackupsReq) InitDefault() {
}

func (p *ListBackupsReq) GetOffset() (v int64) {
	return p.Offset
}

func (p *ListBackupsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListBackupsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListBackupsReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListBackupsReq) GetEndTime() (v string) {
	return p.EndTime
}

var ListBackupsReq_BackupStrategy_DEFAULT BackupStrategy

func (p *ListBackupsReq) GetBackupStrategy() (v BackupStrategy) {
	if !p.IsSetBackupStrategy() {
		return ListBackupsReq_BackupStrategy_DEFAULT
	}
	return *p.BackupStrategy
}

var ListBackupsReq_BackupType_DEFAULT BackupType

func (p *ListBackupsReq) GetBackupType() (v BackupType) {
	if !p.IsSetBackupType() {
		return ListBackupsReq_BackupType_DEFAULT
	}
	return *p.BackupType
}

var ListBackupsReq_BackupMode_DEFAULT BackupMode

func (p *ListBackupsReq) GetBackupMode() (v BackupMode) {
	if !p.IsSetBackupMode() {
		return ListBackupsReq_BackupMode_DEFAULT
	}
	return *p.BackupMode
}

var ListBackupsReq_BackupStatus_DEFAULT BackupStatus

func (p *ListBackupsReq) GetBackupStatus() (v BackupStatus) {
	if !p.IsSetBackupStatus() {
		return ListBackupsReq_BackupStatus_DEFAULT
	}
	return *p.BackupStatus
}

func (p *ListBackupsReq) GetBackupId() (v string) {
	return p.BackupId
}

func (p *ListBackupsReq) GetBackupDataType() (v BackupDataType) {
	return p.BackupDataType
}

var ListBackupsReq_QueryKeyWord_DEFAULT string

func (p *ListBackupsReq) GetQueryKeyWord() (v string) {
	if !p.IsSetQueryKeyWord() {
		return ListBackupsReq_QueryKeyWord_DEFAULT
	}
	return *p.QueryKeyWord
}

var ListBackupsReq_CreateType_DEFAULT CreateType

func (p *ListBackupsReq) GetCreateType() (v CreateType) {
	if !p.IsSetCreateType() {
		return ListBackupsReq_CreateType_DEFAULT
	}
	return *p.CreateType
}
func (p *ListBackupsReq) SetOffset(val int64) {
	p.Offset = val
}
func (p *ListBackupsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListBackupsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListBackupsReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListBackupsReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *ListBackupsReq) SetBackupStrategy(val *BackupStrategy) {
	p.BackupStrategy = val
}
func (p *ListBackupsReq) SetBackupType(val *BackupType) {
	p.BackupType = val
}
func (p *ListBackupsReq) SetBackupMode(val *BackupMode) {
	p.BackupMode = val
}
func (p *ListBackupsReq) SetBackupStatus(val *BackupStatus) {
	p.BackupStatus = val
}
func (p *ListBackupsReq) SetBackupId(val string) {
	p.BackupId = val
}
func (p *ListBackupsReq) SetBackupDataType(val BackupDataType) {
	p.BackupDataType = val
}
func (p *ListBackupsReq) SetQueryKeyWord(val *string) {
	p.QueryKeyWord = val
}
func (p *ListBackupsReq) SetCreateType(val *CreateType) {
	p.CreateType = val
}

var fieldIDToName_ListBackupsReq = map[int16]string{
	1:  "Offset",
	2:  "Limit",
	3:  "InstanceId",
	4:  "StartTime",
	5:  "EndTime",
	6:  "BackupStrategy",
	7:  "BackupType",
	8:  "BackupMode",
	9:  "BackupStatus",
	10: "BackupId",
	11: "BackupDataType",
	12: "QueryKeyWord",
	13: "CreateType",
}

func (p *ListBackupsReq) IsSetBackupStrategy() bool {
	return p.BackupStrategy != nil
}

func (p *ListBackupsReq) IsSetBackupType() bool {
	return p.BackupType != nil
}

func (p *ListBackupsReq) IsSetBackupMode() bool {
	return p.BackupMode != nil
}

func (p *ListBackupsReq) IsSetBackupStatus() bool {
	return p.BackupStatus != nil
}

func (p *ListBackupsReq) IsSetQueryKeyWord() bool {
	return p.QueryKeyWord != nil
}

func (p *ListBackupsReq) IsSetCreateType() bool {
	return p.CreateType != nil
}

func (p *ListBackupsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListBackupsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false
	var issetBackupDataType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBackupDataType {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListBackupsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListBackupsReq[fieldId]))
}

func (p *ListBackupsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListBackupsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListBackupsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListBackupsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListBackupsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListBackupsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *BackupStrategy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupStrategy(v)
		_field = &tmp
	}
	p.BackupStrategy = _field
	return nil
}
func (p *ListBackupsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return nil
}
func (p *ListBackupsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *BackupMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupMode(v)
		_field = &tmp
	}
	p.BackupMode = _field
	return nil
}
func (p *ListBackupsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *BackupStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupStatus(v)
		_field = &tmp
	}
	p.BackupStatus = _field
	return nil
}
func (p *ListBackupsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}
func (p *ListBackupsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field BackupDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupDataType(v)
	}
	p.BackupDataType = _field
	return nil
}
func (p *ListBackupsReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryKeyWord = _field
	return nil
}
func (p *ListBackupsReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *CreateType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CreateType(v)
		_field = &tmp
	}
	p.CreateType = _field
	return nil
}

func (p *ListBackupsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListBackupsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListBackupsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListBackupsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListBackupsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListBackupsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListBackupsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListBackupsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListBackupsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupStrategy() {
		if err = oprot.WriteFieldBegin("BackupStrategy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupStrategy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListBackupsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupType() {
		if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListBackupsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupMode() {
		if err = oprot.WriteFieldBegin("BackupMode", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListBackupsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupStatus() {
		if err = oprot.WriteFieldBegin("BackupStatus", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListBackupsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ListBackupsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupDataType", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupDataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ListBackupsReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryKeyWord() {
		if err = oprot.WriteFieldBegin("QueryKeyWord", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryKeyWord); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ListBackupsReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateType() {
		if err = oprot.WriteFieldBegin("CreateType", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.CreateType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ListBackupsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListBackupsReq(%+v)", *p)

}

func (p *ListBackupsReq) DeepEqual(ano *ListBackupsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.BackupStrategy) {
		return false
	}
	if !p.Field7DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field8DeepEqual(ano.BackupMode) {
		return false
	}
	if !p.Field9DeepEqual(ano.BackupStatus) {
		return false
	}
	if !p.Field10DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field11DeepEqual(ano.BackupDataType) {
		return false
	}
	if !p.Field12DeepEqual(ano.QueryKeyWord) {
		return false
	}
	if !p.Field13DeepEqual(ano.CreateType) {
		return false
	}
	return true
}

func (p *ListBackupsReq) Field1DeepEqual(src int64) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field6DeepEqual(src *BackupStrategy) bool {

	if p.BackupStrategy == src {
		return true
	} else if p.BackupStrategy == nil || src == nil {
		return false
	}
	if *p.BackupStrategy != *src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field7DeepEqual(src *BackupType) bool {

	if p.BackupType == src {
		return true
	} else if p.BackupType == nil || src == nil {
		return false
	}
	if *p.BackupType != *src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field8DeepEqual(src *BackupMode) bool {

	if p.BackupMode == src {
		return true
	} else if p.BackupMode == nil || src == nil {
		return false
	}
	if *p.BackupMode != *src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field9DeepEqual(src *BackupStatus) bool {

	if p.BackupStatus == src {
		return true
	} else if p.BackupStatus == nil || src == nil {
		return false
	}
	if *p.BackupStatus != *src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field10DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field11DeepEqual(src BackupDataType) bool {

	if p.BackupDataType != src {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field12DeepEqual(src *string) bool {

	if p.QueryKeyWord == src {
		return true
	} else if p.QueryKeyWord == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryKeyWord, *src) != 0 {
		return false
	}
	return true
}
func (p *ListBackupsReq) Field13DeepEqual(src *CreateType) bool {

	if p.CreateType == src {
		return true
	} else if p.CreateType == nil || src == nil {
		return false
	}
	if *p.CreateType != *src {
		return false
	}
	return true
}

type ListBackupsResp struct {
	Total int32         `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*BackupInfo `thrift:"Datas,2,required" frugal:"2,required,list<BackupInfo>" json:"Datas"`
}

func NewListBackupsResp() *ListBackupsResp {
	return &ListBackupsResp{}
}

func (p *ListBackupsResp) InitDefault() {
}

func (p *ListBackupsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListBackupsResp) GetDatas() (v []*BackupInfo) {
	return p.Datas
}
func (p *ListBackupsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListBackupsResp) SetDatas(val []*BackupInfo) {
	p.Datas = val
}

var fieldIDToName_ListBackupsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListBackupsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListBackupsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListBackupsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListBackupsResp[fieldId]))
}

func (p *ListBackupsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListBackupsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*BackupInfo, 0, size)
	values := make([]BackupInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListBackupsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListBackupsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListBackupsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListBackupsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListBackupsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListBackupsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListBackupsResp(%+v)", *p)

}

func (p *ListBackupsResp) DeepEqual(ano *ListBackupsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListBackupsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListBackupsResp) Field2DeepEqual(src []*BackupInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeBackupConfigReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeBackupConfigReq() *DescribeBackupConfigReq {
	return &DescribeBackupConfigReq{}
}

func (p *DescribeBackupConfigReq) InitDefault() {
}

func (p *DescribeBackupConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeBackupConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeBackupConfigReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeBackupConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupConfigReq[fieldId]))
}

func (p *DescribeBackupConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeBackupConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupConfigReq(%+v)", *p)

}

func (p *DescribeBackupConfigReq) DeepEqual(ano *DescribeBackupConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeBackupConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeBackupConfigResp struct {
	DataFullBackupTime     string                 `thrift:"DataFullBackupTime,1,required" frugal:"1,required,string" json:"DataFullBackupTime"`
	DataBackupRetentionDay int32                  `thrift:"DataBackupRetentionDay,2,required" frugal:"2,required,i32" json:"DataBackupRetentionDay"`
	DataFullBackupPeriods  string                 `thrift:"DataFullBackupPeriods,3,required" frugal:"3,required,string" json:"DataFullBackupPeriods"`
	DataIncrBackupPeriods  *string                `thrift:"DataIncrBackupPeriods,4,optional" frugal:"4,optional,string" json:"DataIncrBackupPeriods,omitempty"`
	LogBackupRetentionDay  int32                  `thrift:"LogBackupRetentionDay,5,required" frugal:"5,required,i32" json:"LogBackupRetentionDay"`
	EnableDataBackup       EnableSwitch           `thrift:"EnableDataBackup,6,required" frugal:"6,required,EnableSwitch" json:"EnableDataBackup"`
	BinlogPolicy           *BinlogRetentionPolicy `thrift:"BinlogPolicy,7,required" frugal:"7,required,BinlogRetentionPolicy" json:"BinlogPolicy"`
}

func NewDescribeBackupConfigResp() *DescribeBackupConfigResp {
	return &DescribeBackupConfigResp{}
}

func (p *DescribeBackupConfigResp) InitDefault() {
}

func (p *DescribeBackupConfigResp) GetDataFullBackupTime() (v string) {
	return p.DataFullBackupTime
}

func (p *DescribeBackupConfigResp) GetDataBackupRetentionDay() (v int32) {
	return p.DataBackupRetentionDay
}

func (p *DescribeBackupConfigResp) GetDataFullBackupPeriods() (v string) {
	return p.DataFullBackupPeriods
}

var DescribeBackupConfigResp_DataIncrBackupPeriods_DEFAULT string

func (p *DescribeBackupConfigResp) GetDataIncrBackupPeriods() (v string) {
	if !p.IsSetDataIncrBackupPeriods() {
		return DescribeBackupConfigResp_DataIncrBackupPeriods_DEFAULT
	}
	return *p.DataIncrBackupPeriods
}

func (p *DescribeBackupConfigResp) GetLogBackupRetentionDay() (v int32) {
	return p.LogBackupRetentionDay
}

func (p *DescribeBackupConfigResp) GetEnableDataBackup() (v EnableSwitch) {
	return p.EnableDataBackup
}

var DescribeBackupConfigResp_BinlogPolicy_DEFAULT *BinlogRetentionPolicy

func (p *DescribeBackupConfigResp) GetBinlogPolicy() (v *BinlogRetentionPolicy) {
	if !p.IsSetBinlogPolicy() {
		return DescribeBackupConfigResp_BinlogPolicy_DEFAULT
	}
	return p.BinlogPolicy
}
func (p *DescribeBackupConfigResp) SetDataFullBackupTime(val string) {
	p.DataFullBackupTime = val
}
func (p *DescribeBackupConfigResp) SetDataBackupRetentionDay(val int32) {
	p.DataBackupRetentionDay = val
}
func (p *DescribeBackupConfigResp) SetDataFullBackupPeriods(val string) {
	p.DataFullBackupPeriods = val
}
func (p *DescribeBackupConfigResp) SetDataIncrBackupPeriods(val *string) {
	p.DataIncrBackupPeriods = val
}
func (p *DescribeBackupConfigResp) SetLogBackupRetentionDay(val int32) {
	p.LogBackupRetentionDay = val
}
func (p *DescribeBackupConfigResp) SetEnableDataBackup(val EnableSwitch) {
	p.EnableDataBackup = val
}
func (p *DescribeBackupConfigResp) SetBinlogPolicy(val *BinlogRetentionPolicy) {
	p.BinlogPolicy = val
}

var fieldIDToName_DescribeBackupConfigResp = map[int16]string{
	1: "DataFullBackupTime",
	2: "DataBackupRetentionDay",
	3: "DataFullBackupPeriods",
	4: "DataIncrBackupPeriods",
	5: "LogBackupRetentionDay",
	6: "EnableDataBackup",
	7: "BinlogPolicy",
}

func (p *DescribeBackupConfigResp) IsSetDataIncrBackupPeriods() bool {
	return p.DataIncrBackupPeriods != nil
}

func (p *DescribeBackupConfigResp) IsSetBinlogPolicy() bool {
	return p.BinlogPolicy != nil
}

func (p *DescribeBackupConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataFullBackupTime bool = false
	var issetDataBackupRetentionDay bool = false
	var issetDataFullBackupPeriods bool = false
	var issetLogBackupRetentionDay bool = false
	var issetEnableDataBackup bool = false
	var issetBinlogPolicy bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFullBackupTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataBackupRetentionDay = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFullBackupPeriods = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogBackupRetentionDay = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableDataBackup = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetBinlogPolicy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataFullBackupTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataBackupRetentionDay {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDataFullBackupPeriods {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupRetentionDay {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEnableDataBackup {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetBinlogPolicy {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupConfigResp[fieldId]))
}

func (p *DescribeBackupConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFullBackupTime = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataBackupRetentionDay = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFullBackupPeriods = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DataIncrBackupPeriods = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogBackupRetentionDay = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField6(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableDataBackup = _field
	return nil
}
func (p *DescribeBackupConfigResp) ReadField7(iprot thrift.TProtocol) error {
	_field := NewBinlogRetentionPolicy()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BinlogPolicy = _field
	return nil
}

func (p *DescribeBackupConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFullBackupTime", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFullBackupTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataBackupRetentionDay", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.DataBackupRetentionDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFullBackupPeriods", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFullBackupPeriods); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataIncrBackupPeriods() {
		if err = oprot.WriteFieldBegin("DataIncrBackupPeriods", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DataIncrBackupPeriods); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogBackupRetentionDay", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.LogBackupRetentionDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableDataBackup", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableDataBackup)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BinlogPolicy", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BinlogPolicy.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeBackupConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupConfigResp(%+v)", *p)

}

func (p *DescribeBackupConfigResp) DeepEqual(ano *DescribeBackupConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataFullBackupTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataBackupRetentionDay) {
		return false
	}
	if !p.Field3DeepEqual(ano.DataFullBackupPeriods) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataIncrBackupPeriods) {
		return false
	}
	if !p.Field5DeepEqual(ano.LogBackupRetentionDay) {
		return false
	}
	if !p.Field6DeepEqual(ano.EnableDataBackup) {
		return false
	}
	if !p.Field7DeepEqual(ano.BinlogPolicy) {
		return false
	}
	return true
}

func (p *DescribeBackupConfigResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DataFullBackupTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field2DeepEqual(src int32) bool {

	if p.DataBackupRetentionDay != src {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DataFullBackupPeriods, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field4DeepEqual(src *string) bool {

	if p.DataIncrBackupPeriods == src {
		return true
	} else if p.DataIncrBackupPeriods == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DataIncrBackupPeriods, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field5DeepEqual(src int32) bool {

	if p.LogBackupRetentionDay != src {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field6DeepEqual(src EnableSwitch) bool {

	if p.EnableDataBackup != src {
		return false
	}
	return true
}
func (p *DescribeBackupConfigResp) Field7DeepEqual(src *BinlogRetentionPolicy) bool {

	if !p.BinlogPolicy.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyBackupConfigReq struct {
	InstanceId             string                 `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	DataFullBackupTime     string                 `thrift:"DataFullBackupTime,2" frugal:"2,default,string" json:"DataFullBackupTime"`
	DataBackupRetentionDay int32                  `thrift:"DataBackupRetentionDay,3" frugal:"3,default,i32" json:"DataBackupRetentionDay"`
	DataFullBackupPeriods  string                 `thrift:"DataFullBackupPeriods,4" frugal:"4,default,string" json:"DataFullBackupPeriods"`
	DataIncrBackupPeriods  string                 `thrift:"DataIncrBackupPeriods,5" frugal:"5,default,string" json:"DataIncrBackupPeriods"`
	LogBackupRetentionDay  int32                  `thrift:"LogBackupRetentionDay,6" frugal:"6,default,i32" json:"LogBackupRetentionDay"`
	EnableDataBackup       *EnableSwitch          `thrift:"EnableDataBackup,7,optional" frugal:"7,optional,EnableSwitch" json:"EnableDataBackup,omitempty"`
	BinlogPolicy           *BinlogRetentionPolicy `thrift:"BinlogPolicy,8,optional" frugal:"8,optional,BinlogRetentionPolicy" json:"BinlogPolicy,omitempty"`
}

func NewModifyBackupConfigReq() *ModifyBackupConfigReq {
	return &ModifyBackupConfigReq{}
}

func (p *ModifyBackupConfigReq) InitDefault() {
}

func (p *ModifyBackupConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyBackupConfigReq) GetDataFullBackupTime() (v string) {
	return p.DataFullBackupTime
}

func (p *ModifyBackupConfigReq) GetDataBackupRetentionDay() (v int32) {
	return p.DataBackupRetentionDay
}

func (p *ModifyBackupConfigReq) GetDataFullBackupPeriods() (v string) {
	return p.DataFullBackupPeriods
}

func (p *ModifyBackupConfigReq) GetDataIncrBackupPeriods() (v string) {
	return p.DataIncrBackupPeriods
}

func (p *ModifyBackupConfigReq) GetLogBackupRetentionDay() (v int32) {
	return p.LogBackupRetentionDay
}

var ModifyBackupConfigReq_EnableDataBackup_DEFAULT EnableSwitch

func (p *ModifyBackupConfigReq) GetEnableDataBackup() (v EnableSwitch) {
	if !p.IsSetEnableDataBackup() {
		return ModifyBackupConfigReq_EnableDataBackup_DEFAULT
	}
	return *p.EnableDataBackup
}

var ModifyBackupConfigReq_BinlogPolicy_DEFAULT *BinlogRetentionPolicy

func (p *ModifyBackupConfigReq) GetBinlogPolicy() (v *BinlogRetentionPolicy) {
	if !p.IsSetBinlogPolicy() {
		return ModifyBackupConfigReq_BinlogPolicy_DEFAULT
	}
	return p.BinlogPolicy
}
func (p *ModifyBackupConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyBackupConfigReq) SetDataFullBackupTime(val string) {
	p.DataFullBackupTime = val
}
func (p *ModifyBackupConfigReq) SetDataBackupRetentionDay(val int32) {
	p.DataBackupRetentionDay = val
}
func (p *ModifyBackupConfigReq) SetDataFullBackupPeriods(val string) {
	p.DataFullBackupPeriods = val
}
func (p *ModifyBackupConfigReq) SetDataIncrBackupPeriods(val string) {
	p.DataIncrBackupPeriods = val
}
func (p *ModifyBackupConfigReq) SetLogBackupRetentionDay(val int32) {
	p.LogBackupRetentionDay = val
}
func (p *ModifyBackupConfigReq) SetEnableDataBackup(val *EnableSwitch) {
	p.EnableDataBackup = val
}
func (p *ModifyBackupConfigReq) SetBinlogPolicy(val *BinlogRetentionPolicy) {
	p.BinlogPolicy = val
}

var fieldIDToName_ModifyBackupConfigReq = map[int16]string{
	1: "InstanceId",
	2: "DataFullBackupTime",
	3: "DataBackupRetentionDay",
	4: "DataFullBackupPeriods",
	5: "DataIncrBackupPeriods",
	6: "LogBackupRetentionDay",
	7: "EnableDataBackup",
	8: "BinlogPolicy",
}

func (p *ModifyBackupConfigReq) IsSetEnableDataBackup() bool {
	return p.EnableDataBackup != nil
}

func (p *ModifyBackupConfigReq) IsSetBinlogPolicy() bool {
	return p.BinlogPolicy != nil
}

func (p *ModifyBackupConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyBackupConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyBackupConfigReq[fieldId]))
}

func (p *ModifyBackupConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFullBackupTime = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataBackupRetentionDay = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFullBackupPeriods = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataIncrBackupPeriods = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogBackupRetentionDay = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EnableSwitch(v)
		_field = &tmp
	}
	p.EnableDataBackup = _field
	return nil
}
func (p *ModifyBackupConfigReq) ReadField8(iprot thrift.TProtocol) error {
	_field := NewBinlogRetentionPolicy()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BinlogPolicy = _field
	return nil
}

func (p *ModifyBackupConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyBackupConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFullBackupTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFullBackupTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataBackupRetentionDay", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.DataBackupRetentionDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFullBackupPeriods", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFullBackupPeriods); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataIncrBackupPeriods", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataIncrBackupPeriods); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogBackupRetentionDay", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.LogBackupRetentionDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableDataBackup() {
		if err = oprot.WriteFieldBegin("EnableDataBackup", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EnableDataBackup)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBinlogPolicy() {
		if err = oprot.WriteFieldBegin("BinlogPolicy", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BinlogPolicy.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ModifyBackupConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyBackupConfigReq(%+v)", *p)

}

func (p *ModifyBackupConfigReq) DeepEqual(ano *ModifyBackupConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataFullBackupTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.DataBackupRetentionDay) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataFullBackupPeriods) {
		return false
	}
	if !p.Field5DeepEqual(ano.DataIncrBackupPeriods) {
		return false
	}
	if !p.Field6DeepEqual(ano.LogBackupRetentionDay) {
		return false
	}
	if !p.Field7DeepEqual(ano.EnableDataBackup) {
		return false
	}
	if !p.Field8DeepEqual(ano.BinlogPolicy) {
		return false
	}
	return true
}

func (p *ModifyBackupConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DataFullBackupTime, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field3DeepEqual(src int32) bool {

	if p.DataBackupRetentionDay != src {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DataFullBackupPeriods, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DataIncrBackupPeriods, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field6DeepEqual(src int32) bool {

	if p.LogBackupRetentionDay != src {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field7DeepEqual(src *EnableSwitch) bool {

	if p.EnableDataBackup == src {
		return true
	} else if p.EnableDataBackup == nil || src == nil {
		return false
	}
	if *p.EnableDataBackup != *src {
		return false
	}
	return true
}
func (p *ModifyBackupConfigReq) Field8DeepEqual(src *BinlogRetentionPolicy) bool {

	if !p.BinlogPolicy.DeepEqual(src) {
		return false
	}
	return true
}

type RecoveryDBInstanceReq struct {
	InstanceId           string            `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	RecoveryType         RecoveryType      `thrift:"RecoveryType,2,required" frugal:"2,required,RecoveryType" json:"RecoveryType"`
	BackupId             string            `thrift:"BackupId,3" frugal:"3,default,string" json:"BackupId"`
	RestoreTime          string            `thrift:"RestoreTime,4" frugal:"4,default,string" json:"RestoreTime"`
	DBNames              string            `thrift:"DBNames,5" frugal:"5,default,string" json:"DBNames"`
	NodeName             string            `thrift:"NodeName,6" frugal:"6,default,string" json:"NodeName"`
	InstanceName         *string           `thrift:"InstanceName,7,optional" frugal:"7,optional,string" json:"InstanceName,omitempty"`
	InstanceSpec         *InstanceSpec     `thrift:"InstanceSpec,8,optional" frugal:"8,optional,InstanceSpec" json:"InstanceSpec,omitempty"`
	StorageType          *StorageType      `thrift:"StorageType,9,optional" frugal:"9,optional,StorageType" json:"StorageType,omitempty"`
	StorageSpaceGB       *int32            `thrift:"StorageSpaceGB,10,optional" frugal:"10,optional,i32" json:"StorageSpaceGB,omitempty"`
	NetworkType          *NetworkType      `thrift:"NetworkType,11,optional" frugal:"11,optional,NetworkType" json:"NetworkType,omitempty"`
	ChargeType           *ChargeType       `thrift:"ChargeType,12,optional" frugal:"12,optional,ChargeType" json:"ChargeType,omitempty"`
	VpcID                *string           `thrift:"VpcID,13,optional" frugal:"13,optional,string" json:"VpcID,omitempty"`
	RecoveryFromMaster   *bool             `thrift:"RecoveryFromMaster,14,optional" frugal:"14,optional,bool" json:"RecoveryFromMaster,omitempty"`
	AutoRenew            *bool             `thrift:"AutoRenew,15,optional" frugal:"15,optional,bool" json:"AutoRenew,omitempty"`
	PrepaidPeriod        *PrepaidPeriod    `thrift:"PrepaidPeriod,16,optional" frugal:"16,optional,PrepaidPeriod" json:"PrepaidPeriod,omitempty"`
	UsedTime             *int32            `thrift:"UsedTime,17,optional" frugal:"17,optional,i32" json:"UsedTime,omitempty"`
	RequestSource        *RequestSource    `thrift:"RequestSource,18,optional" frugal:"18,optional,RequestSource" json:"RequestSource,omitempty"`
	ParameterTemplateId  *string           `thrift:"ParameterTemplateId,19,optional" frugal:"19,optional,string" json:"ParameterTemplateId,omitempty"`
	APIVersion           *string           `thrift:"APIVersion,20,optional" frugal:"20,optional,string" json:"APIVersion,omitempty"`
	ProjectName          *string           `thrift:"ProjectName,21,optional" frugal:"21,optional,string" json:"ProjectName,omitempty"`
	SubnetId             *string           `thrift:"SubnetId,22,optional" frugal:"22,optional,string" json:"SubnetId,omitempty"`
	NodeInfo             []*NodeInfoObject `thrift:"NodeInfo,23,optional" frugal:"23,optional,list<NodeInfoObject>" json:"NodeInfo,omitempty"`
	ShardNumber          *int32            `thrift:"ShardNumber,24,optional" frugal:"24,optional,i32" json:"ShardNumber,omitempty"`
	SuperAccountPassword *string           `thrift:"SuperAccountPassword,25,optional" frugal:"25,optional,string" json:"SuperAccountPassword,omitempty"`
}

func NewRecoveryDBInstanceReq() *RecoveryDBInstanceReq {
	return &RecoveryDBInstanceReq{}
}

func (p *RecoveryDBInstanceReq) InitDefault() {
}

func (p *RecoveryDBInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RecoveryDBInstanceReq) GetRecoveryType() (v RecoveryType) {
	return p.RecoveryType
}

func (p *RecoveryDBInstanceReq) GetBackupId() (v string) {
	return p.BackupId
}

func (p *RecoveryDBInstanceReq) GetRestoreTime() (v string) {
	return p.RestoreTime
}

func (p *RecoveryDBInstanceReq) GetDBNames() (v string) {
	return p.DBNames
}

func (p *RecoveryDBInstanceReq) GetNodeName() (v string) {
	return p.NodeName
}

var RecoveryDBInstanceReq_InstanceName_DEFAULT string

func (p *RecoveryDBInstanceReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return RecoveryDBInstanceReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var RecoveryDBInstanceReq_InstanceSpec_DEFAULT *InstanceSpec

func (p *RecoveryDBInstanceReq) GetInstanceSpec() (v *InstanceSpec) {
	if !p.IsSetInstanceSpec() {
		return RecoveryDBInstanceReq_InstanceSpec_DEFAULT
	}
	return p.InstanceSpec
}

var RecoveryDBInstanceReq_StorageType_DEFAULT StorageType

func (p *RecoveryDBInstanceReq) GetStorageType() (v StorageType) {
	if !p.IsSetStorageType() {
		return RecoveryDBInstanceReq_StorageType_DEFAULT
	}
	return *p.StorageType
}

var RecoveryDBInstanceReq_StorageSpaceGB_DEFAULT int32

func (p *RecoveryDBInstanceReq) GetStorageSpaceGB() (v int32) {
	if !p.IsSetStorageSpaceGB() {
		return RecoveryDBInstanceReq_StorageSpaceGB_DEFAULT
	}
	return *p.StorageSpaceGB
}

var RecoveryDBInstanceReq_NetworkType_DEFAULT NetworkType

func (p *RecoveryDBInstanceReq) GetNetworkType() (v NetworkType) {
	if !p.IsSetNetworkType() {
		return RecoveryDBInstanceReq_NetworkType_DEFAULT
	}
	return *p.NetworkType
}

var RecoveryDBInstanceReq_ChargeType_DEFAULT ChargeType

func (p *RecoveryDBInstanceReq) GetChargeType() (v ChargeType) {
	if !p.IsSetChargeType() {
		return RecoveryDBInstanceReq_ChargeType_DEFAULT
	}
	return *p.ChargeType
}

var RecoveryDBInstanceReq_VpcID_DEFAULT string

func (p *RecoveryDBInstanceReq) GetVpcID() (v string) {
	if !p.IsSetVpcID() {
		return RecoveryDBInstanceReq_VpcID_DEFAULT
	}
	return *p.VpcID
}

var RecoveryDBInstanceReq_RecoveryFromMaster_DEFAULT bool

func (p *RecoveryDBInstanceReq) GetRecoveryFromMaster() (v bool) {
	if !p.IsSetRecoveryFromMaster() {
		return RecoveryDBInstanceReq_RecoveryFromMaster_DEFAULT
	}
	return *p.RecoveryFromMaster
}

var RecoveryDBInstanceReq_AutoRenew_DEFAULT bool

func (p *RecoveryDBInstanceReq) GetAutoRenew() (v bool) {
	if !p.IsSetAutoRenew() {
		return RecoveryDBInstanceReq_AutoRenew_DEFAULT
	}
	return *p.AutoRenew
}

var RecoveryDBInstanceReq_PrepaidPeriod_DEFAULT PrepaidPeriod

func (p *RecoveryDBInstanceReq) GetPrepaidPeriod() (v PrepaidPeriod) {
	if !p.IsSetPrepaidPeriod() {
		return RecoveryDBInstanceReq_PrepaidPeriod_DEFAULT
	}
	return *p.PrepaidPeriod
}

var RecoveryDBInstanceReq_UsedTime_DEFAULT int32

func (p *RecoveryDBInstanceReq) GetUsedTime() (v int32) {
	if !p.IsSetUsedTime() {
		return RecoveryDBInstanceReq_UsedTime_DEFAULT
	}
	return *p.UsedTime
}

var RecoveryDBInstanceReq_RequestSource_DEFAULT RequestSource

func (p *RecoveryDBInstanceReq) GetRequestSource() (v RequestSource) {
	if !p.IsSetRequestSource() {
		return RecoveryDBInstanceReq_RequestSource_DEFAULT
	}
	return *p.RequestSource
}

var RecoveryDBInstanceReq_ParameterTemplateId_DEFAULT string

func (p *RecoveryDBInstanceReq) GetParameterTemplateId() (v string) {
	if !p.IsSetParameterTemplateId() {
		return RecoveryDBInstanceReq_ParameterTemplateId_DEFAULT
	}
	return *p.ParameterTemplateId
}

var RecoveryDBInstanceReq_APIVersion_DEFAULT string

func (p *RecoveryDBInstanceReq) GetAPIVersion() (v string) {
	if !p.IsSetAPIVersion() {
		return RecoveryDBInstanceReq_APIVersion_DEFAULT
	}
	return *p.APIVersion
}

var RecoveryDBInstanceReq_ProjectName_DEFAULT string

func (p *RecoveryDBInstanceReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return RecoveryDBInstanceReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var RecoveryDBInstanceReq_SubnetId_DEFAULT string

func (p *RecoveryDBInstanceReq) GetSubnetId() (v string) {
	if !p.IsSetSubnetId() {
		return RecoveryDBInstanceReq_SubnetId_DEFAULT
	}
	return *p.SubnetId
}

var RecoveryDBInstanceReq_NodeInfo_DEFAULT []*NodeInfoObject

func (p *RecoveryDBInstanceReq) GetNodeInfo() (v []*NodeInfoObject) {
	if !p.IsSetNodeInfo() {
		return RecoveryDBInstanceReq_NodeInfo_DEFAULT
	}
	return p.NodeInfo
}

var RecoveryDBInstanceReq_ShardNumber_DEFAULT int32

func (p *RecoveryDBInstanceReq) GetShardNumber() (v int32) {
	if !p.IsSetShardNumber() {
		return RecoveryDBInstanceReq_ShardNumber_DEFAULT
	}
	return *p.ShardNumber
}

var RecoveryDBInstanceReq_SuperAccountPassword_DEFAULT string

func (p *RecoveryDBInstanceReq) GetSuperAccountPassword() (v string) {
	if !p.IsSetSuperAccountPassword() {
		return RecoveryDBInstanceReq_SuperAccountPassword_DEFAULT
	}
	return *p.SuperAccountPassword
}
func (p *RecoveryDBInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RecoveryDBInstanceReq) SetRecoveryType(val RecoveryType) {
	p.RecoveryType = val
}
func (p *RecoveryDBInstanceReq) SetBackupId(val string) {
	p.BackupId = val
}
func (p *RecoveryDBInstanceReq) SetRestoreTime(val string) {
	p.RestoreTime = val
}
func (p *RecoveryDBInstanceReq) SetDBNames(val string) {
	p.DBNames = val
}
func (p *RecoveryDBInstanceReq) SetNodeName(val string) {
	p.NodeName = val
}
func (p *RecoveryDBInstanceReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *RecoveryDBInstanceReq) SetInstanceSpec(val *InstanceSpec) {
	p.InstanceSpec = val
}
func (p *RecoveryDBInstanceReq) SetStorageType(val *StorageType) {
	p.StorageType = val
}
func (p *RecoveryDBInstanceReq) SetStorageSpaceGB(val *int32) {
	p.StorageSpaceGB = val
}
func (p *RecoveryDBInstanceReq) SetNetworkType(val *NetworkType) {
	p.NetworkType = val
}
func (p *RecoveryDBInstanceReq) SetChargeType(val *ChargeType) {
	p.ChargeType = val
}
func (p *RecoveryDBInstanceReq) SetVpcID(val *string) {
	p.VpcID = val
}
func (p *RecoveryDBInstanceReq) SetRecoveryFromMaster(val *bool) {
	p.RecoveryFromMaster = val
}
func (p *RecoveryDBInstanceReq) SetAutoRenew(val *bool) {
	p.AutoRenew = val
}
func (p *RecoveryDBInstanceReq) SetPrepaidPeriod(val *PrepaidPeriod) {
	p.PrepaidPeriod = val
}
func (p *RecoveryDBInstanceReq) SetUsedTime(val *int32) {
	p.UsedTime = val
}
func (p *RecoveryDBInstanceReq) SetRequestSource(val *RequestSource) {
	p.RequestSource = val
}
func (p *RecoveryDBInstanceReq) SetParameterTemplateId(val *string) {
	p.ParameterTemplateId = val
}
func (p *RecoveryDBInstanceReq) SetAPIVersion(val *string) {
	p.APIVersion = val
}
func (p *RecoveryDBInstanceReq) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *RecoveryDBInstanceReq) SetSubnetId(val *string) {
	p.SubnetId = val
}
func (p *RecoveryDBInstanceReq) SetNodeInfo(val []*NodeInfoObject) {
	p.NodeInfo = val
}
func (p *RecoveryDBInstanceReq) SetShardNumber(val *int32) {
	p.ShardNumber = val
}
func (p *RecoveryDBInstanceReq) SetSuperAccountPassword(val *string) {
	p.SuperAccountPassword = val
}

var fieldIDToName_RecoveryDBInstanceReq = map[int16]string{
	1:  "InstanceId",
	2:  "RecoveryType",
	3:  "BackupId",
	4:  "RestoreTime",
	5:  "DBNames",
	6:  "NodeName",
	7:  "InstanceName",
	8:  "InstanceSpec",
	9:  "StorageType",
	10: "StorageSpaceGB",
	11: "NetworkType",
	12: "ChargeType",
	13: "VpcID",
	14: "RecoveryFromMaster",
	15: "AutoRenew",
	16: "PrepaidPeriod",
	17: "UsedTime",
	18: "RequestSource",
	19: "ParameterTemplateId",
	20: "APIVersion",
	21: "ProjectName",
	22: "SubnetId",
	23: "NodeInfo",
	24: "ShardNumber",
	25: "SuperAccountPassword",
}

func (p *RecoveryDBInstanceReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *RecoveryDBInstanceReq) IsSetInstanceSpec() bool {
	return p.InstanceSpec != nil
}

func (p *RecoveryDBInstanceReq) IsSetStorageType() bool {
	return p.StorageType != nil
}

func (p *RecoveryDBInstanceReq) IsSetStorageSpaceGB() bool {
	return p.StorageSpaceGB != nil
}

func (p *RecoveryDBInstanceReq) IsSetNetworkType() bool {
	return p.NetworkType != nil
}

func (p *RecoveryDBInstanceReq) IsSetChargeType() bool {
	return p.ChargeType != nil
}

func (p *RecoveryDBInstanceReq) IsSetVpcID() bool {
	return p.VpcID != nil
}

func (p *RecoveryDBInstanceReq) IsSetRecoveryFromMaster() bool {
	return p.RecoveryFromMaster != nil
}

func (p *RecoveryDBInstanceReq) IsSetAutoRenew() bool {
	return p.AutoRenew != nil
}

func (p *RecoveryDBInstanceReq) IsSetPrepaidPeriod() bool {
	return p.PrepaidPeriod != nil
}

func (p *RecoveryDBInstanceReq) IsSetUsedTime() bool {
	return p.UsedTime != nil
}

func (p *RecoveryDBInstanceReq) IsSetRequestSource() bool {
	return p.RequestSource != nil
}

func (p *RecoveryDBInstanceReq) IsSetParameterTemplateId() bool {
	return p.ParameterTemplateId != nil
}

func (p *RecoveryDBInstanceReq) IsSetAPIVersion() bool {
	return p.APIVersion != nil
}

func (p *RecoveryDBInstanceReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *RecoveryDBInstanceReq) IsSetSubnetId() bool {
	return p.SubnetId != nil
}

func (p *RecoveryDBInstanceReq) IsSetNodeInfo() bool {
	return p.NodeInfo != nil
}

func (p *RecoveryDBInstanceReq) IsSetShardNumber() bool {
	return p.ShardNumber != nil
}

func (p *RecoveryDBInstanceReq) IsSetSuperAccountPassword() bool {
	return p.SuperAccountPassword != nil
}

func (p *RecoveryDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetRecoveryType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRecoveryType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRecoveryType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RecoveryDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RecoveryDBInstanceReq[fieldId]))
}

func (p *RecoveryDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field RecoveryType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RecoveryType(v)
	}
	p.RecoveryType = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBNames = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeName = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField8(iprot thrift.TProtocol) error {
	_field := NewInstanceSpec()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceSpec = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *StorageType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := StorageType(v)
		_field = &tmp
	}
	p.StorageType = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StorageSpaceGB = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *NetworkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := NetworkType(v)
		_field = &tmp
	}
	p.NetworkType = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *ChargeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ChargeType(v)
		_field = &tmp
	}
	p.ChargeType = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VpcID = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RecoveryFromMaster = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoRenew = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *PrepaidPeriod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PrepaidPeriod(v)
		_field = &tmp
	}
	p.PrepaidPeriod = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UsedTime = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField18(iprot thrift.TProtocol) error {

	var _field *RequestSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RequestSource(v)
		_field = &tmp
	}
	p.RequestSource = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParameterTemplateId = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.APIVersion = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField22(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubnetId = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeInfoObject, 0, size)
	values := make([]NodeInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeInfo = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField24(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardNumber = _field
	return nil
}
func (p *RecoveryDBInstanceReq) ReadField25(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuperAccountPassword = _field
	return nil
}

func (p *RecoveryDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RecoveryDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RecoveryType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RecoveryType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RestoreTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBNames", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBNames); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceSpec() {
		if err = oprot.WriteFieldBegin("InstanceSpec", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InstanceSpec.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageType() {
		if err = oprot.WriteFieldBegin("StorageType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.StorageType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageSpaceGB() {
		if err = oprot.WriteFieldBegin("StorageSpaceGB", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StorageSpaceGB); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNetworkType() {
		if err = oprot.WriteFieldBegin("NetworkType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NetworkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeType() {
		if err = oprot.WriteFieldBegin("ChargeType", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ChargeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetVpcID() {
		if err = oprot.WriteFieldBegin("VpcID", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VpcID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecoveryFromMaster() {
		if err = oprot.WriteFieldBegin("RecoveryFromMaster", thrift.BOOL, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.RecoveryFromMaster); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoRenew() {
		if err = oprot.WriteFieldBegin("AutoRenew", thrift.BOOL, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AutoRenew); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrepaidPeriod() {
		if err = oprot.WriteFieldBegin("PrepaidPeriod", thrift.I32, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.PrepaidPeriod)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetUsedTime() {
		if err = oprot.WriteFieldBegin("UsedTime", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.UsedTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestSource() {
		if err = oprot.WriteFieldBegin("RequestSource", thrift.I32, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RequestSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetParameterTemplateId() {
		if err = oprot.WriteFieldBegin("ParameterTemplateId", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParameterTemplateId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetAPIVersion() {
		if err = oprot.WriteFieldBegin("APIVersion", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.APIVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubnetId() {
		if err = oprot.WriteFieldBegin("SubnetId", thrift.STRING, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SubnetId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeInfo() {
		if err = oprot.WriteFieldBegin("NodeInfo", thrift.LIST, 23); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeInfo)); err != nil {
			return err
		}
		for _, v := range p.NodeInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardNumber() {
		if err = oprot.WriteFieldBegin("ShardNumber", thrift.I32, 24); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ShardNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) writeField25(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuperAccountPassword() {
		if err = oprot.WriteFieldBegin("SuperAccountPassword", thrift.STRING, 25); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SuperAccountPassword); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}

func (p *RecoveryDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecoveryDBInstanceReq(%+v)", *p)

}

func (p *RecoveryDBInstanceReq) DeepEqual(ano *RecoveryDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.RecoveryType) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field4DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.DBNames) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeName) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field8DeepEqual(ano.InstanceSpec) {
		return false
	}
	if !p.Field9DeepEqual(ano.StorageType) {
		return false
	}
	if !p.Field10DeepEqual(ano.StorageSpaceGB) {
		return false
	}
	if !p.Field11DeepEqual(ano.NetworkType) {
		return false
	}
	if !p.Field12DeepEqual(ano.ChargeType) {
		return false
	}
	if !p.Field13DeepEqual(ano.VpcID) {
		return false
	}
	if !p.Field14DeepEqual(ano.RecoveryFromMaster) {
		return false
	}
	if !p.Field15DeepEqual(ano.AutoRenew) {
		return false
	}
	if !p.Field16DeepEqual(ano.PrepaidPeriod) {
		return false
	}
	if !p.Field17DeepEqual(ano.UsedTime) {
		return false
	}
	if !p.Field18DeepEqual(ano.RequestSource) {
		return false
	}
	if !p.Field19DeepEqual(ano.ParameterTemplateId) {
		return false
	}
	if !p.Field20DeepEqual(ano.APIVersion) {
		return false
	}
	if !p.Field21DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field22DeepEqual(ano.SubnetId) {
		return false
	}
	if !p.Field23DeepEqual(ano.NodeInfo) {
		return false
	}
	if !p.Field24DeepEqual(ano.ShardNumber) {
		return false
	}
	if !p.Field25DeepEqual(ano.SuperAccountPassword) {
		return false
	}
	return true
}

func (p *RecoveryDBInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field2DeepEqual(src RecoveryType) bool {

	if p.RecoveryType != src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RestoreTime, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DBNames, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.NodeName, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field7DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field8DeepEqual(src *InstanceSpec) bool {

	if !p.InstanceSpec.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field9DeepEqual(src *StorageType) bool {

	if p.StorageType == src {
		return true
	} else if p.StorageType == nil || src == nil {
		return false
	}
	if *p.StorageType != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field10DeepEqual(src *int32) bool {

	if p.StorageSpaceGB == src {
		return true
	} else if p.StorageSpaceGB == nil || src == nil {
		return false
	}
	if *p.StorageSpaceGB != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field11DeepEqual(src *NetworkType) bool {

	if p.NetworkType == src {
		return true
	} else if p.NetworkType == nil || src == nil {
		return false
	}
	if *p.NetworkType != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field12DeepEqual(src *ChargeType) bool {

	if p.ChargeType == src {
		return true
	} else if p.ChargeType == nil || src == nil {
		return false
	}
	if *p.ChargeType != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field13DeepEqual(src *string) bool {

	if p.VpcID == src {
		return true
	} else if p.VpcID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.VpcID, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field14DeepEqual(src *bool) bool {

	if p.RecoveryFromMaster == src {
		return true
	} else if p.RecoveryFromMaster == nil || src == nil {
		return false
	}
	if *p.RecoveryFromMaster != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field15DeepEqual(src *bool) bool {

	if p.AutoRenew == src {
		return true
	} else if p.AutoRenew == nil || src == nil {
		return false
	}
	if *p.AutoRenew != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field16DeepEqual(src *PrepaidPeriod) bool {

	if p.PrepaidPeriod == src {
		return true
	} else if p.PrepaidPeriod == nil || src == nil {
		return false
	}
	if *p.PrepaidPeriod != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field17DeepEqual(src *int32) bool {

	if p.UsedTime == src {
		return true
	} else if p.UsedTime == nil || src == nil {
		return false
	}
	if *p.UsedTime != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field18DeepEqual(src *RequestSource) bool {

	if p.RequestSource == src {
		return true
	} else if p.RequestSource == nil || src == nil {
		return false
	}
	if *p.RequestSource != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field19DeepEqual(src *string) bool {

	if p.ParameterTemplateId == src {
		return true
	} else if p.ParameterTemplateId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParameterTemplateId, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field20DeepEqual(src *string) bool {

	if p.APIVersion == src {
		return true
	} else if p.APIVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.APIVersion, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field21DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field22DeepEqual(src *string) bool {

	if p.SubnetId == src {
		return true
	} else if p.SubnetId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SubnetId, *src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field23DeepEqual(src []*NodeInfoObject) bool {

	if len(p.NodeInfo) != len(src) {
		return false
	}
	for i, v := range p.NodeInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field24DeepEqual(src *int32) bool {

	if p.ShardNumber == src {
		return true
	} else if p.ShardNumber == nil || src == nil {
		return false
	}
	if *p.ShardNumber != *src {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceReq) Field25DeepEqual(src *string) bool {

	if p.SuperAccountPassword == src {
		return true
	} else if p.SuperAccountPassword == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SuperAccountPassword, *src) != 0 {
		return false
	}
	return true
}

type RecoveryDBInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	OrderNO    string `thrift:"OrderNO,2,required" frugal:"2,required,string" json:"OrderNO"`
}

func NewRecoveryDBInstanceResp() *RecoveryDBInstanceResp {
	return &RecoveryDBInstanceResp{}
}

func (p *RecoveryDBInstanceResp) InitDefault() {
}

func (p *RecoveryDBInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RecoveryDBInstanceResp) GetOrderNO() (v string) {
	return p.OrderNO
}
func (p *RecoveryDBInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RecoveryDBInstanceResp) SetOrderNO(val string) {
	p.OrderNO = val
}

var fieldIDToName_RecoveryDBInstanceResp = map[int16]string{
	1: "InstanceId",
	2: "OrderNO",
}

func (p *RecoveryDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderNO bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderNO = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderNO {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RecoveryDBInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RecoveryDBInstanceResp[fieldId]))
}

func (p *RecoveryDBInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RecoveryDBInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderNO = _field
	return nil
}

func (p *RecoveryDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryDBInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RecoveryDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RecoveryDBInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RecoveryDBInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderNO", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderNO); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RecoveryDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecoveryDBInstanceResp(%+v)", *p)

}

func (p *RecoveryDBInstanceResp) DeepEqual(ano *RecoveryDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderNO) {
		return false
	}
	return true
}

func (p *RecoveryDBInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryDBInstanceResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderNO, src) != 0 {
		return false
	}
	return true
}

type RecoveryInstanceNodeReq struct {
	InstanceId         string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId           string `thrift:"BackupId,2" frugal:"2,default,string" json:"BackupId"`
	RestoreTime        string `thrift:"RestoreTime,3" frugal:"3,default,string" json:"RestoreTime"`
	NodeName           string `thrift:"NodeName,4" frugal:"4,default,string" json:"NodeName"`
	RecoveryFromMaster *bool  `thrift:"RecoveryFromMaster,5,optional" frugal:"5,optional,bool" json:"RecoveryFromMaster,omitempty"`
	ForceRecovery      *bool  `thrift:"ForceRecovery,6,optional" frugal:"6,optional,bool" json:"ForceRecovery,omitempty"`
}

func NewRecoveryInstanceNodeReq() *RecoveryInstanceNodeReq {
	return &RecoveryInstanceNodeReq{}
}

func (p *RecoveryInstanceNodeReq) InitDefault() {
}

func (p *RecoveryInstanceNodeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RecoveryInstanceNodeReq) GetBackupId() (v string) {
	return p.BackupId
}

func (p *RecoveryInstanceNodeReq) GetRestoreTime() (v string) {
	return p.RestoreTime
}

func (p *RecoveryInstanceNodeReq) GetNodeName() (v string) {
	return p.NodeName
}

var RecoveryInstanceNodeReq_RecoveryFromMaster_DEFAULT bool

func (p *RecoveryInstanceNodeReq) GetRecoveryFromMaster() (v bool) {
	if !p.IsSetRecoveryFromMaster() {
		return RecoveryInstanceNodeReq_RecoveryFromMaster_DEFAULT
	}
	return *p.RecoveryFromMaster
}

var RecoveryInstanceNodeReq_ForceRecovery_DEFAULT bool

func (p *RecoveryInstanceNodeReq) GetForceRecovery() (v bool) {
	if !p.IsSetForceRecovery() {
		return RecoveryInstanceNodeReq_ForceRecovery_DEFAULT
	}
	return *p.ForceRecovery
}
func (p *RecoveryInstanceNodeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RecoveryInstanceNodeReq) SetBackupId(val string) {
	p.BackupId = val
}
func (p *RecoveryInstanceNodeReq) SetRestoreTime(val string) {
	p.RestoreTime = val
}
func (p *RecoveryInstanceNodeReq) SetNodeName(val string) {
	p.NodeName = val
}
func (p *RecoveryInstanceNodeReq) SetRecoveryFromMaster(val *bool) {
	p.RecoveryFromMaster = val
}
func (p *RecoveryInstanceNodeReq) SetForceRecovery(val *bool) {
	p.ForceRecovery = val
}

var fieldIDToName_RecoveryInstanceNodeReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
	3: "RestoreTime",
	4: "NodeName",
	5: "RecoveryFromMaster",
	6: "ForceRecovery",
}

func (p *RecoveryInstanceNodeReq) IsSetRecoveryFromMaster() bool {
	return p.RecoveryFromMaster != nil
}

func (p *RecoveryInstanceNodeReq) IsSetForceRecovery() bool {
	return p.ForceRecovery != nil
}

func (p *RecoveryInstanceNodeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryInstanceNodeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RecoveryInstanceNodeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RecoveryInstanceNodeReq[fieldId]))
}

func (p *RecoveryInstanceNodeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RecoveryInstanceNodeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}
func (p *RecoveryInstanceNodeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RecoveryInstanceNodeReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeName = _field
	return nil
}
func (p *RecoveryInstanceNodeReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RecoveryFromMaster = _field
	return nil
}
func (p *RecoveryInstanceNodeReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ForceRecovery = _field
	return nil
}

func (p *RecoveryInstanceNodeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RecoveryInstanceNodeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RecoveryInstanceNodeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RestoreTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecoveryFromMaster() {
		if err = oprot.WriteFieldBegin("RecoveryFromMaster", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.RecoveryFromMaster); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetForceRecovery() {
		if err = oprot.WriteFieldBegin("ForceRecovery", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ForceRecovery); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RecoveryInstanceNodeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecoveryInstanceNodeReq(%+v)", *p)

}

func (p *RecoveryInstanceNodeReq) DeepEqual(ano *RecoveryInstanceNodeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeName) {
		return false
	}
	if !p.Field5DeepEqual(ano.RecoveryFromMaster) {
		return false
	}
	if !p.Field6DeepEqual(ano.ForceRecovery) {
		return false
	}
	return true
}

func (p *RecoveryInstanceNodeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryInstanceNodeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryInstanceNodeReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RestoreTime, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryInstanceNodeReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.NodeName, src) != 0 {
		return false
	}
	return true
}
func (p *RecoveryInstanceNodeReq) Field5DeepEqual(src *bool) bool {

	if p.RecoveryFromMaster == src {
		return true
	} else if p.RecoveryFromMaster == nil || src == nil {
		return false
	}
	if *p.RecoveryFromMaster != *src {
		return false
	}
	return true
}
func (p *RecoveryInstanceNodeReq) Field6DeepEqual(src *bool) bool {

	if p.ForceRecovery == src {
		return true
	} else if p.ForceRecovery == nil || src == nil {
		return false
	}
	if *p.ForceRecovery != *src {
		return false
	}
	return true
}

type DescribeRecoverableTimeReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeRecoverableTimeReq() *DescribeRecoverableTimeReq {
	return &DescribeRecoverableTimeReq{}
}

func (p *DescribeRecoverableTimeReq) InitDefault() {
}

func (p *DescribeRecoverableTimeReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeRecoverableTimeReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeRecoverableTimeReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeRecoverableTimeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeReq[fieldId]))
}

func (p *DescribeRecoverableTimeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeRecoverableTimeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableTimeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableTimeReq(%+v)", *p)

}

func (p *DescribeRecoverableTimeReq) DeepEqual(ano *DescribeRecoverableTimeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeRecoverableTimeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeRecoverableTimeResp struct {
	EarliestRecoverableTime string `thrift:"EarliestRecoverableTime,1,required" frugal:"1,required,string" json:"EarliestRecoverableTime"`
}

func NewDescribeRecoverableTimeResp() *DescribeRecoverableTimeResp {
	return &DescribeRecoverableTimeResp{}
}

func (p *DescribeRecoverableTimeResp) InitDefault() {
}

func (p *DescribeRecoverableTimeResp) GetEarliestRecoverableTime() (v string) {
	return p.EarliestRecoverableTime
}
func (p *DescribeRecoverableTimeResp) SetEarliestRecoverableTime(val string) {
	p.EarliestRecoverableTime = val
}

var fieldIDToName_DescribeRecoverableTimeResp = map[int16]string{
	1: "EarliestRecoverableTime",
}

func (p *DescribeRecoverableTimeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEarliestRecoverableTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEarliestRecoverableTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEarliestRecoverableTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeResp[fieldId]))
}

func (p *DescribeRecoverableTimeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EarliestRecoverableTime = _field
	return nil
}

func (p *DescribeRecoverableTimeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableTimeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableTimeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EarliestRecoverableTime", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EarliestRecoverableTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableTimeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableTimeResp(%+v)", *p)

}

func (p *DescribeRecoverableTimeResp) DeepEqual(ano *DescribeRecoverableTimeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EarliestRecoverableTime) {
		return false
	}
	return true
}

func (p *DescribeRecoverableTimeResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EarliestRecoverableTime, src) != 0 {
		return false
	}
	return true
}

type ListRecoveryReq struct {
	Offset       int32         `thrift:"Offset,1,required" frugal:"1,required,i32" json:"Offset"`
	Limit        int32         `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId   string        `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime    string        `thrift:"StartTime,4" frugal:"4,default,string" json:"StartTime"`
	EndTime      string        `thrift:"EndTime,5" frugal:"5,default,string" json:"EndTime"`
	RecoveryType *RecoveryType `thrift:"RecoveryType,6,optional" frugal:"6,optional,RecoveryType" json:"RecoveryType,omitempty"`
}

func NewListRecoveryReq() *ListRecoveryReq {
	return &ListRecoveryReq{}
}

func (p *ListRecoveryReq) InitDefault() {
}

func (p *ListRecoveryReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *ListRecoveryReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListRecoveryReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListRecoveryReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListRecoveryReq) GetEndTime() (v string) {
	return p.EndTime
}

var ListRecoveryReq_RecoveryType_DEFAULT RecoveryType

func (p *ListRecoveryReq) GetRecoveryType() (v RecoveryType) {
	if !p.IsSetRecoveryType() {
		return ListRecoveryReq_RecoveryType_DEFAULT
	}
	return *p.RecoveryType
}
func (p *ListRecoveryReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *ListRecoveryReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListRecoveryReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListRecoveryReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListRecoveryReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *ListRecoveryReq) SetRecoveryType(val *RecoveryType) {
	p.RecoveryType = val
}

var fieldIDToName_ListRecoveryReq = map[int16]string{
	1: "Offset",
	2: "Limit",
	3: "InstanceId",
	4: "StartTime",
	5: "EndTime",
	6: "RecoveryType",
}

func (p *ListRecoveryReq) IsSetRecoveryType() bool {
	return p.RecoveryType != nil
}

func (p *ListRecoveryReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListRecoveryReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOffset bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOffset {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListRecoveryReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListRecoveryReq[fieldId]))
}

func (p *ListRecoveryReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *ListRecoveryReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListRecoveryReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListRecoveryReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListRecoveryReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListRecoveryReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *RecoveryType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RecoveryType(v)
		_field = &tmp
	}
	p.RecoveryType = _field
	return nil
}

func (p *ListRecoveryReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListRecoveryReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListRecoveryReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListRecoveryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Offset", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListRecoveryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListRecoveryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListRecoveryReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListRecoveryReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListRecoveryReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecoveryType() {
		if err = oprot.WriteFieldBegin("RecoveryType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RecoveryType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListRecoveryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListRecoveryReq(%+v)", *p)

}

func (p *ListRecoveryReq) DeepEqual(ano *ListRecoveryReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.RecoveryType) {
		return false
	}
	return true
}

func (p *ListRecoveryReq) Field1DeepEqual(src int32) bool {

	if p.Offset != src {
		return false
	}
	return true
}
func (p *ListRecoveryReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListRecoveryReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListRecoveryReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListRecoveryReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListRecoveryReq) Field6DeepEqual(src *RecoveryType) bool {

	if p.RecoveryType == src {
		return true
	} else if p.RecoveryType == nil || src == nil {
		return false
	}
	if *p.RecoveryType != *src {
		return false
	}
	return true
}

type ListRecoveryResp struct {
	Total int32           `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*RecoveryInfo `thrift:"Datas,2,required" frugal:"2,required,list<RecoveryInfo>" json:"Datas"`
}

func NewListRecoveryResp() *ListRecoveryResp {
	return &ListRecoveryResp{}
}

func (p *ListRecoveryResp) InitDefault() {
}

func (p *ListRecoveryResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListRecoveryResp) GetDatas() (v []*RecoveryInfo) {
	return p.Datas
}
func (p *ListRecoveryResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListRecoveryResp) SetDatas(val []*RecoveryInfo) {
	p.Datas = val
}

var fieldIDToName_ListRecoveryResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListRecoveryResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListRecoveryResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListRecoveryResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListRecoveryResp[fieldId]))
}

func (p *ListRecoveryResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListRecoveryResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RecoveryInfo, 0, size)
	values := make([]RecoveryInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListRecoveryResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListRecoveryResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListRecoveryResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListRecoveryResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListRecoveryResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListRecoveryResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListRecoveryResp(%+v)", *p)

}

func (p *ListRecoveryResp) DeepEqual(ano *ListRecoveryResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListRecoveryResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListRecoveryResp) Field2DeepEqual(src []*RecoveryInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateBinlogBackupReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewCreateBinlogBackupReq() *CreateBinlogBackupReq {
	return &CreateBinlogBackupReq{}
}

func (p *CreateBinlogBackupReq) InitDefault() {
}

func (p *CreateBinlogBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *CreateBinlogBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_CreateBinlogBackupReq = map[int16]string{
	1: "InstanceId",
}

func (p *CreateBinlogBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBinlogBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBinlogBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBinlogBackupReq[fieldId]))
}

func (p *CreateBinlogBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *CreateBinlogBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBinlogBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBinlogBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBinlogBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBinlogBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBinlogBackupReq(%+v)", *p)

}

func (p *CreateBinlogBackupReq) DeepEqual(ano *CreateBinlogBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *CreateBinlogBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type RestoreTableReq struct {
	InstanceId          string                 `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId            string                 `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
	RestoreTime         *string                `thrift:"RestoreTime,3,optional" frugal:"3,optional,string" json:"RestoreTime,omitempty"`
	TableMeta           []*RestoreDatabaseItem `thrift:"TableMeta,4" frugal:"4,default,list<RestoreDatabaseItem>" json:"TableMeta"`
	InstanceName        *string                `thrift:"InstanceName,5,optional" frugal:"5,optional,string" json:"InstanceName,omitempty"`
	InstanceSpec        *InstanceSpec          `thrift:"InstanceSpec,6,optional" frugal:"6,optional,InstanceSpec" json:"InstanceSpec,omitempty"`
	StorageType         *StorageType           `thrift:"StorageType,7,optional" frugal:"7,optional,StorageType" json:"StorageType,omitempty"`
	StorageSpaceGB      *int32                 `thrift:"StorageSpaceGB,8,optional" frugal:"8,optional,i32" json:"StorageSpaceGB,omitempty"`
	NetworkType         *NetworkType           `thrift:"NetworkType,9,optional" frugal:"9,optional,NetworkType" json:"NetworkType,omitempty"`
	ChargeType          *ChargeType            `thrift:"ChargeType,10,optional" frugal:"10,optional,ChargeType" json:"ChargeType,omitempty"`
	VpcID               *string                `thrift:"VpcID,11,optional" frugal:"11,optional,string" json:"VpcID,omitempty"`
	AutoRenew           *bool                  `thrift:"AutoRenew,12,optional" frugal:"12,optional,bool" json:"AutoRenew,omitempty"`
	PrepaidPeriod       *PrepaidPeriod         `thrift:"PrepaidPeriod,13,optional" frugal:"13,optional,PrepaidPeriod" json:"PrepaidPeriod,omitempty"`
	UsedTime            *int32                 `thrift:"UsedTime,14,optional" frugal:"14,optional,i32" json:"UsedTime,omitempty"`
	RequestSource       *RequestSource         `thrift:"RequestSource,15,optional" frugal:"15,optional,RequestSource" json:"RequestSource,omitempty"`
	ParameterTemplateId *string                `thrift:"ParameterTemplateId,16,optional" frugal:"16,optional,string" json:"ParameterTemplateId,omitempty"`
	APIVersion          *string                `thrift:"APIVersion,17,optional" frugal:"17,optional,string" json:"APIVersion,omitempty"`
	SubnetId            *string                `thrift:"SubnetId,18,optional" frugal:"18,optional,string" json:"SubnetId,omitempty"`
	NodeInfo            []*NodeInfoObject      `thrift:"NodeInfo,19,optional" frugal:"19,optional,list<NodeInfoObject>" json:"NodeInfo,omitempty"`
}

func NewRestoreTableReq() *RestoreTableReq {
	return &RestoreTableReq{}
}

func (p *RestoreTableReq) InitDefault() {
}

func (p *RestoreTableReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RestoreTableReq) GetBackupId() (v string) {
	return p.BackupId
}

var RestoreTableReq_RestoreTime_DEFAULT string

func (p *RestoreTableReq) GetRestoreTime() (v string) {
	if !p.IsSetRestoreTime() {
		return RestoreTableReq_RestoreTime_DEFAULT
	}
	return *p.RestoreTime
}

func (p *RestoreTableReq) GetTableMeta() (v []*RestoreDatabaseItem) {
	return p.TableMeta
}

var RestoreTableReq_InstanceName_DEFAULT string

func (p *RestoreTableReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return RestoreTableReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var RestoreTableReq_InstanceSpec_DEFAULT *InstanceSpec

func (p *RestoreTableReq) GetInstanceSpec() (v *InstanceSpec) {
	if !p.IsSetInstanceSpec() {
		return RestoreTableReq_InstanceSpec_DEFAULT
	}
	return p.InstanceSpec
}

var RestoreTableReq_StorageType_DEFAULT StorageType

func (p *RestoreTableReq) GetStorageType() (v StorageType) {
	if !p.IsSetStorageType() {
		return RestoreTableReq_StorageType_DEFAULT
	}
	return *p.StorageType
}

var RestoreTableReq_StorageSpaceGB_DEFAULT int32

func (p *RestoreTableReq) GetStorageSpaceGB() (v int32) {
	if !p.IsSetStorageSpaceGB() {
		return RestoreTableReq_StorageSpaceGB_DEFAULT
	}
	return *p.StorageSpaceGB
}

var RestoreTableReq_NetworkType_DEFAULT NetworkType

func (p *RestoreTableReq) GetNetworkType() (v NetworkType) {
	if !p.IsSetNetworkType() {
		return RestoreTableReq_NetworkType_DEFAULT
	}
	return *p.NetworkType
}

var RestoreTableReq_ChargeType_DEFAULT ChargeType

func (p *RestoreTableReq) GetChargeType() (v ChargeType) {
	if !p.IsSetChargeType() {
		return RestoreTableReq_ChargeType_DEFAULT
	}
	return *p.ChargeType
}

var RestoreTableReq_VpcID_DEFAULT string

func (p *RestoreTableReq) GetVpcID() (v string) {
	if !p.IsSetVpcID() {
		return RestoreTableReq_VpcID_DEFAULT
	}
	return *p.VpcID
}

var RestoreTableReq_AutoRenew_DEFAULT bool

func (p *RestoreTableReq) GetAutoRenew() (v bool) {
	if !p.IsSetAutoRenew() {
		return RestoreTableReq_AutoRenew_DEFAULT
	}
	return *p.AutoRenew
}

var RestoreTableReq_PrepaidPeriod_DEFAULT PrepaidPeriod

func (p *RestoreTableReq) GetPrepaidPeriod() (v PrepaidPeriod) {
	if !p.IsSetPrepaidPeriod() {
		return RestoreTableReq_PrepaidPeriod_DEFAULT
	}
	return *p.PrepaidPeriod
}

var RestoreTableReq_UsedTime_DEFAULT int32

func (p *RestoreTableReq) GetUsedTime() (v int32) {
	if !p.IsSetUsedTime() {
		return RestoreTableReq_UsedTime_DEFAULT
	}
	return *p.UsedTime
}

var RestoreTableReq_RequestSource_DEFAULT RequestSource

func (p *RestoreTableReq) GetRequestSource() (v RequestSource) {
	if !p.IsSetRequestSource() {
		return RestoreTableReq_RequestSource_DEFAULT
	}
	return *p.RequestSource
}

var RestoreTableReq_ParameterTemplateId_DEFAULT string

func (p *RestoreTableReq) GetParameterTemplateId() (v string) {
	if !p.IsSetParameterTemplateId() {
		return RestoreTableReq_ParameterTemplateId_DEFAULT
	}
	return *p.ParameterTemplateId
}

var RestoreTableReq_APIVersion_DEFAULT string

func (p *RestoreTableReq) GetAPIVersion() (v string) {
	if !p.IsSetAPIVersion() {
		return RestoreTableReq_APIVersion_DEFAULT
	}
	return *p.APIVersion
}

var RestoreTableReq_SubnetId_DEFAULT string

func (p *RestoreTableReq) GetSubnetId() (v string) {
	if !p.IsSetSubnetId() {
		return RestoreTableReq_SubnetId_DEFAULT
	}
	return *p.SubnetId
}

var RestoreTableReq_NodeInfo_DEFAULT []*NodeInfoObject

func (p *RestoreTableReq) GetNodeInfo() (v []*NodeInfoObject) {
	if !p.IsSetNodeInfo() {
		return RestoreTableReq_NodeInfo_DEFAULT
	}
	return p.NodeInfo
}
func (p *RestoreTableReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RestoreTableReq) SetBackupId(val string) {
	p.BackupId = val
}
func (p *RestoreTableReq) SetRestoreTime(val *string) {
	p.RestoreTime = val
}
func (p *RestoreTableReq) SetTableMeta(val []*RestoreDatabaseItem) {
	p.TableMeta = val
}
func (p *RestoreTableReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *RestoreTableReq) SetInstanceSpec(val *InstanceSpec) {
	p.InstanceSpec = val
}
func (p *RestoreTableReq) SetStorageType(val *StorageType) {
	p.StorageType = val
}
func (p *RestoreTableReq) SetStorageSpaceGB(val *int32) {
	p.StorageSpaceGB = val
}
func (p *RestoreTableReq) SetNetworkType(val *NetworkType) {
	p.NetworkType = val
}
func (p *RestoreTableReq) SetChargeType(val *ChargeType) {
	p.ChargeType = val
}
func (p *RestoreTableReq) SetVpcID(val *string) {
	p.VpcID = val
}
func (p *RestoreTableReq) SetAutoRenew(val *bool) {
	p.AutoRenew = val
}
func (p *RestoreTableReq) SetPrepaidPeriod(val *PrepaidPeriod) {
	p.PrepaidPeriod = val
}
func (p *RestoreTableReq) SetUsedTime(val *int32) {
	p.UsedTime = val
}
func (p *RestoreTableReq) SetRequestSource(val *RequestSource) {
	p.RequestSource = val
}
func (p *RestoreTableReq) SetParameterTemplateId(val *string) {
	p.ParameterTemplateId = val
}
func (p *RestoreTableReq) SetAPIVersion(val *string) {
	p.APIVersion = val
}
func (p *RestoreTableReq) SetSubnetId(val *string) {
	p.SubnetId = val
}
func (p *RestoreTableReq) SetNodeInfo(val []*NodeInfoObject) {
	p.NodeInfo = val
}

var fieldIDToName_RestoreTableReq = map[int16]string{
	1:  "InstanceId",
	2:  "BackupId",
	3:  "RestoreTime",
	4:  "TableMeta",
	5:  "InstanceName",
	6:  "InstanceSpec",
	7:  "StorageType",
	8:  "StorageSpaceGB",
	9:  "NetworkType",
	10: "ChargeType",
	11: "VpcID",
	12: "AutoRenew",
	13: "PrepaidPeriod",
	14: "UsedTime",
	15: "RequestSource",
	16: "ParameterTemplateId",
	17: "APIVersion",
	18: "SubnetId",
	19: "NodeInfo",
}

func (p *RestoreTableReq) IsSetRestoreTime() bool {
	return p.RestoreTime != nil
}

func (p *RestoreTableReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *RestoreTableReq) IsSetInstanceSpec() bool {
	return p.InstanceSpec != nil
}

func (p *RestoreTableReq) IsSetStorageType() bool {
	return p.StorageType != nil
}

func (p *RestoreTableReq) IsSetStorageSpaceGB() bool {
	return p.StorageSpaceGB != nil
}

func (p *RestoreTableReq) IsSetNetworkType() bool {
	return p.NetworkType != nil
}

func (p *RestoreTableReq) IsSetChargeType() bool {
	return p.ChargeType != nil
}

func (p *RestoreTableReq) IsSetVpcID() bool {
	return p.VpcID != nil
}

func (p *RestoreTableReq) IsSetAutoRenew() bool {
	return p.AutoRenew != nil
}

func (p *RestoreTableReq) IsSetPrepaidPeriod() bool {
	return p.PrepaidPeriod != nil
}

func (p *RestoreTableReq) IsSetUsedTime() bool {
	return p.UsedTime != nil
}

func (p *RestoreTableReq) IsSetRequestSource() bool {
	return p.RequestSource != nil
}

func (p *RestoreTableReq) IsSetParameterTemplateId() bool {
	return p.ParameterTemplateId != nil
}

func (p *RestoreTableReq) IsSetAPIVersion() bool {
	return p.APIVersion != nil
}

func (p *RestoreTableReq) IsSetSubnetId() bool {
	return p.SubnetId != nil
}

func (p *RestoreTableReq) IsSetNodeInfo() bool {
	return p.NodeInfo != nil
}

func (p *RestoreTableReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreTableReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreTableReq[fieldId]))
}

func (p *RestoreTableReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestoreTableReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}
func (p *RestoreTableReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RestoreTableReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RestoreDatabaseItem, 0, size)
	values := make([]RestoreDatabaseItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TableMeta = _field
	return nil
}
func (p *RestoreTableReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *RestoreTableReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewInstanceSpec()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceSpec = _field
	return nil
}
func (p *RestoreTableReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *StorageType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := StorageType(v)
		_field = &tmp
	}
	p.StorageType = _field
	return nil
}
func (p *RestoreTableReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StorageSpaceGB = _field
	return nil
}
func (p *RestoreTableReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *NetworkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := NetworkType(v)
		_field = &tmp
	}
	p.NetworkType = _field
	return nil
}
func (p *RestoreTableReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *ChargeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ChargeType(v)
		_field = &tmp
	}
	p.ChargeType = _field
	return nil
}
func (p *RestoreTableReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VpcID = _field
	return nil
}
func (p *RestoreTableReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoRenew = _field
	return nil
}
func (p *RestoreTableReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *PrepaidPeriod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PrepaidPeriod(v)
		_field = &tmp
	}
	p.PrepaidPeriod = _field
	return nil
}
func (p *RestoreTableReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UsedTime = _field
	return nil
}
func (p *RestoreTableReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *RequestSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RequestSource(v)
		_field = &tmp
	}
	p.RequestSource = _field
	return nil
}
func (p *RestoreTableReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParameterTemplateId = _field
	return nil
}
func (p *RestoreTableReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.APIVersion = _field
	return nil
}
func (p *RestoreTableReq) ReadField18(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubnetId = _field
	return nil
}
func (p *RestoreTableReq) ReadField19(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeInfoObject, 0, size)
	values := make([]NodeInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeInfo = _field
	return nil
}

func (p *RestoreTableReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreTableReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreTableReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreTableReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreTableReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTime() {
		if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RestoreTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestoreTableReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableMeta", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TableMeta)); err != nil {
		return err
	}
	for _, v := range p.TableMeta {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RestoreTableReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RestoreTableReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceSpec() {
		if err = oprot.WriteFieldBegin("InstanceSpec", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InstanceSpec.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RestoreTableReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageType() {
		if err = oprot.WriteFieldBegin("StorageType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.StorageType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RestoreTableReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageSpaceGB() {
		if err = oprot.WriteFieldBegin("StorageSpaceGB", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StorageSpaceGB); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RestoreTableReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNetworkType() {
		if err = oprot.WriteFieldBegin("NetworkType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NetworkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RestoreTableReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeType() {
		if err = oprot.WriteFieldBegin("ChargeType", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ChargeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RestoreTableReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetVpcID() {
		if err = oprot.WriteFieldBegin("VpcID", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VpcID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *RestoreTableReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoRenew() {
		if err = oprot.WriteFieldBegin("AutoRenew", thrift.BOOL, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AutoRenew); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *RestoreTableReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrepaidPeriod() {
		if err = oprot.WriteFieldBegin("PrepaidPeriod", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.PrepaidPeriod)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *RestoreTableReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetUsedTime() {
		if err = oprot.WriteFieldBegin("UsedTime", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.UsedTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *RestoreTableReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestSource() {
		if err = oprot.WriteFieldBegin("RequestSource", thrift.I32, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RequestSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *RestoreTableReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetParameterTemplateId() {
		if err = oprot.WriteFieldBegin("ParameterTemplateId", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParameterTemplateId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *RestoreTableReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetAPIVersion() {
		if err = oprot.WriteFieldBegin("APIVersion", thrift.STRING, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.APIVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *RestoreTableReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubnetId() {
		if err = oprot.WriteFieldBegin("SubnetId", thrift.STRING, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SubnetId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *RestoreTableReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeInfo() {
		if err = oprot.WriteFieldBegin("NodeInfo", thrift.LIST, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeInfo)); err != nil {
			return err
		}
		for _, v := range p.NodeInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *RestoreTableReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreTableReq(%+v)", *p)

}

func (p *RestoreTableReq) DeepEqual(ano *RestoreTableReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.TableMeta) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceSpec) {
		return false
	}
	if !p.Field7DeepEqual(ano.StorageType) {
		return false
	}
	if !p.Field8DeepEqual(ano.StorageSpaceGB) {
		return false
	}
	if !p.Field9DeepEqual(ano.NetworkType) {
		return false
	}
	if !p.Field10DeepEqual(ano.ChargeType) {
		return false
	}
	if !p.Field11DeepEqual(ano.VpcID) {
		return false
	}
	if !p.Field12DeepEqual(ano.AutoRenew) {
		return false
	}
	if !p.Field13DeepEqual(ano.PrepaidPeriod) {
		return false
	}
	if !p.Field14DeepEqual(ano.UsedTime) {
		return false
	}
	if !p.Field15DeepEqual(ano.RequestSource) {
		return false
	}
	if !p.Field16DeepEqual(ano.ParameterTemplateId) {
		return false
	}
	if !p.Field17DeepEqual(ano.APIVersion) {
		return false
	}
	if !p.Field18DeepEqual(ano.SubnetId) {
		return false
	}
	if !p.Field19DeepEqual(ano.NodeInfo) {
		return false
	}
	return true
}

func (p *RestoreTableReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field3DeepEqual(src *string) bool {

	if p.RestoreTime == src {
		return true
	} else if p.RestoreTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RestoreTime, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field4DeepEqual(src []*RestoreDatabaseItem) bool {

	if len(p.TableMeta) != len(src) {
		return false
	}
	for i, v := range p.TableMeta {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreTableReq) Field5DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field6DeepEqual(src *InstanceSpec) bool {

	if !p.InstanceSpec.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field7DeepEqual(src *StorageType) bool {

	if p.StorageType == src {
		return true
	} else if p.StorageType == nil || src == nil {
		return false
	}
	if *p.StorageType != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field8DeepEqual(src *int32) bool {

	if p.StorageSpaceGB == src {
		return true
	} else if p.StorageSpaceGB == nil || src == nil {
		return false
	}
	if *p.StorageSpaceGB != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field9DeepEqual(src *NetworkType) bool {

	if p.NetworkType == src {
		return true
	} else if p.NetworkType == nil || src == nil {
		return false
	}
	if *p.NetworkType != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field10DeepEqual(src *ChargeType) bool {

	if p.ChargeType == src {
		return true
	} else if p.ChargeType == nil || src == nil {
		return false
	}
	if *p.ChargeType != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field11DeepEqual(src *string) bool {

	if p.VpcID == src {
		return true
	} else if p.VpcID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.VpcID, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field12DeepEqual(src *bool) bool {

	if p.AutoRenew == src {
		return true
	} else if p.AutoRenew == nil || src == nil {
		return false
	}
	if *p.AutoRenew != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field13DeepEqual(src *PrepaidPeriod) bool {

	if p.PrepaidPeriod == src {
		return true
	} else if p.PrepaidPeriod == nil || src == nil {
		return false
	}
	if *p.PrepaidPeriod != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field14DeepEqual(src *int32) bool {

	if p.UsedTime == src {
		return true
	} else if p.UsedTime == nil || src == nil {
		return false
	}
	if *p.UsedTime != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field15DeepEqual(src *RequestSource) bool {

	if p.RequestSource == src {
		return true
	} else if p.RequestSource == nil || src == nil {
		return false
	}
	if *p.RequestSource != *src {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field16DeepEqual(src *string) bool {

	if p.ParameterTemplateId == src {
		return true
	} else if p.ParameterTemplateId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParameterTemplateId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field17DeepEqual(src *string) bool {

	if p.APIVersion == src {
		return true
	} else if p.APIVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.APIVersion, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field18DeepEqual(src *string) bool {

	if p.SubnetId == src {
		return true
	} else if p.SubnetId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SubnetId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableReq) Field19DeepEqual(src []*NodeInfoObject) bool {

	if len(p.NodeInfo) != len(src) {
		return false
	}
	for i, v := range p.NodeInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RestoreTableResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	OrderNO    string `thrift:"OrderNO,2,required" frugal:"2,required,string" json:"OrderNO"`
}

func NewRestoreTableResp() *RestoreTableResp {
	return &RestoreTableResp{}
}

func (p *RestoreTableResp) InitDefault() {
}

func (p *RestoreTableResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RestoreTableResp) GetOrderNO() (v string) {
	return p.OrderNO
}
func (p *RestoreTableResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RestoreTableResp) SetOrderNO(val string) {
	p.OrderNO = val
}

var fieldIDToName_RestoreTableResp = map[int16]string{
	1: "InstanceId",
	2: "OrderNO",
}

func (p *RestoreTableResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderNO bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderNO = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderNO {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreTableResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreTableResp[fieldId]))
}

func (p *RestoreTableResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestoreTableResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderNO = _field
	return nil
}

func (p *RestoreTableResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreTableResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreTableResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreTableResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderNO", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderNO); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreTableResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreTableResp(%+v)", *p)

}

func (p *RestoreTableResp) DeepEqual(ano *RestoreTableResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderNO) {
		return false
	}
	return true
}

func (p *RestoreTableResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderNO, src) != 0 {
		return false
	}
	return true
}

type RestoreDatabaseItem struct {
	OriginDBName string              `thrift:"OriginDBName,1,required" frugal:"1,required,string" json:"OriginDBName"`
	NewDBName_   string              `thrift:"NewDBName,2,required" frugal:"2,required,string" json:"NewDBName"`
	Tables       []*RestoreTableItem `thrift:"Tables,3,required" frugal:"3,required,list<RestoreTableItem>" json:"Tables"`
}

func NewRestoreDatabaseItem() *RestoreDatabaseItem {
	return &RestoreDatabaseItem{}
}

func (p *RestoreDatabaseItem) InitDefault() {
}

func (p *RestoreDatabaseItem) GetOriginDBName() (v string) {
	return p.OriginDBName
}

func (p *RestoreDatabaseItem) GetNewDBName_() (v string) {
	return p.NewDBName_
}

func (p *RestoreDatabaseItem) GetTables() (v []*RestoreTableItem) {
	return p.Tables
}
func (p *RestoreDatabaseItem) SetOriginDBName(val string) {
	p.OriginDBName = val
}
func (p *RestoreDatabaseItem) SetNewDBName_(val string) {
	p.NewDBName_ = val
}
func (p *RestoreDatabaseItem) SetTables(val []*RestoreTableItem) {
	p.Tables = val
}

var fieldIDToName_RestoreDatabaseItem = map[int16]string{
	1: "OriginDBName",
	2: "NewDBName",
	3: "Tables",
}

func (p *RestoreDatabaseItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreDatabaseItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOriginDBName bool = false
	var issetNewDBName_ bool = false
	var issetTables bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOriginDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNewDBName_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTables = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOriginDBName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNewDBName_ {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTables {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreDatabaseItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreDatabaseItem[fieldId]))
}

func (p *RestoreDatabaseItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OriginDBName = _field
	return nil
}
func (p *RestoreDatabaseItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NewDBName_ = _field
	return nil
}
func (p *RestoreDatabaseItem) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RestoreTableItem, 0, size)
	values := make([]RestoreTableItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tables = _field
	return nil
}

func (p *RestoreDatabaseItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreDatabaseItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreDatabaseItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreDatabaseItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OriginDBName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OriginDBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreDatabaseItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NewDBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NewDBName_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreDatabaseItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tables", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tables)); err != nil {
		return err
	}
	for _, v := range p.Tables {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestoreDatabaseItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreDatabaseItem(%+v)", *p)

}

func (p *RestoreDatabaseItem) DeepEqual(ano *RestoreDatabaseItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.OriginDBName) {
		return false
	}
	if !p.Field2DeepEqual(ano.NewDBName_) {
		return false
	}
	if !p.Field3DeepEqual(ano.Tables) {
		return false
	}
	return true
}

func (p *RestoreDatabaseItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.OriginDBName, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreDatabaseItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NewDBName_, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreDatabaseItem) Field3DeepEqual(src []*RestoreTableItem) bool {

	if len(p.Tables) != len(src) {
		return false
	}
	for i, v := range p.Tables {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RestoreTableItem struct {
	Name     string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	NewName_ string `thrift:"NewName,2,required" frugal:"2,required,string" json:"NewName"`
}

func NewRestoreTableItem() *RestoreTableItem {
	return &RestoreTableItem{}
}

func (p *RestoreTableItem) InitDefault() {
}

func (p *RestoreTableItem) GetName() (v string) {
	return p.Name
}

func (p *RestoreTableItem) GetNewName_() (v string) {
	return p.NewName_
}
func (p *RestoreTableItem) SetName(val string) {
	p.Name = val
}
func (p *RestoreTableItem) SetNewName_(val string) {
	p.NewName_ = val
}

var fieldIDToName_RestoreTableItem = map[int16]string{
	1: "Name",
	2: "NewName",
}

func (p *RestoreTableItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetNewName_ bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNewName_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNewName_ {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreTableItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreTableItem[fieldId]))
}

func (p *RestoreTableItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *RestoreTableItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NewName_ = _field
	return nil
}

func (p *RestoreTableItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreTableItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreTableItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreTableItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreTableItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NewName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NewName_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreTableItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreTableItem(%+v)", *p)

}

func (p *RestoreTableItem) DeepEqual(ano *RestoreTableItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.NewName_) {
		return false
	}
	return true
}

func (p *RestoreTableItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreTableItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NewName_, src) != 0 {
		return false
	}
	return true
}

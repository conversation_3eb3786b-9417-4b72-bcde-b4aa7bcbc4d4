// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDatabaseReq) IsValid() error {
	return nil
}
func (p *DeleteDatabaseReq) IsValid() error {
	return nil
}
func (p *ListDatabasesReq) IsValid() error {
	return nil
}
func (p *ListDatabasesResp) IsValid() error {
	return nil
}
func (p *ListTablesReq) IsValid() error {
	return nil
}
func (p *ListTablesResp) IsValid() error {
	return nil
}
func (p *ListHostDatabasesReq) IsValid() error {
	return nil
}
func (p *ListHostDatabasesResp) IsValid() error {
	return nil
}
func (p *ListHostTablesReq) IsValid() error {
	return nil
}
func (p *ListHostTablesResp) IsValid() error {
	return nil
}
func (p *CreateUserDataImportTaskReq) IsValid() error {
	return nil
}
func (p *CreateUserDataImportTaskResp) IsValid() error {
	return nil
}
func (p *ListUserDataImportTasksReq) IsValid() error {
	return nil
}
func (p *ListUserDataImportTasksResp) IsValid() error {
	return nil
}

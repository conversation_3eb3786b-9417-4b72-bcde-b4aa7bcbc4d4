// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateBackupReq) IsValid() error {
	return nil
}
func (p *CreateBackupResp) IsValid() error {
	return nil
}
func (p *ApplyDownloadBackupUrlReq) IsValid() error {
	return nil
}
func (p *ApplyDownloadBackupUrlResp) IsValid() error {
	return nil
}
func (p *ListBackupsReq) IsValid() error {
	return nil
}
func (p *ListBackupsResp) IsValid() error {
	return nil
}
func (p *DescribeBackupConfigReq) IsValid() error {
	return nil
}
func (p *DescribeBackupConfigResp) IsValid() error {
	if p.BinlogPolicy != nil {
		if err := p.BinlogPolicy.IsValid(); err != nil {
			return fmt.Errorf("field BinlogPolicy not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyBackupConfigReq) IsValid() error {
	if p.BinlogPolicy != nil {
		if err := p.BinlogPolicy.IsValid(); err != nil {
			return fmt.Errorf("field BinlogPolicy not valid, %w", err)
		}
	}
	return nil
}
func (p *RecoveryDBInstanceReq) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *RecoveryDBInstanceResp) IsValid() error {
	return nil
}
func (p *RecoveryInstanceNodeReq) IsValid() error {
	return nil
}
func (p *DescribeRecoverableTimeReq) IsValid() error {
	return nil
}
func (p *DescribeRecoverableTimeResp) IsValid() error {
	return nil
}
func (p *ListRecoveryReq) IsValid() error {
	return nil
}
func (p *ListRecoveryResp) IsValid() error {
	return nil
}
func (p *CreateBinlogBackupReq) IsValid() error {
	return nil
}
func (p *RestoreTableReq) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *RestoreTableResp) IsValid() error {
	return nil
}
func (p *RestoreDatabaseItem) IsValid() error {
	return nil
}
func (p *RestoreTableItem) IsValid() error {
	return nil
}

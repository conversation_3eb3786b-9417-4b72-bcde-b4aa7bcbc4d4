// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *InstanceSpec) IsValid() error {
	return nil
}
func (p *ConnectionInfo) IsValid() error {
	return nil
}
func (p *InstanceInfo) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	return nil
}
func (p *DBPrivilege) IsValid() error {
	return nil
}
func (p *AccountInfo) IsValid() error {
	return nil
}
func (p *DBInfo) IsValid() error {
	return nil
}
func (p *DataItem) IsValid() error {
	return nil
}
func (p *WhiteIPGroup) IsValid() error {
	return nil
}
func (p *BackupInfo) IsValid() error {
	return nil
}
func (p *MigrationInfo) IsValid() error {
	return nil
}
func (p *EventInfo) IsValid() error {
	return nil
}
func (p *HAChangeLog) IsValid() error {
	return nil
}
func (p *DBLog) IsValid() error {
	return nil
}
func (p *InstanceNodeInfo) IsValid() error {
	return nil
}
func (p *RecoveryInfo) IsValid() error {
	return nil
}
func (p *InstanceParam) IsValid() error {
	return nil
}
func (p *InstanceParamChg) IsValid() error {
	return nil
}
func (p *MgrParam) IsValid() error {
	return nil
}
func (p *MgrInstance) IsValid() error {
	return nil
}
func (p *KubePod) IsValid() error {
	return nil
}
func (p *KubeContainer) IsValid() error {
	return nil
}
func (p *UserDataImportTask) IsValid() error {
	return nil
}
func (p *DeployRuleInfo) IsValid() error {
	return nil
}
func (p *InstanceParamsHistoryInfo) IsValid() error {
	return nil
}
func (p *InstanceTosConfig) IsValid() error {
	return nil
}
func (p *InstanceTlsConfig) IsValid() error {
	return nil
}
func (p *ParameterTemplateInfo) IsValid() error {
	return nil
}
func (p *PerformanceData) IsValid() error {
	return nil
}
func (p *PerformanceValue) IsValid() error {
	return nil
}
func (p *VersionInfo) IsValid() error {
	return nil
}
func (p *DescribeStatisticsResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceDistributionInfo) IsValid() error {
	return nil
}
func (p *FreeLockCorrectOrder) IsValid() error {
	return nil
}
func (p *ChargeItemPrice) IsValid() error {
	return nil
}
func (p *SlowLogRecord) IsValid() error {
	return nil
}
func (p *AuditLogRecord) IsValid() error {
	return nil
}
func (p *ErrorLogRecord) IsValid() error {
	return nil
}
func (p *OperationCommand) IsValid() error {
	return nil
}
func (p *QueryFilterByName) IsValid() error {
	return nil
}
func (p *Dimension) IsValid() error {
	return nil
}
func (p *DimensionValue) IsValid() error {
	return nil
}
func (p *SinglePLBInfo) IsValid() error {
	return nil
}
func (p *BinlogRetentionPolicy) IsValid() error {
	return nil
}
func (p *RouteNode) IsValid() error {
	return nil
}
func (p *RouteAddress) IsValid() error {
	return nil
}
func (p *NodeInfoObject) IsValid() error {
	return nil
}
func (p *InstanceDbwConfig) IsValid() error {
	return nil
}
func (p *ZoneAddressInfo) IsValid() error {
	return nil
}
func (p *TagObject) IsValid() error {
	return nil
}
func (p *InstanceInspectResult_) IsValid() error {
	return nil
}
func (p *InspectItemResult_) IsValid() error {
	return nil
}
func (p *InspectItem) IsValid() error {
	return nil
}
func (p *InspectOption) IsValid() error {
	if p.InspectScene != nil {
		if p.InspectScene.String() == "<UNSET>" {
			return fmt.Errorf("field InspectScene defined_only rule failed")
		}
	}
	return nil
}
func (p *InspectResultSummary) IsValid() error {
	return nil
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type AllocateDBInstancePublicConnectionReq struct {
	InstanceId       string     `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	ISP              *PublicISP `thrift:"ISP,2,optional" frugal:"2,optional,PublicISP" json:"ISP,omitempty"`
	ConnectionPrefix *string    `thrift:"ConnectionPrefix,3,optional" frugal:"3,optional,string" json:"ConnectionPrefix,omitempty"`
}

func NewAllocateDBInstancePublicConnectionReq() *AllocateDBInstancePublicConnectionReq {
	return &AllocateDBInstancePublicConnectionReq{}
}

func (p *AllocateDBInstancePublicConnectionReq) InitDefault() {
}

func (p *AllocateDBInstancePublicConnectionReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var AllocateDBInstancePublicConnectionReq_ISP_DEFAULT PublicISP

func (p *AllocateDBInstancePublicConnectionReq) GetISP() (v PublicISP) {
	if !p.IsSetISP() {
		return AllocateDBInstancePublicConnectionReq_ISP_DEFAULT
	}
	return *p.ISP
}

var AllocateDBInstancePublicConnectionReq_ConnectionPrefix_DEFAULT string

func (p *AllocateDBInstancePublicConnectionReq) GetConnectionPrefix() (v string) {
	if !p.IsSetConnectionPrefix() {
		return AllocateDBInstancePublicConnectionReq_ConnectionPrefix_DEFAULT
	}
	return *p.ConnectionPrefix
}
func (p *AllocateDBInstancePublicConnectionReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AllocateDBInstancePublicConnectionReq) SetISP(val *PublicISP) {
	p.ISP = val
}
func (p *AllocateDBInstancePublicConnectionReq) SetConnectionPrefix(val *string) {
	p.ConnectionPrefix = val
}

var fieldIDToName_AllocateDBInstancePublicConnectionReq = map[int16]string{
	1: "InstanceId",
	2: "ISP",
	3: "ConnectionPrefix",
}

func (p *AllocateDBInstancePublicConnectionReq) IsSetISP() bool {
	return p.ISP != nil
}

func (p *AllocateDBInstancePublicConnectionReq) IsSetConnectionPrefix() bool {
	return p.ConnectionPrefix != nil
}

func (p *AllocateDBInstancePublicConnectionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateDBInstancePublicConnectionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateDBInstancePublicConnectionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateDBInstancePublicConnectionReq[fieldId]))
}

func (p *AllocateDBInstancePublicConnectionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AllocateDBInstancePublicConnectionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *PublicISP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PublicISP(v)
		_field = &tmp
	}
	p.ISP = _field
	return nil
}
func (p *AllocateDBInstancePublicConnectionReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConnectionPrefix = _field
	return nil
}

func (p *AllocateDBInstancePublicConnectionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateDBInstancePublicConnectionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateDBInstancePublicConnectionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetISP() {
		if err = oprot.WriteFieldBegin("ISP", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ISP)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectionPrefix() {
		if err = oprot.WriteFieldBegin("ConnectionPrefix", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ConnectionPrefix); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateDBInstancePublicConnectionReq(%+v)", *p)

}

func (p *AllocateDBInstancePublicConnectionReq) DeepEqual(ano *AllocateDBInstancePublicConnectionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ISP) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConnectionPrefix) {
		return false
	}
	return true
}

func (p *AllocateDBInstancePublicConnectionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AllocateDBInstancePublicConnectionReq) Field2DeepEqual(src *PublicISP) bool {

	if p.ISP == src {
		return true
	} else if p.ISP == nil || src == nil {
		return false
	}
	if *p.ISP != *src {
		return false
	}
	return true
}
func (p *AllocateDBInstancePublicConnectionReq) Field3DeepEqual(src *string) bool {

	if p.ConnectionPrefix == src {
		return true
	} else if p.ConnectionPrefix == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ConnectionPrefix, *src) != 0 {
		return false
	}
	return true
}

type AllocateDBInstancePublicConnectionResp struct {
	Domain string `thrift:"Domain,1,required" frugal:"1,required,string" json:"Domain"`
	Port   string `thrift:"Port,2,required" frugal:"2,required,string" json:"Port"`
}

func NewAllocateDBInstancePublicConnectionResp() *AllocateDBInstancePublicConnectionResp {
	return &AllocateDBInstancePublicConnectionResp{}
}

func (p *AllocateDBInstancePublicConnectionResp) InitDefault() {
}

func (p *AllocateDBInstancePublicConnectionResp) GetDomain() (v string) {
	return p.Domain
}

func (p *AllocateDBInstancePublicConnectionResp) GetPort() (v string) {
	return p.Port
}
func (p *AllocateDBInstancePublicConnectionResp) SetDomain(val string) {
	p.Domain = val
}
func (p *AllocateDBInstancePublicConnectionResp) SetPort(val string) {
	p.Port = val
}

var fieldIDToName_AllocateDBInstancePublicConnectionResp = map[int16]string{
	1: "Domain",
	2: "Port",
}

func (p *AllocateDBInstancePublicConnectionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateDBInstancePublicConnectionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDomain bool = false
	var issetPort bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDomain = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPort = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDomain {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPort {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateDBInstancePublicConnectionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateDBInstancePublicConnectionResp[fieldId]))
}

func (p *AllocateDBInstancePublicConnectionResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Domain = _field
	return nil
}
func (p *AllocateDBInstancePublicConnectionResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Port = _field
	return nil
}

func (p *AllocateDBInstancePublicConnectionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateDBInstancePublicConnectionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateDBInstancePublicConnectionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Domain", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Domain); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Port", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Port); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllocateDBInstancePublicConnectionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateDBInstancePublicConnectionResp(%+v)", *p)

}

func (p *AllocateDBInstancePublicConnectionResp) DeepEqual(ano *AllocateDBInstancePublicConnectionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Domain) {
		return false
	}
	if !p.Field2DeepEqual(ano.Port) {
		return false
	}
	return true
}

func (p *AllocateDBInstancePublicConnectionResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Domain, src) != 0 {
		return false
	}
	return true
}
func (p *AllocateDBInstancePublicConnectionResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Port, src) != 0 {
		return false
	}
	return true
}

type ReleaseDBInstancePublicConnectionReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewReleaseDBInstancePublicConnectionReq() *ReleaseDBInstancePublicConnectionReq {
	return &ReleaseDBInstancePublicConnectionReq{}
}

func (p *ReleaseDBInstancePublicConnectionReq) InitDefault() {
}

func (p *ReleaseDBInstancePublicConnectionReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ReleaseDBInstancePublicConnectionReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ReleaseDBInstancePublicConnectionReq = map[int16]string{
	1: "InstanceId",
}

func (p *ReleaseDBInstancePublicConnectionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReleaseDBInstancePublicConnectionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReleaseDBInstancePublicConnectionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReleaseDBInstancePublicConnectionReq[fieldId]))
}

func (p *ReleaseDBInstancePublicConnectionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ReleaseDBInstancePublicConnectionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReleaseDBInstancePublicConnectionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseDBInstancePublicConnectionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReleaseDBInstancePublicConnectionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReleaseDBInstancePublicConnectionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReleaseDBInstancePublicConnectionReq(%+v)", *p)

}

func (p *ReleaseDBInstancePublicConnectionReq) DeepEqual(ano *ReleaseDBInstancePublicConnectionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ReleaseDBInstancePublicConnectionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceConnectionReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeDBInstanceConnectionReq() *DescribeDBInstanceConnectionReq {
	return &DescribeDBInstanceConnectionReq{}
}

func (p *DescribeDBInstanceConnectionReq) InitDefault() {
}

func (p *DescribeDBInstanceConnectionReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBInstanceConnectionReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBInstanceConnectionReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBInstanceConnectionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceConnectionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceConnectionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceConnectionReq[fieldId]))
}

func (p *DescribeDBInstanceConnectionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBInstanceConnectionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceConnectionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceConnectionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceConnectionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceConnectionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceConnectionReq(%+v)", *p)

}

func (p *DescribeDBInstanceConnectionReq) DeepEqual(ano *DescribeDBInstanceConnectionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceConnectionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceConnectionResp struct {
	ConnectionInfo *ConnectionInfo `thrift:"ConnectionInfo,1,required" frugal:"1,required,ConnectionInfo" json:"ConnectionInfo"`
}

func NewDescribeDBInstanceConnectionResp() *DescribeDBInstanceConnectionResp {
	return &DescribeDBInstanceConnectionResp{}
}

func (p *DescribeDBInstanceConnectionResp) InitDefault() {
}

var DescribeDBInstanceConnectionResp_ConnectionInfo_DEFAULT *ConnectionInfo

func (p *DescribeDBInstanceConnectionResp) GetConnectionInfo() (v *ConnectionInfo) {
	if !p.IsSetConnectionInfo() {
		return DescribeDBInstanceConnectionResp_ConnectionInfo_DEFAULT
	}
	return p.ConnectionInfo
}
func (p *DescribeDBInstanceConnectionResp) SetConnectionInfo(val *ConnectionInfo) {
	p.ConnectionInfo = val
}

var fieldIDToName_DescribeDBInstanceConnectionResp = map[int16]string{
	1: "ConnectionInfo",
}

func (p *DescribeDBInstanceConnectionResp) IsSetConnectionInfo() bool {
	return p.ConnectionInfo != nil
}

func (p *DescribeDBInstanceConnectionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceConnectionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConnectionInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectionInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConnectionInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceConnectionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceConnectionResp[fieldId]))
}

func (p *DescribeDBInstanceConnectionResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewConnectionInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ConnectionInfo = _field
	return nil
}

func (p *DescribeDBInstanceConnectionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceConnectionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceConnectionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceConnectionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnectionInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ConnectionInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceConnectionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceConnectionResp(%+v)", *p)

}

func (p *DescribeDBInstanceConnectionResp) DeepEqual(ano *DescribeDBInstanceConnectionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ConnectionInfo) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceConnectionResp) Field1DeepEqual(src *ConnectionInfo) bool {

	if !p.ConnectionInfo.DeepEqual(src) {
		return false
	}
	return true
}

type AllocateReadWriteSplittingReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EnableSwitch EnableSwitch `thrift:"EnableSwitch,2,required" frugal:"2,required,EnableSwitch" json:"EnableSwitch"`
}

func NewAllocateReadWriteSplittingReq() *AllocateReadWriteSplittingReq {
	return &AllocateReadWriteSplittingReq{}
}

func (p *AllocateReadWriteSplittingReq) InitDefault() {
}

func (p *AllocateReadWriteSplittingReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AllocateReadWriteSplittingReq) GetEnableSwitch() (v EnableSwitch) {
	return p.EnableSwitch
}
func (p *AllocateReadWriteSplittingReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AllocateReadWriteSplittingReq) SetEnableSwitch(val EnableSwitch) {
	p.EnableSwitch = val
}

var fieldIDToName_AllocateReadWriteSplittingReq = map[int16]string{
	1: "InstanceId",
	2: "EnableSwitch",
}

func (p *AllocateReadWriteSplittingReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateReadWriteSplittingReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEnableSwitch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableSwitch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnableSwitch {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateReadWriteSplittingReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateReadWriteSplittingReq[fieldId]))
}

func (p *AllocateReadWriteSplittingReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AllocateReadWriteSplittingReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableSwitch = _field
	return nil
}

func (p *AllocateReadWriteSplittingReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateReadWriteSplittingReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateReadWriteSplittingReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateReadWriteSplittingReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateReadWriteSplittingReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableSwitch", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableSwitch)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllocateReadWriteSplittingReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateReadWriteSplittingReq(%+v)", *p)

}

func (p *AllocateReadWriteSplittingReq) DeepEqual(ano *AllocateReadWriteSplittingReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableSwitch) {
		return false
	}
	return true
}

func (p *AllocateReadWriteSplittingReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AllocateReadWriteSplittingReq) Field2DeepEqual(src EnableSwitch) bool {

	if p.EnableSwitch != src {
		return false
	}
	return true
}

type ModifyReadOnlyConfigReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EnableSwitch EnableSwitch `thrift:"EnableSwitch,2,required" frugal:"2,required,EnableSwitch" json:"EnableSwitch"`
}

func NewModifyReadOnlyConfigReq() *ModifyReadOnlyConfigReq {
	return &ModifyReadOnlyConfigReq{}
}

func (p *ModifyReadOnlyConfigReq) InitDefault() {
}

func (p *ModifyReadOnlyConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyReadOnlyConfigReq) GetEnableSwitch() (v EnableSwitch) {
	return p.EnableSwitch
}
func (p *ModifyReadOnlyConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyReadOnlyConfigReq) SetEnableSwitch(val EnableSwitch) {
	p.EnableSwitch = val
}

var fieldIDToName_ModifyReadOnlyConfigReq = map[int16]string{
	1: "InstanceId",
	2: "EnableSwitch",
}

func (p *ModifyReadOnlyConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyReadOnlyConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEnableSwitch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableSwitch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnableSwitch {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyReadOnlyConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyReadOnlyConfigReq[fieldId]))
}

func (p *ModifyReadOnlyConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyReadOnlyConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableSwitch = _field
	return nil
}

func (p *ModifyReadOnlyConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyReadOnlyConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyReadOnlyConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyReadOnlyConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyReadOnlyConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableSwitch", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableSwitch)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyReadOnlyConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyReadOnlyConfigReq(%+v)", *p)

}

func (p *ModifyReadOnlyConfigReq) DeepEqual(ano *ModifyReadOnlyConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableSwitch) {
		return false
	}
	return true
}

func (p *ModifyReadOnlyConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyReadOnlyConfigReq) Field2DeepEqual(src EnableSwitch) bool {

	if p.EnableSwitch != src {
		return false
	}
	return true
}

type ModifyDBProxyConfigReq struct {
	InstanceId          string    `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	MaxFrontConnections *int64    `thrift:"MaxFrontConnections,2,optional" frugal:"2,optional,i64" json:"MaxFrontConnections,omitempty"`
	MaxQPS              *int64    `thrift:"MaxQPS,3,optional" frugal:"3,optional,i64" json:"MaxQPS,omitempty"`
	MaxConnections      *int64    `thrift:"MaxConnections,4,optional" frugal:"4,optional,i64" json:"MaxConnections,omitempty"`
	MaxIdleConnections  *int64    `thrift:"MaxIdleConnections,5,optional" frugal:"5,optional,i64" json:"MaxIdleConnections,omitempty"`
	MinIdleConnections  *int64    `thrift:"MinIdleConnections,6,optional" frugal:"6,optional,i64" json:"MinIdleConnections,omitempty"`
	DialTimeout         *string   `thrift:"DialTimeout,7,optional" frugal:"7,optional,string" json:"DialTimeout,omitempty"`
	ReadTimeout         *string   `thrift:"ReadTimeout,8,optional" frugal:"8,optional,string" json:"ReadTimeout,omitempty"`
	WriteTimeout        *string   `thrift:"WriteTimeout,9,optional" frugal:"9,optional,string" json:"WriteTimeout,omitempty"`
	PingIntervalSeconds *int64    `thrift:"PingIntervalSeconds,10,optional" frugal:"10,optional,i64" json:"PingIntervalSeconds,omitempty"`
	ReadOnly            *bool     `thrift:"ReadOnly,11,optional" frugal:"11,optional,bool" json:"ReadOnly,omitempty"`
	SQLRules            []string  `thrift:"SQLRules,12,optional" frugal:"12,optional,list<string>" json:"SQLRules,omitempty"`
	ConnectionPoolType  *PoolType `thrift:"ConnectionPoolType,13,optional" frugal:"13,optional,PoolType" json:"ConnectionPoolType,omitempty"`
}

func NewModifyDBProxyConfigReq() *ModifyDBProxyConfigReq {
	return &ModifyDBProxyConfigReq{}
}

func (p *ModifyDBProxyConfigReq) InitDefault() {
}

func (p *ModifyDBProxyConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyDBProxyConfigReq_MaxFrontConnections_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetMaxFrontConnections() (v int64) {
	if !p.IsSetMaxFrontConnections() {
		return ModifyDBProxyConfigReq_MaxFrontConnections_DEFAULT
	}
	return *p.MaxFrontConnections
}

var ModifyDBProxyConfigReq_MaxQPS_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetMaxQPS() (v int64) {
	if !p.IsSetMaxQPS() {
		return ModifyDBProxyConfigReq_MaxQPS_DEFAULT
	}
	return *p.MaxQPS
}

var ModifyDBProxyConfigReq_MaxConnections_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetMaxConnections() (v int64) {
	if !p.IsSetMaxConnections() {
		return ModifyDBProxyConfigReq_MaxConnections_DEFAULT
	}
	return *p.MaxConnections
}

var ModifyDBProxyConfigReq_MaxIdleConnections_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetMaxIdleConnections() (v int64) {
	if !p.IsSetMaxIdleConnections() {
		return ModifyDBProxyConfigReq_MaxIdleConnections_DEFAULT
	}
	return *p.MaxIdleConnections
}

var ModifyDBProxyConfigReq_MinIdleConnections_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetMinIdleConnections() (v int64) {
	if !p.IsSetMinIdleConnections() {
		return ModifyDBProxyConfigReq_MinIdleConnections_DEFAULT
	}
	return *p.MinIdleConnections
}

var ModifyDBProxyConfigReq_DialTimeout_DEFAULT string

func (p *ModifyDBProxyConfigReq) GetDialTimeout() (v string) {
	if !p.IsSetDialTimeout() {
		return ModifyDBProxyConfigReq_DialTimeout_DEFAULT
	}
	return *p.DialTimeout
}

var ModifyDBProxyConfigReq_ReadTimeout_DEFAULT string

func (p *ModifyDBProxyConfigReq) GetReadTimeout() (v string) {
	if !p.IsSetReadTimeout() {
		return ModifyDBProxyConfigReq_ReadTimeout_DEFAULT
	}
	return *p.ReadTimeout
}

var ModifyDBProxyConfigReq_WriteTimeout_DEFAULT string

func (p *ModifyDBProxyConfigReq) GetWriteTimeout() (v string) {
	if !p.IsSetWriteTimeout() {
		return ModifyDBProxyConfigReq_WriteTimeout_DEFAULT
	}
	return *p.WriteTimeout
}

var ModifyDBProxyConfigReq_PingIntervalSeconds_DEFAULT int64

func (p *ModifyDBProxyConfigReq) GetPingIntervalSeconds() (v int64) {
	if !p.IsSetPingIntervalSeconds() {
		return ModifyDBProxyConfigReq_PingIntervalSeconds_DEFAULT
	}
	return *p.PingIntervalSeconds
}

var ModifyDBProxyConfigReq_ReadOnly_DEFAULT bool

func (p *ModifyDBProxyConfigReq) GetReadOnly() (v bool) {
	if !p.IsSetReadOnly() {
		return ModifyDBProxyConfigReq_ReadOnly_DEFAULT
	}
	return *p.ReadOnly
}

var ModifyDBProxyConfigReq_SQLRules_DEFAULT []string

func (p *ModifyDBProxyConfigReq) GetSQLRules() (v []string) {
	if !p.IsSetSQLRules() {
		return ModifyDBProxyConfigReq_SQLRules_DEFAULT
	}
	return p.SQLRules
}

var ModifyDBProxyConfigReq_ConnectionPoolType_DEFAULT PoolType

func (p *ModifyDBProxyConfigReq) GetConnectionPoolType() (v PoolType) {
	if !p.IsSetConnectionPoolType() {
		return ModifyDBProxyConfigReq_ConnectionPoolType_DEFAULT
	}
	return *p.ConnectionPoolType
}
func (p *ModifyDBProxyConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBProxyConfigReq) SetMaxFrontConnections(val *int64) {
	p.MaxFrontConnections = val
}
func (p *ModifyDBProxyConfigReq) SetMaxQPS(val *int64) {
	p.MaxQPS = val
}
func (p *ModifyDBProxyConfigReq) SetMaxConnections(val *int64) {
	p.MaxConnections = val
}
func (p *ModifyDBProxyConfigReq) SetMaxIdleConnections(val *int64) {
	p.MaxIdleConnections = val
}
func (p *ModifyDBProxyConfigReq) SetMinIdleConnections(val *int64) {
	p.MinIdleConnections = val
}
func (p *ModifyDBProxyConfigReq) SetDialTimeout(val *string) {
	p.DialTimeout = val
}
func (p *ModifyDBProxyConfigReq) SetReadTimeout(val *string) {
	p.ReadTimeout = val
}
func (p *ModifyDBProxyConfigReq) SetWriteTimeout(val *string) {
	p.WriteTimeout = val
}
func (p *ModifyDBProxyConfigReq) SetPingIntervalSeconds(val *int64) {
	p.PingIntervalSeconds = val
}
func (p *ModifyDBProxyConfigReq) SetReadOnly(val *bool) {
	p.ReadOnly = val
}
func (p *ModifyDBProxyConfigReq) SetSQLRules(val []string) {
	p.SQLRules = val
}
func (p *ModifyDBProxyConfigReq) SetConnectionPoolType(val *PoolType) {
	p.ConnectionPoolType = val
}

var fieldIDToName_ModifyDBProxyConfigReq = map[int16]string{
	1:  "InstanceId",
	2:  "MaxFrontConnections",
	3:  "MaxQPS",
	4:  "MaxConnections",
	5:  "MaxIdleConnections",
	6:  "MinIdleConnections",
	7:  "DialTimeout",
	8:  "ReadTimeout",
	9:  "WriteTimeout",
	10: "PingIntervalSeconds",
	11: "ReadOnly",
	12: "SQLRules",
	13: "ConnectionPoolType",
}

func (p *ModifyDBProxyConfigReq) IsSetMaxFrontConnections() bool {
	return p.MaxFrontConnections != nil
}

func (p *ModifyDBProxyConfigReq) IsSetMaxQPS() bool {
	return p.MaxQPS != nil
}

func (p *ModifyDBProxyConfigReq) IsSetMaxConnections() bool {
	return p.MaxConnections != nil
}

func (p *ModifyDBProxyConfigReq) IsSetMaxIdleConnections() bool {
	return p.MaxIdleConnections != nil
}

func (p *ModifyDBProxyConfigReq) IsSetMinIdleConnections() bool {
	return p.MinIdleConnections != nil
}

func (p *ModifyDBProxyConfigReq) IsSetDialTimeout() bool {
	return p.DialTimeout != nil
}

func (p *ModifyDBProxyConfigReq) IsSetReadTimeout() bool {
	return p.ReadTimeout != nil
}

func (p *ModifyDBProxyConfigReq) IsSetWriteTimeout() bool {
	return p.WriteTimeout != nil
}

func (p *ModifyDBProxyConfigReq) IsSetPingIntervalSeconds() bool {
	return p.PingIntervalSeconds != nil
}

func (p *ModifyDBProxyConfigReq) IsSetReadOnly() bool {
	return p.ReadOnly != nil
}

func (p *ModifyDBProxyConfigReq) IsSetSQLRules() bool {
	return p.SQLRules != nil
}

func (p *ModifyDBProxyConfigReq) IsSetConnectionPoolType() bool {
	return p.ConnectionPoolType != nil
}

func (p *ModifyDBProxyConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBProxyConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBProxyConfigReq[fieldId]))
}

func (p *ModifyDBProxyConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxFrontConnections = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxQPS = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxConnections = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxIdleConnections = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MinIdleConnections = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DialTimeout = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReadTimeout = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WriteTimeout = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PingIntervalSeconds = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReadOnly = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLRules = _field
	return nil
}
func (p *ModifyDBProxyConfigReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *PoolType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PoolType(v)
		_field = &tmp
	}
	p.ConnectionPoolType = _field
	return nil
}

func (p *ModifyDBProxyConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBProxyConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxFrontConnections() {
		if err = oprot.WriteFieldBegin("MaxFrontConnections", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxFrontConnections); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxQPS() {
		if err = oprot.WriteFieldBegin("MaxQPS", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxQPS); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxConnections() {
		if err = oprot.WriteFieldBegin("MaxConnections", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxConnections); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxIdleConnections() {
		if err = oprot.WriteFieldBegin("MaxIdleConnections", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxIdleConnections); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetMinIdleConnections() {
		if err = oprot.WriteFieldBegin("MinIdleConnections", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MinIdleConnections); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDialTimeout() {
		if err = oprot.WriteFieldBegin("DialTimeout", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DialTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadTimeout() {
		if err = oprot.WriteFieldBegin("ReadTimeout", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReadTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetWriteTimeout() {
		if err = oprot.WriteFieldBegin("WriteTimeout", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.WriteTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetPingIntervalSeconds() {
		if err = oprot.WriteFieldBegin("PingIntervalSeconds", thrift.I64, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PingIntervalSeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadOnly() {
		if err = oprot.WriteFieldBegin("ReadOnly", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ReadOnly); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetSQLRules() {
		if err = oprot.WriteFieldBegin("SQLRules", thrift.LIST, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SQLRules)); err != nil {
			return err
		}
		for _, v := range p.SQLRules {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectionPoolType() {
		if err = oprot.WriteFieldBegin("ConnectionPoolType", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ConnectionPoolType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ModifyDBProxyConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBProxyConfigReq(%+v)", *p)

}

func (p *ModifyDBProxyConfigReq) DeepEqual(ano *ModifyDBProxyConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.MaxFrontConnections) {
		return false
	}
	if !p.Field3DeepEqual(ano.MaxQPS) {
		return false
	}
	if !p.Field4DeepEqual(ano.MaxConnections) {
		return false
	}
	if !p.Field5DeepEqual(ano.MaxIdleConnections) {
		return false
	}
	if !p.Field6DeepEqual(ano.MinIdleConnections) {
		return false
	}
	if !p.Field7DeepEqual(ano.DialTimeout) {
		return false
	}
	if !p.Field8DeepEqual(ano.ReadTimeout) {
		return false
	}
	if !p.Field9DeepEqual(ano.WriteTimeout) {
		return false
	}
	if !p.Field10DeepEqual(ano.PingIntervalSeconds) {
		return false
	}
	if !p.Field11DeepEqual(ano.ReadOnly) {
		return false
	}
	if !p.Field12DeepEqual(ano.SQLRules) {
		return false
	}
	if !p.Field13DeepEqual(ano.ConnectionPoolType) {
		return false
	}
	return true
}

func (p *ModifyDBProxyConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field2DeepEqual(src *int64) bool {

	if p.MaxFrontConnections == src {
		return true
	} else if p.MaxFrontConnections == nil || src == nil {
		return false
	}
	if *p.MaxFrontConnections != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field3DeepEqual(src *int64) bool {

	if p.MaxQPS == src {
		return true
	} else if p.MaxQPS == nil || src == nil {
		return false
	}
	if *p.MaxQPS != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field4DeepEqual(src *int64) bool {

	if p.MaxConnections == src {
		return true
	} else if p.MaxConnections == nil || src == nil {
		return false
	}
	if *p.MaxConnections != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field5DeepEqual(src *int64) bool {

	if p.MaxIdleConnections == src {
		return true
	} else if p.MaxIdleConnections == nil || src == nil {
		return false
	}
	if *p.MaxIdleConnections != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field6DeepEqual(src *int64) bool {

	if p.MinIdleConnections == src {
		return true
	} else if p.MinIdleConnections == nil || src == nil {
		return false
	}
	if *p.MinIdleConnections != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field7DeepEqual(src *string) bool {

	if p.DialTimeout == src {
		return true
	} else if p.DialTimeout == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DialTimeout, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field8DeepEqual(src *string) bool {

	if p.ReadTimeout == src {
		return true
	} else if p.ReadTimeout == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReadTimeout, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field9DeepEqual(src *string) bool {

	if p.WriteTimeout == src {
		return true
	} else if p.WriteTimeout == nil || src == nil {
		return false
	}
	if strings.Compare(*p.WriteTimeout, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field10DeepEqual(src *int64) bool {

	if p.PingIntervalSeconds == src {
		return true
	} else if p.PingIntervalSeconds == nil || src == nil {
		return false
	}
	if *p.PingIntervalSeconds != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field11DeepEqual(src *bool) bool {

	if p.ReadOnly == src {
		return true
	} else if p.ReadOnly == nil || src == nil {
		return false
	}
	if *p.ReadOnly != *src {
		return false
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field12DeepEqual(src []string) bool {

	if len(p.SQLRules) != len(src) {
		return false
	}
	for i, v := range p.SQLRules {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ModifyDBProxyConfigReq) Field13DeepEqual(src *PoolType) bool {

	if p.ConnectionPoolType == src {
		return true
	} else if p.ConnectionPoolType == nil || src == nil {
		return false
	}
	if *p.ConnectionPoolType != *src {
		return false
	}
	return true
}

type DescribeDBProxyConfigReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeDBProxyConfigReq() *DescribeDBProxyConfigReq {
	return &DescribeDBProxyConfigReq{}
}

func (p *DescribeDBProxyConfigReq) InitDefault() {
}

func (p *DescribeDBProxyConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBProxyConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBProxyConfigReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBProxyConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyConfigReq[fieldId]))
}

func (p *DescribeDBProxyConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBProxyConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyConfigReq(%+v)", *p)

}

func (p *DescribeDBProxyConfigReq) DeepEqual(ano *DescribeDBProxyConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBProxyConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBProxyConfigResp struct {
	MaxFrontConnections int64    `thrift:"MaxFrontConnections,1,required" frugal:"1,required,i64" json:"MaxFrontConnections"`
	MaxQPS              int64    `thrift:"MaxQPS,2,required" frugal:"2,required,i64" json:"MaxQPS"`
	MaxConnections      int64    `thrift:"MaxConnections,3,required" frugal:"3,required,i64" json:"MaxConnections"`
	MaxIdleConnections  int64    `thrift:"MaxIdleConnections,4,required" frugal:"4,required,i64" json:"MaxIdleConnections"`
	MinIdleConnections  int64    `thrift:"MinIdleConnections,5,required" frugal:"5,required,i64" json:"MinIdleConnections"`
	DialTimeout         string   `thrift:"DialTimeout,6,required" frugal:"6,required,string" json:"DialTimeout"`
	ReadTimeout         string   `thrift:"ReadTimeout,7,required" frugal:"7,required,string" json:"ReadTimeout"`
	WriteTimeout        string   `thrift:"WriteTimeout,8,required" frugal:"8,required,string" json:"WriteTimeout"`
	PingIntervalSeconds int64    `thrift:"PingIntervalSeconds,9" frugal:"9,default,i64" json:"PingIntervalSeconds"`
	ReadOnly            bool     `thrift:"ReadOnly,10" frugal:"10,default,bool" json:"ReadOnly"`
	SQLRules            []string `thrift:"SQLRules,11" frugal:"11,default,list<string>" json:"SQLRules"`
	InstanceId          string   `thrift:"InstanceId,12" frugal:"12,default,string" json:"InstanceId"`
	ConnectionPoolType  string   `thrift:"ConnectionPoolType,13" frugal:"13,default,string" json:"ConnectionPoolType"`
}

func NewDescribeDBProxyConfigResp() *DescribeDBProxyConfigResp {
	return &DescribeDBProxyConfigResp{}
}

func (p *DescribeDBProxyConfigResp) InitDefault() {
}

func (p *DescribeDBProxyConfigResp) GetMaxFrontConnections() (v int64) {
	return p.MaxFrontConnections
}

func (p *DescribeDBProxyConfigResp) GetMaxQPS() (v int64) {
	return p.MaxQPS
}

func (p *DescribeDBProxyConfigResp) GetMaxConnections() (v int64) {
	return p.MaxConnections
}

func (p *DescribeDBProxyConfigResp) GetMaxIdleConnections() (v int64) {
	return p.MaxIdleConnections
}

func (p *DescribeDBProxyConfigResp) GetMinIdleConnections() (v int64) {
	return p.MinIdleConnections
}

func (p *DescribeDBProxyConfigResp) GetDialTimeout() (v string) {
	return p.DialTimeout
}

func (p *DescribeDBProxyConfigResp) GetReadTimeout() (v string) {
	return p.ReadTimeout
}

func (p *DescribeDBProxyConfigResp) GetWriteTimeout() (v string) {
	return p.WriteTimeout
}

func (p *DescribeDBProxyConfigResp) GetPingIntervalSeconds() (v int64) {
	return p.PingIntervalSeconds
}

func (p *DescribeDBProxyConfigResp) GetReadOnly() (v bool) {
	return p.ReadOnly
}

func (p *DescribeDBProxyConfigResp) GetSQLRules() (v []string) {
	return p.SQLRules
}

func (p *DescribeDBProxyConfigResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBProxyConfigResp) GetConnectionPoolType() (v string) {
	return p.ConnectionPoolType
}
func (p *DescribeDBProxyConfigResp) SetMaxFrontConnections(val int64) {
	p.MaxFrontConnections = val
}
func (p *DescribeDBProxyConfigResp) SetMaxQPS(val int64) {
	p.MaxQPS = val
}
func (p *DescribeDBProxyConfigResp) SetMaxConnections(val int64) {
	p.MaxConnections = val
}
func (p *DescribeDBProxyConfigResp) SetMaxIdleConnections(val int64) {
	p.MaxIdleConnections = val
}
func (p *DescribeDBProxyConfigResp) SetMinIdleConnections(val int64) {
	p.MinIdleConnections = val
}
func (p *DescribeDBProxyConfigResp) SetDialTimeout(val string) {
	p.DialTimeout = val
}
func (p *DescribeDBProxyConfigResp) SetReadTimeout(val string) {
	p.ReadTimeout = val
}
func (p *DescribeDBProxyConfigResp) SetWriteTimeout(val string) {
	p.WriteTimeout = val
}
func (p *DescribeDBProxyConfigResp) SetPingIntervalSeconds(val int64) {
	p.PingIntervalSeconds = val
}
func (p *DescribeDBProxyConfigResp) SetReadOnly(val bool) {
	p.ReadOnly = val
}
func (p *DescribeDBProxyConfigResp) SetSQLRules(val []string) {
	p.SQLRules = val
}
func (p *DescribeDBProxyConfigResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBProxyConfigResp) SetConnectionPoolType(val string) {
	p.ConnectionPoolType = val
}

var fieldIDToName_DescribeDBProxyConfigResp = map[int16]string{
	1:  "MaxFrontConnections",
	2:  "MaxQPS",
	3:  "MaxConnections",
	4:  "MaxIdleConnections",
	5:  "MinIdleConnections",
	6:  "DialTimeout",
	7:  "ReadTimeout",
	8:  "WriteTimeout",
	9:  "PingIntervalSeconds",
	10: "ReadOnly",
	11: "SQLRules",
	12: "InstanceId",
	13: "ConnectionPoolType",
}

func (p *DescribeDBProxyConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMaxFrontConnections bool = false
	var issetMaxQPS bool = false
	var issetMaxConnections bool = false
	var issetMaxIdleConnections bool = false
	var issetMinIdleConnections bool = false
	var issetDialTimeout bool = false
	var issetReadTimeout bool = false
	var issetWriteTimeout bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxFrontConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxQPS = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxIdleConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetMinIdleConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDialTimeout = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetReadTimeout = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetWriteTimeout = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMaxFrontConnections {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMaxQPS {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMaxConnections {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetMaxIdleConnections {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetMinIdleConnections {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDialTimeout {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetReadTimeout {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetWriteTimeout {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyConfigResp[fieldId]))
}

func (p *DescribeDBProxyConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxFrontConnections = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxQPS = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxConnections = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxIdleConnections = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MinIdleConnections = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DialTimeout = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReadTimeout = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WriteTimeout = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PingIntervalSeconds = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReadOnly = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLRules = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBProxyConfigResp) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectionPoolType = _field
	return nil
}

func (p *DescribeDBProxyConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxFrontConnections", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxFrontConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxQPS", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxQPS); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxConnections", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxIdleConnections", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxIdleConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MinIdleConnections", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MinIdleConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DialTimeout", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DialTimeout); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReadTimeout", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReadTimeout); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WriteTimeout", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WriteTimeout); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PingIntervalSeconds", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PingIntervalSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReadOnly", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ReadOnly); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLRules", thrift.LIST, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SQLRules)); err != nil {
		return err
	}
	for _, v := range p.SQLRules {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnectionPoolType", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectionPoolType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeDBProxyConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyConfigResp(%+v)", *p)

}

func (p *DescribeDBProxyConfigResp) DeepEqual(ano *DescribeDBProxyConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MaxFrontConnections) {
		return false
	}
	if !p.Field2DeepEqual(ano.MaxQPS) {
		return false
	}
	if !p.Field3DeepEqual(ano.MaxConnections) {
		return false
	}
	if !p.Field4DeepEqual(ano.MaxIdleConnections) {
		return false
	}
	if !p.Field5DeepEqual(ano.MinIdleConnections) {
		return false
	}
	if !p.Field6DeepEqual(ano.DialTimeout) {
		return false
	}
	if !p.Field7DeepEqual(ano.ReadTimeout) {
		return false
	}
	if !p.Field8DeepEqual(ano.WriteTimeout) {
		return false
	}
	if !p.Field9DeepEqual(ano.PingIntervalSeconds) {
		return false
	}
	if !p.Field10DeepEqual(ano.ReadOnly) {
		return false
	}
	if !p.Field11DeepEqual(ano.SQLRules) {
		return false
	}
	if !p.Field12DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field13DeepEqual(ano.ConnectionPoolType) {
		return false
	}
	return true
}

func (p *DescribeDBProxyConfigResp) Field1DeepEqual(src int64) bool {

	if p.MaxFrontConnections != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field2DeepEqual(src int64) bool {

	if p.MaxQPS != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field3DeepEqual(src int64) bool {

	if p.MaxConnections != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field4DeepEqual(src int64) bool {

	if p.MaxIdleConnections != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field5DeepEqual(src int64) bool {

	if p.MinIdleConnections != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.DialTimeout, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field7DeepEqual(src string) bool {

	if strings.Compare(p.ReadTimeout, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field8DeepEqual(src string) bool {

	if strings.Compare(p.WriteTimeout, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field9DeepEqual(src int64) bool {

	if p.PingIntervalSeconds != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field10DeepEqual(src bool) bool {

	if p.ReadOnly != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field11DeepEqual(src []string) bool {

	if len(p.SQLRules) != len(src) {
		return false
	}
	for i, v := range p.SQLRules {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field12DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyConfigResp) Field13DeepEqual(src string) bool {

	if strings.Compare(p.ConnectionPoolType, src) != 0 {
		return false
	}
	return true
}

type ModifyDBProxyInstanceReq struct {
	InstanceId       string         `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EnableProxy      EnableSwitch   `thrift:"EnableProxy,2" frugal:"2,default,EnableSwitch" json:"EnableProxy"`
	InstanceSpecName string         `thrift:"InstanceSpecName,3" frugal:"3,default,string" json:"InstanceSpecName"`
	Number           int32          `thrift:"Number,4" frugal:"4,default,i32" json:"Number"`
	RequestSource    *RequestSource `thrift:"RequestSource,5,optional" frugal:"5,optional,RequestSource" json:"RequestSource,omitempty"`
}

func NewModifyDBProxyInstanceReq() *ModifyDBProxyInstanceReq {
	return &ModifyDBProxyInstanceReq{}
}

func (p *ModifyDBProxyInstanceReq) InitDefault() {
}

func (p *ModifyDBProxyInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBProxyInstanceReq) GetEnableProxy() (v EnableSwitch) {
	return p.EnableProxy
}

func (p *ModifyDBProxyInstanceReq) GetInstanceSpecName() (v string) {
	return p.InstanceSpecName
}

func (p *ModifyDBProxyInstanceReq) GetNumber() (v int32) {
	return p.Number
}

var ModifyDBProxyInstanceReq_RequestSource_DEFAULT RequestSource

func (p *ModifyDBProxyInstanceReq) GetRequestSource() (v RequestSource) {
	if !p.IsSetRequestSource() {
		return ModifyDBProxyInstanceReq_RequestSource_DEFAULT
	}
	return *p.RequestSource
}
func (p *ModifyDBProxyInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBProxyInstanceReq) SetEnableProxy(val EnableSwitch) {
	p.EnableProxy = val
}
func (p *ModifyDBProxyInstanceReq) SetInstanceSpecName(val string) {
	p.InstanceSpecName = val
}
func (p *ModifyDBProxyInstanceReq) SetNumber(val int32) {
	p.Number = val
}
func (p *ModifyDBProxyInstanceReq) SetRequestSource(val *RequestSource) {
	p.RequestSource = val
}

var fieldIDToName_ModifyDBProxyInstanceReq = map[int16]string{
	1: "InstanceId",
	2: "EnableProxy",
	3: "InstanceSpecName",
	4: "Number",
	5: "RequestSource",
}

func (p *ModifyDBProxyInstanceReq) IsSetRequestSource() bool {
	return p.RequestSource != nil
}

func (p *ModifyDBProxyInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBProxyInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBProxyInstanceReq[fieldId]))
}

func (p *ModifyDBProxyInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBProxyInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableProxy = _field
	return nil
}
func (p *ModifyDBProxyInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceSpecName = _field
	return nil
}
func (p *ModifyDBProxyInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Number = _field
	return nil
}
func (p *ModifyDBProxyInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *RequestSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RequestSource(v)
		_field = &tmp
	}
	p.RequestSource = _field
	return nil
}

func (p *ModifyDBProxyInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBProxyInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableProxy", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableProxy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceSpecName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceSpecName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Number", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Number); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestSource() {
		if err = oprot.WriteFieldBegin("RequestSource", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RequestSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBProxyInstanceReq(%+v)", *p)

}

func (p *ModifyDBProxyInstanceReq) DeepEqual(ano *ModifyDBProxyInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableProxy) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceSpecName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Number) {
		return false
	}
	if !p.Field5DeepEqual(ano.RequestSource) {
		return false
	}
	return true
}

func (p *ModifyDBProxyInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyInstanceReq) Field2DeepEqual(src EnableSwitch) bool {

	if p.EnableProxy != src {
		return false
	}
	return true
}
func (p *ModifyDBProxyInstanceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceSpecName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyInstanceReq) Field4DeepEqual(src int32) bool {

	if p.Number != src {
		return false
	}
	return true
}
func (p *ModifyDBProxyInstanceReq) Field5DeepEqual(src *RequestSource) bool {

	if p.RequestSource == src {
		return true
	} else if p.RequestSource == nil || src == nil {
		return false
	}
	if *p.RequestSource != *src {
		return false
	}
	return true
}

type ModifyDBProxyInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	OrderNO    string `thrift:"OrderNO,2,required" frugal:"2,required,string" json:"OrderNO"`
}

func NewModifyDBProxyInstanceResp() *ModifyDBProxyInstanceResp {
	return &ModifyDBProxyInstanceResp{}
}

func (p *ModifyDBProxyInstanceResp) InitDefault() {
}

func (p *ModifyDBProxyInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBProxyInstanceResp) GetOrderNO() (v string) {
	return p.OrderNO
}
func (p *ModifyDBProxyInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBProxyInstanceResp) SetOrderNO(val string) {
	p.OrderNO = val
}

var fieldIDToName_ModifyDBProxyInstanceResp = map[int16]string{
	1: "InstanceId",
	2: "OrderNO",
}

func (p *ModifyDBProxyInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderNO bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderNO = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderNO {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBProxyInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBProxyInstanceResp[fieldId]))
}

func (p *ModifyDBProxyInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBProxyInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderNO = _field
	return nil
}

func (p *ModifyDBProxyInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBProxyInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBProxyInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBProxyInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderNO", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderNO); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBProxyInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBProxyInstanceResp(%+v)", *p)

}

func (p *ModifyDBProxyInstanceResp) DeepEqual(ano *ModifyDBProxyInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderNO) {
		return false
	}
	return true
}

func (p *ModifyDBProxyInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBProxyInstanceResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderNO, src) != 0 {
		return false
	}
	return true
}

type DescribeDBProxyInstanceReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeDBProxyInstanceReq() *DescribeDBProxyInstanceReq {
	return &DescribeDBProxyInstanceReq{}
}

func (p *DescribeDBProxyInstanceReq) InitDefault() {
}

func (p *DescribeDBProxyInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBProxyInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBProxyInstanceReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBProxyInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyInstanceReq[fieldId]))
}

func (p *DescribeDBProxyInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBProxyInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyInstanceReq(%+v)", *p)

}

func (p *DescribeDBProxyInstanceReq) DeepEqual(ano *DescribeDBProxyInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBProxyInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBProxyInstanceResp struct {
	InstanceId       string         `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	EnableProxy      EnableSwitch   `thrift:"EnableProxy,2,required" frugal:"2,required,EnableSwitch" json:"EnableProxy"`
	InstanceSpecName string         `thrift:"InstanceSpecName,3,required" frugal:"3,required,string" json:"InstanceSpecName"`
	CpuNum           int32          `thrift:"CpuNum,4,required" frugal:"4,required,i32" json:"CpuNum"`
	MemInGb          int32          `thrift:"MemInGb,5,required" frugal:"5,required,i32" json:"MemInGb"`
	StorageSpaceGB   int32          `thrift:"StorageSpaceGB,6,required" frugal:"6,required,i32" json:"StorageSpaceGB"`
	Number           int32          `thrift:"Number,7,required" frugal:"7,required,i32" json:"Number"`
	ChargeType       *ChargeType    `thrift:"ChargeType,8,optional" frugal:"8,optional,ChargeType" json:"ChargeType,omitempty"`
	ChargeStatus     *ChargeStatus  `thrift:"ChargeStatus,9,optional" frugal:"9,optional,ChargeStatus" json:"ChargeStatus,omitempty"`
	InstanceStatus   InstanceStatus `thrift:"InstanceStatus,10,required" frugal:"10,required,InstanceStatus" json:"InstanceStatus"`
	ProxyInstanceID  string         `thrift:"ProxyInstanceID,11,required" frugal:"11,required,string" json:"ProxyInstanceID"`
	CreateTime       string         `thrift:"CreateTime,12,required" frugal:"12,required,string" json:"CreateTime"`
	UpdateTime       string         `thrift:"UpdateTime,13,required" frugal:"13,required,string" json:"UpdateTime"`
}

func NewDescribeDBProxyInstanceResp() *DescribeDBProxyInstanceResp {
	return &DescribeDBProxyInstanceResp{}
}

func (p *DescribeDBProxyInstanceResp) InitDefault() {
}

func (p *DescribeDBProxyInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBProxyInstanceResp) GetEnableProxy() (v EnableSwitch) {
	return p.EnableProxy
}

func (p *DescribeDBProxyInstanceResp) GetInstanceSpecName() (v string) {
	return p.InstanceSpecName
}

func (p *DescribeDBProxyInstanceResp) GetCpuNum() (v int32) {
	return p.CpuNum
}

func (p *DescribeDBProxyInstanceResp) GetMemInGb() (v int32) {
	return p.MemInGb
}

func (p *DescribeDBProxyInstanceResp) GetStorageSpaceGB() (v int32) {
	return p.StorageSpaceGB
}

func (p *DescribeDBProxyInstanceResp) GetNumber() (v int32) {
	return p.Number
}

var DescribeDBProxyInstanceResp_ChargeType_DEFAULT ChargeType

func (p *DescribeDBProxyInstanceResp) GetChargeType() (v ChargeType) {
	if !p.IsSetChargeType() {
		return DescribeDBProxyInstanceResp_ChargeType_DEFAULT
	}
	return *p.ChargeType
}

var DescribeDBProxyInstanceResp_ChargeStatus_DEFAULT ChargeStatus

func (p *DescribeDBProxyInstanceResp) GetChargeStatus() (v ChargeStatus) {
	if !p.IsSetChargeStatus() {
		return DescribeDBProxyInstanceResp_ChargeStatus_DEFAULT
	}
	return *p.ChargeStatus
}

func (p *DescribeDBProxyInstanceResp) GetInstanceStatus() (v InstanceStatus) {
	return p.InstanceStatus
}

func (p *DescribeDBProxyInstanceResp) GetProxyInstanceID() (v string) {
	return p.ProxyInstanceID
}

func (p *DescribeDBProxyInstanceResp) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *DescribeDBProxyInstanceResp) GetUpdateTime() (v string) {
	return p.UpdateTime
}
func (p *DescribeDBProxyInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBProxyInstanceResp) SetEnableProxy(val EnableSwitch) {
	p.EnableProxy = val
}
func (p *DescribeDBProxyInstanceResp) SetInstanceSpecName(val string) {
	p.InstanceSpecName = val
}
func (p *DescribeDBProxyInstanceResp) SetCpuNum(val int32) {
	p.CpuNum = val
}
func (p *DescribeDBProxyInstanceResp) SetMemInGb(val int32) {
	p.MemInGb = val
}
func (p *DescribeDBProxyInstanceResp) SetStorageSpaceGB(val int32) {
	p.StorageSpaceGB = val
}
func (p *DescribeDBProxyInstanceResp) SetNumber(val int32) {
	p.Number = val
}
func (p *DescribeDBProxyInstanceResp) SetChargeType(val *ChargeType) {
	p.ChargeType = val
}
func (p *DescribeDBProxyInstanceResp) SetChargeStatus(val *ChargeStatus) {
	p.ChargeStatus = val
}
func (p *DescribeDBProxyInstanceResp) SetInstanceStatus(val InstanceStatus) {
	p.InstanceStatus = val
}
func (p *DescribeDBProxyInstanceResp) SetProxyInstanceID(val string) {
	p.ProxyInstanceID = val
}
func (p *DescribeDBProxyInstanceResp) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *DescribeDBProxyInstanceResp) SetUpdateTime(val string) {
	p.UpdateTime = val
}

var fieldIDToName_DescribeDBProxyInstanceResp = map[int16]string{
	1:  "InstanceId",
	2:  "EnableProxy",
	3:  "InstanceSpecName",
	4:  "CpuNum",
	5:  "MemInGb",
	6:  "StorageSpaceGB",
	7:  "Number",
	8:  "ChargeType",
	9:  "ChargeStatus",
	10: "InstanceStatus",
	11: "ProxyInstanceID",
	12: "CreateTime",
	13: "UpdateTime",
}

func (p *DescribeDBProxyInstanceResp) IsSetChargeType() bool {
	return p.ChargeType != nil
}

func (p *DescribeDBProxyInstanceResp) IsSetChargeStatus() bool {
	return p.ChargeStatus != nil
}

func (p *DescribeDBProxyInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEnableProxy bool = false
	var issetInstanceSpecName bool = false
	var issetCpuNum bool = false
	var issetMemInGb bool = false
	var issetStorageSpaceGB bool = false
	var issetNumber bool = false
	var issetInstanceStatus bool = false
	var issetProxyInstanceID bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableProxy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceSpecName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemInGb = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageSpaceGB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetProxyInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnableProxy {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceSpecName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCpuNum {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetMemInGb {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStorageSpaceGB {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetNumber {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetInstanceStatus {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetProxyInstanceID {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyInstanceResp[fieldId]))
}

func (p *DescribeDBProxyInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableProxy = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceSpecName = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpuNum = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MemInGb = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageSpaceGB = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Number = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField8(iprot thrift.TProtocol) error {

	var _field *ChargeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ChargeType(v)
		_field = &tmp
	}
	p.ChargeType = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField9(iprot thrift.TProtocol) error {

	var _field *ChargeStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ChargeStatus(v)
		_field = &tmp
	}
	p.ChargeStatus = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField10(iprot thrift.TProtocol) error {

	var _field InstanceStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceStatus(v)
	}
	p.InstanceStatus = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProxyInstanceID = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *DescribeDBProxyInstanceResp) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *DescribeDBProxyInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableProxy", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableProxy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceSpecName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceSpecName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuNum", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CpuNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MemInGb", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.MemInGb); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageSpaceGB", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageSpaceGB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Number", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Number); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeType() {
		if err = oprot.WriteFieldBegin("ChargeType", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ChargeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeStatus() {
		if err = oprot.WriteFieldBegin("ChargeStatus", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ChargeStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceStatus", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProxyInstanceID", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProxyInstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeDBProxyInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyInstanceResp(%+v)", *p)

}

func (p *DescribeDBProxyInstanceResp) DeepEqual(ano *DescribeDBProxyInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableProxy) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceSpecName) {
		return false
	}
	if !p.Field4DeepEqual(ano.CpuNum) {
		return false
	}
	if !p.Field5DeepEqual(ano.MemInGb) {
		return false
	}
	if !p.Field6DeepEqual(ano.StorageSpaceGB) {
		return false
	}
	if !p.Field7DeepEqual(ano.Number) {
		return false
	}
	if !p.Field8DeepEqual(ano.ChargeType) {
		return false
	}
	if !p.Field9DeepEqual(ano.ChargeStatus) {
		return false
	}
	if !p.Field10DeepEqual(ano.InstanceStatus) {
		return false
	}
	if !p.Field11DeepEqual(ano.ProxyInstanceID) {
		return false
	}
	if !p.Field12DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field13DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *DescribeDBProxyInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field2DeepEqual(src EnableSwitch) bool {

	if p.EnableProxy != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceSpecName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field4DeepEqual(src int32) bool {

	if p.CpuNum != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field5DeepEqual(src int32) bool {

	if p.MemInGb != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field6DeepEqual(src int32) bool {

	if p.StorageSpaceGB != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field7DeepEqual(src int32) bool {

	if p.Number != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field8DeepEqual(src *ChargeType) bool {

	if p.ChargeType == src {
		return true
	} else if p.ChargeType == nil || src == nil {
		return false
	}
	if *p.ChargeType != *src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field9DeepEqual(src *ChargeStatus) bool {

	if p.ChargeStatus == src {
		return true
	} else if p.ChargeStatus == nil || src == nil {
		return false
	}
	if *p.ChargeStatus != *src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field10DeepEqual(src InstanceStatus) bool {

	if p.InstanceStatus != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field11DeepEqual(src string) bool {

	if strings.Compare(p.ProxyInstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field12DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyInstanceResp) Field13DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}

type DescribeDBProxyRecommendReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeDBProxyRecommendReq() *DescribeDBProxyRecommendReq {
	return &DescribeDBProxyRecommendReq{}
}

func (p *DescribeDBProxyRecommendReq) InitDefault() {
}

func (p *DescribeDBProxyRecommendReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBProxyRecommendReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBProxyRecommendReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBProxyRecommendReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyRecommendReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyRecommendReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyRecommendReq[fieldId]))
}

func (p *DescribeDBProxyRecommendReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBProxyRecommendReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyRecommendReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyRecommendReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyRecommendReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyRecommendReq(%+v)", *p)

}

func (p *DescribeDBProxyRecommendReq) DeepEqual(ano *DescribeDBProxyRecommendReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBProxyRecommendReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBProxyRecommendResp struct {
	InstanceId       string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceSpecName string `thrift:"InstanceSpecName,2,required" frugal:"2,required,string" json:"InstanceSpecName"`
	CpuNum           int32  `thrift:"CpuNum,3,required" frugal:"3,required,i32" json:"CpuNum"`
	MemInGb          int32  `thrift:"MemInGb,4,required" frugal:"4,required,i32" json:"MemInGb"`
	StorageSpaceGB   int32  `thrift:"StorageSpaceGB,5,required" frugal:"5,required,i32" json:"StorageSpaceGB"`
	Number           int32  `thrift:"Number,6,required" frugal:"6,required,i32" json:"Number"`
}

func NewDescribeDBProxyRecommendResp() *DescribeDBProxyRecommendResp {
	return &DescribeDBProxyRecommendResp{}
}

func (p *DescribeDBProxyRecommendResp) InitDefault() {
}

func (p *DescribeDBProxyRecommendResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBProxyRecommendResp) GetInstanceSpecName() (v string) {
	return p.InstanceSpecName
}

func (p *DescribeDBProxyRecommendResp) GetCpuNum() (v int32) {
	return p.CpuNum
}

func (p *DescribeDBProxyRecommendResp) GetMemInGb() (v int32) {
	return p.MemInGb
}

func (p *DescribeDBProxyRecommendResp) GetStorageSpaceGB() (v int32) {
	return p.StorageSpaceGB
}

func (p *DescribeDBProxyRecommendResp) GetNumber() (v int32) {
	return p.Number
}
func (p *DescribeDBProxyRecommendResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBProxyRecommendResp) SetInstanceSpecName(val string) {
	p.InstanceSpecName = val
}
func (p *DescribeDBProxyRecommendResp) SetCpuNum(val int32) {
	p.CpuNum = val
}
func (p *DescribeDBProxyRecommendResp) SetMemInGb(val int32) {
	p.MemInGb = val
}
func (p *DescribeDBProxyRecommendResp) SetStorageSpaceGB(val int32) {
	p.StorageSpaceGB = val
}
func (p *DescribeDBProxyRecommendResp) SetNumber(val int32) {
	p.Number = val
}

var fieldIDToName_DescribeDBProxyRecommendResp = map[int16]string{
	1: "InstanceId",
	2: "InstanceSpecName",
	3: "CpuNum",
	4: "MemInGb",
	5: "StorageSpaceGB",
	6: "Number",
}

func (p *DescribeDBProxyRecommendResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyRecommendResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceSpecName bool = false
	var issetCpuNum bool = false
	var issetMemInGb bool = false
	var issetStorageSpaceGB bool = false
	var issetNumber bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceSpecName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemInGb = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageSpaceGB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceSpecName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCpuNum {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetMemInGb {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStorageSpaceGB {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNumber {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBProxyRecommendResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBProxyRecommendResp[fieldId]))
}

func (p *DescribeDBProxyRecommendResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBProxyRecommendResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceSpecName = _field
	return nil
}
func (p *DescribeDBProxyRecommendResp) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpuNum = _field
	return nil
}
func (p *DescribeDBProxyRecommendResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MemInGb = _field
	return nil
}
func (p *DescribeDBProxyRecommendResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageSpaceGB = _field
	return nil
}
func (p *DescribeDBProxyRecommendResp) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Number = _field
	return nil
}

func (p *DescribeDBProxyRecommendResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBProxyRecommendResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBProxyRecommendResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceSpecName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceSpecName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuNum", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CpuNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MemInGb", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.MemInGb); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageSpaceGB", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageSpaceGB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Number", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Number); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBProxyRecommendResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBProxyRecommendResp(%+v)", *p)

}

func (p *DescribeDBProxyRecommendResp) DeepEqual(ano *DescribeDBProxyRecommendResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceSpecName) {
		return false
	}
	if !p.Field3DeepEqual(ano.CpuNum) {
		return false
	}
	if !p.Field4DeepEqual(ano.MemInGb) {
		return false
	}
	if !p.Field5DeepEqual(ano.StorageSpaceGB) {
		return false
	}
	if !p.Field6DeepEqual(ano.Number) {
		return false
	}
	return true
}

func (p *DescribeDBProxyRecommendResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyRecommendResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceSpecName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBProxyRecommendResp) Field3DeepEqual(src int32) bool {

	if p.CpuNum != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyRecommendResp) Field4DeepEqual(src int32) bool {

	if p.MemInGb != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyRecommendResp) Field5DeepEqual(src int32) bool {

	if p.StorageSpaceGB != src {
		return false
	}
	return true
}
func (p *DescribeDBProxyRecommendResp) Field6DeepEqual(src int32) bool {

	if p.Number != src {
		return false
	}
	return true
}

type AllocateInstancePublicServiceNetworkReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewAllocateInstancePublicServiceNetworkReq() *AllocateInstancePublicServiceNetworkReq {
	return &AllocateInstancePublicServiceNetworkReq{}
}

func (p *AllocateInstancePublicServiceNetworkReq) InitDefault() {
}

func (p *AllocateInstancePublicServiceNetworkReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *AllocateInstancePublicServiceNetworkReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_AllocateInstancePublicServiceNetworkReq = map[int16]string{
	1: "InstanceId",
}

func (p *AllocateInstancePublicServiceNetworkReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateInstancePublicServiceNetworkReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateInstancePublicServiceNetworkReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateInstancePublicServiceNetworkReq[fieldId]))
}

func (p *AllocateInstancePublicServiceNetworkReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *AllocateInstancePublicServiceNetworkReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateInstancePublicServiceNetworkReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateInstancePublicServiceNetworkReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateInstancePublicServiceNetworkReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateInstancePublicServiceNetworkReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateInstancePublicServiceNetworkReq(%+v)", *p)

}

func (p *AllocateInstancePublicServiceNetworkReq) DeepEqual(ano *AllocateInstancePublicServiceNetworkReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *AllocateInstancePublicServiceNetworkReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type AllocateInstancePublicServiceNetworkResp struct {
	Domain string `thrift:"Domain,1,required" frugal:"1,required,string" json:"Domain"`
	Port   string `thrift:"Port,2,required" frugal:"2,required,string" json:"Port"`
}

func NewAllocateInstancePublicServiceNetworkResp() *AllocateInstancePublicServiceNetworkResp {
	return &AllocateInstancePublicServiceNetworkResp{}
}

func (p *AllocateInstancePublicServiceNetworkResp) InitDefault() {
}

func (p *AllocateInstancePublicServiceNetworkResp) GetDomain() (v string) {
	return p.Domain
}

func (p *AllocateInstancePublicServiceNetworkResp) GetPort() (v string) {
	return p.Port
}
func (p *AllocateInstancePublicServiceNetworkResp) SetDomain(val string) {
	p.Domain = val
}
func (p *AllocateInstancePublicServiceNetworkResp) SetPort(val string) {
	p.Port = val
}

var fieldIDToName_AllocateInstancePublicServiceNetworkResp = map[int16]string{
	1: "Domain",
	2: "Port",
}

func (p *AllocateInstancePublicServiceNetworkResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateInstancePublicServiceNetworkResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDomain bool = false
	var issetPort bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDomain = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPort = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDomain {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPort {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateInstancePublicServiceNetworkResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateInstancePublicServiceNetworkResp[fieldId]))
}

func (p *AllocateInstancePublicServiceNetworkResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Domain = _field
	return nil
}
func (p *AllocateInstancePublicServiceNetworkResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Port = _field
	return nil
}

func (p *AllocateInstancePublicServiceNetworkResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateInstancePublicServiceNetworkResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateInstancePublicServiceNetworkResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateInstancePublicServiceNetworkResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Domain", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Domain); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateInstancePublicServiceNetworkResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Port", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Port); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllocateInstancePublicServiceNetworkResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateInstancePublicServiceNetworkResp(%+v)", *p)

}

func (p *AllocateInstancePublicServiceNetworkResp) DeepEqual(ano *AllocateInstancePublicServiceNetworkResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Domain) {
		return false
	}
	if !p.Field2DeepEqual(ano.Port) {
		return false
	}
	return true
}

func (p *AllocateInstancePublicServiceNetworkResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Domain, src) != 0 {
		return false
	}
	return true
}
func (p *AllocateInstancePublicServiceNetworkResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Port, src) != 0 {
		return false
	}
	return true
}

type ReleaseInstancePublicServiceNetworkReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewReleaseInstancePublicServiceNetworkReq() *ReleaseInstancePublicServiceNetworkReq {
	return &ReleaseInstancePublicServiceNetworkReq{}
}

func (p *ReleaseInstancePublicServiceNetworkReq) InitDefault() {
}

func (p *ReleaseInstancePublicServiceNetworkReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ReleaseInstancePublicServiceNetworkReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ReleaseInstancePublicServiceNetworkReq = map[int16]string{
	1: "InstanceId",
}

func (p *ReleaseInstancePublicServiceNetworkReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReleaseInstancePublicServiceNetworkReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReleaseInstancePublicServiceNetworkReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReleaseInstancePublicServiceNetworkReq[fieldId]))
}

func (p *ReleaseInstancePublicServiceNetworkReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ReleaseInstancePublicServiceNetworkReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReleaseInstancePublicServiceNetworkReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseInstancePublicServiceNetworkReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReleaseInstancePublicServiceNetworkReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReleaseInstancePublicServiceNetworkReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReleaseInstancePublicServiceNetworkReq(%+v)", *p)

}

func (p *ReleaseInstancePublicServiceNetworkReq) DeepEqual(ano *ReleaseInstancePublicServiceNetworkReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ReleaseInstancePublicServiceNetworkReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type CreateCrossInstanceSinglePLBReq struct {
	SrcInstanceId string `thrift:"SrcInstanceId,1" frugal:"1,default,string" json:"SrcInstanceId"`
	SrcVpcId      string `thrift:"SrcVpcId,2" frugal:"2,default,string" json:"SrcVpcId"`
	DstInstanceId string `thrift:"DstInstanceId,3,required" frugal:"3,required,string" json:"DstInstanceId"`
}

func NewCreateCrossInstanceSinglePLBReq() *CreateCrossInstanceSinglePLBReq {
	return &CreateCrossInstanceSinglePLBReq{}
}

func (p *CreateCrossInstanceSinglePLBReq) InitDefault() {
}

func (p *CreateCrossInstanceSinglePLBReq) GetSrcInstanceId() (v string) {
	return p.SrcInstanceId
}

func (p *CreateCrossInstanceSinglePLBReq) GetSrcVpcId() (v string) {
	return p.SrcVpcId
}

func (p *CreateCrossInstanceSinglePLBReq) GetDstInstanceId() (v string) {
	return p.DstInstanceId
}
func (p *CreateCrossInstanceSinglePLBReq) SetSrcInstanceId(val string) {
	p.SrcInstanceId = val
}
func (p *CreateCrossInstanceSinglePLBReq) SetSrcVpcId(val string) {
	p.SrcVpcId = val
}
func (p *CreateCrossInstanceSinglePLBReq) SetDstInstanceId(val string) {
	p.DstInstanceId = val
}

var fieldIDToName_CreateCrossInstanceSinglePLBReq = map[int16]string{
	1: "SrcInstanceId",
	2: "SrcVpcId",
	3: "DstInstanceId",
}

func (p *CreateCrossInstanceSinglePLBReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCrossInstanceSinglePLBReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDstInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDstInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDstInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateCrossInstanceSinglePLBReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateCrossInstanceSinglePLBReq[fieldId]))
}

func (p *CreateCrossInstanceSinglePLBReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcInstanceId = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcVpcId = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DstInstanceId = _field
	return nil
}

func (p *CreateCrossInstanceSinglePLBReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCrossInstanceSinglePLBReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCrossInstanceSinglePLBReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcVpcId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcVpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DstInstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DstInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCrossInstanceSinglePLBReq(%+v)", *p)

}

func (p *CreateCrossInstanceSinglePLBReq) DeepEqual(ano *CreateCrossInstanceSinglePLBReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SrcInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SrcVpcId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DstInstanceId) {
		return false
	}
	return true
}

func (p *CreateCrossInstanceSinglePLBReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SrcInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SrcVpcId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DstInstanceId, src) != 0 {
		return false
	}
	return true
}

type CreateCrossInstanceSinglePLBResp struct {
	SrcInstanceId string `thrift:"SrcInstanceId,1,required" frugal:"1,required,string" json:"SrcInstanceId"`
	SrcVpcId      string `thrift:"SrcVpcId,2,required" frugal:"2,required,string" json:"SrcVpcId"`
	DstInstanceId string `thrift:"DstInstanceId,3,required" frugal:"3,required,string" json:"DstInstanceId"`
	VIP           string `thrift:"VIP,4,required" frugal:"4,required,string" json:"VIP"`
	Port          string `thrift:"Port,5,required" frugal:"5,required,string" json:"Port"`
	Domain        string `thrift:"Domain,6,required" frugal:"6,required,string" json:"Domain"`
}

func NewCreateCrossInstanceSinglePLBResp() *CreateCrossInstanceSinglePLBResp {
	return &CreateCrossInstanceSinglePLBResp{}
}

func (p *CreateCrossInstanceSinglePLBResp) InitDefault() {
}

func (p *CreateCrossInstanceSinglePLBResp) GetSrcInstanceId() (v string) {
	return p.SrcInstanceId
}

func (p *CreateCrossInstanceSinglePLBResp) GetSrcVpcId() (v string) {
	return p.SrcVpcId
}

func (p *CreateCrossInstanceSinglePLBResp) GetDstInstanceId() (v string) {
	return p.DstInstanceId
}

func (p *CreateCrossInstanceSinglePLBResp) GetVIP() (v string) {
	return p.VIP
}

func (p *CreateCrossInstanceSinglePLBResp) GetPort() (v string) {
	return p.Port
}

func (p *CreateCrossInstanceSinglePLBResp) GetDomain() (v string) {
	return p.Domain
}
func (p *CreateCrossInstanceSinglePLBResp) SetSrcInstanceId(val string) {
	p.SrcInstanceId = val
}
func (p *CreateCrossInstanceSinglePLBResp) SetSrcVpcId(val string) {
	p.SrcVpcId = val
}
func (p *CreateCrossInstanceSinglePLBResp) SetDstInstanceId(val string) {
	p.DstInstanceId = val
}
func (p *CreateCrossInstanceSinglePLBResp) SetVIP(val string) {
	p.VIP = val
}
func (p *CreateCrossInstanceSinglePLBResp) SetPort(val string) {
	p.Port = val
}
func (p *CreateCrossInstanceSinglePLBResp) SetDomain(val string) {
	p.Domain = val
}

var fieldIDToName_CreateCrossInstanceSinglePLBResp = map[int16]string{
	1: "SrcInstanceId",
	2: "SrcVpcId",
	3: "DstInstanceId",
	4: "VIP",
	5: "Port",
	6: "Domain",
}

func (p *CreateCrossInstanceSinglePLBResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCrossInstanceSinglePLBResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSrcInstanceId bool = false
	var issetSrcVpcId bool = false
	var issetDstInstanceId bool = false
	var issetVIP bool = false
	var issetPort bool = false
	var issetDomain bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrcInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrcVpcId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDstInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetVIP = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPort = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDomain = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSrcInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSrcVpcId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDstInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetVIP {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPort {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDomain {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateCrossInstanceSinglePLBResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateCrossInstanceSinglePLBResp[fieldId]))
}

func (p *CreateCrossInstanceSinglePLBResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcInstanceId = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcVpcId = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DstInstanceId = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VIP = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Port = _field
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Domain = _field
	return nil
}

func (p *CreateCrossInstanceSinglePLBResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCrossInstanceSinglePLBResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCrossInstanceSinglePLBResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcVpcId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcVpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DstInstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DstInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VIP", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VIP); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Port", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Port); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Domain", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Domain); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateCrossInstanceSinglePLBResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCrossInstanceSinglePLBResp(%+v)", *p)

}

func (p *CreateCrossInstanceSinglePLBResp) DeepEqual(ano *CreateCrossInstanceSinglePLBResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SrcInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SrcVpcId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DstInstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.VIP) {
		return false
	}
	if !p.Field5DeepEqual(ano.Port) {
		return false
	}
	if !p.Field6DeepEqual(ano.Domain) {
		return false
	}
	return true
}

func (p *CreateCrossInstanceSinglePLBResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SrcInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SrcVpcId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DstInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.VIP, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Port, src) != 0 {
		return false
	}
	return true
}
func (p *CreateCrossInstanceSinglePLBResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Domain, src) != 0 {
		return false
	}
	return true
}

type DeleteCrossInstanceSinglePLBReq struct {
	SrcInstanceId string `thrift:"SrcInstanceId,1" frugal:"1,default,string" json:"SrcInstanceId"`
	SrcVpcId      string `thrift:"SrcVpcId,2" frugal:"2,default,string" json:"SrcVpcId"`
	DstInstanceId string `thrift:"DstInstanceId,3,required" frugal:"3,required,string" json:"DstInstanceId"`
}

func NewDeleteCrossInstanceSinglePLBReq() *DeleteCrossInstanceSinglePLBReq {
	return &DeleteCrossInstanceSinglePLBReq{}
}

func (p *DeleteCrossInstanceSinglePLBReq) InitDefault() {
}

func (p *DeleteCrossInstanceSinglePLBReq) GetSrcInstanceId() (v string) {
	return p.SrcInstanceId
}

func (p *DeleteCrossInstanceSinglePLBReq) GetSrcVpcId() (v string) {
	return p.SrcVpcId
}

func (p *DeleteCrossInstanceSinglePLBReq) GetDstInstanceId() (v string) {
	return p.DstInstanceId
}
func (p *DeleteCrossInstanceSinglePLBReq) SetSrcInstanceId(val string) {
	p.SrcInstanceId = val
}
func (p *DeleteCrossInstanceSinglePLBReq) SetSrcVpcId(val string) {
	p.SrcVpcId = val
}
func (p *DeleteCrossInstanceSinglePLBReq) SetDstInstanceId(val string) {
	p.DstInstanceId = val
}

var fieldIDToName_DeleteCrossInstanceSinglePLBReq = map[int16]string{
	1: "SrcInstanceId",
	2: "SrcVpcId",
	3: "DstInstanceId",
}

func (p *DeleteCrossInstanceSinglePLBReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCrossInstanceSinglePLBReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDstInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDstInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDstInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteCrossInstanceSinglePLBReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteCrossInstanceSinglePLBReq[fieldId]))
}

func (p *DeleteCrossInstanceSinglePLBReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcInstanceId = _field
	return nil
}
func (p *DeleteCrossInstanceSinglePLBReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcVpcId = _field
	return nil
}
func (p *DeleteCrossInstanceSinglePLBReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DstInstanceId = _field
	return nil
}

func (p *DeleteCrossInstanceSinglePLBReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCrossInstanceSinglePLBReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCrossInstanceSinglePLBReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteCrossInstanceSinglePLBReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteCrossInstanceSinglePLBReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcVpcId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcVpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteCrossInstanceSinglePLBReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DstInstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DstInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeleteCrossInstanceSinglePLBReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCrossInstanceSinglePLBReq(%+v)", *p)

}

func (p *DeleteCrossInstanceSinglePLBReq) DeepEqual(ano *DeleteCrossInstanceSinglePLBReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SrcInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SrcVpcId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DstInstanceId) {
		return false
	}
	return true
}

func (p *DeleteCrossInstanceSinglePLBReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SrcInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteCrossInstanceSinglePLBReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SrcVpcId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteCrossInstanceSinglePLBReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DstInstanceId, src) != 0 {
		return false
	}
	return true
}

type ListCrossInstanceSinglePLBReq struct {
	SrcInstanceId string `thrift:"SrcInstanceId,1" frugal:"1,default,string" json:"SrcInstanceId"`
	SrcVpcId      string `thrift:"SrcVpcId,2" frugal:"2,default,string" json:"SrcVpcId"`
	DstInstanceId string `thrift:"DstInstanceId,3" frugal:"3,default,string" json:"DstInstanceId"`
}

func NewListCrossInstanceSinglePLBReq() *ListCrossInstanceSinglePLBReq {
	return &ListCrossInstanceSinglePLBReq{}
}

func (p *ListCrossInstanceSinglePLBReq) InitDefault() {
}

func (p *ListCrossInstanceSinglePLBReq) GetSrcInstanceId() (v string) {
	return p.SrcInstanceId
}

func (p *ListCrossInstanceSinglePLBReq) GetSrcVpcId() (v string) {
	return p.SrcVpcId
}

func (p *ListCrossInstanceSinglePLBReq) GetDstInstanceId() (v string) {
	return p.DstInstanceId
}
func (p *ListCrossInstanceSinglePLBReq) SetSrcInstanceId(val string) {
	p.SrcInstanceId = val
}
func (p *ListCrossInstanceSinglePLBReq) SetSrcVpcId(val string) {
	p.SrcVpcId = val
}
func (p *ListCrossInstanceSinglePLBReq) SetDstInstanceId(val string) {
	p.DstInstanceId = val
}

var fieldIDToName_ListCrossInstanceSinglePLBReq = map[int16]string{
	1: "SrcInstanceId",
	2: "SrcVpcId",
	3: "DstInstanceId",
}

func (p *ListCrossInstanceSinglePLBReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListCrossInstanceSinglePLBReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListCrossInstanceSinglePLBReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcInstanceId = _field
	return nil
}
func (p *ListCrossInstanceSinglePLBReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcVpcId = _field
	return nil
}
func (p *ListCrossInstanceSinglePLBReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DstInstanceId = _field
	return nil
}

func (p *ListCrossInstanceSinglePLBReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListCrossInstanceSinglePLBReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListCrossInstanceSinglePLBReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcVpcId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcVpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DstInstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DstInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCrossInstanceSinglePLBReq(%+v)", *p)

}

func (p *ListCrossInstanceSinglePLBReq) DeepEqual(ano *ListCrossInstanceSinglePLBReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SrcInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SrcVpcId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DstInstanceId) {
		return false
	}
	return true
}

func (p *ListCrossInstanceSinglePLBReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SrcInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListCrossInstanceSinglePLBReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SrcVpcId, src) != 0 {
		return false
	}
	return true
}
func (p *ListCrossInstanceSinglePLBReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DstInstanceId, src) != 0 {
		return false
	}
	return true
}

type ListCrossInstanceSinglePLBResp struct {
	Total int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*SinglePLBInfo `thrift:"Datas,2,required" frugal:"2,required,list<SinglePLBInfo>" json:"Datas"`
}

func NewListCrossInstanceSinglePLBResp() *ListCrossInstanceSinglePLBResp {
	return &ListCrossInstanceSinglePLBResp{}
}

func (p *ListCrossInstanceSinglePLBResp) InitDefault() {
}

func (p *ListCrossInstanceSinglePLBResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListCrossInstanceSinglePLBResp) GetDatas() (v []*SinglePLBInfo) {
	return p.Datas
}
func (p *ListCrossInstanceSinglePLBResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListCrossInstanceSinglePLBResp) SetDatas(val []*SinglePLBInfo) {
	p.Datas = val
}

var fieldIDToName_ListCrossInstanceSinglePLBResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListCrossInstanceSinglePLBResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListCrossInstanceSinglePLBResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListCrossInstanceSinglePLBResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListCrossInstanceSinglePLBResp[fieldId]))
}

func (p *ListCrossInstanceSinglePLBResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListCrossInstanceSinglePLBResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SinglePLBInfo, 0, size)
	values := make([]SinglePLBInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListCrossInstanceSinglePLBResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListCrossInstanceSinglePLBResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListCrossInstanceSinglePLBResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListCrossInstanceSinglePLBResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCrossInstanceSinglePLBResp(%+v)", *p)

}

func (p *ListCrossInstanceSinglePLBResp) DeepEqual(ano *ListCrossInstanceSinglePLBResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListCrossInstanceSinglePLBResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListCrossInstanceSinglePLBResp) Field2DeepEqual(src []*SinglePLBInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AllocateProxyRouteAddressReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EnableSwitch EnableSwitch  `thrift:"EnableSwitch,2,required" frugal:"2,required,EnableSwitch" json:"EnableSwitch"`
	RouteAddress *RouteAddress `thrift:"RouteAddress,3,required" frugal:"3,required,RouteAddress" json:"RouteAddress"`
}

func NewAllocateProxyRouteAddressReq() *AllocateProxyRouteAddressReq {
	return &AllocateProxyRouteAddressReq{}
}

func (p *AllocateProxyRouteAddressReq) InitDefault() {
}

func (p *AllocateProxyRouteAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AllocateProxyRouteAddressReq) GetEnableSwitch() (v EnableSwitch) {
	return p.EnableSwitch
}

var AllocateProxyRouteAddressReq_RouteAddress_DEFAULT *RouteAddress

func (p *AllocateProxyRouteAddressReq) GetRouteAddress() (v *RouteAddress) {
	if !p.IsSetRouteAddress() {
		return AllocateProxyRouteAddressReq_RouteAddress_DEFAULT
	}
	return p.RouteAddress
}
func (p *AllocateProxyRouteAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AllocateProxyRouteAddressReq) SetEnableSwitch(val EnableSwitch) {
	p.EnableSwitch = val
}
func (p *AllocateProxyRouteAddressReq) SetRouteAddress(val *RouteAddress) {
	p.RouteAddress = val
}

var fieldIDToName_AllocateProxyRouteAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EnableSwitch",
	3: "RouteAddress",
}

func (p *AllocateProxyRouteAddressReq) IsSetRouteAddress() bool {
	return p.RouteAddress != nil
}

func (p *AllocateProxyRouteAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateProxyRouteAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEnableSwitch bool = false
	var issetRouteAddress bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableSwitch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRouteAddress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnableSwitch {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRouteAddress {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllocateProxyRouteAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AllocateProxyRouteAddressReq[fieldId]))
}

func (p *AllocateProxyRouteAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AllocateProxyRouteAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableSwitch
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableSwitch(v)
	}
	p.EnableSwitch = _field
	return nil
}
func (p *AllocateProxyRouteAddressReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewRouteAddress()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RouteAddress = _field
	return nil
}

func (p *AllocateProxyRouteAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllocateProxyRouteAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllocateProxyRouteAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllocateProxyRouteAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllocateProxyRouteAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableSwitch", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EnableSwitch)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllocateProxyRouteAddressReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RouteAddress", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.RouteAddress.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AllocateProxyRouteAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllocateProxyRouteAddressReq(%+v)", *p)

}

func (p *AllocateProxyRouteAddressReq) DeepEqual(ano *AllocateProxyRouteAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableSwitch) {
		return false
	}
	if !p.Field3DeepEqual(ano.RouteAddress) {
		return false
	}
	return true
}

func (p *AllocateProxyRouteAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AllocateProxyRouteAddressReq) Field2DeepEqual(src EnableSwitch) bool {

	if p.EnableSwitch != src {
		return false
	}
	return true
}
func (p *AllocateProxyRouteAddressReq) Field3DeepEqual(src *RouteAddress) bool {

	if !p.RouteAddress.DeepEqual(src) {
		return false
	}
	return true
}

type ListProxyRouteAddressReq struct {
	InstanceId       string            `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	RouteAddressType *RouteAddressType `thrift:"RouteAddressType,2,optional" frugal:"2,optional,RouteAddressType" json:"RouteAddressType,omitempty"`
}

func NewListProxyRouteAddressReq() *ListProxyRouteAddressReq {
	return &ListProxyRouteAddressReq{}
}

func (p *ListProxyRouteAddressReq) InitDefault() {
}

func (p *ListProxyRouteAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ListProxyRouteAddressReq_RouteAddressType_DEFAULT RouteAddressType

func (p *ListProxyRouteAddressReq) GetRouteAddressType() (v RouteAddressType) {
	if !p.IsSetRouteAddressType() {
		return ListProxyRouteAddressReq_RouteAddressType_DEFAULT
	}
	return *p.RouteAddressType
}
func (p *ListProxyRouteAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListProxyRouteAddressReq) SetRouteAddressType(val *RouteAddressType) {
	p.RouteAddressType = val
}

var fieldIDToName_ListProxyRouteAddressReq = map[int16]string{
	1: "InstanceId",
	2: "RouteAddressType",
}

func (p *ListProxyRouteAddressReq) IsSetRouteAddressType() bool {
	return p.RouteAddressType != nil
}

func (p *ListProxyRouteAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListProxyRouteAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListProxyRouteAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListProxyRouteAddressReq[fieldId]))
}

func (p *ListProxyRouteAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListProxyRouteAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *RouteAddressType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RouteAddressType(v)
		_field = &tmp
	}
	p.RouteAddressType = _field
	return nil
}

func (p *ListProxyRouteAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListProxyRouteAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListProxyRouteAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListProxyRouteAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListProxyRouteAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRouteAddressType() {
		if err = oprot.WriteFieldBegin("RouteAddressType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RouteAddressType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListProxyRouteAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProxyRouteAddressReq(%+v)", *p)

}

func (p *ListProxyRouteAddressReq) DeepEqual(ano *ListProxyRouteAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.RouteAddressType) {
		return false
	}
	return true
}

func (p *ListProxyRouteAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListProxyRouteAddressReq) Field2DeepEqual(src *RouteAddressType) bool {

	if p.RouteAddressType == src {
		return true
	} else if p.RouteAddressType == nil || src == nil {
		return false
	}
	if *p.RouteAddressType != *src {
		return false
	}
	return true
}

type ListProxyRouteAddressResp struct {
	Datas []*RouteAddress `thrift:"Datas,1,required" frugal:"1,required,list<RouteAddress>" json:"Datas"`
}

func NewListProxyRouteAddressResp() *ListProxyRouteAddressResp {
	return &ListProxyRouteAddressResp{}
}

func (p *ListProxyRouteAddressResp) InitDefault() {
}

func (p *ListProxyRouteAddressResp) GetDatas() (v []*RouteAddress) {
	return p.Datas
}
func (p *ListProxyRouteAddressResp) SetDatas(val []*RouteAddress) {
	p.Datas = val
}

var fieldIDToName_ListProxyRouteAddressResp = map[int16]string{
	1: "Datas",
}

func (p *ListProxyRouteAddressResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListProxyRouteAddressResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatas {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListProxyRouteAddressResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListProxyRouteAddressResp[fieldId]))
}

func (p *ListProxyRouteAddressResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RouteAddress, 0, size)
	values := make([]RouteAddress, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListProxyRouteAddressResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListProxyRouteAddressResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListProxyRouteAddressResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListProxyRouteAddressResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListProxyRouteAddressResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProxyRouteAddressResp(%+v)", *p)

}

func (p *ListProxyRouteAddressResp) DeepEqual(ano *ListProxyRouteAddressResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListProxyRouteAddressResp) Field1DeepEqual(src []*RouteAddress) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDBInstanceIPListReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceIPListReq) IsValid() error {
	return nil
}
func (p *DeleteDBInstanceIPListReq) IsValid() error {
	return nil
}
func (p *ListDBInstanceIPListsReq) IsValid() error {
	return nil
}
func (p *ListDBInstanceIPListsResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceTDEReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceTDEResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceTDEReq) IsValid() error {
	return nil
}
func (p *UpgradeAllowListVersionReq) IsValid() error {
	return nil
}
func (p *UpgradeAllowListVersionResp) IsValid() error {
	return nil
}
func (p *DescribeAllowListsReq) IsValid() error {
	return nil
}
func (p *AllowListObject) IsValid() error {
	return nil
}
func (p *DescribeAllowListsResp) IsValid() error {
	return nil
}
func (p *DescribeAllowListDetailReq) IsValid() error {
	return nil
}
func (p *AssociatedInstanceObject) IsValid() error {
	return nil
}
func (p *DescribeAllowListDetailResp) IsValid() error {
	return nil
}
func (p *CreateAllowListReq) IsValid() error {
	return nil
}
func (p *CreateAllowListResp) IsValid() error {
	return nil
}
func (p *ModifyAllowListReq) IsValid() error {
	return nil
}
func (p *ModifyAllowListResp) IsValid() error {
	return nil
}
func (p *DeleteAllowListReq) IsValid() error {
	return nil
}
func (p *DeleteAllowListResp) IsValid() error {
	return nil
}
func (p *AssociateAllowListReq) IsValid() error {
	return nil
}
func (p *AssociateAllowListResp) IsValid() error {
	return nil
}
func (p *DisassociateAllowListReq) IsValid() error {
	return nil
}
func (p *DisassociateAllowListResp) IsValid() error {
	return nil
}

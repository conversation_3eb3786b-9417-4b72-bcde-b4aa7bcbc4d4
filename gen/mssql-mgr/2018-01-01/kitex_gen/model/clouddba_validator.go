// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ListSlowLogRecordsReq) IsValid() error {
	return nil
}
func (p *ListSlowLogRecordsResp) IsValid() error {
	return nil
}
func (p *ListAuditLogRecordsReq) IsValid() error {
	return nil
}
func (p *ListAuditLogRecordsResp) IsValid() error {
	return nil
}
func (p *ListErrorLogRecordsReq) IsValid() error {
	return nil
}
func (p *ListErrorLogRecordsResp) IsValid() error {
	return nil
}

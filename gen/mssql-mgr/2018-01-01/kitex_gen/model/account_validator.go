// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateAccountReq) IsValid() error {
	return nil
}
func (p *DeleteAccount) IsValid() error {
	return nil
}
func (p *GrantAccountPrivilegeReq) IsValid() error {
	return nil
}
func (p *GrantAccountPrivilegesReq) IsValid() error {
	return nil
}
func (p *RevokeAccountPrivilegeReq) IsValid() error {
	return nil
}
func (p *ResetAccountPasswordReq) IsValid() error {
	return nil
}
func (p *ListAccountsReq) IsValid() error {
	return nil
}
func (p *ListAccountsResp) IsValid() error {
	return nil
}
func (p *ListAccountDBPrivilegeReq) IsValid() error {
	return nil
}
func (p *ListAccountDBPrivilegeResp) IsValid() error {
	return nil
}

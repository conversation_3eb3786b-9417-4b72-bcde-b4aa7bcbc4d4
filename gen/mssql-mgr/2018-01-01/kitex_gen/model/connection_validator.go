// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *AllocateDBInstancePublicConnectionReq) IsValid() error {
	return nil
}
func (p *AllocateDBInstancePublicConnectionResp) IsValid() error {
	return nil
}
func (p *ReleaseDBInstancePublicConnectionReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceConnectionReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceConnectionResp) IsValid() error {
	if p.ConnectionInfo != nil {
		if err := p.ConnectionInfo.IsValid(); err != nil {
			return fmt.Errorf("field ConnectionInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *AllocateReadWriteSplittingReq) IsValid() error {
	return nil
}
func (p *ModifyReadOnlyConfigReq) IsValid() error {
	return nil
}
func (p *ModifyDBProxyConfigReq) IsValid() error {
	return nil
}
func (p *DescribeDBProxyConfigReq) IsValid() error {
	return nil
}
func (p *DescribeDBProxyConfigResp) IsValid() error {
	return nil
}
func (p *ModifyDBProxyInstanceReq) IsValid() error {
	return nil
}
func (p *ModifyDBProxyInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeDBProxyInstanceReq) IsValid() error {
	return nil
}
func (p *DescribeDBProxyInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeDBProxyRecommendReq) IsValid() error {
	return nil
}
func (p *DescribeDBProxyRecommendResp) IsValid() error {
	return nil
}
func (p *AllocateInstancePublicServiceNetworkReq) IsValid() error {
	return nil
}
func (p *AllocateInstancePublicServiceNetworkResp) IsValid() error {
	return nil
}
func (p *ReleaseInstancePublicServiceNetworkReq) IsValid() error {
	return nil
}
func (p *CreateCrossInstanceSinglePLBReq) IsValid() error {
	return nil
}
func (p *CreateCrossInstanceSinglePLBResp) IsValid() error {
	return nil
}
func (p *DeleteCrossInstanceSinglePLBReq) IsValid() error {
	return nil
}
func (p *ListCrossInstanceSinglePLBReq) IsValid() error {
	return nil
}
func (p *ListCrossInstanceSinglePLBResp) IsValid() error {
	return nil
}
func (p *AllocateProxyRouteAddressReq) IsValid() error {
	if p.RouteAddress != nil {
		if err := p.RouteAddress.IsValid(); err != nil {
			return fmt.Errorf("field RouteAddress not valid, %w", err)
		}
	}
	return nil
}
func (p *ListProxyRouteAddressReq) IsValid() error {
	return nil
}
func (p *ListProxyRouteAddressResp) IsValid() error {
	return nil
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ListSlowLogRecordsReq struct {
	Context        string          `thrift:"Context,1,required" frugal:"1,required,string" json:"Context"`
	Limit          int32           `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId     string          `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime      int64           `thrift:"StartTime,4,required" frugal:"4,required,i64" validate:"required"`
	EndTime        int64           `thrift:"EndTime,5,required" frugal:"5,required,i64" validate:"required"`
	DBName         *string         `thrift:"DBName,6,optional" frugal:"6,optional,string" json:"DBName,omitempty"`
	QueryOrderKey  *QueryKeyType   `thrift:"QueryOrderKey,7,optional" frugal:"7,optional,QueryKeyType" json:"QueryOrderKey,omitempty"`
	QueryOrderType *QueryOrderType `thrift:"QueryOrderType,8,optional" frugal:"8,optional,QueryOrderType" json:"QueryOrderType,omitempty"`
	NodeName       []string        `thrift:"NodeName,9,optional" frugal:"9,optional,list<string>" json:"NodeName,omitempty"`
}

func NewListSlowLogRecordsReq() *ListSlowLogRecordsReq {
	return &ListSlowLogRecordsReq{}
}

func (p *ListSlowLogRecordsReq) InitDefault() {
}

func (p *ListSlowLogRecordsReq) GetContext() (v string) {
	return p.Context
}

func (p *ListSlowLogRecordsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListSlowLogRecordsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListSlowLogRecordsReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *ListSlowLogRecordsReq) GetEndTime() (v int64) {
	return p.EndTime
}

var ListSlowLogRecordsReq_DBName_DEFAULT string

func (p *ListSlowLogRecordsReq) GetDBName() (v string) {
	if !p.IsSetDBName() {
		return ListSlowLogRecordsReq_DBName_DEFAULT
	}
	return *p.DBName
}

var ListSlowLogRecordsReq_QueryOrderKey_DEFAULT QueryKeyType

func (p *ListSlowLogRecordsReq) GetQueryOrderKey() (v QueryKeyType) {
	if !p.IsSetQueryOrderKey() {
		return ListSlowLogRecordsReq_QueryOrderKey_DEFAULT
	}
	return *p.QueryOrderKey
}

var ListSlowLogRecordsReq_QueryOrderType_DEFAULT QueryOrderType

func (p *ListSlowLogRecordsReq) GetQueryOrderType() (v QueryOrderType) {
	if !p.IsSetQueryOrderType() {
		return ListSlowLogRecordsReq_QueryOrderType_DEFAULT
	}
	return *p.QueryOrderType
}

var ListSlowLogRecordsReq_NodeName_DEFAULT []string

func (p *ListSlowLogRecordsReq) GetNodeName() (v []string) {
	if !p.IsSetNodeName() {
		return ListSlowLogRecordsReq_NodeName_DEFAULT
	}
	return p.NodeName
}
func (p *ListSlowLogRecordsReq) SetContext(val string) {
	p.Context = val
}
func (p *ListSlowLogRecordsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListSlowLogRecordsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListSlowLogRecordsReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *ListSlowLogRecordsReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *ListSlowLogRecordsReq) SetDBName(val *string) {
	p.DBName = val
}
func (p *ListSlowLogRecordsReq) SetQueryOrderKey(val *QueryKeyType) {
	p.QueryOrderKey = val
}
func (p *ListSlowLogRecordsReq) SetQueryOrderType(val *QueryOrderType) {
	p.QueryOrderType = val
}
func (p *ListSlowLogRecordsReq) SetNodeName(val []string) {
	p.NodeName = val
}

var fieldIDToName_ListSlowLogRecordsReq = map[int16]string{
	1: "Context",
	2: "Limit",
	3: "InstanceId",
	4: "StartTime",
	5: "EndTime",
	6: "DBName",
	7: "QueryOrderKey",
	8: "QueryOrderType",
	9: "NodeName",
}

func (p *ListSlowLogRecordsReq) IsSetDBName() bool {
	return p.DBName != nil
}

func (p *ListSlowLogRecordsReq) IsSetQueryOrderKey() bool {
	return p.QueryOrderKey != nil
}

func (p *ListSlowLogRecordsReq) IsSetQueryOrderType() bool {
	return p.QueryOrderType != nil
}

func (p *ListSlowLogRecordsReq) IsSetNodeName() bool {
	return p.NodeName != nil
}

func (p *ListSlowLogRecordsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowLogRecordsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContext bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContext {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowLogRecordsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowLogRecordsReq[fieldId]))
}

func (p *ListSlowLogRecordsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBName = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *QueryKeyType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryKeyType(v)
		_field = &tmp
	}
	p.QueryOrderKey = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *QueryOrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryOrderType(v)
		_field = &tmp
	}
	p.QueryOrderType = _field
	return nil
}
func (p *ListSlowLogRecordsReq) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeName = _field
	return nil
}

func (p *ListSlowLogRecordsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowLogRecordsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowLogRecordsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBName() {
		if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryOrderKey() {
		if err = oprot.WriteFieldBegin("QueryOrderKey", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryOrderKey)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryOrderType() {
		if err = oprot.WriteFieldBegin("QueryOrderType", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryOrderType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeName() {
		if err = oprot.WriteFieldBegin("NodeName", thrift.LIST, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeName)); err != nil {
			return err
		}
		for _, v := range p.NodeName {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListSlowLogRecordsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowLogRecordsReq(%+v)", *p)

}

func (p *ListSlowLogRecordsReq) DeepEqual(ano *ListSlowLogRecordsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Context) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field7DeepEqual(ano.QueryOrderKey) {
		return false
	}
	if !p.Field8DeepEqual(ano.QueryOrderType) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeName) {
		return false
	}
	return true
}

func (p *ListSlowLogRecordsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field4DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field5DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field6DeepEqual(src *string) bool {

	if p.DBName == src {
		return true
	} else if p.DBName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBName, *src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field7DeepEqual(src *QueryKeyType) bool {

	if p.QueryOrderKey == src {
		return true
	} else if p.QueryOrderKey == nil || src == nil {
		return false
	}
	if *p.QueryOrderKey != *src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field8DeepEqual(src *QueryOrderType) bool {

	if p.QueryOrderType == src {
		return true
	} else if p.QueryOrderType == nil || src == nil {
		return false
	}
	if *p.QueryOrderType != *src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsReq) Field9DeepEqual(src []string) bool {

	if len(p.NodeName) != len(src) {
		return false
	}
	for i, v := range p.NodeName {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ListSlowLogRecordsResp struct {
	Total   int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Context string           `thrift:"Context,2,required" frugal:"2,required,string" json:"Context"`
	Datas   []*SlowLogRecord `thrift:"Datas,3,required" frugal:"3,required,list<SlowLogRecord>" json:"Datas"`
}

func NewListSlowLogRecordsResp() *ListSlowLogRecordsResp {
	return &ListSlowLogRecordsResp{}
}

func (p *ListSlowLogRecordsResp) InitDefault() {
}

func (p *ListSlowLogRecordsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListSlowLogRecordsResp) GetContext() (v string) {
	return p.Context
}

func (p *ListSlowLogRecordsResp) GetDatas() (v []*SlowLogRecord) {
	return p.Datas
}
func (p *ListSlowLogRecordsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListSlowLogRecordsResp) SetContext(val string) {
	p.Context = val
}
func (p *ListSlowLogRecordsResp) SetDatas(val []*SlowLogRecord) {
	p.Datas = val
}

var fieldIDToName_ListSlowLogRecordsResp = map[int16]string{
	1: "Total",
	2: "Context",
	3: "Datas",
}

func (p *ListSlowLogRecordsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowLogRecordsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetContext bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContext {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowLogRecordsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowLogRecordsResp[fieldId]))
}

func (p *ListSlowLogRecordsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListSlowLogRecordsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListSlowLogRecordsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowLogRecord, 0, size)
	values := make([]SlowLogRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListSlowLogRecordsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowLogRecordsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowLogRecordsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowLogRecordsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowLogRecordsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowLogRecordsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowLogRecordsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowLogRecordsResp(%+v)", *p)

}

func (p *ListSlowLogRecordsResp) DeepEqual(ano *ListSlowLogRecordsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Context) {
		return false
	}
	if !p.Field3DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListSlowLogRecordsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowLogRecordsResp) Field3DeepEqual(src []*SlowLogRecord) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListAuditLogRecordsReq struct {
	Context        string          `thrift:"Context,1,required" frugal:"1,required,string" json:"Context"`
	Limit          int32           `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId     string          `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime      int64           `thrift:"StartTime,4,required" frugal:"4,required,i64" validate:"required"`
	EndTime        int64           `thrift:"EndTime,5,required" frugal:"5,required,i64" validate:"required"`
	DBName         *string         `thrift:"DBName,6,optional" frugal:"6,optional,string" json:"DBName,omitempty"`
	HostAddress    *string         `thrift:"HostAddress,7,optional" frugal:"7,optional,string" json:"HostAddress,omitempty"`
	AccountName    *string         `thrift:"AccountName,8,optional" frugal:"8,optional,string" json:"AccountName,omitempty"`
	ThreadID       *string         `thrift:"ThreadID,9,optional" frugal:"9,optional,string" json:"ThreadID,omitempty"`
	OperationType  *string         `thrift:"OperationType,10,optional" frugal:"10,optional,string" json:"OperationType,omitempty"`
	ExecuteStatus  []ExecuteStatus `thrift:"ExecuteStatus,11,optional" frugal:"11,optional,list<ExecuteStatus>" json:"ExecuteStatus,omitempty"`
	SQLKeywords    *string         `thrift:"SQLKeywords,12,optional" frugal:"12,optional,string" json:"SQLKeywords,omitempty"`
	QueryKeywords  *string         `thrift:"QueryKeywords,13,optional" frugal:"13,optional,string" json:"QueryKeywords,omitempty"`
	QueryLinkType  *QueryLinkType  `thrift:"QueryLinkType,14,optional" frugal:"14,optional,QueryLinkType" json:"QueryLinkType,omitempty"`
	QueryOrderKey  *QueryKeyType   `thrift:"QueryOrderKey,15,optional" frugal:"15,optional,QueryKeyType" json:"QueryOrderKey,omitempty"`
	QueryOrderType *QueryOrderType `thrift:"QueryOrderType,16,optional" frugal:"16,optional,QueryOrderType" json:"QueryOrderType,omitempty"`
	NodeName       []string        `thrift:"NodeName,17,optional" frugal:"17,optional,list<string>" json:"NodeName,omitempty"`
}

func NewListAuditLogRecordsReq() *ListAuditLogRecordsReq {
	return &ListAuditLogRecordsReq{}
}

func (p *ListAuditLogRecordsReq) InitDefault() {
}

func (p *ListAuditLogRecordsReq) GetContext() (v string) {
	return p.Context
}

func (p *ListAuditLogRecordsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListAuditLogRecordsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListAuditLogRecordsReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *ListAuditLogRecordsReq) GetEndTime() (v int64) {
	return p.EndTime
}

var ListAuditLogRecordsReq_DBName_DEFAULT string

func (p *ListAuditLogRecordsReq) GetDBName() (v string) {
	if !p.IsSetDBName() {
		return ListAuditLogRecordsReq_DBName_DEFAULT
	}
	return *p.DBName
}

var ListAuditLogRecordsReq_HostAddress_DEFAULT string

func (p *ListAuditLogRecordsReq) GetHostAddress() (v string) {
	if !p.IsSetHostAddress() {
		return ListAuditLogRecordsReq_HostAddress_DEFAULT
	}
	return *p.HostAddress
}

var ListAuditLogRecordsReq_AccountName_DEFAULT string

func (p *ListAuditLogRecordsReq) GetAccountName() (v string) {
	if !p.IsSetAccountName() {
		return ListAuditLogRecordsReq_AccountName_DEFAULT
	}
	return *p.AccountName
}

var ListAuditLogRecordsReq_ThreadID_DEFAULT string

func (p *ListAuditLogRecordsReq) GetThreadID() (v string) {
	if !p.IsSetThreadID() {
		return ListAuditLogRecordsReq_ThreadID_DEFAULT
	}
	return *p.ThreadID
}

var ListAuditLogRecordsReq_OperationType_DEFAULT string

func (p *ListAuditLogRecordsReq) GetOperationType() (v string) {
	if !p.IsSetOperationType() {
		return ListAuditLogRecordsReq_OperationType_DEFAULT
	}
	return *p.OperationType
}

var ListAuditLogRecordsReq_ExecuteStatus_DEFAULT []ExecuteStatus

func (p *ListAuditLogRecordsReq) GetExecuteStatus() (v []ExecuteStatus) {
	if !p.IsSetExecuteStatus() {
		return ListAuditLogRecordsReq_ExecuteStatus_DEFAULT
	}
	return p.ExecuteStatus
}

var ListAuditLogRecordsReq_SQLKeywords_DEFAULT string

func (p *ListAuditLogRecordsReq) GetSQLKeywords() (v string) {
	if !p.IsSetSQLKeywords() {
		return ListAuditLogRecordsReq_SQLKeywords_DEFAULT
	}
	return *p.SQLKeywords
}

var ListAuditLogRecordsReq_QueryKeywords_DEFAULT string

func (p *ListAuditLogRecordsReq) GetQueryKeywords() (v string) {
	if !p.IsSetQueryKeywords() {
		return ListAuditLogRecordsReq_QueryKeywords_DEFAULT
	}
	return *p.QueryKeywords
}

var ListAuditLogRecordsReq_QueryLinkType_DEFAULT QueryLinkType

func (p *ListAuditLogRecordsReq) GetQueryLinkType() (v QueryLinkType) {
	if !p.IsSetQueryLinkType() {
		return ListAuditLogRecordsReq_QueryLinkType_DEFAULT
	}
	return *p.QueryLinkType
}

var ListAuditLogRecordsReq_QueryOrderKey_DEFAULT QueryKeyType

func (p *ListAuditLogRecordsReq) GetQueryOrderKey() (v QueryKeyType) {
	if !p.IsSetQueryOrderKey() {
		return ListAuditLogRecordsReq_QueryOrderKey_DEFAULT
	}
	return *p.QueryOrderKey
}

var ListAuditLogRecordsReq_QueryOrderType_DEFAULT QueryOrderType

func (p *ListAuditLogRecordsReq) GetQueryOrderType() (v QueryOrderType) {
	if !p.IsSetQueryOrderType() {
		return ListAuditLogRecordsReq_QueryOrderType_DEFAULT
	}
	return *p.QueryOrderType
}

var ListAuditLogRecordsReq_NodeName_DEFAULT []string

func (p *ListAuditLogRecordsReq) GetNodeName() (v []string) {
	if !p.IsSetNodeName() {
		return ListAuditLogRecordsReq_NodeName_DEFAULT
	}
	return p.NodeName
}
func (p *ListAuditLogRecordsReq) SetContext(val string) {
	p.Context = val
}
func (p *ListAuditLogRecordsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListAuditLogRecordsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListAuditLogRecordsReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *ListAuditLogRecordsReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *ListAuditLogRecordsReq) SetDBName(val *string) {
	p.DBName = val
}
func (p *ListAuditLogRecordsReq) SetHostAddress(val *string) {
	p.HostAddress = val
}
func (p *ListAuditLogRecordsReq) SetAccountName(val *string) {
	p.AccountName = val
}
func (p *ListAuditLogRecordsReq) SetThreadID(val *string) {
	p.ThreadID = val
}
func (p *ListAuditLogRecordsReq) SetOperationType(val *string) {
	p.OperationType = val
}
func (p *ListAuditLogRecordsReq) SetExecuteStatus(val []ExecuteStatus) {
	p.ExecuteStatus = val
}
func (p *ListAuditLogRecordsReq) SetSQLKeywords(val *string) {
	p.SQLKeywords = val
}
func (p *ListAuditLogRecordsReq) SetQueryKeywords(val *string) {
	p.QueryKeywords = val
}
func (p *ListAuditLogRecordsReq) SetQueryLinkType(val *QueryLinkType) {
	p.QueryLinkType = val
}
func (p *ListAuditLogRecordsReq) SetQueryOrderKey(val *QueryKeyType) {
	p.QueryOrderKey = val
}
func (p *ListAuditLogRecordsReq) SetQueryOrderType(val *QueryOrderType) {
	p.QueryOrderType = val
}
func (p *ListAuditLogRecordsReq) SetNodeName(val []string) {
	p.NodeName = val
}

var fieldIDToName_ListAuditLogRecordsReq = map[int16]string{
	1:  "Context",
	2:  "Limit",
	3:  "InstanceId",
	4:  "StartTime",
	5:  "EndTime",
	6:  "DBName",
	7:  "HostAddress",
	8:  "AccountName",
	9:  "ThreadID",
	10: "OperationType",
	11: "ExecuteStatus",
	12: "SQLKeywords",
	13: "QueryKeywords",
	14: "QueryLinkType",
	15: "QueryOrderKey",
	16: "QueryOrderType",
	17: "NodeName",
}

func (p *ListAuditLogRecordsReq) IsSetDBName() bool {
	return p.DBName != nil
}

func (p *ListAuditLogRecordsReq) IsSetHostAddress() bool {
	return p.HostAddress != nil
}

func (p *ListAuditLogRecordsReq) IsSetAccountName() bool {
	return p.AccountName != nil
}

func (p *ListAuditLogRecordsReq) IsSetThreadID() bool {
	return p.ThreadID != nil
}

func (p *ListAuditLogRecordsReq) IsSetOperationType() bool {
	return p.OperationType != nil
}

func (p *ListAuditLogRecordsReq) IsSetExecuteStatus() bool {
	return p.ExecuteStatus != nil
}

func (p *ListAuditLogRecordsReq) IsSetSQLKeywords() bool {
	return p.SQLKeywords != nil
}

func (p *ListAuditLogRecordsReq) IsSetQueryKeywords() bool {
	return p.QueryKeywords != nil
}

func (p *ListAuditLogRecordsReq) IsSetQueryLinkType() bool {
	return p.QueryLinkType != nil
}

func (p *ListAuditLogRecordsReq) IsSetQueryOrderKey() bool {
	return p.QueryOrderKey != nil
}

func (p *ListAuditLogRecordsReq) IsSetQueryOrderType() bool {
	return p.QueryOrderType != nil
}

func (p *ListAuditLogRecordsReq) IsSetNodeName() bool {
	return p.NodeName != nil
}

func (p *ListAuditLogRecordsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAuditLogRecordsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContext bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContext {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAuditLogRecordsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAuditLogRecordsReq[fieldId]))
}

func (p *ListAuditLogRecordsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBName = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HostAddress = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountName = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThreadID = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OperationType = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]ExecuteStatus, 0, size)
	for i := 0; i < size; i++ {

		var _elem ExecuteStatus
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = ExecuteStatus(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExecuteStatus = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SQLKeywords = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryKeywords = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *QueryLinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryLinkType(v)
		_field = &tmp
	}
	p.QueryLinkType = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *QueryKeyType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryKeyType(v)
		_field = &tmp
	}
	p.QueryOrderKey = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *QueryOrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryOrderType(v)
		_field = &tmp
	}
	p.QueryOrderType = _field
	return nil
}
func (p *ListAuditLogRecordsReq) ReadField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeName = _field
	return nil
}

func (p *ListAuditLogRecordsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAuditLogRecordsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAuditLogRecordsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBName() {
		if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetHostAddress() {
		if err = oprot.WriteFieldBegin("HostAddress", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.HostAddress); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountName() {
		if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetThreadID() {
		if err = oprot.WriteFieldBegin("ThreadID", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThreadID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetOperationType() {
		if err = oprot.WriteFieldBegin("OperationType", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OperationType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteStatus() {
		if err = oprot.WriteFieldBegin("ExecuteStatus", thrift.LIST, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExecuteStatus)); err != nil {
			return err
		}
		for _, v := range p.ExecuteStatus {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetSQLKeywords() {
		if err = oprot.WriteFieldBegin("SQLKeywords", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SQLKeywords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryKeywords() {
		if err = oprot.WriteFieldBegin("QueryKeywords", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryKeywords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryLinkType() {
		if err = oprot.WriteFieldBegin("QueryLinkType", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryLinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryOrderKey() {
		if err = oprot.WriteFieldBegin("QueryOrderKey", thrift.I32, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryOrderKey)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryOrderType() {
		if err = oprot.WriteFieldBegin("QueryOrderType", thrift.I32, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryOrderType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeName() {
		if err = oprot.WriteFieldBegin("NodeName", thrift.LIST, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeName)); err != nil {
			return err
		}
		for _, v := range p.NodeName {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *ListAuditLogRecordsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAuditLogRecordsReq(%+v)", *p)

}

func (p *ListAuditLogRecordsReq) DeepEqual(ano *ListAuditLogRecordsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Context) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field7DeepEqual(ano.HostAddress) {
		return false
	}
	if !p.Field8DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field9DeepEqual(ano.ThreadID) {
		return false
	}
	if !p.Field10DeepEqual(ano.OperationType) {
		return false
	}
	if !p.Field11DeepEqual(ano.ExecuteStatus) {
		return false
	}
	if !p.Field12DeepEqual(ano.SQLKeywords) {
		return false
	}
	if !p.Field13DeepEqual(ano.QueryKeywords) {
		return false
	}
	if !p.Field14DeepEqual(ano.QueryLinkType) {
		return false
	}
	if !p.Field15DeepEqual(ano.QueryOrderKey) {
		return false
	}
	if !p.Field16DeepEqual(ano.QueryOrderType) {
		return false
	}
	if !p.Field17DeepEqual(ano.NodeName) {
		return false
	}
	return true
}

func (p *ListAuditLogRecordsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field4DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field5DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field6DeepEqual(src *string) bool {

	if p.DBName == src {
		return true
	} else if p.DBName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBName, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field7DeepEqual(src *string) bool {

	if p.HostAddress == src {
		return true
	} else if p.HostAddress == nil || src == nil {
		return false
	}
	if strings.Compare(*p.HostAddress, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field8DeepEqual(src *string) bool {

	if p.AccountName == src {
		return true
	} else if p.AccountName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountName, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field9DeepEqual(src *string) bool {

	if p.ThreadID == src {
		return true
	} else if p.ThreadID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThreadID, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field10DeepEqual(src *string) bool {

	if p.OperationType == src {
		return true
	} else if p.OperationType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OperationType, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field11DeepEqual(src []ExecuteStatus) bool {

	if len(p.ExecuteStatus) != len(src) {
		return false
	}
	for i, v := range p.ExecuteStatus {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field12DeepEqual(src *string) bool {

	if p.SQLKeywords == src {
		return true
	} else if p.SQLKeywords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SQLKeywords, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field13DeepEqual(src *string) bool {

	if p.QueryKeywords == src {
		return true
	} else if p.QueryKeywords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryKeywords, *src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field14DeepEqual(src *QueryLinkType) bool {

	if p.QueryLinkType == src {
		return true
	} else if p.QueryLinkType == nil || src == nil {
		return false
	}
	if *p.QueryLinkType != *src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field15DeepEqual(src *QueryKeyType) bool {

	if p.QueryOrderKey == src {
		return true
	} else if p.QueryOrderKey == nil || src == nil {
		return false
	}
	if *p.QueryOrderKey != *src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field16DeepEqual(src *QueryOrderType) bool {

	if p.QueryOrderType == src {
		return true
	} else if p.QueryOrderType == nil || src == nil {
		return false
	}
	if *p.QueryOrderType != *src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsReq) Field17DeepEqual(src []string) bool {

	if len(p.NodeName) != len(src) {
		return false
	}
	for i, v := range p.NodeName {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ListAuditLogRecordsResp struct {
	Total   int32             `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Context string            `thrift:"Context,2,required" frugal:"2,required,string" json:"Context"`
	Datas   []*AuditLogRecord `thrift:"Datas,3,required" frugal:"3,required,list<AuditLogRecord>" json:"Datas"`
}

func NewListAuditLogRecordsResp() *ListAuditLogRecordsResp {
	return &ListAuditLogRecordsResp{}
}

func (p *ListAuditLogRecordsResp) InitDefault() {
}

func (p *ListAuditLogRecordsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListAuditLogRecordsResp) GetContext() (v string) {
	return p.Context
}

func (p *ListAuditLogRecordsResp) GetDatas() (v []*AuditLogRecord) {
	return p.Datas
}
func (p *ListAuditLogRecordsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListAuditLogRecordsResp) SetContext(val string) {
	p.Context = val
}
func (p *ListAuditLogRecordsResp) SetDatas(val []*AuditLogRecord) {
	p.Datas = val
}

var fieldIDToName_ListAuditLogRecordsResp = map[int16]string{
	1: "Total",
	2: "Context",
	3: "Datas",
}

func (p *ListAuditLogRecordsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAuditLogRecordsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetContext bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContext {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListAuditLogRecordsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListAuditLogRecordsResp[fieldId]))
}

func (p *ListAuditLogRecordsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListAuditLogRecordsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListAuditLogRecordsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AuditLogRecord, 0, size)
	values := make([]AuditLogRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListAuditLogRecordsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListAuditLogRecordsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListAuditLogRecordsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListAuditLogRecordsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListAuditLogRecordsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListAuditLogRecordsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListAuditLogRecordsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAuditLogRecordsResp(%+v)", *p)

}

func (p *ListAuditLogRecordsResp) DeepEqual(ano *ListAuditLogRecordsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Context) {
		return false
	}
	if !p.Field3DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListAuditLogRecordsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListAuditLogRecordsResp) Field3DeepEqual(src []*AuditLogRecord) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListErrorLogRecordsReq struct {
	Context        string           `thrift:"Context,1,required" frugal:"1,required,string" json:"Context"`
	Limit          int32            `thrift:"Limit,2,required" frugal:"2,required,i32" json:"Limit"`
	InstanceId     string           `thrift:"InstanceId,3,required" frugal:"3,required,string" validate:"required"`
	StartTime      int64            `thrift:"StartTime,4,required" frugal:"4,required,i64" validate:"required"`
	EndTime        int64            `thrift:"EndTime,5,required" frugal:"5,required,i64" validate:"required"`
	VerbosityLevel []VerbosityLevel `thrift:"VerbosityLevel,6,optional" frugal:"6,optional,list<VerbosityLevel>" json:"VerbosityLevel,omitempty"`
	Message        *string          `thrift:"Message,7,optional" frugal:"7,optional,string" json:"Message,omitempty"`
	NodeName       []string         `thrift:"NodeName,8,optional" frugal:"8,optional,list<string>" json:"NodeName,omitempty"`
}

func NewListErrorLogRecordsReq() *ListErrorLogRecordsReq {
	return &ListErrorLogRecordsReq{}
}

func (p *ListErrorLogRecordsReq) InitDefault() {
}

func (p *ListErrorLogRecordsReq) GetContext() (v string) {
	return p.Context
}

func (p *ListErrorLogRecordsReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *ListErrorLogRecordsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListErrorLogRecordsReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *ListErrorLogRecordsReq) GetEndTime() (v int64) {
	return p.EndTime
}

var ListErrorLogRecordsReq_VerbosityLevel_DEFAULT []VerbosityLevel

func (p *ListErrorLogRecordsReq) GetVerbosityLevel() (v []VerbosityLevel) {
	if !p.IsSetVerbosityLevel() {
		return ListErrorLogRecordsReq_VerbosityLevel_DEFAULT
	}
	return p.VerbosityLevel
}

var ListErrorLogRecordsReq_Message_DEFAULT string

func (p *ListErrorLogRecordsReq) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return ListErrorLogRecordsReq_Message_DEFAULT
	}
	return *p.Message
}

var ListErrorLogRecordsReq_NodeName_DEFAULT []string

func (p *ListErrorLogRecordsReq) GetNodeName() (v []string) {
	if !p.IsSetNodeName() {
		return ListErrorLogRecordsReq_NodeName_DEFAULT
	}
	return p.NodeName
}
func (p *ListErrorLogRecordsReq) SetContext(val string) {
	p.Context = val
}
func (p *ListErrorLogRecordsReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *ListErrorLogRecordsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListErrorLogRecordsReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *ListErrorLogRecordsReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *ListErrorLogRecordsReq) SetVerbosityLevel(val []VerbosityLevel) {
	p.VerbosityLevel = val
}
func (p *ListErrorLogRecordsReq) SetMessage(val *string) {
	p.Message = val
}
func (p *ListErrorLogRecordsReq) SetNodeName(val []string) {
	p.NodeName = val
}

var fieldIDToName_ListErrorLogRecordsReq = map[int16]string{
	1: "Context",
	2: "Limit",
	3: "InstanceId",
	4: "StartTime",
	5: "EndTime",
	6: "VerbosityLevel",
	7: "Message",
	8: "NodeName",
}

func (p *ListErrorLogRecordsReq) IsSetVerbosityLevel() bool {
	return p.VerbosityLevel != nil
}

func (p *ListErrorLogRecordsReq) IsSetMessage() bool {
	return p.Message != nil
}

func (p *ListErrorLogRecordsReq) IsSetNodeName() bool {
	return p.NodeName != nil
}

func (p *ListErrorLogRecordsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListErrorLogRecordsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContext bool = false
	var issetLimit bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContext {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListErrorLogRecordsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListErrorLogRecordsReq[fieldId]))
}

func (p *ListErrorLogRecordsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]VerbosityLevel, 0, size)
	for i := 0; i < size; i++ {

		var _elem VerbosityLevel
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = VerbosityLevel(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VerbosityLevel = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *ListErrorLogRecordsReq) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeName = _field
	return nil
}

func (p *ListErrorLogRecordsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListErrorLogRecordsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListErrorLogRecordsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetVerbosityLevel() {
		if err = oprot.WriteFieldBegin("VerbosityLevel", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VerbosityLevel)); err != nil {
			return err
		}
		for _, v := range p.VerbosityLevel {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeName() {
		if err = oprot.WriteFieldBegin("NodeName", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeName)); err != nil {
			return err
		}
		for _, v := range p.NodeName {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListErrorLogRecordsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListErrorLogRecordsReq(%+v)", *p)

}

func (p *ListErrorLogRecordsReq) DeepEqual(ano *ListErrorLogRecordsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Context) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.VerbosityLevel) {
		return false
	}
	if !p.Field7DeepEqual(ano.Message) {
		return false
	}
	if !p.Field8DeepEqual(ano.NodeName) {
		return false
	}
	return true
}

func (p *ListErrorLogRecordsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field2DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field4DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field5DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field6DeepEqual(src []VerbosityLevel) bool {

	if len(p.VerbosityLevel) != len(src) {
		return false
	}
	for i, v := range p.VerbosityLevel {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field7DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsReq) Field8DeepEqual(src []string) bool {

	if len(p.NodeName) != len(src) {
		return false
	}
	for i, v := range p.NodeName {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ListErrorLogRecordsResp struct {
	Total   int32             `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Context string            `thrift:"Context,2,required" frugal:"2,required,string" json:"Context"`
	Datas   []*ErrorLogRecord `thrift:"Datas,3,required" frugal:"3,required,list<ErrorLogRecord>" json:"Datas"`
}

func NewListErrorLogRecordsResp() *ListErrorLogRecordsResp {
	return &ListErrorLogRecordsResp{}
}

func (p *ListErrorLogRecordsResp) InitDefault() {
}

func (p *ListErrorLogRecordsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListErrorLogRecordsResp) GetContext() (v string) {
	return p.Context
}

func (p *ListErrorLogRecordsResp) GetDatas() (v []*ErrorLogRecord) {
	return p.Datas
}
func (p *ListErrorLogRecordsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListErrorLogRecordsResp) SetContext(val string) {
	p.Context = val
}
func (p *ListErrorLogRecordsResp) SetDatas(val []*ErrorLogRecord) {
	p.Datas = val
}

var fieldIDToName_ListErrorLogRecordsResp = map[int16]string{
	1: "Total",
	2: "Context",
	3: "Datas",
}

func (p *ListErrorLogRecordsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListErrorLogRecordsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetContext bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContext {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListErrorLogRecordsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListErrorLogRecordsResp[fieldId]))
}

func (p *ListErrorLogRecordsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListErrorLogRecordsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *ListErrorLogRecordsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ErrorLogRecord, 0, size)
	values := make([]ErrorLogRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListErrorLogRecordsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListErrorLogRecordsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListErrorLogRecordsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListErrorLogRecordsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListErrorLogRecordsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListErrorLogRecordsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListErrorLogRecordsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListErrorLogRecordsResp(%+v)", *p)

}

func (p *ListErrorLogRecordsResp) DeepEqual(ano *ListErrorLogRecordsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Context) {
		return false
	}
	if !p.Field3DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListErrorLogRecordsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *ListErrorLogRecordsResp) Field3DeepEqual(src []*ErrorLogRecord) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

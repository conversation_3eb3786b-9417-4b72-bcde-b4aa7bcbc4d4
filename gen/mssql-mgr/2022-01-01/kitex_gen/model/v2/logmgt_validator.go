// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DescribeEventsReq) IsValid() error {
	return nil
}
func (p *DescribeEventsResp) IsValid() error {
	return nil
}
func (p *DescribeFailoverLogsReq) IsValid() error {
	return nil
}
func (p *DescribeFailoverLogsResp) IsValid() error {
	return nil
}
func (p *DescribeErrorLogsReq) IsValid() error {
	return nil
}
func (p *DescribeErrorLogsResp) IsValid() error {
	return nil
}
func (p *DescribeAuditLogsReq) IsValid() error {
	return nil
}
func (p *DescribeAuditLogsResp) IsValid() error {
	return nil
}
func (p *DescribeSlowLogsReq) IsValid() error {
	return nil
}
func (p *DescribeSlowLogsResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSlowQueryTimeReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSlowQueryTimeResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSlowQueryEnableReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSlowQueryEnableResp) IsValid() error {
	return nil
}

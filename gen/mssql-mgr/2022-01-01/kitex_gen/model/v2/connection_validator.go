// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ModifyDBEndpointAddressReq) IsValid() error {
	return nil
}
func (p *ModifyDBEndpointAddressResp) IsValid() error {
	return nil
}
func (p *ModifyDBEndpointReq) IsValid() error {
	return nil
}
func (p *CreateDBEndpointPublicAddressReq) IsValid() error {
	return nil
}
func (p *CreateDBEndpointPublicAddressResp) IsValid() error {
	return nil
}
func (p *DeleteDBEndpointPublicAddressReq) IsValid() error {
	return nil
}
func (p *DeleteDBEndpointPublicAddressResp) IsValid() error {
	return nil
}
func (p *CreateDBEndpointInnerAddressReq) IsValid() error {
	return nil
}
func (p *DeleteDBEndpointInnerAddressReq) IsValid() error {
	return nil
}
func (p *CreateDBEndpointReq) IsValid() error {
	return nil
}
func (p *CreateDBEndpointResp) IsValid() error {
	return nil
}
func (p *DeleteDBEndpointReq) IsValid() error {
	return nil
}
func (p *DeleteDBEndpointResp) IsValid() error {
	return nil
}
func (p *ModifyDBEndpointPrivateDNSReq) IsValid() error {
	return nil
}
func (p *ModifyDBEndpointPrivateDNSResp) IsValid() error {
	return nil
}

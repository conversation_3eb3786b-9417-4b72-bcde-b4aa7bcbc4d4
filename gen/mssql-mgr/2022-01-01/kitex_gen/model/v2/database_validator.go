// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDatabaseReq) IsValid() error {
	return nil
}
func (p *CreateDatabaseResp) IsValid() error {
	return nil
}
func (p *DeleteDatabaseReq) IsValid() error {
	return nil
}
func (p *DeleteDatabaseResp) IsValid() error {
	return nil
}
func (p *ModifyDatabasePrivilegeReq) IsValid() error {
	return nil
}
func (p *ModifyDatabasePrivilegeResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceDatabasesReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceDatabasesResp) IsValid() error {
	return nil
}
func (p *DescribeRecoverableDatabasesReq) IsValid() error {
	return nil
}
func (p *DescribeRecoverableDatabasesResp) IsValid() error {
	return nil
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ModifyDBEndpointAddressReq struct {
	InstanceId   string      `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId   string      `thrift:"EndpointId,2,required" frugal:"2,required,string" json:"EndpointId"`
	NetworkType  NetworkType `thrift:"NetworkType,3,required" frugal:"3,required,NetworkType" json:"NetworkType"`
	DomainPrefix *string     `thrift:"DomainPrefix,4,optional" frugal:"4,optional,string" json:"DomainPrefix,omitempty"`
}

func NewModifyDBEndpointAddressReq() *ModifyDBEndpointAddressReq {
	return &ModifyDBEndpointAddressReq{}
}

func (p *ModifyDBEndpointAddressReq) InitDefault() {
}

func (p *ModifyDBEndpointAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBEndpointAddressReq) GetEndpointId() (v string) {
	return p.EndpointId
}

func (p *ModifyDBEndpointAddressReq) GetNetworkType() (v NetworkType) {
	return p.NetworkType
}

var ModifyDBEndpointAddressReq_DomainPrefix_DEFAULT string

func (p *ModifyDBEndpointAddressReq) GetDomainPrefix() (v string) {
	if !p.IsSetDomainPrefix() {
		return ModifyDBEndpointAddressReq_DomainPrefix_DEFAULT
	}
	return *p.DomainPrefix
}
func (p *ModifyDBEndpointAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBEndpointAddressReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *ModifyDBEndpointAddressReq) SetNetworkType(val NetworkType) {
	p.NetworkType = val
}
func (p *ModifyDBEndpointAddressReq) SetDomainPrefix(val *string) {
	p.DomainPrefix = val
}

var fieldIDToName_ModifyDBEndpointAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
	3: "NetworkType",
	4: "DomainPrefix",
}

func (p *ModifyDBEndpointAddressReq) IsSetDomainPrefix() bool {
	return p.DomainPrefix != nil
}

func (p *ModifyDBEndpointAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false
	var issetNetworkType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNetworkType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNetworkType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBEndpointAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBEndpointAddressReq[fieldId]))
}

func (p *ModifyDBEndpointAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBEndpointAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *ModifyDBEndpointAddressReq) ReadField3(iprot thrift.TProtocol) error {

	var _field NetworkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = NetworkType(v)
	}
	p.NetworkType = _field
	return nil
}
func (p *ModifyDBEndpointAddressReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DomainPrefix = _field
	return nil
}

func (p *ModifyDBEndpointAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBEndpointAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBEndpointAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBEndpointAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBEndpointAddressReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NetworkType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.NetworkType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBEndpointAddressReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDomainPrefix() {
		if err = oprot.WriteFieldBegin("DomainPrefix", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DomainPrefix); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBEndpointAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBEndpointAddressReq(%+v)", *p)

}

func (p *ModifyDBEndpointAddressReq) DeepEqual(ano *ModifyDBEndpointAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.NetworkType) {
		return false
	}
	if !p.Field4DeepEqual(ano.DomainPrefix) {
		return false
	}
	return true
}

func (p *ModifyDBEndpointAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointAddressReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointAddressReq) Field3DeepEqual(src NetworkType) bool {

	if p.NetworkType != src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointAddressReq) Field4DeepEqual(src *string) bool {

	if p.DomainPrefix == src {
		return true
	} else if p.DomainPrefix == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DomainPrefix, *src) != 0 {
		return false
	}
	return true
}

type ModifyDBEndpointAddressResp struct {
}

func NewModifyDBEndpointAddressResp() *ModifyDBEndpointAddressResp {
	return &ModifyDBEndpointAddressResp{}
}

func (p *ModifyDBEndpointAddressResp) InitDefault() {
}

var fieldIDToName_ModifyDBEndpointAddressResp = map[int16]string{}

func (p *ModifyDBEndpointAddressResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointAddressResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBEndpointAddressResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointAddressResp")

	if err = oprot.WriteStructBegin("ModifyDBEndpointAddressResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBEndpointAddressResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBEndpointAddressResp(%+v)", *p)

}

func (p *ModifyDBEndpointAddressResp) DeepEqual(ano *ModifyDBEndpointAddressResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ModifyDBEndpointReq struct {
	InstanceId                   string              `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId                   string              `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
	ReadWriteSpliting            *bool               `thrift:"ReadWriteSpliting,3,optional" frugal:"3,optional,bool" json:"ReadWriteSpliting,omitempty"`
	GlobalReadOnly               *bool               `thrift:"GlobalReadOnly,4,optional" frugal:"4,optional,bool" json:"GlobalReadOnly,omitempty"`
	EndpointName                 *string             `thrift:"EndpointName,5,optional" frugal:"5,optional,string" json:"EndpointName,omitempty"`
	Description                  *string             `thrift:"Description,6,optional" frugal:"6,optional,string" json:"Description,omitempty"`
	Nodes                        *string             `thrift:"Nodes,7,optional" frugal:"7,optional,string" json:"Nodes,omitempty"`
	AutoAddNewNodes              *bool               `thrift:"AutoAddNewNodes,8,optional" frugal:"8,optional,bool" json:"AutoAddNewNodes,omitempty"`
	ReadOnlyNodeDistributionType *ReadOnlyWeightType `thrift:"ReadOnlyNodeDistributionType,9,optional" frugal:"9,optional,ReadOnlyWeightType" json:"ReadOnlyNodeDistributionType,omitempty"`
	ReadOnlyNodeWeight           []*NodeWeightObject `thrift:"ReadOnlyNodeWeight,10,optional" frugal:"10,optional,list<NodeWeightObject>" json:"ReadOnlyNodeWeight,omitempty"`
	ReadWriteMode                *ReadWriteMode      `thrift:"ReadWriteMode,11,optional" frugal:"11,optional,ReadWriteMode" json:"ReadWriteMode,omitempty"`
	ReadOnlyNodeMaxDelayTime     *int32              `thrift:"ReadOnlyNodeMaxDelayTime,12,optional" frugal:"12,optional,i32" json:"ReadOnlyNodeMaxDelayTime,omitempty"`
}

func NewModifyDBEndpointReq() *ModifyDBEndpointReq {
	return &ModifyDBEndpointReq{}
}

func (p *ModifyDBEndpointReq) InitDefault() {
}

func (p *ModifyDBEndpointReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBEndpointReq) GetEndpointId() (v string) {
	return p.EndpointId
}

var ModifyDBEndpointReq_ReadWriteSpliting_DEFAULT bool

func (p *ModifyDBEndpointReq) GetReadWriteSpliting() (v bool) {
	if !p.IsSetReadWriteSpliting() {
		return ModifyDBEndpointReq_ReadWriteSpliting_DEFAULT
	}
	return *p.ReadWriteSpliting
}

var ModifyDBEndpointReq_GlobalReadOnly_DEFAULT bool

func (p *ModifyDBEndpointReq) GetGlobalReadOnly() (v bool) {
	if !p.IsSetGlobalReadOnly() {
		return ModifyDBEndpointReq_GlobalReadOnly_DEFAULT
	}
	return *p.GlobalReadOnly
}

var ModifyDBEndpointReq_EndpointName_DEFAULT string

func (p *ModifyDBEndpointReq) GetEndpointName() (v string) {
	if !p.IsSetEndpointName() {
		return ModifyDBEndpointReq_EndpointName_DEFAULT
	}
	return *p.EndpointName
}

var ModifyDBEndpointReq_Description_DEFAULT string

func (p *ModifyDBEndpointReq) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return ModifyDBEndpointReq_Description_DEFAULT
	}
	return *p.Description
}

var ModifyDBEndpointReq_Nodes_DEFAULT string

func (p *ModifyDBEndpointReq) GetNodes() (v string) {
	if !p.IsSetNodes() {
		return ModifyDBEndpointReq_Nodes_DEFAULT
	}
	return *p.Nodes
}

var ModifyDBEndpointReq_AutoAddNewNodes_DEFAULT bool

func (p *ModifyDBEndpointReq) GetAutoAddNewNodes() (v bool) {
	if !p.IsSetAutoAddNewNodes() {
		return ModifyDBEndpointReq_AutoAddNewNodes_DEFAULT
	}
	return *p.AutoAddNewNodes
}

var ModifyDBEndpointReq_ReadOnlyNodeDistributionType_DEFAULT ReadOnlyWeightType

func (p *ModifyDBEndpointReq) GetReadOnlyNodeDistributionType() (v ReadOnlyWeightType) {
	if !p.IsSetReadOnlyNodeDistributionType() {
		return ModifyDBEndpointReq_ReadOnlyNodeDistributionType_DEFAULT
	}
	return *p.ReadOnlyNodeDistributionType
}

var ModifyDBEndpointReq_ReadOnlyNodeWeight_DEFAULT []*NodeWeightObject

func (p *ModifyDBEndpointReq) GetReadOnlyNodeWeight() (v []*NodeWeightObject) {
	if !p.IsSetReadOnlyNodeWeight() {
		return ModifyDBEndpointReq_ReadOnlyNodeWeight_DEFAULT
	}
	return p.ReadOnlyNodeWeight
}

var ModifyDBEndpointReq_ReadWriteMode_DEFAULT ReadWriteMode

func (p *ModifyDBEndpointReq) GetReadWriteMode() (v ReadWriteMode) {
	if !p.IsSetReadWriteMode() {
		return ModifyDBEndpointReq_ReadWriteMode_DEFAULT
	}
	return *p.ReadWriteMode
}

var ModifyDBEndpointReq_ReadOnlyNodeMaxDelayTime_DEFAULT int32

func (p *ModifyDBEndpointReq) GetReadOnlyNodeMaxDelayTime() (v int32) {
	if !p.IsSetReadOnlyNodeMaxDelayTime() {
		return ModifyDBEndpointReq_ReadOnlyNodeMaxDelayTime_DEFAULT
	}
	return *p.ReadOnlyNodeMaxDelayTime
}
func (p *ModifyDBEndpointReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBEndpointReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *ModifyDBEndpointReq) SetReadWriteSpliting(val *bool) {
	p.ReadWriteSpliting = val
}
func (p *ModifyDBEndpointReq) SetGlobalReadOnly(val *bool) {
	p.GlobalReadOnly = val
}
func (p *ModifyDBEndpointReq) SetEndpointName(val *string) {
	p.EndpointName = val
}
func (p *ModifyDBEndpointReq) SetDescription(val *string) {
	p.Description = val
}
func (p *ModifyDBEndpointReq) SetNodes(val *string) {
	p.Nodes = val
}
func (p *ModifyDBEndpointReq) SetAutoAddNewNodes(val *bool) {
	p.AutoAddNewNodes = val
}
func (p *ModifyDBEndpointReq) SetReadOnlyNodeDistributionType(val *ReadOnlyWeightType) {
	p.ReadOnlyNodeDistributionType = val
}
func (p *ModifyDBEndpointReq) SetReadOnlyNodeWeight(val []*NodeWeightObject) {
	p.ReadOnlyNodeWeight = val
}
func (p *ModifyDBEndpointReq) SetReadWriteMode(val *ReadWriteMode) {
	p.ReadWriteMode = val
}
func (p *ModifyDBEndpointReq) SetReadOnlyNodeMaxDelayTime(val *int32) {
	p.ReadOnlyNodeMaxDelayTime = val
}

var fieldIDToName_ModifyDBEndpointReq = map[int16]string{
	1:  "InstanceId",
	2:  "EndpointId",
	3:  "ReadWriteSpliting",
	4:  "GlobalReadOnly",
	5:  "EndpointName",
	6:  "Description",
	7:  "Nodes",
	8:  "AutoAddNewNodes",
	9:  "ReadOnlyNodeDistributionType",
	10: "ReadOnlyNodeWeight",
	11: "ReadWriteMode",
	12: "ReadOnlyNodeMaxDelayTime",
}

func (p *ModifyDBEndpointReq) IsSetReadWriteSpliting() bool {
	return p.ReadWriteSpliting != nil
}

func (p *ModifyDBEndpointReq) IsSetGlobalReadOnly() bool {
	return p.GlobalReadOnly != nil
}

func (p *ModifyDBEndpointReq) IsSetEndpointName() bool {
	return p.EndpointName != nil
}

func (p *ModifyDBEndpointReq) IsSetDescription() bool {
	return p.Description != nil
}

func (p *ModifyDBEndpointReq) IsSetNodes() bool {
	return p.Nodes != nil
}

func (p *ModifyDBEndpointReq) IsSetAutoAddNewNodes() bool {
	return p.AutoAddNewNodes != nil
}

func (p *ModifyDBEndpointReq) IsSetReadOnlyNodeDistributionType() bool {
	return p.ReadOnlyNodeDistributionType != nil
}

func (p *ModifyDBEndpointReq) IsSetReadOnlyNodeWeight() bool {
	return p.ReadOnlyNodeWeight != nil
}

func (p *ModifyDBEndpointReq) IsSetReadWriteMode() bool {
	return p.ReadWriteMode != nil
}

func (p *ModifyDBEndpointReq) IsSetReadOnlyNodeMaxDelayTime() bool {
	return p.ReadOnlyNodeMaxDelayTime != nil
}

func (p *ModifyDBEndpointReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBEndpointReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBEndpointReq[fieldId]))
}

func (p *ModifyDBEndpointReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReadWriteSpliting = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.GlobalReadOnly = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndpointName = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Nodes = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoAddNewNodes = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *ReadOnlyWeightType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ReadOnlyWeightType(v)
		_field = &tmp
	}
	p.ReadOnlyNodeDistributionType = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeWeightObject, 0, size)
	values := make([]NodeWeightObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ReadOnlyNodeWeight = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *ReadWriteMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ReadWriteMode(v)
		_field = &tmp
	}
	p.ReadWriteMode = _field
	return nil
}
func (p *ModifyDBEndpointReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReadOnlyNodeMaxDelayTime = _field
	return nil
}

func (p *ModifyDBEndpointReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBEndpointReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadWriteSpliting() {
		if err = oprot.WriteFieldBegin("ReadWriteSpliting", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ReadWriteSpliting); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetGlobalReadOnly() {
		if err = oprot.WriteFieldBegin("GlobalReadOnly", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.GlobalReadOnly); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointName() {
		if err = oprot.WriteFieldBegin("EndpointName", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndpointName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("Description", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodes() {
		if err = oprot.WriteFieldBegin("Nodes", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Nodes); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoAddNewNodes() {
		if err = oprot.WriteFieldBegin("AutoAddNewNodes", thrift.BOOL, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AutoAddNewNodes); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadOnlyNodeDistributionType() {
		if err = oprot.WriteFieldBegin("ReadOnlyNodeDistributionType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ReadOnlyNodeDistributionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadOnlyNodeWeight() {
		if err = oprot.WriteFieldBegin("ReadOnlyNodeWeight", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ReadOnlyNodeWeight)); err != nil {
			return err
		}
		for _, v := range p.ReadOnlyNodeWeight {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadWriteMode() {
		if err = oprot.WriteFieldBegin("ReadWriteMode", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ReadWriteMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadOnlyNodeMaxDelayTime() {
		if err = oprot.WriteFieldBegin("ReadOnlyNodeMaxDelayTime", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ReadOnlyNodeMaxDelayTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ModifyDBEndpointReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBEndpointReq(%+v)", *p)

}

func (p *ModifyDBEndpointReq) DeepEqual(ano *ModifyDBEndpointReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReadWriteSpliting) {
		return false
	}
	if !p.Field4DeepEqual(ano.GlobalReadOnly) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndpointName) {
		return false
	}
	if !p.Field6DeepEqual(ano.Description) {
		return false
	}
	if !p.Field7DeepEqual(ano.Nodes) {
		return false
	}
	if !p.Field8DeepEqual(ano.AutoAddNewNodes) {
		return false
	}
	if !p.Field9DeepEqual(ano.ReadOnlyNodeDistributionType) {
		return false
	}
	if !p.Field10DeepEqual(ano.ReadOnlyNodeWeight) {
		return false
	}
	if !p.Field11DeepEqual(ano.ReadWriteMode) {
		return false
	}
	if !p.Field12DeepEqual(ano.ReadOnlyNodeMaxDelayTime) {
		return false
	}
	return true
}

func (p *ModifyDBEndpointReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field3DeepEqual(src *bool) bool {

	if p.ReadWriteSpliting == src {
		return true
	} else if p.ReadWriteSpliting == nil || src == nil {
		return false
	}
	if *p.ReadWriteSpliting != *src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field4DeepEqual(src *bool) bool {

	if p.GlobalReadOnly == src {
		return true
	} else if p.GlobalReadOnly == nil || src == nil {
		return false
	}
	if *p.GlobalReadOnly != *src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field5DeepEqual(src *string) bool {

	if p.EndpointName == src {
		return true
	} else if p.EndpointName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndpointName, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field6DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field7DeepEqual(src *string) bool {

	if p.Nodes == src {
		return true
	} else if p.Nodes == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Nodes, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field8DeepEqual(src *bool) bool {

	if p.AutoAddNewNodes == src {
		return true
	} else if p.AutoAddNewNodes == nil || src == nil {
		return false
	}
	if *p.AutoAddNewNodes != *src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field9DeepEqual(src *ReadOnlyWeightType) bool {

	if p.ReadOnlyNodeDistributionType == src {
		return true
	} else if p.ReadOnlyNodeDistributionType == nil || src == nil {
		return false
	}
	if *p.ReadOnlyNodeDistributionType != *src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field10DeepEqual(src []*NodeWeightObject) bool {

	if len(p.ReadOnlyNodeWeight) != len(src) {
		return false
	}
	for i, v := range p.ReadOnlyNodeWeight {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyDBEndpointReq) Field11DeepEqual(src *ReadWriteMode) bool {

	if p.ReadWriteMode == src {
		return true
	} else if p.ReadWriteMode == nil || src == nil {
		return false
	}
	if *p.ReadWriteMode != *src {
		return false
	}
	return true
}
func (p *ModifyDBEndpointReq) Field12DeepEqual(src *int32) bool {

	if p.ReadOnlyNodeMaxDelayTime == src {
		return true
	} else if p.ReadOnlyNodeMaxDelayTime == nil || src == nil {
		return false
	}
	if *p.ReadOnlyNodeMaxDelayTime != *src {
		return false
	}
	return true
}

type CreateDBEndpointPublicAddressReq struct {
	InstanceId   string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId   string  `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
	EipId        string  `thrift:"EipId,3,required" frugal:"3,required,string" validate:"required"`
	DomainPrefix *string `thrift:"DomainPrefix,4,optional" frugal:"4,optional,string" json:"DomainPrefix,omitempty"`
}

func NewCreateDBEndpointPublicAddressReq() *CreateDBEndpointPublicAddressReq {
	return &CreateDBEndpointPublicAddressReq{}
}

func (p *CreateDBEndpointPublicAddressReq) InitDefault() {
}

func (p *CreateDBEndpointPublicAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBEndpointPublicAddressReq) GetEndpointId() (v string) {
	return p.EndpointId
}

func (p *CreateDBEndpointPublicAddressReq) GetEipId() (v string) {
	return p.EipId
}

var CreateDBEndpointPublicAddressReq_DomainPrefix_DEFAULT string

func (p *CreateDBEndpointPublicAddressReq) GetDomainPrefix() (v string) {
	if !p.IsSetDomainPrefix() {
		return CreateDBEndpointPublicAddressReq_DomainPrefix_DEFAULT
	}
	return *p.DomainPrefix
}
func (p *CreateDBEndpointPublicAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBEndpointPublicAddressReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *CreateDBEndpointPublicAddressReq) SetEipId(val string) {
	p.EipId = val
}
func (p *CreateDBEndpointPublicAddressReq) SetDomainPrefix(val *string) {
	p.DomainPrefix = val
}

var fieldIDToName_CreateDBEndpointPublicAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
	3: "EipId",
	4: "DomainPrefix",
}

func (p *CreateDBEndpointPublicAddressReq) IsSetDomainPrefix() bool {
	return p.DomainPrefix != nil
}

func (p *CreateDBEndpointPublicAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointPublicAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false
	var issetEipId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEipId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEipId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBEndpointPublicAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBEndpointPublicAddressReq[fieldId]))
}

func (p *CreateDBEndpointPublicAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBEndpointPublicAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *CreateDBEndpointPublicAddressReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EipId = _field
	return nil
}
func (p *CreateDBEndpointPublicAddressReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DomainPrefix = _field
	return nil
}

func (p *CreateDBEndpointPublicAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointPublicAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBEndpointPublicAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EipId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EipId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDomainPrefix() {
		if err = oprot.WriteFieldBegin("DomainPrefix", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DomainPrefix); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBEndpointPublicAddressReq(%+v)", *p)

}

func (p *CreateDBEndpointPublicAddressReq) DeepEqual(ano *CreateDBEndpointPublicAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.EipId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DomainPrefix) {
		return false
	}
	return true
}

func (p *CreateDBEndpointPublicAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointPublicAddressReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointPublicAddressReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EipId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointPublicAddressReq) Field4DeepEqual(src *string) bool {

	if p.DomainPrefix == src {
		return true
	} else if p.DomainPrefix == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DomainPrefix, *src) != 0 {
		return false
	}
	return true
}

type CreateDBEndpointPublicAddressResp struct {
}

func NewCreateDBEndpointPublicAddressResp() *CreateDBEndpointPublicAddressResp {
	return &CreateDBEndpointPublicAddressResp{}
}

func (p *CreateDBEndpointPublicAddressResp) InitDefault() {
}

var fieldIDToName_CreateDBEndpointPublicAddressResp = map[int16]string{}

func (p *CreateDBEndpointPublicAddressResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointPublicAddressResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointPublicAddressResp")

	if err = oprot.WriteStructBegin("CreateDBEndpointPublicAddressResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBEndpointPublicAddressResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBEndpointPublicAddressResp(%+v)", *p)

}

func (p *CreateDBEndpointPublicAddressResp) DeepEqual(ano *CreateDBEndpointPublicAddressResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteDBEndpointPublicAddressReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId string `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
	EipId      string `thrift:"EipId,3,required" frugal:"3,required,string" validate:"required"`
	Domain     string `thrift:"Domain,4,required" frugal:"4,required,string" validate:"required"`
}

func NewDeleteDBEndpointPublicAddressReq() *DeleteDBEndpointPublicAddressReq {
	return &DeleteDBEndpointPublicAddressReq{}
}

func (p *DeleteDBEndpointPublicAddressReq) InitDefault() {
}

func (p *DeleteDBEndpointPublicAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBEndpointPublicAddressReq) GetEndpointId() (v string) {
	return p.EndpointId
}

func (p *DeleteDBEndpointPublicAddressReq) GetEipId() (v string) {
	return p.EipId
}

func (p *DeleteDBEndpointPublicAddressReq) GetDomain() (v string) {
	return p.Domain
}
func (p *DeleteDBEndpointPublicAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBEndpointPublicAddressReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *DeleteDBEndpointPublicAddressReq) SetEipId(val string) {
	p.EipId = val
}
func (p *DeleteDBEndpointPublicAddressReq) SetDomain(val string) {
	p.Domain = val
}

var fieldIDToName_DeleteDBEndpointPublicAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
	3: "EipId",
	4: "Domain",
}

func (p *DeleteDBEndpointPublicAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointPublicAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false
	var issetEipId bool = false
	var issetDomain bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEipId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDomain = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEipId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDomain {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBEndpointPublicAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBEndpointPublicAddressReq[fieldId]))
}

func (p *DeleteDBEndpointPublicAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBEndpointPublicAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *DeleteDBEndpointPublicAddressReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EipId = _field
	return nil
}
func (p *DeleteDBEndpointPublicAddressReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Domain = _field
	return nil
}

func (p *DeleteDBEndpointPublicAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointPublicAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBEndpointPublicAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EipId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EipId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Domain", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Domain); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBEndpointPublicAddressReq(%+v)", *p)

}

func (p *DeleteDBEndpointPublicAddressReq) DeepEqual(ano *DeleteDBEndpointPublicAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.EipId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Domain) {
		return false
	}
	return true
}

func (p *DeleteDBEndpointPublicAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBEndpointPublicAddressReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBEndpointPublicAddressReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EipId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBEndpointPublicAddressReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Domain, src) != 0 {
		return false
	}
	return true
}

type DeleteDBEndpointPublicAddressResp struct {
}

func NewDeleteDBEndpointPublicAddressResp() *DeleteDBEndpointPublicAddressResp {
	return &DeleteDBEndpointPublicAddressResp{}
}

func (p *DeleteDBEndpointPublicAddressResp) InitDefault() {
}

var fieldIDToName_DeleteDBEndpointPublicAddressResp = map[int16]string{}

func (p *DeleteDBEndpointPublicAddressResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointPublicAddressResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointPublicAddressResp")

	if err = oprot.WriteStructBegin("DeleteDBEndpointPublicAddressResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBEndpointPublicAddressResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBEndpointPublicAddressResp(%+v)", *p)

}

func (p *DeleteDBEndpointPublicAddressResp) DeepEqual(ano *DeleteDBEndpointPublicAddressResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateDBEndpointInnerAddressReq struct {
	InstanceId   string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId   string  `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
	DomainPrefix *string `thrift:"DomainPrefix,3,optional" frugal:"3,optional,string" json:"DomainPrefix,omitempty"`
}

func NewCreateDBEndpointInnerAddressReq() *CreateDBEndpointInnerAddressReq {
	return &CreateDBEndpointInnerAddressReq{}
}

func (p *CreateDBEndpointInnerAddressReq) InitDefault() {
}

func (p *CreateDBEndpointInnerAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBEndpointInnerAddressReq) GetEndpointId() (v string) {
	return p.EndpointId
}

var CreateDBEndpointInnerAddressReq_DomainPrefix_DEFAULT string

func (p *CreateDBEndpointInnerAddressReq) GetDomainPrefix() (v string) {
	if !p.IsSetDomainPrefix() {
		return CreateDBEndpointInnerAddressReq_DomainPrefix_DEFAULT
	}
	return *p.DomainPrefix
}
func (p *CreateDBEndpointInnerAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBEndpointInnerAddressReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *CreateDBEndpointInnerAddressReq) SetDomainPrefix(val *string) {
	p.DomainPrefix = val
}

var fieldIDToName_CreateDBEndpointInnerAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
	3: "DomainPrefix",
}

func (p *CreateDBEndpointInnerAddressReq) IsSetDomainPrefix() bool {
	return p.DomainPrefix != nil
}

func (p *CreateDBEndpointInnerAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointInnerAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBEndpointInnerAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBEndpointInnerAddressReq[fieldId]))
}

func (p *CreateDBEndpointInnerAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBEndpointInnerAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *CreateDBEndpointInnerAddressReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DomainPrefix = _field
	return nil
}

func (p *CreateDBEndpointInnerAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointInnerAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBEndpointInnerAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBEndpointInnerAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBEndpointInnerAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBEndpointInnerAddressReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDomainPrefix() {
		if err = oprot.WriteFieldBegin("DomainPrefix", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DomainPrefix); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDBEndpointInnerAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBEndpointInnerAddressReq(%+v)", *p)

}

func (p *CreateDBEndpointInnerAddressReq) DeepEqual(ano *CreateDBEndpointInnerAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DomainPrefix) {
		return false
	}
	return true
}

func (p *CreateDBEndpointInnerAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointInnerAddressReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointInnerAddressReq) Field3DeepEqual(src *string) bool {

	if p.DomainPrefix == src {
		return true
	} else if p.DomainPrefix == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DomainPrefix, *src) != 0 {
		return false
	}
	return true
}

type DeleteDBEndpointInnerAddressReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId string `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteDBEndpointInnerAddressReq() *DeleteDBEndpointInnerAddressReq {
	return &DeleteDBEndpointInnerAddressReq{}
}

func (p *DeleteDBEndpointInnerAddressReq) InitDefault() {
}

func (p *DeleteDBEndpointInnerAddressReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBEndpointInnerAddressReq) GetEndpointId() (v string) {
	return p.EndpointId
}
func (p *DeleteDBEndpointInnerAddressReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBEndpointInnerAddressReq) SetEndpointId(val string) {
	p.EndpointId = val
}

var fieldIDToName_DeleteDBEndpointInnerAddressReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
}

func (p *DeleteDBEndpointInnerAddressReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointInnerAddressReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBEndpointInnerAddressReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBEndpointInnerAddressReq[fieldId]))
}

func (p *DeleteDBEndpointInnerAddressReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBEndpointInnerAddressReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}

func (p *DeleteDBEndpointInnerAddressReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointInnerAddressReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBEndpointInnerAddressReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBEndpointInnerAddressReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBEndpointInnerAddressReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBEndpointInnerAddressReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBEndpointInnerAddressReq(%+v)", *p)

}

func (p *DeleteDBEndpointInnerAddressReq) DeepEqual(ano *DeleteDBEndpointInnerAddressReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	return true
}

func (p *DeleteDBEndpointInnerAddressReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBEndpointInnerAddressReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}

type CreateDBEndpointReq struct {
	InstanceId      string         `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointType    EndpointType   `thrift:"EndpointType,2,required" frugal:"2,required,EndpointType" json:"EndpointType"`
	EndpointName    *string        `thrift:"EndpointName,3,optional" frugal:"3,optional,string" json:"EndpointName,omitempty"`
	Description     *string        `thrift:"Description,4,optional" frugal:"4,optional,string" json:"Description,omitempty"`
	Nodes           *string        `thrift:"Nodes,5,optional" frugal:"5,optional,string" json:"Nodes,omitempty"`
	AutoAddNewNodes *bool          `thrift:"AutoAddNewNodes,6,optional" frugal:"6,optional,bool" json:"AutoAddNewNodes,omitempty"`
	ReadWriteMode   *ReadWriteMode `thrift:"ReadWriteMode,7,optional" frugal:"7,optional,ReadWriteMode" json:"ReadWriteMode,omitempty"`
}

func NewCreateDBEndpointReq() *CreateDBEndpointReq {
	return &CreateDBEndpointReq{}
}

func (p *CreateDBEndpointReq) InitDefault() {
}

func (p *CreateDBEndpointReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBEndpointReq) GetEndpointType() (v EndpointType) {
	return p.EndpointType
}

var CreateDBEndpointReq_EndpointName_DEFAULT string

func (p *CreateDBEndpointReq) GetEndpointName() (v string) {
	if !p.IsSetEndpointName() {
		return CreateDBEndpointReq_EndpointName_DEFAULT
	}
	return *p.EndpointName
}

var CreateDBEndpointReq_Description_DEFAULT string

func (p *CreateDBEndpointReq) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CreateDBEndpointReq_Description_DEFAULT
	}
	return *p.Description
}

var CreateDBEndpointReq_Nodes_DEFAULT string

func (p *CreateDBEndpointReq) GetNodes() (v string) {
	if !p.IsSetNodes() {
		return CreateDBEndpointReq_Nodes_DEFAULT
	}
	return *p.Nodes
}

var CreateDBEndpointReq_AutoAddNewNodes_DEFAULT bool

func (p *CreateDBEndpointReq) GetAutoAddNewNodes() (v bool) {
	if !p.IsSetAutoAddNewNodes() {
		return CreateDBEndpointReq_AutoAddNewNodes_DEFAULT
	}
	return *p.AutoAddNewNodes
}

var CreateDBEndpointReq_ReadWriteMode_DEFAULT ReadWriteMode

func (p *CreateDBEndpointReq) GetReadWriteMode() (v ReadWriteMode) {
	if !p.IsSetReadWriteMode() {
		return CreateDBEndpointReq_ReadWriteMode_DEFAULT
	}
	return *p.ReadWriteMode
}
func (p *CreateDBEndpointReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBEndpointReq) SetEndpointType(val EndpointType) {
	p.EndpointType = val
}
func (p *CreateDBEndpointReq) SetEndpointName(val *string) {
	p.EndpointName = val
}
func (p *CreateDBEndpointReq) SetDescription(val *string) {
	p.Description = val
}
func (p *CreateDBEndpointReq) SetNodes(val *string) {
	p.Nodes = val
}
func (p *CreateDBEndpointReq) SetAutoAddNewNodes(val *bool) {
	p.AutoAddNewNodes = val
}
func (p *CreateDBEndpointReq) SetReadWriteMode(val *ReadWriteMode) {
	p.ReadWriteMode = val
}

var fieldIDToName_CreateDBEndpointReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointType",
	3: "EndpointName",
	4: "Description",
	5: "Nodes",
	6: "AutoAddNewNodes",
	7: "ReadWriteMode",
}

func (p *CreateDBEndpointReq) IsSetEndpointName() bool {
	return p.EndpointName != nil
}

func (p *CreateDBEndpointReq) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CreateDBEndpointReq) IsSetNodes() bool {
	return p.Nodes != nil
}

func (p *CreateDBEndpointReq) IsSetAutoAddNewNodes() bool {
	return p.AutoAddNewNodes != nil
}

func (p *CreateDBEndpointReq) IsSetReadWriteMode() bool {
	return p.ReadWriteMode != nil
}

func (p *CreateDBEndpointReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBEndpointReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBEndpointReq[fieldId]))
}

func (p *CreateDBEndpointReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EndpointType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EndpointType(v)
	}
	p.EndpointType = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndpointName = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Nodes = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoAddNewNodes = _field
	return nil
}
func (p *CreateDBEndpointReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *ReadWriteMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ReadWriteMode(v)
		_field = &tmp
	}
	p.ReadWriteMode = _field
	return nil
}

func (p *CreateDBEndpointReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBEndpointReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EndpointType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointName() {
		if err = oprot.WriteFieldBegin("EndpointName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndpointName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("Description", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodes() {
		if err = oprot.WriteFieldBegin("Nodes", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Nodes); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoAddNewNodes() {
		if err = oprot.WriteFieldBegin("AutoAddNewNodes", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AutoAddNewNodes); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateDBEndpointReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetReadWriteMode() {
		if err = oprot.WriteFieldBegin("ReadWriteMode", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ReadWriteMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateDBEndpointReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBEndpointReq(%+v)", *p)

}

func (p *CreateDBEndpointReq) DeepEqual(ano *CreateDBEndpointReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointType) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndpointName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Description) {
		return false
	}
	if !p.Field5DeepEqual(ano.Nodes) {
		return false
	}
	if !p.Field6DeepEqual(ano.AutoAddNewNodes) {
		return false
	}
	if !p.Field7DeepEqual(ano.ReadWriteMode) {
		return false
	}
	return true
}

func (p *CreateDBEndpointReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field2DeepEqual(src EndpointType) bool {

	if p.EndpointType != src {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field3DeepEqual(src *string) bool {

	if p.EndpointName == src {
		return true
	} else if p.EndpointName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndpointName, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field4DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field5DeepEqual(src *string) bool {

	if p.Nodes == src {
		return true
	} else if p.Nodes == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Nodes, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field6DeepEqual(src *bool) bool {

	if p.AutoAddNewNodes == src {
		return true
	} else if p.AutoAddNewNodes == nil || src == nil {
		return false
	}
	if *p.AutoAddNewNodes != *src {
		return false
	}
	return true
}
func (p *CreateDBEndpointReq) Field7DeepEqual(src *ReadWriteMode) bool {

	if p.ReadWriteMode == src {
		return true
	} else if p.ReadWriteMode == nil || src == nil {
		return false
	}
	if *p.ReadWriteMode != *src {
		return false
	}
	return true
}

type CreateDBEndpointResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	EndpointId string `thrift:"EndpointId,2,required" frugal:"2,required,string" json:"EndpointId"`
}

func NewCreateDBEndpointResp() *CreateDBEndpointResp {
	return &CreateDBEndpointResp{}
}

func (p *CreateDBEndpointResp) InitDefault() {
}

func (p *CreateDBEndpointResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBEndpointResp) GetEndpointId() (v string) {
	return p.EndpointId
}
func (p *CreateDBEndpointResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBEndpointResp) SetEndpointId(val string) {
	p.EndpointId = val
}

var fieldIDToName_CreateDBEndpointResp = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
}

func (p *CreateDBEndpointResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBEndpointResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBEndpointResp[fieldId]))
}

func (p *CreateDBEndpointResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBEndpointResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}

func (p *CreateDBEndpointResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBEndpointResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBEndpointResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBEndpointResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBEndpointResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBEndpointResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBEndpointResp(%+v)", *p)

}

func (p *CreateDBEndpointResp) DeepEqual(ano *CreateDBEndpointResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	return true
}

func (p *CreateDBEndpointResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBEndpointResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}

type DeleteDBEndpointReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId string `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteDBEndpointReq() *DeleteDBEndpointReq {
	return &DeleteDBEndpointReq{}
}

func (p *DeleteDBEndpointReq) InitDefault() {
}

func (p *DeleteDBEndpointReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBEndpointReq) GetEndpointId() (v string) {
	return p.EndpointId
}
func (p *DeleteDBEndpointReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBEndpointReq) SetEndpointId(val string) {
	p.EndpointId = val
}

var fieldIDToName_DeleteDBEndpointReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
}

func (p *DeleteDBEndpointReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBEndpointReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBEndpointReq[fieldId]))
}

func (p *DeleteDBEndpointReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBEndpointReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}

func (p *DeleteDBEndpointReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBEndpointReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBEndpointReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBEndpointReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBEndpointReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBEndpointReq(%+v)", *p)

}

func (p *DeleteDBEndpointReq) DeepEqual(ano *DeleteDBEndpointReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	return true
}

func (p *DeleteDBEndpointReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBEndpointReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}

type DeleteDBEndpointResp struct {
}

func NewDeleteDBEndpointResp() *DeleteDBEndpointResp {
	return &DeleteDBEndpointResp{}
}

func (p *DeleteDBEndpointResp) InitDefault() {
}

var fieldIDToName_DeleteDBEndpointResp = map[int16]string{}

func (p *DeleteDBEndpointResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteDBEndpointResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBEndpointResp")

	if err = oprot.WriteStructBegin("DeleteDBEndpointResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBEndpointResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBEndpointResp(%+v)", *p)

}

func (p *DeleteDBEndpointResp) DeepEqual(ano *DeleteDBEndpointResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ModifyDBEndpointPrivateDNSReq struct {
	InstanceId    string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	EndpointId    string `thrift:"EndpointId,2,required" frugal:"2,required,string" validate:"required"`
	DNSVisibility *bool  `thrift:"DNSVisibility,3,optional" frugal:"3,optional,bool" json:"DNSVisibility,omitempty"`
}

func NewModifyDBEndpointPrivateDNSReq() *ModifyDBEndpointPrivateDNSReq {
	return &ModifyDBEndpointPrivateDNSReq{}
}

func (p *ModifyDBEndpointPrivateDNSReq) InitDefault() {
}

func (p *ModifyDBEndpointPrivateDNSReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBEndpointPrivateDNSReq) GetEndpointId() (v string) {
	return p.EndpointId
}

var ModifyDBEndpointPrivateDNSReq_DNSVisibility_DEFAULT bool

func (p *ModifyDBEndpointPrivateDNSReq) GetDNSVisibility() (v bool) {
	if !p.IsSetDNSVisibility() {
		return ModifyDBEndpointPrivateDNSReq_DNSVisibility_DEFAULT
	}
	return *p.DNSVisibility
}
func (p *ModifyDBEndpointPrivateDNSReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBEndpointPrivateDNSReq) SetEndpointId(val string) {
	p.EndpointId = val
}
func (p *ModifyDBEndpointPrivateDNSReq) SetDNSVisibility(val *bool) {
	p.DNSVisibility = val
}

var fieldIDToName_ModifyDBEndpointPrivateDNSReq = map[int16]string{
	1: "InstanceId",
	2: "EndpointId",
	3: "DNSVisibility",
}

func (p *ModifyDBEndpointPrivateDNSReq) IsSetDNSVisibility() bool {
	return p.DNSVisibility != nil
}

func (p *ModifyDBEndpointPrivateDNSReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointPrivateDNSReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEndpointId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpointId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEndpointId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBEndpointPrivateDNSReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBEndpointPrivateDNSReq[fieldId]))
}

func (p *ModifyDBEndpointPrivateDNSReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBEndpointPrivateDNSReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndpointId = _field
	return nil
}
func (p *ModifyDBEndpointPrivateDNSReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DNSVisibility = _field
	return nil
}

func (p *ModifyDBEndpointPrivateDNSReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointPrivateDNSReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBEndpointPrivateDNSReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndpointId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDNSVisibility() {
		if err = oprot.WriteFieldBegin("DNSVisibility", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DNSVisibility); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBEndpointPrivateDNSReq(%+v)", *p)

}

func (p *ModifyDBEndpointPrivateDNSReq) DeepEqual(ano *ModifyDBEndpointPrivateDNSReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DNSVisibility) {
		return false
	}
	return true
}

func (p *ModifyDBEndpointPrivateDNSReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointPrivateDNSReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EndpointId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBEndpointPrivateDNSReq) Field3DeepEqual(src *bool) bool {

	if p.DNSVisibility == src {
		return true
	} else if p.DNSVisibility == nil || src == nil {
		return false
	}
	if *p.DNSVisibility != *src {
		return false
	}
	return true
}

type ModifyDBEndpointPrivateDNSResp struct {
}

func NewModifyDBEndpointPrivateDNSResp() *ModifyDBEndpointPrivateDNSResp {
	return &ModifyDBEndpointPrivateDNSResp{}
}

func (p *ModifyDBEndpointPrivateDNSResp) InitDefault() {
}

var fieldIDToName_ModifyDBEndpointPrivateDNSResp = map[int16]string{}

func (p *ModifyDBEndpointPrivateDNSResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointPrivateDNSResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBEndpointPrivateDNSResp")

	if err = oprot.WriteStructBegin("ModifyDBEndpointPrivateDNSResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBEndpointPrivateDNSResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBEndpointPrivateDNSResp(%+v)", *p)

}

func (p *ModifyDBEndpointPrivateDNSResp) DeepEqual(ano *ModifyDBEndpointPrivateDNSResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

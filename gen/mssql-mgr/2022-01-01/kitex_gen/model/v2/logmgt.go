// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeEventsReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	StartTime    *string       `thrift:"StartTime,2,optional" frugal:"2,optional,string" json:"StartTime,omitempty"`
	EndTime      *string       `thrift:"EndTime,3,optional" frugal:"3,optional,string" json:"EndTime,omitempty"`
	EventName    *string       `thrift:"EventName,4,optional" frugal:"4,optional,string" json:"EventName,omitempty"`
	EventResult_ *EventResult_ `thrift:"EventResult,5,optional" frugal:"5,optional,EventResult_" json:"EventResult,omitempty"`
	EventType    *EventType    `thrift:"EventType,6,optional" frugal:"6,optional,EventType" json:"EventType,omitempty"`
	EventSource  *EventSource  `thrift:"EventSource,7,optional" frugal:"7,optional,EventSource" json:"EventSource,omitempty"`
	PageNumber   *int32        `thrift:"PageNumber,8,optional" frugal:"8,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32        `thrift:"PageSize,9,optional" frugal:"9,optional,i32" json:"PageSize,omitempty"`
	ProjectName  *string       `thrift:"ProjectName,10,optional" frugal:"10,optional,string" json:"ProjectName,omitempty"`
}

func NewDescribeEventsReq() *DescribeEventsReq {
	return &DescribeEventsReq{}
}

func (p *DescribeEventsReq) InitDefault() {
}

var DescribeEventsReq_InstanceId_DEFAULT string

func (p *DescribeEventsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeEventsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeEventsReq_StartTime_DEFAULT string

func (p *DescribeEventsReq) GetStartTime() (v string) {
	if !p.IsSetStartTime() {
		return DescribeEventsReq_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DescribeEventsReq_EndTime_DEFAULT string

func (p *DescribeEventsReq) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return DescribeEventsReq_EndTime_DEFAULT
	}
	return *p.EndTime
}

var DescribeEventsReq_EventName_DEFAULT string

func (p *DescribeEventsReq) GetEventName() (v string) {
	if !p.IsSetEventName() {
		return DescribeEventsReq_EventName_DEFAULT
	}
	return *p.EventName
}

var DescribeEventsReq_EventResult__DEFAULT EventResult_

func (p *DescribeEventsReq) GetEventResult_() (v EventResult_) {
	if !p.IsSetEventResult_() {
		return DescribeEventsReq_EventResult__DEFAULT
	}
	return *p.EventResult_
}

var DescribeEventsReq_EventType_DEFAULT EventType

func (p *DescribeEventsReq) GetEventType() (v EventType) {
	if !p.IsSetEventType() {
		return DescribeEventsReq_EventType_DEFAULT
	}
	return *p.EventType
}

var DescribeEventsReq_EventSource_DEFAULT EventSource

func (p *DescribeEventsReq) GetEventSource() (v EventSource) {
	if !p.IsSetEventSource() {
		return DescribeEventsReq_EventSource_DEFAULT
	}
	return *p.EventSource
}

var DescribeEventsReq_PageNumber_DEFAULT int32

func (p *DescribeEventsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeEventsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeEventsReq_PageSize_DEFAULT int32

func (p *DescribeEventsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeEventsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeEventsReq_ProjectName_DEFAULT string

func (p *DescribeEventsReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeEventsReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *DescribeEventsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeEventsReq) SetStartTime(val *string) {
	p.StartTime = val
}
func (p *DescribeEventsReq) SetEndTime(val *string) {
	p.EndTime = val
}
func (p *DescribeEventsReq) SetEventName(val *string) {
	p.EventName = val
}
func (p *DescribeEventsReq) SetEventResult_(val *EventResult_) {
	p.EventResult_ = val
}
func (p *DescribeEventsReq) SetEventType(val *EventType) {
	p.EventType = val
}
func (p *DescribeEventsReq) SetEventSource(val *EventSource) {
	p.EventSource = val
}
func (p *DescribeEventsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeEventsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeEventsReq) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_DescribeEventsReq = map[int16]string{
	1:  "InstanceId",
	2:  "StartTime",
	3:  "EndTime",
	4:  "EventName",
	5:  "EventResult",
	6:  "EventType",
	7:  "EventSource",
	8:  "PageNumber",
	9:  "PageSize",
	10: "ProjectName",
}

func (p *DescribeEventsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeEventsReq) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DescribeEventsReq) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DescribeEventsReq) IsSetEventName() bool {
	return p.EventName != nil
}

func (p *DescribeEventsReq) IsSetEventResult_() bool {
	return p.EventResult_ != nil
}

func (p *DescribeEventsReq) IsSetEventType() bool {
	return p.EventType != nil
}

func (p *DescribeEventsReq) IsSetEventSource() bool {
	return p.EventSource != nil
}

func (p *DescribeEventsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeEventsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeEventsReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeEventsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EventName = _field
	return nil
}
func (p *DescribeEventsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *EventResult_
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventResult_(v)
		_field = &tmp
	}
	p.EventResult_ = _field
	return nil
}
func (p *DescribeEventsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *EventType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventType(v)
		_field = &tmp
	}
	p.EventType = _field
	return nil
}
func (p *DescribeEventsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *EventSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EventSource(v)
		_field = &tmp
	}
	p.EventSource = _field
	return nil
}
func (p *DescribeEventsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeEventsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeEventsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *DescribeEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventName() {
		if err = oprot.WriteFieldBegin("EventName", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EventName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventResult_() {
		if err = oprot.WriteFieldBegin("EventResult", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventResult_)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventType() {
		if err = oprot.WriteFieldBegin("EventType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventSource() {
		if err = oprot.WriteFieldBegin("EventSource", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EventSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventsReq(%+v)", *p)

}

func (p *DescribeEventsReq) DeepEqual(ano *DescribeEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EventName) {
		return false
	}
	if !p.Field5DeepEqual(ano.EventResult_) {
		return false
	}
	if !p.Field6DeepEqual(ano.EventType) {
		return false
	}
	if !p.Field7DeepEqual(ano.EventSource) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field9DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field10DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *DescribeEventsReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field2DeepEqual(src *string) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field3DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field4DeepEqual(src *string) bool {

	if p.EventName == src {
		return true
	} else if p.EventName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EventName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field5DeepEqual(src *EventResult_) bool {

	if p.EventResult_ == src {
		return true
	} else if p.EventResult_ == nil || src == nil {
		return false
	}
	if *p.EventResult_ != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field6DeepEqual(src *EventType) bool {

	if p.EventType == src {
		return true
	} else if p.EventType == nil || src == nil {
		return false
	}
	if *p.EventType != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field7DeepEqual(src *EventSource) bool {

	if p.EventSource == src {
		return true
	} else if p.EventSource == nil || src == nil {
		return false
	}
	if *p.EventSource != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field8DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field9DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field10DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type DescribeEventsResp struct {
	Total  int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Events []*EventObject `thrift:"Events,2,required" frugal:"2,required,list<EventObject>" json:"Events"`
}

func NewDescribeEventsResp() *DescribeEventsResp {
	return &DescribeEventsResp{}
}

func (p *DescribeEventsResp) InitDefault() {
}

func (p *DescribeEventsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeEventsResp) GetEvents() (v []*EventObject) {
	return p.Events
}
func (p *DescribeEventsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeEventsResp) SetEvents(val []*EventObject) {
	p.Events = val
}

var fieldIDToName_DescribeEventsResp = map[int16]string{
	1: "Total",
	2: "Events",
}

func (p *DescribeEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetEvents bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEvents = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEvents {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeEventsResp[fieldId]))
}

func (p *DescribeEventsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeEventsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*EventObject, 0, size)
	values := make([]EventObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Events = _field
	return nil
}

func (p *DescribeEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Events", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Events)); err != nil {
		return err
	}
	for _, v := range p.Events {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventsResp(%+v)", *p)

}

func (p *DescribeEventsResp) DeepEqual(ano *DescribeEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Events) {
		return false
	}
	return true
}

func (p *DescribeEventsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeEventsResp) Field2DeepEqual(src []*EventObject) bool {

	if len(p.Events) != len(src) {
		return false
	}
	for i, v := range p.Events {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeFailoverLogsReq struct {
	InstanceId     string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	QueryStartTime *string `thrift:"QueryStartTime,2,optional" frugal:"2,optional,string" json:"QueryStartTime,omitempty"`
	QueryEndTime   *string `thrift:"QueryEndTime,3,optional" frugal:"3,optional,string" json:"QueryEndTime,omitempty"`
	PageNumber     *int32  `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize       *int32  `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeFailoverLogsReq() *DescribeFailoverLogsReq {
	return &DescribeFailoverLogsReq{}
}

func (p *DescribeFailoverLogsReq) InitDefault() {
}

func (p *DescribeFailoverLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeFailoverLogsReq_QueryStartTime_DEFAULT string

func (p *DescribeFailoverLogsReq) GetQueryStartTime() (v string) {
	if !p.IsSetQueryStartTime() {
		return DescribeFailoverLogsReq_QueryStartTime_DEFAULT
	}
	return *p.QueryStartTime
}

var DescribeFailoverLogsReq_QueryEndTime_DEFAULT string

func (p *DescribeFailoverLogsReq) GetQueryEndTime() (v string) {
	if !p.IsSetQueryEndTime() {
		return DescribeFailoverLogsReq_QueryEndTime_DEFAULT
	}
	return *p.QueryEndTime
}

var DescribeFailoverLogsReq_PageNumber_DEFAULT int32

func (p *DescribeFailoverLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeFailoverLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeFailoverLogsReq_PageSize_DEFAULT int32

func (p *DescribeFailoverLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeFailoverLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeFailoverLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeFailoverLogsReq) SetQueryStartTime(val *string) {
	p.QueryStartTime = val
}
func (p *DescribeFailoverLogsReq) SetQueryEndTime(val *string) {
	p.QueryEndTime = val
}
func (p *DescribeFailoverLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeFailoverLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeFailoverLogsReq = map[int16]string{
	1: "InstanceId",
	2: "QueryStartTime",
	3: "QueryEndTime",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeFailoverLogsReq) IsSetQueryStartTime() bool {
	return p.QueryStartTime != nil
}

func (p *DescribeFailoverLogsReq) IsSetQueryEndTime() bool {
	return p.QueryEndTime != nil
}

func (p *DescribeFailoverLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeFailoverLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeFailoverLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeFailoverLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeFailoverLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeFailoverLogsReq[fieldId]))
}

func (p *DescribeFailoverLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeFailoverLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeFailoverLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeFailoverLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeFailoverLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeFailoverLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeFailoverLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeFailoverLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryStartTime() {
		if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryEndTime() {
		if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeFailoverLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeFailoverLogsReq(%+v)", *p)

}

func (p *DescribeFailoverLogsReq) DeepEqual(ano *DescribeFailoverLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeFailoverLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsReq) Field2DeepEqual(src *string) bool {

	if p.QueryStartTime == src {
		return true
	} else if p.QueryStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsReq) Field3DeepEqual(src *string) bool {

	if p.QueryEndTime == src {
		return true
	} else if p.QueryEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeFailoverLogsResp struct {
	Total          int32                  `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	InstanceId     string                 `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	FailoverQuerys []*FailoverQueryObject `thrift:"FailoverQuerys,3,required" frugal:"3,required,list<FailoverQueryObject>" json:"FailoverQuerys"`
}

func NewDescribeFailoverLogsResp() *DescribeFailoverLogsResp {
	return &DescribeFailoverLogsResp{}
}

func (p *DescribeFailoverLogsResp) InitDefault() {
}

func (p *DescribeFailoverLogsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeFailoverLogsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeFailoverLogsResp) GetFailoverQuerys() (v []*FailoverQueryObject) {
	return p.FailoverQuerys
}
func (p *DescribeFailoverLogsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeFailoverLogsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeFailoverLogsResp) SetFailoverQuerys(val []*FailoverQueryObject) {
	p.FailoverQuerys = val
}

var fieldIDToName_DescribeFailoverLogsResp = map[int16]string{
	1: "Total",
	2: "InstanceId",
	3: "FailoverQuerys",
}

func (p *DescribeFailoverLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeFailoverLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetInstanceId bool = false
	var issetFailoverQuerys bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetFailoverQuerys = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFailoverQuerys {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeFailoverLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeFailoverLogsResp[fieldId]))
}

func (p *DescribeFailoverLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeFailoverLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeFailoverLogsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FailoverQueryObject, 0, size)
	values := make([]FailoverQueryObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FailoverQuerys = _field
	return nil
}

func (p *DescribeFailoverLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeFailoverLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeFailoverLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeFailoverLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeFailoverLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeFailoverLogsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FailoverQuerys", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FailoverQuerys)); err != nil {
		return err
	}
	for _, v := range p.FailoverQuerys {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeFailoverLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeFailoverLogsResp(%+v)", *p)

}

func (p *DescribeFailoverLogsResp) DeepEqual(ano *DescribeFailoverLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.FailoverQuerys) {
		return false
	}
	return true
}

func (p *DescribeFailoverLogsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeFailoverLogsResp) Field3DeepEqual(src []*FailoverQueryObject) bool {

	if len(p.FailoverQuerys) != len(src) {
		return false
	}
	for i, v := range p.FailoverQuerys {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeErrorLogsReq struct {
	InstanceId        string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	ErrorLogStartTime *string `thrift:"ErrorLogStartTime,2,optional" frugal:"2,optional,string" json:"ErrorLogStartTime,omitempty"`
	ErrorLogEndTime   *string `thrift:"ErrorLogEndTime,3,optional" frugal:"3,optional,string" json:"ErrorLogEndTime,omitempty"`
	ErrorLevel        *string `thrift:"ErrorLevel,4,optional" frugal:"4,optional,string" json:"ErrorLevel,omitempty"`
	PageNumber        *int32  `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
	PageSize          *int32  `thrift:"PageSize,6,optional" frugal:"6,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeErrorLogsReq() *DescribeErrorLogsReq {
	return &DescribeErrorLogsReq{}
}

func (p *DescribeErrorLogsReq) InitDefault() {
}

func (p *DescribeErrorLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeErrorLogsReq_ErrorLogStartTime_DEFAULT string

func (p *DescribeErrorLogsReq) GetErrorLogStartTime() (v string) {
	if !p.IsSetErrorLogStartTime() {
		return DescribeErrorLogsReq_ErrorLogStartTime_DEFAULT
	}
	return *p.ErrorLogStartTime
}

var DescribeErrorLogsReq_ErrorLogEndTime_DEFAULT string

func (p *DescribeErrorLogsReq) GetErrorLogEndTime() (v string) {
	if !p.IsSetErrorLogEndTime() {
		return DescribeErrorLogsReq_ErrorLogEndTime_DEFAULT
	}
	return *p.ErrorLogEndTime
}

var DescribeErrorLogsReq_ErrorLevel_DEFAULT string

func (p *DescribeErrorLogsReq) GetErrorLevel() (v string) {
	if !p.IsSetErrorLevel() {
		return DescribeErrorLogsReq_ErrorLevel_DEFAULT
	}
	return *p.ErrorLevel
}

var DescribeErrorLogsReq_PageNumber_DEFAULT int32

func (p *DescribeErrorLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeErrorLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeErrorLogsReq_PageSize_DEFAULT int32

func (p *DescribeErrorLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeErrorLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeErrorLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeErrorLogsReq) SetErrorLogStartTime(val *string) {
	p.ErrorLogStartTime = val
}
func (p *DescribeErrorLogsReq) SetErrorLogEndTime(val *string) {
	p.ErrorLogEndTime = val
}
func (p *DescribeErrorLogsReq) SetErrorLevel(val *string) {
	p.ErrorLevel = val
}
func (p *DescribeErrorLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeErrorLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeErrorLogsReq = map[int16]string{
	1: "InstanceId",
	2: "ErrorLogStartTime",
	3: "ErrorLogEndTime",
	4: "ErrorLevel",
	5: "PageNumber",
	6: "PageSize",
}

func (p *DescribeErrorLogsReq) IsSetErrorLogStartTime() bool {
	return p.ErrorLogStartTime != nil
}

func (p *DescribeErrorLogsReq) IsSetErrorLogEndTime() bool {
	return p.ErrorLogEndTime != nil
}

func (p *DescribeErrorLogsReq) IsSetErrorLevel() bool {
	return p.ErrorLevel != nil
}

func (p *DescribeErrorLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeErrorLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeErrorLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrorLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrorLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrorLogsReq[fieldId]))
}

func (p *DescribeErrorLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeErrorLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorLogStartTime = _field
	return nil
}
func (p *DescribeErrorLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorLogEndTime = _field
	return nil
}
func (p *DescribeErrorLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorLevel = _field
	return nil
}
func (p *DescribeErrorLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeErrorLogsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeErrorLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrorLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrorLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorLogStartTime() {
		if err = oprot.WriteFieldBegin("ErrorLogStartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorLogStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorLogEndTime() {
		if err = oprot.WriteFieldBegin("ErrorLogEndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorLogEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorLevel() {
		if err = oprot.WriteFieldBegin("ErrorLevel", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorLevel); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeErrorLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrorLogsReq(%+v)", *p)

}

func (p *DescribeErrorLogsReq) DeepEqual(ano *DescribeErrorLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrorLogStartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.ErrorLogEndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.ErrorLevel) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeErrorLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrorLogsReq) Field2DeepEqual(src *string) bool {

	if p.ErrorLogStartTime == src {
		return true
	} else if p.ErrorLogStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorLogStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrorLogsReq) Field3DeepEqual(src *string) bool {

	if p.ErrorLogEndTime == src {
		return true
	} else if p.ErrorLogEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorLogEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrorLogsReq) Field4DeepEqual(src *string) bool {

	if p.ErrorLevel == src {
		return true
	} else if p.ErrorLevel == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorLevel, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrorLogsReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeErrorLogsReq) Field6DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeErrorLogsResp struct {
	InstanceId string            `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	ErrorLogs  []*ErrorLogObject `thrift:"ErrorLogs,2,required" frugal:"2,required,list<ErrorLogObject>" json:"ErrorLogs"`
}

func NewDescribeErrorLogsResp() *DescribeErrorLogsResp {
	return &DescribeErrorLogsResp{}
}

func (p *DescribeErrorLogsResp) InitDefault() {
}

func (p *DescribeErrorLogsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeErrorLogsResp) GetErrorLogs() (v []*ErrorLogObject) {
	return p.ErrorLogs
}
func (p *DescribeErrorLogsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeErrorLogsResp) SetErrorLogs(val []*ErrorLogObject) {
	p.ErrorLogs = val
}

var fieldIDToName_DescribeErrorLogsResp = map[int16]string{
	1: "InstanceId",
	2: "ErrorLogs",
}

func (p *DescribeErrorLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrorLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetErrorLogs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrorLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetErrorLogs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrorLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrorLogsResp[fieldId]))
}

func (p *DescribeErrorLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeErrorLogsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ErrorLogObject, 0, size)
	values := make([]ErrorLogObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ErrorLogs = _field
	return nil
}

func (p *DescribeErrorLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrorLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrorLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrorLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrorLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrorLogs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ErrorLogs)); err != nil {
		return err
	}
	for _, v := range p.ErrorLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrorLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrorLogsResp(%+v)", *p)

}

func (p *DescribeErrorLogsResp) DeepEqual(ano *DescribeErrorLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrorLogs) {
		return false
	}
	return true
}

func (p *DescribeErrorLogsResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrorLogsResp) Field2DeepEqual(src []*ErrorLogObject) bool {

	if len(p.ErrorLogs) != len(src) {
		return false
	}
	for i, v := range p.ErrorLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeAuditLogsReq struct {
	InstanceId     string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	QueryStartTime *string `thrift:"QueryStartTime,2,optional" frugal:"2,optional,string" json:"QueryStartTime,omitempty"`
	QueryEndTime   *string `thrift:"QueryEndTime,3,optional" frugal:"3,optional,string" json:"QueryEndTime,omitempty"`
	PageNumber     *int32  `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize       *int32  `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeAuditLogsReq() *DescribeAuditLogsReq {
	return &DescribeAuditLogsReq{}
}

func (p *DescribeAuditLogsReq) InitDefault() {
}

func (p *DescribeAuditLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeAuditLogsReq_QueryStartTime_DEFAULT string

func (p *DescribeAuditLogsReq) GetQueryStartTime() (v string) {
	if !p.IsSetQueryStartTime() {
		return DescribeAuditLogsReq_QueryStartTime_DEFAULT
	}
	return *p.QueryStartTime
}

var DescribeAuditLogsReq_QueryEndTime_DEFAULT string

func (p *DescribeAuditLogsReq) GetQueryEndTime() (v string) {
	if !p.IsSetQueryEndTime() {
		return DescribeAuditLogsReq_QueryEndTime_DEFAULT
	}
	return *p.QueryEndTime
}

var DescribeAuditLogsReq_PageNumber_DEFAULT int32

func (p *DescribeAuditLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeAuditLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeAuditLogsReq_PageSize_DEFAULT int32

func (p *DescribeAuditLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeAuditLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeAuditLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAuditLogsReq) SetQueryStartTime(val *string) {
	p.QueryStartTime = val
}
func (p *DescribeAuditLogsReq) SetQueryEndTime(val *string) {
	p.QueryEndTime = val
}
func (p *DescribeAuditLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeAuditLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeAuditLogsReq = map[int16]string{
	1: "InstanceId",
	2: "QueryStartTime",
	3: "QueryEndTime",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeAuditLogsReq) IsSetQueryStartTime() bool {
	return p.QueryStartTime != nil
}

func (p *DescribeAuditLogsReq) IsSetQueryEndTime() bool {
	return p.QueryEndTime != nil
}

func (p *DescribeAuditLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeAuditLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeAuditLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAuditLogsReq[fieldId]))
}

func (p *DescribeAuditLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAuditLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeAuditLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeAuditLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeAuditLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeAuditLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAuditLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryStartTime() {
		if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAuditLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryEndTime() {
		if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAuditLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAuditLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAuditLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogsReq(%+v)", *p)

}

func (p *DescribeAuditLogsReq) DeepEqual(ano *DescribeAuditLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeAuditLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAuditLogsReq) Field2DeepEqual(src *string) bool {

	if p.QueryStartTime == src {
		return true
	} else if p.QueryStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAuditLogsReq) Field3DeepEqual(src *string) bool {

	if p.QueryEndTime == src {
		return true
	} else if p.QueryEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAuditLogsReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeAuditLogsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeAuditLogsResp struct {
	Total       int32               `thrift:"Total,1" frugal:"1,default,i32" json:"Total"`
	InstanceId  string              `thrift:"InstanceId,2" frugal:"2,default,string" json:"InstanceId"`
	AuditQuerys []*AuditQueryObject `thrift:"AuditQuerys,3" frugal:"3,default,list<AuditQueryObject>" json:"AuditQuerys"`
}

func NewDescribeAuditLogsResp() *DescribeAuditLogsResp {
	return &DescribeAuditLogsResp{}
}

func (p *DescribeAuditLogsResp) InitDefault() {
}

func (p *DescribeAuditLogsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeAuditLogsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAuditLogsResp) GetAuditQuerys() (v []*AuditQueryObject) {
	return p.AuditQuerys
}
func (p *DescribeAuditLogsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeAuditLogsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAuditLogsResp) SetAuditQuerys(val []*AuditQueryObject) {
	p.AuditQuerys = val
}

var fieldIDToName_DescribeAuditLogsResp = map[int16]string{
	1: "Total",
	2: "InstanceId",
	3: "AuditQuerys",
}

func (p *DescribeAuditLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAuditLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeAuditLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAuditLogsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AuditQueryObject, 0, size)
	values := make([]AuditQueryObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AuditQuerys = _field
	return nil
}

func (p *DescribeAuditLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAuditLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAuditLogsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AuditQuerys", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AuditQuerys)); err != nil {
		return err
	}
	for _, v := range p.AuditQuerys {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAuditLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogsResp(%+v)", *p)

}

func (p *DescribeAuditLogsResp) DeepEqual(ano *DescribeAuditLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.AuditQuerys) {
		return false
	}
	return true
}

func (p *DescribeAuditLogsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeAuditLogsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAuditLogsResp) Field3DeepEqual(src []*AuditQueryObject) bool {

	if len(p.AuditQuerys) != len(src) {
		return false
	}
	for i, v := range p.AuditQuerys {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeSlowLogsReq struct {
	InstanceId     string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	QueryStartTime *string `thrift:"QueryStartTime,2,optional" frugal:"2,optional,string" json:"QueryStartTime,omitempty"`
	QueryEndTime   *string `thrift:"QueryEndTime,3,optional" frugal:"3,optional,string" json:"QueryEndTime,omitempty"`
	PageNumber     *int32  `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize       *int32  `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeSlowLogsReq() *DescribeSlowLogsReq {
	return &DescribeSlowLogsReq{}
}

func (p *DescribeSlowLogsReq) InitDefault() {
}

func (p *DescribeSlowLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeSlowLogsReq_QueryStartTime_DEFAULT string

func (p *DescribeSlowLogsReq) GetQueryStartTime() (v string) {
	if !p.IsSetQueryStartTime() {
		return DescribeSlowLogsReq_QueryStartTime_DEFAULT
	}
	return *p.QueryStartTime
}

var DescribeSlowLogsReq_QueryEndTime_DEFAULT string

func (p *DescribeSlowLogsReq) GetQueryEndTime() (v string) {
	if !p.IsSetQueryEndTime() {
		return DescribeSlowLogsReq_QueryEndTime_DEFAULT
	}
	return *p.QueryEndTime
}

var DescribeSlowLogsReq_PageNumber_DEFAULT int32

func (p *DescribeSlowLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSlowLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSlowLogsReq_PageSize_DEFAULT int32

func (p *DescribeSlowLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSlowLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeSlowLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogsReq) SetQueryStartTime(val *string) {
	p.QueryStartTime = val
}
func (p *DescribeSlowLogsReq) SetQueryEndTime(val *string) {
	p.QueryEndTime = val
}
func (p *DescribeSlowLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSlowLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeSlowLogsReq = map[int16]string{
	1: "InstanceId",
	2: "QueryStartTime",
	3: "QueryEndTime",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeSlowLogsReq) IsSetQueryStartTime() bool {
	return p.QueryStartTime != nil
}

func (p *DescribeSlowLogsReq) IsSetQueryEndTime() bool {
	return p.QueryEndTime != nil
}

func (p *DescribeSlowLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSlowLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSlowLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsReq[fieldId]))
}

func (p *DescribeSlowLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeSlowLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryStartTime() {
		if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryEndTime() {
		if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsReq(%+v)", *p)

}

func (p *DescribeSlowLogsReq) DeepEqual(ano *DescribeSlowLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field2DeepEqual(src *string) bool {

	if p.QueryStartTime == src {
		return true
	} else if p.QueryStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field3DeepEqual(src *string) bool {

	if p.QueryEndTime == src {
		return true
	} else if p.QueryEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeSlowLogsResp struct {
	Total      int32              `thrift:"Total,1" frugal:"1,default,i32" json:"Total"`
	InstanceId string             `thrift:"InstanceId,2" frugal:"2,default,string" json:"InstanceId"`
	SlowQuerys []*SlowQueryObject `thrift:"SlowQuerys,3" frugal:"3,default,list<SlowQueryObject>" json:"SlowQuerys"`
}

func NewDescribeSlowLogsResp() *DescribeSlowLogsResp {
	return &DescribeSlowLogsResp{}
}

func (p *DescribeSlowLogsResp) InitDefault() {
}

func (p *DescribeSlowLogsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeSlowLogsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeSlowLogsResp) GetSlowQuerys() (v []*SlowQueryObject) {
	return p.SlowQuerys
}
func (p *DescribeSlowLogsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeSlowLogsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogsResp) SetSlowQuerys(val []*SlowQueryObject) {
	p.SlowQuerys = val
}

var fieldIDToName_DescribeSlowLogsResp = map[int16]string{
	1: "Total",
	2: "InstanceId",
	3: "SlowQuerys",
}

func (p *DescribeSlowLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSlowLogsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeSlowLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowQueryObject, 0, size)
	values := make([]SlowQueryObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowQuerys = _field
	return nil
}

func (p *DescribeSlowLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowQuerys", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowQuerys)); err != nil {
		return err
	}
	for _, v := range p.SlowQuerys {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsResp(%+v)", *p)

}

func (p *DescribeSlowLogsResp) DeepEqual(ano *DescribeSlowLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SlowQuerys) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsResp) Field3DeepEqual(src []*SlowQueryObject) bool {

	if len(p.SlowQuerys) != len(src) {
		return false
	}
	for i, v := range p.SlowQuerys {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ModifyDBInstanceSlowQueryTimeReq struct {
	InstanceId    string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	SlowQueryTime int32  `thrift:"SlowQueryTime,2,required" frugal:"2,required,i32" json:"SlowQueryTime"`
}

func NewModifyDBInstanceSlowQueryTimeReq() *ModifyDBInstanceSlowQueryTimeReq {
	return &ModifyDBInstanceSlowQueryTimeReq{}
}

func (p *ModifyDBInstanceSlowQueryTimeReq) InitDefault() {
}

func (p *ModifyDBInstanceSlowQueryTimeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceSlowQueryTimeReq) GetSlowQueryTime() (v int32) {
	return p.SlowQueryTime
}
func (p *ModifyDBInstanceSlowQueryTimeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceSlowQueryTimeReq) SetSlowQueryTime(val int32) {
	p.SlowQueryTime = val
}

var fieldIDToName_ModifyDBInstanceSlowQueryTimeReq = map[int16]string{
	1: "InstanceId",
	2: "SlowQueryTime",
}

func (p *ModifyDBInstanceSlowQueryTimeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryTimeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetSlowQueryTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowQueryTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSlowQueryTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceSlowQueryTimeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceSlowQueryTimeReq[fieldId]))
}

func (p *ModifyDBInstanceSlowQueryTimeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceSlowQueryTimeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SlowQueryTime = _field
	return nil
}

func (p *ModifyDBInstanceSlowQueryTimeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryTimeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceSlowQueryTimeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryTimeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryTimeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowQueryTime", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SlowQueryTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryTimeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSlowQueryTimeReq(%+v)", *p)

}

func (p *ModifyDBInstanceSlowQueryTimeReq) DeepEqual(ano *ModifyDBInstanceSlowQueryTimeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SlowQueryTime) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceSlowQueryTimeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSlowQueryTimeReq) Field2DeepEqual(src int32) bool {

	if p.SlowQueryTime != src {
		return false
	}
	return true
}

type ModifyDBInstanceSlowQueryTimeResp struct {
}

func NewModifyDBInstanceSlowQueryTimeResp() *ModifyDBInstanceSlowQueryTimeResp {
	return &ModifyDBInstanceSlowQueryTimeResp{}
}

func (p *ModifyDBInstanceSlowQueryTimeResp) InitDefault() {
}

var fieldIDToName_ModifyDBInstanceSlowQueryTimeResp = map[int16]string{}

func (p *ModifyDBInstanceSlowQueryTimeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryTimeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryTimeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryTimeResp")

	if err = oprot.WriteStructBegin("ModifyDBInstanceSlowQueryTimeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryTimeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSlowQueryTimeResp(%+v)", *p)

}

func (p *ModifyDBInstanceSlowQueryTimeResp) DeepEqual(ano *ModifyDBInstanceSlowQueryTimeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ModifyDBInstanceSlowQueryEnableReq struct {
	InstanceId      string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	SlowQueryEnable bool   `thrift:"SlowQueryEnable,2,required" frugal:"2,required,bool" json:"SlowQueryEnable"`
}

func NewModifyDBInstanceSlowQueryEnableReq() *ModifyDBInstanceSlowQueryEnableReq {
	return &ModifyDBInstanceSlowQueryEnableReq{}
}

func (p *ModifyDBInstanceSlowQueryEnableReq) InitDefault() {
}

func (p *ModifyDBInstanceSlowQueryEnableReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceSlowQueryEnableReq) GetSlowQueryEnable() (v bool) {
	return p.SlowQueryEnable
}
func (p *ModifyDBInstanceSlowQueryEnableReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceSlowQueryEnableReq) SetSlowQueryEnable(val bool) {
	p.SlowQueryEnable = val
}

var fieldIDToName_ModifyDBInstanceSlowQueryEnableReq = map[int16]string{
	1: "InstanceId",
	2: "SlowQueryEnable",
}

func (p *ModifyDBInstanceSlowQueryEnableReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryEnableReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetSlowQueryEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowQueryEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSlowQueryEnable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceSlowQueryEnableReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceSlowQueryEnableReq[fieldId]))
}

func (p *ModifyDBInstanceSlowQueryEnableReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceSlowQueryEnableReq) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SlowQueryEnable = _field
	return nil
}

func (p *ModifyDBInstanceSlowQueryEnableReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryEnableReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceSlowQueryEnableReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryEnableReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryEnableReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowQueryEnable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.SlowQueryEnable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryEnableReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSlowQueryEnableReq(%+v)", *p)

}

func (p *ModifyDBInstanceSlowQueryEnableReq) DeepEqual(ano *ModifyDBInstanceSlowQueryEnableReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SlowQueryEnable) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceSlowQueryEnableReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSlowQueryEnableReq) Field2DeepEqual(src bool) bool {

	if p.SlowQueryEnable != src {
		return false
	}
	return true
}

type ModifyDBInstanceSlowQueryEnableResp struct {
}

func NewModifyDBInstanceSlowQueryEnableResp() *ModifyDBInstanceSlowQueryEnableResp {
	return &ModifyDBInstanceSlowQueryEnableResp{}
}

func (p *ModifyDBInstanceSlowQueryEnableResp) InitDefault() {
}

var fieldIDToName_ModifyDBInstanceSlowQueryEnableResp = map[int16]string{}

func (p *ModifyDBInstanceSlowQueryEnableResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryEnableResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryEnableResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSlowQueryEnableResp")

	if err = oprot.WriteStructBegin("ModifyDBInstanceSlowQueryEnableResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSlowQueryEnableResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSlowQueryEnableResp(%+v)", *p)

}

func (p *ModifyDBInstanceSlowQueryEnableResp) DeepEqual(ano *ModifyDBInstanceSlowQueryEnableResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

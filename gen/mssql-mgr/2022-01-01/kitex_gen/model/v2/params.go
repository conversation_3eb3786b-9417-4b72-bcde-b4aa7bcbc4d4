// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ModifyDBInstanceParametersReq struct {
	InstanceId   string              `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	Parameters   []*ParametersObject `thrift:"Parameters,2,required" frugal:"2,required,list<ParametersObject>" json:"Parameters"`
	Forcerestart *bool               `thrift:"Forcerestart,3,optional" frugal:"3,optional,bool" json:"Forcerestart,omitempty"`
	SwitchType   *SwitchType         `thrift:"SwitchType,4,optional" frugal:"4,optional,SwitchType" json:"SwitchType,omitempty"`
}

func NewModifyDBInstanceParametersReq() *ModifyDBInstanceParametersReq {
	return &ModifyDBInstanceParametersReq{}
}

func (p *ModifyDBInstanceParametersReq) InitDefault() {
}

func (p *ModifyDBInstanceParametersReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceParametersReq) GetParameters() (v []*ParametersObject) {
	return p.Parameters
}

var ModifyDBInstanceParametersReq_Forcerestart_DEFAULT bool

func (p *ModifyDBInstanceParametersReq) GetForcerestart() (v bool) {
	if !p.IsSetForcerestart() {
		return ModifyDBInstanceParametersReq_Forcerestart_DEFAULT
	}
	return *p.Forcerestart
}

var ModifyDBInstanceParametersReq_SwitchType_DEFAULT SwitchType

func (p *ModifyDBInstanceParametersReq) GetSwitchType() (v SwitchType) {
	if !p.IsSetSwitchType() {
		return ModifyDBInstanceParametersReq_SwitchType_DEFAULT
	}
	return *p.SwitchType
}
func (p *ModifyDBInstanceParametersReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceParametersReq) SetParameters(val []*ParametersObject) {
	p.Parameters = val
}
func (p *ModifyDBInstanceParametersReq) SetForcerestart(val *bool) {
	p.Forcerestart = val
}
func (p *ModifyDBInstanceParametersReq) SetSwitchType(val *SwitchType) {
	p.SwitchType = val
}

var fieldIDToName_ModifyDBInstanceParametersReq = map[int16]string{
	1: "InstanceId",
	2: "Parameters",
	3: "Forcerestart",
	4: "SwitchType",
}

func (p *ModifyDBInstanceParametersReq) IsSetForcerestart() bool {
	return p.Forcerestart != nil
}

func (p *ModifyDBInstanceParametersReq) IsSetSwitchType() bool {
	return p.SwitchType != nil
}

func (p *ModifyDBInstanceParametersReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceParametersReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetParameters bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameters = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetParameters {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceParametersReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceParametersReq[fieldId]))
}

func (p *ModifyDBInstanceParametersReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceParametersReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ParametersObject, 0, size)
	values := make([]ParametersObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Parameters = _field
	return nil
}
func (p *ModifyDBInstanceParametersReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Forcerestart = _field
	return nil
}
func (p *ModifyDBInstanceParametersReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SwitchType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SwitchType(v)
		_field = &tmp
	}
	p.SwitchType = _field
	return nil
}

func (p *ModifyDBInstanceParametersReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceParametersReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceParametersReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceParametersReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceParametersReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Parameters", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Parameters)); err != nil {
		return err
	}
	for _, v := range p.Parameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceParametersReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetForcerestart() {
		if err = oprot.WriteFieldBegin("Forcerestart", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Forcerestart); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceParametersReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSwitchType() {
		if err = oprot.WriteFieldBegin("SwitchType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SwitchType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBInstanceParametersReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceParametersReq(%+v)", *p)

}

func (p *ModifyDBInstanceParametersReq) DeepEqual(ano *ModifyDBInstanceParametersReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Parameters) {
		return false
	}
	if !p.Field3DeepEqual(ano.Forcerestart) {
		return false
	}
	if !p.Field4DeepEqual(ano.SwitchType) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceParametersReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceParametersReq) Field2DeepEqual(src []*ParametersObject) bool {

	if len(p.Parameters) != len(src) {
		return false
	}
	for i, v := range p.Parameters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyDBInstanceParametersReq) Field3DeepEqual(src *bool) bool {

	if p.Forcerestart == src {
		return true
	} else if p.Forcerestart == nil || src == nil {
		return false
	}
	if *p.Forcerestart != *src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceParametersReq) Field4DeepEqual(src *SwitchType) bool {

	if p.SwitchType == src {
		return true
	} else if p.SwitchType == nil || src == nil {
		return false
	}
	if *p.SwitchType != *src {
		return false
	}
	return true
}

type ModifyDBInstanceParametersResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
}

func NewModifyDBInstanceParametersResp() *ModifyDBInstanceParametersResp {
	return &ModifyDBInstanceParametersResp{}
}

func (p *ModifyDBInstanceParametersResp) InitDefault() {
}

func (p *ModifyDBInstanceParametersResp) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ModifyDBInstanceParametersResp) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ModifyDBInstanceParametersResp = map[int16]string{
	1: "InstanceId",
}

func (p *ModifyDBInstanceParametersResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceParametersResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceParametersResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceParametersResp[fieldId]))
}

func (p *ModifyDBInstanceParametersResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ModifyDBInstanceParametersResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceParametersResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceParametersResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceParametersResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceParametersResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceParametersResp(%+v)", *p)

}

func (p *ModifyDBInstanceParametersResp) DeepEqual(ano *ModifyDBInstanceParametersResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceParametersResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceParametersReq struct {
	InstanceId     string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	ParameterNames *string `thrift:"ParameterNames,2,optional" frugal:"2,optional,string" json:"ParameterNames,omitempty"`
}

func NewDescribeDBInstanceParametersReq() *DescribeDBInstanceParametersReq {
	return &DescribeDBInstanceParametersReq{}
}

func (p *DescribeDBInstanceParametersReq) InitDefault() {
}

func (p *DescribeDBInstanceParametersReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeDBInstanceParametersReq_ParameterNames_DEFAULT string

func (p *DescribeDBInstanceParametersReq) GetParameterNames() (v string) {
	if !p.IsSetParameterNames() {
		return DescribeDBInstanceParametersReq_ParameterNames_DEFAULT
	}
	return *p.ParameterNames
}
func (p *DescribeDBInstanceParametersReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceParametersReq) SetParameterNames(val *string) {
	p.ParameterNames = val
}

var fieldIDToName_DescribeDBInstanceParametersReq = map[int16]string{
	1: "InstanceId",
	2: "ParameterNames",
}

func (p *DescribeDBInstanceParametersReq) IsSetParameterNames() bool {
	return p.ParameterNames != nil
}

func (p *DescribeDBInstanceParametersReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceParametersReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceParametersReq[fieldId]))
}

func (p *DescribeDBInstanceParametersReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceParametersReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParameterNames = _field
	return nil
}

func (p *DescribeDBInstanceParametersReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceParametersReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceParametersReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetParameterNames() {
		if err = oprot.WriteFieldBegin("ParameterNames", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParameterNames); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceParametersReq(%+v)", *p)

}

func (p *DescribeDBInstanceParametersReq) DeepEqual(ano *DescribeDBInstanceParametersReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ParameterNames) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceParametersReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersReq) Field2DeepEqual(src *string) bool {

	if p.ParameterNames == src {
		return true
	} else if p.ParameterNames == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParameterNames, *src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceParametersResp struct {
	InstanceId         string                      `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DBEngine           string                      `thrift:"DBEngine,2,required" frugal:"2,required,string" json:"DBEngine"`
	DBEngineVersion    string                      `thrift:"DBEngineVersion,3,required" frugal:"3,required,string" json:"DBEngineVersion"`
	ParameterCount     int32                       `thrift:"ParameterCount,4,required" frugal:"4,required,i32" json:"ParameterCount"`
	InstanceParameters []*InstanceParametersObject `thrift:"InstanceParameters,5,required" frugal:"5,required,list<InstanceParametersObject>" json:"InstanceParameters"`
}

func NewDescribeDBInstanceParametersResp() *DescribeDBInstanceParametersResp {
	return &DescribeDBInstanceParametersResp{}
}

func (p *DescribeDBInstanceParametersResp) InitDefault() {
}

func (p *DescribeDBInstanceParametersResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBInstanceParametersResp) GetDBEngine() (v string) {
	return p.DBEngine
}

func (p *DescribeDBInstanceParametersResp) GetDBEngineVersion() (v string) {
	return p.DBEngineVersion
}

func (p *DescribeDBInstanceParametersResp) GetParameterCount() (v int32) {
	return p.ParameterCount
}

func (p *DescribeDBInstanceParametersResp) GetInstanceParameters() (v []*InstanceParametersObject) {
	return p.InstanceParameters
}
func (p *DescribeDBInstanceParametersResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceParametersResp) SetDBEngine(val string) {
	p.DBEngine = val
}
func (p *DescribeDBInstanceParametersResp) SetDBEngineVersion(val string) {
	p.DBEngineVersion = val
}
func (p *DescribeDBInstanceParametersResp) SetParameterCount(val int32) {
	p.ParameterCount = val
}
func (p *DescribeDBInstanceParametersResp) SetInstanceParameters(val []*InstanceParametersObject) {
	p.InstanceParameters = val
}

var fieldIDToName_DescribeDBInstanceParametersResp = map[int16]string{
	1: "InstanceId",
	2: "DBEngine",
	3: "DBEngineVersion",
	4: "ParameterCount",
	5: "InstanceParameters",
}

func (p *DescribeDBInstanceParametersResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBEngine bool = false
	var issetDBEngineVersion bool = false
	var issetParameterCount bool = false
	var issetInstanceParameters bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngine = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameterCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceParameters = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBEngine {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineVersion {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetParameterCount {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceParameters {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceParametersResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceParametersResp[fieldId]))
}

func (p *DescribeDBInstanceParametersResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceParametersResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBEngine = _field
	return nil
}
func (p *DescribeDBInstanceParametersResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *DescribeDBInstanceParametersResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParameterCount = _field
	return nil
}
func (p *DescribeDBInstanceParametersResp) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParametersObject, 0, size)
	values := make([]InstanceParametersObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceParameters = _field
	return nil
}

func (p *DescribeDBInstanceParametersResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceParametersResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngine", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBEngine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBEngineVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParameterCount", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ParameterCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceParameters", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceParameters)); err != nil {
		return err
	}
	for _, v := range p.InstanceParameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceParametersResp(%+v)", *p)

}

func (p *DescribeDBInstanceParametersResp) DeepEqual(ano *DescribeDBInstanceParametersResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBEngine) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field4DeepEqual(ano.ParameterCount) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceParameters) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceParametersResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBEngine, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBEngineVersion, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersResp) Field4DeepEqual(src int32) bool {

	if p.ParameterCount != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersResp) Field5DeepEqual(src []*InstanceParametersObject) bool {

	if len(p.InstanceParameters) != len(src) {
		return false
	}
	for i, v := range p.InstanceParameters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDBInstanceParametersLogReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	StartTime  string `thrift:"StartTime,2,required" frugal:"2,required,string" validate:"required"`
	EndTime    string `thrift:"EndTime,3,required" frugal:"3,required,string" validate:"required"`
	PageNumber *int32 `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32 `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeDBInstanceParametersLogReq() *DescribeDBInstanceParametersLogReq {
	return &DescribeDBInstanceParametersLogReq{}
}

func (p *DescribeDBInstanceParametersLogReq) InitDefault() {
}

func (p *DescribeDBInstanceParametersLogReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBInstanceParametersLogReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *DescribeDBInstanceParametersLogReq) GetEndTime() (v string) {
	return p.EndTime
}

var DescribeDBInstanceParametersLogReq_PageNumber_DEFAULT int32

func (p *DescribeDBInstanceParametersLogReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBInstanceParametersLogReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDBInstanceParametersLogReq_PageSize_DEFAULT int32

func (p *DescribeDBInstanceParametersLogReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBInstanceParametersLogReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeDBInstanceParametersLogReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceParametersLogReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *DescribeDBInstanceParametersLogReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *DescribeDBInstanceParametersLogReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDBInstanceParametersLogReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeDBInstanceParametersLogReq = map[int16]string{
	1: "InstanceId",
	2: "StartTime",
	3: "EndTime",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeDBInstanceParametersLogReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBInstanceParametersLogReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBInstanceParametersLogReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersLogReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceParametersLogReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceParametersLogReq[fieldId]))
}

func (p *DescribeDBInstanceParametersLogReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceParametersLogReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeDBInstanceParametersLogReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeDBInstanceParametersLogReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDBInstanceParametersLogReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeDBInstanceParametersLogReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersLogReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceParametersLogReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceParametersLogReq(%+v)", *p)

}

func (p *DescribeDBInstanceParametersLogReq) DeepEqual(ano *DescribeDBInstanceParametersLogReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceParametersLogReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersLogReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersLogReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersLogReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersLogReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeDBInstanceParametersLogResp struct {
	Total               int32                       `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	ParameterChangeLogs []*ParameterChangeLogObject `thrift:"ParameterChangeLogs,2,required" frugal:"2,required,list<ParameterChangeLogObject>" json:"ParameterChangeLogs"`
}

func NewDescribeDBInstanceParametersLogResp() *DescribeDBInstanceParametersLogResp {
	return &DescribeDBInstanceParametersLogResp{}
}

func (p *DescribeDBInstanceParametersLogResp) InitDefault() {
}

func (p *DescribeDBInstanceParametersLogResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeDBInstanceParametersLogResp) GetParameterChangeLogs() (v []*ParameterChangeLogObject) {
	return p.ParameterChangeLogs
}
func (p *DescribeDBInstanceParametersLogResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeDBInstanceParametersLogResp) SetParameterChangeLogs(val []*ParameterChangeLogObject) {
	p.ParameterChangeLogs = val
}

var fieldIDToName_DescribeDBInstanceParametersLogResp = map[int16]string{
	1: "Total",
	2: "ParameterChangeLogs",
}

func (p *DescribeDBInstanceParametersLogResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersLogResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetParameterChangeLogs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameterChangeLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetParameterChangeLogs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceParametersLogResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceParametersLogResp[fieldId]))
}

func (p *DescribeDBInstanceParametersLogResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeDBInstanceParametersLogResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ParameterChangeLogObject, 0, size)
	values := make([]ParameterChangeLogObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ParameterChangeLogs = _field
	return nil
}

func (p *DescribeDBInstanceParametersLogResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceParametersLogResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceParametersLogResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParameterChangeLogs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ParameterChangeLogs)); err != nil {
		return err
	}
	for _, v := range p.ParameterChangeLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceParametersLogResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceParametersLogResp(%+v)", *p)

}

func (p *DescribeDBInstanceParametersLogResp) DeepEqual(ano *DescribeDBInstanceParametersLogResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.ParameterChangeLogs) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceParametersLogResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceParametersLogResp) Field2DeepEqual(src []*ParameterChangeLogObject) bool {

	if len(p.ParameterChangeLogs) != len(src) {
		return false
	}
	for i, v := range p.ParameterChangeLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

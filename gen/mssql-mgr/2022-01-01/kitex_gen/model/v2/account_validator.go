// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDBAccountReq) IsValid() error {
	return nil
}
func (p *CreateDBAccountResp) IsValid() error {
	return nil
}
func (p *DeleteDBAccountReq) IsValid() error {
	return nil
}
func (p *DeleteDBAccountResp) IsValid() error {
	return nil
}
func (p *DescribeDBAccountsReq) IsValid() error {
	return nil
}
func (p *DescribeDBAccountsResp) IsValid() error {
	return nil
}
func (p *ResetDBAccountReq) IsValid() error {
	return nil
}
func (p *ResetDBAccountResp) IsValid() error {
	return nil
}
func (p *GrantDBAccountPrivilegeReq) IsValid() error {
	return nil
}
func (p *GrantDBAccountPrivilegeResp) IsValid() error {
	return nil
}
func (p *RevokeDBAccountPrivilegeReq) IsValid() error {
	return nil
}
func (p *RevokeDBAccountPrivilegeResp) IsValid() error {
	return nil
}

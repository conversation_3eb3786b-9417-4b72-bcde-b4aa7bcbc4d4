// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateDBAccountReq struct {
	InstanceId        string                    `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName       string                    `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required,min=2,max=128"`
	AccountPassword   string                    `thrift:"AccountPassword,3,required" frugal:"3,required,string" validate:"required,min=8,max=32"`
	AccountType       *AccountType              `thrift:"AccountType,4,optional" frugal:"4,optional,AccountType" json:"AccountType,omitempty"`
	AccountPrivileges []*AccountPrivilegeObject `thrift:"AccountPrivileges,5,optional" frugal:"5,optional,list<AccountPrivilegeObject>" json:"AccountPrivileges,omitempty"`
	SID               *string                   `thrift:"SID,6,optional" frugal:"6,optional,string" json:"SID,omitempty"`
}

func NewCreateDBAccountReq() *CreateDBAccountReq {
	return &CreateDBAccountReq{}
}

func (p *CreateDBAccountReq) InitDefault() {
}

func (p *CreateDBAccountReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDBAccountReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *CreateDBAccountReq) GetAccountPassword() (v string) {
	return p.AccountPassword
}

var CreateDBAccountReq_AccountType_DEFAULT AccountType

func (p *CreateDBAccountReq) GetAccountType() (v AccountType) {
	if !p.IsSetAccountType() {
		return CreateDBAccountReq_AccountType_DEFAULT
	}
	return *p.AccountType
}

var CreateDBAccountReq_AccountPrivileges_DEFAULT []*AccountPrivilegeObject

func (p *CreateDBAccountReq) GetAccountPrivileges() (v []*AccountPrivilegeObject) {
	if !p.IsSetAccountPrivileges() {
		return CreateDBAccountReq_AccountPrivileges_DEFAULT
	}
	return p.AccountPrivileges
}

var CreateDBAccountReq_SID_DEFAULT string

func (p *CreateDBAccountReq) GetSID() (v string) {
	if !p.IsSetSID() {
		return CreateDBAccountReq_SID_DEFAULT
	}
	return *p.SID
}
func (p *CreateDBAccountReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDBAccountReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *CreateDBAccountReq) SetAccountPassword(val string) {
	p.AccountPassword = val
}
func (p *CreateDBAccountReq) SetAccountType(val *AccountType) {
	p.AccountType = val
}
func (p *CreateDBAccountReq) SetAccountPrivileges(val []*AccountPrivilegeObject) {
	p.AccountPrivileges = val
}
func (p *CreateDBAccountReq) SetSID(val *string) {
	p.SID = val
}

var fieldIDToName_CreateDBAccountReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccountPassword",
	4: "AccountType",
	5: "AccountPrivileges",
	6: "SID",
}

func (p *CreateDBAccountReq) IsSetAccountType() bool {
	return p.AccountType != nil
}

func (p *CreateDBAccountReq) IsSetAccountPrivileges() bool {
	return p.AccountPrivileges != nil
}

func (p *CreateDBAccountReq) IsSetSID() bool {
	return p.SID != nil
}

func (p *CreateDBAccountReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBAccountReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetAccountPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountPassword {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDBAccountReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDBAccountReq[fieldId]))
}

func (p *CreateDBAccountReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDBAccountReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *CreateDBAccountReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountPassword = _field
	return nil
}
func (p *CreateDBAccountReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *AccountType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AccountType(v)
		_field = &tmp
	}
	p.AccountType = _field
	return nil
}
func (p *CreateDBAccountReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AccountPrivilegeObject, 0, size)
	values := make([]AccountPrivilegeObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AccountPrivileges = _field
	return nil
}
func (p *CreateDBAccountReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SID = _field
	return nil
}

func (p *CreateDBAccountReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBAccountReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDBAccountReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPassword", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountPassword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountType() {
		if err = oprot.WriteFieldBegin("AccountType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AccountType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountPrivileges() {
		if err = oprot.WriteFieldBegin("AccountPrivileges", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AccountPrivileges)); err != nil {
			return err
		}
		for _, v := range p.AccountPrivileges {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateDBAccountReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSID() {
		if err = oprot.WriteFieldBegin("SID", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateDBAccountReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBAccountReq(%+v)", *p)

}

func (p *CreateDBAccountReq) DeepEqual(ano *CreateDBAccountReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountPassword) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AccountPrivileges) {
		return false
	}
	if !p.Field6DeepEqual(ano.SID) {
		return false
	}
	return true
}

func (p *CreateDBAccountReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBAccountReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBAccountReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountPassword, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDBAccountReq) Field4DeepEqual(src *AccountType) bool {

	if p.AccountType == src {
		return true
	} else if p.AccountType == nil || src == nil {
		return false
	}
	if *p.AccountType != *src {
		return false
	}
	return true
}
func (p *CreateDBAccountReq) Field5DeepEqual(src []*AccountPrivilegeObject) bool {

	if len(p.AccountPrivileges) != len(src) {
		return false
	}
	for i, v := range p.AccountPrivileges {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateDBAccountReq) Field6DeepEqual(src *string) bool {

	if p.SID == src {
		return true
	} else if p.SID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SID, *src) != 0 {
		return false
	}
	return true
}

type CreateDBAccountResp struct {
}

func NewCreateDBAccountResp() *CreateDBAccountResp {
	return &CreateDBAccountResp{}
}

func (p *CreateDBAccountResp) InitDefault() {
}

var fieldIDToName_CreateDBAccountResp = map[int16]string{}

func (p *CreateDBAccountResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBAccountResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateDBAccountResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDBAccountResp")

	if err = oprot.WriteStructBegin("CreateDBAccountResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDBAccountResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDBAccountResp(%+v)", *p)

}

func (p *CreateDBAccountResp) DeepEqual(ano *CreateDBAccountResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteDBAccountReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteDBAccountReq() *DeleteDBAccountReq {
	return &DeleteDBAccountReq{}
}

func (p *DeleteDBAccountReq) InitDefault() {
}

func (p *DeleteDBAccountReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBAccountReq) GetAccountName() (v string) {
	return p.AccountName
}
func (p *DeleteDBAccountReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBAccountReq) SetAccountName(val string) {
	p.AccountName = val
}

var fieldIDToName_DeleteDBAccountReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
}

func (p *DeleteDBAccountReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBAccountReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBAccountReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBAccountReq[fieldId]))
}

func (p *DeleteDBAccountReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBAccountReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}

func (p *DeleteDBAccountReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBAccountReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBAccountReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBAccountReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBAccountReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBAccountReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBAccountReq(%+v)", *p)

}

func (p *DeleteDBAccountReq) DeepEqual(ano *DeleteDBAccountReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	return true
}

func (p *DeleteDBAccountReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBAccountReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}

type DeleteDBAccountResp struct {
}

func NewDeleteDBAccountResp() *DeleteDBAccountResp {
	return &DeleteDBAccountResp{}
}

func (p *DeleteDBAccountResp) InitDefault() {
}

var fieldIDToName_DeleteDBAccountResp = map[int16]string{}

func (p *DeleteDBAccountResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBAccountResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteDBAccountResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBAccountResp")

	if err = oprot.WriteStructBegin("DeleteDBAccountResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBAccountResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBAccountResp(%+v)", *p)

}

func (p *DeleteDBAccountResp) DeepEqual(ano *DeleteDBAccountResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeDBAccountsReq struct {
	InstanceId  string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName *string `thrift:"AccountName,2,optional" frugal:"2,optional,string" json:"AccountName,omitempty"`
	PageSize    *int32  `thrift:"PageSize,3,optional" frugal:"3,optional,i32" json:"PageSize,omitempty"`
	PageNumber  *int32  `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
}

func NewDescribeDBAccountsReq() *DescribeDBAccountsReq {
	return &DescribeDBAccountsReq{}
}

func (p *DescribeDBAccountsReq) InitDefault() {
}

func (p *DescribeDBAccountsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeDBAccountsReq_AccountName_DEFAULT string

func (p *DescribeDBAccountsReq) GetAccountName() (v string) {
	if !p.IsSetAccountName() {
		return DescribeDBAccountsReq_AccountName_DEFAULT
	}
	return *p.AccountName
}

var DescribeDBAccountsReq_PageSize_DEFAULT int32

func (p *DescribeDBAccountsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBAccountsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDBAccountsReq_PageNumber_DEFAULT int32

func (p *DescribeDBAccountsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBAccountsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}
func (p *DescribeDBAccountsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBAccountsReq) SetAccountName(val *string) {
	p.AccountName = val
}
func (p *DescribeDBAccountsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDBAccountsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}

var fieldIDToName_DescribeDBAccountsReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "PageSize",
	4: "PageNumber",
}

func (p *DescribeDBAccountsReq) IsSetAccountName() bool {
	return p.AccountName != nil
}

func (p *DescribeDBAccountsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBAccountsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBAccountsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAccountsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAccountsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAccountsReq[fieldId]))
}

func (p *DescribeDBAccountsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBAccountsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountName = _field
	return nil
}
func (p *DescribeDBAccountsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDBAccountsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}

func (p *DescribeDBAccountsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAccountsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAccountsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAccountsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAccountsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountName() {
		if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAccountsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBAccountsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBAccountsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAccountsReq(%+v)", *p)

}

func (p *DescribeDBAccountsReq) DeepEqual(ano *DescribeDBAccountsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	return true
}

func (p *DescribeDBAccountsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAccountsReq) Field2DeepEqual(src *string) bool {

	if p.AccountName == src {
		return true
	} else if p.AccountName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAccountsReq) Field3DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDBAccountsReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}

type DescribeDBAccountsResp struct {
	Total   int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Account []*AccountObject `thrift:"Account,2,required" frugal:"2,required,list<AccountObject>" json:"Account"`
}

func NewDescribeDBAccountsResp() *DescribeDBAccountsResp {
	return &DescribeDBAccountsResp{}
}

func (p *DescribeDBAccountsResp) InitDefault() {
}

func (p *DescribeDBAccountsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeDBAccountsResp) GetAccount() (v []*AccountObject) {
	return p.Account
}
func (p *DescribeDBAccountsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeDBAccountsResp) SetAccount(val []*AccountObject) {
	p.Account = val
}

var fieldIDToName_DescribeDBAccountsResp = map[int16]string{
	1: "Total",
	2: "Account",
}

func (p *DescribeDBAccountsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAccountsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetAccount bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccount {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAccountsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAccountsResp[fieldId]))
}

func (p *DescribeDBAccountsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeDBAccountsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AccountObject, 0, size)
	values := make([]AccountObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Account = _field
	return nil
}

func (p *DescribeDBAccountsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAccountsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAccountsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAccountsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAccountsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Account", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Account)); err != nil {
		return err
	}
	for _, v := range p.Account {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAccountsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAccountsResp(%+v)", *p)

}

func (p *DescribeDBAccountsResp) DeepEqual(ano *DescribeDBAccountsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Account) {
		return false
	}
	return true
}

func (p *DescribeDBAccountsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeDBAccountsResp) Field2DeepEqual(src []*AccountObject) bool {

	if len(p.Account) != len(src) {
		return false
	}
	for i, v := range p.Account {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ResetDBAccountReq struct {
	InstanceId      string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName     string       `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	AccountPassword string       `thrift:"AccountPassword,3,required" frugal:"3,required,string" json:"AccountPassword"`
	AccountType     *AccountType `thrift:"AccountType,4,optional" frugal:"4,optional,AccountType" json:"AccountType,omitempty"`
}

func NewResetDBAccountReq() *ResetDBAccountReq {
	return &ResetDBAccountReq{}
}

func (p *ResetDBAccountReq) InitDefault() {
}

func (p *ResetDBAccountReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ResetDBAccountReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *ResetDBAccountReq) GetAccountPassword() (v string) {
	return p.AccountPassword
}

var ResetDBAccountReq_AccountType_DEFAULT AccountType

func (p *ResetDBAccountReq) GetAccountType() (v AccountType) {
	if !p.IsSetAccountType() {
		return ResetDBAccountReq_AccountType_DEFAULT
	}
	return *p.AccountType
}
func (p *ResetDBAccountReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ResetDBAccountReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *ResetDBAccountReq) SetAccountPassword(val string) {
	p.AccountPassword = val
}
func (p *ResetDBAccountReq) SetAccountType(val *AccountType) {
	p.AccountType = val
}

var fieldIDToName_ResetDBAccountReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccountPassword",
	4: "AccountType",
}

func (p *ResetDBAccountReq) IsSetAccountType() bool {
	return p.AccountType != nil
}

func (p *ResetDBAccountReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetDBAccountReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetAccountPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountPassword {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetDBAccountReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResetDBAccountReq[fieldId]))
}

func (p *ResetDBAccountReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ResetDBAccountReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *ResetDBAccountReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountPassword = _field
	return nil
}
func (p *ResetDBAccountReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *AccountType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AccountType(v)
		_field = &tmp
	}
	p.AccountType = _field
	return nil
}

func (p *ResetDBAccountReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetDBAccountReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetDBAccountReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResetDBAccountReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResetDBAccountReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ResetDBAccountReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPassword", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountPassword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ResetDBAccountReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountType() {
		if err = oprot.WriteFieldBegin("AccountType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AccountType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ResetDBAccountReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetDBAccountReq(%+v)", *p)

}

func (p *ResetDBAccountReq) DeepEqual(ano *ResetDBAccountReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountPassword) {
		return false
	}
	if !p.Field4DeepEqual(ano.AccountType) {
		return false
	}
	return true
}

func (p *ResetDBAccountReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ResetDBAccountReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *ResetDBAccountReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountPassword, src) != 0 {
		return false
	}
	return true
}
func (p *ResetDBAccountReq) Field4DeepEqual(src *AccountType) bool {

	if p.AccountType == src {
		return true
	} else if p.AccountType == nil || src == nil {
		return false
	}
	if *p.AccountType != *src {
		return false
	}
	return true
}

type ResetDBAccountResp struct {
}

func NewResetDBAccountResp() *ResetDBAccountResp {
	return &ResetDBAccountResp{}
}

func (p *ResetDBAccountResp) InitDefault() {
}

var fieldIDToName_ResetDBAccountResp = map[int16]string{}

func (p *ResetDBAccountResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetDBAccountResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResetDBAccountResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetDBAccountResp")

	if err = oprot.WriteStructBegin("ResetDBAccountResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResetDBAccountResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetDBAccountResp(%+v)", *p)

}

func (p *ResetDBAccountResp) DeepEqual(ano *ResetDBAccountResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type GrantDBAccountPrivilegeReq struct {
	InstanceId        string                    `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName       string                    `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	AccountPrivileges []*AccountPrivilegeObject `thrift:"AccountPrivileges,3,required" frugal:"3,required,list<AccountPrivilegeObject>" json:"AccountPrivileges"`
}

func NewGrantDBAccountPrivilegeReq() *GrantDBAccountPrivilegeReq {
	return &GrantDBAccountPrivilegeReq{}
}

func (p *GrantDBAccountPrivilegeReq) InitDefault() {
}

func (p *GrantDBAccountPrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GrantDBAccountPrivilegeReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *GrantDBAccountPrivilegeReq) GetAccountPrivileges() (v []*AccountPrivilegeObject) {
	return p.AccountPrivileges
}
func (p *GrantDBAccountPrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GrantDBAccountPrivilegeReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *GrantDBAccountPrivilegeReq) SetAccountPrivileges(val []*AccountPrivilegeObject) {
	p.AccountPrivileges = val
}

var fieldIDToName_GrantDBAccountPrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "AccountPrivileges",
}

func (p *GrantDBAccountPrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantDBAccountPrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetAccountPrivileges bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountPrivileges = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountPrivileges {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GrantDBAccountPrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GrantDBAccountPrivilegeReq[fieldId]))
}

func (p *GrantDBAccountPrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GrantDBAccountPrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *GrantDBAccountPrivilegeReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AccountPrivilegeObject, 0, size)
	values := make([]AccountPrivilegeObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AccountPrivileges = _field
	return nil
}

func (p *GrantDBAccountPrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantDBAccountPrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GrantDBAccountPrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountPrivileges", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AccountPrivileges)); err != nil {
		return err
	}
	for _, v := range p.AccountPrivileges {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GrantDBAccountPrivilegeReq(%+v)", *p)

}

func (p *GrantDBAccountPrivilegeReq) DeepEqual(ano *GrantDBAccountPrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountPrivileges) {
		return false
	}
	return true
}

func (p *GrantDBAccountPrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GrantDBAccountPrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *GrantDBAccountPrivilegeReq) Field3DeepEqual(src []*AccountPrivilegeObject) bool {

	if len(p.AccountPrivileges) != len(src) {
		return false
	}
	for i, v := range p.AccountPrivileges {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type GrantDBAccountPrivilegeResp struct {
}

func NewGrantDBAccountPrivilegeResp() *GrantDBAccountPrivilegeResp {
	return &GrantDBAccountPrivilegeResp{}
}

func (p *GrantDBAccountPrivilegeResp) InitDefault() {
}

var fieldIDToName_GrantDBAccountPrivilegeResp = map[int16]string{}

func (p *GrantDBAccountPrivilegeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantDBAccountPrivilegeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GrantDBAccountPrivilegeResp")

	if err = oprot.WriteStructBegin("GrantDBAccountPrivilegeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GrantDBAccountPrivilegeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GrantDBAccountPrivilegeResp(%+v)", *p)

}

func (p *GrantDBAccountPrivilegeResp) DeepEqual(ano *GrantDBAccountPrivilegeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type RevokeDBAccountPrivilegeReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" validate:"required"`
	DBNames     string `thrift:"DBNames,3,required" frugal:"3,required,string" json:"DBNames"`
}

func NewRevokeDBAccountPrivilegeReq() *RevokeDBAccountPrivilegeReq {
	return &RevokeDBAccountPrivilegeReq{}
}

func (p *RevokeDBAccountPrivilegeReq) InitDefault() {
}

func (p *RevokeDBAccountPrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RevokeDBAccountPrivilegeReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *RevokeDBAccountPrivilegeReq) GetDBNames() (v string) {
	return p.DBNames
}
func (p *RevokeDBAccountPrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RevokeDBAccountPrivilegeReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *RevokeDBAccountPrivilegeReq) SetDBNames(val string) {
	p.DBNames = val
}

var fieldIDToName_RevokeDBAccountPrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "AccountName",
	3: "DBNames",
}

func (p *RevokeDBAccountPrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeDBAccountPrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAccountName bool = false
	var issetDBNames bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBNames = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBNames {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RevokeDBAccountPrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RevokeDBAccountPrivilegeReq[fieldId]))
}

func (p *RevokeDBAccountPrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RevokeDBAccountPrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *RevokeDBAccountPrivilegeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBNames = _field
	return nil
}

func (p *RevokeDBAccountPrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeDBAccountPrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RevokeDBAccountPrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBNames", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBNames); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RevokeDBAccountPrivilegeReq(%+v)", *p)

}

func (p *RevokeDBAccountPrivilegeReq) DeepEqual(ano *RevokeDBAccountPrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBNames) {
		return false
	}
	return true
}

func (p *RevokeDBAccountPrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RevokeDBAccountPrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *RevokeDBAccountPrivilegeReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBNames, src) != 0 {
		return false
	}
	return true
}

type RevokeDBAccountPrivilegeResp struct {
}

func NewRevokeDBAccountPrivilegeResp() *RevokeDBAccountPrivilegeResp {
	return &RevokeDBAccountPrivilegeResp{}
}

func (p *RevokeDBAccountPrivilegeResp) InitDefault() {
}

var fieldIDToName_RevokeDBAccountPrivilegeResp = map[int16]string{}

func (p *RevokeDBAccountPrivilegeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeDBAccountPrivilegeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RevokeDBAccountPrivilegeResp")

	if err = oprot.WriteStructBegin("RevokeDBAccountPrivilegeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RevokeDBAccountPrivilegeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RevokeDBAccountPrivilegeResp(%+v)", *p)

}

func (p *RevokeDBAccountPrivilegeResp) DeepEqual(ano *RevokeDBAccountPrivilegeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

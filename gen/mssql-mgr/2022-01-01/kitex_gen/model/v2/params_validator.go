// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ModifyDBInstanceParametersReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceParametersResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceParametersReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceParametersResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceParametersLogReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceParametersLogResp) IsValid() error {
	return nil
}

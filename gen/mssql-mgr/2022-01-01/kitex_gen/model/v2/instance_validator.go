// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateDBInstanceReq) IsValid() error {
	if p.ChargeInfo != nil {
		if err := p.ChargeInfo.IsValid(); err != nil {
			return fmt.Errorf("field ChargeInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateDBInstanceResp) IsValid() error {
	return nil
}
func (p *CreateReadOnlyDBInstanceReq) IsValid() error {
	if p.ChargeInfo != nil {
		if err := p.ChargeInfo.IsValid(); err != nil {
			return fmt.Errorf("field ChargeInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateReadOnlyDBInstanceResp) IsValid() error {
	return nil
}
func (p *RebuildDBInstanceReq) IsValid() error {
	if p.ChargeInfo != nil {
		if err := p.ChargeInfo.IsValid(); err != nil {
			return fmt.Errorf("field ChargeInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *RebuildDBInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstancesReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceSpecsReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceSpecsResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSpecReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceSpecResp) IsValid() error {
	return nil
}
func (p *ModifyDBFailoverReq) IsValid() error {
	return nil
}
func (p *ModifyDBFailoverResp) IsValid() error {
	return nil
}
func (p *DeleteDBInstanceReq) IsValid() error {
	return nil
}
func (p *DeleteDBInstanceResp) IsValid() error {
	return nil
}
func (p *RestartDBInstanceReq) IsValid() error {
	return nil
}
func (p *RestartDBInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceDetailReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceDetailResp) IsValid() error {
	if p.BasicInfo != nil {
		if err := p.BasicInfo.IsValid(); err != nil {
			return fmt.Errorf("field BasicInfo not valid, %w", err)
		}
	}
	if p.ChargeDetail != nil {
		if err := p.ChargeDetail.IsValid(); err != nil {
			return fmt.Errorf("field ChargeDetail not valid, %w", err)
		}
	}
	return nil
}
func (p *VerifyBYOLInstanceReq) IsValid() error {
	return nil
}
func (p *VerifyBYOLInstanceResp) IsValid() error {
	return nil
}
func (p *ModifyInstanceAdvancedFeaturesReq) IsValid() error {
	return nil
}
func (p *ModifyInstanceAdvancedFeaturesResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePriceDetailReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstancePriceDetailResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceChargeTypeReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceChargeTypeResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceNameReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceNameResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceShardsReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstanceShardsResp) IsValid() error {
	return nil
}
func (p *DescribeDBInstancesForAllowListInnerReq) IsValid() error {
	return nil
}
func (p *DescribeDBInstancesForAllowListInnerResp) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceMaintenanceWindowReq) IsValid() error {
	return nil
}
func (p *ModifyDBInstanceMaintenanceWindowResp) IsValid() error {
	return nil
}
func (p *AddTagsToResourceReq) IsValid() error {
	return nil
}
func (p *AddTagsToResourceResp) IsValid() error {
	return nil
}
func (p *RemoveTagsFromResourceReq) IsValid() error {
	return nil
}
func (p *RemoveTagsFromResourceResp) IsValid() error {
	return nil
}
func (p *DescribeTagsByResourceReq) IsValid() error {
	return nil
}
func (p *DescribeTagsByResourceResp) IsValid() error {
	return nil
}
func (p *TagFilterObject) IsValid() error {
	return nil
}
func (p *TagResourceObject) IsValid() error {
	return nil
}
func (p *UpgradeMSSQLAgentVersionReq) IsValid() error {
	return nil
}
func (p *UpgradeMSSQLAgentVersionResp) IsValid() error {
	return nil
}
func (p *ListUpgradeAgentResultReq) IsValid() error {
	return nil
}
func (p *ListUpgradeAgentResultResp) IsValid() error {
	return nil
}
func (p *InnerDescribeInstanceECSInfosReq) IsValid() error {
	return nil
}
func (p *InnerDescribeInstanceECSInfosResp) IsValid() error {
	return nil
}
func (p *InstanceEcsInfo) IsValid() error {
	return nil
}
func (p *EbsInfo) IsValid() error {
	if p.BaselinePerformance != nil {
		if err := p.BaselinePerformance.IsValid(); err != nil {
			return fmt.Errorf("field BaselinePerformance not valid, %w", err)
		}
	}
	if p.ExtraPerformance != nil {
		if err := p.ExtraPerformance.IsValid(); err != nil {
			return fmt.Errorf("field ExtraPerformance not valid, %w", err)
		}
	}
	if p.TotalPerformance != nil {
		if err := p.TotalPerformance.IsValid(); err != nil {
			return fmt.Errorf("field TotalPerformance not valid, %w", err)
		}
	}
	return nil
}
func (p *Performance) IsValid() error {
	return nil
}
func (p *ExtraPerformance) IsValid() error {
	return nil
}

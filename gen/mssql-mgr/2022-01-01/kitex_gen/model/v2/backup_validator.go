// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *RestoreToNewInstanceReq) IsValid() error {
	if p.ChargeInfo != nil {
		if err := p.ChargeInfo.IsValid(); err != nil {
			return fmt.Errorf("field ChargeInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *RestoreToNewInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeRecoverableTimeReq) IsValid() error {
	return nil
}
func (p *DescribeRecoverableTimeResp) IsValid() error {
	return nil
}
func (p *CreateBackupReq) IsValid() error {
	return nil
}
func (p *CreateBackupResp) IsValid() error {
	return nil
}
func (p *CreateInnerBackupReq) IsValid() error {
	return nil
}
func (p *CreateInnerBackupResp) IsValid() error {
	return nil
}
func (p *DescribeBackupsReq) IsValid() error {
	return nil
}
func (p *DescribeBackupsResp) IsValid() error {
	return nil
}
func (p *DescribeCrossBackupsReq) IsValid() error {
	return nil
}
func (p *DescribeCrossBackupsResp) IsValid() error {
	return nil
}
func (p *DescribeBackupDetailReq) IsValid() error {
	return nil
}
func (p *DescribeBackupDetailResp) IsValid() error {
	if p.BackupsInfo != nil {
		if err := p.BackupsInfo.IsValid(); err != nil {
			return fmt.Errorf("field BackupsInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) IsValid() error {
	if p.ChargeInfo != nil {
		if err := p.ChargeInfo.IsValid(); err != nil {
			return fmt.Errorf("field ChargeInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *RestoreToCrossRegionInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeDeletedBackupsReq) IsValid() error {
	return nil
}
func (p *DescribeDeletedBackupsResp) IsValid() error {
	return nil
}
func (p *DescribeBackupPolicyReq) IsValid() error {
	return nil
}
func (p *DescribeBackupPolicyResp) IsValid() error {
	return nil
}
func (p *DescribeCrossBackupPolicyReq) IsValid() error {
	return nil
}
func (p *DescribeCrossBackupPolicyResp) IsValid() error {
	return nil
}
func (p *DescribeAvailableCrossRegionReq) IsValid() error {
	return nil
}
func (p *DescribeAvailableCrossRegionResp) IsValid() error {
	return nil
}
func (p *DeleteBackupReq) IsValid() error {
	return nil
}
func (p *DeleteBackupResp) IsValid() error {
	return nil
}
func (p *ModifyBackupPolicyReq) IsValid() error {
	return nil
}
func (p *ModifyBackupPolicyResp) IsValid() error {
	return nil
}
func (p *ModifyCrossBackupPolicyReq) IsValid() error {
	return nil
}
func (p *ModifyCrossBackupPolicyResp) IsValid() error {
	return nil
}
func (p *DownloadBackupReq) IsValid() error {
	return nil
}
func (p *DownloadBackupResp) IsValid() error {
	return nil
}
func (p *GetBackupDownloadLinkReq) IsValid() error {
	return nil
}
func (p *GetBackupDownloadLinkResp) IsValid() error {
	return nil
}
func (p *RestoreToExistedInstanceReq) IsValid() error {
	return nil
}
func (p *RestoreToExistedInstanceResp) IsValid() error {
	return nil
}
func (p *CreateTosRestoreReq) IsValid() error {
	return nil
}
func (p *CreateTosRestoreResp) IsValid() error {
	return nil
}
func (p *DescribeTosRestoreTasksReq) IsValid() error {
	return nil
}
func (p *DescribeTosRestoreTasksResp) IsValid() error {
	return nil
}
func (p *DescribeTosRestoreTaskDetailReq) IsValid() error {
	return nil
}
func (p *DescribeTosRestoreTaskDetailResp) IsValid() error {
	return nil
}

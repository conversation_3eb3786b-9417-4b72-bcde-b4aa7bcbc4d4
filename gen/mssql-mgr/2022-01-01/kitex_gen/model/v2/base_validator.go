// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *Region) IsValid() error {
	return nil
}
func (p *Zone) IsValid() error {
	return nil
}
func (p *InstanceInfoObject) IsValid() error {
	if p.ChargeDetail != nil {
		if err := p.ChargeDetail.IsValid(); err != nil {
			return fmt.Errorf("field ChargeDetail not valid, %w", err)
		}
	}
	return nil
}
func (p *InstanceSpecsInfoObject) IsValid() error {
	return nil
}
func (p *BasicInfoObject) IsValid() error {
	return nil
}
func (p *ChargeInfoObject) IsValid() error {
	return nil
}
func (p *ChargeDetailObject) IsValid() error {
	return nil
}
func (p *ConnectionInfoObject) IsValid() error {
	return nil
}
func (p *AddressObject) IsValid() error {
	return nil
}
func (p *NodeDetailInfoObject) IsValid() error {
	return nil
}
func (p *ShardInfoObject) IsValid() error {
	return nil
}
func (p *NodeInfoObject) IsValid() error {
	return nil
}
func (p *AccountPrivilegeObject) IsValid() error {
	return nil
}
func (p *AccountObject) IsValid() error {
	return nil
}
func (p *ModifyAccountPrivilegesInfo) IsValid() error {
	return nil
}
func (p *DatabasePrivilegeObject) IsValid() error {
	return nil
}
func (p *ModifyDatabasePrivilegesInfo) IsValid() error {
	return nil
}
func (p *DatabaseParameterObject) IsValid() error {
	return nil
}
func (p *DatabasesDetailObject) IsValid() error {
	return nil
}
func (p *BackupMeta) IsValid() error {
	return nil
}
func (p *BackupInfo) IsValid() error {
	return nil
}
func (p *BackupDatabaseDetailObject) IsValid() error {
	return nil
}
func (p *DeletedBackupInfoObject) IsValid() error {
	return nil
}
func (p *TableMeta) IsValid() error {
	return nil
}
func (p *Table) IsValid() error {
	return nil
}
func (p *RecoverableTimeInfo) IsValid() error {
	return nil
}
func (p *ErrorLogObject) IsValid() error {
	return nil
}
func (p *AuditQueryObject) IsValid() error {
	return nil
}
func (p *SlowQueryObject) IsValid() error {
	return nil
}
func (p *ParametersObject) IsValid() error {
	return nil
}
func (p *InstanceParametersObject) IsValid() error {
	return nil
}
func (p *ParameterChangeLogObject) IsValid() error {
	return nil
}
func (p *ChargeItemPrice) IsValid() error {
	return nil
}
func (p *ConfigItemPrice) IsValid() error {
	return nil
}
func (p *QueryFilter) IsValid() error {
	return nil
}
func (p *ShardDetailInfoObject) IsValid() error {
	return nil
}
func (p *DBRangeObject) IsValid() error {
	return nil
}
func (p *NodeWeightObject) IsValid() error {
	return nil
}
func (p *TagObject) IsValid() error {
	return nil
}
func (p *DBTableInfoObject) IsValid() error {
	return nil
}
func (p *EventObject) IsValid() error {
	return nil
}
func (p *FailoverQueryObject) IsValid() error {
	return nil
}
func (p *DatabasesObject) IsValid() error {
	return nil
}
func (p *RestoreTaskObject) IsValid() error {
	return nil
}
func (p *RestoreTaskDetailObject) IsValid() error {
	return nil
}
func (p *ChargeItem) IsValid() error {
	return nil
}
func (p *BillingInstanceStatus) IsValid() error {
	return nil
}
func (p *DescribeInstanceBillingStatusReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceBillingStatusResp) IsValid() error {
	return nil
}

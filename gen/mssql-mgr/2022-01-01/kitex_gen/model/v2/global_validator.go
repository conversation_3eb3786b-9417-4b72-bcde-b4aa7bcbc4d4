// Code generated by Validator v0.2.5. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DescribeRegionsReq) IsValid() error {
	return nil
}
func (p *DescribeRegionsResp) IsValid() error {
	return nil
}
func (p *DescribeAvailabilityZonesReq) IsValid() error {
	return nil
}
func (p *DescribeAvailabilityZonesResp) IsValid() error {
	return nil
}

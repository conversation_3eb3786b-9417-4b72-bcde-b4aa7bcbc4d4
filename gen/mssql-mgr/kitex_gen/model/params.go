// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ListInstanceParamsReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewListInstanceParamsReq() *ListInstanceParamsReq {
	return &ListInstanceParamsReq{}
}

func (p *ListInstanceParamsReq) InitDefault() {
}

func (p *ListInstanceParamsReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ListInstanceParamsReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ListInstanceParamsReq = map[int16]string{
	1: "InstanceId",
}

func (p *ListInstanceParamsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListInstanceParamsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListInstanceParamsReq[fieldId]))
}

func (p *ListInstanceParamsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ListInstanceParamsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListInstanceParamsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListInstanceParamsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListInstanceParamsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListInstanceParamsReq(%+v)", *p)

}

func (p *ListInstanceParamsReq) DeepEqual(ano *ListInstanceParamsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ListInstanceParamsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type ListInstanceParamsResp struct {
	Total int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*InstanceParam `thrift:"Datas,2,required" frugal:"2,required,list<InstanceParam>" json:"Datas"`
}

func NewListInstanceParamsResp() *ListInstanceParamsResp {
	return &ListInstanceParamsResp{}
}

func (p *ListInstanceParamsResp) InitDefault() {
}

func (p *ListInstanceParamsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListInstanceParamsResp) GetDatas() (v []*InstanceParam) {
	return p.Datas
}
func (p *ListInstanceParamsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListInstanceParamsResp) SetDatas(val []*InstanceParam) {
	p.Datas = val
}

var fieldIDToName_ListInstanceParamsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListInstanceParamsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListInstanceParamsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListInstanceParamsResp[fieldId]))
}

func (p *ListInstanceParamsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListInstanceParamsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParam, 0, size)
	values := make([]InstanceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListInstanceParamsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListInstanceParamsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListInstanceParamsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListInstanceParamsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListInstanceParamsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListInstanceParamsResp(%+v)", *p)

}

func (p *ListInstanceParamsResp) DeepEqual(ano *ListInstanceParamsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListInstanceParamsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListInstanceParamsResp) Field2DeepEqual(src []*InstanceParam) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ModifyInstanceParamsReq struct {
	InstanceId string              `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	Parameters []*InstanceParamChg `thrift:"Parameters,2,required" frugal:"2,required,list<InstanceParamChg>" validate:"required"`
}

func NewModifyInstanceParamsReq() *ModifyInstanceParamsReq {
	return &ModifyInstanceParamsReq{}
}

func (p *ModifyInstanceParamsReq) InitDefault() {
}

func (p *ModifyInstanceParamsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyInstanceParamsReq) GetParameters() (v []*InstanceParamChg) {
	return p.Parameters
}
func (p *ModifyInstanceParamsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyInstanceParamsReq) SetParameters(val []*InstanceParamChg) {
	p.Parameters = val
}

var fieldIDToName_ModifyInstanceParamsReq = map[int16]string{
	1: "InstanceId",
	2: "Parameters",
}

func (p *ModifyInstanceParamsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceParamsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetParameters bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameters = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetParameters {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceParamsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyInstanceParamsReq[fieldId]))
}

func (p *ModifyInstanceParamsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceParamsReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParamChg, 0, size)
	values := make([]InstanceParamChg, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Parameters = _field
	return nil
}

func (p *ModifyInstanceParamsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceParamsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceParamsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceParamsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceParamsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Parameters", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Parameters)); err != nil {
		return err
	}
	for _, v := range p.Parameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInstanceParamsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceParamsReq(%+v)", *p)

}

func (p *ModifyInstanceParamsReq) DeepEqual(ano *ModifyInstanceParamsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Parameters) {
		return false
	}
	return true
}

func (p *ModifyInstanceParamsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceParamsReq) Field2DeepEqual(src []*InstanceParamChg) bool {

	if len(p.Parameters) != len(src) {
		return false
	}
	for i, v := range p.Parameters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListInstanceParamsHistoryReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	StartTime  string `thrift:"StartTime,2,required" frugal:"2,required,string" validate:"required"`
	EndTime    string `thrift:"EndTime,3,required" frugal:"3,required,string" validate:"required"`
}

func NewListInstanceParamsHistoryReq() *ListInstanceParamsHistoryReq {
	return &ListInstanceParamsHistoryReq{}
}

func (p *ListInstanceParamsHistoryReq) InitDefault() {
}

func (p *ListInstanceParamsHistoryReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListInstanceParamsHistoryReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *ListInstanceParamsHistoryReq) GetEndTime() (v string) {
	return p.EndTime
}
func (p *ListInstanceParamsHistoryReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListInstanceParamsHistoryReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ListInstanceParamsHistoryReq) SetEndTime(val string) {
	p.EndTime = val
}

var fieldIDToName_ListInstanceParamsHistoryReq = map[int16]string{
	1: "InstanceId",
	2: "StartTime",
	3: "EndTime",
}

func (p *ListInstanceParamsHistoryReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsHistoryReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListInstanceParamsHistoryReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListInstanceParamsHistoryReq[fieldId]))
}

func (p *ListInstanceParamsHistoryReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListInstanceParamsHistoryReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ListInstanceParamsHistoryReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *ListInstanceParamsHistoryReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsHistoryReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListInstanceParamsHistoryReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListInstanceParamsHistoryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListInstanceParamsHistoryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListInstanceParamsHistoryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListInstanceParamsHistoryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListInstanceParamsHistoryReq(%+v)", *p)

}

func (p *ListInstanceParamsHistoryReq) DeepEqual(ano *ListInstanceParamsHistoryReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *ListInstanceParamsHistoryReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListInstanceParamsHistoryReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ListInstanceParamsHistoryReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}

type ListInstanceParamsHistoryResp struct {
	Total int32                        `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*InstanceParamsHistoryInfo `thrift:"Datas,2,required" frugal:"2,required,list<InstanceParamsHistoryInfo>" json:"Datas"`
}

func NewListInstanceParamsHistoryResp() *ListInstanceParamsHistoryResp {
	return &ListInstanceParamsHistoryResp{}
}

func (p *ListInstanceParamsHistoryResp) InitDefault() {
}

func (p *ListInstanceParamsHistoryResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListInstanceParamsHistoryResp) GetDatas() (v []*InstanceParamsHistoryInfo) {
	return p.Datas
}
func (p *ListInstanceParamsHistoryResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListInstanceParamsHistoryResp) SetDatas(val []*InstanceParamsHistoryInfo) {
	p.Datas = val
}

var fieldIDToName_ListInstanceParamsHistoryResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListInstanceParamsHistoryResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsHistoryResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListInstanceParamsHistoryResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListInstanceParamsHistoryResp[fieldId]))
}

func (p *ListInstanceParamsHistoryResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListInstanceParamsHistoryResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParamsHistoryInfo, 0, size)
	values := make([]InstanceParamsHistoryInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListInstanceParamsHistoryResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListInstanceParamsHistoryResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListInstanceParamsHistoryResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListInstanceParamsHistoryResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListInstanceParamsHistoryResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListInstanceParamsHistoryResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListInstanceParamsHistoryResp(%+v)", *p)

}

func (p *ListInstanceParamsHistoryResp) DeepEqual(ano *ListInstanceParamsHistoryResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListInstanceParamsHistoryResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListInstanceParamsHistoryResp) Field2DeepEqual(src []*InstanceParamsHistoryInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeInstanceConfigReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeInstanceConfigReq() *DescribeInstanceConfigReq {
	return &DescribeInstanceConfigReq{}
}

func (p *DescribeInstanceConfigReq) InitDefault() {
}

func (p *DescribeInstanceConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeInstanceConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeInstanceConfigReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeInstanceConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceConfigReq[fieldId]))
}

func (p *DescribeInstanceConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeInstanceConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceConfigReq(%+v)", *p)

}

func (p *DescribeInstanceConfigReq) DeepEqual(ano *DescribeInstanceConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeInstanceConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceConfigResp struct {
	TosConfig *InstanceTosConfig `thrift:"TosConfig,1,required" frugal:"1,required,InstanceTosConfig" json:"TosConfig"`
	TlsConfig *InstanceTlsConfig `thrift:"TlsConfig,2,required" frugal:"2,required,InstanceTlsConfig" json:"TlsConfig"`
	DbwConfig *InstanceDbwConfig `thrift:"DbwConfig,3,required" frugal:"3,required,InstanceDbwConfig" json:"DbwConfig"`
}

func NewDescribeInstanceConfigResp() *DescribeInstanceConfigResp {
	return &DescribeInstanceConfigResp{}
}

func (p *DescribeInstanceConfigResp) InitDefault() {
}

var DescribeInstanceConfigResp_TosConfig_DEFAULT *InstanceTosConfig

func (p *DescribeInstanceConfigResp) GetTosConfig() (v *InstanceTosConfig) {
	if !p.IsSetTosConfig() {
		return DescribeInstanceConfigResp_TosConfig_DEFAULT
	}
	return p.TosConfig
}

var DescribeInstanceConfigResp_TlsConfig_DEFAULT *InstanceTlsConfig

func (p *DescribeInstanceConfigResp) GetTlsConfig() (v *InstanceTlsConfig) {
	if !p.IsSetTlsConfig() {
		return DescribeInstanceConfigResp_TlsConfig_DEFAULT
	}
	return p.TlsConfig
}

var DescribeInstanceConfigResp_DbwConfig_DEFAULT *InstanceDbwConfig

func (p *DescribeInstanceConfigResp) GetDbwConfig() (v *InstanceDbwConfig) {
	if !p.IsSetDbwConfig() {
		return DescribeInstanceConfigResp_DbwConfig_DEFAULT
	}
	return p.DbwConfig
}
func (p *DescribeInstanceConfigResp) SetTosConfig(val *InstanceTosConfig) {
	p.TosConfig = val
}
func (p *DescribeInstanceConfigResp) SetTlsConfig(val *InstanceTlsConfig) {
	p.TlsConfig = val
}
func (p *DescribeInstanceConfigResp) SetDbwConfig(val *InstanceDbwConfig) {
	p.DbwConfig = val
}

var fieldIDToName_DescribeInstanceConfigResp = map[int16]string{
	1: "TosConfig",
	2: "TlsConfig",
	3: "DbwConfig",
}

func (p *DescribeInstanceConfigResp) IsSetTosConfig() bool {
	return p.TosConfig != nil
}

func (p *DescribeInstanceConfigResp) IsSetTlsConfig() bool {
	return p.TlsConfig != nil
}

func (p *DescribeInstanceConfigResp) IsSetDbwConfig() bool {
	return p.DbwConfig != nil
}

func (p *DescribeInstanceConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTosConfig bool = false
	var issetTlsConfig bool = false
	var issetDbwConfig bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTosConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTlsConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbwConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTosConfig {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTlsConfig {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDbwConfig {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceConfigResp[fieldId]))
}

func (p *DescribeInstanceConfigResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInstanceTosConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TosConfig = _field
	return nil
}
func (p *DescribeInstanceConfigResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstanceTlsConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TlsConfig = _field
	return nil
}
func (p *DescribeInstanceConfigResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewInstanceDbwConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DbwConfig = _field
	return nil
}

func (p *DescribeInstanceConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TosConfig", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TosConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TlsConfig", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TlsConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceConfigResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbwConfig", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.DbwConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceConfigResp(%+v)", *p)

}

func (p *DescribeInstanceConfigResp) DeepEqual(ano *DescribeInstanceConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TosConfig) {
		return false
	}
	if !p.Field2DeepEqual(ano.TlsConfig) {
		return false
	}
	if !p.Field3DeepEqual(ano.DbwConfig) {
		return false
	}
	return true
}

func (p *DescribeInstanceConfigResp) Field1DeepEqual(src *InstanceTosConfig) bool {

	if !p.TosConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeInstanceConfigResp) Field2DeepEqual(src *InstanceTlsConfig) bool {

	if !p.TlsConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeInstanceConfigResp) Field3DeepEqual(src *InstanceDbwConfig) bool {

	if !p.DbwConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyInstanceConfigReq struct {
	InstanceId string             `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	TosConfig  *InstanceTosConfig `thrift:"TosConfig,2,optional" frugal:"2,optional,InstanceTosConfig" json:"TosConfig,omitempty"`
	TlsConfig  *InstanceTlsConfig `thrift:"TlsConfig,3,optional" frugal:"3,optional,InstanceTlsConfig" json:"TlsConfig,omitempty"`
}

func NewModifyInstanceConfigReq() *ModifyInstanceConfigReq {
	return &ModifyInstanceConfigReq{}
}

func (p *ModifyInstanceConfigReq) InitDefault() {
}

func (p *ModifyInstanceConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyInstanceConfigReq_TosConfig_DEFAULT *InstanceTosConfig

func (p *ModifyInstanceConfigReq) GetTosConfig() (v *InstanceTosConfig) {
	if !p.IsSetTosConfig() {
		return ModifyInstanceConfigReq_TosConfig_DEFAULT
	}
	return p.TosConfig
}

var ModifyInstanceConfigReq_TlsConfig_DEFAULT *InstanceTlsConfig

func (p *ModifyInstanceConfigReq) GetTlsConfig() (v *InstanceTlsConfig) {
	if !p.IsSetTlsConfig() {
		return ModifyInstanceConfigReq_TlsConfig_DEFAULT
	}
	return p.TlsConfig
}
func (p *ModifyInstanceConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyInstanceConfigReq) SetTosConfig(val *InstanceTosConfig) {
	p.TosConfig = val
}
func (p *ModifyInstanceConfigReq) SetTlsConfig(val *InstanceTlsConfig) {
	p.TlsConfig = val
}

var fieldIDToName_ModifyInstanceConfigReq = map[int16]string{
	1: "InstanceId",
	2: "TosConfig",
	3: "TlsConfig",
}

func (p *ModifyInstanceConfigReq) IsSetTosConfig() bool {
	return p.TosConfig != nil
}

func (p *ModifyInstanceConfigReq) IsSetTlsConfig() bool {
	return p.TlsConfig != nil
}

func (p *ModifyInstanceConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyInstanceConfigReq[fieldId]))
}

func (p *ModifyInstanceConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceConfigReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstanceTosConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TosConfig = _field
	return nil
}
func (p *ModifyInstanceConfigReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewInstanceTlsConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TlsConfig = _field
	return nil
}

func (p *ModifyInstanceConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTosConfig() {
		if err = oprot.WriteFieldBegin("TosConfig", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TosConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInstanceConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTlsConfig() {
		if err = oprot.WriteFieldBegin("TlsConfig", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TlsConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyInstanceConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceConfigReq(%+v)", *p)

}

func (p *ModifyInstanceConfigReq) DeepEqual(ano *ModifyInstanceConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TosConfig) {
		return false
	}
	if !p.Field3DeepEqual(ano.TlsConfig) {
		return false
	}
	return true
}

func (p *ModifyInstanceConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceConfigReq) Field2DeepEqual(src *InstanceTosConfig) bool {

	if !p.TosConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ModifyInstanceConfigReq) Field3DeepEqual(src *InstanceTlsConfig) bool {

	if !p.TlsConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ListParameterTemplatesReq struct {
	TemplateCategory    *ParameterTemplateCategory `thrift:"TemplateCategory,1,optional" frugal:"1,optional,ParameterTemplateCategory" json:"TemplateCategory,omitempty"`
	TemplateType        *DBEngine                  `thrift:"TemplateType,2,optional" frugal:"2,optional,DBEngine" json:"TemplateType,omitempty"`
	TemplateTypeVersion *DBEngineVersion           `thrift:"TemplateTypeVersion,3,optional" frugal:"3,optional,DBEngineVersion" json:"TemplateTypeVersion,omitempty"`
	TemplateSource      *CreateType                `thrift:"TemplateSource,4,optional" frugal:"4,optional,CreateType" json:"TemplateSource,omitempty"`
	Offset              *int32                     `thrift:"Offset,5,optional" frugal:"5,optional,i32" json:"Offset,omitempty"`
	Limit               *int32                     `thrift:"Limit,6,optional" frugal:"6,optional,i32" json:"Limit,omitempty"`
}

func NewListParameterTemplatesReq() *ListParameterTemplatesReq {
	return &ListParameterTemplatesReq{}
}

func (p *ListParameterTemplatesReq) InitDefault() {
}

var ListParameterTemplatesReq_TemplateCategory_DEFAULT ParameterTemplateCategory

func (p *ListParameterTemplatesReq) GetTemplateCategory() (v ParameterTemplateCategory) {
	if !p.IsSetTemplateCategory() {
		return ListParameterTemplatesReq_TemplateCategory_DEFAULT
	}
	return *p.TemplateCategory
}

var ListParameterTemplatesReq_TemplateType_DEFAULT DBEngine

func (p *ListParameterTemplatesReq) GetTemplateType() (v DBEngine) {
	if !p.IsSetTemplateType() {
		return ListParameterTemplatesReq_TemplateType_DEFAULT
	}
	return *p.TemplateType
}

var ListParameterTemplatesReq_TemplateTypeVersion_DEFAULT DBEngineVersion

func (p *ListParameterTemplatesReq) GetTemplateTypeVersion() (v DBEngineVersion) {
	if !p.IsSetTemplateTypeVersion() {
		return ListParameterTemplatesReq_TemplateTypeVersion_DEFAULT
	}
	return *p.TemplateTypeVersion
}

var ListParameterTemplatesReq_TemplateSource_DEFAULT CreateType

func (p *ListParameterTemplatesReq) GetTemplateSource() (v CreateType) {
	if !p.IsSetTemplateSource() {
		return ListParameterTemplatesReq_TemplateSource_DEFAULT
	}
	return *p.TemplateSource
}

var ListParameterTemplatesReq_Offset_DEFAULT int32

func (p *ListParameterTemplatesReq) GetOffset() (v int32) {
	if !p.IsSetOffset() {
		return ListParameterTemplatesReq_Offset_DEFAULT
	}
	return *p.Offset
}

var ListParameterTemplatesReq_Limit_DEFAULT int32

func (p *ListParameterTemplatesReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return ListParameterTemplatesReq_Limit_DEFAULT
	}
	return *p.Limit
}
func (p *ListParameterTemplatesReq) SetTemplateCategory(val *ParameterTemplateCategory) {
	p.TemplateCategory = val
}
func (p *ListParameterTemplatesReq) SetTemplateType(val *DBEngine) {
	p.TemplateType = val
}
func (p *ListParameterTemplatesReq) SetTemplateTypeVersion(val *DBEngineVersion) {
	p.TemplateTypeVersion = val
}
func (p *ListParameterTemplatesReq) SetTemplateSource(val *CreateType) {
	p.TemplateSource = val
}
func (p *ListParameterTemplatesReq) SetOffset(val *int32) {
	p.Offset = val
}
func (p *ListParameterTemplatesReq) SetLimit(val *int32) {
	p.Limit = val
}

var fieldIDToName_ListParameterTemplatesReq = map[int16]string{
	1: "TemplateCategory",
	2: "TemplateType",
	3: "TemplateTypeVersion",
	4: "TemplateSource",
	5: "Offset",
	6: "Limit",
}

func (p *ListParameterTemplatesReq) IsSetTemplateCategory() bool {
	return p.TemplateCategory != nil
}

func (p *ListParameterTemplatesReq) IsSetTemplateType() bool {
	return p.TemplateType != nil
}

func (p *ListParameterTemplatesReq) IsSetTemplateTypeVersion() bool {
	return p.TemplateTypeVersion != nil
}

func (p *ListParameterTemplatesReq) IsSetTemplateSource() bool {
	return p.TemplateSource != nil
}

func (p *ListParameterTemplatesReq) IsSetOffset() bool {
	return p.Offset != nil
}

func (p *ListParameterTemplatesReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListParameterTemplatesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListParameterTemplatesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListParameterTemplatesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListParameterTemplatesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *ParameterTemplateCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ParameterTemplateCategory(v)
		_field = &tmp
	}
	p.TemplateCategory = _field
	return nil
}
func (p *ListParameterTemplatesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DBEngine
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngine(v)
		_field = &tmp
	}
	p.TemplateType = _field
	return nil
}
func (p *ListParameterTemplatesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.TemplateTypeVersion = _field
	return nil
}
func (p *ListParameterTemplatesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *CreateType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CreateType(v)
		_field = &tmp
	}
	p.TemplateSource = _field
	return nil
}
func (p *ListParameterTemplatesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Offset = _field
	return nil
}
func (p *ListParameterTemplatesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}

func (p *ListParameterTemplatesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListParameterTemplatesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListParameterTemplatesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateCategory() {
		if err = oprot.WriteFieldBegin("TemplateCategory", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TemplateCategory)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateType() {
		if err = oprot.WriteFieldBegin("TemplateType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TemplateType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateTypeVersion() {
		if err = oprot.WriteFieldBegin("TemplateTypeVersion", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TemplateTypeVersion)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateSource() {
		if err = oprot.WriteFieldBegin("TemplateSource", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TemplateSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOffset() {
		if err = oprot.WriteFieldBegin("Offset", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Offset); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListParameterTemplatesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListParameterTemplatesReq(%+v)", *p)

}

func (p *ListParameterTemplatesReq) DeepEqual(ano *ListParameterTemplatesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateCategory) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TemplateTypeVersion) {
		return false
	}
	if !p.Field4DeepEqual(ano.TemplateSource) {
		return false
	}
	if !p.Field5DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field6DeepEqual(ano.Limit) {
		return false
	}
	return true
}

func (p *ListParameterTemplatesReq) Field1DeepEqual(src *ParameterTemplateCategory) bool {

	if p.TemplateCategory == src {
		return true
	} else if p.TemplateCategory == nil || src == nil {
		return false
	}
	if *p.TemplateCategory != *src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesReq) Field2DeepEqual(src *DBEngine) bool {

	if p.TemplateType == src {
		return true
	} else if p.TemplateType == nil || src == nil {
		return false
	}
	if *p.TemplateType != *src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesReq) Field3DeepEqual(src *DBEngineVersion) bool {

	if p.TemplateTypeVersion == src {
		return true
	} else if p.TemplateTypeVersion == nil || src == nil {
		return false
	}
	if *p.TemplateTypeVersion != *src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesReq) Field4DeepEqual(src *CreateType) bool {

	if p.TemplateSource == src {
		return true
	} else if p.TemplateSource == nil || src == nil {
		return false
	}
	if *p.TemplateSource != *src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesReq) Field5DeepEqual(src *int32) bool {

	if p.Offset == src {
		return true
	} else if p.Offset == nil || src == nil {
		return false
	}
	if *p.Offset != *src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesReq) Field6DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}

type ListParameterTemplatesResp struct {
	Total int32                    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*ParameterTemplateInfo `thrift:"Datas,2,required" frugal:"2,required,list<ParameterTemplateInfo>" json:"Datas"`
}

func NewListParameterTemplatesResp() *ListParameterTemplatesResp {
	return &ListParameterTemplatesResp{}
}

func (p *ListParameterTemplatesResp) InitDefault() {
}

func (p *ListParameterTemplatesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListParameterTemplatesResp) GetDatas() (v []*ParameterTemplateInfo) {
	return p.Datas
}
func (p *ListParameterTemplatesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListParameterTemplatesResp) SetDatas(val []*ParameterTemplateInfo) {
	p.Datas = val
}

var fieldIDToName_ListParameterTemplatesResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *ListParameterTemplatesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListParameterTemplatesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListParameterTemplatesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListParameterTemplatesResp[fieldId]))
}

func (p *ListParameterTemplatesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListParameterTemplatesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ParameterTemplateInfo, 0, size)
	values := make([]ParameterTemplateInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *ListParameterTemplatesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListParameterTemplatesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListParameterTemplatesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListParameterTemplatesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListParameterTemplatesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListParameterTemplatesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListParameterTemplatesResp(%+v)", *p)

}

func (p *ListParameterTemplatesResp) DeepEqual(ano *ListParameterTemplatesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *ListParameterTemplatesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListParameterTemplatesResp) Field2DeepEqual(src []*ParameterTemplateInfo) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeParameterTemplateReq struct {
	TemplateId string `thrift:"TemplateId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeParameterTemplateReq() *DescribeParameterTemplateReq {
	return &DescribeParameterTemplateReq{}
}

func (p *DescribeParameterTemplateReq) InitDefault() {
}

func (p *DescribeParameterTemplateReq) GetTemplateId() (v string) {
	return p.TemplateId
}
func (p *DescribeParameterTemplateReq) SetTemplateId(val string) {
	p.TemplateId = val
}

var fieldIDToName_DescribeParameterTemplateReq = map[int16]string{
	1: "TemplateId",
}

func (p *DescribeParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTemplateId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTemplateId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeParameterTemplateReq[fieldId]))
}

func (p *DescribeParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateId = _field
	return nil
}

func (p *DescribeParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeParameterTemplateReq(%+v)", *p)

}

func (p *DescribeParameterTemplateReq) DeepEqual(ano *DescribeParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateId) {
		return false
	}
	return true
}

func (p *DescribeParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TemplateId, src) != 0 {
		return false
	}
	return true
}

type DescribeParameterTemplateResp struct {
	TemplateInfo *ParameterTemplateInfo `thrift:"TemplateInfo,1,required" frugal:"1,required,ParameterTemplateInfo" json:"TemplateInfo"`
}

func NewDescribeParameterTemplateResp() *DescribeParameterTemplateResp {
	return &DescribeParameterTemplateResp{}
}

func (p *DescribeParameterTemplateResp) InitDefault() {
}

var DescribeParameterTemplateResp_TemplateInfo_DEFAULT *ParameterTemplateInfo

func (p *DescribeParameterTemplateResp) GetTemplateInfo() (v *ParameterTemplateInfo) {
	if !p.IsSetTemplateInfo() {
		return DescribeParameterTemplateResp_TemplateInfo_DEFAULT
	}
	return p.TemplateInfo
}
func (p *DescribeParameterTemplateResp) SetTemplateInfo(val *ParameterTemplateInfo) {
	p.TemplateInfo = val
}

var fieldIDToName_DescribeParameterTemplateResp = map[int16]string{
	1: "TemplateInfo",
}

func (p *DescribeParameterTemplateResp) IsSetTemplateInfo() bool {
	return p.TemplateInfo != nil
}

func (p *DescribeParameterTemplateResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeParameterTemplateResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTemplateInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTemplateInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeParameterTemplateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeParameterTemplateResp[fieldId]))
}

func (p *DescribeParameterTemplateResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewParameterTemplateInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TemplateInfo = _field
	return nil
}

func (p *DescribeParameterTemplateResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeParameterTemplateResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeParameterTemplateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeParameterTemplateResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TemplateInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeParameterTemplateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeParameterTemplateResp(%+v)", *p)

}

func (p *DescribeParameterTemplateResp) DeepEqual(ano *DescribeParameterTemplateResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateInfo) {
		return false
	}
	return true
}

func (p *DescribeParameterTemplateResp) Field1DeepEqual(src *ParameterTemplateInfo) bool {

	if !p.TemplateInfo.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteParameterTemplateReq struct {
	TemplateId string `thrift:"TemplateId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDeleteParameterTemplateReq() *DeleteParameterTemplateReq {
	return &DeleteParameterTemplateReq{}
}

func (p *DeleteParameterTemplateReq) InitDefault() {
}

func (p *DeleteParameterTemplateReq) GetTemplateId() (v string) {
	return p.TemplateId
}
func (p *DeleteParameterTemplateReq) SetTemplateId(val string) {
	p.TemplateId = val
}

var fieldIDToName_DeleteParameterTemplateReq = map[int16]string{
	1: "TemplateId",
}

func (p *DeleteParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTemplateId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTemplateId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteParameterTemplateReq[fieldId]))
}

func (p *DeleteParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateId = _field
	return nil
}

func (p *DeleteParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteParameterTemplateReq(%+v)", *p)

}

func (p *DeleteParameterTemplateReq) DeepEqual(ano *DeleteParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateId) {
		return false
	}
	return true
}

func (p *DeleteParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TemplateId, src) != 0 {
		return false
	}
	return true
}

type CopyParameterTemplateReq struct {
	SrcTemplateId string `thrift:"SrcTemplateId,1,required" frugal:"1,required,string" validate:"required"`
	TemplateName  string `thrift:"TemplateName,2,required" frugal:"2,required,string" validate:"required,min=2,max=64"`
	TemplateDesc  string `thrift:"TemplateDesc,3" frugal:"3,default,string" validate:"max=200"`
}

func NewCopyParameterTemplateReq() *CopyParameterTemplateReq {
	return &CopyParameterTemplateReq{}
}

func (p *CopyParameterTemplateReq) InitDefault() {
}

func (p *CopyParameterTemplateReq) GetSrcTemplateId() (v string) {
	return p.SrcTemplateId
}

func (p *CopyParameterTemplateReq) GetTemplateName() (v string) {
	return p.TemplateName
}

func (p *CopyParameterTemplateReq) GetTemplateDesc() (v string) {
	return p.TemplateDesc
}
func (p *CopyParameterTemplateReq) SetSrcTemplateId(val string) {
	p.SrcTemplateId = val
}
func (p *CopyParameterTemplateReq) SetTemplateName(val string) {
	p.TemplateName = val
}
func (p *CopyParameterTemplateReq) SetTemplateDesc(val string) {
	p.TemplateDesc = val
}

var fieldIDToName_CopyParameterTemplateReq = map[int16]string{
	1: "SrcTemplateId",
	2: "TemplateName",
	3: "TemplateDesc",
}

func (p *CopyParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopyParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSrcTemplateId bool = false
	var issetTemplateName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrcTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSrcTemplateId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopyParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopyParameterTemplateReq[fieldId]))
}

func (p *CopyParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcTemplateId = _field
	return nil
}
func (p *CopyParameterTemplateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateName = _field
	return nil
}
func (p *CopyParameterTemplateReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateDesc = _field
	return nil
}

func (p *CopyParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopyParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopyParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopyParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcTemplateId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcTemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopyParameterTemplateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopyParameterTemplateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CopyParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopyParameterTemplateReq(%+v)", *p)

}

func (p *CopyParameterTemplateReq) DeepEqual(ano *CopyParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SrcTemplateId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateName) {
		return false
	}
	if !p.Field3DeepEqual(ano.TemplateDesc) {
		return false
	}
	return true
}

func (p *CopyParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SrcTemplateId, src) != 0 {
		return false
	}
	return true
}
func (p *CopyParameterTemplateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateName, src) != 0 {
		return false
	}
	return true
}
func (p *CopyParameterTemplateReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TemplateDesc, src) != 0 {
		return false
	}
	return true
}

type CreateParameterTemplateReq struct {
	TemplateName        string                    `thrift:"TemplateName,1,required" frugal:"1,required,string" validate:"required,min=2,max=64"`
	TemplateDesc        string                    `thrift:"TemplateDesc,2" frugal:"2,default,string" validate:"max=200"`
	TemplateType        DBEngine                  `thrift:"TemplateType,3,required" frugal:"3,required,DBEngine" json:"TemplateType"`
	TemplateTypeVersion DBEngineVersion           `thrift:"TemplateTypeVersion,4,required" frugal:"4,required,DBEngineVersion" json:"TemplateTypeVersion"`
	TemplateCategory    ParameterTemplateCategory `thrift:"TemplateCategory,5" frugal:"5,default,ParameterTemplateCategory" json:"TemplateCategory"`
	TemplateParams      []*InstanceParam          `thrift:"TemplateParams,6,required" frugal:"6,required,list<InstanceParam>" json:"TemplateParams"`
	CustomParams        []*InstanceParam          `thrift:"CustomParams,7,required" frugal:"7,required,list<InstanceParam>" json:"CustomParams"`
}

func NewCreateParameterTemplateReq() *CreateParameterTemplateReq {
	return &CreateParameterTemplateReq{}
}

func (p *CreateParameterTemplateReq) InitDefault() {
}

func (p *CreateParameterTemplateReq) GetTemplateName() (v string) {
	return p.TemplateName
}

func (p *CreateParameterTemplateReq) GetTemplateDesc() (v string) {
	return p.TemplateDesc
}

func (p *CreateParameterTemplateReq) GetTemplateType() (v DBEngine) {
	return p.TemplateType
}

func (p *CreateParameterTemplateReq) GetTemplateTypeVersion() (v DBEngineVersion) {
	return p.TemplateTypeVersion
}

func (p *CreateParameterTemplateReq) GetTemplateCategory() (v ParameterTemplateCategory) {
	return p.TemplateCategory
}

func (p *CreateParameterTemplateReq) GetTemplateParams() (v []*InstanceParam) {
	return p.TemplateParams
}

func (p *CreateParameterTemplateReq) GetCustomParams() (v []*InstanceParam) {
	return p.CustomParams
}
func (p *CreateParameterTemplateReq) SetTemplateName(val string) {
	p.TemplateName = val
}
func (p *CreateParameterTemplateReq) SetTemplateDesc(val string) {
	p.TemplateDesc = val
}
func (p *CreateParameterTemplateReq) SetTemplateType(val DBEngine) {
	p.TemplateType = val
}
func (p *CreateParameterTemplateReq) SetTemplateTypeVersion(val DBEngineVersion) {
	p.TemplateTypeVersion = val
}
func (p *CreateParameterTemplateReq) SetTemplateCategory(val ParameterTemplateCategory) {
	p.TemplateCategory = val
}
func (p *CreateParameterTemplateReq) SetTemplateParams(val []*InstanceParam) {
	p.TemplateParams = val
}
func (p *CreateParameterTemplateReq) SetCustomParams(val []*InstanceParam) {
	p.CustomParams = val
}

var fieldIDToName_CreateParameterTemplateReq = map[int16]string{
	1: "TemplateName",
	2: "TemplateDesc",
	3: "TemplateType",
	4: "TemplateTypeVersion",
	5: "TemplateCategory",
	6: "TemplateParams",
	7: "CustomParams",
}

func (p *CreateParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTemplateName bool = false
	var issetTemplateType bool = false
	var issetTemplateTypeVersion bool = false
	var issetTemplateParams bool = false
	var issetCustomParams bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateTypeVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateParams = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCustomParams = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTemplateName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTemplateTypeVersion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTemplateParams {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCustomParams {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateParameterTemplateReq[fieldId]))
}

func (p *CreateParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateName = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateDesc = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DBEngine
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBEngine(v)
	}
	p.TemplateType = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField4(iprot thrift.TProtocol) error {

	var _field DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBEngineVersion(v)
	}
	p.TemplateTypeVersion = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField5(iprot thrift.TProtocol) error {

	var _field ParameterTemplateCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ParameterTemplateCategory(v)
	}
	p.TemplateCategory = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParam, 0, size)
	values := make([]InstanceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TemplateParams = _field
	return nil
}
func (p *CreateParameterTemplateReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParam, 0, size)
	values := make([]InstanceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CustomParams = _field
	return nil
}

func (p *CreateParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateDesc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TemplateType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateTypeVersion", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TemplateTypeVersion)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateCategory", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TemplateCategory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateParams", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TemplateParams)); err != nil {
		return err
	}
	for _, v := range p.TemplateParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CustomParams", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CustomParams)); err != nil {
		return err
	}
	for _, v := range p.CustomParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateParameterTemplateReq(%+v)", *p)

}

func (p *CreateParameterTemplateReq) DeepEqual(ano *CreateParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateName) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateDesc) {
		return false
	}
	if !p.Field3DeepEqual(ano.TemplateType) {
		return false
	}
	if !p.Field4DeepEqual(ano.TemplateTypeVersion) {
		return false
	}
	if !p.Field5DeepEqual(ano.TemplateCategory) {
		return false
	}
	if !p.Field6DeepEqual(ano.TemplateParams) {
		return false
	}
	if !p.Field7DeepEqual(ano.CustomParams) {
		return false
	}
	return true
}

func (p *CreateParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TemplateName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateParameterTemplateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateDesc, src) != 0 {
		return false
	}
	return true
}
func (p *CreateParameterTemplateReq) Field3DeepEqual(src DBEngine) bool {

	if p.TemplateType != src {
		return false
	}
	return true
}
func (p *CreateParameterTemplateReq) Field4DeepEqual(src DBEngineVersion) bool {

	if p.TemplateTypeVersion != src {
		return false
	}
	return true
}
func (p *CreateParameterTemplateReq) Field5DeepEqual(src ParameterTemplateCategory) bool {

	if p.TemplateCategory != src {
		return false
	}
	return true
}
func (p *CreateParameterTemplateReq) Field6DeepEqual(src []*InstanceParam) bool {

	if len(p.TemplateParams) != len(src) {
		return false
	}
	for i, v := range p.TemplateParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateParameterTemplateReq) Field7DeepEqual(src []*InstanceParam) bool {

	if len(p.CustomParams) != len(src) {
		return false
	}
	for i, v := range p.CustomParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ModifyParameterTemplateReq struct {
	TemplateId     string           `thrift:"TemplateId,1,required" frugal:"1,required,string" validate:"required"`
	TemplateName   string           `thrift:"TemplateName,2,required" frugal:"2,required,string" validate:"required,min=2,max=64"`
	TemplateDesc   string           `thrift:"TemplateDesc,3" frugal:"3,default,string" validate:"max=200"`
	TemplateParams []*InstanceParam `thrift:"TemplateParams,4" frugal:"4,default,list<InstanceParam>" json:"TemplateParams"`
	CustomParams   []*InstanceParam `thrift:"CustomParams,5" frugal:"5,default,list<InstanceParam>" json:"CustomParams"`
}

func NewModifyParameterTemplateReq() *ModifyParameterTemplateReq {
	return &ModifyParameterTemplateReq{}
}

func (p *ModifyParameterTemplateReq) InitDefault() {
}

func (p *ModifyParameterTemplateReq) GetTemplateId() (v string) {
	return p.TemplateId
}

func (p *ModifyParameterTemplateReq) GetTemplateName() (v string) {
	return p.TemplateName
}

func (p *ModifyParameterTemplateReq) GetTemplateDesc() (v string) {
	return p.TemplateDesc
}

func (p *ModifyParameterTemplateReq) GetTemplateParams() (v []*InstanceParam) {
	return p.TemplateParams
}

func (p *ModifyParameterTemplateReq) GetCustomParams() (v []*InstanceParam) {
	return p.CustomParams
}
func (p *ModifyParameterTemplateReq) SetTemplateId(val string) {
	p.TemplateId = val
}
func (p *ModifyParameterTemplateReq) SetTemplateName(val string) {
	p.TemplateName = val
}
func (p *ModifyParameterTemplateReq) SetTemplateDesc(val string) {
	p.TemplateDesc = val
}
func (p *ModifyParameterTemplateReq) SetTemplateParams(val []*InstanceParam) {
	p.TemplateParams = val
}
func (p *ModifyParameterTemplateReq) SetCustomParams(val []*InstanceParam) {
	p.CustomParams = val
}

var fieldIDToName_ModifyParameterTemplateReq = map[int16]string{
	1: "TemplateId",
	2: "TemplateName",
	3: "TemplateDesc",
	4: "TemplateParams",
	5: "CustomParams",
}

func (p *ModifyParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTemplateId bool = false
	var issetTemplateName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTemplateId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyParameterTemplateReq[fieldId]))
}

func (p *ModifyParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateId = _field
	return nil
}
func (p *ModifyParameterTemplateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateName = _field
	return nil
}
func (p *ModifyParameterTemplateReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateDesc = _field
	return nil
}
func (p *ModifyParameterTemplateReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParam, 0, size)
	values := make([]InstanceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TemplateParams = _field
	return nil
}
func (p *ModifyParameterTemplateReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParam, 0, size)
	values := make([]InstanceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CustomParams = _field
	return nil
}

func (p *ModifyParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateParams", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TemplateParams)); err != nil {
		return err
	}
	for _, v := range p.TemplateParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CustomParams", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CustomParams)); err != nil {
		return err
	}
	for _, v := range p.CustomParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyParameterTemplateReq(%+v)", *p)

}

func (p *ModifyParameterTemplateReq) DeepEqual(ano *ModifyParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TemplateId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateName) {
		return false
	}
	if !p.Field3DeepEqual(ano.TemplateDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.TemplateParams) {
		return false
	}
	if !p.Field5DeepEqual(ano.CustomParams) {
		return false
	}
	return true
}

func (p *ModifyParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TemplateId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyParameterTemplateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyParameterTemplateReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TemplateDesc, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyParameterTemplateReq) Field4DeepEqual(src []*InstanceParam) bool {

	if len(p.TemplateParams) != len(src) {
		return false
	}
	for i, v := range p.TemplateParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyParameterTemplateReq) Field5DeepEqual(src []*InstanceParam) bool {

	if len(p.CustomParams) != len(src) {
		return false
	}
	for i, v := range p.CustomParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeApplyParameterTemplateReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	TemplateId string `thrift:"TemplateId,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDescribeApplyParameterTemplateReq() *DescribeApplyParameterTemplateReq {
	return &DescribeApplyParameterTemplateReq{}
}

func (p *DescribeApplyParameterTemplateReq) InitDefault() {
}

func (p *DescribeApplyParameterTemplateReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeApplyParameterTemplateReq) GetTemplateId() (v string) {
	return p.TemplateId
}
func (p *DescribeApplyParameterTemplateReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeApplyParameterTemplateReq) SetTemplateId(val string) {
	p.TemplateId = val
}

var fieldIDToName_DescribeApplyParameterTemplateReq = map[int16]string{
	1: "InstanceId",
	2: "TemplateId",
}

func (p *DescribeApplyParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApplyParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTemplateId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeApplyParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeApplyParameterTemplateReq[fieldId]))
}

func (p *DescribeApplyParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeApplyParameterTemplateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateId = _field
	return nil
}

func (p *DescribeApplyParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApplyParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeApplyParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeApplyParameterTemplateReq(%+v)", *p)

}

func (p *DescribeApplyParameterTemplateReq) DeepEqual(ano *DescribeApplyParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateId) {
		return false
	}
	return true
}

func (p *DescribeApplyParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeApplyParameterTemplateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateId, src) != 0 {
		return false
	}
	return true
}

type DescribeApplyParameterTemplateResp struct {
	InstanceId string              `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	TemplateId string              `thrift:"TemplateId,2,required" frugal:"2,required,string" json:"TemplateId"`
	Parameters []*InstanceParamChg `thrift:"Parameters,3,required" frugal:"3,required,list<InstanceParamChg>" json:"Parameters"`
}

func NewDescribeApplyParameterTemplateResp() *DescribeApplyParameterTemplateResp {
	return &DescribeApplyParameterTemplateResp{}
}

func (p *DescribeApplyParameterTemplateResp) InitDefault() {
}

func (p *DescribeApplyParameterTemplateResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeApplyParameterTemplateResp) GetTemplateId() (v string) {
	return p.TemplateId
}

func (p *DescribeApplyParameterTemplateResp) GetParameters() (v []*InstanceParamChg) {
	return p.Parameters
}
func (p *DescribeApplyParameterTemplateResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeApplyParameterTemplateResp) SetTemplateId(val string) {
	p.TemplateId = val
}
func (p *DescribeApplyParameterTemplateResp) SetParameters(val []*InstanceParamChg) {
	p.Parameters = val
}

var fieldIDToName_DescribeApplyParameterTemplateResp = map[int16]string{
	1: "InstanceId",
	2: "TemplateId",
	3: "Parameters",
}

func (p *DescribeApplyParameterTemplateResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApplyParameterTemplateResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTemplateId bool = false
	var issetParameters bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameters = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetParameters {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeApplyParameterTemplateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeApplyParameterTemplateResp[fieldId]))
}

func (p *DescribeApplyParameterTemplateResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeApplyParameterTemplateResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateId = _field
	return nil
}
func (p *DescribeApplyParameterTemplateResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceParamChg, 0, size)
	values := make([]InstanceParamChg, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Parameters = _field
	return nil
}

func (p *DescribeApplyParameterTemplateResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApplyParameterTemplateResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeApplyParameterTemplateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Parameters", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Parameters)); err != nil {
		return err
	}
	for _, v := range p.Parameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeApplyParameterTemplateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeApplyParameterTemplateResp(%+v)", *p)

}

func (p *DescribeApplyParameterTemplateResp) DeepEqual(ano *DescribeApplyParameterTemplateResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Parameters) {
		return false
	}
	return true
}

func (p *DescribeApplyParameterTemplateResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeApplyParameterTemplateResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeApplyParameterTemplateResp) Field3DeepEqual(src []*InstanceParamChg) bool {

	if len(p.Parameters) != len(src) {
		return false
	}
	for i, v := range p.Parameters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SaveAsParameterTemplateReq struct {
	InstanceId   string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	TemplateName string `thrift:"TemplateName,2,required" frugal:"2,required,string" validate:"required,min=2,max=64"`
	TemplateDesc string `thrift:"TemplateDesc,3" frugal:"3,default,string" validate:"max=200"`
}

func NewSaveAsParameterTemplateReq() *SaveAsParameterTemplateReq {
	return &SaveAsParameterTemplateReq{}
}

func (p *SaveAsParameterTemplateReq) InitDefault() {
}

func (p *SaveAsParameterTemplateReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SaveAsParameterTemplateReq) GetTemplateName() (v string) {
	return p.TemplateName
}

func (p *SaveAsParameterTemplateReq) GetTemplateDesc() (v string) {
	return p.TemplateDesc
}
func (p *SaveAsParameterTemplateReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SaveAsParameterTemplateReq) SetTemplateName(val string) {
	p.TemplateName = val
}
func (p *SaveAsParameterTemplateReq) SetTemplateDesc(val string) {
	p.TemplateDesc = val
}

var fieldIDToName_SaveAsParameterTemplateReq = map[int16]string{
	1: "InstanceId",
	2: "TemplateName",
	3: "TemplateDesc",
}

func (p *SaveAsParameterTemplateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveAsParameterTemplateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTemplateName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTemplateName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SaveAsParameterTemplateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SaveAsParameterTemplateReq[fieldId]))
}

func (p *SaveAsParameterTemplateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SaveAsParameterTemplateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateName = _field
	return nil
}
func (p *SaveAsParameterTemplateReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateDesc = _field
	return nil
}

func (p *SaveAsParameterTemplateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveAsParameterTemplateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SaveAsParameterTemplateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SaveAsParameterTemplateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SaveAsParameterTemplateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SaveAsParameterTemplateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SaveAsParameterTemplateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveAsParameterTemplateReq(%+v)", *p)

}

func (p *SaveAsParameterTemplateReq) DeepEqual(ano *SaveAsParameterTemplateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateName) {
		return false
	}
	if !p.Field3DeepEqual(ano.TemplateDesc) {
		return false
	}
	return true
}

func (p *SaveAsParameterTemplateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SaveAsParameterTemplateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TemplateName, src) != 0 {
		return false
	}
	return true
}
func (p *SaveAsParameterTemplateReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TemplateDesc, src) != 0 {
		return false
	}
	return true
}

type ModifyCustomMySQLParamsReq struct {
	InstanceId    string            `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	Restart       bool              `thrift:"Restart,2,required" frugal:"2,required,bool" json:"Restart"`
	Parameters    map[string]string `thrift:"Parameters,3,required" frugal:"3,required,map<string:string>" json:"Parameters"`
	SubInstanceId *string           `thrift:"SubInstanceId,4,optional" frugal:"4,optional,string" json:"SubInstanceId,omitempty"`
}

func NewModifyCustomMySQLParamsReq() *ModifyCustomMySQLParamsReq {
	return &ModifyCustomMySQLParamsReq{}
}

func (p *ModifyCustomMySQLParamsReq) InitDefault() {
}

func (p *ModifyCustomMySQLParamsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyCustomMySQLParamsReq) GetRestart() (v bool) {
	return p.Restart
}

func (p *ModifyCustomMySQLParamsReq) GetParameters() (v map[string]string) {
	return p.Parameters
}

var ModifyCustomMySQLParamsReq_SubInstanceId_DEFAULT string

func (p *ModifyCustomMySQLParamsReq) GetSubInstanceId() (v string) {
	if !p.IsSetSubInstanceId() {
		return ModifyCustomMySQLParamsReq_SubInstanceId_DEFAULT
	}
	return *p.SubInstanceId
}
func (p *ModifyCustomMySQLParamsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyCustomMySQLParamsReq) SetRestart(val bool) {
	p.Restart = val
}
func (p *ModifyCustomMySQLParamsReq) SetParameters(val map[string]string) {
	p.Parameters = val
}
func (p *ModifyCustomMySQLParamsReq) SetSubInstanceId(val *string) {
	p.SubInstanceId = val
}

var fieldIDToName_ModifyCustomMySQLParamsReq = map[int16]string{
	1: "InstanceId",
	2: "Restart",
	3: "Parameters",
	4: "SubInstanceId",
}

func (p *ModifyCustomMySQLParamsReq) IsSetSubInstanceId() bool {
	return p.SubInstanceId != nil
}

func (p *ModifyCustomMySQLParamsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCustomMySQLParamsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetRestart bool = false
	var issetParameters bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestart = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetParameters = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRestart {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetParameters {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyCustomMySQLParamsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyCustomMySQLParamsReq[fieldId]))
}

func (p *ModifyCustomMySQLParamsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyCustomMySQLParamsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Restart = _field
	return nil
}
func (p *ModifyCustomMySQLParamsReq) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Parameters = _field
	return nil
}
func (p *ModifyCustomMySQLParamsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubInstanceId = _field
	return nil
}

func (p *ModifyCustomMySQLParamsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCustomMySQLParamsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyCustomMySQLParamsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyCustomMySQLParamsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyCustomMySQLParamsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Restart", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Restart); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyCustomMySQLParamsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Parameters", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Parameters)); err != nil {
		return err
	}
	for k, v := range p.Parameters {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyCustomMySQLParamsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubInstanceId() {
		if err = oprot.WriteFieldBegin("SubInstanceId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SubInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyCustomMySQLParamsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyCustomMySQLParamsReq(%+v)", *p)

}

func (p *ModifyCustomMySQLParamsReq) DeepEqual(ano *ModifyCustomMySQLParamsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Restart) {
		return false
	}
	if !p.Field3DeepEqual(ano.Parameters) {
		return false
	}
	if !p.Field4DeepEqual(ano.SubInstanceId) {
		return false
	}
	return true
}

func (p *ModifyCustomMySQLParamsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyCustomMySQLParamsReq) Field2DeepEqual(src bool) bool {

	if p.Restart != src {
		return false
	}
	return true
}
func (p *ModifyCustomMySQLParamsReq) Field3DeepEqual(src map[string]string) bool {

	if len(p.Parameters) != len(src) {
		return false
	}
	for k, v := range p.Parameters {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ModifyCustomMySQLParamsReq) Field4DeepEqual(src *string) bool {

	if p.SubInstanceId == src {
		return true
	} else if p.SubInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SubInstanceId, *src) != 0 {
		return false
	}
	return true
}

// Code generated by Kitex v1.18.1. DO NOT EDIT.

package v2

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *RestoreToNewInstanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetZoneId bool = false
	var issetNodeSpec bool = false
	var issetNodeNumber bool = false
	var issetVpcId bool = false
	var issetChargeInfo bool = false
	var issetSubnetId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetZoneId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetNodeSpec = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetNodeNumber = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetVpcId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetChargeInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSubnetId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField24(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField25(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetZoneId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeSpec {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNodeNumber {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetVpcId {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetChargeInfo {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetSubnetId {
		fieldId = 16
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToNewInstanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToNewInstanceReq[fieldId]))
}

func (p *RestoreToNewInstanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ZoneId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RestoreTime = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NodeSpec = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NodeNumber = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *StorageType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := StorageType(v)
		_field = &tmp
	}
	p.StorageType = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StorageSpace = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.VpcId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DBParamGroupId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField12(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeInfoObject()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ChargeInfo = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TableMeta, 0, size)
	values := make([]TableMeta, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.TableMeta = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProjectName = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*NodeInfoObject, 0, size)
	values := make([]NodeInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.NodeInfo = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SubnetId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ShardNumber = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SuperAccountPassword = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField19(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TagObject, 0, size)
	values := make([]TagObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Tags = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Databases = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field *DBEngineVersion
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField22(buf []byte) (int, error) {
	offset := 0

	var _field *InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField23(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ServerCollation = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField24(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DBTimeZone = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastReadField25(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.AllowListIds = _field
	return offset, nil
}

func (p *RestoreToNewInstanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToNewInstanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField24(buf[offset:], w)
		offset += p.fastWriteField25(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToNewInstanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field24Length()
		l += p.field25Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToNewInstanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ZoneId)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupId)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRestoreTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RestoreTime)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.NodeSpec)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], p.NodeNumber)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStorageType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.StorageType))
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStorageSpace() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.StorageSpace)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.VpcId)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBParamGroupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.DBParamGroupId)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 12)
	offset += p.ChargeInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTableMeta() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 13)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.TableMeta {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProjectName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProjectName)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 15)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeInfo {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 16)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SubnetId)
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetShardNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 17)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.ShardNumber)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuperAccountPassword() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 18)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SuperAccountPassword)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTags() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 19)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Tags {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDatabases() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 20)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Databases {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBEngineVersion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 21)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.DBEngineVersion))
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 22)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetServerCollation() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 23)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ServerCollation)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField24(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBTimeZone() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 24)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.DBTimeZone)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) fastWriteField25(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAllowListIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 25)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.AllowListIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *RestoreToNewInstanceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *RestoreToNewInstanceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ZoneId)
	return l
}

func (p *RestoreToNewInstanceReq) field3Length() int {
	l := 0
	if p.IsSetBackupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupId)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field4Length() int {
	l := 0
	if p.IsSetRestoreTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RestoreTime)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.NodeSpec)
	return l
}

func (p *RestoreToNewInstanceReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RestoreToNewInstanceReq) field7Length() int {
	l := 0
	if p.IsSetStorageType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToNewInstanceReq) field8Length() int {
	l := 0
	if p.IsSetStorageSpace() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToNewInstanceReq) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.VpcId)
	return l
}

func (p *RestoreToNewInstanceReq) field10Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field11Length() int {
	l := 0
	if p.IsSetDBParamGroupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.DBParamGroupId)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ChargeInfo.BLength()
	return l
}

func (p *RestoreToNewInstanceReq) field13Length() int {
	l := 0
	if p.IsSetTableMeta() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.TableMeta {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToNewInstanceReq) field14Length() int {
	l := 0
	if p.IsSetProjectName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProjectName)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field15Length() int {
	l := 0
	if p.IsSetNodeInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeInfo {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToNewInstanceReq) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SubnetId)
	return l
}

func (p *RestoreToNewInstanceReq) field17Length() int {
	l := 0
	if p.IsSetShardNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToNewInstanceReq) field18Length() int {
	l := 0
	if p.IsSetSuperAccountPassword() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SuperAccountPassword)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field19Length() int {
	l := 0
	if p.IsSetTags() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Tags {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToNewInstanceReq) field20Length() int {
	l := 0
	if p.IsSetDatabases() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Databases {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToNewInstanceReq) field21Length() int {
	l := 0
	if p.IsSetDBEngineVersion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToNewInstanceReq) field22Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToNewInstanceReq) field23Length() int {
	l := 0
	if p.IsSetServerCollation() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ServerCollation)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field24Length() int {
	l := 0
	if p.IsSetDBTimeZone() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.DBTimeZone)
	}
	return l
}

func (p *RestoreToNewInstanceReq) field25Length() int {
	l := 0
	if p.IsSetAllowListIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.AllowListIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *RestoreToNewInstanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToNewInstanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.ZoneId != "" {
		p.ZoneId = kutils.StringDeepCopy(src.ZoneId)
	}

	if src.BackupId != nil {
		var tmp string
		if *src.BackupId != "" {
			tmp = kutils.StringDeepCopy(*src.BackupId)
		}
		p.BackupId = &tmp
	}

	if src.RestoreTime != nil {
		var tmp string
		if *src.RestoreTime != "" {
			tmp = kutils.StringDeepCopy(*src.RestoreTime)
		}
		p.RestoreTime = &tmp
	}

	if src.NodeSpec != "" {
		p.NodeSpec = kutils.StringDeepCopy(src.NodeSpec)
	}

	p.NodeNumber = src.NodeNumber

	if src.StorageType != nil {
		tmp := *src.StorageType
		p.StorageType = &tmp
	}

	if src.StorageSpace != nil {
		tmp := *src.StorageSpace
		p.StorageSpace = &tmp
	}

	if src.VpcId != "" {
		p.VpcId = kutils.StringDeepCopy(src.VpcId)
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	if src.DBParamGroupId != nil {
		var tmp string
		if *src.DBParamGroupId != "" {
			tmp = kutils.StringDeepCopy(*src.DBParamGroupId)
		}
		p.DBParamGroupId = &tmp
	}

	var _chargeInfo *ChargeInfoObject
	if src.ChargeInfo != nil {
		_chargeInfo = &ChargeInfoObject{}
		if err := _chargeInfo.DeepCopy(src.ChargeInfo); err != nil {
			return err
		}
	}
	p.ChargeInfo = _chargeInfo

	if src.TableMeta != nil {
		p.TableMeta = make([]*TableMeta, 0, len(src.TableMeta))
		for _, elem := range src.TableMeta {
			var _elem *TableMeta
			if elem != nil {
				_elem = &TableMeta{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.TableMeta = append(p.TableMeta, _elem)
		}
	}

	if src.ProjectName != nil {
		var tmp string
		if *src.ProjectName != "" {
			tmp = kutils.StringDeepCopy(*src.ProjectName)
		}
		p.ProjectName = &tmp
	}

	if src.NodeInfo != nil {
		p.NodeInfo = make([]*NodeInfoObject, 0, len(src.NodeInfo))
		for _, elem := range src.NodeInfo {
			var _elem *NodeInfoObject
			if elem != nil {
				_elem = &NodeInfoObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.NodeInfo = append(p.NodeInfo, _elem)
		}
	}

	if src.SubnetId != "" {
		p.SubnetId = kutils.StringDeepCopy(src.SubnetId)
	}

	if src.ShardNumber != nil {
		tmp := *src.ShardNumber
		p.ShardNumber = &tmp
	}

	if src.SuperAccountPassword != nil {
		var tmp string
		if *src.SuperAccountPassword != "" {
			tmp = kutils.StringDeepCopy(*src.SuperAccountPassword)
		}
		p.SuperAccountPassword = &tmp
	}

	if src.Tags != nil {
		p.Tags = make([]*TagObject, 0, len(src.Tags))
		for _, elem := range src.Tags {
			var _elem *TagObject
			if elem != nil {
				_elem = &TagObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Tags = append(p.Tags, _elem)
		}
	}

	if src.Databases != nil {
		p.Databases = make([]*DatabasesObject, 0, len(src.Databases))
		for _, elem := range src.Databases {
			var _elem *DatabasesObject
			if elem != nil {
				_elem = &DatabasesObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Databases = append(p.Databases, _elem)
		}
	}

	if src.DBEngineVersion != nil {
		tmp := *src.DBEngineVersion
		p.DBEngineVersion = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.ServerCollation != nil {
		var tmp string
		if *src.ServerCollation != "" {
			tmp = kutils.StringDeepCopy(*src.ServerCollation)
		}
		p.ServerCollation = &tmp
	}

	if src.DBTimeZone != nil {
		var tmp string
		if *src.DBTimeZone != "" {
			tmp = kutils.StringDeepCopy(*src.DBTimeZone)
		}
		p.DBTimeZone = &tmp
	}

	if src.AllowListIds != nil {
		p.AllowListIds = make([]string, 0, len(src.AllowListIds))
		for _, elem := range src.AllowListIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.AllowListIds = append(p.AllowListIds, _elem)
		}
	}

	return nil
}

func (p *RestoreToNewInstanceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToNewInstanceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToNewInstanceResp[fieldId]))
}

func (p *RestoreToNewInstanceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderId = _field
	return offset, nil
}

func (p *RestoreToNewInstanceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToNewInstanceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToNewInstanceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToNewInstanceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *RestoreToNewInstanceResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderId)
	return offset
}

func (p *RestoreToNewInstanceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *RestoreToNewInstanceResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderId)
	return l
}

func (p *RestoreToNewInstanceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToNewInstanceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.OrderId != "" {
		p.OrderId = kutils.StringDeepCopy(src.OrderId)
	}

	return nil
}

func (p *DescribeRecoverableTimeReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeReq[fieldId]))
}

func (p *DescribeRecoverableTimeReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeRecoverableTimeReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *RestoreType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := RestoreType(v)
		_field = &tmp
	}
	p.RestoreType = _field
	return offset, nil
}

func (p *DescribeRecoverableTimeReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupRegion = _field
	return offset, nil
}

func (p *DescribeRecoverableTimeReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeRecoverableTimeReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeRecoverableTimeReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeRecoverableTimeReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeRecoverableTimeReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRestoreType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.RestoreType))
	}
	return offset
}

func (p *DescribeRecoverableTimeReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupRegion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupRegion)
	}
	return offset
}

func (p *DescribeRecoverableTimeReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeRecoverableTimeReq) field2Length() int {
	l := 0
	if p.IsSetRestoreType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeRecoverableTimeReq) field3Length() int {
	l := 0
	if p.IsSetBackupRegion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupRegion)
	}
	return l
}

func (p *DescribeRecoverableTimeReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeRecoverableTimeReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.RestoreType != nil {
		tmp := *src.RestoreType
		p.RestoreType = &tmp
	}

	if src.BackupRegion != nil {
		var tmp string
		if *src.BackupRegion != "" {
			tmp = kutils.StringDeepCopy(*src.BackupRegion)
		}
		p.BackupRegion = &tmp
	}

	return nil
}

func (p *DescribeRecoverableTimeResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRecoverableTimeInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRecoverableTimeInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRecoverableTimeInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeResp[fieldId]))
}

func (p *DescribeRecoverableTimeResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*RecoverableTimeInfo, 0, size)
	values := make([]RecoverableTimeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RecoverableTimeInfo = _field
	return offset, nil
}

func (p *DescribeRecoverableTimeResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeRecoverableTimeResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeRecoverableTimeResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeRecoverableTimeResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RecoverableTimeInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeRecoverableTimeResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RecoverableTimeInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeRecoverableTimeResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeRecoverableTimeResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RecoverableTimeInfo != nil {
		p.RecoverableTimeInfo = make([]*RecoverableTimeInfo, 0, len(src.RecoverableTimeInfo))
		for _, elem := range src.RecoverableTimeInfo {
			var _elem *RecoverableTimeInfo
			if elem != nil {
				_elem = &RecoverableTimeInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RecoverableTimeInfo = append(p.RecoverableTimeInfo, _elem)
		}
	}

	return nil
}

func (p *CreateBackupReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateBackupReq[fieldId]))
}

func (p *CreateBackupReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateBackupReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *BackupType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return offset, nil
}

func (p *CreateBackupReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *BackupMethod
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return offset, nil
}

func (p *CreateBackupReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*BackupMeta, 0, size)
	values := make([]BackupMeta, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.BackupMeta = _field
	return offset, nil
}

func (p *CreateBackupReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateBackupReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateBackupReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateBackupReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateBackupReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupType))
	}
	return offset
}

func (p *CreateBackupReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupMethod() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupMethod))
	}
	return offset
}

func (p *CreateBackupReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupMeta() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.BackupMeta {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *CreateBackupReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateBackupReq) field2Length() int {
	l := 0
	if p.IsSetBackupType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateBackupReq) field3Length() int {
	l := 0
	if p.IsSetBackupMethod() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateBackupReq) field4Length() int {
	l := 0
	if p.IsSetBackupMeta() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.BackupMeta {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *CreateBackupReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateBackupReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupType != nil {
		tmp := *src.BackupType
		p.BackupType = &tmp
	}

	if src.BackupMethod != nil {
		tmp := *src.BackupMethod
		p.BackupMethod = &tmp
	}

	if src.BackupMeta != nil {
		p.BackupMeta = make([]*BackupMeta, 0, len(src.BackupMeta))
		for _, elem := range src.BackupMeta {
			var _elem *BackupMeta
			if elem != nil {
				_elem = &BackupMeta{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.BackupMeta = append(p.BackupMeta, _elem)
		}
	}

	return nil
}

func (p *CreateBackupResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateBackupResp[fieldId]))
}

func (p *CreateBackupResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateBackupResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *CreateBackupResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateBackupResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateBackupResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateBackupResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateBackupResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *CreateBackupResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateBackupResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *CreateBackupResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateBackupResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *CreateInnerBackupReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInnerBackupReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateInnerBackupReq[fieldId]))
}

func (p *CreateInnerBackupReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateInnerBackupReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *BackupType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return offset, nil
}

func (p *CreateInnerBackupReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *BackupMethod
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return offset, nil
}

func (p *CreateInnerBackupReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateInnerBackupReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateInnerBackupReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateInnerBackupReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateInnerBackupReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupType))
	}
	return offset
}

func (p *CreateInnerBackupReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupMethod() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupMethod))
	}
	return offset
}

func (p *CreateInnerBackupReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateInnerBackupReq) field2Length() int {
	l := 0
	if p.IsSetBackupType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateInnerBackupReq) field3Length() int {
	l := 0
	if p.IsSetBackupMethod() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateInnerBackupReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateInnerBackupReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupType != nil {
		tmp := *src.BackupType
		p.BackupType = &tmp
	}

	if src.BackupMethod != nil {
		tmp := *src.BackupMethod
		p.BackupMethod = &tmp
	}

	return nil
}

func (p *CreateInnerBackupResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInnerBackupResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateInnerBackupResp[fieldId]))
}

func (p *CreateInnerBackupResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateInnerBackupResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *CreateInnerBackupResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateInnerBackupResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateInnerBackupResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateInnerBackupResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateInnerBackupResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *CreateInnerBackupResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateInnerBackupResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *CreateInnerBackupResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateInnerBackupResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *DescribeBackupsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupsReq[fieldId]))
}

func (p *DescribeBackupsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupStartTime = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupEndTime = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *BackupStatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupStatus(v)
		_field = &tmp
	}
	p.BackupStatus = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *BackupType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *BackupMethod
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *CreateType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := CreateType(v)
		_field = &tmp
	}
	p.CreateType = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *BackupRegionType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupRegionType(v)
		_field = &tmp
	}
	p.BackupRegionType = _field
	return offset, nil
}

func (p *DescribeBackupsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeBackupsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupStartTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupStartTime)
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupEndTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupEndTime)
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupStatus() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupStatus))
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupType))
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupMethod() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupMethod))
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupId)
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCreateType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.CreateType))
	}
	return offset
}

func (p *DescribeBackupsReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupRegionType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupRegionType))
	}
	return offset
}

func (p *DescribeBackupsReq) field1Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field2Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeBackupsReq) field4Length() int {
	l := 0
	if p.IsSetBackupStartTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupStartTime)
	}
	return l
}

func (p *DescribeBackupsReq) field5Length() int {
	l := 0
	if p.IsSetBackupEndTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupEndTime)
	}
	return l
}

func (p *DescribeBackupsReq) field6Length() int {
	l := 0
	if p.IsSetBackupStatus() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field7Length() int {
	l := 0
	if p.IsSetBackupType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field8Length() int {
	l := 0
	if p.IsSetBackupMethod() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field9Length() int {
	l := 0
	if p.IsSetBackupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupId)
	}
	return l
}

func (p *DescribeBackupsReq) field10Length() int {
	l := 0
	if p.IsSetCreateType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) field11Length() int {
	l := 0
	if p.IsSetBackupRegionType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBackupsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupStartTime != nil {
		var tmp string
		if *src.BackupStartTime != "" {
			tmp = kutils.StringDeepCopy(*src.BackupStartTime)
		}
		p.BackupStartTime = &tmp
	}

	if src.BackupEndTime != nil {
		var tmp string
		if *src.BackupEndTime != "" {
			tmp = kutils.StringDeepCopy(*src.BackupEndTime)
		}
		p.BackupEndTime = &tmp
	}

	if src.BackupStatus != nil {
		tmp := *src.BackupStatus
		p.BackupStatus = &tmp
	}

	if src.BackupType != nil {
		tmp := *src.BackupType
		p.BackupType = &tmp
	}

	if src.BackupMethod != nil {
		tmp := *src.BackupMethod
		p.BackupMethod = &tmp
	}

	if src.BackupId != nil {
		var tmp string
		if *src.BackupId != "" {
			tmp = kutils.StringDeepCopy(*src.BackupId)
		}
		p.BackupId = &tmp
	}

	if src.CreateType != nil {
		tmp := *src.CreateType
		p.CreateType = &tmp
	}

	if src.BackupRegionType != nil {
		tmp := *src.BackupRegionType
		p.BackupRegionType = &tmp
	}

	return nil
}

func (p *DescribeBackupsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBackupsInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupsInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupsResp[fieldId]))
}

func (p *DescribeBackupsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeBackupsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*BackupInfo, 0, size)
	values := make([]BackupInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.BackupsInfo = _field
	return offset, nil
}

func (p *DescribeBackupsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeBackupsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.BackupsInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeBackupsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeBackupsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.BackupsInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeBackupsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.BackupsInfo != nil {
		p.BackupsInfo = make([]*BackupInfo, 0, len(src.BackupsInfo))
		for _, elem := range src.BackupsInfo {
			var _elem *BackupInfo
			if elem != nil {
				_elem = &BackupInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.BackupsInfo = append(p.BackupsInfo, _elem)
		}
	}

	return nil
}

func (p *DescribeCrossBackupsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeCrossBackupsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupStartTime = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupEndTime = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *BackupType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProjectName = _field
	return offset, nil
}

func (p *DescribeCrossBackupsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeCrossBackupsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeCrossBackupsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeCrossBackupsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupStartTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupStartTime)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupEndTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupEndTime)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BackupType))
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupId)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProjectName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProjectName)
	}
	return offset
}

func (p *DescribeCrossBackupsReq) field1Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeCrossBackupsReq) field2Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeCrossBackupsReq) field3Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeCrossBackupsReq) field4Length() int {
	l := 0
	if p.IsSetBackupStartTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupStartTime)
	}
	return l
}

func (p *DescribeCrossBackupsReq) field5Length() int {
	l := 0
	if p.IsSetBackupEndTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupEndTime)
	}
	return l
}

func (p *DescribeCrossBackupsReq) field6Length() int {
	l := 0
	if p.IsSetBackupType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeCrossBackupsReq) field7Length() int {
	l := 0
	if p.IsSetBackupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupId)
	}
	return l
}

func (p *DescribeCrossBackupsReq) field8Length() int {
	l := 0
	if p.IsSetProjectName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProjectName)
	}
	return l
}

func (p *DescribeCrossBackupsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeCrossBackupsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.BackupStartTime != nil {
		var tmp string
		if *src.BackupStartTime != "" {
			tmp = kutils.StringDeepCopy(*src.BackupStartTime)
		}
		p.BackupStartTime = &tmp
	}

	if src.BackupEndTime != nil {
		var tmp string
		if *src.BackupEndTime != "" {
			tmp = kutils.StringDeepCopy(*src.BackupEndTime)
		}
		p.BackupEndTime = &tmp
	}

	if src.BackupType != nil {
		tmp := *src.BackupType
		p.BackupType = &tmp
	}

	if src.BackupId != nil {
		var tmp string
		if *src.BackupId != "" {
			tmp = kutils.StringDeepCopy(*src.BackupId)
		}
		p.BackupId = &tmp
	}

	if src.ProjectName != nil {
		var tmp string
		if *src.ProjectName != "" {
			tmp = kutils.StringDeepCopy(*src.ProjectName)
		}
		p.ProjectName = &tmp
	}

	return nil
}

func (p *DescribeCrossBackupsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBackupsInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupsInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeCrossBackupsResp[fieldId]))
}

func (p *DescribeCrossBackupsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeCrossBackupsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*BackupInfo, 0, size)
	values := make([]BackupInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.BackupsInfo = _field
	return offset, nil
}

func (p *DescribeCrossBackupsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeCrossBackupsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeCrossBackupsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeCrossBackupsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeCrossBackupsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.BackupsInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeCrossBackupsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeCrossBackupsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.BackupsInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeCrossBackupsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeCrossBackupsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.BackupsInfo != nil {
		p.BackupsInfo = make([]*BackupInfo, 0, len(src.BackupsInfo))
		for _, elem := range src.BackupsInfo {
			var _elem *BackupInfo
			if elem != nil {
				_elem = &BackupInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.BackupsInfo = append(p.BackupsInfo, _elem)
		}
	}

	return nil
}

func (p *DescribeBackupDetailReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupDetailReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupDetailReq[fieldId]))
}

func (p *DescribeBackupDetailReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBackupDetailReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DescribeBackupDetailReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupDetailReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupDetailReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupDetailReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeBackupDetailReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *DescribeBackupDetailReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeBackupDetailReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *DescribeBackupDetailReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupDetailReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *DescribeBackupDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBackupsInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBackupsInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupDetailResp[fieldId]))
}

func (p *DescribeBackupDetailResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBackupInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BackupsInfo = _field
	return offset, nil
}

func (p *DescribeBackupDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupDetailResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.BackupsInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DescribeBackupDetailResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BackupsInfo.BLength()
	return l
}

func (p *DescribeBackupDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _backupsInfo *BackupInfo
	if src.BackupsInfo != nil {
		_backupsInfo = &BackupInfo{}
		if err := _backupsInfo.DeepCopy(src.BackupsInfo); err != nil {
			return err
		}
	}
	p.BackupsInfo = _backupsInfo

	return nil
}

func (p *RestoreToCrossRegionInstanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDstRegionId bool = false
	var issetSrcRegionId bool = false
	var issetSrcRegionInstanceId bool = false
	var issetNodeSpec bool = false
	var issetStorageType bool = false
	var issetVpcId bool = false
	var issetSubnetId bool = false
	var issetSuperAccountPassword bool = false
	var issetChargeInfo bool = false
	var issetZoneId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDstRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSrcRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSrcRegionInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetNodeSpec = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStorageType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetVpcId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSubnetId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSuperAccountPassword = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetChargeInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetZoneId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDstRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSrcRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSrcRegionInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNodeSpec {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStorageType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetVpcId {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetSubnetId {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetSuperAccountPassword {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetChargeInfo {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetZoneId {
		fieldId = 21
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToCrossRegionInstanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToCrossRegionInstanceReq[fieldId]))
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DstRegionId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SrcRegionId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SrcRegionInstanceId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RestoreTime = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NodeSpec = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field StorageType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = StorageType(v)
	}
	p.StorageType = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StorageSpace = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.VpcId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SubnetId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SuperAccountPassword = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProjectName = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField14(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeInfoObject()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ChargeInfo = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Databases = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TagObject, 0, size)
	values := make([]TagObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Tags = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *DBEngineVersion
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ServerCollation = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DBTimeZone = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ZoneId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastReadField22(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.AllowListIds = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToCrossRegionInstanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DstRegionId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SrcRegionId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SrcRegionInstanceId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupId)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRestoreTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RestoreTime)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.NodeSpec)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.StorageType))
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStorageSpace() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.StorageSpace)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.VpcId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SubnetId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SuperAccountPassword)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProjectName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProjectName)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 14)
	offset += p.ChargeInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDatabases() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 15)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Databases {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTags() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 16)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Tags {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBEngineVersion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 17)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.DBEngineVersion))
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 18)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetServerCollation() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 19)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ServerCollation)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBTimeZone() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.DBTimeZone)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ZoneId)
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAllowListIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 22)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.AllowListIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *RestoreToCrossRegionInstanceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DstRegionId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SrcRegionId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SrcRegionInstanceId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field4Length() int {
	l := 0
	if p.IsSetBackupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupId)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field5Length() int {
	l := 0
	if p.IsSetRestoreTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RestoreTime)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.NodeSpec)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field8Length() int {
	l := 0
	if p.IsSetStorageSpace() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.VpcId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SubnetId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field11Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SuperAccountPassword)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field13Length() int {
	l := 0
	if p.IsSetProjectName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProjectName)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ChargeInfo.BLength()
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field15Length() int {
	l := 0
	if p.IsSetDatabases() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Databases {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field16Length() int {
	l := 0
	if p.IsSetTags() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Tags {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field17Length() int {
	l := 0
	if p.IsSetDBEngineVersion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field18Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field19Length() int {
	l := 0
	if p.IsSetServerCollation() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ServerCollation)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field20Length() int {
	l := 0
	if p.IsSetDBTimeZone() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.DBTimeZone)
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ZoneId)
	return l
}

func (p *RestoreToCrossRegionInstanceReq) field22Length() int {
	l := 0
	if p.IsSetAllowListIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.AllowListIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *RestoreToCrossRegionInstanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToCrossRegionInstanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DstRegionId != "" {
		p.DstRegionId = kutils.StringDeepCopy(src.DstRegionId)
	}

	if src.SrcRegionId != "" {
		p.SrcRegionId = kutils.StringDeepCopy(src.SrcRegionId)
	}

	if src.SrcRegionInstanceId != "" {
		p.SrcRegionInstanceId = kutils.StringDeepCopy(src.SrcRegionInstanceId)
	}

	if src.BackupId != nil {
		var tmp string
		if *src.BackupId != "" {
			tmp = kutils.StringDeepCopy(*src.BackupId)
		}
		p.BackupId = &tmp
	}

	if src.RestoreTime != nil {
		var tmp string
		if *src.RestoreTime != "" {
			tmp = kutils.StringDeepCopy(*src.RestoreTime)
		}
		p.RestoreTime = &tmp
	}

	if src.NodeSpec != "" {
		p.NodeSpec = kutils.StringDeepCopy(src.NodeSpec)
	}

	p.StorageType = src.StorageType

	if src.StorageSpace != nil {
		tmp := *src.StorageSpace
		p.StorageSpace = &tmp
	}

	if src.VpcId != "" {
		p.VpcId = kutils.StringDeepCopy(src.VpcId)
	}

	if src.SubnetId != "" {
		p.SubnetId = kutils.StringDeepCopy(src.SubnetId)
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	if src.SuperAccountPassword != "" {
		p.SuperAccountPassword = kutils.StringDeepCopy(src.SuperAccountPassword)
	}

	if src.ProjectName != nil {
		var tmp string
		if *src.ProjectName != "" {
			tmp = kutils.StringDeepCopy(*src.ProjectName)
		}
		p.ProjectName = &tmp
	}

	var _chargeInfo *ChargeInfoObject
	if src.ChargeInfo != nil {
		_chargeInfo = &ChargeInfoObject{}
		if err := _chargeInfo.DeepCopy(src.ChargeInfo); err != nil {
			return err
		}
	}
	p.ChargeInfo = _chargeInfo

	if src.Databases != nil {
		p.Databases = make([]*DatabasesObject, 0, len(src.Databases))
		for _, elem := range src.Databases {
			var _elem *DatabasesObject
			if elem != nil {
				_elem = &DatabasesObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Databases = append(p.Databases, _elem)
		}
	}

	if src.Tags != nil {
		p.Tags = make([]*TagObject, 0, len(src.Tags))
		for _, elem := range src.Tags {
			var _elem *TagObject
			if elem != nil {
				_elem = &TagObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Tags = append(p.Tags, _elem)
		}
	}

	if src.DBEngineVersion != nil {
		tmp := *src.DBEngineVersion
		p.DBEngineVersion = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.ServerCollation != nil {
		var tmp string
		if *src.ServerCollation != "" {
			tmp = kutils.StringDeepCopy(*src.ServerCollation)
		}
		p.ServerCollation = &tmp
	}

	if src.DBTimeZone != nil {
		var tmp string
		if *src.DBTimeZone != "" {
			tmp = kutils.StringDeepCopy(*src.DBTimeZone)
		}
		p.DBTimeZone = &tmp
	}

	if src.ZoneId != "" {
		p.ZoneId = kutils.StringDeepCopy(src.ZoneId)
	}

	if src.AllowListIds != nil {
		p.AllowListIds = make([]string, 0, len(src.AllowListIds))
		for _, elem := range src.AllowListIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.AllowListIds = append(p.AllowListIds, _elem)
		}
	}

	return nil
}

func (p *RestoreToCrossRegionInstanceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToCrossRegionInstanceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToCrossRegionInstanceResp[fieldId]))
}

func (p *RestoreToCrossRegionInstanceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderId = _field
	return offset, nil
}

func (p *RestoreToCrossRegionInstanceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToCrossRegionInstanceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToCrossRegionInstanceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToCrossRegionInstanceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *RestoreToCrossRegionInstanceResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderId)
	return offset
}

func (p *RestoreToCrossRegionInstanceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *RestoreToCrossRegionInstanceResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderId)
	return l
}

func (p *RestoreToCrossRegionInstanceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToCrossRegionInstanceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.OrderId != "" {
		p.OrderId = kutils.StringDeepCopy(src.OrderId)
	}

	return nil
}

func (p *DescribeDeletedBackupsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeletedBackupsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeDeletedBackupsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProjectName = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeletedBackupsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeletedBackupsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeletedBackupsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeDeletedBackupsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *DescribeDeletedBackupsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeDeletedBackupsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeDeletedBackupsReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProjectName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProjectName)
	}
	return offset
}

func (p *DescribeDeletedBackupsReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeDeletedBackupsReq) field2Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *DescribeDeletedBackupsReq) field3Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeDeletedBackupsReq) field4Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeDeletedBackupsReq) field5Length() int {
	l := 0
	if p.IsSetProjectName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProjectName)
	}
	return l
}

func (p *DescribeDeletedBackupsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeletedBackupsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.ProjectName != nil {
		var tmp string
		if *src.ProjectName != "" {
			tmp = kutils.StringDeepCopy(*src.ProjectName)
		}
		p.ProjectName = &tmp
	}

	return nil
}

func (p *DescribeDeletedBackupsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDeletedBackupInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDeletedBackupInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDeletedBackupInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeletedBackupsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeDeletedBackupsResp[fieldId]))
}

func (p *DescribeDeletedBackupsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DeletedBackupInfoObject, 0, size)
	values := make([]DeletedBackupInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DeletedBackupInfo = _field
	return offset, nil
}

func (p *DescribeDeletedBackupsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeletedBackupsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeletedBackupsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeletedBackupsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeDeletedBackupsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DeletedBackupInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeDeletedBackupsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeDeletedBackupsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DeletedBackupInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeDeletedBackupsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeletedBackupsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.DeletedBackupInfo != nil {
		p.DeletedBackupInfo = make([]*DeletedBackupInfoObject, 0, len(src.DeletedBackupInfo))
		for _, elem := range src.DeletedBackupInfo {
			var _elem *DeletedBackupInfoObject
			if elem != nil {
				_elem = &DeletedBackupInfoObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DeletedBackupInfo = append(p.DeletedBackupInfo, _elem)
		}
	}

	return nil
}

func (p *DescribeBackupPolicyReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupPolicyReq[fieldId]))
}

func (p *DescribeBackupPolicyReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBackupPolicyReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupPolicyReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupPolicyReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupPolicyReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeBackupPolicyReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeBackupPolicyReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupPolicyReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	return nil
}

func (p *DescribeBackupPolicyResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupTime bool = false
	var issetFullBackupPeriod bool = false
	var issetIncrementBackupPeriod bool = false
	var issetBackupRetentionPeriod bool = false
	var issetLogBackupInterval bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFullBackupPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIncrementBackupPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupRetentionPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLogBackupInterval = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFullBackupPeriod {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIncrementBackupPeriod {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupRetentionPeriod {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupInterval {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBackupPolicyResp[fieldId]))
}

func (p *DescribeBackupPolicyResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupTime = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FullBackupPeriod = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IncrementBackupPeriod = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupRetentionPeriod = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LogBackupInterval = _field
	return offset, nil
}

func (p *DescribeBackupPolicyResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBackupPolicyResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBackupPolicyResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBackupPolicyResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeBackupPolicyResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupTime)
	return offset
}

func (p *DescribeBackupPolicyResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FullBackupPeriod)
	return offset
}

func (p *DescribeBackupPolicyResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IncrementBackupPeriod)
	return offset
}

func (p *DescribeBackupPolicyResp) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BackupRetentionPeriod)
	return offset
}

func (p *DescribeBackupPolicyResp) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], p.LogBackupInterval)
	return offset
}

func (p *DescribeBackupPolicyResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeBackupPolicyResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupTime)
	return l
}

func (p *DescribeBackupPolicyResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FullBackupPeriod)
	return l
}

func (p *DescribeBackupPolicyResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IncrementBackupPeriod)
	return l
}

func (p *DescribeBackupPolicyResp) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeBackupPolicyResp) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeBackupPolicyResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBackupPolicyResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupTime != "" {
		p.BackupTime = kutils.StringDeepCopy(src.BackupTime)
	}

	if src.FullBackupPeriod != "" {
		p.FullBackupPeriod = kutils.StringDeepCopy(src.FullBackupPeriod)
	}

	if src.IncrementBackupPeriod != "" {
		p.IncrementBackupPeriod = kutils.StringDeepCopy(src.IncrementBackupPeriod)
	}

	p.BackupRetentionPeriod = src.BackupRetentionPeriod

	p.LogBackupInterval = src.LogBackupInterval

	return nil
}

func (p *DescribeCrossBackupPolicyReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeCrossBackupPolicyReq[fieldId]))
}

func (p *DescribeCrossBackupPolicyReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeCrossBackupPolicyReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeCrossBackupPolicyReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeCrossBackupPolicyReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeCrossBackupPolicyReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeCrossBackupPolicyReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeCrossBackupPolicyReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	return nil
}

func (p *DescribeCrossBackupPolicyResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupEnabled bool = false
	var issetLogBackupEnabled bool = false
	var issetCrossBackupRegion bool = false
	var issetRetention bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupEnabled = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLogBackupEnabled = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCrossBackupRegion = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRetention = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupEnabled {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupEnabled {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCrossBackupRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRetention {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeCrossBackupPolicyResp[fieldId]))
}

func (p *DescribeCrossBackupPolicyResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupEnabled = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LogBackupEnabled = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CrossBackupRegion = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyResp) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Retention = _field
	return offset, nil
}

func (p *DescribeCrossBackupPolicyResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeCrossBackupPolicyResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeCrossBackupPolicyResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeCrossBackupPolicyResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeCrossBackupPolicyResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 2)
	offset += thrift.Binary.WriteBool(buf[offset:], p.BackupEnabled)
	return offset
}

func (p *DescribeCrossBackupPolicyResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
	offset += thrift.Binary.WriteBool(buf[offset:], p.LogBackupEnabled)
	return offset
}

func (p *DescribeCrossBackupPolicyResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CrossBackupRegion)
	return offset
}

func (p *DescribeCrossBackupPolicyResp) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Retention)
	return offset
}

func (p *DescribeCrossBackupPolicyResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeCrossBackupPolicyResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *DescribeCrossBackupPolicyResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *DescribeCrossBackupPolicyResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CrossBackupRegion)
	return l
}

func (p *DescribeCrossBackupPolicyResp) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeCrossBackupPolicyResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeCrossBackupPolicyResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.BackupEnabled = src.BackupEnabled

	p.LogBackupEnabled = src.LogBackupEnabled

	if src.CrossBackupRegion != "" {
		p.CrossBackupRegion = kutils.StringDeepCopy(src.CrossBackupRegion)
	}

	p.Retention = src.Retention

	return nil
}

func (p *DescribeAvailableCrossRegionReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableCrossRegionReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeAvailableCrossRegionReq[fieldId]))
}

func (p *DescribeAvailableCrossRegionReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeAvailableCrossRegionReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeAvailableCrossRegionReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeAvailableCrossRegionReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeAvailableCrossRegionReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeAvailableCrossRegionReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *DescribeAvailableCrossRegionReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeAvailableCrossRegionReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *DescribeAvailableCrossRegionReq) field2Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeAvailableCrossRegionReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeAvailableCrossRegionReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	return nil
}

func (p *DescribeAvailableCrossRegionResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegions bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegions = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRegions {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableCrossRegionResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeAvailableCrossRegionResp[fieldId]))
}

func (p *DescribeAvailableCrossRegionResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.Regions = _field
	return offset, nil
}

func (p *DescribeAvailableCrossRegionResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeAvailableCrossRegionResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeAvailableCrossRegionResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeAvailableCrossRegionResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Regions {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *DescribeAvailableCrossRegionResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Regions {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *DescribeAvailableCrossRegionResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeAvailableCrossRegionResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Regions != nil {
		p.Regions = make([]string, 0, len(src.Regions))
		for _, elem := range src.Regions {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.Regions = append(p.Regions, _elem)
		}
	}

	return nil
}

func (p *DeleteBackupReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBackupReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteBackupReq[fieldId]))
}

func (p *DeleteBackupReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteBackupReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DeleteBackupReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteBackupReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteBackupReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteBackupReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DeleteBackupReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *DeleteBackupReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DeleteBackupReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *DeleteBackupReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteBackupReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *DeleteBackupResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBackupResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteBackupResp[fieldId]))
}

func (p *DeleteBackupResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteBackupResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DeleteBackupResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteBackupResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteBackupResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteBackupResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DeleteBackupResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *DeleteBackupResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DeleteBackupResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *DeleteBackupResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteBackupResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *ModifyBackupPolicyReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupTime bool = false
	var issetFullBackupPeriod bool = false
	var issetIncrementBackupPeriod bool = false
	var issetBackupRetentionPeriod bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFullBackupPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIncrementBackupPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupRetentionPeriod = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFullBackupPeriod {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIncrementBackupPeriod {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupRetentionPeriod {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyBackupPolicyReq[fieldId]))
}

func (p *ModifyBackupPolicyReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupTime = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FullBackupPeriod = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IncrementBackupPeriod = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupRetentionPeriod = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.LogBackupInterval = _field
	return offset, nil
}

func (p *ModifyBackupPolicyReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyBackupPolicyReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyBackupPolicyReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyBackupPolicyReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyBackupPolicyReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupTime)
	return offset
}

func (p *ModifyBackupPolicyReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FullBackupPeriod)
	return offset
}

func (p *ModifyBackupPolicyReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IncrementBackupPeriod)
	return offset
}

func (p *ModifyBackupPolicyReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BackupRetentionPeriod)
	return offset
}

func (p *ModifyBackupPolicyReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLogBackupInterval() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.LogBackupInterval)
	}
	return offset
}

func (p *ModifyBackupPolicyReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyBackupPolicyReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupTime)
	return l
}

func (p *ModifyBackupPolicyReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FullBackupPeriod)
	return l
}

func (p *ModifyBackupPolicyReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IncrementBackupPeriod)
	return l
}

func (p *ModifyBackupPolicyReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ModifyBackupPolicyReq) field6Length() int {
	l := 0
	if p.IsSetLogBackupInterval() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ModifyBackupPolicyReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyBackupPolicyReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupTime != "" {
		p.BackupTime = kutils.StringDeepCopy(src.BackupTime)
	}

	if src.FullBackupPeriod != "" {
		p.FullBackupPeriod = kutils.StringDeepCopy(src.FullBackupPeriod)
	}

	if src.IncrementBackupPeriod != "" {
		p.IncrementBackupPeriod = kutils.StringDeepCopy(src.IncrementBackupPeriod)
	}

	p.BackupRetentionPeriod = src.BackupRetentionPeriod

	if src.LogBackupInterval != nil {
		tmp := *src.LogBackupInterval
		p.LogBackupInterval = &tmp
	}

	return nil
}

func (p *ModifyBackupPolicyResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyBackupPolicyResp[fieldId]))
}

func (p *ModifyBackupPolicyResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyBackupPolicyResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyBackupPolicyResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyBackupPolicyResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyBackupPolicyResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyBackupPolicyResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyBackupPolicyResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyBackupPolicyResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	return nil
}

func (p *ModifyCrossBackupPolicyReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyCrossBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyCrossBackupPolicyReq[fieldId]))
}

func (p *ModifyCrossBackupPolicyReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupEnabled = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.LogBackupEnabled = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CrossBackupRegion = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Retention = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyCrossBackupPolicyReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyCrossBackupPolicyReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyCrossBackupPolicyReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyCrossBackupPolicyReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupEnabled() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 2)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.BackupEnabled)
	}
	return offset
}

func (p *ModifyCrossBackupPolicyReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLogBackupEnabled() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.LogBackupEnabled)
	}
	return offset
}

func (p *ModifyCrossBackupPolicyReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCrossBackupRegion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CrossBackupRegion)
	}
	return offset
}

func (p *ModifyCrossBackupPolicyReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRetention() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Retention)
	}
	return offset
}

func (p *ModifyCrossBackupPolicyReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyCrossBackupPolicyReq) field2Length() int {
	l := 0
	if p.IsSetBackupEnabled() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *ModifyCrossBackupPolicyReq) field3Length() int {
	l := 0
	if p.IsSetLogBackupEnabled() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *ModifyCrossBackupPolicyReq) field4Length() int {
	l := 0
	if p.IsSetCrossBackupRegion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CrossBackupRegion)
	}
	return l
}

func (p *ModifyCrossBackupPolicyReq) field5Length() int {
	l := 0
	if p.IsSetRetention() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ModifyCrossBackupPolicyReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyCrossBackupPolicyReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupEnabled != nil {
		tmp := *src.BackupEnabled
		p.BackupEnabled = &tmp
	}

	if src.LogBackupEnabled != nil {
		tmp := *src.LogBackupEnabled
		p.LogBackupEnabled = &tmp
	}

	if src.CrossBackupRegion != nil {
		var tmp string
		if *src.CrossBackupRegion != "" {
			tmp = kutils.StringDeepCopy(*src.CrossBackupRegion)
		}
		p.CrossBackupRegion = &tmp
	}

	if src.Retention != nil {
		tmp := *src.Retention
		p.Retention = &tmp
	}

	return nil
}

func (p *ModifyCrossBackupPolicyResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupEnabled bool = false
	var issetLogBackupEnabled bool = false
	var issetCrossBackupRegion bool = false
	var issetRetention bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupEnabled = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLogBackupEnabled = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCrossBackupRegion = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRetention = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupEnabled {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupEnabled {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCrossBackupRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRetention {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyCrossBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyCrossBackupPolicyResp[fieldId]))
}

func (p *ModifyCrossBackupPolicyResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupEnabled = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LogBackupEnabled = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CrossBackupRegion = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyResp) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Retention = _field
	return offset, nil
}

func (p *ModifyCrossBackupPolicyResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyCrossBackupPolicyResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyCrossBackupPolicyResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyCrossBackupPolicyResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyCrossBackupPolicyResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 2)
	offset += thrift.Binary.WriteBool(buf[offset:], p.BackupEnabled)
	return offset
}

func (p *ModifyCrossBackupPolicyResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
	offset += thrift.Binary.WriteBool(buf[offset:], p.LogBackupEnabled)
	return offset
}

func (p *ModifyCrossBackupPolicyResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CrossBackupRegion)
	return offset
}

func (p *ModifyCrossBackupPolicyResp) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Retention)
	return offset
}

func (p *ModifyCrossBackupPolicyResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyCrossBackupPolicyResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *ModifyCrossBackupPolicyResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *ModifyCrossBackupPolicyResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CrossBackupRegion)
	return l
}

func (p *ModifyCrossBackupPolicyResp) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ModifyCrossBackupPolicyResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyCrossBackupPolicyResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.BackupEnabled = src.BackupEnabled

	p.LogBackupEnabled = src.LogBackupEnabled

	if src.CrossBackupRegion != "" {
		p.CrossBackupRegion = kutils.StringDeepCopy(src.CrossBackupRegion)
	}

	p.Retention = src.Retention

	return nil
}

func (p *DownloadBackupReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadBackupReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DownloadBackupReq[fieldId]))
}

func (p *DownloadBackupReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DownloadBackupReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DownloadBackupReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DownloadBackupReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DownloadBackupReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DownloadBackupReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DownloadBackupReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *DownloadBackupReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DownloadBackupReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *DownloadBackupReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DownloadBackupReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *DownloadBackupResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadBackupResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DownloadBackupResp[fieldId]))
}

func (p *DownloadBackupResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DownloadBackupResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *DownloadBackupResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DownloadBackupResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DownloadBackupResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DownloadBackupResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DownloadBackupResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *DownloadBackupResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DownloadBackupResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *DownloadBackupResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DownloadBackupResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *GetBackupDownloadLinkReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBackupDownloadLinkReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_GetBackupDownloadLinkReq[fieldId]))
}

func (p *GetBackupDownloadLinkReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetBackupDownloadLinkReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetBackupDownloadLinkReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetBackupDownloadLinkReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *GetBackupDownloadLinkReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *GetBackupDownloadLinkReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *GetBackupDownloadLinkReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *GetBackupDownloadLinkReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GetBackupDownloadLinkReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	return nil
}

func (p *GetBackupDownloadLinkResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	var issetBackupType bool = false
	var issetBackupFileName bool = false
	var issetBackupFileSize bool = false
	var issetBackupDownloadLink bool = false
	var issetLinkExpiredTime bool = false
	var issetDownloadProgress bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupFileName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupFileSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBackupDownloadLink = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLinkExpiredTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDownloadProgress = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBackupType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBackupFileName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupFileSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetBackupDownloadLink {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLinkExpiredTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDownloadProgress {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBackupDownloadLinkResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_GetBackupDownloadLinkResp[fieldId]))
}

func (p *GetBackupDownloadLinkResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field BackupType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = BackupType(v)
	}
	p.BackupType = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupFileName = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupFileSize = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BackupDownloadLink = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LinkExpiredTime = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DownloadProgress = _field
	return offset, nil
}

func (p *GetBackupDownloadLinkResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetBackupDownloadLinkResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetBackupDownloadLinkResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetBackupDownloadLinkResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupId)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.BackupType))
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupFileName)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BackupFileSize)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BackupDownloadLink)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LinkExpiredTime)
	return offset
}

func (p *GetBackupDownloadLinkResp) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DownloadProgress)
	return offset
}

func (p *GetBackupDownloadLinkResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *GetBackupDownloadLinkResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupId)
	return l
}

func (p *GetBackupDownloadLinkResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *GetBackupDownloadLinkResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupFileName)
	return l
}

func (p *GetBackupDownloadLinkResp) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *GetBackupDownloadLinkResp) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BackupDownloadLink)
	return l
}

func (p *GetBackupDownloadLinkResp) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LinkExpiredTime)
	return l
}

func (p *GetBackupDownloadLinkResp) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *GetBackupDownloadLinkResp) DeepCopy(s interface{}) error {
	src, ok := s.(*GetBackupDownloadLinkResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BackupId != "" {
		p.BackupId = kutils.StringDeepCopy(src.BackupId)
	}

	p.BackupType = src.BackupType

	if src.BackupFileName != "" {
		p.BackupFileName = kutils.StringDeepCopy(src.BackupFileName)
	}

	p.BackupFileSize = src.BackupFileSize

	if src.BackupDownloadLink != "" {
		p.BackupDownloadLink = kutils.StringDeepCopy(src.BackupDownloadLink)
	}

	if src.LinkExpiredTime != "" {
		p.LinkExpiredTime = kutils.StringDeepCopy(src.LinkExpiredTime)
	}

	p.DownloadProgress = src.DownloadProgress

	return nil
}

func (p *RestoreToExistedInstanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSourceDBInstanceId bool = false
	var issetTargetDBInstanceId bool = false
	var issetDatabases bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSourceDBInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTargetDBInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDatabases = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSourceDBInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTargetDBInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatabases {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToExistedInstanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToExistedInstanceReq[fieldId]))
}

func (p *RestoreToExistedInstanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SourceDBInstanceId = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TargetDBInstanceId = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BackupId = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RestoreTime = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Databases = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToExistedInstanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToExistedInstanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToExistedInstanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SourceDBInstanceId)
	return offset
}

func (p *RestoreToExistedInstanceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TargetDBInstanceId)
	return offset
}

func (p *RestoreToExistedInstanceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBackupId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BackupId)
	}
	return offset
}

func (p *RestoreToExistedInstanceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRestoreTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RestoreTime)
	}
	return offset
}

func (p *RestoreToExistedInstanceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Databases {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *RestoreToExistedInstanceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SourceDBInstanceId)
	return l
}

func (p *RestoreToExistedInstanceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TargetDBInstanceId)
	return l
}

func (p *RestoreToExistedInstanceReq) field3Length() int {
	l := 0
	if p.IsSetBackupId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BackupId)
	}
	return l
}

func (p *RestoreToExistedInstanceReq) field4Length() int {
	l := 0
	if p.IsSetRestoreTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RestoreTime)
	}
	return l
}

func (p *RestoreToExistedInstanceReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Databases {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *RestoreToExistedInstanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToExistedInstanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SourceDBInstanceId != "" {
		p.SourceDBInstanceId = kutils.StringDeepCopy(src.SourceDBInstanceId)
	}

	if src.TargetDBInstanceId != "" {
		p.TargetDBInstanceId = kutils.StringDeepCopy(src.TargetDBInstanceId)
	}

	if src.BackupId != nil {
		var tmp string
		if *src.BackupId != "" {
			tmp = kutils.StringDeepCopy(*src.BackupId)
		}
		p.BackupId = &tmp
	}

	if src.RestoreTime != nil {
		var tmp string
		if *src.RestoreTime != "" {
			tmp = kutils.StringDeepCopy(*src.RestoreTime)
		}
		p.RestoreTime = &tmp
	}

	if src.Databases != nil {
		p.Databases = make([]*DatabasesObject, 0, len(src.Databases))
		for _, elem := range src.Databases {
			var _elem *DatabasesObject
			if elem != nil {
				_elem = &DatabasesObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Databases = append(p.Databases, _elem)
		}
	}

	return nil
}

func (p *RestoreToExistedInstanceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToExistedInstanceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RestoreToExistedInstanceResp[fieldId]))
}

func (p *RestoreToExistedInstanceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *RestoreToExistedInstanceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestoreToExistedInstanceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestoreToExistedInstanceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestoreToExistedInstanceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *RestoreToExistedInstanceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *RestoreToExistedInstanceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*RestoreToExistedInstanceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	return nil
}

func (p *CreateTosRestoreReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTargetDBInstanceId bool = false
	var issetTosObjectPositions bool = false
	var issetRestoreType bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTargetDBInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTosObjectPositions = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRestoreType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTargetDBInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTosObjectPositions {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRestoreType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTosRestoreReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateTosRestoreReq[fieldId]))
}

func (p *CreateTosRestoreReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TargetDBInstanceId = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TosObjectPositions = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field TosRestoreType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = TosRestoreType(v)
	}
	p.RestoreType = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IsOnline = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IsReplace = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Databases = _field
	return offset, nil
}

func (p *CreateTosRestoreReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateTosRestoreReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateTosRestoreReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateTosRestoreReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TargetDBInstanceId)
	return offset
}

func (p *CreateTosRestoreReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TosObjectPositions)
	return offset
}

func (p *CreateTosRestoreReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RestoreType))
	return offset
}

func (p *CreateTosRestoreReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIsOnline() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 4)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.IsOnline)
	}
	return offset
}

func (p *CreateTosRestoreReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIsReplace() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 5)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.IsReplace)
	}
	return offset
}

func (p *CreateTosRestoreReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDatabases() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 6)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Databases {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *CreateTosRestoreReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TargetDBInstanceId)
	return l
}

func (p *CreateTosRestoreReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TosObjectPositions)
	return l
}

func (p *CreateTosRestoreReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CreateTosRestoreReq) field4Length() int {
	l := 0
	if p.IsSetIsOnline() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *CreateTosRestoreReq) field5Length() int {
	l := 0
	if p.IsSetIsReplace() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *CreateTosRestoreReq) field6Length() int {
	l := 0
	if p.IsSetDatabases() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Databases {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *CreateTosRestoreReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateTosRestoreReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TargetDBInstanceId != "" {
		p.TargetDBInstanceId = kutils.StringDeepCopy(src.TargetDBInstanceId)
	}

	if src.TosObjectPositions != "" {
		p.TosObjectPositions = kutils.StringDeepCopy(src.TosObjectPositions)
	}

	p.RestoreType = src.RestoreType

	if src.IsOnline != nil {
		tmp := *src.IsOnline
		p.IsOnline = &tmp
	}

	if src.IsReplace != nil {
		tmp := *src.IsReplace
		p.IsReplace = &tmp
	}

	if src.Databases != nil {
		p.Databases = make([]*DatabasesObject, 0, len(src.Databases))
		for _, elem := range src.Databases {
			var _elem *DatabasesObject
			if elem != nil {
				_elem = &DatabasesObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Databases = append(p.Databases, _elem)
		}
	}

	return nil
}

func (p *CreateTosRestoreResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRestoreTaskId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRestoreTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTosRestoreResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateTosRestoreResp[fieldId]))
}

func (p *CreateTosRestoreResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RestoreTaskId = _field
	return offset, nil
}

func (p *CreateTosRestoreResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateTosRestoreResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateTosRestoreResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateTosRestoreResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RestoreTaskId)
	return offset
}

func (p *CreateTosRestoreResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RestoreTaskId)
	return l
}

func (p *CreateTosRestoreResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateTosRestoreResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RestoreTaskId != "" {
		p.RestoreTaskId = kutils.StringDeepCopy(src.RestoreTaskId)
	}

	return nil
}

func (p *DescribeTosRestoreTasksReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTasksReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeTosRestoreTasksReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.QueryStartTime = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.QueryEndTime = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProjectName = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.RestoreTaskIds = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTosRestoreTasksReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTosRestoreTasksReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTosRestoreTasksReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryStartTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.QueryStartTime)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryEndTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.QueryEndTime)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProjectName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProjectName)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRestoreTaskIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 8)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.RestoreTaskIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeTosRestoreTasksReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field2Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field3Length() int {
	l := 0
	if p.IsSetQueryStartTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.QueryStartTime)
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field4Length() int {
	l := 0
	if p.IsSetQueryEndTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.QueryEndTime)
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field5Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field6Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field7Length() int {
	l := 0
	if p.IsSetProjectName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProjectName)
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) field8Length() int {
	l := 0
	if p.IsSetRestoreTaskIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.RestoreTaskIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeTosRestoreTasksReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTosRestoreTasksReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	if src.QueryStartTime != nil {
		var tmp string
		if *src.QueryStartTime != "" {
			tmp = kutils.StringDeepCopy(*src.QueryStartTime)
		}
		p.QueryStartTime = &tmp
	}

	if src.QueryEndTime != nil {
		var tmp string
		if *src.QueryEndTime != "" {
			tmp = kutils.StringDeepCopy(*src.QueryEndTime)
		}
		p.QueryEndTime = &tmp
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.ProjectName != nil {
		var tmp string
		if *src.ProjectName != "" {
			tmp = kutils.StringDeepCopy(*src.ProjectName)
		}
		p.ProjectName = &tmp
	}

	if src.RestoreTaskIds != nil {
		p.RestoreTaskIds = make([]string, 0, len(src.RestoreTaskIds))
		for _, elem := range src.RestoreTaskIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.RestoreTaskIds = append(p.RestoreTaskIds, _elem)
		}
	}

	return nil
}

func (p *DescribeTosRestoreTasksResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetRestoreTasks bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRestoreTasks = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRestoreTasks {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTasksResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTosRestoreTasksResp[fieldId]))
}

func (p *DescribeTosRestoreTasksResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*RestoreTaskObject, 0, size)
	values := make([]RestoreTaskObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RestoreTasks = _field
	return offset, nil
}

func (p *DescribeTosRestoreTasksResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTosRestoreTasksResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTosRestoreTasksResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTosRestoreTasksResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeTosRestoreTasksResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RestoreTasks {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeTosRestoreTasksResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTosRestoreTasksResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RestoreTasks {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeTosRestoreTasksResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTosRestoreTasksResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.RestoreTasks != nil {
		p.RestoreTasks = make([]*RestoreTaskObject, 0, len(src.RestoreTasks))
		for _, elem := range src.RestoreTasks {
			var _elem *RestoreTaskObject
			if elem != nil {
				_elem = &RestoreTaskObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RestoreTasks = append(p.RestoreTasks, _elem)
		}
	}

	return nil
}

func (p *DescribeTosRestoreTaskDetailReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRestoreTaskId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRestoreTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTaskDetailReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTosRestoreTaskDetailReq[fieldId]))
}

func (p *DescribeTosRestoreTaskDetailReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RestoreTaskId = _field
	return offset, nil
}

func (p *DescribeTosRestoreTaskDetailReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeTosRestoreTaskDetailReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeTosRestoreTaskDetailReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTosRestoreTaskDetailReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTosRestoreTaskDetailReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTosRestoreTaskDetailReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RestoreTaskId)
	return offset
}

func (p *DescribeTosRestoreTaskDetailReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeTosRestoreTaskDetailReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeTosRestoreTaskDetailReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RestoreTaskId)
	return l
}

func (p *DescribeTosRestoreTaskDetailReq) field2Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTosRestoreTaskDetailReq) field3Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTosRestoreTaskDetailReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTosRestoreTaskDetailReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RestoreTaskId != "" {
		p.RestoreTaskId = kutils.StringDeepCopy(src.RestoreTaskId)
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	return nil
}

func (p *DescribeTosRestoreTaskDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetRestoreTaskDetails bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskDetails = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRestoreTaskDetails {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTaskDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTosRestoreTaskDetailResp[fieldId]))
}

func (p *DescribeTosRestoreTaskDetailResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeTosRestoreTaskDetailResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*RestoreTaskDetailObject, 0, size)
	values := make([]RestoreTaskDetailObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RestoreTaskDetails = _field
	return offset, nil
}

func (p *DescribeTosRestoreTaskDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTosRestoreTaskDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTosRestoreTaskDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTosRestoreTaskDetailResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeTosRestoreTaskDetailResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RestoreTaskDetails {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeTosRestoreTaskDetailResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTosRestoreTaskDetailResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RestoreTaskDetails {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeTosRestoreTaskDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTosRestoreTaskDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.RestoreTaskDetails != nil {
		p.RestoreTaskDetails = make([]*RestoreTaskDetailObject, 0, len(src.RestoreTaskDetails))
		for _, elem := range src.RestoreTaskDetails {
			var _elem *RestoreTaskDetailObject
			if elem != nil {
				_elem = &RestoreTaskDetailObject{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RestoreTaskDetails = append(p.RestoreTaskDetails, _elem)
		}
	}

	return nil
}

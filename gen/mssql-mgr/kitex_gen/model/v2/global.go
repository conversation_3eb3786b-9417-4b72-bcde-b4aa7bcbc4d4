// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeRegionsReq struct {
}

func NewDescribeRegionsReq() *DescribeRegionsReq {
	return &DescribeRegionsReq{}
}

func (p *DescribeRegionsReq) InitDefault() {
}

var fieldIDToName_DescribeRegionsReq = map[int16]string{}

func (p *DescribeRegionsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRegionsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeRegionsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRegionsReq")

	if err = oprot.WriteStructBegin("DescribeRegionsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRegionsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRegionsReq(%+v)", *p)

}

func (p *DescribeRegionsReq) DeepEqual(ano *DescribeRegionsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeRegionsResp struct {
	Regions []*Region `thrift:"Regions,1,required" frugal:"1,required,list<Region>" json:"Regions"`
}

func NewDescribeRegionsResp() *DescribeRegionsResp {
	return &DescribeRegionsResp{}
}

func (p *DescribeRegionsResp) InitDefault() {
}

func (p *DescribeRegionsResp) GetRegions() (v []*Region) {
	return p.Regions
}
func (p *DescribeRegionsResp) SetRegions(val []*Region) {
	p.Regions = val
}

var fieldIDToName_DescribeRegionsResp = map[int16]string{
	1: "Regions",
}

func (p *DescribeRegionsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRegionsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegions bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegions {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRegionsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRegionsResp[fieldId]))
}

func (p *DescribeRegionsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Region, 0, size)
	values := make([]Region, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Regions = _field
	return nil
}

func (p *DescribeRegionsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRegionsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRegionsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRegionsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Regions", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Regions)); err != nil {
		return err
	}
	for _, v := range p.Regions {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRegionsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRegionsResp(%+v)", *p)

}

func (p *DescribeRegionsResp) DeepEqual(ano *DescribeRegionsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Regions) {
		return false
	}
	return true
}

func (p *DescribeRegionsResp) Field1DeepEqual(src []*Region) bool {

	if len(p.Regions) != len(src) {
		return false
	}
	for i, v := range p.Regions {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeAvailabilityZonesReq struct {
	RegionId *string `thrift:"RegionId,1,optional" frugal:"1,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeAvailabilityZonesReq() *DescribeAvailabilityZonesReq {
	return &DescribeAvailabilityZonesReq{}
}

func (p *DescribeAvailabilityZonesReq) InitDefault() {
}

var DescribeAvailabilityZonesReq_RegionId_DEFAULT string

func (p *DescribeAvailabilityZonesReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeAvailabilityZonesReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeAvailabilityZonesReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeAvailabilityZonesReq = map[int16]string{
	1: "RegionId",
}

func (p *DescribeAvailabilityZonesReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeAvailabilityZonesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailabilityZonesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailabilityZonesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAvailabilityZonesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeAvailabilityZonesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailabilityZonesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailabilityZonesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailabilityZonesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailabilityZonesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailabilityZonesReq(%+v)", *p)

}

func (p *DescribeAvailabilityZonesReq) DeepEqual(ano *DescribeAvailabilityZonesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeAvailabilityZonesReq) Field1DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeAvailabilityZonesResp struct {
	RegionId string  `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	Zones    []*Zone `thrift:"Zones,2,required" frugal:"2,required,list<Zone>" json:"Zones"`
}

func NewDescribeAvailabilityZonesResp() *DescribeAvailabilityZonesResp {
	return &DescribeAvailabilityZonesResp{}
}

func (p *DescribeAvailabilityZonesResp) InitDefault() {
}

func (p *DescribeAvailabilityZonesResp) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAvailabilityZonesResp) GetZones() (v []*Zone) {
	return p.Zones
}
func (p *DescribeAvailabilityZonesResp) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAvailabilityZonesResp) SetZones(val []*Zone) {
	p.Zones = val
}

var fieldIDToName_DescribeAvailabilityZonesResp = map[int16]string{
	1: "RegionId",
	2: "Zones",
}

func (p *DescribeAvailabilityZonesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailabilityZonesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetZones bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetZones = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetZones {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailabilityZonesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAvailabilityZonesResp[fieldId]))
}

func (p *DescribeAvailabilityZonesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAvailabilityZonesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Zone, 0, size)
	values := make([]Zone, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Zones = _field
	return nil
}

func (p *DescribeAvailabilityZonesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailabilityZonesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailabilityZonesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailabilityZonesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailabilityZonesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Zones", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Zones)); err != nil {
		return err
	}
	for _, v := range p.Zones {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAvailabilityZonesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailabilityZonesResp(%+v)", *p)

}

func (p *DescribeAvailabilityZonesResp) DeepEqual(ano *DescribeAvailabilityZonesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Zones) {
		return false
	}
	return true
}

func (p *DescribeAvailabilityZonesResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAvailabilityZonesResp) Field2DeepEqual(src []*Zone) bool {

	if len(p.Zones) != len(src) {
		return false
	}
	for i, v := range p.Zones {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type RestoreToNewInstanceReq struct {
	InstanceId           string             `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"SrcInstanceId"`
	ZoneId               string             `thrift:"ZoneId,2,required" frugal:"2,required,string" json:"ZoneId"`
	BackupId             *string            `thrift:"BackupId,3,optional" frugal:"3,optional,string" json:"BackupId,omitempty"`
	RestoreTime          *string            `thrift:"RestoreTime,4,optional" frugal:"4,optional,string" json:"RestoreTime,omitempty"`
	NodeSpec             string             `thrift:"NodeSpec,5,required" frugal:"5,required,string" json:"NodeSpec"`
	NodeNumber           int32              `thrift:"NodeNumber,6,required" frugal:"6,required,i32" json:"NodeNumber"`
	StorageType          *StorageType       `thrift:"StorageType,7,optional" frugal:"7,optional,StorageType" json:"StorageType,omitempty"`
	StorageSpace         *int32             `thrift:"StorageSpace,8,optional" frugal:"8,optional,i32" json:"StorageSpace,omitempty"`
	VpcId                string             `thrift:"VpcId,9,required" frugal:"9,required,string" json:"VpcId"`
	InstanceName         *string            `thrift:"InstanceName,10,optional" frugal:"10,optional,string" json:"InstanceName,omitempty"`
	DBParamGroupId       *string            `thrift:"DBParamGroupId,11,optional" frugal:"11,optional,string" json:"DBParamGroupId,omitempty"`
	ChargeInfo           *ChargeInfoObject  `thrift:"ChargeInfo,12,required" frugal:"12,required,ChargeInfoObject" json:"ChargeInfo"`
	TableMeta            []*TableMeta       `thrift:"TableMeta,13,optional" frugal:"13,optional,list<TableMeta>" json:"TableMeta,omitempty"`
	ProjectName          *string            `thrift:"ProjectName,14,optional" frugal:"14,optional,string" json:"ProjectName,omitempty"`
	NodeInfo             []*NodeInfoObject  `thrift:"NodeInfo,15,optional" frugal:"15,optional,list<NodeInfoObject>" json:"NodeInfo,omitempty"`
	SubnetId             string             `thrift:"SubnetId,16,required" frugal:"16,required,string" json:"SubnetId"`
	ShardNumber          *int32             `thrift:"ShardNumber,17,optional" frugal:"17,optional,i32" json:"ShardNumber,omitempty"`
	SuperAccountPassword *string            `thrift:"SuperAccountPassword,18,optional" frugal:"18,optional,string" json:"SuperAccountPassword,omitempty"`
	Tags                 []*TagObject       `thrift:"Tags,19,optional" frugal:"19,optional,list<TagObject>" json:"Tags,omitempty"`
	Databases            []*DatabasesObject `thrift:"Databases,20,optional" frugal:"20,optional,list<DatabasesObject>" json:"Databases,omitempty"`
	DBEngineVersion      *DBEngineVersion   `thrift:"DBEngineVersion,21,optional" frugal:"21,optional,DBEngineVersion" json:"DBEngineVersion,omitempty"`
	InstanceType         *InstanceType      `thrift:"InstanceType,22,optional" frugal:"22,optional,InstanceType" json:"InstanceType,omitempty"`
	ServerCollation      *string            `thrift:"ServerCollation,23,optional" frugal:"23,optional,string" json:"ServerCollation,omitempty"`
	DBTimeZone           *string            `thrift:"DBTimeZone,24,optional" frugal:"24,optional,string" json:"DBTimeZone,omitempty"`
	AllowListIds         []string           `thrift:"AllowListIds,25,optional" frugal:"25,optional,list<string>" json:"AllowListIds,omitempty"`
}

func NewRestoreToNewInstanceReq() *RestoreToNewInstanceReq {
	return &RestoreToNewInstanceReq{}
}

func (p *RestoreToNewInstanceReq) InitDefault() {
}

func (p *RestoreToNewInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RestoreToNewInstanceReq) GetZoneId() (v string) {
	return p.ZoneId
}

var RestoreToNewInstanceReq_BackupId_DEFAULT string

func (p *RestoreToNewInstanceReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return RestoreToNewInstanceReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var RestoreToNewInstanceReq_RestoreTime_DEFAULT string

func (p *RestoreToNewInstanceReq) GetRestoreTime() (v string) {
	if !p.IsSetRestoreTime() {
		return RestoreToNewInstanceReq_RestoreTime_DEFAULT
	}
	return *p.RestoreTime
}

func (p *RestoreToNewInstanceReq) GetNodeSpec() (v string) {
	return p.NodeSpec
}

func (p *RestoreToNewInstanceReq) GetNodeNumber() (v int32) {
	return p.NodeNumber
}

var RestoreToNewInstanceReq_StorageType_DEFAULT StorageType

func (p *RestoreToNewInstanceReq) GetStorageType() (v StorageType) {
	if !p.IsSetStorageType() {
		return RestoreToNewInstanceReq_StorageType_DEFAULT
	}
	return *p.StorageType
}

var RestoreToNewInstanceReq_StorageSpace_DEFAULT int32

func (p *RestoreToNewInstanceReq) GetStorageSpace() (v int32) {
	if !p.IsSetStorageSpace() {
		return RestoreToNewInstanceReq_StorageSpace_DEFAULT
	}
	return *p.StorageSpace
}

func (p *RestoreToNewInstanceReq) GetVpcId() (v string) {
	return p.VpcId
}

var RestoreToNewInstanceReq_InstanceName_DEFAULT string

func (p *RestoreToNewInstanceReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return RestoreToNewInstanceReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var RestoreToNewInstanceReq_DBParamGroupId_DEFAULT string

func (p *RestoreToNewInstanceReq) GetDBParamGroupId() (v string) {
	if !p.IsSetDBParamGroupId() {
		return RestoreToNewInstanceReq_DBParamGroupId_DEFAULT
	}
	return *p.DBParamGroupId
}

var RestoreToNewInstanceReq_ChargeInfo_DEFAULT *ChargeInfoObject

func (p *RestoreToNewInstanceReq) GetChargeInfo() (v *ChargeInfoObject) {
	if !p.IsSetChargeInfo() {
		return RestoreToNewInstanceReq_ChargeInfo_DEFAULT
	}
	return p.ChargeInfo
}

var RestoreToNewInstanceReq_TableMeta_DEFAULT []*TableMeta

func (p *RestoreToNewInstanceReq) GetTableMeta() (v []*TableMeta) {
	if !p.IsSetTableMeta() {
		return RestoreToNewInstanceReq_TableMeta_DEFAULT
	}
	return p.TableMeta
}

var RestoreToNewInstanceReq_ProjectName_DEFAULT string

func (p *RestoreToNewInstanceReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return RestoreToNewInstanceReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var RestoreToNewInstanceReq_NodeInfo_DEFAULT []*NodeInfoObject

func (p *RestoreToNewInstanceReq) GetNodeInfo() (v []*NodeInfoObject) {
	if !p.IsSetNodeInfo() {
		return RestoreToNewInstanceReq_NodeInfo_DEFAULT
	}
	return p.NodeInfo
}

func (p *RestoreToNewInstanceReq) GetSubnetId() (v string) {
	return p.SubnetId
}

var RestoreToNewInstanceReq_ShardNumber_DEFAULT int32

func (p *RestoreToNewInstanceReq) GetShardNumber() (v int32) {
	if !p.IsSetShardNumber() {
		return RestoreToNewInstanceReq_ShardNumber_DEFAULT
	}
	return *p.ShardNumber
}

var RestoreToNewInstanceReq_SuperAccountPassword_DEFAULT string

func (p *RestoreToNewInstanceReq) GetSuperAccountPassword() (v string) {
	if !p.IsSetSuperAccountPassword() {
		return RestoreToNewInstanceReq_SuperAccountPassword_DEFAULT
	}
	return *p.SuperAccountPassword
}

var RestoreToNewInstanceReq_Tags_DEFAULT []*TagObject

func (p *RestoreToNewInstanceReq) GetTags() (v []*TagObject) {
	if !p.IsSetTags() {
		return RestoreToNewInstanceReq_Tags_DEFAULT
	}
	return p.Tags
}

var RestoreToNewInstanceReq_Databases_DEFAULT []*DatabasesObject

func (p *RestoreToNewInstanceReq) GetDatabases() (v []*DatabasesObject) {
	if !p.IsSetDatabases() {
		return RestoreToNewInstanceReq_Databases_DEFAULT
	}
	return p.Databases
}

var RestoreToNewInstanceReq_DBEngineVersion_DEFAULT DBEngineVersion

func (p *RestoreToNewInstanceReq) GetDBEngineVersion() (v DBEngineVersion) {
	if !p.IsSetDBEngineVersion() {
		return RestoreToNewInstanceReq_DBEngineVersion_DEFAULT
	}
	return *p.DBEngineVersion
}

var RestoreToNewInstanceReq_InstanceType_DEFAULT InstanceType

func (p *RestoreToNewInstanceReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return RestoreToNewInstanceReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var RestoreToNewInstanceReq_ServerCollation_DEFAULT string

func (p *RestoreToNewInstanceReq) GetServerCollation() (v string) {
	if !p.IsSetServerCollation() {
		return RestoreToNewInstanceReq_ServerCollation_DEFAULT
	}
	return *p.ServerCollation
}

var RestoreToNewInstanceReq_DBTimeZone_DEFAULT string

func (p *RestoreToNewInstanceReq) GetDBTimeZone() (v string) {
	if !p.IsSetDBTimeZone() {
		return RestoreToNewInstanceReq_DBTimeZone_DEFAULT
	}
	return *p.DBTimeZone
}

var RestoreToNewInstanceReq_AllowListIds_DEFAULT []string

func (p *RestoreToNewInstanceReq) GetAllowListIds() (v []string) {
	if !p.IsSetAllowListIds() {
		return RestoreToNewInstanceReq_AllowListIds_DEFAULT
	}
	return p.AllowListIds
}
func (p *RestoreToNewInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RestoreToNewInstanceReq) SetZoneId(val string) {
	p.ZoneId = val
}
func (p *RestoreToNewInstanceReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *RestoreToNewInstanceReq) SetRestoreTime(val *string) {
	p.RestoreTime = val
}
func (p *RestoreToNewInstanceReq) SetNodeSpec(val string) {
	p.NodeSpec = val
}
func (p *RestoreToNewInstanceReq) SetNodeNumber(val int32) {
	p.NodeNumber = val
}
func (p *RestoreToNewInstanceReq) SetStorageType(val *StorageType) {
	p.StorageType = val
}
func (p *RestoreToNewInstanceReq) SetStorageSpace(val *int32) {
	p.StorageSpace = val
}
func (p *RestoreToNewInstanceReq) SetVpcId(val string) {
	p.VpcId = val
}
func (p *RestoreToNewInstanceReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *RestoreToNewInstanceReq) SetDBParamGroupId(val *string) {
	p.DBParamGroupId = val
}
func (p *RestoreToNewInstanceReq) SetChargeInfo(val *ChargeInfoObject) {
	p.ChargeInfo = val
}
func (p *RestoreToNewInstanceReq) SetTableMeta(val []*TableMeta) {
	p.TableMeta = val
}
func (p *RestoreToNewInstanceReq) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *RestoreToNewInstanceReq) SetNodeInfo(val []*NodeInfoObject) {
	p.NodeInfo = val
}
func (p *RestoreToNewInstanceReq) SetSubnetId(val string) {
	p.SubnetId = val
}
func (p *RestoreToNewInstanceReq) SetShardNumber(val *int32) {
	p.ShardNumber = val
}
func (p *RestoreToNewInstanceReq) SetSuperAccountPassword(val *string) {
	p.SuperAccountPassword = val
}
func (p *RestoreToNewInstanceReq) SetTags(val []*TagObject) {
	p.Tags = val
}
func (p *RestoreToNewInstanceReq) SetDatabases(val []*DatabasesObject) {
	p.Databases = val
}
func (p *RestoreToNewInstanceReq) SetDBEngineVersion(val *DBEngineVersion) {
	p.DBEngineVersion = val
}
func (p *RestoreToNewInstanceReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *RestoreToNewInstanceReq) SetServerCollation(val *string) {
	p.ServerCollation = val
}
func (p *RestoreToNewInstanceReq) SetDBTimeZone(val *string) {
	p.DBTimeZone = val
}
func (p *RestoreToNewInstanceReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_RestoreToNewInstanceReq = map[int16]string{
	1:  "InstanceId",
	2:  "ZoneId",
	3:  "BackupId",
	4:  "RestoreTime",
	5:  "NodeSpec",
	6:  "NodeNumber",
	7:  "StorageType",
	8:  "StorageSpace",
	9:  "VpcId",
	10: "InstanceName",
	11: "DBParamGroupId",
	12: "ChargeInfo",
	13: "TableMeta",
	14: "ProjectName",
	15: "NodeInfo",
	16: "SubnetId",
	17: "ShardNumber",
	18: "SuperAccountPassword",
	19: "Tags",
	20: "Databases",
	21: "DBEngineVersion",
	22: "InstanceType",
	23: "ServerCollation",
	24: "DBTimeZone",
	25: "AllowListIds",
}

func (p *RestoreToNewInstanceReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *RestoreToNewInstanceReq) IsSetRestoreTime() bool {
	return p.RestoreTime != nil
}

func (p *RestoreToNewInstanceReq) IsSetStorageType() bool {
	return p.StorageType != nil
}

func (p *RestoreToNewInstanceReq) IsSetStorageSpace() bool {
	return p.StorageSpace != nil
}

func (p *RestoreToNewInstanceReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *RestoreToNewInstanceReq) IsSetDBParamGroupId() bool {
	return p.DBParamGroupId != nil
}

func (p *RestoreToNewInstanceReq) IsSetChargeInfo() bool {
	return p.ChargeInfo != nil
}

func (p *RestoreToNewInstanceReq) IsSetTableMeta() bool {
	return p.TableMeta != nil
}

func (p *RestoreToNewInstanceReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *RestoreToNewInstanceReq) IsSetNodeInfo() bool {
	return p.NodeInfo != nil
}

func (p *RestoreToNewInstanceReq) IsSetShardNumber() bool {
	return p.ShardNumber != nil
}

func (p *RestoreToNewInstanceReq) IsSetSuperAccountPassword() bool {
	return p.SuperAccountPassword != nil
}

func (p *RestoreToNewInstanceReq) IsSetTags() bool {
	return p.Tags != nil
}

func (p *RestoreToNewInstanceReq) IsSetDatabases() bool {
	return p.Databases != nil
}

func (p *RestoreToNewInstanceReq) IsSetDBEngineVersion() bool {
	return p.DBEngineVersion != nil
}

func (p *RestoreToNewInstanceReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *RestoreToNewInstanceReq) IsSetServerCollation() bool {
	return p.ServerCollation != nil
}

func (p *RestoreToNewInstanceReq) IsSetDBTimeZone() bool {
	return p.DBTimeZone != nil
}

func (p *RestoreToNewInstanceReq) IsSetAllowListIds() bool {
	return p.AllowListIds != nil
}

func (p *RestoreToNewInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToNewInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetZoneId bool = false
	var issetNodeSpec bool = false
	var issetNodeNumber bool = false
	var issetVpcId bool = false
	var issetChargeInfo bool = false
	var issetSubnetId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetZoneId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeSpec = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetVpcId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetChargeInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetSubnetId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetZoneId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeSpec {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNodeNumber {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetVpcId {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetChargeInfo {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetSubnetId {
		fieldId = 16
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToNewInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToNewInstanceReq[fieldId]))
}

func (p *RestoreToNewInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ZoneId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeSpec = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeNumber = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *StorageType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := StorageType(v)
		_field = &tmp
	}
	p.StorageType = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StorageSpace = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VpcId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBParamGroupId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField12(iprot thrift.TProtocol) error {
	_field := NewChargeInfoObject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ChargeInfo = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TableMeta, 0, size)
	values := make([]TableMeta, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TableMeta = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeInfoObject, 0, size)
	values := make([]NodeInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeInfo = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubnetId = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardNumber = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField18(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuperAccountPassword = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField19(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TagObject, 0, size)
	values := make([]TagObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Databases = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField21(iprot thrift.TProtocol) error {

	var _field *DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField22(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField23(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ServerCollation = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField24(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBTimeZone = _field
	return nil
}
func (p *RestoreToNewInstanceReq) ReadField25(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *RestoreToNewInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToNewInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToNewInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ZoneId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTime() {
		if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RestoreTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeSpec", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeSpec); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeNumber", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.NodeNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageType() {
		if err = oprot.WriteFieldBegin("StorageType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.StorageType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageSpace() {
		if err = oprot.WriteFieldBegin("StorageSpace", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StorageSpace); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VpcId", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBParamGroupId() {
		if err = oprot.WriteFieldBegin("DBParamGroupId", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBParamGroupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChargeInfo", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ChargeInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableMeta() {
		if err = oprot.WriteFieldBegin("TableMeta", thrift.LIST, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TableMeta)); err != nil {
			return err
		}
		for _, v := range p.TableMeta {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeInfo() {
		if err = oprot.WriteFieldBegin("NodeInfo", thrift.LIST, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeInfo)); err != nil {
			return err
		}
		for _, v := range p.NodeInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SubnetId", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubnetId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardNumber() {
		if err = oprot.WriteFieldBegin("ShardNumber", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ShardNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuperAccountPassword() {
		if err = oprot.WriteFieldBegin("SuperAccountPassword", thrift.STRING, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SuperAccountPassword); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabases() {
		if err = oprot.WriteFieldBegin("Databases", thrift.LIST, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Databases)); err != nil {
			return err
		}
		for _, v := range p.Databases {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineVersion() {
		if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.I32, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DBEngineVersion)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetServerCollation() {
		if err = oprot.WriteFieldBegin("ServerCollation", thrift.STRING, 23); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ServerCollation); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBTimeZone() {
		if err = oprot.WriteFieldBegin("DBTimeZone", thrift.STRING, 24); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBTimeZone); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) writeField25(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListIds() {
		if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 25); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
			return err
		}
		for _, v := range p.AllowListIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}

func (p *RestoreToNewInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToNewInstanceReq(%+v)", *p)

}

func (p *RestoreToNewInstanceReq) DeepEqual(ano *RestoreToNewInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ZoneId) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field4DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.NodeSpec) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.StorageType) {
		return false
	}
	if !p.Field8DeepEqual(ano.StorageSpace) {
		return false
	}
	if !p.Field9DeepEqual(ano.VpcId) {
		return false
	}
	if !p.Field10DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field11DeepEqual(ano.DBParamGroupId) {
		return false
	}
	if !p.Field12DeepEqual(ano.ChargeInfo) {
		return false
	}
	if !p.Field13DeepEqual(ano.TableMeta) {
		return false
	}
	if !p.Field14DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field15DeepEqual(ano.NodeInfo) {
		return false
	}
	if !p.Field16DeepEqual(ano.SubnetId) {
		return false
	}
	if !p.Field17DeepEqual(ano.ShardNumber) {
		return false
	}
	if !p.Field18DeepEqual(ano.SuperAccountPassword) {
		return false
	}
	if !p.Field19DeepEqual(ano.Tags) {
		return false
	}
	if !p.Field20DeepEqual(ano.Databases) {
		return false
	}
	if !p.Field21DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field22DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field23DeepEqual(ano.ServerCollation) {
		return false
	}
	if !p.Field24DeepEqual(ano.DBTimeZone) {
		return false
	}
	if !p.Field25DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *RestoreToNewInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ZoneId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field3DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field4DeepEqual(src *string) bool {

	if p.RestoreTime == src {
		return true
	} else if p.RestoreTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RestoreTime, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.NodeSpec, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field6DeepEqual(src int32) bool {

	if p.NodeNumber != src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field7DeepEqual(src *StorageType) bool {

	if p.StorageType == src {
		return true
	} else if p.StorageType == nil || src == nil {
		return false
	}
	if *p.StorageType != *src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field8DeepEqual(src *int32) bool {

	if p.StorageSpace == src {
		return true
	} else if p.StorageSpace == nil || src == nil {
		return false
	}
	if *p.StorageSpace != *src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field9DeepEqual(src string) bool {

	if strings.Compare(p.VpcId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field10DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field11DeepEqual(src *string) bool {

	if p.DBParamGroupId == src {
		return true
	} else if p.DBParamGroupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBParamGroupId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field12DeepEqual(src *ChargeInfoObject) bool {

	if !p.ChargeInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field13DeepEqual(src []*TableMeta) bool {

	if len(p.TableMeta) != len(src) {
		return false
	}
	for i, v := range p.TableMeta {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field14DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field15DeepEqual(src []*NodeInfoObject) bool {

	if len(p.NodeInfo) != len(src) {
		return false
	}
	for i, v := range p.NodeInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field16DeepEqual(src string) bool {

	if strings.Compare(p.SubnetId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field17DeepEqual(src *int32) bool {

	if p.ShardNumber == src {
		return true
	} else if p.ShardNumber == nil || src == nil {
		return false
	}
	if *p.ShardNumber != *src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field18DeepEqual(src *string) bool {

	if p.SuperAccountPassword == src {
		return true
	} else if p.SuperAccountPassword == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SuperAccountPassword, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field19DeepEqual(src []*TagObject) bool {

	if len(p.Tags) != len(src) {
		return false
	}
	for i, v := range p.Tags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field20DeepEqual(src []*DatabasesObject) bool {

	if len(p.Databases) != len(src) {
		return false
	}
	for i, v := range p.Databases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field21DeepEqual(src *DBEngineVersion) bool {

	if p.DBEngineVersion == src {
		return true
	} else if p.DBEngineVersion == nil || src == nil {
		return false
	}
	if *p.DBEngineVersion != *src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field22DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field23DeepEqual(src *string) bool {

	if p.ServerCollation == src {
		return true
	} else if p.ServerCollation == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ServerCollation, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field24DeepEqual(src *string) bool {

	if p.DBTimeZone == src {
		return true
	} else if p.DBTimeZone == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBTimeZone, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceReq) Field25DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type RestoreToNewInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	OrderId    string `thrift:"OrderId,2,required" frugal:"2,required,string" json:"OrderId"`
}

func NewRestoreToNewInstanceResp() *RestoreToNewInstanceResp {
	return &RestoreToNewInstanceResp{}
}

func (p *RestoreToNewInstanceResp) InitDefault() {
}

func (p *RestoreToNewInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RestoreToNewInstanceResp) GetOrderId() (v string) {
	return p.OrderId
}
func (p *RestoreToNewInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RestoreToNewInstanceResp) SetOrderId(val string) {
	p.OrderId = val
}

var fieldIDToName_RestoreToNewInstanceResp = map[int16]string{
	1: "InstanceId",
	2: "OrderId",
}

func (p *RestoreToNewInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToNewInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToNewInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToNewInstanceResp[fieldId]))
}

func (p *RestoreToNewInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestoreToNewInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}

func (p *RestoreToNewInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToNewInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToNewInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToNewInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToNewInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreToNewInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToNewInstanceResp(%+v)", *p)

}

func (p *RestoreToNewInstanceResp) DeepEqual(ano *RestoreToNewInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderId) {
		return false
	}
	return true
}

func (p *RestoreToNewInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToNewInstanceResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}

type DescribeRecoverableTimeReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	RestoreType  *RestoreType `thrift:"RestoreType,2,optional" frugal:"2,optional,RestoreType" json:"RestoreType,omitempty"`
	BackupRegion *string      `thrift:"BackupRegion,3,optional" frugal:"3,optional,string" json:"BackupRegion,omitempty"`
}

func NewDescribeRecoverableTimeReq() *DescribeRecoverableTimeReq {
	return &DescribeRecoverableTimeReq{}
}

func (p *DescribeRecoverableTimeReq) InitDefault() {
}

func (p *DescribeRecoverableTimeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeRecoverableTimeReq_RestoreType_DEFAULT RestoreType

func (p *DescribeRecoverableTimeReq) GetRestoreType() (v RestoreType) {
	if !p.IsSetRestoreType() {
		return DescribeRecoverableTimeReq_RestoreType_DEFAULT
	}
	return *p.RestoreType
}

var DescribeRecoverableTimeReq_BackupRegion_DEFAULT string

func (p *DescribeRecoverableTimeReq) GetBackupRegion() (v string) {
	if !p.IsSetBackupRegion() {
		return DescribeRecoverableTimeReq_BackupRegion_DEFAULT
	}
	return *p.BackupRegion
}
func (p *DescribeRecoverableTimeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeRecoverableTimeReq) SetRestoreType(val *RestoreType) {
	p.RestoreType = val
}
func (p *DescribeRecoverableTimeReq) SetBackupRegion(val *string) {
	p.BackupRegion = val
}

var fieldIDToName_DescribeRecoverableTimeReq = map[int16]string{
	1: "InstanceId",
	2: "RestoreType",
	3: "BackupRegion",
}

func (p *DescribeRecoverableTimeReq) IsSetRestoreType() bool {
	return p.RestoreType != nil
}

func (p *DescribeRecoverableTimeReq) IsSetBackupRegion() bool {
	return p.BackupRegion != nil
}

func (p *DescribeRecoverableTimeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeReq[fieldId]))
}

func (p *DescribeRecoverableTimeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeRecoverableTimeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *RestoreType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RestoreType(v)
		_field = &tmp
	}
	p.RestoreType = _field
	return nil
}
func (p *DescribeRecoverableTimeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupRegion = _field
	return nil
}

func (p *DescribeRecoverableTimeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableTimeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreType() {
		if err = oprot.WriteFieldBegin("RestoreType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RestoreType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupRegion() {
		if err = oprot.WriteFieldBegin("BackupRegion", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupRegion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeRecoverableTimeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableTimeReq(%+v)", *p)

}

func (p *DescribeRecoverableTimeReq) DeepEqual(ano *DescribeRecoverableTimeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.RestoreType) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupRegion) {
		return false
	}
	return true
}

func (p *DescribeRecoverableTimeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecoverableTimeReq) Field2DeepEqual(src *RestoreType) bool {

	if p.RestoreType == src {
		return true
	} else if p.RestoreType == nil || src == nil {
		return false
	}
	if *p.RestoreType != *src {
		return false
	}
	return true
}
func (p *DescribeRecoverableTimeReq) Field3DeepEqual(src *string) bool {

	if p.BackupRegion == src {
		return true
	} else if p.BackupRegion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupRegion, *src) != 0 {
		return false
	}
	return true
}

type DescribeRecoverableTimeResp struct {
	RecoverableTimeInfo []*RecoverableTimeInfo `thrift:"RecoverableTimeInfo,1,required" frugal:"1,required,list<RecoverableTimeInfo>" json:"RecoverableTimeInfo"`
}

func NewDescribeRecoverableTimeResp() *DescribeRecoverableTimeResp {
	return &DescribeRecoverableTimeResp{}
}

func (p *DescribeRecoverableTimeResp) InitDefault() {
}

func (p *DescribeRecoverableTimeResp) GetRecoverableTimeInfo() (v []*RecoverableTimeInfo) {
	return p.RecoverableTimeInfo
}
func (p *DescribeRecoverableTimeResp) SetRecoverableTimeInfo(val []*RecoverableTimeInfo) {
	p.RecoverableTimeInfo = val
}

var fieldIDToName_DescribeRecoverableTimeResp = map[int16]string{
	1: "RecoverableTimeInfo",
}

func (p *DescribeRecoverableTimeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRecoverableTimeInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRecoverableTimeInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRecoverableTimeInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableTimeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableTimeResp[fieldId]))
}

func (p *DescribeRecoverableTimeResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RecoverableTimeInfo, 0, size)
	values := make([]RecoverableTimeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RecoverableTimeInfo = _field
	return nil
}

func (p *DescribeRecoverableTimeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableTimeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableTimeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableTimeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RecoverableTimeInfo", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RecoverableTimeInfo)); err != nil {
		return err
	}
	for _, v := range p.RecoverableTimeInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableTimeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableTimeResp(%+v)", *p)

}

func (p *DescribeRecoverableTimeResp) DeepEqual(ano *DescribeRecoverableTimeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RecoverableTimeInfo) {
		return false
	}
	return true
}

func (p *DescribeRecoverableTimeResp) Field1DeepEqual(src []*RecoverableTimeInfo) bool {

	if len(p.RecoverableTimeInfo) != len(src) {
		return false
	}
	for i, v := range p.RecoverableTimeInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateBackupReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupType   *BackupType   `thrift:"BackupType,2,optional" frugal:"2,optional,BackupType" json:"BackupType,omitempty"`
	BackupMethod *BackupMethod `thrift:"BackupMethod,3,optional" frugal:"3,optional,BackupMethod" json:"BackupMethod,omitempty"`
	BackupMeta   []*BackupMeta `thrift:"BackupMeta,4,optional" frugal:"4,optional,list<BackupMeta>" json:"BackupMeta,omitempty"`
}

func NewCreateBackupReq() *CreateBackupReq {
	return &CreateBackupReq{}
}

func (p *CreateBackupReq) InitDefault() {
}

func (p *CreateBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateBackupReq_BackupType_DEFAULT BackupType

func (p *CreateBackupReq) GetBackupType() (v BackupType) {
	if !p.IsSetBackupType() {
		return CreateBackupReq_BackupType_DEFAULT
	}
	return *p.BackupType
}

var CreateBackupReq_BackupMethod_DEFAULT BackupMethod

func (p *CreateBackupReq) GetBackupMethod() (v BackupMethod) {
	if !p.IsSetBackupMethod() {
		return CreateBackupReq_BackupMethod_DEFAULT
	}
	return *p.BackupMethod
}

var CreateBackupReq_BackupMeta_DEFAULT []*BackupMeta

func (p *CreateBackupReq) GetBackupMeta() (v []*BackupMeta) {
	if !p.IsSetBackupMeta() {
		return CreateBackupReq_BackupMeta_DEFAULT
	}
	return p.BackupMeta
}
func (p *CreateBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateBackupReq) SetBackupType(val *BackupType) {
	p.BackupType = val
}
func (p *CreateBackupReq) SetBackupMethod(val *BackupMethod) {
	p.BackupMethod = val
}
func (p *CreateBackupReq) SetBackupMeta(val []*BackupMeta) {
	p.BackupMeta = val
}

var fieldIDToName_CreateBackupReq = map[int16]string{
	1: "InstanceId",
	2: "BackupType",
	3: "BackupMethod",
	4: "BackupMeta",
}

func (p *CreateBackupReq) IsSetBackupType() bool {
	return p.BackupType != nil
}

func (p *CreateBackupReq) IsSetBackupMethod() bool {
	return p.BackupMethod != nil
}

func (p *CreateBackupReq) IsSetBackupMeta() bool {
	return p.BackupMeta != nil
}

func (p *CreateBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBackupReq[fieldId]))
}

func (p *CreateBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateBackupReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return nil
}
func (p *CreateBackupReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *BackupMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return nil
}
func (p *CreateBackupReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*BackupMeta, 0, size)
	values := make([]BackupMeta, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BackupMeta = _field
	return nil
}

func (p *CreateBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBackupReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupType() {
		if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateBackupReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupMethod() {
		if err = oprot.WriteFieldBegin("BackupMethod", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupMethod)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateBackupReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupMeta() {
		if err = oprot.WriteFieldBegin("BackupMeta", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BackupMeta)); err != nil {
			return err
		}
		for _, v := range p.BackupMeta {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBackupReq(%+v)", *p)

}

func (p *CreateBackupReq) DeepEqual(ano *CreateBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupMethod) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupMeta) {
		return false
	}
	return true
}

func (p *CreateBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field2DeepEqual(src *BackupType) bool {

	if p.BackupType == src {
		return true
	} else if p.BackupType == nil || src == nil {
		return false
	}
	if *p.BackupType != *src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field3DeepEqual(src *BackupMethod) bool {

	if p.BackupMethod == src {
		return true
	} else if p.BackupMethod == nil || src == nil {
		return false
	}
	if *p.BackupMethod != *src {
		return false
	}
	return true
}
func (p *CreateBackupReq) Field4DeepEqual(src []*BackupMeta) bool {

	if len(p.BackupMeta) != len(src) {
		return false
	}
	for i, v := range p.BackupMeta {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateBackupResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewCreateBackupResp() *CreateBackupResp {
	return &CreateBackupResp{}
}

func (p *CreateBackupResp) InitDefault() {
}

func (p *CreateBackupResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateBackupResp) GetBackupId() (v string) {
	return p.BackupId
}
func (p *CreateBackupResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateBackupResp) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_CreateBackupResp = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *CreateBackupResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBackupResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBackupResp[fieldId]))
}

func (p *CreateBackupResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateBackupResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *CreateBackupResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBackupResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBackupResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBackupResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBackupResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateBackupResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBackupResp(%+v)", *p)

}

func (p *CreateBackupResp) DeepEqual(ano *CreateBackupResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *CreateBackupResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBackupResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type CreateInnerBackupReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupType   *BackupType   `thrift:"BackupType,2,optional" frugal:"2,optional,BackupType" json:"BackupType,omitempty"`
	BackupMethod *BackupMethod `thrift:"BackupMethod,3,optional" frugal:"3,optional,BackupMethod" json:"BackupMethod,omitempty"`
}

func NewCreateInnerBackupReq() *CreateInnerBackupReq {
	return &CreateInnerBackupReq{}
}

func (p *CreateInnerBackupReq) InitDefault() {
}

func (p *CreateInnerBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateInnerBackupReq_BackupType_DEFAULT BackupType

func (p *CreateInnerBackupReq) GetBackupType() (v BackupType) {
	if !p.IsSetBackupType() {
		return CreateInnerBackupReq_BackupType_DEFAULT
	}
	return *p.BackupType
}

var CreateInnerBackupReq_BackupMethod_DEFAULT BackupMethod

func (p *CreateInnerBackupReq) GetBackupMethod() (v BackupMethod) {
	if !p.IsSetBackupMethod() {
		return CreateInnerBackupReq_BackupMethod_DEFAULT
	}
	return *p.BackupMethod
}
func (p *CreateInnerBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateInnerBackupReq) SetBackupType(val *BackupType) {
	p.BackupType = val
}
func (p *CreateInnerBackupReq) SetBackupMethod(val *BackupMethod) {
	p.BackupMethod = val
}

var fieldIDToName_CreateInnerBackupReq = map[int16]string{
	1: "InstanceId",
	2: "BackupType",
	3: "BackupMethod",
}

func (p *CreateInnerBackupReq) IsSetBackupType() bool {
	return p.BackupType != nil
}

func (p *CreateInnerBackupReq) IsSetBackupMethod() bool {
	return p.BackupMethod != nil
}

func (p *CreateInnerBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInnerBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInnerBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateInnerBackupReq[fieldId]))
}

func (p *CreateInnerBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateInnerBackupReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return nil
}
func (p *CreateInnerBackupReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *BackupMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return nil
}

func (p *CreateInnerBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInnerBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInnerBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateInnerBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateInnerBackupReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupType() {
		if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateInnerBackupReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupMethod() {
		if err = oprot.WriteFieldBegin("BackupMethod", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupMethod)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateInnerBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateInnerBackupReq(%+v)", *p)

}

func (p *CreateInnerBackupReq) DeepEqual(ano *CreateInnerBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupMethod) {
		return false
	}
	return true
}

func (p *CreateInnerBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateInnerBackupReq) Field2DeepEqual(src *BackupType) bool {

	if p.BackupType == src {
		return true
	} else if p.BackupType == nil || src == nil {
		return false
	}
	if *p.BackupType != *src {
		return false
	}
	return true
}
func (p *CreateInnerBackupReq) Field3DeepEqual(src *BackupMethod) bool {

	if p.BackupMethod == src {
		return true
	} else if p.BackupMethod == nil || src == nil {
		return false
	}
	if *p.BackupMethod != *src {
		return false
	}
	return true
}

type CreateInnerBackupResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewCreateInnerBackupResp() *CreateInnerBackupResp {
	return &CreateInnerBackupResp{}
}

func (p *CreateInnerBackupResp) InitDefault() {
}

func (p *CreateInnerBackupResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateInnerBackupResp) GetBackupId() (v string) {
	return p.BackupId
}
func (p *CreateInnerBackupResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateInnerBackupResp) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_CreateInnerBackupResp = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *CreateInnerBackupResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInnerBackupResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInnerBackupResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateInnerBackupResp[fieldId]))
}

func (p *CreateInnerBackupResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateInnerBackupResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *CreateInnerBackupResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInnerBackupResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInnerBackupResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateInnerBackupResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateInnerBackupResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateInnerBackupResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateInnerBackupResp(%+v)", *p)

}

func (p *CreateInnerBackupResp) DeepEqual(ano *CreateInnerBackupResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *CreateInnerBackupResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateInnerBackupResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type DescribeBackupsReq struct {
	PageNumber       *int32            `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize         *int32            `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	InstanceId       string            `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	BackupStartTime  *string           `thrift:"BackupStartTime,4,optional" frugal:"4,optional,string" json:"BackupStartTime,omitempty"`
	BackupEndTime    *string           `thrift:"BackupEndTime,5,optional" frugal:"5,optional,string" json:"BackupEndTime,omitempty"`
	BackupStatus     *BackupStatus     `thrift:"BackupStatus,6,optional" frugal:"6,optional,BackupStatus" json:"BackupStatus,omitempty"`
	BackupType       *BackupType       `thrift:"BackupType,7,optional" frugal:"7,optional,BackupType" json:"BackupType,omitempty"`
	BackupMethod     *BackupMethod     `thrift:"BackupMethod,8,optional" frugal:"8,optional,BackupMethod" json:"BackupMethod,omitempty"`
	BackupId         *string           `thrift:"BackupId,9,optional" frugal:"9,optional,string" json:"BackupId,omitempty"`
	CreateType       *CreateType       `thrift:"CreateType,10,optional" frugal:"10,optional,CreateType" json:"CreateType,omitempty"`
	BackupRegionType *BackupRegionType `thrift:"BackupRegionType,11,optional" frugal:"11,optional,BackupRegionType" json:"BackupRegionType,omitempty"`
}

func NewDescribeBackupsReq() *DescribeBackupsReq {
	return &DescribeBackupsReq{}
}

func (p *DescribeBackupsReq) InitDefault() {
}

var DescribeBackupsReq_PageNumber_DEFAULT int32

func (p *DescribeBackupsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeBackupsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeBackupsReq_PageSize_DEFAULT int32

func (p *DescribeBackupsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeBackupsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

func (p *DescribeBackupsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeBackupsReq_BackupStartTime_DEFAULT string

func (p *DescribeBackupsReq) GetBackupStartTime() (v string) {
	if !p.IsSetBackupStartTime() {
		return DescribeBackupsReq_BackupStartTime_DEFAULT
	}
	return *p.BackupStartTime
}

var DescribeBackupsReq_BackupEndTime_DEFAULT string

func (p *DescribeBackupsReq) GetBackupEndTime() (v string) {
	if !p.IsSetBackupEndTime() {
		return DescribeBackupsReq_BackupEndTime_DEFAULT
	}
	return *p.BackupEndTime
}

var DescribeBackupsReq_BackupStatus_DEFAULT BackupStatus

func (p *DescribeBackupsReq) GetBackupStatus() (v BackupStatus) {
	if !p.IsSetBackupStatus() {
		return DescribeBackupsReq_BackupStatus_DEFAULT
	}
	return *p.BackupStatus
}

var DescribeBackupsReq_BackupType_DEFAULT BackupType

func (p *DescribeBackupsReq) GetBackupType() (v BackupType) {
	if !p.IsSetBackupType() {
		return DescribeBackupsReq_BackupType_DEFAULT
	}
	return *p.BackupType
}

var DescribeBackupsReq_BackupMethod_DEFAULT BackupMethod

func (p *DescribeBackupsReq) GetBackupMethod() (v BackupMethod) {
	if !p.IsSetBackupMethod() {
		return DescribeBackupsReq_BackupMethod_DEFAULT
	}
	return *p.BackupMethod
}

var DescribeBackupsReq_BackupId_DEFAULT string

func (p *DescribeBackupsReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return DescribeBackupsReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var DescribeBackupsReq_CreateType_DEFAULT CreateType

func (p *DescribeBackupsReq) GetCreateType() (v CreateType) {
	if !p.IsSetCreateType() {
		return DescribeBackupsReq_CreateType_DEFAULT
	}
	return *p.CreateType
}

var DescribeBackupsReq_BackupRegionType_DEFAULT BackupRegionType

func (p *DescribeBackupsReq) GetBackupRegionType() (v BackupRegionType) {
	if !p.IsSetBackupRegionType() {
		return DescribeBackupsReq_BackupRegionType_DEFAULT
	}
	return *p.BackupRegionType
}
func (p *DescribeBackupsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeBackupsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeBackupsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeBackupsReq) SetBackupStartTime(val *string) {
	p.BackupStartTime = val
}
func (p *DescribeBackupsReq) SetBackupEndTime(val *string) {
	p.BackupEndTime = val
}
func (p *DescribeBackupsReq) SetBackupStatus(val *BackupStatus) {
	p.BackupStatus = val
}
func (p *DescribeBackupsReq) SetBackupType(val *BackupType) {
	p.BackupType = val
}
func (p *DescribeBackupsReq) SetBackupMethod(val *BackupMethod) {
	p.BackupMethod = val
}
func (p *DescribeBackupsReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *DescribeBackupsReq) SetCreateType(val *CreateType) {
	p.CreateType = val
}
func (p *DescribeBackupsReq) SetBackupRegionType(val *BackupRegionType) {
	p.BackupRegionType = val
}

var fieldIDToName_DescribeBackupsReq = map[int16]string{
	1:  "PageNumber",
	2:  "PageSize",
	3:  "InstanceId",
	4:  "BackupStartTime",
	5:  "BackupEndTime",
	6:  "BackupStatus",
	7:  "BackupType",
	8:  "BackupMethod",
	9:  "BackupId",
	10: "CreateType",
	11: "BackupRegionType",
}

func (p *DescribeBackupsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeBackupsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeBackupsReq) IsSetBackupStartTime() bool {
	return p.BackupStartTime != nil
}

func (p *DescribeBackupsReq) IsSetBackupEndTime() bool {
	return p.BackupEndTime != nil
}

func (p *DescribeBackupsReq) IsSetBackupStatus() bool {
	return p.BackupStatus != nil
}

func (p *DescribeBackupsReq) IsSetBackupType() bool {
	return p.BackupType != nil
}

func (p *DescribeBackupsReq) IsSetBackupMethod() bool {
	return p.BackupMethod != nil
}

func (p *DescribeBackupsReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *DescribeBackupsReq) IsSetCreateType() bool {
	return p.CreateType != nil
}

func (p *DescribeBackupsReq) IsSetBackupRegionType() bool {
	return p.BackupRegionType != nil
}

func (p *DescribeBackupsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupsReq[fieldId]))
}

func (p *DescribeBackupsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupStartTime = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupEndTime = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *BackupStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupStatus(v)
		_field = &tmp
	}
	p.BackupStatus = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *BackupMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupMethod(v)
		_field = &tmp
	}
	p.BackupMethod = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *CreateType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CreateType(v)
		_field = &tmp
	}
	p.CreateType = _field
	return nil
}
func (p *DescribeBackupsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *BackupRegionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupRegionType(v)
		_field = &tmp
	}
	p.BackupRegionType = _field
	return nil
}

func (p *DescribeBackupsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupStartTime() {
		if err = oprot.WriteFieldBegin("BackupStartTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupEndTime() {
		if err = oprot.WriteFieldBegin("BackupEndTime", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupStatus() {
		if err = oprot.WriteFieldBegin("BackupStatus", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupType() {
		if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupMethod() {
		if err = oprot.WriteFieldBegin("BackupMethod", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupMethod)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateType() {
		if err = oprot.WriteFieldBegin("CreateType", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.CreateType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeBackupsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupRegionType() {
		if err = oprot.WriteFieldBegin("BackupRegionType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupRegionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeBackupsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupsReq(%+v)", *p)

}

func (p *DescribeBackupsReq) DeepEqual(ano *DescribeBackupsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupStartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupEndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.BackupStatus) {
		return false
	}
	if !p.Field7DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field8DeepEqual(ano.BackupMethod) {
		return false
	}
	if !p.Field9DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreateType) {
		return false
	}
	if !p.Field11DeepEqual(ano.BackupRegionType) {
		return false
	}
	return true
}

func (p *DescribeBackupsReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field4DeepEqual(src *string) bool {

	if p.BackupStartTime == src {
		return true
	} else if p.BackupStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field5DeepEqual(src *string) bool {

	if p.BackupEndTime == src {
		return true
	} else if p.BackupEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field6DeepEqual(src *BackupStatus) bool {

	if p.BackupStatus == src {
		return true
	} else if p.BackupStatus == nil || src == nil {
		return false
	}
	if *p.BackupStatus != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field7DeepEqual(src *BackupType) bool {

	if p.BackupType == src {
		return true
	} else if p.BackupType == nil || src == nil {
		return false
	}
	if *p.BackupType != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field8DeepEqual(src *BackupMethod) bool {

	if p.BackupMethod == src {
		return true
	} else if p.BackupMethod == nil || src == nil {
		return false
	}
	if *p.BackupMethod != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field9DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field10DeepEqual(src *CreateType) bool {

	if p.CreateType == src {
		return true
	} else if p.CreateType == nil || src == nil {
		return false
	}
	if *p.CreateType != *src {
		return false
	}
	return true
}
func (p *DescribeBackupsReq) Field11DeepEqual(src *BackupRegionType) bool {

	if p.BackupRegionType == src {
		return true
	} else if p.BackupRegionType == nil || src == nil {
		return false
	}
	if *p.BackupRegionType != *src {
		return false
	}
	return true
}

type DescribeBackupsResp struct {
	Total       int32         `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	BackupsInfo []*BackupInfo `thrift:"BackupsInfo,2,required" frugal:"2,required,list<BackupInfo>" json:"BackupsInfo"`
}

func NewDescribeBackupsResp() *DescribeBackupsResp {
	return &DescribeBackupsResp{}
}

func (p *DescribeBackupsResp) InitDefault() {
}

func (p *DescribeBackupsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeBackupsResp) GetBackupsInfo() (v []*BackupInfo) {
	return p.BackupsInfo
}
func (p *DescribeBackupsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeBackupsResp) SetBackupsInfo(val []*BackupInfo) {
	p.BackupsInfo = val
}

var fieldIDToName_DescribeBackupsResp = map[int16]string{
	1: "Total",
	2: "BackupsInfo",
}

func (p *DescribeBackupsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBackupsInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupsInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupsResp[fieldId]))
}

func (p *DescribeBackupsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeBackupsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*BackupInfo, 0, size)
	values := make([]BackupInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BackupsInfo = _field
	return nil
}

func (p *DescribeBackupsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupsInfo", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BackupsInfo)); err != nil {
		return err
	}
	for _, v := range p.BackupsInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBackupsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupsResp(%+v)", *p)

}

func (p *DescribeBackupsResp) DeepEqual(ano *DescribeBackupsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupsInfo) {
		return false
	}
	return true
}

func (p *DescribeBackupsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeBackupsResp) Field2DeepEqual(src []*BackupInfo) bool {

	if len(p.BackupsInfo) != len(src) {
		return false
	}
	for i, v := range p.BackupsInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeCrossBackupsReq struct {
	PageNumber      *int32      `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize        *int32      `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	InstanceId      *string     `thrift:"InstanceId,3,optional" frugal:"3,optional,string" json:"InstanceId,omitempty"`
	BackupStartTime *string     `thrift:"BackupStartTime,4,optional" frugal:"4,optional,string" json:"BackupStartTime,omitempty"`
	BackupEndTime   *string     `thrift:"BackupEndTime,5,optional" frugal:"5,optional,string" json:"BackupEndTime,omitempty"`
	BackupType      *BackupType `thrift:"BackupType,6,optional" frugal:"6,optional,BackupType" json:"BackupType,omitempty"`
	BackupId        *string     `thrift:"BackupId,7,optional" frugal:"7,optional,string" json:"BackupId,omitempty"`
	ProjectName     *string     `thrift:"ProjectName,8,optional" frugal:"8,optional,string" json:"ProjectName,omitempty"`
}

func NewDescribeCrossBackupsReq() *DescribeCrossBackupsReq {
	return &DescribeCrossBackupsReq{}
}

func (p *DescribeCrossBackupsReq) InitDefault() {
}

var DescribeCrossBackupsReq_PageNumber_DEFAULT int32

func (p *DescribeCrossBackupsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeCrossBackupsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeCrossBackupsReq_PageSize_DEFAULT int32

func (p *DescribeCrossBackupsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeCrossBackupsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeCrossBackupsReq_InstanceId_DEFAULT string

func (p *DescribeCrossBackupsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeCrossBackupsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeCrossBackupsReq_BackupStartTime_DEFAULT string

func (p *DescribeCrossBackupsReq) GetBackupStartTime() (v string) {
	if !p.IsSetBackupStartTime() {
		return DescribeCrossBackupsReq_BackupStartTime_DEFAULT
	}
	return *p.BackupStartTime
}

var DescribeCrossBackupsReq_BackupEndTime_DEFAULT string

func (p *DescribeCrossBackupsReq) GetBackupEndTime() (v string) {
	if !p.IsSetBackupEndTime() {
		return DescribeCrossBackupsReq_BackupEndTime_DEFAULT
	}
	return *p.BackupEndTime
}

var DescribeCrossBackupsReq_BackupType_DEFAULT BackupType

func (p *DescribeCrossBackupsReq) GetBackupType() (v BackupType) {
	if !p.IsSetBackupType() {
		return DescribeCrossBackupsReq_BackupType_DEFAULT
	}
	return *p.BackupType
}

var DescribeCrossBackupsReq_BackupId_DEFAULT string

func (p *DescribeCrossBackupsReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return DescribeCrossBackupsReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var DescribeCrossBackupsReq_ProjectName_DEFAULT string

func (p *DescribeCrossBackupsReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeCrossBackupsReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *DescribeCrossBackupsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeCrossBackupsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeCrossBackupsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeCrossBackupsReq) SetBackupStartTime(val *string) {
	p.BackupStartTime = val
}
func (p *DescribeCrossBackupsReq) SetBackupEndTime(val *string) {
	p.BackupEndTime = val
}
func (p *DescribeCrossBackupsReq) SetBackupType(val *BackupType) {
	p.BackupType = val
}
func (p *DescribeCrossBackupsReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *DescribeCrossBackupsReq) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_DescribeCrossBackupsReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "InstanceId",
	4: "BackupStartTime",
	5: "BackupEndTime",
	6: "BackupType",
	7: "BackupId",
	8: "ProjectName",
}

func (p *DescribeCrossBackupsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeCrossBackupsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeCrossBackupsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeCrossBackupsReq) IsSetBackupStartTime() bool {
	return p.BackupStartTime != nil
}

func (p *DescribeCrossBackupsReq) IsSetBackupEndTime() bool {
	return p.BackupEndTime != nil
}

func (p *DescribeCrossBackupsReq) IsSetBackupType() bool {
	return p.BackupType != nil
}

func (p *DescribeCrossBackupsReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *DescribeCrossBackupsReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeCrossBackupsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupStartTime = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupEndTime = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BackupType(v)
		_field = &tmp
	}
	p.BackupType = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *DescribeCrossBackupsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *DescribeCrossBackupsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCrossBackupsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupStartTime() {
		if err = oprot.WriteFieldBegin("BackupStartTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupEndTime() {
		if err = oprot.WriteFieldBegin("BackupEndTime", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupType() {
		if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BackupType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeCrossBackupsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCrossBackupsReq(%+v)", *p)

}

func (p *DescribeCrossBackupsReq) DeepEqual(ano *DescribeCrossBackupsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupStartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupEndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field7DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field8DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *DescribeCrossBackupsReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field3DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field4DeepEqual(src *string) bool {

	if p.BackupStartTime == src {
		return true
	} else if p.BackupStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field5DeepEqual(src *string) bool {

	if p.BackupEndTime == src {
		return true
	} else if p.BackupEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field6DeepEqual(src *BackupType) bool {

	if p.BackupType == src {
		return true
	} else if p.BackupType == nil || src == nil {
		return false
	}
	if *p.BackupType != *src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field7DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsReq) Field8DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type DescribeCrossBackupsResp struct {
	Total       int32         `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	BackupsInfo []*BackupInfo `thrift:"BackupsInfo,2,required" frugal:"2,required,list<BackupInfo>" json:"BackupsInfo"`
}

func NewDescribeCrossBackupsResp() *DescribeCrossBackupsResp {
	return &DescribeCrossBackupsResp{}
}

func (p *DescribeCrossBackupsResp) InitDefault() {
}

func (p *DescribeCrossBackupsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeCrossBackupsResp) GetBackupsInfo() (v []*BackupInfo) {
	return p.BackupsInfo
}
func (p *DescribeCrossBackupsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeCrossBackupsResp) SetBackupsInfo(val []*BackupInfo) {
	p.BackupsInfo = val
}

var fieldIDToName_DescribeCrossBackupsResp = map[int16]string{
	1: "Total",
	2: "BackupsInfo",
}

func (p *DescribeCrossBackupsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBackupsInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupsInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCrossBackupsResp[fieldId]))
}

func (p *DescribeCrossBackupsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeCrossBackupsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*BackupInfo, 0, size)
	values := make([]BackupInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BackupsInfo = _field
	return nil
}

func (p *DescribeCrossBackupsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCrossBackupsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCrossBackupsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCrossBackupsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupsInfo", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BackupsInfo)); err != nil {
		return err
	}
	for _, v := range p.BackupsInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCrossBackupsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCrossBackupsResp(%+v)", *p)

}

func (p *DescribeCrossBackupsResp) DeepEqual(ano *DescribeCrossBackupsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupsInfo) {
		return false
	}
	return true
}

func (p *DescribeCrossBackupsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupsResp) Field2DeepEqual(src []*BackupInfo) bool {

	if len(p.BackupsInfo) != len(src) {
		return false
	}
	for i, v := range p.BackupsInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeBackupDetailReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewDescribeBackupDetailReq() *DescribeBackupDetailReq {
	return &DescribeBackupDetailReq{}
}

func (p *DescribeBackupDetailReq) InitDefault() {
}

func (p *DescribeBackupDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeBackupDetailReq) GetBackupId() (v string) {
	return p.BackupId
}
func (p *DescribeBackupDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeBackupDetailReq) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_DescribeBackupDetailReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *DescribeBackupDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupDetailReq[fieldId]))
}

func (p *DescribeBackupDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBackupDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *DescribeBackupDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBackupDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupDetailReq(%+v)", *p)

}

func (p *DescribeBackupDetailReq) DeepEqual(ano *DescribeBackupDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *DescribeBackupDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupDetailReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type DescribeBackupDetailResp struct {
	BackupsInfo *BackupInfo `thrift:"BackupsInfo,1,required" frugal:"1,required,BackupInfo" json:"BackupsInfo"`
}

func NewDescribeBackupDetailResp() *DescribeBackupDetailResp {
	return &DescribeBackupDetailResp{}
}

func (p *DescribeBackupDetailResp) InitDefault() {
}

var DescribeBackupDetailResp_BackupsInfo_DEFAULT *BackupInfo

func (p *DescribeBackupDetailResp) GetBackupsInfo() (v *BackupInfo) {
	if !p.IsSetBackupsInfo() {
		return DescribeBackupDetailResp_BackupsInfo_DEFAULT
	}
	return p.BackupsInfo
}
func (p *DescribeBackupDetailResp) SetBackupsInfo(val *BackupInfo) {
	p.BackupsInfo = val
}

var fieldIDToName_DescribeBackupDetailResp = map[int16]string{
	1: "BackupsInfo",
}

func (p *DescribeBackupDetailResp) IsSetBackupsInfo() bool {
	return p.BackupsInfo != nil
}

func (p *DescribeBackupDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBackupsInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupsInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBackupsInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupDetailResp[fieldId]))
}

func (p *DescribeBackupDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBackupInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BackupsInfo = _field
	return nil
}

func (p *DescribeBackupDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupsInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BackupsInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupDetailResp(%+v)", *p)

}

func (p *DescribeBackupDetailResp) DeepEqual(ano *DescribeBackupDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BackupsInfo) {
		return false
	}
	return true
}

func (p *DescribeBackupDetailResp) Field1DeepEqual(src *BackupInfo) bool {

	if !p.BackupsInfo.DeepEqual(src) {
		return false
	}
	return true
}

type RestoreToCrossRegionInstanceReq struct {
	DstRegionId          string             `thrift:"DstRegionId,1,required" frugal:"1,required,string" json:"DstRegionId"`
	SrcRegionId          string             `thrift:"SrcRegionId,2,required" frugal:"2,required,string" json:"SrcRegionId"`
	SrcRegionInstanceId  string             `thrift:"SrcRegionInstanceId,3,required" frugal:"3,required,string" json:"SrcRegionInstanceId"`
	BackupId             *string            `thrift:"BackupId,4,optional" frugal:"4,optional,string" json:"BackupId,omitempty"`
	RestoreTime          *string            `thrift:"RestoreTime,5,optional" frugal:"5,optional,string" json:"RestoreTime,omitempty"`
	NodeSpec             string             `thrift:"NodeSpec,6,required" frugal:"6,required,string" json:"NodeSpec"`
	StorageType          StorageType        `thrift:"StorageType,7,required" frugal:"7,required,StorageType" json:"StorageType"`
	StorageSpace         *int32             `thrift:"StorageSpace,8,optional" frugal:"8,optional,i32" json:"StorageSpace,omitempty"`
	VpcId                string             `thrift:"VpcId,9,required" frugal:"9,required,string" json:"VpcId"`
	SubnetId             string             `thrift:"SubnetId,10,required" frugal:"10,required,string" json:"SubnetId"`
	InstanceName         *string            `thrift:"InstanceName,11,optional" frugal:"11,optional,string" json:"InstanceName,omitempty"`
	SuperAccountPassword string             `thrift:"SuperAccountPassword,12,required" frugal:"12,required,string" json:"SuperAccountPassword"`
	ProjectName          *string            `thrift:"ProjectName,13,optional" frugal:"13,optional,string" json:"ProjectName,omitempty"`
	ChargeInfo           *ChargeInfoObject  `thrift:"ChargeInfo,14,required" frugal:"14,required,ChargeInfoObject" json:"ChargeInfo"`
	Databases            []*DatabasesObject `thrift:"Databases,15,optional" frugal:"15,optional,list<DatabasesObject>" json:"Databases,omitempty"`
	Tags                 []*TagObject       `thrift:"Tags,16,optional" frugal:"16,optional,list<TagObject>" json:"Tags,omitempty"`
	DBEngineVersion      *DBEngineVersion   `thrift:"DBEngineVersion,17,optional" frugal:"17,optional,DBEngineVersion" json:"DBEngineVersion,omitempty"`
	InstanceType         *InstanceType      `thrift:"InstanceType,18,optional" frugal:"18,optional,InstanceType" json:"InstanceType,omitempty"`
	ServerCollation      *string            `thrift:"ServerCollation,19,optional" frugal:"19,optional,string" json:"ServerCollation,omitempty"`
	DBTimeZone           *string            `thrift:"DBTimeZone,20,optional" frugal:"20,optional,string" json:"DBTimeZone,omitempty"`
	ZoneId               string             `thrift:"ZoneId,21,required" frugal:"21,required,string" json:"ZoneId"`
	AllowListIds         []string           `thrift:"AllowListIds,22,optional" frugal:"22,optional,list<string>" json:"AllowListIds,omitempty"`
}

func NewRestoreToCrossRegionInstanceReq() *RestoreToCrossRegionInstanceReq {
	return &RestoreToCrossRegionInstanceReq{}
}

func (p *RestoreToCrossRegionInstanceReq) InitDefault() {
}

func (p *RestoreToCrossRegionInstanceReq) GetDstRegionId() (v string) {
	return p.DstRegionId
}

func (p *RestoreToCrossRegionInstanceReq) GetSrcRegionId() (v string) {
	return p.SrcRegionId
}

func (p *RestoreToCrossRegionInstanceReq) GetSrcRegionInstanceId() (v string) {
	return p.SrcRegionInstanceId
}

var RestoreToCrossRegionInstanceReq_BackupId_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return RestoreToCrossRegionInstanceReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var RestoreToCrossRegionInstanceReq_RestoreTime_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetRestoreTime() (v string) {
	if !p.IsSetRestoreTime() {
		return RestoreToCrossRegionInstanceReq_RestoreTime_DEFAULT
	}
	return *p.RestoreTime
}

func (p *RestoreToCrossRegionInstanceReq) GetNodeSpec() (v string) {
	return p.NodeSpec
}

func (p *RestoreToCrossRegionInstanceReq) GetStorageType() (v StorageType) {
	return p.StorageType
}

var RestoreToCrossRegionInstanceReq_StorageSpace_DEFAULT int32

func (p *RestoreToCrossRegionInstanceReq) GetStorageSpace() (v int32) {
	if !p.IsSetStorageSpace() {
		return RestoreToCrossRegionInstanceReq_StorageSpace_DEFAULT
	}
	return *p.StorageSpace
}

func (p *RestoreToCrossRegionInstanceReq) GetVpcId() (v string) {
	return p.VpcId
}

func (p *RestoreToCrossRegionInstanceReq) GetSubnetId() (v string) {
	return p.SubnetId
}

var RestoreToCrossRegionInstanceReq_InstanceName_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return RestoreToCrossRegionInstanceReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

func (p *RestoreToCrossRegionInstanceReq) GetSuperAccountPassword() (v string) {
	return p.SuperAccountPassword
}

var RestoreToCrossRegionInstanceReq_ProjectName_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return RestoreToCrossRegionInstanceReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var RestoreToCrossRegionInstanceReq_ChargeInfo_DEFAULT *ChargeInfoObject

func (p *RestoreToCrossRegionInstanceReq) GetChargeInfo() (v *ChargeInfoObject) {
	if !p.IsSetChargeInfo() {
		return RestoreToCrossRegionInstanceReq_ChargeInfo_DEFAULT
	}
	return p.ChargeInfo
}

var RestoreToCrossRegionInstanceReq_Databases_DEFAULT []*DatabasesObject

func (p *RestoreToCrossRegionInstanceReq) GetDatabases() (v []*DatabasesObject) {
	if !p.IsSetDatabases() {
		return RestoreToCrossRegionInstanceReq_Databases_DEFAULT
	}
	return p.Databases
}

var RestoreToCrossRegionInstanceReq_Tags_DEFAULT []*TagObject

func (p *RestoreToCrossRegionInstanceReq) GetTags() (v []*TagObject) {
	if !p.IsSetTags() {
		return RestoreToCrossRegionInstanceReq_Tags_DEFAULT
	}
	return p.Tags
}

var RestoreToCrossRegionInstanceReq_DBEngineVersion_DEFAULT DBEngineVersion

func (p *RestoreToCrossRegionInstanceReq) GetDBEngineVersion() (v DBEngineVersion) {
	if !p.IsSetDBEngineVersion() {
		return RestoreToCrossRegionInstanceReq_DBEngineVersion_DEFAULT
	}
	return *p.DBEngineVersion
}

var RestoreToCrossRegionInstanceReq_InstanceType_DEFAULT InstanceType

func (p *RestoreToCrossRegionInstanceReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return RestoreToCrossRegionInstanceReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var RestoreToCrossRegionInstanceReq_ServerCollation_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetServerCollation() (v string) {
	if !p.IsSetServerCollation() {
		return RestoreToCrossRegionInstanceReq_ServerCollation_DEFAULT
	}
	return *p.ServerCollation
}

var RestoreToCrossRegionInstanceReq_DBTimeZone_DEFAULT string

func (p *RestoreToCrossRegionInstanceReq) GetDBTimeZone() (v string) {
	if !p.IsSetDBTimeZone() {
		return RestoreToCrossRegionInstanceReq_DBTimeZone_DEFAULT
	}
	return *p.DBTimeZone
}

func (p *RestoreToCrossRegionInstanceReq) GetZoneId() (v string) {
	return p.ZoneId
}

var RestoreToCrossRegionInstanceReq_AllowListIds_DEFAULT []string

func (p *RestoreToCrossRegionInstanceReq) GetAllowListIds() (v []string) {
	if !p.IsSetAllowListIds() {
		return RestoreToCrossRegionInstanceReq_AllowListIds_DEFAULT
	}
	return p.AllowListIds
}
func (p *RestoreToCrossRegionInstanceReq) SetDstRegionId(val string) {
	p.DstRegionId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetSrcRegionId(val string) {
	p.SrcRegionId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetSrcRegionInstanceId(val string) {
	p.SrcRegionInstanceId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetRestoreTime(val *string) {
	p.RestoreTime = val
}
func (p *RestoreToCrossRegionInstanceReq) SetNodeSpec(val string) {
	p.NodeSpec = val
}
func (p *RestoreToCrossRegionInstanceReq) SetStorageType(val StorageType) {
	p.StorageType = val
}
func (p *RestoreToCrossRegionInstanceReq) SetStorageSpace(val *int32) {
	p.StorageSpace = val
}
func (p *RestoreToCrossRegionInstanceReq) SetVpcId(val string) {
	p.VpcId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetSubnetId(val string) {
	p.SubnetId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *RestoreToCrossRegionInstanceReq) SetSuperAccountPassword(val string) {
	p.SuperAccountPassword = val
}
func (p *RestoreToCrossRegionInstanceReq) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *RestoreToCrossRegionInstanceReq) SetChargeInfo(val *ChargeInfoObject) {
	p.ChargeInfo = val
}
func (p *RestoreToCrossRegionInstanceReq) SetDatabases(val []*DatabasesObject) {
	p.Databases = val
}
func (p *RestoreToCrossRegionInstanceReq) SetTags(val []*TagObject) {
	p.Tags = val
}
func (p *RestoreToCrossRegionInstanceReq) SetDBEngineVersion(val *DBEngineVersion) {
	p.DBEngineVersion = val
}
func (p *RestoreToCrossRegionInstanceReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *RestoreToCrossRegionInstanceReq) SetServerCollation(val *string) {
	p.ServerCollation = val
}
func (p *RestoreToCrossRegionInstanceReq) SetDBTimeZone(val *string) {
	p.DBTimeZone = val
}
func (p *RestoreToCrossRegionInstanceReq) SetZoneId(val string) {
	p.ZoneId = val
}
func (p *RestoreToCrossRegionInstanceReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_RestoreToCrossRegionInstanceReq = map[int16]string{
	1:  "DstRegionId",
	2:  "SrcRegionId",
	3:  "SrcRegionInstanceId",
	4:  "BackupId",
	5:  "RestoreTime",
	6:  "NodeSpec",
	7:  "StorageType",
	8:  "StorageSpace",
	9:  "VpcId",
	10: "SubnetId",
	11: "InstanceName",
	12: "SuperAccountPassword",
	13: "ProjectName",
	14: "ChargeInfo",
	15: "Databases",
	16: "Tags",
	17: "DBEngineVersion",
	18: "InstanceType",
	19: "ServerCollation",
	20: "DBTimeZone",
	21: "ZoneId",
	22: "AllowListIds",
}

func (p *RestoreToCrossRegionInstanceReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetRestoreTime() bool {
	return p.RestoreTime != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetStorageSpace() bool {
	return p.StorageSpace != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetChargeInfo() bool {
	return p.ChargeInfo != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetDatabases() bool {
	return p.Databases != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetTags() bool {
	return p.Tags != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetDBEngineVersion() bool {
	return p.DBEngineVersion != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetServerCollation() bool {
	return p.ServerCollation != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetDBTimeZone() bool {
	return p.DBTimeZone != nil
}

func (p *RestoreToCrossRegionInstanceReq) IsSetAllowListIds() bool {
	return p.AllowListIds != nil
}

func (p *RestoreToCrossRegionInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToCrossRegionInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDstRegionId bool = false
	var issetSrcRegionId bool = false
	var issetSrcRegionInstanceId bool = false
	var issetNodeSpec bool = false
	var issetStorageType bool = false
	var issetVpcId bool = false
	var issetSubnetId bool = false
	var issetSuperAccountPassword bool = false
	var issetChargeInfo bool = false
	var issetZoneId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDstRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrcRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrcRegionInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeSpec = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetVpcId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetSubnetId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuperAccountPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetChargeInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
				issetZoneId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDstRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSrcRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSrcRegionInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNodeSpec {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStorageType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetVpcId {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetSubnetId {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetSuperAccountPassword {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetChargeInfo {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetZoneId {
		fieldId = 21
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToCrossRegionInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToCrossRegionInstanceReq[fieldId]))
}

func (p *RestoreToCrossRegionInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DstRegionId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcRegionId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrcRegionInstanceId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeSpec = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field StorageType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StorageType(v)
	}
	p.StorageType = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StorageSpace = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VpcId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubnetId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SuperAccountPassword = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField14(iprot thrift.TProtocol) error {
	_field := NewChargeInfoObject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ChargeInfo = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Databases = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TagObject, 0, size)
	values := make([]TagObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField18(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ServerCollation = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBTimeZone = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ZoneId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceReq) ReadField22(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *RestoreToCrossRegionInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToCrossRegionInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToCrossRegionInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DstRegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DstRegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcRegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcRegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrcRegionInstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrcRegionInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTime() {
		if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RestoreTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeSpec", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeSpec); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageType", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.StorageType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStorageSpace() {
		if err = oprot.WriteFieldBegin("StorageSpace", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StorageSpace); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VpcId", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VpcId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SubnetId", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubnetId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SuperAccountPassword", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SuperAccountPassword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChargeInfo", thrift.STRUCT, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ChargeInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabases() {
		if err = oprot.WriteFieldBegin("Databases", thrift.LIST, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Databases)); err != nil {
			return err
		}
		for _, v := range p.Databases {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineVersion() {
		if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DBEngineVersion)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetServerCollation() {
		if err = oprot.WriteFieldBegin("ServerCollation", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ServerCollation); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBTimeZone() {
		if err = oprot.WriteFieldBegin("DBTimeZone", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBTimeZone); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ZoneId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListIds() {
		if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
			return err
		}
		for _, v := range p.AllowListIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToCrossRegionInstanceReq(%+v)", *p)

}

func (p *RestoreToCrossRegionInstanceReq) DeepEqual(ano *RestoreToCrossRegionInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DstRegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SrcRegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SrcRegionInstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field5DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeSpec) {
		return false
	}
	if !p.Field7DeepEqual(ano.StorageType) {
		return false
	}
	if !p.Field8DeepEqual(ano.StorageSpace) {
		return false
	}
	if !p.Field9DeepEqual(ano.VpcId) {
		return false
	}
	if !p.Field10DeepEqual(ano.SubnetId) {
		return false
	}
	if !p.Field11DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field12DeepEqual(ano.SuperAccountPassword) {
		return false
	}
	if !p.Field13DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field14DeepEqual(ano.ChargeInfo) {
		return false
	}
	if !p.Field15DeepEqual(ano.Databases) {
		return false
	}
	if !p.Field16DeepEqual(ano.Tags) {
		return false
	}
	if !p.Field17DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field18DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field19DeepEqual(ano.ServerCollation) {
		return false
	}
	if !p.Field20DeepEqual(ano.DBTimeZone) {
		return false
	}
	if !p.Field21DeepEqual(ano.ZoneId) {
		return false
	}
	if !p.Field22DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *RestoreToCrossRegionInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DstRegionId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SrcRegionId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SrcRegionInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field4DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field5DeepEqual(src *string) bool {

	if p.RestoreTime == src {
		return true
	} else if p.RestoreTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RestoreTime, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.NodeSpec, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field7DeepEqual(src StorageType) bool {

	if p.StorageType != src {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field8DeepEqual(src *int32) bool {

	if p.StorageSpace == src {
		return true
	} else if p.StorageSpace == nil || src == nil {
		return false
	}
	if *p.StorageSpace != *src {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field9DeepEqual(src string) bool {

	if strings.Compare(p.VpcId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field10DeepEqual(src string) bool {

	if strings.Compare(p.SubnetId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field11DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field12DeepEqual(src string) bool {

	if strings.Compare(p.SuperAccountPassword, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field13DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field14DeepEqual(src *ChargeInfoObject) bool {

	if !p.ChargeInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field15DeepEqual(src []*DatabasesObject) bool {

	if len(p.Databases) != len(src) {
		return false
	}
	for i, v := range p.Databases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field16DeepEqual(src []*TagObject) bool {

	if len(p.Tags) != len(src) {
		return false
	}
	for i, v := range p.Tags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field17DeepEqual(src *DBEngineVersion) bool {

	if p.DBEngineVersion == src {
		return true
	} else if p.DBEngineVersion == nil || src == nil {
		return false
	}
	if *p.DBEngineVersion != *src {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field18DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field19DeepEqual(src *string) bool {

	if p.ServerCollation == src {
		return true
	} else if p.ServerCollation == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ServerCollation, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field20DeepEqual(src *string) bool {

	if p.DBTimeZone == src {
		return true
	} else if p.DBTimeZone == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBTimeZone, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field21DeepEqual(src string) bool {

	if strings.Compare(p.ZoneId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceReq) Field22DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type RestoreToCrossRegionInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	OrderId    string `thrift:"OrderId,2,required" frugal:"2,required,string" json:"OrderId"`
}

func NewRestoreToCrossRegionInstanceResp() *RestoreToCrossRegionInstanceResp {
	return &RestoreToCrossRegionInstanceResp{}
}

func (p *RestoreToCrossRegionInstanceResp) InitDefault() {
}

func (p *RestoreToCrossRegionInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *RestoreToCrossRegionInstanceResp) GetOrderId() (v string) {
	return p.OrderId
}
func (p *RestoreToCrossRegionInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *RestoreToCrossRegionInstanceResp) SetOrderId(val string) {
	p.OrderId = val
}

var fieldIDToName_RestoreToCrossRegionInstanceResp = map[int16]string{
	1: "InstanceId",
	2: "OrderId",
}

func (p *RestoreToCrossRegionInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToCrossRegionInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetOrderId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToCrossRegionInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToCrossRegionInstanceResp[fieldId]))
}

func (p *RestoreToCrossRegionInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestoreToCrossRegionInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}

func (p *RestoreToCrossRegionInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToCrossRegionInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToCrossRegionInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreToCrossRegionInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToCrossRegionInstanceResp(%+v)", *p)

}

func (p *RestoreToCrossRegionInstanceResp) DeepEqual(ano *RestoreToCrossRegionInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.OrderId) {
		return false
	}
	return true
}

func (p *RestoreToCrossRegionInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToCrossRegionInstanceResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}

type DescribeDeletedBackupsReq struct {
	InstanceId   *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceName *string `thrift:"InstanceName,2,optional" frugal:"2,optional,string" json:"InstanceName,omitempty"`
	PageNumber   *int32  `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32  `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	ProjectName  *string `thrift:"ProjectName,5,optional" frugal:"5,optional,string" json:"ProjectName,omitempty"`
}

func NewDescribeDeletedBackupsReq() *DescribeDeletedBackupsReq {
	return &DescribeDeletedBackupsReq{}
}

func (p *DescribeDeletedBackupsReq) InitDefault() {
}

var DescribeDeletedBackupsReq_InstanceId_DEFAULT string

func (p *DescribeDeletedBackupsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeDeletedBackupsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeDeletedBackupsReq_InstanceName_DEFAULT string

func (p *DescribeDeletedBackupsReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return DescribeDeletedBackupsReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var DescribeDeletedBackupsReq_PageNumber_DEFAULT int32

func (p *DescribeDeletedBackupsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDeletedBackupsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDeletedBackupsReq_PageSize_DEFAULT int32

func (p *DescribeDeletedBackupsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDeletedBackupsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDeletedBackupsReq_ProjectName_DEFAULT string

func (p *DescribeDeletedBackupsReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeDeletedBackupsReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *DescribeDeletedBackupsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeDeletedBackupsReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *DescribeDeletedBackupsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDeletedBackupsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDeletedBackupsReq) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_DescribeDeletedBackupsReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "PageNumber",
	4: "PageSize",
	5: "ProjectName",
}

func (p *DescribeDeletedBackupsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeDeletedBackupsReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *DescribeDeletedBackupsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDeletedBackupsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDeletedBackupsReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeDeletedBackupsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeletedBackupsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeletedBackupsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDeletedBackupsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *DescribeDeletedBackupsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDeletedBackupsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDeletedBackupsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *DescribeDeletedBackupsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeletedBackupsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeletedBackupsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDeletedBackupsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeletedBackupsReq(%+v)", *p)

}

func (p *DescribeDeletedBackupsReq) DeepEqual(ano *DescribeDeletedBackupsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *DescribeDeletedBackupsReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDeletedBackupsReq) Field2DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDeletedBackupsReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDeletedBackupsReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDeletedBackupsReq) Field5DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type DescribeDeletedBackupsResp struct {
	Total             int32                      `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	DeletedBackupInfo []*DeletedBackupInfoObject `thrift:"DeletedBackupInfo,2,required" frugal:"2,required,list<DeletedBackupInfoObject>" json:"DeletedBackupInfo"`
}

func NewDescribeDeletedBackupsResp() *DescribeDeletedBackupsResp {
	return &DescribeDeletedBackupsResp{}
}

func (p *DescribeDeletedBackupsResp) InitDefault() {
}

func (p *DescribeDeletedBackupsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeDeletedBackupsResp) GetDeletedBackupInfo() (v []*DeletedBackupInfoObject) {
	return p.DeletedBackupInfo
}
func (p *DescribeDeletedBackupsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeDeletedBackupsResp) SetDeletedBackupInfo(val []*DeletedBackupInfoObject) {
	p.DeletedBackupInfo = val
}

var fieldIDToName_DescribeDeletedBackupsResp = map[int16]string{
	1: "Total",
	2: "DeletedBackupInfo",
}

func (p *DescribeDeletedBackupsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeletedBackupsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDeletedBackupInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeletedBackupInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDeletedBackupInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeletedBackupsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDeletedBackupsResp[fieldId]))
}

func (p *DescribeDeletedBackupsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeDeletedBackupsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DeletedBackupInfoObject, 0, size)
	values := make([]DeletedBackupInfoObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeletedBackupInfo = _field
	return nil
}

func (p *DescribeDeletedBackupsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeletedBackupsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeletedBackupsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeletedBackupsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeletedBackupsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeletedBackupInfo", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DeletedBackupInfo)); err != nil {
		return err
	}
	for _, v := range p.DeletedBackupInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDeletedBackupsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeletedBackupsResp(%+v)", *p)

}

func (p *DescribeDeletedBackupsResp) DeepEqual(ano *DescribeDeletedBackupsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.DeletedBackupInfo) {
		return false
	}
	return true
}

func (p *DescribeDeletedBackupsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeDeletedBackupsResp) Field2DeepEqual(src []*DeletedBackupInfoObject) bool {

	if len(p.DeletedBackupInfo) != len(src) {
		return false
	}
	for i, v := range p.DeletedBackupInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeBackupPolicyReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeBackupPolicyReq() *DescribeBackupPolicyReq {
	return &DescribeBackupPolicyReq{}
}

func (p *DescribeBackupPolicyReq) InitDefault() {
}

func (p *DescribeBackupPolicyReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeBackupPolicyReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeBackupPolicyReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeBackupPolicyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupPolicyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupPolicyReq[fieldId]))
}

func (p *DescribeBackupPolicyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeBackupPolicyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupPolicyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupPolicyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupPolicyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupPolicyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupPolicyReq(%+v)", *p)

}

func (p *DescribeBackupPolicyReq) DeepEqual(ano *DescribeBackupPolicyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeBackupPolicyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeBackupPolicyResp struct {
	InstanceId            string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupTime            string `thrift:"BackupTime,2,required" frugal:"2,required,string" json:"BackupTime"`
	FullBackupPeriod      string `thrift:"FullBackupPeriod,3,required" frugal:"3,required,string" json:"FullBackupPeriod"`
	IncrementBackupPeriod string `thrift:"IncrementBackupPeriod,4,required" frugal:"4,required,string" json:"IncrementBackupPeriod"`
	BackupRetentionPeriod int32  `thrift:"BackupRetentionPeriod,5,required" frugal:"5,required,i32" json:"BackupRetentionPeriod"`
	LogBackupInterval     int32  `thrift:"LogBackupInterval,6,required" frugal:"6,required,i32" json:"LogBackupInterval"`
}

func NewDescribeBackupPolicyResp() *DescribeBackupPolicyResp {
	return &DescribeBackupPolicyResp{}
}

func (p *DescribeBackupPolicyResp) InitDefault() {
}

func (p *DescribeBackupPolicyResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeBackupPolicyResp) GetBackupTime() (v string) {
	return p.BackupTime
}

func (p *DescribeBackupPolicyResp) GetFullBackupPeriod() (v string) {
	return p.FullBackupPeriod
}

func (p *DescribeBackupPolicyResp) GetIncrementBackupPeriod() (v string) {
	return p.IncrementBackupPeriod
}

func (p *DescribeBackupPolicyResp) GetBackupRetentionPeriod() (v int32) {
	return p.BackupRetentionPeriod
}

func (p *DescribeBackupPolicyResp) GetLogBackupInterval() (v int32) {
	return p.LogBackupInterval
}
func (p *DescribeBackupPolicyResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeBackupPolicyResp) SetBackupTime(val string) {
	p.BackupTime = val
}
func (p *DescribeBackupPolicyResp) SetFullBackupPeriod(val string) {
	p.FullBackupPeriod = val
}
func (p *DescribeBackupPolicyResp) SetIncrementBackupPeriod(val string) {
	p.IncrementBackupPeriod = val
}
func (p *DescribeBackupPolicyResp) SetBackupRetentionPeriod(val int32) {
	p.BackupRetentionPeriod = val
}
func (p *DescribeBackupPolicyResp) SetLogBackupInterval(val int32) {
	p.LogBackupInterval = val
}

var fieldIDToName_DescribeBackupPolicyResp = map[int16]string{
	1: "InstanceId",
	2: "BackupTime",
	3: "FullBackupPeriod",
	4: "IncrementBackupPeriod",
	5: "BackupRetentionPeriod",
	6: "LogBackupInterval",
}

func (p *DescribeBackupPolicyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupPolicyResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupTime bool = false
	var issetFullBackupPeriod bool = false
	var issetIncrementBackupPeriod bool = false
	var issetBackupRetentionPeriod bool = false
	var issetLogBackupInterval bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetFullBackupPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetIncrementBackupPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupRetentionPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogBackupInterval = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFullBackupPeriod {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIncrementBackupPeriod {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupRetentionPeriod {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupInterval {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBackupPolicyResp[fieldId]))
}

func (p *DescribeBackupPolicyResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBackupPolicyResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupTime = _field
	return nil
}
func (p *DescribeBackupPolicyResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FullBackupPeriod = _field
	return nil
}
func (p *DescribeBackupPolicyResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IncrementBackupPeriod = _field
	return nil
}
func (p *DescribeBackupPolicyResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupRetentionPeriod = _field
	return nil
}
func (p *DescribeBackupPolicyResp) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogBackupInterval = _field
	return nil
}

func (p *DescribeBackupPolicyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBackupPolicyResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBackupPolicyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FullBackupPeriod", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FullBackupPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IncrementBackupPeriod", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IncrementBackupPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupRetentionPeriod", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BackupRetentionPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogBackupInterval", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.LogBackupInterval); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeBackupPolicyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBackupPolicyResp(%+v)", *p)

}

func (p *DescribeBackupPolicyResp) DeepEqual(ano *DescribeBackupPolicyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.FullBackupPeriod) {
		return false
	}
	if !p.Field4DeepEqual(ano.IncrementBackupPeriod) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupRetentionPeriod) {
		return false
	}
	if !p.Field6DeepEqual(ano.LogBackupInterval) {
		return false
	}
	return true
}

func (p *DescribeBackupPolicyResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupPolicyResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupPolicyResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.FullBackupPeriod, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupPolicyResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.IncrementBackupPeriod, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBackupPolicyResp) Field5DeepEqual(src int32) bool {

	if p.BackupRetentionPeriod != src {
		return false
	}
	return true
}
func (p *DescribeBackupPolicyResp) Field6DeepEqual(src int32) bool {

	if p.LogBackupInterval != src {
		return false
	}
	return true
}

type DescribeCrossBackupPolicyReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeCrossBackupPolicyReq() *DescribeCrossBackupPolicyReq {
	return &DescribeCrossBackupPolicyReq{}
}

func (p *DescribeCrossBackupPolicyReq) InitDefault() {
}

func (p *DescribeCrossBackupPolicyReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeCrossBackupPolicyReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeCrossBackupPolicyReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeCrossBackupPolicyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupPolicyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCrossBackupPolicyReq[fieldId]))
}

func (p *DescribeCrossBackupPolicyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeCrossBackupPolicyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupPolicyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCrossBackupPolicyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCrossBackupPolicyReq(%+v)", *p)

}

func (p *DescribeCrossBackupPolicyReq) DeepEqual(ano *DescribeCrossBackupPolicyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeCrossBackupPolicyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeCrossBackupPolicyResp struct {
	InstanceId        string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupEnabled     bool   `thrift:"BackupEnabled,2,required" frugal:"2,required,bool" json:"BackupEnabled"`
	LogBackupEnabled  bool   `thrift:"LogBackupEnabled,3,required" frugal:"3,required,bool" json:"LogBackupEnabled"`
	CrossBackupRegion string `thrift:"CrossBackupRegion,4,required" frugal:"4,required,string" json:"CrossBackupRegion"`
	Retention         int32  `thrift:"Retention,5,required" frugal:"5,required,i32" json:"Retention"`
}

func NewDescribeCrossBackupPolicyResp() *DescribeCrossBackupPolicyResp {
	return &DescribeCrossBackupPolicyResp{}
}

func (p *DescribeCrossBackupPolicyResp) InitDefault() {
}

func (p *DescribeCrossBackupPolicyResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeCrossBackupPolicyResp) GetBackupEnabled() (v bool) {
	return p.BackupEnabled
}

func (p *DescribeCrossBackupPolicyResp) GetLogBackupEnabled() (v bool) {
	return p.LogBackupEnabled
}

func (p *DescribeCrossBackupPolicyResp) GetCrossBackupRegion() (v string) {
	return p.CrossBackupRegion
}

func (p *DescribeCrossBackupPolicyResp) GetRetention() (v int32) {
	return p.Retention
}
func (p *DescribeCrossBackupPolicyResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeCrossBackupPolicyResp) SetBackupEnabled(val bool) {
	p.BackupEnabled = val
}
func (p *DescribeCrossBackupPolicyResp) SetLogBackupEnabled(val bool) {
	p.LogBackupEnabled = val
}
func (p *DescribeCrossBackupPolicyResp) SetCrossBackupRegion(val string) {
	p.CrossBackupRegion = val
}
func (p *DescribeCrossBackupPolicyResp) SetRetention(val int32) {
	p.Retention = val
}

var fieldIDToName_DescribeCrossBackupPolicyResp = map[int16]string{
	1: "InstanceId",
	2: "BackupEnabled",
	3: "LogBackupEnabled",
	4: "CrossBackupRegion",
	5: "Retention",
}

func (p *DescribeCrossBackupPolicyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupPolicyResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupEnabled bool = false
	var issetLogBackupEnabled bool = false
	var issetCrossBackupRegion bool = false
	var issetRetention bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupEnabled = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogBackupEnabled = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCrossBackupRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRetention = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupEnabled {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupEnabled {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCrossBackupRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRetention {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCrossBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCrossBackupPolicyResp[fieldId]))
}

func (p *DescribeCrossBackupPolicyResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeCrossBackupPolicyResp) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupEnabled = _field
	return nil
}
func (p *DescribeCrossBackupPolicyResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogBackupEnabled = _field
	return nil
}
func (p *DescribeCrossBackupPolicyResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CrossBackupRegion = _field
	return nil
}
func (p *DescribeCrossBackupPolicyResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Retention = _field
	return nil
}

func (p *DescribeCrossBackupPolicyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCrossBackupPolicyResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCrossBackupPolicyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupEnabled", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.BackupEnabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogBackupEnabled", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.LogBackupEnabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CrossBackupRegion", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CrossBackupRegion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Retention", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Retention); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeCrossBackupPolicyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCrossBackupPolicyResp(%+v)", *p)

}

func (p *DescribeCrossBackupPolicyResp) DeepEqual(ano *DescribeCrossBackupPolicyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupEnabled) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogBackupEnabled) {
		return false
	}
	if !p.Field4DeepEqual(ano.CrossBackupRegion) {
		return false
	}
	if !p.Field5DeepEqual(ano.Retention) {
		return false
	}
	return true
}

func (p *DescribeCrossBackupPolicyResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupPolicyResp) Field2DeepEqual(src bool) bool {

	if p.BackupEnabled != src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupPolicyResp) Field3DeepEqual(src bool) bool {

	if p.LogBackupEnabled != src {
		return false
	}
	return true
}
func (p *DescribeCrossBackupPolicyResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CrossBackupRegion, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCrossBackupPolicyResp) Field5DeepEqual(src int32) bool {

	if p.Retention != src {
		return false
	}
	return true
}

type DescribeAvailableCrossRegionReq struct {
	RegionId   string  `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId *string `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
}

func NewDescribeAvailableCrossRegionReq() *DescribeAvailableCrossRegionReq {
	return &DescribeAvailableCrossRegionReq{}
}

func (p *DescribeAvailableCrossRegionReq) InitDefault() {
}

func (p *DescribeAvailableCrossRegionReq) GetRegionId() (v string) {
	return p.RegionId
}

var DescribeAvailableCrossRegionReq_InstanceId_DEFAULT string

func (p *DescribeAvailableCrossRegionReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeAvailableCrossRegionReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *DescribeAvailableCrossRegionReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAvailableCrossRegionReq) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeAvailableCrossRegionReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
}

func (p *DescribeAvailableCrossRegionReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeAvailableCrossRegionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableCrossRegionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableCrossRegionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAvailableCrossRegionReq[fieldId]))
}

func (p *DescribeAvailableCrossRegionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAvailableCrossRegionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeAvailableCrossRegionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableCrossRegionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailableCrossRegionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailableCrossRegionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailableCrossRegionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAvailableCrossRegionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailableCrossRegionReq(%+v)", *p)

}

func (p *DescribeAvailableCrossRegionReq) DeepEqual(ano *DescribeAvailableCrossRegionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeAvailableCrossRegionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAvailableCrossRegionReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type DescribeAvailableCrossRegionResp struct {
	Regions []string `thrift:"Regions,1,required" frugal:"1,required,list<string>" json:"Regions"`
}

func NewDescribeAvailableCrossRegionResp() *DescribeAvailableCrossRegionResp {
	return &DescribeAvailableCrossRegionResp{}
}

func (p *DescribeAvailableCrossRegionResp) InitDefault() {
}

func (p *DescribeAvailableCrossRegionResp) GetRegions() (v []string) {
	return p.Regions
}
func (p *DescribeAvailableCrossRegionResp) SetRegions(val []string) {
	p.Regions = val
}

var fieldIDToName_DescribeAvailableCrossRegionResp = map[int16]string{
	1: "Regions",
}

func (p *DescribeAvailableCrossRegionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableCrossRegionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegions bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegions {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableCrossRegionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAvailableCrossRegionResp[fieldId]))
}

func (p *DescribeAvailableCrossRegionResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Regions = _field
	return nil
}

func (p *DescribeAvailableCrossRegionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableCrossRegionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailableCrossRegionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailableCrossRegionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Regions", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Regions)); err != nil {
		return err
	}
	for _, v := range p.Regions {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailableCrossRegionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailableCrossRegionResp(%+v)", *p)

}

func (p *DescribeAvailableCrossRegionResp) DeepEqual(ano *DescribeAvailableCrossRegionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Regions) {
		return false
	}
	return true
}

func (p *DescribeAvailableCrossRegionResp) Field1DeepEqual(src []string) bool {

	if len(p.Regions) != len(src) {
		return false
	}
	for i, v := range p.Regions {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DeleteBackupReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" validate:"required"`
}

func NewDeleteBackupReq() *DeleteBackupReq {
	return &DeleteBackupReq{}
}

func (p *DeleteBackupReq) InitDefault() {
}

func (p *DeleteBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteBackupReq) GetBackupId() (v string) {
	return p.BackupId
}
func (p *DeleteBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteBackupReq) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_DeleteBackupReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *DeleteBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteBackupReq[fieldId]))
}

func (p *DeleteBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteBackupReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *DeleteBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteBackupReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteBackupReq(%+v)", *p)

}

func (p *DeleteBackupReq) DeepEqual(ano *DeleteBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *DeleteBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteBackupReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type DeleteBackupResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewDeleteBackupResp() *DeleteBackupResp {
	return &DeleteBackupResp{}
}

func (p *DeleteBackupResp) InitDefault() {
}

func (p *DeleteBackupResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteBackupResp) GetBackupId() (v string) {
	return p.BackupId
}
func (p *DeleteBackupResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteBackupResp) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_DeleteBackupResp = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *DeleteBackupResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBackupResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBackupResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteBackupResp[fieldId]))
}

func (p *DeleteBackupResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteBackupResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *DeleteBackupResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBackupResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBackupResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteBackupResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteBackupResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteBackupResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteBackupResp(%+v)", *p)

}

func (p *DeleteBackupResp) DeepEqual(ano *DeleteBackupResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *DeleteBackupResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteBackupResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type ModifyBackupPolicyReq struct {
	InstanceId            string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupTime            string `thrift:"BackupTime,2,required" frugal:"2,required,string" json:"BackupTime"`
	FullBackupPeriod      string `thrift:"FullBackupPeriod,3,required" frugal:"3,required,string" json:"FullBackupPeriod"`
	IncrementBackupPeriod string `thrift:"IncrementBackupPeriod,4,required" frugal:"4,required,string" json:"IncrementBackupPeriod"`
	BackupRetentionPeriod int32  `thrift:"BackupRetentionPeriod,5,required" frugal:"5,required,i32" json:"BackupRetentionPeriod"`
	LogBackupInterval     *int32 `thrift:"LogBackupInterval,6,optional" frugal:"6,optional,i32" json:"LogBackupInterval,omitempty"`
}

func NewModifyBackupPolicyReq() *ModifyBackupPolicyReq {
	return &ModifyBackupPolicyReq{}
}

func (p *ModifyBackupPolicyReq) InitDefault() {
}

func (p *ModifyBackupPolicyReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyBackupPolicyReq) GetBackupTime() (v string) {
	return p.BackupTime
}

func (p *ModifyBackupPolicyReq) GetFullBackupPeriod() (v string) {
	return p.FullBackupPeriod
}

func (p *ModifyBackupPolicyReq) GetIncrementBackupPeriod() (v string) {
	return p.IncrementBackupPeriod
}

func (p *ModifyBackupPolicyReq) GetBackupRetentionPeriod() (v int32) {
	return p.BackupRetentionPeriod
}

var ModifyBackupPolicyReq_LogBackupInterval_DEFAULT int32

func (p *ModifyBackupPolicyReq) GetLogBackupInterval() (v int32) {
	if !p.IsSetLogBackupInterval() {
		return ModifyBackupPolicyReq_LogBackupInterval_DEFAULT
	}
	return *p.LogBackupInterval
}
func (p *ModifyBackupPolicyReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyBackupPolicyReq) SetBackupTime(val string) {
	p.BackupTime = val
}
func (p *ModifyBackupPolicyReq) SetFullBackupPeriod(val string) {
	p.FullBackupPeriod = val
}
func (p *ModifyBackupPolicyReq) SetIncrementBackupPeriod(val string) {
	p.IncrementBackupPeriod = val
}
func (p *ModifyBackupPolicyReq) SetBackupRetentionPeriod(val int32) {
	p.BackupRetentionPeriod = val
}
func (p *ModifyBackupPolicyReq) SetLogBackupInterval(val *int32) {
	p.LogBackupInterval = val
}

var fieldIDToName_ModifyBackupPolicyReq = map[int16]string{
	1: "InstanceId",
	2: "BackupTime",
	3: "FullBackupPeriod",
	4: "IncrementBackupPeriod",
	5: "BackupRetentionPeriod",
	6: "LogBackupInterval",
}

func (p *ModifyBackupPolicyReq) IsSetLogBackupInterval() bool {
	return p.LogBackupInterval != nil
}

func (p *ModifyBackupPolicyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupPolicyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupTime bool = false
	var issetFullBackupPeriod bool = false
	var issetIncrementBackupPeriod bool = false
	var issetBackupRetentionPeriod bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetFullBackupPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetIncrementBackupPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupRetentionPeriod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFullBackupPeriod {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIncrementBackupPeriod {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupRetentionPeriod {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyBackupPolicyReq[fieldId]))
}

func (p *ModifyBackupPolicyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyBackupPolicyReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupTime = _field
	return nil
}
func (p *ModifyBackupPolicyReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FullBackupPeriod = _field
	return nil
}
func (p *ModifyBackupPolicyReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IncrementBackupPeriod = _field
	return nil
}
func (p *ModifyBackupPolicyReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupRetentionPeriod = _field
	return nil
}
func (p *ModifyBackupPolicyReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LogBackupInterval = _field
	return nil
}

func (p *ModifyBackupPolicyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupPolicyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyBackupPolicyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FullBackupPeriod", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FullBackupPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IncrementBackupPeriod", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IncrementBackupPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupRetentionPeriod", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BackupRetentionPeriod); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogBackupInterval() {
		if err = oprot.WriteFieldBegin("LogBackupInterval", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.LogBackupInterval); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyBackupPolicyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyBackupPolicyReq(%+v)", *p)

}

func (p *ModifyBackupPolicyReq) DeepEqual(ano *ModifyBackupPolicyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.FullBackupPeriod) {
		return false
	}
	if !p.Field4DeepEqual(ano.IncrementBackupPeriod) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupRetentionPeriod) {
		return false
	}
	if !p.Field6DeepEqual(ano.LogBackupInterval) {
		return false
	}
	return true
}

func (p *ModifyBackupPolicyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupPolicyReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupTime, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupPolicyReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.FullBackupPeriod, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupPolicyReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.IncrementBackupPeriod, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyBackupPolicyReq) Field5DeepEqual(src int32) bool {

	if p.BackupRetentionPeriod != src {
		return false
	}
	return true
}
func (p *ModifyBackupPolicyReq) Field6DeepEqual(src *int32) bool {

	if p.LogBackupInterval == src {
		return true
	} else if p.LogBackupInterval == nil || src == nil {
		return false
	}
	if *p.LogBackupInterval != *src {
		return false
	}
	return true
}

type ModifyBackupPolicyResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
}

func NewModifyBackupPolicyResp() *ModifyBackupPolicyResp {
	return &ModifyBackupPolicyResp{}
}

func (p *ModifyBackupPolicyResp) InitDefault() {
}

func (p *ModifyBackupPolicyResp) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ModifyBackupPolicyResp) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ModifyBackupPolicyResp = map[int16]string{
	1: "InstanceId",
}

func (p *ModifyBackupPolicyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupPolicyResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyBackupPolicyResp[fieldId]))
}

func (p *ModifyBackupPolicyResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ModifyBackupPolicyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyBackupPolicyResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyBackupPolicyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyBackupPolicyResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyBackupPolicyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyBackupPolicyResp(%+v)", *p)

}

func (p *ModifyBackupPolicyResp) DeepEqual(ano *ModifyBackupPolicyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ModifyBackupPolicyResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type ModifyCrossBackupPolicyReq struct {
	InstanceId        string  `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupEnabled     *bool   `thrift:"BackupEnabled,2,optional" frugal:"2,optional,bool" json:"BackupEnabled,omitempty"`
	LogBackupEnabled  *bool   `thrift:"LogBackupEnabled,3,optional" frugal:"3,optional,bool" json:"LogBackupEnabled,omitempty"`
	CrossBackupRegion *string `thrift:"CrossBackupRegion,4,optional" frugal:"4,optional,string" json:"CrossBackupRegion,omitempty"`
	Retention         *int32  `thrift:"Retention,5,optional" frugal:"5,optional,i32" json:"Retention,omitempty"`
}

func NewModifyCrossBackupPolicyReq() *ModifyCrossBackupPolicyReq {
	return &ModifyCrossBackupPolicyReq{}
}

func (p *ModifyCrossBackupPolicyReq) InitDefault() {
}

func (p *ModifyCrossBackupPolicyReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyCrossBackupPolicyReq_BackupEnabled_DEFAULT bool

func (p *ModifyCrossBackupPolicyReq) GetBackupEnabled() (v bool) {
	if !p.IsSetBackupEnabled() {
		return ModifyCrossBackupPolicyReq_BackupEnabled_DEFAULT
	}
	return *p.BackupEnabled
}

var ModifyCrossBackupPolicyReq_LogBackupEnabled_DEFAULT bool

func (p *ModifyCrossBackupPolicyReq) GetLogBackupEnabled() (v bool) {
	if !p.IsSetLogBackupEnabled() {
		return ModifyCrossBackupPolicyReq_LogBackupEnabled_DEFAULT
	}
	return *p.LogBackupEnabled
}

var ModifyCrossBackupPolicyReq_CrossBackupRegion_DEFAULT string

func (p *ModifyCrossBackupPolicyReq) GetCrossBackupRegion() (v string) {
	if !p.IsSetCrossBackupRegion() {
		return ModifyCrossBackupPolicyReq_CrossBackupRegion_DEFAULT
	}
	return *p.CrossBackupRegion
}

var ModifyCrossBackupPolicyReq_Retention_DEFAULT int32

func (p *ModifyCrossBackupPolicyReq) GetRetention() (v int32) {
	if !p.IsSetRetention() {
		return ModifyCrossBackupPolicyReq_Retention_DEFAULT
	}
	return *p.Retention
}
func (p *ModifyCrossBackupPolicyReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyCrossBackupPolicyReq) SetBackupEnabled(val *bool) {
	p.BackupEnabled = val
}
func (p *ModifyCrossBackupPolicyReq) SetLogBackupEnabled(val *bool) {
	p.LogBackupEnabled = val
}
func (p *ModifyCrossBackupPolicyReq) SetCrossBackupRegion(val *string) {
	p.CrossBackupRegion = val
}
func (p *ModifyCrossBackupPolicyReq) SetRetention(val *int32) {
	p.Retention = val
}

var fieldIDToName_ModifyCrossBackupPolicyReq = map[int16]string{
	1: "InstanceId",
	2: "BackupEnabled",
	3: "LogBackupEnabled",
	4: "CrossBackupRegion",
	5: "Retention",
}

func (p *ModifyCrossBackupPolicyReq) IsSetBackupEnabled() bool {
	return p.BackupEnabled != nil
}

func (p *ModifyCrossBackupPolicyReq) IsSetLogBackupEnabled() bool {
	return p.LogBackupEnabled != nil
}

func (p *ModifyCrossBackupPolicyReq) IsSetCrossBackupRegion() bool {
	return p.CrossBackupRegion != nil
}

func (p *ModifyCrossBackupPolicyReq) IsSetRetention() bool {
	return p.Retention != nil
}

func (p *ModifyCrossBackupPolicyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCrossBackupPolicyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyCrossBackupPolicyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyCrossBackupPolicyReq[fieldId]))
}

func (p *ModifyCrossBackupPolicyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyCrossBackupPolicyReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupEnabled = _field
	return nil
}
func (p *ModifyCrossBackupPolicyReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LogBackupEnabled = _field
	return nil
}
func (p *ModifyCrossBackupPolicyReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CrossBackupRegion = _field
	return nil
}
func (p *ModifyCrossBackupPolicyReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Retention = _field
	return nil
}

func (p *ModifyCrossBackupPolicyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCrossBackupPolicyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyCrossBackupPolicyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupEnabled() {
		if err = oprot.WriteFieldBegin("BackupEnabled", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.BackupEnabled); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogBackupEnabled() {
		if err = oprot.WriteFieldBegin("LogBackupEnabled", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.LogBackupEnabled); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCrossBackupRegion() {
		if err = oprot.WriteFieldBegin("CrossBackupRegion", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CrossBackupRegion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRetention() {
		if err = oprot.WriteFieldBegin("Retention", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Retention); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyCrossBackupPolicyReq(%+v)", *p)

}

func (p *ModifyCrossBackupPolicyReq) DeepEqual(ano *ModifyCrossBackupPolicyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupEnabled) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogBackupEnabled) {
		return false
	}
	if !p.Field4DeepEqual(ano.CrossBackupRegion) {
		return false
	}
	if !p.Field5DeepEqual(ano.Retention) {
		return false
	}
	return true
}

func (p *ModifyCrossBackupPolicyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyReq) Field2DeepEqual(src *bool) bool {

	if p.BackupEnabled == src {
		return true
	} else if p.BackupEnabled == nil || src == nil {
		return false
	}
	if *p.BackupEnabled != *src {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyReq) Field3DeepEqual(src *bool) bool {

	if p.LogBackupEnabled == src {
		return true
	} else if p.LogBackupEnabled == nil || src == nil {
		return false
	}
	if *p.LogBackupEnabled != *src {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyReq) Field4DeepEqual(src *string) bool {

	if p.CrossBackupRegion == src {
		return true
	} else if p.CrossBackupRegion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CrossBackupRegion, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyReq) Field5DeepEqual(src *int32) bool {

	if p.Retention == src {
		return true
	} else if p.Retention == nil || src == nil {
		return false
	}
	if *p.Retention != *src {
		return false
	}
	return true
}

type ModifyCrossBackupPolicyResp struct {
	InstanceId        string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupEnabled     bool   `thrift:"BackupEnabled,2,required" frugal:"2,required,bool" json:"BackupEnabled"`
	LogBackupEnabled  bool   `thrift:"LogBackupEnabled,3,required" frugal:"3,required,bool" json:"LogBackupEnabled"`
	CrossBackupRegion string `thrift:"CrossBackupRegion,4,required" frugal:"4,required,string" json:"CrossBackupRegion"`
	Retention         int32  `thrift:"Retention,5,required" frugal:"5,required,i32" json:"Retention"`
}

func NewModifyCrossBackupPolicyResp() *ModifyCrossBackupPolicyResp {
	return &ModifyCrossBackupPolicyResp{}
}

func (p *ModifyCrossBackupPolicyResp) InitDefault() {
}

func (p *ModifyCrossBackupPolicyResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyCrossBackupPolicyResp) GetBackupEnabled() (v bool) {
	return p.BackupEnabled
}

func (p *ModifyCrossBackupPolicyResp) GetLogBackupEnabled() (v bool) {
	return p.LogBackupEnabled
}

func (p *ModifyCrossBackupPolicyResp) GetCrossBackupRegion() (v string) {
	return p.CrossBackupRegion
}

func (p *ModifyCrossBackupPolicyResp) GetRetention() (v int32) {
	return p.Retention
}
func (p *ModifyCrossBackupPolicyResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyCrossBackupPolicyResp) SetBackupEnabled(val bool) {
	p.BackupEnabled = val
}
func (p *ModifyCrossBackupPolicyResp) SetLogBackupEnabled(val bool) {
	p.LogBackupEnabled = val
}
func (p *ModifyCrossBackupPolicyResp) SetCrossBackupRegion(val string) {
	p.CrossBackupRegion = val
}
func (p *ModifyCrossBackupPolicyResp) SetRetention(val int32) {
	p.Retention = val
}

var fieldIDToName_ModifyCrossBackupPolicyResp = map[int16]string{
	1: "InstanceId",
	2: "BackupEnabled",
	3: "LogBackupEnabled",
	4: "CrossBackupRegion",
	5: "Retention",
}

func (p *ModifyCrossBackupPolicyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCrossBackupPolicyResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupEnabled bool = false
	var issetLogBackupEnabled bool = false
	var issetCrossBackupRegion bool = false
	var issetRetention bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupEnabled = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogBackupEnabled = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCrossBackupRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRetention = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupEnabled {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLogBackupEnabled {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCrossBackupRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRetention {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyCrossBackupPolicyResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyCrossBackupPolicyResp[fieldId]))
}

func (p *ModifyCrossBackupPolicyResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyCrossBackupPolicyResp) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupEnabled = _field
	return nil
}
func (p *ModifyCrossBackupPolicyResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogBackupEnabled = _field
	return nil
}
func (p *ModifyCrossBackupPolicyResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CrossBackupRegion = _field
	return nil
}
func (p *ModifyCrossBackupPolicyResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Retention = _field
	return nil
}

func (p *ModifyCrossBackupPolicyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyCrossBackupPolicyResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyCrossBackupPolicyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupEnabled", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.BackupEnabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogBackupEnabled", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.LogBackupEnabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CrossBackupRegion", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CrossBackupRegion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Retention", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Retention); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyCrossBackupPolicyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyCrossBackupPolicyResp(%+v)", *p)

}

func (p *ModifyCrossBackupPolicyResp) DeepEqual(ano *ModifyCrossBackupPolicyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupEnabled) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogBackupEnabled) {
		return false
	}
	if !p.Field4DeepEqual(ano.CrossBackupRegion) {
		return false
	}
	if !p.Field5DeepEqual(ano.Retention) {
		return false
	}
	return true
}

func (p *ModifyCrossBackupPolicyResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyResp) Field2DeepEqual(src bool) bool {

	if p.BackupEnabled != src {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyResp) Field3DeepEqual(src bool) bool {

	if p.LogBackupEnabled != src {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CrossBackupRegion, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyCrossBackupPolicyResp) Field5DeepEqual(src int32) bool {

	if p.Retention != src {
		return false
	}
	return true
}

type DownloadBackupReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewDownloadBackupReq() *DownloadBackupReq {
	return &DownloadBackupReq{}
}

func (p *DownloadBackupReq) InitDefault() {
}

func (p *DownloadBackupReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DownloadBackupReq) GetBackupId() (v string) {
	return p.BackupId
}
func (p *DownloadBackupReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DownloadBackupReq) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_DownloadBackupReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *DownloadBackupReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadBackupReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadBackupReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadBackupReq[fieldId]))
}

func (p *DownloadBackupReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DownloadBackupReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *DownloadBackupReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadBackupReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadBackupReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadBackupReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DownloadBackupReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DownloadBackupReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadBackupReq(%+v)", *p)

}

func (p *DownloadBackupReq) DeepEqual(ano *DownloadBackupReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *DownloadBackupReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadBackupReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type DownloadBackupResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewDownloadBackupResp() *DownloadBackupResp {
	return &DownloadBackupResp{}
}

func (p *DownloadBackupResp) InitDefault() {
}

func (p *DownloadBackupResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DownloadBackupResp) GetBackupId() (v string) {
	return p.BackupId
}
func (p *DownloadBackupResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DownloadBackupResp) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_DownloadBackupResp = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *DownloadBackupResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadBackupResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadBackupResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadBackupResp[fieldId]))
}

func (p *DownloadBackupResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DownloadBackupResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *DownloadBackupResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadBackupResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadBackupResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadBackupResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DownloadBackupResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DownloadBackupResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadBackupResp(%+v)", *p)

}

func (p *DownloadBackupResp) DeepEqual(ano *DownloadBackupResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *DownloadBackupResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadBackupResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type GetBackupDownloadLinkReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId   string `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
}

func NewGetBackupDownloadLinkReq() *GetBackupDownloadLinkReq {
	return &GetBackupDownloadLinkReq{}
}

func (p *GetBackupDownloadLinkReq) InitDefault() {
}

func (p *GetBackupDownloadLinkReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GetBackupDownloadLinkReq) GetBackupId() (v string) {
	return p.BackupId
}
func (p *GetBackupDownloadLinkReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GetBackupDownloadLinkReq) SetBackupId(val string) {
	p.BackupId = val
}

var fieldIDToName_GetBackupDownloadLinkReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
}

func (p *GetBackupDownloadLinkReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetBackupDownloadLinkReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBackupDownloadLinkReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetBackupDownloadLinkReq[fieldId]))
}

func (p *GetBackupDownloadLinkReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GetBackupDownloadLinkReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}

func (p *GetBackupDownloadLinkReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetBackupDownloadLinkReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetBackupDownloadLinkReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetBackupDownloadLinkReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetBackupDownloadLinkReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetBackupDownloadLinkReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBackupDownloadLinkReq(%+v)", *p)

}

func (p *GetBackupDownloadLinkReq) DeepEqual(ano *GetBackupDownloadLinkReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	return true
}

func (p *GetBackupDownloadLinkReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}

type GetBackupDownloadLinkResp struct {
	InstanceId         string     `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	BackupId           string     `thrift:"BackupId,2,required" frugal:"2,required,string" json:"BackupId"`
	BackupType         BackupType `thrift:"BackupType,3,required" frugal:"3,required,BackupType" json:"BackupType"`
	BackupFileName     string     `thrift:"BackupFileName,4,required" frugal:"4,required,string" json:"BackupFileName"`
	BackupFileSize     int32      `thrift:"BackupFileSize,5,required" frugal:"5,required,i32" json:"BackupFileSize"`
	BackupDownloadLink string     `thrift:"BackupDownloadLink,6,required" frugal:"6,required,string" json:"BackupDownloadLink"`
	LinkExpiredTime    string     `thrift:"LinkExpiredTime,7,required" frugal:"7,required,string" json:"LinkExpiredTime"`
	DownloadProgress   int64      `thrift:"DownloadProgress,8,required" frugal:"8,required,i64" json:"DownloadProgress"`
}

func NewGetBackupDownloadLinkResp() *GetBackupDownloadLinkResp {
	return &GetBackupDownloadLinkResp{}
}

func (p *GetBackupDownloadLinkResp) InitDefault() {
}

func (p *GetBackupDownloadLinkResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GetBackupDownloadLinkResp) GetBackupId() (v string) {
	return p.BackupId
}

func (p *GetBackupDownloadLinkResp) GetBackupType() (v BackupType) {
	return p.BackupType
}

func (p *GetBackupDownloadLinkResp) GetBackupFileName() (v string) {
	return p.BackupFileName
}

func (p *GetBackupDownloadLinkResp) GetBackupFileSize() (v int32) {
	return p.BackupFileSize
}

func (p *GetBackupDownloadLinkResp) GetBackupDownloadLink() (v string) {
	return p.BackupDownloadLink
}

func (p *GetBackupDownloadLinkResp) GetLinkExpiredTime() (v string) {
	return p.LinkExpiredTime
}

func (p *GetBackupDownloadLinkResp) GetDownloadProgress() (v int64) {
	return p.DownloadProgress
}
func (p *GetBackupDownloadLinkResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GetBackupDownloadLinkResp) SetBackupId(val string) {
	p.BackupId = val
}
func (p *GetBackupDownloadLinkResp) SetBackupType(val BackupType) {
	p.BackupType = val
}
func (p *GetBackupDownloadLinkResp) SetBackupFileName(val string) {
	p.BackupFileName = val
}
func (p *GetBackupDownloadLinkResp) SetBackupFileSize(val int32) {
	p.BackupFileSize = val
}
func (p *GetBackupDownloadLinkResp) SetBackupDownloadLink(val string) {
	p.BackupDownloadLink = val
}
func (p *GetBackupDownloadLinkResp) SetLinkExpiredTime(val string) {
	p.LinkExpiredTime = val
}
func (p *GetBackupDownloadLinkResp) SetDownloadProgress(val int64) {
	p.DownloadProgress = val
}

var fieldIDToName_GetBackupDownloadLinkResp = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
	3: "BackupType",
	4: "BackupFileName",
	5: "BackupFileSize",
	6: "BackupDownloadLink",
	7: "LinkExpiredTime",
	8: "DownloadProgress",
}

func (p *GetBackupDownloadLinkResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetBackupDownloadLinkResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBackupId bool = false
	var issetBackupType bool = false
	var issetBackupFileName bool = false
	var issetBackupFileSize bool = false
	var issetBackupDownloadLink bool = false
	var issetLinkExpiredTime bool = false
	var issetDownloadProgress bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupFileName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupFileSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackupDownloadLink = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetLinkExpiredTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetDownloadProgress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBackupId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBackupType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBackupFileName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBackupFileSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetBackupDownloadLink {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLinkExpiredTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDownloadProgress {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBackupDownloadLinkResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetBackupDownloadLinkResp[fieldId]))
}

func (p *GetBackupDownloadLinkResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupId = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField3(iprot thrift.TProtocol) error {

	var _field BackupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BackupType(v)
	}
	p.BackupType = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupFileName = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupFileSize = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BackupDownloadLink = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LinkExpiredTime = _field
	return nil
}
func (p *GetBackupDownloadLinkResp) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DownloadProgress = _field
	return nil
}

func (p *GetBackupDownloadLinkResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetBackupDownloadLinkResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetBackupDownloadLinkResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BackupType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupFileName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupFileName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupFileSize", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BackupFileSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BackupDownloadLink", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BackupDownloadLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkExpiredTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LinkExpiredTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DownloadProgress", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DownloadProgress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *GetBackupDownloadLinkResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBackupDownloadLinkResp(%+v)", *p)

}

func (p *GetBackupDownloadLinkResp) DeepEqual(ano *GetBackupDownloadLinkResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupType) {
		return false
	}
	if !p.Field4DeepEqual(ano.BackupFileName) {
		return false
	}
	if !p.Field5DeepEqual(ano.BackupFileSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.BackupDownloadLink) {
		return false
	}
	if !p.Field7DeepEqual(ano.LinkExpiredTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.DownloadProgress) {
		return false
	}
	return true
}

func (p *GetBackupDownloadLinkResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BackupId, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field3DeepEqual(src BackupType) bool {

	if p.BackupType != src {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.BackupFileName, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field5DeepEqual(src int32) bool {

	if p.BackupFileSize != src {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.BackupDownloadLink, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field7DeepEqual(src string) bool {

	if strings.Compare(p.LinkExpiredTime, src) != 0 {
		return false
	}
	return true
}
func (p *GetBackupDownloadLinkResp) Field8DeepEqual(src int64) bool {

	if p.DownloadProgress != src {
		return false
	}
	return true
}

type RestoreToExistedInstanceReq struct {
	SourceDBInstanceId string             `thrift:"SourceDBInstanceId,1,required" frugal:"1,required,string" json:"SourceDBInstanceId"`
	TargetDBInstanceId string             `thrift:"TargetDBInstanceId,2,required" frugal:"2,required,string" json:"TargetDBInstanceId"`
	BackupId           *string            `thrift:"BackupId,3,optional" frugal:"3,optional,string" json:"BackupId,omitempty"`
	RestoreTime        *string            `thrift:"RestoreTime,4,optional" frugal:"4,optional,string" json:"RestoreTime,omitempty"`
	Databases          []*DatabasesObject `thrift:"Databases,5,required" frugal:"5,required,list<DatabasesObject>" json:"Databases"`
}

func NewRestoreToExistedInstanceReq() *RestoreToExistedInstanceReq {
	return &RestoreToExistedInstanceReq{}
}

func (p *RestoreToExistedInstanceReq) InitDefault() {
}

func (p *RestoreToExistedInstanceReq) GetSourceDBInstanceId() (v string) {
	return p.SourceDBInstanceId
}

func (p *RestoreToExistedInstanceReq) GetTargetDBInstanceId() (v string) {
	return p.TargetDBInstanceId
}

var RestoreToExistedInstanceReq_BackupId_DEFAULT string

func (p *RestoreToExistedInstanceReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return RestoreToExistedInstanceReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var RestoreToExistedInstanceReq_RestoreTime_DEFAULT string

func (p *RestoreToExistedInstanceReq) GetRestoreTime() (v string) {
	if !p.IsSetRestoreTime() {
		return RestoreToExistedInstanceReq_RestoreTime_DEFAULT
	}
	return *p.RestoreTime
}

func (p *RestoreToExistedInstanceReq) GetDatabases() (v []*DatabasesObject) {
	return p.Databases
}
func (p *RestoreToExistedInstanceReq) SetSourceDBInstanceId(val string) {
	p.SourceDBInstanceId = val
}
func (p *RestoreToExistedInstanceReq) SetTargetDBInstanceId(val string) {
	p.TargetDBInstanceId = val
}
func (p *RestoreToExistedInstanceReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *RestoreToExistedInstanceReq) SetRestoreTime(val *string) {
	p.RestoreTime = val
}
func (p *RestoreToExistedInstanceReq) SetDatabases(val []*DatabasesObject) {
	p.Databases = val
}

var fieldIDToName_RestoreToExistedInstanceReq = map[int16]string{
	1: "SourceDBInstanceId",
	2: "TargetDBInstanceId",
	3: "BackupId",
	4: "RestoreTime",
	5: "Databases",
}

func (p *RestoreToExistedInstanceReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *RestoreToExistedInstanceReq) IsSetRestoreTime() bool {
	return p.RestoreTime != nil
}

func (p *RestoreToExistedInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToExistedInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSourceDBInstanceId bool = false
	var issetTargetDBInstanceId bool = false
	var issetDatabases bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSourceDBInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTargetDBInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabases = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSourceDBInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTargetDBInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatabases {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToExistedInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToExistedInstanceReq[fieldId]))
}

func (p *RestoreToExistedInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SourceDBInstanceId = _field
	return nil
}
func (p *RestoreToExistedInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TargetDBInstanceId = _field
	return nil
}
func (p *RestoreToExistedInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *RestoreToExistedInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RestoreTime = _field
	return nil
}
func (p *RestoreToExistedInstanceReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Databases = _field
	return nil
}

func (p *RestoreToExistedInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToExistedInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToExistedInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SourceDBInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SourceDBInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TargetDBInstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TargetDBInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTime() {
		if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RestoreTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Databases", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Databases)); err != nil {
		return err
	}
	for _, v := range p.Databases {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RestoreToExistedInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToExistedInstanceReq(%+v)", *p)

}

func (p *RestoreToExistedInstanceReq) DeepEqual(ano *RestoreToExistedInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SourceDBInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TargetDBInstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field4DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.Databases) {
		return false
	}
	return true
}

func (p *RestoreToExistedInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SourceDBInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToExistedInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TargetDBInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToExistedInstanceReq) Field3DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToExistedInstanceReq) Field4DeepEqual(src *string) bool {

	if p.RestoreTime == src {
		return true
	} else if p.RestoreTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RestoreTime, *src) != 0 {
		return false
	}
	return true
}
func (p *RestoreToExistedInstanceReq) Field5DeepEqual(src []*DatabasesObject) bool {

	if len(p.Databases) != len(src) {
		return false
	}
	for i, v := range p.Databases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RestoreToExistedInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
}

func NewRestoreToExistedInstanceResp() *RestoreToExistedInstanceResp {
	return &RestoreToExistedInstanceResp{}
}

func (p *RestoreToExistedInstanceResp) InitDefault() {
}

func (p *RestoreToExistedInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *RestoreToExistedInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_RestoreToExistedInstanceResp = map[int16]string{
	1: "InstanceId",
}

func (p *RestoreToExistedInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToExistedInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestoreToExistedInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RestoreToExistedInstanceResp[fieldId]))
}

func (p *RestoreToExistedInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *RestoreToExistedInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestoreToExistedInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestoreToExistedInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestoreToExistedInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestoreToExistedInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestoreToExistedInstanceResp(%+v)", *p)

}

func (p *RestoreToExistedInstanceResp) DeepEqual(ano *RestoreToExistedInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *RestoreToExistedInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type CreateTosRestoreReq struct {
	TargetDBInstanceId string             `thrift:"TargetDBInstanceId,1,required" frugal:"1,required,string" json:"TargetDBInstanceId"`
	TosObjectPositions string             `thrift:"TosObjectPositions,2,required" frugal:"2,required,string" json:"TosObjectPositions"`
	RestoreType        TosRestoreType     `thrift:"RestoreType,3,required" frugal:"3,required,TosRestoreType" json:"RestoreType"`
	IsOnline           *bool              `thrift:"IsOnline,4,optional" frugal:"4,optional,bool" json:"IsOnline,omitempty"`
	IsReplace          *bool              `thrift:"IsReplace,5,optional" frugal:"5,optional,bool" json:"IsReplace,omitempty"`
	Databases          []*DatabasesObject `thrift:"Databases,6,optional" frugal:"6,optional,list<DatabasesObject>" json:"Databases,omitempty"`
}

func NewCreateTosRestoreReq() *CreateTosRestoreReq {
	return &CreateTosRestoreReq{}
}

func (p *CreateTosRestoreReq) InitDefault() {
}

func (p *CreateTosRestoreReq) GetTargetDBInstanceId() (v string) {
	return p.TargetDBInstanceId
}

func (p *CreateTosRestoreReq) GetTosObjectPositions() (v string) {
	return p.TosObjectPositions
}

func (p *CreateTosRestoreReq) GetRestoreType() (v TosRestoreType) {
	return p.RestoreType
}

var CreateTosRestoreReq_IsOnline_DEFAULT bool

func (p *CreateTosRestoreReq) GetIsOnline() (v bool) {
	if !p.IsSetIsOnline() {
		return CreateTosRestoreReq_IsOnline_DEFAULT
	}
	return *p.IsOnline
}

var CreateTosRestoreReq_IsReplace_DEFAULT bool

func (p *CreateTosRestoreReq) GetIsReplace() (v bool) {
	if !p.IsSetIsReplace() {
		return CreateTosRestoreReq_IsReplace_DEFAULT
	}
	return *p.IsReplace
}

var CreateTosRestoreReq_Databases_DEFAULT []*DatabasesObject

func (p *CreateTosRestoreReq) GetDatabases() (v []*DatabasesObject) {
	if !p.IsSetDatabases() {
		return CreateTosRestoreReq_Databases_DEFAULT
	}
	return p.Databases
}
func (p *CreateTosRestoreReq) SetTargetDBInstanceId(val string) {
	p.TargetDBInstanceId = val
}
func (p *CreateTosRestoreReq) SetTosObjectPositions(val string) {
	p.TosObjectPositions = val
}
func (p *CreateTosRestoreReq) SetRestoreType(val TosRestoreType) {
	p.RestoreType = val
}
func (p *CreateTosRestoreReq) SetIsOnline(val *bool) {
	p.IsOnline = val
}
func (p *CreateTosRestoreReq) SetIsReplace(val *bool) {
	p.IsReplace = val
}
func (p *CreateTosRestoreReq) SetDatabases(val []*DatabasesObject) {
	p.Databases = val
}

var fieldIDToName_CreateTosRestoreReq = map[int16]string{
	1: "TargetDBInstanceId",
	2: "TosObjectPositions",
	3: "RestoreType",
	4: "IsOnline",
	5: "IsReplace",
	6: "Databases",
}

func (p *CreateTosRestoreReq) IsSetIsOnline() bool {
	return p.IsOnline != nil
}

func (p *CreateTosRestoreReq) IsSetIsReplace() bool {
	return p.IsReplace != nil
}

func (p *CreateTosRestoreReq) IsSetDatabases() bool {
	return p.Databases != nil
}

func (p *CreateTosRestoreReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTosRestoreReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTargetDBInstanceId bool = false
	var issetTosObjectPositions bool = false
	var issetRestoreType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTargetDBInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTosObjectPositions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestoreType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTargetDBInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTosObjectPositions {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRestoreType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTosRestoreReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTosRestoreReq[fieldId]))
}

func (p *CreateTosRestoreReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TargetDBInstanceId = _field
	return nil
}
func (p *CreateTosRestoreReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TosObjectPositions = _field
	return nil
}
func (p *CreateTosRestoreReq) ReadField3(iprot thrift.TProtocol) error {

	var _field TosRestoreType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TosRestoreType(v)
	}
	p.RestoreType = _field
	return nil
}
func (p *CreateTosRestoreReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsOnline = _field
	return nil
}
func (p *CreateTosRestoreReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsReplace = _field
	return nil
}
func (p *CreateTosRestoreReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesObject, 0, size)
	values := make([]DatabasesObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Databases = _field
	return nil
}

func (p *CreateTosRestoreReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTosRestoreReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTosRestoreReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TargetDBInstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TargetDBInstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TosObjectPositions", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TosObjectPositions); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RestoreType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsOnline() {
		if err = oprot.WriteFieldBegin("IsOnline", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsOnline); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsReplace() {
		if err = oprot.WriteFieldBegin("IsReplace", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsReplace); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateTosRestoreReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabases() {
		if err = oprot.WriteFieldBegin("Databases", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Databases)); err != nil {
			return err
		}
		for _, v := range p.Databases {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateTosRestoreReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTosRestoreReq(%+v)", *p)

}

func (p *CreateTosRestoreReq) DeepEqual(ano *CreateTosRestoreReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TargetDBInstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TosObjectPositions) {
		return false
	}
	if !p.Field3DeepEqual(ano.RestoreType) {
		return false
	}
	if !p.Field4DeepEqual(ano.IsOnline) {
		return false
	}
	if !p.Field5DeepEqual(ano.IsReplace) {
		return false
	}
	if !p.Field6DeepEqual(ano.Databases) {
		return false
	}
	return true
}

func (p *CreateTosRestoreReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TargetDBInstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTosRestoreReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TosObjectPositions, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTosRestoreReq) Field3DeepEqual(src TosRestoreType) bool {

	if p.RestoreType != src {
		return false
	}
	return true
}
func (p *CreateTosRestoreReq) Field4DeepEqual(src *bool) bool {

	if p.IsOnline == src {
		return true
	} else if p.IsOnline == nil || src == nil {
		return false
	}
	if *p.IsOnline != *src {
		return false
	}
	return true
}
func (p *CreateTosRestoreReq) Field5DeepEqual(src *bool) bool {

	if p.IsReplace == src {
		return true
	} else if p.IsReplace == nil || src == nil {
		return false
	}
	if *p.IsReplace != *src {
		return false
	}
	return true
}
func (p *CreateTosRestoreReq) Field6DeepEqual(src []*DatabasesObject) bool {

	if len(p.Databases) != len(src) {
		return false
	}
	for i, v := range p.Databases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateTosRestoreResp struct {
	RestoreTaskId string `thrift:"RestoreTaskId,1,required" frugal:"1,required,string" json:"RestoreTaskId"`
}

func NewCreateTosRestoreResp() *CreateTosRestoreResp {
	return &CreateTosRestoreResp{}
}

func (p *CreateTosRestoreResp) InitDefault() {
}

func (p *CreateTosRestoreResp) GetRestoreTaskId() (v string) {
	return p.RestoreTaskId
}
func (p *CreateTosRestoreResp) SetRestoreTaskId(val string) {
	p.RestoreTaskId = val
}

var fieldIDToName_CreateTosRestoreResp = map[int16]string{
	1: "RestoreTaskId",
}

func (p *CreateTosRestoreResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTosRestoreResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRestoreTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRestoreTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTosRestoreResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTosRestoreResp[fieldId]))
}

func (p *CreateTosRestoreResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RestoreTaskId = _field
	return nil
}

func (p *CreateTosRestoreResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTosRestoreResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTosRestoreResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTosRestoreResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RestoreTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTosRestoreResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTosRestoreResp(%+v)", *p)

}

func (p *CreateTosRestoreResp) DeepEqual(ano *CreateTosRestoreResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RestoreTaskId) {
		return false
	}
	return true
}

func (p *CreateTosRestoreResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RestoreTaskId, src) != 0 {
		return false
	}
	return true
}

type DescribeTosRestoreTasksReq struct {
	InstanceId     *string  `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceName   *string  `thrift:"InstanceName,2,optional" frugal:"2,optional,string" json:"InstanceName,omitempty"`
	QueryStartTime *string  `thrift:"QueryStartTime,3,optional" frugal:"3,optional,string" json:"QueryStartTime,omitempty"`
	QueryEndTime   *string  `thrift:"QueryEndTime,4,optional" frugal:"4,optional,string" json:"QueryEndTime,omitempty"`
	PageNumber     *int32   `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
	PageSize       *int32   `thrift:"PageSize,6,optional" frugal:"6,optional,i32" json:"PageSize,omitempty"`
	ProjectName    *string  `thrift:"ProjectName,7,optional" frugal:"7,optional,string" json:"ProjectName,omitempty"`
	RestoreTaskIds []string `thrift:"RestoreTaskIds,8,optional" frugal:"8,optional,list<string>" json:"RestoreTaskIds,omitempty"`
}

func NewDescribeTosRestoreTasksReq() *DescribeTosRestoreTasksReq {
	return &DescribeTosRestoreTasksReq{}
}

func (p *DescribeTosRestoreTasksReq) InitDefault() {
}

var DescribeTosRestoreTasksReq_InstanceId_DEFAULT string

func (p *DescribeTosRestoreTasksReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeTosRestoreTasksReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeTosRestoreTasksReq_InstanceName_DEFAULT string

func (p *DescribeTosRestoreTasksReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return DescribeTosRestoreTasksReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var DescribeTosRestoreTasksReq_QueryStartTime_DEFAULT string

func (p *DescribeTosRestoreTasksReq) GetQueryStartTime() (v string) {
	if !p.IsSetQueryStartTime() {
		return DescribeTosRestoreTasksReq_QueryStartTime_DEFAULT
	}
	return *p.QueryStartTime
}

var DescribeTosRestoreTasksReq_QueryEndTime_DEFAULT string

func (p *DescribeTosRestoreTasksReq) GetQueryEndTime() (v string) {
	if !p.IsSetQueryEndTime() {
		return DescribeTosRestoreTasksReq_QueryEndTime_DEFAULT
	}
	return *p.QueryEndTime
}

var DescribeTosRestoreTasksReq_PageNumber_DEFAULT int32

func (p *DescribeTosRestoreTasksReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTosRestoreTasksReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTosRestoreTasksReq_PageSize_DEFAULT int32

func (p *DescribeTosRestoreTasksReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTosRestoreTasksReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTosRestoreTasksReq_ProjectName_DEFAULT string

func (p *DescribeTosRestoreTasksReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeTosRestoreTasksReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var DescribeTosRestoreTasksReq_RestoreTaskIds_DEFAULT []string

func (p *DescribeTosRestoreTasksReq) GetRestoreTaskIds() (v []string) {
	if !p.IsSetRestoreTaskIds() {
		return DescribeTosRestoreTasksReq_RestoreTaskIds_DEFAULT
	}
	return p.RestoreTaskIds
}
func (p *DescribeTosRestoreTasksReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeTosRestoreTasksReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *DescribeTosRestoreTasksReq) SetQueryStartTime(val *string) {
	p.QueryStartTime = val
}
func (p *DescribeTosRestoreTasksReq) SetQueryEndTime(val *string) {
	p.QueryEndTime = val
}
func (p *DescribeTosRestoreTasksReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTosRestoreTasksReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTosRestoreTasksReq) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *DescribeTosRestoreTasksReq) SetRestoreTaskIds(val []string) {
	p.RestoreTaskIds = val
}

var fieldIDToName_DescribeTosRestoreTasksReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "QueryStartTime",
	4: "QueryEndTime",
	5: "PageNumber",
	6: "PageSize",
	7: "ProjectName",
	8: "RestoreTaskIds",
}

func (p *DescribeTosRestoreTasksReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetQueryStartTime() bool {
	return p.QueryStartTime != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetQueryEndTime() bool {
	return p.QueryEndTime != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeTosRestoreTasksReq) IsSetRestoreTaskIds() bool {
	return p.RestoreTaskIds != nil
}

func (p *DescribeTosRestoreTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *DescribeTosRestoreTasksReq) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RestoreTaskIds = _field
	return nil
}

func (p *DescribeTosRestoreTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTosRestoreTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryStartTime() {
		if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryEndTime() {
		if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.QueryEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTaskIds() {
		if err = oprot.WriteFieldBegin("RestoreTaskIds", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.RestoreTaskIds)); err != nil {
			return err
		}
		for _, v := range p.RestoreTaskIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTosRestoreTasksReq(%+v)", *p)

}

func (p *DescribeTosRestoreTasksReq) DeepEqual(ano *DescribeTosRestoreTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field7DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field8DeepEqual(ano.RestoreTaskIds) {
		return false
	}
	return true
}

func (p *DescribeTosRestoreTasksReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field2DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field3DeepEqual(src *string) bool {

	if p.QueryStartTime == src {
		return true
	} else if p.QueryStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field4DeepEqual(src *string) bool {

	if p.QueryEndTime == src {
		return true
	} else if p.QueryEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.QueryEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field6DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field7DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksReq) Field8DeepEqual(src []string) bool {

	if len(p.RestoreTaskIds) != len(src) {
		return false
	}
	for i, v := range p.RestoreTaskIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeTosRestoreTasksResp struct {
	Total        int32                `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	RestoreTasks []*RestoreTaskObject `thrift:"RestoreTasks,2,required" frugal:"2,required,list<RestoreTaskObject>" json:"RestoreTasks"`
}

func NewDescribeTosRestoreTasksResp() *DescribeTosRestoreTasksResp {
	return &DescribeTosRestoreTasksResp{}
}

func (p *DescribeTosRestoreTasksResp) InitDefault() {
}

func (p *DescribeTosRestoreTasksResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeTosRestoreTasksResp) GetRestoreTasks() (v []*RestoreTaskObject) {
	return p.RestoreTasks
}
func (p *DescribeTosRestoreTasksResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeTosRestoreTasksResp) SetRestoreTasks(val []*RestoreTaskObject) {
	p.RestoreTasks = val
}

var fieldIDToName_DescribeTosRestoreTasksResp = map[int16]string{
	1: "Total",
	2: "RestoreTasks",
}

func (p *DescribeTosRestoreTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetRestoreTasks bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestoreTasks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRestoreTasks {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTosRestoreTasksResp[fieldId]))
}

func (p *DescribeTosRestoreTasksResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeTosRestoreTasksResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RestoreTaskObject, 0, size)
	values := make([]RestoreTaskObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RestoreTasks = _field
	return nil
}

func (p *DescribeTosRestoreTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTosRestoreTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTosRestoreTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTasks", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RestoreTasks)); err != nil {
		return err
	}
	for _, v := range p.RestoreTasks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTosRestoreTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTosRestoreTasksResp(%+v)", *p)

}

func (p *DescribeTosRestoreTasksResp) DeepEqual(ano *DescribeTosRestoreTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.RestoreTasks) {
		return false
	}
	return true
}

func (p *DescribeTosRestoreTasksResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTasksResp) Field2DeepEqual(src []*RestoreTaskObject) bool {

	if len(p.RestoreTasks) != len(src) {
		return false
	}
	for i, v := range p.RestoreTasks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeTosRestoreTaskDetailReq struct {
	RestoreTaskId string `thrift:"RestoreTaskId,1,required" frugal:"1,required,string" json:"RestoreTaskId"`
	PageNumber    *int32 `thrift:"PageNumber,2,optional" frugal:"2,optional,i32" json:"PageNumber,omitempty"`
	PageSize      *int32 `thrift:"PageSize,3,optional" frugal:"3,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeTosRestoreTaskDetailReq() *DescribeTosRestoreTaskDetailReq {
	return &DescribeTosRestoreTaskDetailReq{}
}

func (p *DescribeTosRestoreTaskDetailReq) InitDefault() {
}

func (p *DescribeTosRestoreTaskDetailReq) GetRestoreTaskId() (v string) {
	return p.RestoreTaskId
}

var DescribeTosRestoreTaskDetailReq_PageNumber_DEFAULT int32

func (p *DescribeTosRestoreTaskDetailReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTosRestoreTaskDetailReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTosRestoreTaskDetailReq_PageSize_DEFAULT int32

func (p *DescribeTosRestoreTaskDetailReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTosRestoreTaskDetailReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeTosRestoreTaskDetailReq) SetRestoreTaskId(val string) {
	p.RestoreTaskId = val
}
func (p *DescribeTosRestoreTaskDetailReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTosRestoreTaskDetailReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeTosRestoreTaskDetailReq = map[int16]string{
	1: "RestoreTaskId",
	2: "PageNumber",
	3: "PageSize",
}

func (p *DescribeTosRestoreTaskDetailReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTosRestoreTaskDetailReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTosRestoreTaskDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTaskDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRestoreTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRestoreTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTaskDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTosRestoreTaskDetailReq[fieldId]))
}

func (p *DescribeTosRestoreTaskDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RestoreTaskId = _field
	return nil
}
func (p *DescribeTosRestoreTaskDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTosRestoreTaskDetailReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeTosRestoreTaskDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTaskDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTosRestoreTaskDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RestoreTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTosRestoreTaskDetailReq(%+v)", *p)

}

func (p *DescribeTosRestoreTaskDetailReq) DeepEqual(ano *DescribeTosRestoreTaskDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RestoreTaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeTosRestoreTaskDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RestoreTaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTaskDetailReq) Field2DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTaskDetailReq) Field3DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeTosRestoreTaskDetailResp struct {
	Total              int32                      `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	RestoreTaskDetails []*RestoreTaskDetailObject `thrift:"RestoreTaskDetails,2,required" frugal:"2,required,list<RestoreTaskDetailObject>" json:"RestoreTaskDetails"`
}

func NewDescribeTosRestoreTaskDetailResp() *DescribeTosRestoreTaskDetailResp {
	return &DescribeTosRestoreTaskDetailResp{}
}

func (p *DescribeTosRestoreTaskDetailResp) InitDefault() {
}

func (p *DescribeTosRestoreTaskDetailResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeTosRestoreTaskDetailResp) GetRestoreTaskDetails() (v []*RestoreTaskDetailObject) {
	return p.RestoreTaskDetails
}
func (p *DescribeTosRestoreTaskDetailResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeTosRestoreTaskDetailResp) SetRestoreTaskDetails(val []*RestoreTaskDetailObject) {
	p.RestoreTaskDetails = val
}

var fieldIDToName_DescribeTosRestoreTaskDetailResp = map[int16]string{
	1: "Total",
	2: "RestoreTaskDetails",
}

func (p *DescribeTosRestoreTaskDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTaskDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetRestoreTaskDetails bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRestoreTaskDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRestoreTaskDetails {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTosRestoreTaskDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTosRestoreTaskDetailResp[fieldId]))
}

func (p *DescribeTosRestoreTaskDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeTosRestoreTaskDetailResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RestoreTaskDetailObject, 0, size)
	values := make([]RestoreTaskDetailObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RestoreTaskDetails = _field
	return nil
}

func (p *DescribeTosRestoreTaskDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTosRestoreTaskDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTosRestoreTaskDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RestoreTaskDetails", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RestoreTaskDetails)); err != nil {
		return err
	}
	for _, v := range p.RestoreTaskDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTosRestoreTaskDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTosRestoreTaskDetailResp(%+v)", *p)

}

func (p *DescribeTosRestoreTaskDetailResp) DeepEqual(ano *DescribeTosRestoreTaskDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.RestoreTaskDetails) {
		return false
	}
	return true
}

func (p *DescribeTosRestoreTaskDetailResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeTosRestoreTaskDetailResp) Field2DeepEqual(src []*RestoreTaskDetailObject) bool {

	if len(p.RestoreTaskDetails) != len(src) {
		return false
	}
	for i, v := range p.RestoreTaskDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

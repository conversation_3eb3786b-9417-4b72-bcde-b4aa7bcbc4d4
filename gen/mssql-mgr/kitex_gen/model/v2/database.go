// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CreateDatabaseReq struct {
	InstanceId         string                     `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DBName             string                     `thrift:"DBName,2,required" frugal:"2,required,string" json:"DBName"`
	DatabasePrivileges []*DatabasePrivilegeObject `thrift:"DatabasePrivileges,3,optional" frugal:"3,optional,list<DatabasePrivilegeObject>" json:"DatabasePrivileges,omitempty"`
}

func NewCreateDatabaseReq() *CreateDatabaseReq {
	return &CreateDatabaseReq{}
}

func (p *CreateDatabaseReq) InitDefault() {
}

func (p *CreateDatabaseReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateDatabaseReq) GetDBName() (v string) {
	return p.DBName
}

var CreateDatabaseReq_DatabasePrivileges_DEFAULT []*DatabasePrivilegeObject

func (p *CreateDatabaseReq) GetDatabasePrivileges() (v []*DatabasePrivilegeObject) {
	if !p.IsSetDatabasePrivileges() {
		return CreateDatabaseReq_DatabasePrivileges_DEFAULT
	}
	return p.DatabasePrivileges
}
func (p *CreateDatabaseReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateDatabaseReq) SetDBName(val string) {
	p.DBName = val
}
func (p *CreateDatabaseReq) SetDatabasePrivileges(val []*DatabasePrivilegeObject) {
	p.DatabasePrivileges = val
}

var fieldIDToName_CreateDatabaseReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
	3: "DatabasePrivileges",
}

func (p *CreateDatabaseReq) IsSetDatabasePrivileges() bool {
	return p.DatabasePrivileges != nil
}

func (p *CreateDatabaseReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateDatabaseReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateDatabaseReq[fieldId]))
}

func (p *CreateDatabaseReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateDatabaseReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *CreateDatabaseReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasePrivilegeObject, 0, size)
	values := make([]DatabasePrivilegeObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DatabasePrivileges = _field
	return nil
}

func (p *CreateDatabaseReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDatabaseReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateDatabaseReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabasePrivileges() {
		if err = oprot.WriteFieldBegin("DatabasePrivileges", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DatabasePrivileges)); err != nil {
			return err
		}
		for _, v := range p.DatabasePrivileges {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateDatabaseReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDatabaseReq(%+v)", *p)

}

func (p *CreateDatabaseReq) DeepEqual(ano *CreateDatabaseReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field3DeepEqual(ano.DatabasePrivileges) {
		return false
	}
	return true
}

func (p *CreateDatabaseReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDatabaseReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateDatabaseReq) Field3DeepEqual(src []*DatabasePrivilegeObject) bool {

	if len(p.DatabasePrivileges) != len(src) {
		return false
	}
	for i, v := range p.DatabasePrivileges {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateDatabaseResp struct {
}

func NewCreateDatabaseResp() *CreateDatabaseResp {
	return &CreateDatabaseResp{}
}

func (p *CreateDatabaseResp) InitDefault() {
}

var fieldIDToName_CreateDatabaseResp = map[int16]string{}

func (p *CreateDatabaseResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateDatabaseResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateDatabaseResp")

	if err = oprot.WriteStructBegin("CreateDatabaseResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateDatabaseResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDatabaseResp(%+v)", *p)

}

func (p *CreateDatabaseResp) DeepEqual(ano *CreateDatabaseResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteDatabaseReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DBName     string `thrift:"DBName,2,required" frugal:"2,required,string" json:"DBName"`
}

func NewDeleteDatabaseReq() *DeleteDatabaseReq {
	return &DeleteDatabaseReq{}
}

func (p *DeleteDatabaseReq) InitDefault() {
}

func (p *DeleteDatabaseReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDatabaseReq) GetDBName() (v string) {
	return p.DBName
}
func (p *DeleteDatabaseReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDatabaseReq) SetDBName(val string) {
	p.DBName = val
}

var fieldIDToName_DeleteDatabaseReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
}

func (p *DeleteDatabaseReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDatabaseReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDatabaseReq[fieldId]))
}

func (p *DeleteDatabaseReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDatabaseReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}

func (p *DeleteDatabaseReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDatabaseReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDatabaseReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDatabaseReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDatabaseReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDatabaseReq(%+v)", *p)

}

func (p *DeleteDatabaseReq) DeepEqual(ano *DeleteDatabaseReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	return true
}

func (p *DeleteDatabaseReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDatabaseReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}

type DeleteDatabaseResp struct {
}

func NewDeleteDatabaseResp() *DeleteDatabaseResp {
	return &DeleteDatabaseResp{}
}

func (p *DeleteDatabaseResp) InitDefault() {
}

var fieldIDToName_DeleteDatabaseResp = map[int16]string{}

func (p *DeleteDatabaseResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteDatabaseResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDatabaseResp")

	if err = oprot.WriteStructBegin("DeleteDatabaseResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDatabaseResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDatabaseResp(%+v)", *p)

}

func (p *DeleteDatabaseResp) DeepEqual(ano *DeleteDatabaseResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ModifyDatabasePrivilegeReq struct {
	InstanceId                   string                          `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	DBName                       string                          `thrift:"DBName,2,required" frugal:"2,required,string" validate:"required"`
	ModifyDatabasePrivilegesInfo []*ModifyDatabasePrivilegesInfo `thrift:"ModifyDatabasePrivilegesInfo,3,optional" frugal:"3,optional,list<ModifyDatabasePrivilegesInfo>" json:"ModifyDatabasePrivilegesInfo,omitempty"`
}

func NewModifyDatabasePrivilegeReq() *ModifyDatabasePrivilegeReq {
	return &ModifyDatabasePrivilegeReq{}
}

func (p *ModifyDatabasePrivilegeReq) InitDefault() {
}

func (p *ModifyDatabasePrivilegeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDatabasePrivilegeReq) GetDBName() (v string) {
	return p.DBName
}

var ModifyDatabasePrivilegeReq_ModifyDatabasePrivilegesInfo_DEFAULT []*ModifyDatabasePrivilegesInfo

func (p *ModifyDatabasePrivilegeReq) GetModifyDatabasePrivilegesInfo() (v []*ModifyDatabasePrivilegesInfo) {
	if !p.IsSetModifyDatabasePrivilegesInfo() {
		return ModifyDatabasePrivilegeReq_ModifyDatabasePrivilegesInfo_DEFAULT
	}
	return p.ModifyDatabasePrivilegesInfo
}
func (p *ModifyDatabasePrivilegeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDatabasePrivilegeReq) SetDBName(val string) {
	p.DBName = val
}
func (p *ModifyDatabasePrivilegeReq) SetModifyDatabasePrivilegesInfo(val []*ModifyDatabasePrivilegesInfo) {
	p.ModifyDatabasePrivilegesInfo = val
}

var fieldIDToName_ModifyDatabasePrivilegeReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
	3: "ModifyDatabasePrivilegesInfo",
}

func (p *ModifyDatabasePrivilegeReq) IsSetModifyDatabasePrivilegesInfo() bool {
	return p.ModifyDatabasePrivilegesInfo != nil
}

func (p *ModifyDatabasePrivilegeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDatabasePrivilegeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDatabasePrivilegeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDatabasePrivilegeReq[fieldId]))
}

func (p *ModifyDatabasePrivilegeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDatabasePrivilegeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *ModifyDatabasePrivilegeReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ModifyDatabasePrivilegesInfo, 0, size)
	values := make([]ModifyDatabasePrivilegesInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ModifyDatabasePrivilegesInfo = _field
	return nil
}

func (p *ModifyDatabasePrivilegeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDatabasePrivilegeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDatabasePrivilegeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetModifyDatabasePrivilegesInfo() {
		if err = oprot.WriteFieldBegin("ModifyDatabasePrivilegesInfo", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ModifyDatabasePrivilegesInfo)); err != nil {
			return err
		}
		for _, v := range p.ModifyDatabasePrivilegesInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDatabasePrivilegeReq(%+v)", *p)

}

func (p *ModifyDatabasePrivilegeReq) DeepEqual(ano *ModifyDatabasePrivilegeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field3DeepEqual(ano.ModifyDatabasePrivilegesInfo) {
		return false
	}
	return true
}

func (p *ModifyDatabasePrivilegeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDatabasePrivilegeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDatabasePrivilegeReq) Field3DeepEqual(src []*ModifyDatabasePrivilegesInfo) bool {

	if len(p.ModifyDatabasePrivilegesInfo) != len(src) {
		return false
	}
	for i, v := range p.ModifyDatabasePrivilegesInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ModifyDatabasePrivilegeResp struct {
}

func NewModifyDatabasePrivilegeResp() *ModifyDatabasePrivilegeResp {
	return &ModifyDatabasePrivilegeResp{}
}

func (p *ModifyDatabasePrivilegeResp) InitDefault() {
}

var fieldIDToName_ModifyDatabasePrivilegeResp = map[int16]string{}

func (p *ModifyDatabasePrivilegeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDatabasePrivilegeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDatabasePrivilegeResp")

	if err = oprot.WriteStructBegin("ModifyDatabasePrivilegeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDatabasePrivilegeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDatabasePrivilegeResp(%+v)", *p)

}

func (p *ModifyDatabasePrivilegeResp) DeepEqual(ano *ModifyDatabasePrivilegeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeDBInstanceDatabasesReq struct {
	InstanceId string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DBName     *string `thrift:"DBName,2,optional" frugal:"2,optional,string" json:"DBName,omitempty"`
	IsDetail   *bool   `thrift:"IsDetail,3,optional" frugal:"3,optional,bool" json:"IsDetail,omitempty"`
	PageSize   *int32  `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	PageNumber *int32  `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
}

func NewDescribeDBInstanceDatabasesReq() *DescribeDBInstanceDatabasesReq {
	return &DescribeDBInstanceDatabasesReq{}
}

func (p *DescribeDBInstanceDatabasesReq) InitDefault() {
}

func (p *DescribeDBInstanceDatabasesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeDBInstanceDatabasesReq_DBName_DEFAULT string

func (p *DescribeDBInstanceDatabasesReq) GetDBName() (v string) {
	if !p.IsSetDBName() {
		return DescribeDBInstanceDatabasesReq_DBName_DEFAULT
	}
	return *p.DBName
}

var DescribeDBInstanceDatabasesReq_IsDetail_DEFAULT bool

func (p *DescribeDBInstanceDatabasesReq) GetIsDetail() (v bool) {
	if !p.IsSetIsDetail() {
		return DescribeDBInstanceDatabasesReq_IsDetail_DEFAULT
	}
	return *p.IsDetail
}

var DescribeDBInstanceDatabasesReq_PageSize_DEFAULT int32

func (p *DescribeDBInstanceDatabasesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBInstanceDatabasesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDBInstanceDatabasesReq_PageNumber_DEFAULT int32

func (p *DescribeDBInstanceDatabasesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBInstanceDatabasesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}
func (p *DescribeDBInstanceDatabasesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceDatabasesReq) SetDBName(val *string) {
	p.DBName = val
}
func (p *DescribeDBInstanceDatabasesReq) SetIsDetail(val *bool) {
	p.IsDetail = val
}
func (p *DescribeDBInstanceDatabasesReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDBInstanceDatabasesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}

var fieldIDToName_DescribeDBInstanceDatabasesReq = map[int16]string{
	1: "InstanceId",
	2: "DBName",
	3: "IsDetail",
	4: "PageSize",
	5: "PageNumber",
}

func (p *DescribeDBInstanceDatabasesReq) IsSetDBName() bool {
	return p.DBName != nil
}

func (p *DescribeDBInstanceDatabasesReq) IsSetIsDetail() bool {
	return p.IsDetail != nil
}

func (p *DescribeDBInstanceDatabasesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBInstanceDatabasesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBInstanceDatabasesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceDatabasesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceDatabasesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceDatabasesReq[fieldId]))
}

func (p *DescribeDBInstanceDatabasesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceDatabasesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBName = _field
	return nil
}
func (p *DescribeDBInstanceDatabasesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsDetail = _field
	return nil
}
func (p *DescribeDBInstanceDatabasesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDBInstanceDatabasesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}

func (p *DescribeDBInstanceDatabasesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceDatabasesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceDatabasesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBName() {
		if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsDetail() {
		if err = oprot.WriteFieldBegin("IsDetail", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsDetail); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceDatabasesReq(%+v)", *p)

}

func (p *DescribeDBInstanceDatabasesReq) DeepEqual(ano *DescribeDBInstanceDatabasesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsDetail) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceDatabasesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceDatabasesReq) Field2DeepEqual(src *string) bool {

	if p.DBName == src {
		return true
	} else if p.DBName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceDatabasesReq) Field3DeepEqual(src *bool) bool {

	if p.IsDetail == src {
		return true
	} else if p.IsDetail == nil || src == nil {
		return false
	}
	if *p.IsDetail != *src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceDatabasesReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceDatabasesReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}

type DescribeDBInstanceDatabasesResp struct {
	Total            int32                    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	DatabasesDetails []*DatabasesDetailObject `thrift:"DatabasesDetails,2,required" frugal:"2,required,list<DatabasesDetailObject>" json:"DatabasesDetails"`
}

func NewDescribeDBInstanceDatabasesResp() *DescribeDBInstanceDatabasesResp {
	return &DescribeDBInstanceDatabasesResp{}
}

func (p *DescribeDBInstanceDatabasesResp) InitDefault() {
}

func (p *DescribeDBInstanceDatabasesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeDBInstanceDatabasesResp) GetDatabasesDetails() (v []*DatabasesDetailObject) {
	return p.DatabasesDetails
}
func (p *DescribeDBInstanceDatabasesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeDBInstanceDatabasesResp) SetDatabasesDetails(val []*DatabasesDetailObject) {
	p.DatabasesDetails = val
}

var fieldIDToName_DescribeDBInstanceDatabasesResp = map[int16]string{
	1: "Total",
	2: "DatabasesDetails",
}

func (p *DescribeDBInstanceDatabasesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceDatabasesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatabasesDetails bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabasesDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabasesDetails {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceDatabasesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceDatabasesResp[fieldId]))
}

func (p *DescribeDBInstanceDatabasesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeDBInstanceDatabasesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesDetailObject, 0, size)
	values := make([]DatabasesDetailObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DatabasesDetails = _field
	return nil
}

func (p *DescribeDBInstanceDatabasesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceDatabasesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceDatabasesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabasesDetails", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DatabasesDetails)); err != nil {
		return err
	}
	for _, v := range p.DatabasesDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceDatabasesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceDatabasesResp(%+v)", *p)

}

func (p *DescribeDBInstanceDatabasesResp) DeepEqual(ano *DescribeDBInstanceDatabasesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatabasesDetails) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceDatabasesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceDatabasesResp) Field2DeepEqual(src []*DatabasesDetailObject) bool {

	if len(p.DatabasesDetails) != len(src) {
		return false
	}
	for i, v := range p.DatabasesDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeRecoverableDatabasesReq struct {
	InstanceId  string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BackupId    *string `thrift:"BackupId,2,optional" frugal:"2,optional,string" json:"BackupId,omitempty"`
	RestoreTime *string `thrift:"RestoreTime,3,optional" frugal:"3,optional,string" json:"RestoreTime,omitempty"`
	PageSize    *int32  `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	PageNumber  *int32  `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
}

func NewDescribeRecoverableDatabasesReq() *DescribeRecoverableDatabasesReq {
	return &DescribeRecoverableDatabasesReq{}
}

func (p *DescribeRecoverableDatabasesReq) InitDefault() {
}

func (p *DescribeRecoverableDatabasesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeRecoverableDatabasesReq_BackupId_DEFAULT string

func (p *DescribeRecoverableDatabasesReq) GetBackupId() (v string) {
	if !p.IsSetBackupId() {
		return DescribeRecoverableDatabasesReq_BackupId_DEFAULT
	}
	return *p.BackupId
}

var DescribeRecoverableDatabasesReq_RestoreTime_DEFAULT string

func (p *DescribeRecoverableDatabasesReq) GetRestoreTime() (v string) {
	if !p.IsSetRestoreTime() {
		return DescribeRecoverableDatabasesReq_RestoreTime_DEFAULT
	}
	return *p.RestoreTime
}

var DescribeRecoverableDatabasesReq_PageSize_DEFAULT int32

func (p *DescribeRecoverableDatabasesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeRecoverableDatabasesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeRecoverableDatabasesReq_PageNumber_DEFAULT int32

func (p *DescribeRecoverableDatabasesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeRecoverableDatabasesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}
func (p *DescribeRecoverableDatabasesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeRecoverableDatabasesReq) SetBackupId(val *string) {
	p.BackupId = val
}
func (p *DescribeRecoverableDatabasesReq) SetRestoreTime(val *string) {
	p.RestoreTime = val
}
func (p *DescribeRecoverableDatabasesReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeRecoverableDatabasesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}

var fieldIDToName_DescribeRecoverableDatabasesReq = map[int16]string{
	1: "InstanceId",
	2: "BackupId",
	3: "RestoreTime",
	4: "PageSize",
	5: "PageNumber",
}

func (p *DescribeRecoverableDatabasesReq) IsSetBackupId() bool {
	return p.BackupId != nil
}

func (p *DescribeRecoverableDatabasesReq) IsSetRestoreTime() bool {
	return p.RestoreTime != nil
}

func (p *DescribeRecoverableDatabasesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeRecoverableDatabasesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeRecoverableDatabasesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableDatabasesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableDatabasesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableDatabasesReq[fieldId]))
}

func (p *DescribeRecoverableDatabasesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeRecoverableDatabasesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BackupId = _field
	return nil
}
func (p *DescribeRecoverableDatabasesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RestoreTime = _field
	return nil
}
func (p *DescribeRecoverableDatabasesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeRecoverableDatabasesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}

func (p *DescribeRecoverableDatabasesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableDatabasesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableDatabasesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackupId() {
		if err = oprot.WriteFieldBegin("BackupId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BackupId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRestoreTime() {
		if err = oprot.WriteFieldBegin("RestoreTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RestoreTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableDatabasesReq(%+v)", *p)

}

func (p *DescribeRecoverableDatabasesReq) DeepEqual(ano *DescribeRecoverableDatabasesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BackupId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RestoreTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	return true
}

func (p *DescribeRecoverableDatabasesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecoverableDatabasesReq) Field2DeepEqual(src *string) bool {

	if p.BackupId == src {
		return true
	} else if p.BackupId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BackupId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecoverableDatabasesReq) Field3DeepEqual(src *string) bool {

	if p.RestoreTime == src {
		return true
	} else if p.RestoreTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RestoreTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecoverableDatabasesReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeRecoverableDatabasesReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}

type DescribeRecoverableDatabasesResp struct {
	Total            int32                    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	DatabasesDetails []*DatabasesDetailObject `thrift:"DatabasesDetails,2,required" frugal:"2,required,list<DatabasesDetailObject>" json:"DatabasesDetails"`
}

func NewDescribeRecoverableDatabasesResp() *DescribeRecoverableDatabasesResp {
	return &DescribeRecoverableDatabasesResp{}
}

func (p *DescribeRecoverableDatabasesResp) InitDefault() {
}

func (p *DescribeRecoverableDatabasesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeRecoverableDatabasesResp) GetDatabasesDetails() (v []*DatabasesDetailObject) {
	return p.DatabasesDetails
}
func (p *DescribeRecoverableDatabasesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeRecoverableDatabasesResp) SetDatabasesDetails(val []*DatabasesDetailObject) {
	p.DatabasesDetails = val
}

var fieldIDToName_DescribeRecoverableDatabasesResp = map[int16]string{
	1: "Total",
	2: "DatabasesDetails",
}

func (p *DescribeRecoverableDatabasesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableDatabasesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatabasesDetails bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabasesDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabasesDetails {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecoverableDatabasesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecoverableDatabasesResp[fieldId]))
}

func (p *DescribeRecoverableDatabasesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeRecoverableDatabasesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DatabasesDetailObject, 0, size)
	values := make([]DatabasesDetailObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DatabasesDetails = _field
	return nil
}

func (p *DescribeRecoverableDatabasesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecoverableDatabasesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecoverableDatabasesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabasesDetails", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DatabasesDetails)); err != nil {
		return err
	}
	for _, v := range p.DatabasesDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeRecoverableDatabasesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecoverableDatabasesResp(%+v)", *p)

}

func (p *DescribeRecoverableDatabasesResp) DeepEqual(ano *DescribeRecoverableDatabasesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatabasesDetails) {
		return false
	}
	return true
}

func (p *DescribeRecoverableDatabasesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeRecoverableDatabasesResp) Field2DeepEqual(src []*DatabasesDetailObject) bool {

	if len(p.DatabasesDetails) != len(src) {
		return false
	}
	for i, v := range p.DatabasesDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

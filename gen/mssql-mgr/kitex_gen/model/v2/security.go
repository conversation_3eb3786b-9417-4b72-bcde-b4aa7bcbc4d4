// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package v2

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

const (
	TLS_V10 = "1.0"

	TLS_V11 = "1.1"

	TLS_V12 = "1.2"

	TLS_V13 = "1.3"
)

var (
	TLS_MIN_V10 = []string{
		TLS_V12,
		TLS_V11,
		TLS_V10,
	}

	TLS_MIN_V11 = []string{
		TLS_V12,
		TLS_V11,
	}

	TLS_MIN_V12 = []string{
		TLS_V12,
	}

	TLS_MIN_V13 = []string{
		TLS_V13,
		TLS_V12,
	}
)

type CertCategory int64

const (
	CertCategory_WinRM_HTTPS  CertCategory = 0
	CertCategory_Volc_SSL     CertCategory = 1
	CertCategory_Customer_SSL CertCategory = 2
)

func (p CertCategory) String() string {
	switch p {
	case CertCategory_WinRM_HTTPS:
		return "WinRM_HTTPS"
	case CertCategory_Volc_SSL:
		return "Volc_SSL"
	case CertCategory_Customer_SSL:
		return "Customer_SSL"
	}
	return "<UNSET>"
}

func CertCategoryFromString(s string) (CertCategory, error) {
	switch s {
	case "WinRM_HTTPS":
		return CertCategory_WinRM_HTTPS, nil
	case "Volc_SSL":
		return CertCategory_Volc_SSL, nil
	case "Customer_SSL":
		return CertCategory_Customer_SSL, nil
	}
	return CertCategory(0), fmt.Errorf("not a valid CertCategory string")
}

func CertCategoryPtr(v CertCategory) *CertCategory { return &v }

func (p CertCategory) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CertCategory) UnmarshalText(text []byte) error {
	q, err := CertCategoryFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SSLType int64

const (
	SSLType_Enable  SSLType = 0
	SSLType_Disable SSLType = 1
	SSLType_Renew   SSLType = 2
	SSLType_Change  SSLType = 3
)

func (p SSLType) String() string {
	switch p {
	case SSLType_Enable:
		return "Enable"
	case SSLType_Disable:
		return "Disable"
	case SSLType_Renew:
		return "Renew"
	case SSLType_Change:
		return "Change"
	}
	return "<UNSET>"
}

func SSLTypeFromString(s string) (SSLType, error) {
	switch s {
	case "Enable":
		return SSLType_Enable, nil
	case "Disable":
		return SSLType_Disable, nil
	case "Renew":
		return SSLType_Renew, nil
	case "Change":
		return SSLType_Change, nil
	}
	return SSLType(0), fmt.Errorf("not a valid SSLType string")
}

func SSLTypePtr(v SSLType) *SSLType { return &v }

func (p SSLType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SSLType) UnmarshalText(text []byte) error {
	q, err := SSLTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AllowListBindMode int64

const (
	AllowListBindMode_AssociateEcsIp     AllowListBindMode = 0
	AllowListBindMode_IngressDirectionIp AllowListBindMode = 1
	AllowListBindMode_Manual             AllowListBindMode = 2
)

func (p AllowListBindMode) String() string {
	switch p {
	case AllowListBindMode_AssociateEcsIp:
		return "AssociateEcsIp"
	case AllowListBindMode_IngressDirectionIp:
		return "IngressDirectionIp"
	case AllowListBindMode_Manual:
		return "Manual"
	}
	return "<UNSET>"
}

func AllowListBindModeFromString(s string) (AllowListBindMode, error) {
	switch s {
	case "AssociateEcsIp":
		return AllowListBindMode_AssociateEcsIp, nil
	case "IngressDirectionIp":
		return AllowListBindMode_IngressDirectionIp, nil
	case "Manual":
		return AllowListBindMode_Manual, nil
	}
	return AllowListBindMode(0), fmt.Errorf("not a valid AllowListBindMode string")
}

func AllowListBindModePtr(v AllowListBindMode) *AllowListBindMode { return &v }

func (p AllowListBindMode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AllowListBindMode) UnmarshalText(text []byte) error {
	q, err := AllowListBindModeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TDEMode int64

const (
	TDEMode_MSSQL_Default TDEMode = 0
	TDEMode_User_Default  TDEMode = 1
	TDEMode_Volc_KMS      TDEMode = 2
	TDEMode_User_KMS      TDEMode = 3
)

func (p TDEMode) String() string {
	switch p {
	case TDEMode_MSSQL_Default:
		return "MSSQL_Default"
	case TDEMode_User_Default:
		return "User_Default"
	case TDEMode_Volc_KMS:
		return "Volc_KMS"
	case TDEMode_User_KMS:
		return "User_KMS"
	}
	return "<UNSET>"
}

func TDEModeFromString(s string) (TDEMode, error) {
	switch s {
	case "MSSQL_Default":
		return TDEMode_MSSQL_Default, nil
	case "User_Default":
		return TDEMode_User_Default, nil
	case "Volc_KMS":
		return TDEMode_Volc_KMS, nil
	case "User_KMS":
		return TDEMode_User_KMS, nil
	}
	return TDEMode(0), fmt.Errorf("not a valid TDEMode string")
}

func TDEModePtr(v TDEMode) *TDEMode { return &v }

func (p TDEMode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TDEMode) UnmarshalText(text []byte) error {
	q, err := TDEModeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ModifyDBInstanceSSLReq struct {
	InstanceId      string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json-check:"required"`
	Type            SSLType `thrift:"Type,2,required" frugal:"2,required,SSLType" json:"Type"`
	ForceEncryption *bool   `thrift:"ForceEncryption,3,optional" frugal:"3,optional,bool" json:"ForceEncryption,omitempty"`
	TLSVersion      *string `thrift:"TLSVersion,4,optional" frugal:"4,optional,string" json:"TLSVersion,omitempty"`
	ModifyType      *string `thrift:"ModifyType,5,optional" frugal:"5,optional,string" json:"ModifyType,omitempty"`
	ModifyTime      *string `thrift:"ModifyTime,6,optional" frugal:"6,optional,string" json:"ModifyTime,omitempty"`
}

func NewModifyDBInstanceSSLReq() *ModifyDBInstanceSSLReq {
	return &ModifyDBInstanceSSLReq{}
}

func (p *ModifyDBInstanceSSLReq) InitDefault() {
}

func (p *ModifyDBInstanceSSLReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceSSLReq) GetType() (v SSLType) {
	return p.Type
}

var ModifyDBInstanceSSLReq_ForceEncryption_DEFAULT bool

func (p *ModifyDBInstanceSSLReq) GetForceEncryption() (v bool) {
	if !p.IsSetForceEncryption() {
		return ModifyDBInstanceSSLReq_ForceEncryption_DEFAULT
	}
	return *p.ForceEncryption
}

var ModifyDBInstanceSSLReq_TLSVersion_DEFAULT string

func (p *ModifyDBInstanceSSLReq) GetTLSVersion() (v string) {
	if !p.IsSetTLSVersion() {
		return ModifyDBInstanceSSLReq_TLSVersion_DEFAULT
	}
	return *p.TLSVersion
}

var ModifyDBInstanceSSLReq_ModifyType_DEFAULT string

func (p *ModifyDBInstanceSSLReq) GetModifyType() (v string) {
	if !p.IsSetModifyType() {
		return ModifyDBInstanceSSLReq_ModifyType_DEFAULT
	}
	return *p.ModifyType
}

var ModifyDBInstanceSSLReq_ModifyTime_DEFAULT string

func (p *ModifyDBInstanceSSLReq) GetModifyTime() (v string) {
	if !p.IsSetModifyTime() {
		return ModifyDBInstanceSSLReq_ModifyTime_DEFAULT
	}
	return *p.ModifyTime
}
func (p *ModifyDBInstanceSSLReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceSSLReq) SetType(val SSLType) {
	p.Type = val
}
func (p *ModifyDBInstanceSSLReq) SetForceEncryption(val *bool) {
	p.ForceEncryption = val
}
func (p *ModifyDBInstanceSSLReq) SetTLSVersion(val *string) {
	p.TLSVersion = val
}
func (p *ModifyDBInstanceSSLReq) SetModifyType(val *string) {
	p.ModifyType = val
}
func (p *ModifyDBInstanceSSLReq) SetModifyTime(val *string) {
	p.ModifyTime = val
}

var fieldIDToName_ModifyDBInstanceSSLReq = map[int16]string{
	1: "InstanceId",
	2: "Type",
	3: "ForceEncryption",
	4: "TLSVersion",
	5: "ModifyType",
	6: "ModifyTime",
}

func (p *ModifyDBInstanceSSLReq) IsSetForceEncryption() bool {
	return p.ForceEncryption != nil
}

func (p *ModifyDBInstanceSSLReq) IsSetTLSVersion() bool {
	return p.TLSVersion != nil
}

func (p *ModifyDBInstanceSSLReq) IsSetModifyType() bool {
	return p.ModifyType != nil
}

func (p *ModifyDBInstanceSSLReq) IsSetModifyTime() bool {
	return p.ModifyTime != nil
}

func (p *ModifyDBInstanceSSLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSSLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceSSLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceSSLReq[fieldId]))
}

func (p *ModifyDBInstanceSSLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceSSLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field SSLType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SSLType(v)
	}
	p.Type = _field
	return nil
}
func (p *ModifyDBInstanceSSLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ForceEncryption = _field
	return nil
}
func (p *ModifyDBInstanceSSLReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TLSVersion = _field
	return nil
}
func (p *ModifyDBInstanceSSLReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModifyType = _field
	return nil
}
func (p *ModifyDBInstanceSSLReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModifyTime = _field
	return nil
}

func (p *ModifyDBInstanceSSLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSSLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceSSLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetForceEncryption() {
		if err = oprot.WriteFieldBegin("ForceEncryption", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ForceEncryption); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTLSVersion() {
		if err = oprot.WriteFieldBegin("TLSVersion", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TLSVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetModifyType() {
		if err = oprot.WriteFieldBegin("ModifyType", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ModifyType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetModifyTime() {
		if err = oprot.WriteFieldBegin("ModifyTime", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ModifyTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyDBInstanceSSLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSSLReq(%+v)", *p)

}

func (p *ModifyDBInstanceSSLReq) DeepEqual(ano *ModifyDBInstanceSSLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Type) {
		return false
	}
	if !p.Field3DeepEqual(ano.ForceEncryption) {
		return false
	}
	if !p.Field4DeepEqual(ano.TLSVersion) {
		return false
	}
	if !p.Field5DeepEqual(ano.ModifyType) {
		return false
	}
	if !p.Field6DeepEqual(ano.ModifyTime) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceSSLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSSLReq) Field2DeepEqual(src SSLType) bool {

	if p.Type != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSSLReq) Field3DeepEqual(src *bool) bool {

	if p.ForceEncryption == src {
		return true
	} else if p.ForceEncryption == nil || src == nil {
		return false
	}
	if *p.ForceEncryption != *src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSSLReq) Field4DeepEqual(src *string) bool {

	if p.TLSVersion == src {
		return true
	} else if p.TLSVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TLSVersion, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSSLReq) Field5DeepEqual(src *string) bool {

	if p.ModifyType == src {
		return true
	} else if p.ModifyType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ModifyType, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceSSLReq) Field6DeepEqual(src *string) bool {

	if p.ModifyTime == src {
		return true
	} else if p.ModifyTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ModifyTime, *src) != 0 {
		return false
	}
	return true
}

type ModifyDBInstanceSSLResp struct {
}

func NewModifyDBInstanceSSLResp() *ModifyDBInstanceSSLResp {
	return &ModifyDBInstanceSSLResp{}
}

func (p *ModifyDBInstanceSSLResp) InitDefault() {
}

var fieldIDToName_ModifyDBInstanceSSLResp = map[int16]string{}

func (p *ModifyDBInstanceSSLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSSLResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBInstanceSSLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceSSLResp")

	if err = oprot.WriteStructBegin("ModifyDBInstanceSSLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceSSLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceSSLResp(%+v)", *p)

}

func (p *ModifyDBInstanceSSLResp) DeepEqual(ano *ModifyDBInstanceSSLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeDBInstanceSSLReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json-check:"required"`
}

func NewDescribeDBInstanceSSLReq() *DescribeDBInstanceSSLReq {
	return &DescribeDBInstanceSSLReq{}
}

func (p *DescribeDBInstanceSSLReq) InitDefault() {
}

func (p *DescribeDBInstanceSSLReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBInstanceSSLReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBInstanceSSLReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBInstanceSSLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceSSLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceSSLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceSSLReq[fieldId]))
}

func (p *DescribeDBInstanceSSLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBInstanceSSLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceSSLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceSSLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceSSLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceSSLReq(%+v)", *p)

}

func (p *DescribeDBInstanceSSLReq) DeepEqual(ano *DescribeDBInstanceSSLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceSSLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceSSLResp struct {
	InstanceId      string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	SSLEnable       bool   `thrift:"SSLEnable,2,required" frugal:"2,required,bool" json:"SSLEnable"`
	IsValid         bool   `thrift:"IsValid,3,required" frugal:"3,required,bool" json:"IsValid"`
	SSLExpireTime   string `thrift:"SSLExpireTime,4,required" frugal:"4,required,string" json:"SSLExpireTime"`
	TLSVersion      string `thrift:"TLSVersion,5,required" frugal:"5,required,string" json:"TLSVersion"`
	Address         string `thrift:"Address,6,required" frugal:"6,required,string" json:"Address"`
	RequireUpdate   bool   `thrift:"RequireUpdate,7,required" frugal:"7,required,bool" json:"RequireUpdate"`
	ForceEncryption bool   `thrift:"ForceEncryption,8,required" frugal:"8,required,bool" json:"ForceEncryption"`
}

func NewDescribeDBInstanceSSLResp() *DescribeDBInstanceSSLResp {
	return &DescribeDBInstanceSSLResp{}
}

func (p *DescribeDBInstanceSSLResp) InitDefault() {
}

func (p *DescribeDBInstanceSSLResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBInstanceSSLResp) GetSSLEnable() (v bool) {
	return p.SSLEnable
}

func (p *DescribeDBInstanceSSLResp) GetIsValid() (v bool) {
	return p.IsValid
}

func (p *DescribeDBInstanceSSLResp) GetSSLExpireTime() (v string) {
	return p.SSLExpireTime
}

func (p *DescribeDBInstanceSSLResp) GetTLSVersion() (v string) {
	return p.TLSVersion
}

func (p *DescribeDBInstanceSSLResp) GetAddress() (v string) {
	return p.Address
}

func (p *DescribeDBInstanceSSLResp) GetRequireUpdate() (v bool) {
	return p.RequireUpdate
}

func (p *DescribeDBInstanceSSLResp) GetForceEncryption() (v bool) {
	return p.ForceEncryption
}
func (p *DescribeDBInstanceSSLResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceSSLResp) SetSSLEnable(val bool) {
	p.SSLEnable = val
}
func (p *DescribeDBInstanceSSLResp) SetIsValid(val bool) {
	p.IsValid = val
}
func (p *DescribeDBInstanceSSLResp) SetSSLExpireTime(val string) {
	p.SSLExpireTime = val
}
func (p *DescribeDBInstanceSSLResp) SetTLSVersion(val string) {
	p.TLSVersion = val
}
func (p *DescribeDBInstanceSSLResp) SetAddress(val string) {
	p.Address = val
}
func (p *DescribeDBInstanceSSLResp) SetRequireUpdate(val bool) {
	p.RequireUpdate = val
}
func (p *DescribeDBInstanceSSLResp) SetForceEncryption(val bool) {
	p.ForceEncryption = val
}

var fieldIDToName_DescribeDBInstanceSSLResp = map[int16]string{
	1: "InstanceId",
	2: "SSLEnable",
	3: "IsValid",
	4: "SSLExpireTime",
	5: "TLSVersion",
	6: "Address",
	7: "RequireUpdate",
	8: "ForceEncryption",
}

func (p *DescribeDBInstanceSSLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceSSLResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetSSLEnable bool = false
	var issetIsValid bool = false
	var issetSSLExpireTime bool = false
	var issetTLSVersion bool = false
	var issetAddress bool = false
	var issetRequireUpdate bool = false
	var issetForceEncryption bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSSLEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsValid = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetSSLExpireTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAddress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetRequireUpdate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetForceEncryption = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSSLEnable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIsValid {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetSSLExpireTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTLSVersion {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAddress {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRequireUpdate {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetForceEncryption {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceSSLResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceSSLResp[fieldId]))
}

func (p *DescribeDBInstanceSSLResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SSLEnable = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsValid = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SSLExpireTime = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSVersion = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Address = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField7(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RequireUpdate = _field
	return nil
}
func (p *DescribeDBInstanceSSLResp) ReadField8(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ForceEncryption = _field
	return nil
}

func (p *DescribeDBInstanceSSLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceSSLResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceSSLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SSLEnable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.SSLEnable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsValid", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsValid); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SSLExpireTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SSLExpireTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSVersion", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Address", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Address); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RequireUpdate", thrift.BOOL, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.RequireUpdate); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ForceEncryption", thrift.BOOL, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ForceEncryption); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBInstanceSSLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceSSLResp(%+v)", *p)

}

func (p *DescribeDBInstanceSSLResp) DeepEqual(ano *DescribeDBInstanceSSLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SSLEnable) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsValid) {
		return false
	}
	if !p.Field4DeepEqual(ano.SSLExpireTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.TLSVersion) {
		return false
	}
	if !p.Field6DeepEqual(ano.Address) {
		return false
	}
	if !p.Field7DeepEqual(ano.RequireUpdate) {
		return false
	}
	if !p.Field8DeepEqual(ano.ForceEncryption) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceSSLResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field2DeepEqual(src bool) bool {

	if p.SSLEnable != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field3DeepEqual(src bool) bool {

	if p.IsValid != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.SSLExpireTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.TLSVersion, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Address, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field7DeepEqual(src bool) bool {

	if p.RequireUpdate != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceSSLResp) Field8DeepEqual(src bool) bool {

	if p.ForceEncryption != src {
		return false
	}
	return true
}

type DownloadSSLCertificateReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json-check:"required"`
}

func NewDownloadSSLCertificateReq() *DownloadSSLCertificateReq {
	return &DownloadSSLCertificateReq{}
}

func (p *DownloadSSLCertificateReq) InitDefault() {
}

func (p *DownloadSSLCertificateReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DownloadSSLCertificateReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DownloadSSLCertificateReq = map[int16]string{
	1: "InstanceId",
}

func (p *DownloadSSLCertificateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadSSLCertificateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadSSLCertificateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadSSLCertificateReq[fieldId]))
}

func (p *DownloadSSLCertificateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DownloadSSLCertificateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadSSLCertificateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadSSLCertificateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadSSLCertificateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DownloadSSLCertificateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadSSLCertificateReq(%+v)", *p)

}

func (p *DownloadSSLCertificateReq) DeepEqual(ano *DownloadSSLCertificateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DownloadSSLCertificateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DownloadSSLCertificateResp struct {
	Certificate []int8 `thrift:"Certificate,1,required" frugal:"1,required,list<byte>" json:"Certificate"`
}

func NewDownloadSSLCertificateResp() *DownloadSSLCertificateResp {
	return &DownloadSSLCertificateResp{}
}

func (p *DownloadSSLCertificateResp) InitDefault() {
}

func (p *DownloadSSLCertificateResp) GetCertificate() (v []int8) {
	return p.Certificate
}
func (p *DownloadSSLCertificateResp) SetCertificate(val []int8) {
	p.Certificate = val
}

var fieldIDToName_DownloadSSLCertificateResp = map[int16]string{
	1: "Certificate",
}

func (p *DownloadSSLCertificateResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadSSLCertificateResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCertificate bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCertificate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCertificate {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadSSLCertificateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadSSLCertificateResp[fieldId]))
}

func (p *DownloadSSLCertificateResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int8, 0, size)
	for i := 0; i < size; i++ {

		var _elem int8
		if v, err := iprot.ReadByte(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Certificate = _field
	return nil
}

func (p *DownloadSSLCertificateResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DownloadSSLCertificateResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadSSLCertificateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadSSLCertificateResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Certificate", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.BYTE, len(p.Certificate)); err != nil {
		return err
	}
	for _, v := range p.Certificate {
		if err := oprot.WriteByte(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DownloadSSLCertificateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadSSLCertificateResp(%+v)", *p)

}

func (p *DownloadSSLCertificateResp) DeepEqual(ano *DownloadSSLCertificateResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Certificate) {
		return false
	}
	return true
}

func (p *DownloadSSLCertificateResp) Field1DeepEqual(src []int8) bool {

	if len(p.Certificate) != len(src) {
		return false
	}
	for i, v := range p.Certificate {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}

type UpgradeAllowListVersionReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewUpgradeAllowListVersionReq() *UpgradeAllowListVersionReq {
	return &UpgradeAllowListVersionReq{}
}

func (p *UpgradeAllowListVersionReq) InitDefault() {
}

func (p *UpgradeAllowListVersionReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *UpgradeAllowListVersionReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_UpgradeAllowListVersionReq = map[int16]string{
	1: "InstanceId",
}

func (p *UpgradeAllowListVersionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpgradeAllowListVersionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpgradeAllowListVersionReq[fieldId]))
}

func (p *UpgradeAllowListVersionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *UpgradeAllowListVersionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpgradeAllowListVersionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpgradeAllowListVersionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpgradeAllowListVersionReq(%+v)", *p)

}

func (p *UpgradeAllowListVersionReq) DeepEqual(ano *UpgradeAllowListVersionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *UpgradeAllowListVersionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type UpgradeAllowListVersionResp struct {
}

func NewUpgradeAllowListVersionResp() *UpgradeAllowListVersionResp {
	return &UpgradeAllowListVersionResp{}
}

func (p *UpgradeAllowListVersionResp) InitDefault() {
}

var fieldIDToName_UpgradeAllowListVersionResp = map[int16]string{}

func (p *UpgradeAllowListVersionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpgradeAllowListVersionResp")

	if err = oprot.WriteStructBegin("UpgradeAllowListVersionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpgradeAllowListVersionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpgradeAllowListVersionResp(%+v)", *p)

}

func (p *UpgradeAllowListVersionResp) DeepEqual(ano *UpgradeAllowListVersionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeAllowListsReq struct {
	RegionId    string  `thrift:"RegionId,1,required" frugal:"1,required,string" validate:"required"`
	InstanceId  *string `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	ProjectName *string `thrift:"ProjectName,3,optional" frugal:"3,optional,string" json:"ProjectName,omitempty"`
	PageNumber  *int64  `thrift:"PageNumber,4,optional" frugal:"4,optional,i64" json:"PageNumber,omitempty"`
	PageSize    *int64  `thrift:"PageSize,5,optional" frugal:"5,optional,i64" json:"PageSize,omitempty"`
}

func NewDescribeAllowListsReq() *DescribeAllowListsReq {
	return &DescribeAllowListsReq{}
}

func (p *DescribeAllowListsReq) InitDefault() {
}

func (p *DescribeAllowListsReq) GetRegionId() (v string) {
	return p.RegionId
}

var DescribeAllowListsReq_InstanceId_DEFAULT string

func (p *DescribeAllowListsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeAllowListsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeAllowListsReq_ProjectName_DEFAULT string

func (p *DescribeAllowListsReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeAllowListsReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var DescribeAllowListsReq_PageNumber_DEFAULT int64

func (p *DescribeAllowListsReq) GetPageNumber() (v int64) {
	if !p.IsSetPageNumber() {
		return DescribeAllowListsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeAllowListsReq_PageSize_DEFAULT int64

func (p *DescribeAllowListsReq) GetPageSize() (v int64) {
	if !p.IsSetPageSize() {
		return DescribeAllowListsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeAllowListsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAllowListsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeAllowListsReq) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *DescribeAllowListsReq) SetPageNumber(val *int64) {
	p.PageNumber = val
}
func (p *DescribeAllowListsReq) SetPageSize(val *int64) {
	p.PageSize = val
}

var fieldIDToName_DescribeAllowListsReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "ProjectName",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeAllowListsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeAllowListsReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeAllowListsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeAllowListsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeAllowListsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListsReq[fieldId]))
}

func (p *DescribeAllowListsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAllowListsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAllowListsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *DescribeAllowListsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeAllowListsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeAllowListsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAllowListsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAllowListsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListsReq(%+v)", *p)

}

func (p *DescribeAllowListsReq) DeepEqual(ano *DescribeAllowListsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeAllowListsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListsReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListsReq) Field3DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListsReq) Field4DeepEqual(src *int64) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeAllowListsReq) Field5DeepEqual(src *int64) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type AllowListObject struct {
	AllowListId            string                   `thrift:"AllowListId,1" frugal:"1,default,string" json:"AllowListId"`
	AllowListName          string                   `thrift:"AllowListName,2" frugal:"2,default,string" json:"AllowListName"`
	AllowListDesc          string                   `thrift:"AllowListDesc,3" frugal:"3,default,string" json:"AllowListDesc"`
	AllowListType          string                   `thrift:"AllowListType,4" frugal:"4,default,string" json:"AllowListType"`
	AllowListIPNum         int32                    `thrift:"AllowListIPNum,5" frugal:"5,default,i32" json:"AllowListIPNum"`
	AssociatedInstanceNum  int32                    `thrift:"AssociatedInstanceNum,6" frugal:"6,default,i32" json:"AssociatedInstanceNum"`
	AssociatedLBNum        int32                    `thrift:"AssociatedLBNum,7" frugal:"7,default,i32" json:"AssociatedLBNum"`
	ProjectName            *string                  `thrift:"ProjectName,8,optional" frugal:"8,optional,string" json:"ProjectName,omitempty"`
	AllowListCategory      AllowListCategory        `thrift:"AllowListCategory,9" frugal:"9,default,AllowListCategory" json:"AllowListCategory"`
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,10" frugal:"10,default,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos"`
	CreateTime             *string                  `thrift:"CreateTime,11,optional" frugal:"11,optional,string" json:"CreateTime,omitempty"`
}

func NewAllowListObject() *AllowListObject {
	return &AllowListObject{}
}

func (p *AllowListObject) InitDefault() {
}

func (p *AllowListObject) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *AllowListObject) GetAllowListName() (v string) {
	return p.AllowListName
}

func (p *AllowListObject) GetAllowListDesc() (v string) {
	return p.AllowListDesc
}

func (p *AllowListObject) GetAllowListType() (v string) {
	return p.AllowListType
}

func (p *AllowListObject) GetAllowListIPNum() (v int32) {
	return p.AllowListIPNum
}

func (p *AllowListObject) GetAssociatedInstanceNum() (v int32) {
	return p.AssociatedInstanceNum
}

func (p *AllowListObject) GetAssociatedLBNum() (v int32) {
	return p.AssociatedLBNum
}

var AllowListObject_ProjectName_DEFAULT string

func (p *AllowListObject) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return AllowListObject_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

func (p *AllowListObject) GetAllowListCategory() (v AllowListCategory) {
	return p.AllowListCategory
}

func (p *AllowListObject) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	return p.SecurityGroupBindInfos
}

var AllowListObject_CreateTime_DEFAULT string

func (p *AllowListObject) GetCreateTime() (v string) {
	if !p.IsSetCreateTime() {
		return AllowListObject_CreateTime_DEFAULT
	}
	return *p.CreateTime
}
func (p *AllowListObject) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *AllowListObject) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *AllowListObject) SetAllowListDesc(val string) {
	p.AllowListDesc = val
}
func (p *AllowListObject) SetAllowListType(val string) {
	p.AllowListType = val
}
func (p *AllowListObject) SetAllowListIPNum(val int32) {
	p.AllowListIPNum = val
}
func (p *AllowListObject) SetAssociatedInstanceNum(val int32) {
	p.AssociatedInstanceNum = val
}
func (p *AllowListObject) SetAssociatedLBNum(val int32) {
	p.AssociatedLBNum = val
}
func (p *AllowListObject) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *AllowListObject) SetAllowListCategory(val AllowListCategory) {
	p.AllowListCategory = val
}
func (p *AllowListObject) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}
func (p *AllowListObject) SetCreateTime(val *string) {
	p.CreateTime = val
}

var fieldIDToName_AllowListObject = map[int16]string{
	1:  "AllowListId",
	2:  "AllowListName",
	3:  "AllowListDesc",
	4:  "AllowListType",
	5:  "AllowListIPNum",
	6:  "AssociatedInstanceNum",
	7:  "AssociatedLBNum",
	8:  "ProjectName",
	9:  "AllowListCategory",
	10: "SecurityGroupBindInfos",
	11: "CreateTime",
}

func (p *AllowListObject) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *AllowListObject) IsSetCreateTime() bool {
	return p.CreateTime != nil
}

func (p *AllowListObject) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllowListObject")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AllowListObject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AllowListObject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *AllowListObject) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *AllowListObject) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *AllowListObject) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListType = _field
	return nil
}
func (p *AllowListObject) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListIPNum = _field
	return nil
}
func (p *AllowListObject) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AssociatedInstanceNum = _field
	return nil
}
func (p *AllowListObject) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AssociatedLBNum = _field
	return nil
}
func (p *AllowListObject) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *AllowListObject) ReadField9(iprot thrift.TProtocol) error {

	var _field AllowListCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AllowListCategory(v)
	}
	p.AllowListCategory = _field
	return nil
}
func (p *AllowListObject) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}
func (p *AllowListObject) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTime = _field
	return nil
}

func (p *AllowListObject) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AllowListObject")

	var fieldId int16
	if err = oprot.WriteStructBegin("AllowListObject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AllowListObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AllowListObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AllowListObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AllowListObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AllowListObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIPNum", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AllowListIPNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AllowListObject) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AssociatedInstanceNum", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AssociatedInstanceNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AllowListObject) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AssociatedLBNum", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AssociatedLBNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AllowListObject) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AllowListObject) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListCategory", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AllowListCategory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *AllowListObject) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
		return err
	}
	for _, v := range p.SecurityGroupBindInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *AllowListObject) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTime() {
		if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *AllowListObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AllowListObject(%+v)", *p)

}

func (p *AllowListObject) DeepEqual(ano *AllowListObject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AllowListIPNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.AssociatedInstanceNum) {
		return false
	}
	if !p.Field7DeepEqual(ano.AssociatedLBNum) {
		return false
	}
	if !p.Field8DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field9DeepEqual(ano.AllowListCategory) {
		return false
	}
	if !p.Field10DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	if !p.Field11DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *AllowListObject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AllowListDesc, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AllowListType, src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field5DeepEqual(src int32) bool {

	if p.AllowListIPNum != src {
		return false
	}
	return true
}
func (p *AllowListObject) Field6DeepEqual(src int32) bool {

	if p.AssociatedInstanceNum != src {
		return false
	}
	return true
}
func (p *AllowListObject) Field7DeepEqual(src int32) bool {

	if p.AssociatedLBNum != src {
		return false
	}
	return true
}
func (p *AllowListObject) Field8DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *AllowListObject) Field9DeepEqual(src AllowListCategory) bool {

	if p.AllowListCategory != src {
		return false
	}
	return true
}
func (p *AllowListObject) Field10DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *AllowListObject) Field11DeepEqual(src *string) bool {

	if p.CreateTime == src {
		return true
	} else if p.CreateTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateTime, *src) != 0 {
		return false
	}
	return true
}

type DescribeAllowListsResp struct {
	AllowLists []*AllowListObject `thrift:"AllowLists,1,required" frugal:"1,required,list<AllowListObject>" json:"AllowLists"`
	Total      *int64             `thrift:"Total,2,optional" frugal:"2,optional,i64" json:"Total,omitempty"`
}

func NewDescribeAllowListsResp() *DescribeAllowListsResp {
	return &DescribeAllowListsResp{}
}

func (p *DescribeAllowListsResp) InitDefault() {
}

func (p *DescribeAllowListsResp) GetAllowLists() (v []*AllowListObject) {
	return p.AllowLists
}

var DescribeAllowListsResp_Total_DEFAULT int64

func (p *DescribeAllowListsResp) GetTotal() (v int64) {
	if !p.IsSetTotal() {
		return DescribeAllowListsResp_Total_DEFAULT
	}
	return *p.Total
}
func (p *DescribeAllowListsResp) SetAllowLists(val []*AllowListObject) {
	p.AllowLists = val
}
func (p *DescribeAllowListsResp) SetTotal(val *int64) {
	p.Total = val
}

var fieldIDToName_DescribeAllowListsResp = map[int16]string{
	1: "AllowLists",
	2: "Total",
}

func (p *DescribeAllowListsResp) IsSetTotal() bool {
	return p.Total != nil
}

func (p *DescribeAllowListsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowLists bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowLists = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowLists {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListsResp[fieldId]))
}

func (p *DescribeAllowListsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AllowListObject, 0, size)
	values := make([]AllowListObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowLists = _field
	return nil
}
func (p *DescribeAllowListsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Total = _field
	return nil
}

func (p *DescribeAllowListsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowLists", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AllowLists)); err != nil {
		return err
	}
	for _, v := range p.AllowLists {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTotal() {
		if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Total); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAllowListsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListsResp(%+v)", *p)

}

func (p *DescribeAllowListsResp) DeepEqual(ano *DescribeAllowListsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowLists) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeAllowListsResp) Field1DeepEqual(src []*AllowListObject) bool {

	if len(p.AllowLists) != len(src) {
		return false
	}
	for i, v := range p.AllowLists {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAllowListsResp) Field2DeepEqual(src *int64) bool {

	if p.Total == src {
		return true
	} else if p.Total == nil || src == nil {
		return false
	}
	if *p.Total != *src {
		return false
	}
	return true
}

type DescribeInstanceAllowListsReq struct {
	RegionId   string `thrift:"RegionId,1,required" frugal:"1,required,string" validate:"required"`
	InstanceId string `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
}

func NewDescribeInstanceAllowListsReq() *DescribeInstanceAllowListsReq {
	return &DescribeInstanceAllowListsReq{}
}

func (p *DescribeInstanceAllowListsReq) InitDefault() {
}

func (p *DescribeInstanceAllowListsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeInstanceAllowListsReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeInstanceAllowListsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeInstanceAllowListsReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeInstanceAllowListsReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
}

func (p *DescribeInstanceAllowListsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceAllowListsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceAllowListsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceAllowListsReq[fieldId]))
}

func (p *DescribeInstanceAllowListsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeInstanceAllowListsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeInstanceAllowListsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceAllowListsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceAllowListsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceAllowListsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceAllowListsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceAllowListsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceAllowListsReq(%+v)", *p)

}

func (p *DescribeInstanceAllowListsReq) DeepEqual(ano *DescribeInstanceAllowListsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeInstanceAllowListsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceAllowListsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceAllowListsResp struct {
	AuthorizedAllowLists     []*AllowListObject `thrift:"AuthorizedAllowLists,1,required" frugal:"1,required,list<AllowListObject>" json:"AuthorizedAllowLists"`
	UnauthorizedAllowListNum int64              `thrift:"UnauthorizedAllowListNum,2,required" frugal:"2,required,i64" json:"UnauthorizedAllowListNum"`
}

func NewDescribeInstanceAllowListsResp() *DescribeInstanceAllowListsResp {
	return &DescribeInstanceAllowListsResp{}
}

func (p *DescribeInstanceAllowListsResp) InitDefault() {
}

func (p *DescribeInstanceAllowListsResp) GetAuthorizedAllowLists() (v []*AllowListObject) {
	return p.AuthorizedAllowLists
}

func (p *DescribeInstanceAllowListsResp) GetUnauthorizedAllowListNum() (v int64) {
	return p.UnauthorizedAllowListNum
}
func (p *DescribeInstanceAllowListsResp) SetAuthorizedAllowLists(val []*AllowListObject) {
	p.AuthorizedAllowLists = val
}
func (p *DescribeInstanceAllowListsResp) SetUnauthorizedAllowListNum(val int64) {
	p.UnauthorizedAllowListNum = val
}

var fieldIDToName_DescribeInstanceAllowListsResp = map[int16]string{
	1: "AuthorizedAllowLists",
	2: "UnauthorizedAllowListNum",
}

func (p *DescribeInstanceAllowListsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceAllowListsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAuthorizedAllowLists bool = false
	var issetUnauthorizedAllowListNum bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAuthorizedAllowLists = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnauthorizedAllowListNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAuthorizedAllowLists {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUnauthorizedAllowListNum {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceAllowListsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceAllowListsResp[fieldId]))
}

func (p *DescribeInstanceAllowListsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AllowListObject, 0, size)
	values := make([]AllowListObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AuthorizedAllowLists = _field
	return nil
}
func (p *DescribeInstanceAllowListsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UnauthorizedAllowListNum = _field
	return nil
}

func (p *DescribeInstanceAllowListsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceAllowListsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceAllowListsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceAllowListsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AuthorizedAllowLists", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AuthorizedAllowLists)); err != nil {
		return err
	}
	for _, v := range p.AuthorizedAllowLists {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceAllowListsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UnauthorizedAllowListNum", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UnauthorizedAllowListNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceAllowListsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceAllowListsResp(%+v)", *p)

}

func (p *DescribeInstanceAllowListsResp) DeepEqual(ano *DescribeInstanceAllowListsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AuthorizedAllowLists) {
		return false
	}
	if !p.Field2DeepEqual(ano.UnauthorizedAllowListNum) {
		return false
	}
	return true
}

func (p *DescribeInstanceAllowListsResp) Field1DeepEqual(src []*AllowListObject) bool {

	if len(p.AuthorizedAllowLists) != len(src) {
		return false
	}
	for i, v := range p.AuthorizedAllowLists {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeInstanceAllowListsResp) Field2DeepEqual(src int64) bool {

	if p.UnauthorizedAllowListNum != src {
		return false
	}
	return true
}

type DescribeAllowListDetailReq struct {
	AllowListId string `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDescribeAllowListDetailReq() *DescribeAllowListDetailReq {
	return &DescribeAllowListDetailReq{}
}

func (p *DescribeAllowListDetailReq) InitDefault() {
}

func (p *DescribeAllowListDetailReq) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *DescribeAllowListDetailReq) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_DescribeAllowListDetailReq = map[int16]string{
	1: "AllowListId",
}

func (p *DescribeAllowListDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListDetailReq[fieldId]))
}

func (p *DescribeAllowListDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *DescribeAllowListDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListDetailReq(%+v)", *p)

}

func (p *DescribeAllowListDetailReq) DeepEqual(ano *DescribeAllowListDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *DescribeAllowListDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type AssociatedInstanceObject struct {
	InstanceId     string         `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	InstanceName   string         `thrift:"InstanceName,2" frugal:"2,default,string" json:"InstanceName"`
	VPC            string         `thrift:"VPC,3" frugal:"3,default,string" json:"VPC"`
	IsLatest       bool           `thrift:"IsLatest,4" frugal:"4,default,bool" json:"IsLatest"`
	InstanceStatus InstanceStatus `thrift:"InstanceStatus,5" frugal:"5,default,InstanceStatus" json:"InstanceStatus"`
	ProjectName    *string        `thrift:"ProjectName,6,optional" frugal:"6,optional,string" json:"ProjectName,omitempty"`
}

func NewAssociatedInstanceObject() *AssociatedInstanceObject {
	return &AssociatedInstanceObject{}
}

func (p *AssociatedInstanceObject) InitDefault() {
}

func (p *AssociatedInstanceObject) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AssociatedInstanceObject) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *AssociatedInstanceObject) GetVPC() (v string) {
	return p.VPC
}

func (p *AssociatedInstanceObject) GetIsLatest() (v bool) {
	return p.IsLatest
}

func (p *AssociatedInstanceObject) GetInstanceStatus() (v InstanceStatus) {
	return p.InstanceStatus
}

var AssociatedInstanceObject_ProjectName_DEFAULT string

func (p *AssociatedInstanceObject) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return AssociatedInstanceObject_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *AssociatedInstanceObject) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AssociatedInstanceObject) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *AssociatedInstanceObject) SetVPC(val string) {
	p.VPC = val
}
func (p *AssociatedInstanceObject) SetIsLatest(val bool) {
	p.IsLatest = val
}
func (p *AssociatedInstanceObject) SetInstanceStatus(val InstanceStatus) {
	p.InstanceStatus = val
}
func (p *AssociatedInstanceObject) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_AssociatedInstanceObject = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "VPC",
	4: "IsLatest",
	5: "InstanceStatus",
	6: "ProjectName",
}

func (p *AssociatedInstanceObject) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *AssociatedInstanceObject) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociatedInstanceObject")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssociatedInstanceObject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssociatedInstanceObject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VPC = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsLatest = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField5(iprot thrift.TProtocol) error {

	var _field InstanceStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceStatus(v)
	}
	p.InstanceStatus = _field
	return nil
}
func (p *AssociatedInstanceObject) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *AssociatedInstanceObject) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociatedInstanceObject")

	var fieldId int16
	if err = oprot.WriteStructBegin("AssociatedInstanceObject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VPC", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VPC); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsLatest", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsLatest); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceStatus", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AssociatedInstanceObject) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AssociatedInstanceObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociatedInstanceObject(%+v)", *p)

}

func (p *AssociatedInstanceObject) DeepEqual(ano *AssociatedInstanceObject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.VPC) {
		return false
	}
	if !p.Field4DeepEqual(ano.IsLatest) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceStatus) {
		return false
	}
	if !p.Field6DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *AssociatedInstanceObject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field3DeepEqual(src string) bool {

	if strings.Compare(p.VPC, src) != 0 {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field4DeepEqual(src bool) bool {

	if p.IsLatest != src {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field5DeepEqual(src InstanceStatus) bool {

	if p.InstanceStatus != src {
		return false
	}
	return true
}
func (p *AssociatedInstanceObject) Field6DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type DescribeAllowListDetailResp struct {
	AllowListId            string                      `thrift:"AllowListId,1,required" frugal:"1,required,string" json:"AllowListId"`
	AllowListName          string                      `thrift:"AllowListName,2,required" frugal:"2,required,string" json:"AllowListName"`
	AllowListDesc          string                      `thrift:"AllowListDesc,3,required" frugal:"3,required,string" json:"AllowListDesc"`
	AllowListType          string                      `thrift:"AllowListType,4,required" frugal:"4,required,string" json:"AllowListType"`
	AllowList              string                      `thrift:"AllowList,5,required" frugal:"5,required,string" json:"AllowList"`
	AssociatedInstances    []*AssociatedInstanceObject `thrift:"AssociatedInstances,6,required" frugal:"6,required,list<AssociatedInstanceObject>" json:"AssociatedInstances"`
	AllowListCategory      AllowListCategory           `thrift:"AllowListCategory,7,required" frugal:"7,required,AllowListCategory" json:"AllowListCategory"`
	SecurityGroupBindInfos []*SecurityGroupBindInfo    `thrift:"SecurityGroupBindInfos,8,optional" frugal:"8,optional,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos,omitempty"`
	UserAllowList          *string                     `thrift:"UserAllowList,9,optional" frugal:"9,optional,string" json:"UserAllowList,omitempty"`
	ProjectName            *string                     `thrift:"ProjectName,10,optional" frugal:"10,optional,string" json:"ProjectName,omitempty"`
	AssociatedInstanceNum  *int64                      `thrift:"AssociatedInstanceNum,11,optional" frugal:"11,optional,i64" json:"AssociatedInstanceNum,omitempty"`
}

func NewDescribeAllowListDetailResp() *DescribeAllowListDetailResp {
	return &DescribeAllowListDetailResp{}
}

func (p *DescribeAllowListDetailResp) InitDefault() {
}

func (p *DescribeAllowListDetailResp) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *DescribeAllowListDetailResp) GetAllowListName() (v string) {
	return p.AllowListName
}

func (p *DescribeAllowListDetailResp) GetAllowListDesc() (v string) {
	return p.AllowListDesc
}

func (p *DescribeAllowListDetailResp) GetAllowListType() (v string) {
	return p.AllowListType
}

func (p *DescribeAllowListDetailResp) GetAllowList() (v string) {
	return p.AllowList
}

func (p *DescribeAllowListDetailResp) GetAssociatedInstances() (v []*AssociatedInstanceObject) {
	return p.AssociatedInstances
}

func (p *DescribeAllowListDetailResp) GetAllowListCategory() (v AllowListCategory) {
	return p.AllowListCategory
}

var DescribeAllowListDetailResp_SecurityGroupBindInfos_DEFAULT []*SecurityGroupBindInfo

func (p *DescribeAllowListDetailResp) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	if !p.IsSetSecurityGroupBindInfos() {
		return DescribeAllowListDetailResp_SecurityGroupBindInfos_DEFAULT
	}
	return p.SecurityGroupBindInfos
}

var DescribeAllowListDetailResp_UserAllowList_DEFAULT string

func (p *DescribeAllowListDetailResp) GetUserAllowList() (v string) {
	if !p.IsSetUserAllowList() {
		return DescribeAllowListDetailResp_UserAllowList_DEFAULT
	}
	return *p.UserAllowList
}

var DescribeAllowListDetailResp_ProjectName_DEFAULT string

func (p *DescribeAllowListDetailResp) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return DescribeAllowListDetailResp_ProjectName_DEFAULT
	}
	return *p.ProjectName
}

var DescribeAllowListDetailResp_AssociatedInstanceNum_DEFAULT int64

func (p *DescribeAllowListDetailResp) GetAssociatedInstanceNum() (v int64) {
	if !p.IsSetAssociatedInstanceNum() {
		return DescribeAllowListDetailResp_AssociatedInstanceNum_DEFAULT
	}
	return *p.AssociatedInstanceNum
}
func (p *DescribeAllowListDetailResp) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *DescribeAllowListDetailResp) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *DescribeAllowListDetailResp) SetAllowListDesc(val string) {
	p.AllowListDesc = val
}
func (p *DescribeAllowListDetailResp) SetAllowListType(val string) {
	p.AllowListType = val
}
func (p *DescribeAllowListDetailResp) SetAllowList(val string) {
	p.AllowList = val
}
func (p *DescribeAllowListDetailResp) SetAssociatedInstances(val []*AssociatedInstanceObject) {
	p.AssociatedInstances = val
}
func (p *DescribeAllowListDetailResp) SetAllowListCategory(val AllowListCategory) {
	p.AllowListCategory = val
}
func (p *DescribeAllowListDetailResp) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}
func (p *DescribeAllowListDetailResp) SetUserAllowList(val *string) {
	p.UserAllowList = val
}
func (p *DescribeAllowListDetailResp) SetProjectName(val *string) {
	p.ProjectName = val
}
func (p *DescribeAllowListDetailResp) SetAssociatedInstanceNum(val *int64) {
	p.AssociatedInstanceNum = val
}

var fieldIDToName_DescribeAllowListDetailResp = map[int16]string{
	1:  "AllowListId",
	2:  "AllowListName",
	3:  "AllowListDesc",
	4:  "AllowListType",
	5:  "AllowList",
	6:  "AssociatedInstances",
	7:  "AllowListCategory",
	8:  "SecurityGroupBindInfos",
	9:  "UserAllowList",
	10: "ProjectName",
	11: "AssociatedInstanceNum",
}

func (p *DescribeAllowListDetailResp) IsSetSecurityGroupBindInfos() bool {
	return p.SecurityGroupBindInfos != nil
}

func (p *DescribeAllowListDetailResp) IsSetUserAllowList() bool {
	return p.UserAllowList != nil
}

func (p *DescribeAllowListDetailResp) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *DescribeAllowListDetailResp) IsSetAssociatedInstanceNum() bool {
	return p.AssociatedInstanceNum != nil
}

func (p *DescribeAllowListDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false
	var issetAllowListName bool = false
	var issetAllowListDesc bool = false
	var issetAllowListType bool = false
	var issetAllowList bool = false
	var issetAssociatedInstances bool = false
	var issetAllowListCategory bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListDesc = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAssociatedInstances = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListCategory = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAllowListDesc {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAllowListType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetAllowList {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAssociatedInstances {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetAllowListCategory {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAllowListDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAllowListDetailResp[fieldId]))
}

func (p *DescribeAllowListDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListType = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowList = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AssociatedInstanceObject, 0, size)
	values := make([]AssociatedInstanceObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AssociatedInstances = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField7(iprot thrift.TProtocol) error {

	var _field AllowListCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AllowListCategory(v)
	}
	p.AllowListCategory = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserAllowList = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}
func (p *DescribeAllowListDetailResp) ReadField11(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AssociatedInstanceNum = _field
	return nil
}

func (p *DescribeAllowListDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAllowListDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAllowListDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowList); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AssociatedInstances", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AssociatedInstances)); err != nil {
		return err
	}
	for _, v := range p.AssociatedInstances {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListCategory", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AllowListCategory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupBindInfos() {
		if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
			return err
		}
		for _, v := range p.SecurityGroupBindInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserAllowList() {
		if err = oprot.WriteFieldBegin("UserAllowList", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserAllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAssociatedInstanceNum() {
		if err = oprot.WriteFieldBegin("AssociatedInstanceNum", thrift.I64, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AssociatedInstanceNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeAllowListDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAllowListDetailResp(%+v)", *p)

}

func (p *DescribeAllowListDetailResp) DeepEqual(ano *DescribeAllowListDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AllowList) {
		return false
	}
	if !p.Field6DeepEqual(ano.AssociatedInstances) {
		return false
	}
	if !p.Field7DeepEqual(ano.AllowListCategory) {
		return false
	}
	if !p.Field8DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	if !p.Field9DeepEqual(ano.UserAllowList) {
		return false
	}
	if !p.Field10DeepEqual(ano.ProjectName) {
		return false
	}
	if !p.Field11DeepEqual(ano.AssociatedInstanceNum) {
		return false
	}
	return true
}

func (p *DescribeAllowListDetailResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AllowListDesc, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AllowListType, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.AllowList, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field6DeepEqual(src []*AssociatedInstanceObject) bool {

	if len(p.AssociatedInstances) != len(src) {
		return false
	}
	for i, v := range p.AssociatedInstances {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field7DeepEqual(src AllowListCategory) bool {

	if p.AllowListCategory != src {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field8DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field9DeepEqual(src *string) bool {

	if p.UserAllowList == src {
		return true
	} else if p.UserAllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserAllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field10DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAllowListDetailResp) Field11DeepEqual(src *int64) bool {

	if p.AssociatedInstanceNum == src {
		return true
	} else if p.AssociatedInstanceNum == nil || src == nil {
		return false
	}
	if *p.AssociatedInstanceNum != *src {
		return false
	}
	return true
}

type CreateAllowListReq struct {
	AllowListName          string                   `thrift:"AllowListName,1,required" frugal:"1,required,string" validate:"required"`
	AllowListDesc          *string                  `thrift:"AllowListDesc,2,optional" frugal:"2,optional,string" json:"AllowListDesc,omitempty"`
	AllowListType          string                   `thrift:"AllowListType,3,optional" frugal:"3,optional,string" json:"AllowListType,omitempty"`
	AllowList              *string                  `thrift:"AllowList,4,optional" frugal:"4,optional,string" json:"AllowList,omitempty"`
	AllowListCategory      *AllowListCategory       `thrift:"AllowListCategory,5,optional" frugal:"5,optional,AllowListCategory" json:"AllowListCategory,omitempty"`
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,6,optional" frugal:"6,optional,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos,omitempty"`
	UserAllowList          *string                  `thrift:"UserAllowList,7,optional" frugal:"7,optional,string" json:"UserAllowList,omitempty"`
	ProjectName            *string                  `thrift:"ProjectName,8,optional" frugal:"8,optional,string" json:"ProjectName,omitempty"`
}

func NewCreateAllowListReq() *CreateAllowListReq {
	return &CreateAllowListReq{

		AllowListType: "IPv4",
	}
}

func (p *CreateAllowListReq) InitDefault() {
	p.AllowListType = "IPv4"
}

func (p *CreateAllowListReq) GetAllowListName() (v string) {
	return p.AllowListName
}

var CreateAllowListReq_AllowListDesc_DEFAULT string

func (p *CreateAllowListReq) GetAllowListDesc() (v string) {
	if !p.IsSetAllowListDesc() {
		return CreateAllowListReq_AllowListDesc_DEFAULT
	}
	return *p.AllowListDesc
}

var CreateAllowListReq_AllowListType_DEFAULT string = "IPv4"

func (p *CreateAllowListReq) GetAllowListType() (v string) {
	if !p.IsSetAllowListType() {
		return CreateAllowListReq_AllowListType_DEFAULT
	}
	return p.AllowListType
}

var CreateAllowListReq_AllowList_DEFAULT string

func (p *CreateAllowListReq) GetAllowList() (v string) {
	if !p.IsSetAllowList() {
		return CreateAllowListReq_AllowList_DEFAULT
	}
	return *p.AllowList
}

var CreateAllowListReq_AllowListCategory_DEFAULT AllowListCategory

func (p *CreateAllowListReq) GetAllowListCategory() (v AllowListCategory) {
	if !p.IsSetAllowListCategory() {
		return CreateAllowListReq_AllowListCategory_DEFAULT
	}
	return *p.AllowListCategory
}

var CreateAllowListReq_SecurityGroupBindInfos_DEFAULT []*SecurityGroupBindInfo

func (p *CreateAllowListReq) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	if !p.IsSetSecurityGroupBindInfos() {
		return CreateAllowListReq_SecurityGroupBindInfos_DEFAULT
	}
	return p.SecurityGroupBindInfos
}

var CreateAllowListReq_UserAllowList_DEFAULT string

func (p *CreateAllowListReq) GetUserAllowList() (v string) {
	if !p.IsSetUserAllowList() {
		return CreateAllowListReq_UserAllowList_DEFAULT
	}
	return *p.UserAllowList
}

var CreateAllowListReq_ProjectName_DEFAULT string

func (p *CreateAllowListReq) GetProjectName() (v string) {
	if !p.IsSetProjectName() {
		return CreateAllowListReq_ProjectName_DEFAULT
	}
	return *p.ProjectName
}
func (p *CreateAllowListReq) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *CreateAllowListReq) SetAllowListDesc(val *string) {
	p.AllowListDesc = val
}
func (p *CreateAllowListReq) SetAllowListType(val string) {
	p.AllowListType = val
}
func (p *CreateAllowListReq) SetAllowList(val *string) {
	p.AllowList = val
}
func (p *CreateAllowListReq) SetAllowListCategory(val *AllowListCategory) {
	p.AllowListCategory = val
}
func (p *CreateAllowListReq) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}
func (p *CreateAllowListReq) SetUserAllowList(val *string) {
	p.UserAllowList = val
}
func (p *CreateAllowListReq) SetProjectName(val *string) {
	p.ProjectName = val
}

var fieldIDToName_CreateAllowListReq = map[int16]string{
	1: "AllowListName",
	2: "AllowListDesc",
	3: "AllowListType",
	4: "AllowList",
	5: "AllowListCategory",
	6: "SecurityGroupBindInfos",
	7: "UserAllowList",
	8: "ProjectName",
}

func (p *CreateAllowListReq) IsSetAllowListDesc() bool {
	return p.AllowListDesc != nil
}

func (p *CreateAllowListReq) IsSetAllowListType() bool {
	return p.AllowListType != CreateAllowListReq_AllowListType_DEFAULT
}

func (p *CreateAllowListReq) IsSetAllowList() bool {
	return p.AllowList != nil
}

func (p *CreateAllowListReq) IsSetAllowListCategory() bool {
	return p.AllowListCategory != nil
}

func (p *CreateAllowListReq) IsSetSecurityGroupBindInfos() bool {
	return p.SecurityGroupBindInfos != nil
}

func (p *CreateAllowListReq) IsSetUserAllowList() bool {
	return p.UserAllowList != nil
}

func (p *CreateAllowListReq) IsSetProjectName() bool {
	return p.ProjectName != nil
}

func (p *CreateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAllowListReq[fieldId]))
}

func (p *CreateAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *CreateAllowListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *CreateAllowListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListType = _field
	return nil
}
func (p *CreateAllowListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowList = _field
	return nil
}
func (p *CreateAllowListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *AllowListCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AllowListCategory(v)
		_field = &tmp
	}
	p.AllowListCategory = _field
	return nil
}
func (p *CreateAllowListReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}
func (p *CreateAllowListReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserAllowList = _field
	return nil
}
func (p *CreateAllowListReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectName = _field
	return nil
}

func (p *CreateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListDesc() {
		if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowListDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListType() {
		if err = oprot.WriteFieldBegin("AllowListType", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(p.AllowListType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowList() {
		if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListCategory() {
		if err = oprot.WriteFieldBegin("AllowListCategory", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AllowListCategory)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupBindInfos() {
		if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
			return err
		}
		for _, v := range p.SecurityGroupBindInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserAllowList() {
		if err = oprot.WriteFieldBegin("UserAllowList", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserAllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateAllowListReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectName() {
		if err = oprot.WriteFieldBegin("ProjectName", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAllowListReq(%+v)", *p)

}

func (p *CreateAllowListReq) DeepEqual(ano *CreateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListType) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowList) {
		return false
	}
	if !p.Field5DeepEqual(ano.AllowListCategory) {
		return false
	}
	if !p.Field6DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	if !p.Field7DeepEqual(ano.UserAllowList) {
		return false
	}
	if !p.Field8DeepEqual(ano.ProjectName) {
		return false
	}
	return true
}

func (p *CreateAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field2DeepEqual(src *string) bool {

	if p.AllowListDesc == src {
		return true
	} else if p.AllowListDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowListDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AllowListType, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field4DeepEqual(src *string) bool {

	if p.AllowList == src {
		return true
	} else if p.AllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field5DeepEqual(src *AllowListCategory) bool {

	if p.AllowListCategory == src {
		return true
	} else if p.AllowListCategory == nil || src == nil {
		return false
	}
	if *p.AllowListCategory != *src {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field6DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateAllowListReq) Field7DeepEqual(src *string) bool {

	if p.UserAllowList == src {
		return true
	} else if p.UserAllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserAllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateAllowListReq) Field8DeepEqual(src *string) bool {

	if p.ProjectName == src {
		return true
	} else if p.ProjectName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProjectName, *src) != 0 {
		return false
	}
	return true
}

type CreateAllowListResp struct {
	AllowListId string `thrift:"AllowListId,1,required" frugal:"1,required,string" json:"AllowListId"`
}

func NewCreateAllowListResp() *CreateAllowListResp {
	return &CreateAllowListResp{}
}

func (p *CreateAllowListResp) InitDefault() {
}

func (p *CreateAllowListResp) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *CreateAllowListResp) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_CreateAllowListResp = map[int16]string{
	1: "AllowListId",
}

func (p *CreateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAllowListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAllowListResp[fieldId]))
}

func (p *CreateAllowListResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *CreateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAllowListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAllowListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAllowListResp(%+v)", *p)

}

func (p *CreateAllowListResp) DeepEqual(ano *CreateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *CreateAllowListResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type ModifyAllowListReq struct {
	AllowListId            string                   `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
	AllowListName          string                   `thrift:"AllowListName,2,required" frugal:"2,required,string" validate:"required"`
	AllowListDesc          *string                  `thrift:"AllowListDesc,3,optional" frugal:"3,optional,string" json:"AllowListDesc,omitempty"`
	AllowList              *string                  `thrift:"AllowList,4,optional" frugal:"4,optional,string" json:"AllowList,omitempty"`
	ModifyMode             ModifyMode               `thrift:"ModifyMode,5,optional" frugal:"5,optional,ModifyMode" json:"ModifyMode,omitempty"`
	ApplyInstanceNum       *int32                   `thrift:"ApplyInstanceNum,6,optional" frugal:"6,optional,i32" json:"ApplyInstanceNum,omitempty"`
	AllowListCategory      *AllowListCategory       `thrift:"AllowListCategory,7,optional" frugal:"7,optional,AllowListCategory" json:"AllowListCategory,omitempty"`
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,8,optional" frugal:"8,optional,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos,omitempty"`
	UpdateSecurityGroup    *bool                    `thrift:"UpdateSecurityGroup,9,optional" frugal:"9,optional,bool" json:"UpdateSecurityGroup,omitempty"`
	UserAllowList          *string                  `thrift:"UserAllowList,10,optional" frugal:"10,optional,string" json:"UserAllowList,omitempty"`
	IgnoreInstanceStatus   *bool                    `thrift:"IgnoreInstanceStatus,11,optional" frugal:"11,optional,bool" json:"IgnoreInstanceStatus,omitempty"`
}

func NewModifyAllowListReq() *ModifyAllowListReq {
	return &ModifyAllowListReq{

		ModifyMode: ModifyMode_Cover,
	}
}

func (p *ModifyAllowListReq) InitDefault() {
	p.ModifyMode = ModifyMode_Cover
}

func (p *ModifyAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}

func (p *ModifyAllowListReq) GetAllowListName() (v string) {
	return p.AllowListName
}

var ModifyAllowListReq_AllowListDesc_DEFAULT string

func (p *ModifyAllowListReq) GetAllowListDesc() (v string) {
	if !p.IsSetAllowListDesc() {
		return ModifyAllowListReq_AllowListDesc_DEFAULT
	}
	return *p.AllowListDesc
}

var ModifyAllowListReq_AllowList_DEFAULT string

func (p *ModifyAllowListReq) GetAllowList() (v string) {
	if !p.IsSetAllowList() {
		return ModifyAllowListReq_AllowList_DEFAULT
	}
	return *p.AllowList
}

var ModifyAllowListReq_ModifyMode_DEFAULT ModifyMode = ModifyMode_Cover

func (p *ModifyAllowListReq) GetModifyMode() (v ModifyMode) {
	if !p.IsSetModifyMode() {
		return ModifyAllowListReq_ModifyMode_DEFAULT
	}
	return p.ModifyMode
}

var ModifyAllowListReq_ApplyInstanceNum_DEFAULT int32

func (p *ModifyAllowListReq) GetApplyInstanceNum() (v int32) {
	if !p.IsSetApplyInstanceNum() {
		return ModifyAllowListReq_ApplyInstanceNum_DEFAULT
	}
	return *p.ApplyInstanceNum
}

var ModifyAllowListReq_AllowListCategory_DEFAULT AllowListCategory

func (p *ModifyAllowListReq) GetAllowListCategory() (v AllowListCategory) {
	if !p.IsSetAllowListCategory() {
		return ModifyAllowListReq_AllowListCategory_DEFAULT
	}
	return *p.AllowListCategory
}

var ModifyAllowListReq_SecurityGroupBindInfos_DEFAULT []*SecurityGroupBindInfo

func (p *ModifyAllowListReq) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	if !p.IsSetSecurityGroupBindInfos() {
		return ModifyAllowListReq_SecurityGroupBindInfos_DEFAULT
	}
	return p.SecurityGroupBindInfos
}

var ModifyAllowListReq_UpdateSecurityGroup_DEFAULT bool

func (p *ModifyAllowListReq) GetUpdateSecurityGroup() (v bool) {
	if !p.IsSetUpdateSecurityGroup() {
		return ModifyAllowListReq_UpdateSecurityGroup_DEFAULT
	}
	return *p.UpdateSecurityGroup
}

var ModifyAllowListReq_UserAllowList_DEFAULT string

func (p *ModifyAllowListReq) GetUserAllowList() (v string) {
	if !p.IsSetUserAllowList() {
		return ModifyAllowListReq_UserAllowList_DEFAULT
	}
	return *p.UserAllowList
}

var ModifyAllowListReq_IgnoreInstanceStatus_DEFAULT bool

func (p *ModifyAllowListReq) GetIgnoreInstanceStatus() (v bool) {
	if !p.IsSetIgnoreInstanceStatus() {
		return ModifyAllowListReq_IgnoreInstanceStatus_DEFAULT
	}
	return *p.IgnoreInstanceStatus
}
func (p *ModifyAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *ModifyAllowListReq) SetAllowListName(val string) {
	p.AllowListName = val
}
func (p *ModifyAllowListReq) SetAllowListDesc(val *string) {
	p.AllowListDesc = val
}
func (p *ModifyAllowListReq) SetAllowList(val *string) {
	p.AllowList = val
}
func (p *ModifyAllowListReq) SetModifyMode(val ModifyMode) {
	p.ModifyMode = val
}
func (p *ModifyAllowListReq) SetApplyInstanceNum(val *int32) {
	p.ApplyInstanceNum = val
}
func (p *ModifyAllowListReq) SetAllowListCategory(val *AllowListCategory) {
	p.AllowListCategory = val
}
func (p *ModifyAllowListReq) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}
func (p *ModifyAllowListReq) SetUpdateSecurityGroup(val *bool) {
	p.UpdateSecurityGroup = val
}
func (p *ModifyAllowListReq) SetUserAllowList(val *string) {
	p.UserAllowList = val
}
func (p *ModifyAllowListReq) SetIgnoreInstanceStatus(val *bool) {
	p.IgnoreInstanceStatus = val
}

var fieldIDToName_ModifyAllowListReq = map[int16]string{
	1:  "AllowListId",
	2:  "AllowListName",
	3:  "AllowListDesc",
	4:  "AllowList",
	5:  "ModifyMode",
	6:  "ApplyInstanceNum",
	7:  "AllowListCategory",
	8:  "SecurityGroupBindInfos",
	9:  "UpdateSecurityGroup",
	10: "UserAllowList",
	11: "IgnoreInstanceStatus",
}

func (p *ModifyAllowListReq) IsSetAllowListDesc() bool {
	return p.AllowListDesc != nil
}

func (p *ModifyAllowListReq) IsSetAllowList() bool {
	return p.AllowList != nil
}

func (p *ModifyAllowListReq) IsSetModifyMode() bool {
	return p.ModifyMode != ModifyAllowListReq_ModifyMode_DEFAULT
}

func (p *ModifyAllowListReq) IsSetApplyInstanceNum() bool {
	return p.ApplyInstanceNum != nil
}

func (p *ModifyAllowListReq) IsSetAllowListCategory() bool {
	return p.AllowListCategory != nil
}

func (p *ModifyAllowListReq) IsSetSecurityGroupBindInfos() bool {
	return p.SecurityGroupBindInfos != nil
}

func (p *ModifyAllowListReq) IsSetUpdateSecurityGroup() bool {
	return p.UpdateSecurityGroup != nil
}

func (p *ModifyAllowListReq) IsSetUserAllowList() bool {
	return p.UserAllowList != nil
}

func (p *ModifyAllowListReq) IsSetIgnoreInstanceStatus() bool {
	return p.IgnoreInstanceStatus != nil
}

func (p *ModifyAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false
	var issetAllowListName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyAllowListReq[fieldId]))
}

func (p *ModifyAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListName = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowListDesc = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowList = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field ModifyMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ModifyMode(v)
	}
	p.ModifyMode = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApplyInstanceNum = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *AllowListCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AllowListCategory(v)
		_field = &tmp
	}
	p.AllowListCategory = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdateSecurityGroup = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserAllowList = _field
	return nil
}
func (p *ModifyAllowListReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IgnoreInstanceStatus = _field
	return nil
}

func (p *ModifyAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListDesc() {
		if err = oprot.WriteFieldBegin("AllowListDesc", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowListDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowList() {
		if err = oprot.WriteFieldBegin("AllowList", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetModifyMode() {
		if err = oprot.WriteFieldBegin("ModifyMode", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(p.ModifyMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetApplyInstanceNum() {
		if err = oprot.WriteFieldBegin("ApplyInstanceNum", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ApplyInstanceNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowListCategory() {
		if err = oprot.WriteFieldBegin("AllowListCategory", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AllowListCategory)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupBindInfos() {
		if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
			return err
		}
		for _, v := range p.SecurityGroupBindInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateSecurityGroup() {
		if err = oprot.WriteFieldBegin("UpdateSecurityGroup", thrift.BOOL, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.UpdateSecurityGroup); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserAllowList() {
		if err = oprot.WriteFieldBegin("UserAllowList", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserAllowList); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ModifyAllowListReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetIgnoreInstanceStatus() {
		if err = oprot.WriteFieldBegin("IgnoreInstanceStatus", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IgnoreInstanceStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ModifyAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAllowListReq(%+v)", *p)

}

func (p *ModifyAllowListReq) DeepEqual(ano *ModifyAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllowListDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.AllowList) {
		return false
	}
	if !p.Field5DeepEqual(ano.ModifyMode) {
		return false
	}
	if !p.Field6DeepEqual(ano.ApplyInstanceNum) {
		return false
	}
	if !p.Field7DeepEqual(ano.AllowListCategory) {
		return false
	}
	if !p.Field8DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	if !p.Field9DeepEqual(ano.UpdateSecurityGroup) {
		return false
	}
	if !p.Field10DeepEqual(ano.UserAllowList) {
		return false
	}
	if !p.Field11DeepEqual(ano.IgnoreInstanceStatus) {
		return false
	}
	return true
}

func (p *ModifyAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field3DeepEqual(src *string) bool {

	if p.AllowListDesc == src {
		return true
	} else if p.AllowListDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowListDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field4DeepEqual(src *string) bool {

	if p.AllowList == src {
		return true
	} else if p.AllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field5DeepEqual(src ModifyMode) bool {

	if p.ModifyMode != src {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field6DeepEqual(src *int32) bool {

	if p.ApplyInstanceNum == src {
		return true
	} else if p.ApplyInstanceNum == nil || src == nil {
		return false
	}
	if *p.ApplyInstanceNum != *src {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field7DeepEqual(src *AllowListCategory) bool {

	if p.AllowListCategory == src {
		return true
	} else if p.AllowListCategory == nil || src == nil {
		return false
	}
	if *p.AllowListCategory != *src {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field8DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyAllowListReq) Field9DeepEqual(src *bool) bool {

	if p.UpdateSecurityGroup == src {
		return true
	} else if p.UpdateSecurityGroup == nil || src == nil {
		return false
	}
	if *p.UpdateSecurityGroup != *src {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field10DeepEqual(src *string) bool {

	if p.UserAllowList == src {
		return true
	} else if p.UserAllowList == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserAllowList, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAllowListReq) Field11DeepEqual(src *bool) bool {

	if p.IgnoreInstanceStatus == src {
		return true
	} else if p.IgnoreInstanceStatus == nil || src == nil {
		return false
	}
	if *p.IgnoreInstanceStatus != *src {
		return false
	}
	return true
}

type ModifyAllowListResp struct {
}

func NewModifyAllowListResp() *ModifyAllowListResp {
	return &ModifyAllowListResp{}
}

func (p *ModifyAllowListResp) InitDefault() {
}

var fieldIDToName_ModifyAllowListResp = map[int16]string{}

func (p *ModifyAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAllowListResp")

	if err = oprot.WriteStructBegin("ModifyAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAllowListResp(%+v)", *p)

}

func (p *ModifyAllowListResp) DeepEqual(ano *ModifyAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteAllowListReq struct {
	AllowListId string `thrift:"AllowListId,1,required" frugal:"1,required,string" validate:"required"`
}

func NewDeleteAllowListReq() *DeleteAllowListReq {
	return &DeleteAllowListReq{}
}

func (p *DeleteAllowListReq) InitDefault() {
}

func (p *DeleteAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *DeleteAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_DeleteAllowListReq = map[int16]string{
	1: "AllowListId",
}

func (p *DeleteAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteAllowListReq[fieldId]))
}

func (p *DeleteAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *DeleteAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAllowListReq(%+v)", *p)

}

func (p *DeleteAllowListReq) DeepEqual(ano *DeleteAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *DeleteAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type DeleteAllowListResp struct {
}

func NewDeleteAllowListResp() *DeleteAllowListResp {
	return &DeleteAllowListResp{}
}

func (p *DeleteAllowListResp) InitDefault() {
}

var fieldIDToName_DeleteAllowListResp = map[int16]string{}

func (p *DeleteAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteAllowListResp")

	if err = oprot.WriteStructBegin("DeleteAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAllowListResp(%+v)", *p)

}

func (p *DeleteAllowListResp) DeepEqual(ano *DeleteAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type AssociateAllowListReq struct {
	InstanceIds  []string `thrift:"InstanceIds,1,required" frugal:"1,required,list<string>" validate:"required"`
	AllowListIds []string `thrift:"AllowListIds,2,required" frugal:"2,required,list<string>" validate:"required"`
}

func NewAssociateAllowListReq() *AssociateAllowListReq {
	return &AssociateAllowListReq{}
}

func (p *AssociateAllowListReq) InitDefault() {
}

func (p *AssociateAllowListReq) GetInstanceIds() (v []string) {
	return p.InstanceIds
}

func (p *AssociateAllowListReq) GetAllowListIds() (v []string) {
	return p.AllowListIds
}
func (p *AssociateAllowListReq) SetInstanceIds(val []string) {
	p.InstanceIds = val
}
func (p *AssociateAllowListReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_AssociateAllowListReq = map[int16]string{
	1: "InstanceIds",
	2: "AllowListIds",
}

func (p *AssociateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceIds bool = false
	var issetAllowListIds bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceIds {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListIds {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssociateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AssociateAllowListReq[fieldId]))
}

func (p *AssociateAllowListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceIds = _field
	return nil
}
func (p *AssociateAllowListReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *AssociateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AssociateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceIds", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceIds)); err != nil {
		return err
	}
	for _, v := range p.InstanceIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssociateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
		return err
	}
	for _, v := range p.AllowListIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AssociateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociateAllowListReq(%+v)", *p)

}

func (p *AssociateAllowListReq) DeepEqual(ano *AssociateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceIds) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *AssociateAllowListReq) Field1DeepEqual(src []string) bool {

	if len(p.InstanceIds) != len(src) {
		return false
	}
	for i, v := range p.InstanceIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *AssociateAllowListReq) Field2DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type AssociateAllowListResp struct {
}

func NewAssociateAllowListResp() *AssociateAllowListResp {
	return &AssociateAllowListResp{}
}

func (p *AssociateAllowListResp) InitDefault() {
}

var fieldIDToName_AssociateAllowListResp = map[int16]string{}

func (p *AssociateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssociateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AssociateAllowListResp")

	if err = oprot.WriteStructBegin("AssociateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssociateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssociateAllowListResp(%+v)", *p)

}

func (p *AssociateAllowListResp) DeepEqual(ano *AssociateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DisassociateAllowListReq struct {
	InstanceIds  []string `thrift:"InstanceIds,1,required" frugal:"1,required,list<string>" validate:"required"`
	AllowListIds []string `thrift:"AllowListIds,2,required" frugal:"2,required,list<string>" validate:"required"`
}

func NewDisassociateAllowListReq() *DisassociateAllowListReq {
	return &DisassociateAllowListReq{}
}

func (p *DisassociateAllowListReq) InitDefault() {
}

func (p *DisassociateAllowListReq) GetInstanceIds() (v []string) {
	return p.InstanceIds
}

func (p *DisassociateAllowListReq) GetAllowListIds() (v []string) {
	return p.AllowListIds
}
func (p *DisassociateAllowListReq) SetInstanceIds(val []string) {
	p.InstanceIds = val
}
func (p *DisassociateAllowListReq) SetAllowListIds(val []string) {
	p.AllowListIds = val
}

var fieldIDToName_DisassociateAllowListReq = map[int16]string{
	1: "InstanceIds",
	2: "AllowListIds",
}

func (p *DisassociateAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceIds bool = false
	var issetAllowListIds bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceIds {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListIds {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DisassociateAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DisassociateAllowListReq[fieldId]))
}

func (p *DisassociateAllowListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceIds = _field
	return nil
}
func (p *DisassociateAllowListReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllowListIds = _field
	return nil
}

func (p *DisassociateAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DisassociateAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisassociateAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceIds", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceIds)); err != nil {
		return err
	}
	for _, v := range p.InstanceIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DisassociateAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListIds", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AllowListIds)); err != nil {
		return err
	}
	for _, v := range p.AllowListIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DisassociateAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisassociateAllowListReq(%+v)", *p)

}

func (p *DisassociateAllowListReq) DeepEqual(ano *DisassociateAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceIds) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListIds) {
		return false
	}
	return true
}

func (p *DisassociateAllowListReq) Field1DeepEqual(src []string) bool {

	if len(p.InstanceIds) != len(src) {
		return false
	}
	for i, v := range p.InstanceIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DisassociateAllowListReq) Field2DeepEqual(src []string) bool {

	if len(p.AllowListIds) != len(src) {
		return false
	}
	for i, v := range p.AllowListIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DisassociateAllowListResp struct {
}

func NewDisassociateAllowListResp() *DisassociateAllowListResp {
	return &DisassociateAllowListResp{}
}

func (p *DisassociateAllowListResp) InitDefault() {
}

var fieldIDToName_DisassociateAllowListResp = map[int16]string{}

func (p *DisassociateAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisassociateAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisassociateAllowListResp")

	if err = oprot.WriteStructBegin("DisassociateAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisassociateAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisassociateAllowListResp(%+v)", *p)

}

func (p *DisassociateAllowListResp) DeepEqual(ano *DisassociateAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type SecurityGroupBindInfo struct {
	SecurityGroupId   string            `thrift:"SecurityGroupId,1,required" frugal:"1,required,string" json:"SecurityGroupId"`
	SecurityGroupName *string           `thrift:"SecurityGroupName,2,optional" frugal:"2,optional,string" json:"SecurityGroupName,omitempty"`
	BindMode          AllowListBindMode `thrift:"BindMode,3,required" frugal:"3,required,AllowListBindMode" json:"BindMode"`
	IpList            []string          `thrift:"IpList,4,optional" frugal:"4,optional,list<string>" json:"IpList,omitempty"`
}

func NewSecurityGroupBindInfo() *SecurityGroupBindInfo {
	return &SecurityGroupBindInfo{}
}

func (p *SecurityGroupBindInfo) InitDefault() {
}

func (p *SecurityGroupBindInfo) GetSecurityGroupId() (v string) {
	return p.SecurityGroupId
}

var SecurityGroupBindInfo_SecurityGroupName_DEFAULT string

func (p *SecurityGroupBindInfo) GetSecurityGroupName() (v string) {
	if !p.IsSetSecurityGroupName() {
		return SecurityGroupBindInfo_SecurityGroupName_DEFAULT
	}
	return *p.SecurityGroupName
}

func (p *SecurityGroupBindInfo) GetBindMode() (v AllowListBindMode) {
	return p.BindMode
}

var SecurityGroupBindInfo_IpList_DEFAULT []string

func (p *SecurityGroupBindInfo) GetIpList() (v []string) {
	if !p.IsSetIpList() {
		return SecurityGroupBindInfo_IpList_DEFAULT
	}
	return p.IpList
}
func (p *SecurityGroupBindInfo) SetSecurityGroupId(val string) {
	p.SecurityGroupId = val
}
func (p *SecurityGroupBindInfo) SetSecurityGroupName(val *string) {
	p.SecurityGroupName = val
}
func (p *SecurityGroupBindInfo) SetBindMode(val AllowListBindMode) {
	p.BindMode = val
}
func (p *SecurityGroupBindInfo) SetIpList(val []string) {
	p.IpList = val
}

var fieldIDToName_SecurityGroupBindInfo = map[int16]string{
	1: "SecurityGroupId",
	2: "SecurityGroupName",
	3: "BindMode",
	4: "IpList",
}

func (p *SecurityGroupBindInfo) IsSetSecurityGroupName() bool {
	return p.SecurityGroupName != nil
}

func (p *SecurityGroupBindInfo) IsSetIpList() bool {
	return p.IpList != nil
}

func (p *SecurityGroupBindInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SecurityGroupBindInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSecurityGroupId bool = false
	var issetBindMode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityGroupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBindMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSecurityGroupId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBindMode {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SecurityGroupBindInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SecurityGroupBindInfo[fieldId]))
}

func (p *SecurityGroupBindInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SecurityGroupId = _field
	return nil
}
func (p *SecurityGroupBindInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SecurityGroupName = _field
	return nil
}
func (p *SecurityGroupBindInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field AllowListBindMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AllowListBindMode(v)
	}
	p.BindMode = _field
	return nil
}
func (p *SecurityGroupBindInfo) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IpList = _field
	return nil
}

func (p *SecurityGroupBindInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SecurityGroupBindInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SecurityGroupBindInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SecurityGroupBindInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityGroupId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SecurityGroupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SecurityGroupBindInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupName() {
		if err = oprot.WriteFieldBegin("SecurityGroupName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SecurityGroupName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SecurityGroupBindInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BindMode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BindMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SecurityGroupBindInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetIpList() {
		if err = oprot.WriteFieldBegin("IpList", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.IpList)); err != nil {
			return err
		}
		for _, v := range p.IpList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SecurityGroupBindInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SecurityGroupBindInfo(%+v)", *p)

}

func (p *SecurityGroupBindInfo) DeepEqual(ano *SecurityGroupBindInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SecurityGroupId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SecurityGroupName) {
		return false
	}
	if !p.Field3DeepEqual(ano.BindMode) {
		return false
	}
	if !p.Field4DeepEqual(ano.IpList) {
		return false
	}
	return true
}

func (p *SecurityGroupBindInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SecurityGroupId, src) != 0 {
		return false
	}
	return true
}
func (p *SecurityGroupBindInfo) Field2DeepEqual(src *string) bool {

	if p.SecurityGroupName == src {
		return true
	} else if p.SecurityGroupName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SecurityGroupName, *src) != 0 {
		return false
	}
	return true
}
func (p *SecurityGroupBindInfo) Field3DeepEqual(src AllowListBindMode) bool {

	if p.BindMode != src {
		return false
	}
	return true
}
func (p *SecurityGroupBindInfo) Field4DeepEqual(src []string) bool {

	if len(p.IpList) != len(src) {
		return false
	}
	for i, v := range p.IpList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type SyncSecurityGroupChangeObject struct {
	SecurityGroupId   string            `thrift:"SecurityGroupId,1,required" frugal:"1,required,string" json:"SecurityGroupId"`
	SecurityGroupName *string           `thrift:"SecurityGroupName,2,optional" frugal:"2,optional,string" json:"SecurityGroupName,omitempty"`
	HasChanged        bool              `thrift:"HasChanged,3,required" frugal:"3,required,bool" json:"HasChanged"`
	OldIpList         []string          `thrift:"OldIpList,4,required" frugal:"4,required,list<string>" json:"OldIpList"`
	NewIpList_        []string          `thrift:"NewIpList,5,required" frugal:"5,required,list<string>" json:"NewIpList"`
	AddedIpList       []string          `thrift:"AddedIpList,6,required" frugal:"6,required,list<string>" json:"AddedIpList"`
	RemovedIpList     []string          `thrift:"RemovedIpList,7,required" frugal:"7,required,list<string>" json:"RemovedIpList"`
	BindMode          AllowListBindMode `thrift:"BindMode,8,required" frugal:"8,required,AllowListBindMode" json:"BindMode"`
	HasDeleted        bool              `thrift:"HasDeleted,9,required" frugal:"9,required,bool" json:"HasDeleted"`
}

func NewSyncSecurityGroupChangeObject() *SyncSecurityGroupChangeObject {
	return &SyncSecurityGroupChangeObject{}
}

func (p *SyncSecurityGroupChangeObject) InitDefault() {
}

func (p *SyncSecurityGroupChangeObject) GetSecurityGroupId() (v string) {
	return p.SecurityGroupId
}

var SyncSecurityGroupChangeObject_SecurityGroupName_DEFAULT string

func (p *SyncSecurityGroupChangeObject) GetSecurityGroupName() (v string) {
	if !p.IsSetSecurityGroupName() {
		return SyncSecurityGroupChangeObject_SecurityGroupName_DEFAULT
	}
	return *p.SecurityGroupName
}

func (p *SyncSecurityGroupChangeObject) GetHasChanged() (v bool) {
	return p.HasChanged
}

func (p *SyncSecurityGroupChangeObject) GetOldIpList() (v []string) {
	return p.OldIpList
}

func (p *SyncSecurityGroupChangeObject) GetNewIpList_() (v []string) {
	return p.NewIpList_
}

func (p *SyncSecurityGroupChangeObject) GetAddedIpList() (v []string) {
	return p.AddedIpList
}

func (p *SyncSecurityGroupChangeObject) GetRemovedIpList() (v []string) {
	return p.RemovedIpList
}

func (p *SyncSecurityGroupChangeObject) GetBindMode() (v AllowListBindMode) {
	return p.BindMode
}

func (p *SyncSecurityGroupChangeObject) GetHasDeleted() (v bool) {
	return p.HasDeleted
}
func (p *SyncSecurityGroupChangeObject) SetSecurityGroupId(val string) {
	p.SecurityGroupId = val
}
func (p *SyncSecurityGroupChangeObject) SetSecurityGroupName(val *string) {
	p.SecurityGroupName = val
}
func (p *SyncSecurityGroupChangeObject) SetHasChanged(val bool) {
	p.HasChanged = val
}
func (p *SyncSecurityGroupChangeObject) SetOldIpList(val []string) {
	p.OldIpList = val
}
func (p *SyncSecurityGroupChangeObject) SetNewIpList_(val []string) {
	p.NewIpList_ = val
}
func (p *SyncSecurityGroupChangeObject) SetAddedIpList(val []string) {
	p.AddedIpList = val
}
func (p *SyncSecurityGroupChangeObject) SetRemovedIpList(val []string) {
	p.RemovedIpList = val
}
func (p *SyncSecurityGroupChangeObject) SetBindMode(val AllowListBindMode) {
	p.BindMode = val
}
func (p *SyncSecurityGroupChangeObject) SetHasDeleted(val bool) {
	p.HasDeleted = val
}

var fieldIDToName_SyncSecurityGroupChangeObject = map[int16]string{
	1: "SecurityGroupId",
	2: "SecurityGroupName",
	3: "HasChanged",
	4: "OldIpList",
	5: "NewIpList",
	6: "AddedIpList",
	7: "RemovedIpList",
	8: "BindMode",
	9: "HasDeleted",
}

func (p *SyncSecurityGroupChangeObject) IsSetSecurityGroupName() bool {
	return p.SecurityGroupName != nil
}

func (p *SyncSecurityGroupChangeObject) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncSecurityGroupChangeObject")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSecurityGroupId bool = false
	var issetHasChanged bool = false
	var issetOldIpList bool = false
	var issetNewIpList_ bool = false
	var issetAddedIpList bool = false
	var issetRemovedIpList bool = false
	var issetBindMode bool = false
	var issetHasDeleted bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityGroupId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetHasChanged = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetOldIpList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetNewIpList_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAddedIpList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetRemovedIpList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetBindMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetHasDeleted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSecurityGroupId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetHasChanged {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetOldIpList {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetNewIpList_ {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAddedIpList {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRemovedIpList {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetBindMode {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetHasDeleted {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncSecurityGroupChangeObject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SyncSecurityGroupChangeObject[fieldId]))
}

func (p *SyncSecurityGroupChangeObject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SecurityGroupId = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SecurityGroupName = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasChanged = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OldIpList = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NewIpList_ = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AddedIpList = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RemovedIpList = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField8(iprot thrift.TProtocol) error {

	var _field AllowListBindMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AllowListBindMode(v)
	}
	p.BindMode = _field
	return nil
}
func (p *SyncSecurityGroupChangeObject) ReadField9(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasDeleted = _field
	return nil
}

func (p *SyncSecurityGroupChangeObject) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncSecurityGroupChangeObject")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncSecurityGroupChangeObject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityGroupId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SecurityGroupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupName() {
		if err = oprot.WriteFieldBegin("SecurityGroupName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SecurityGroupName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HasChanged", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasChanged); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OldIpList", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.OldIpList)); err != nil {
		return err
	}
	for _, v := range p.OldIpList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NewIpList", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.NewIpList_)); err != nil {
		return err
	}
	for _, v := range p.NewIpList_ {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AddedIpList", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AddedIpList)); err != nil {
		return err
	}
	for _, v := range p.AddedIpList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RemovedIpList", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.RemovedIpList)); err != nil {
		return err
	}
	for _, v := range p.RemovedIpList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BindMode", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BindMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HasDeleted", thrift.BOOL, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasDeleted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SyncSecurityGroupChangeObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncSecurityGroupChangeObject(%+v)", *p)

}

func (p *SyncSecurityGroupChangeObject) DeepEqual(ano *SyncSecurityGroupChangeObject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SecurityGroupId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SecurityGroupName) {
		return false
	}
	if !p.Field3DeepEqual(ano.HasChanged) {
		return false
	}
	if !p.Field4DeepEqual(ano.OldIpList) {
		return false
	}
	if !p.Field5DeepEqual(ano.NewIpList_) {
		return false
	}
	if !p.Field6DeepEqual(ano.AddedIpList) {
		return false
	}
	if !p.Field7DeepEqual(ano.RemovedIpList) {
		return false
	}
	if !p.Field8DeepEqual(ano.BindMode) {
		return false
	}
	if !p.Field9DeepEqual(ano.HasDeleted) {
		return false
	}
	return true
}

func (p *SyncSecurityGroupChangeObject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SecurityGroupId, src) != 0 {
		return false
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field2DeepEqual(src *string) bool {

	if p.SecurityGroupName == src {
		return true
	} else if p.SecurityGroupName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SecurityGroupName, *src) != 0 {
		return false
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field3DeepEqual(src bool) bool {

	if p.HasChanged != src {
		return false
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field4DeepEqual(src []string) bool {

	if len(p.OldIpList) != len(src) {
		return false
	}
	for i, v := range p.OldIpList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field5DeepEqual(src []string) bool {

	if len(p.NewIpList_) != len(src) {
		return false
	}
	for i, v := range p.NewIpList_ {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field6DeepEqual(src []string) bool {

	if len(p.AddedIpList) != len(src) {
		return false
	}
	for i, v := range p.AddedIpList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field7DeepEqual(src []string) bool {

	if len(p.RemovedIpList) != len(src) {
		return false
	}
	for i, v := range p.RemovedIpList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field8DeepEqual(src AllowListBindMode) bool {

	if p.BindMode != src {
		return false
	}
	return true
}
func (p *SyncSecurityGroupChangeObject) Field9DeepEqual(src bool) bool {

	if p.HasDeleted != src {
		return false
	}
	return true
}

type SyncAllowListReq struct {
	AllowListId            string                   `thrift:"AllowListId,1,required" frugal:"1,required,string" json-check:"required"`
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,2,optional" frugal:"2,optional,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos,omitempty"`
}

func NewSyncAllowListReq() *SyncAllowListReq {
	return &SyncAllowListReq{}
}

func (p *SyncAllowListReq) InitDefault() {
}

func (p *SyncAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}

var SyncAllowListReq_SecurityGroupBindInfos_DEFAULT []*SecurityGroupBindInfo

func (p *SyncAllowListReq) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	if !p.IsSetSecurityGroupBindInfos() {
		return SyncAllowListReq_SecurityGroupBindInfos_DEFAULT
	}
	return p.SecurityGroupBindInfos
}
func (p *SyncAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}
func (p *SyncAllowListReq) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}

var fieldIDToName_SyncAllowListReq = map[int16]string{
	1: "AllowListId",
	2: "SecurityGroupBindInfos",
}

func (p *SyncAllowListReq) IsSetSecurityGroupBindInfos() bool {
	return p.SecurityGroupBindInfos != nil
}

func (p *SyncAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllowListId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SyncAllowListReq[fieldId]))
}

func (p *SyncAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}
func (p *SyncAllowListReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}

func (p *SyncAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityGroupBindInfos() {
		if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
			return err
		}
		for _, v := range p.SecurityGroupBindInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SyncAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncAllowListReq(%+v)", *p)

}

func (p *SyncAllowListReq) DeepEqual(ano *SyncAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowListId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	return true
}

func (p *SyncAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}
func (p *SyncAllowListReq) Field2DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SyncAllowListResp struct {
	ChangeObjects []*SyncSecurityGroupChangeObject `thrift:"ChangeObjects,1" frugal:"1,default,list<SyncSecurityGroupChangeObject>" json:"ChangeObjects"`
}

func NewSyncAllowListResp() *SyncAllowListResp {
	return &SyncAllowListResp{}
}

func (p *SyncAllowListResp) InitDefault() {
}

func (p *SyncAllowListResp) GetChangeObjects() (v []*SyncSecurityGroupChangeObject) {
	return p.ChangeObjects
}
func (p *SyncAllowListResp) SetChangeObjects(val []*SyncSecurityGroupChangeObject) {
	p.ChangeObjects = val
}

var fieldIDToName_SyncAllowListResp = map[int16]string{
	1: "ChangeObjects",
}

func (p *SyncAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncAllowListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SyncAllowListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SyncSecurityGroupChangeObject, 0, size)
	values := make([]SyncSecurityGroupChangeObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChangeObjects = _field
	return nil
}

func (p *SyncAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncAllowListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncAllowListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChangeObjects", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChangeObjects)); err != nil {
		return err
	}
	for _, v := range p.ChangeObjects {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncAllowListResp(%+v)", *p)

}

func (p *SyncAllowListResp) DeepEqual(ano *SyncAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChangeObjects) {
		return false
	}
	return true
}

func (p *SyncAllowListResp) Field1DeepEqual(src []*SyncSecurityGroupChangeObject) bool {

	if len(p.ChangeObjects) != len(src) {
		return false
	}
	for i, v := range p.ChangeObjects {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type GetSecurityGroupBindIpListReq struct {
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,1,required" frugal:"1,required,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos"`
}

func NewGetSecurityGroupBindIpListReq() *GetSecurityGroupBindIpListReq {
	return &GetSecurityGroupBindIpListReq{}
}

func (p *GetSecurityGroupBindIpListReq) InitDefault() {
}

func (p *GetSecurityGroupBindIpListReq) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	return p.SecurityGroupBindInfos
}
func (p *GetSecurityGroupBindIpListReq) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}

var fieldIDToName_GetSecurityGroupBindIpListReq = map[int16]string{
	1: "SecurityGroupBindInfos",
}

func (p *GetSecurityGroupBindIpListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSecurityGroupBindIpListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSecurityGroupBindInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityGroupBindInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSecurityGroupBindInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSecurityGroupBindIpListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSecurityGroupBindIpListReq[fieldId]))
}

func (p *GetSecurityGroupBindIpListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}

func (p *GetSecurityGroupBindIpListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSecurityGroupBindIpListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSecurityGroupBindIpListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSecurityGroupBindIpListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
		return err
	}
	for _, v := range p.SecurityGroupBindInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSecurityGroupBindIpListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSecurityGroupBindIpListReq(%+v)", *p)

}

func (p *GetSecurityGroupBindIpListReq) DeepEqual(ano *GetSecurityGroupBindIpListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	return true
}

func (p *GetSecurityGroupBindIpListReq) Field1DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type GetSecurityGroupBindIpListResp struct {
	SecurityGroupBindInfos []*SecurityGroupBindInfo `thrift:"SecurityGroupBindInfos,1" frugal:"1,default,list<SecurityGroupBindInfo>" json:"SecurityGroupBindInfos"`
}

func NewGetSecurityGroupBindIpListResp() *GetSecurityGroupBindIpListResp {
	return &GetSecurityGroupBindIpListResp{}
}

func (p *GetSecurityGroupBindIpListResp) InitDefault() {
}

func (p *GetSecurityGroupBindIpListResp) GetSecurityGroupBindInfos() (v []*SecurityGroupBindInfo) {
	return p.SecurityGroupBindInfos
}
func (p *GetSecurityGroupBindIpListResp) SetSecurityGroupBindInfos(val []*SecurityGroupBindInfo) {
	p.SecurityGroupBindInfos = val
}

var fieldIDToName_GetSecurityGroupBindIpListResp = map[int16]string{
	1: "SecurityGroupBindInfos",
}

func (p *GetSecurityGroupBindIpListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSecurityGroupBindIpListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSecurityGroupBindIpListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSecurityGroupBindIpListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SecurityGroupBindInfo, 0, size)
	values := make([]SecurityGroupBindInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SecurityGroupBindInfos = _field
	return nil
}

func (p *GetSecurityGroupBindIpListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSecurityGroupBindIpListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSecurityGroupBindIpListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSecurityGroupBindIpListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityGroupBindInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SecurityGroupBindInfos)); err != nil {
		return err
	}
	for _, v := range p.SecurityGroupBindInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSecurityGroupBindIpListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSecurityGroupBindIpListResp(%+v)", *p)

}

func (p *GetSecurityGroupBindIpListResp) DeepEqual(ano *GetSecurityGroupBindIpListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SecurityGroupBindInfos) {
		return false
	}
	return true
}

func (p *GetSecurityGroupBindIpListResp) Field1DeepEqual(src []*SecurityGroupBindInfo) bool {

	if len(p.SecurityGroupBindInfos) != len(src) {
		return false
	}
	for i, v := range p.SecurityGroupBindInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SyncInstanceAllowListReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	AllowListId string `thrift:"AllowListId,2,required" frugal:"2,required,string" json:"AllowListId"`
}

func NewSyncInstanceAllowListReq() *SyncInstanceAllowListReq {
	return &SyncInstanceAllowListReq{}
}

func (p *SyncInstanceAllowListReq) InitDefault() {
}

func (p *SyncInstanceAllowListReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SyncInstanceAllowListReq) GetAllowListId() (v string) {
	return p.AllowListId
}
func (p *SyncInstanceAllowListReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SyncInstanceAllowListReq) SetAllowListId(val string) {
	p.AllowListId = val
}

var fieldIDToName_SyncInstanceAllowListReq = map[int16]string{
	1: "InstanceId",
	2: "AllowListId",
}

func (p *SyncInstanceAllowListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncInstanceAllowListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetAllowListId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllowListId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAllowListId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncInstanceAllowListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SyncInstanceAllowListReq[fieldId]))
}

func (p *SyncInstanceAllowListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SyncInstanceAllowListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowListId = _field
	return nil
}

func (p *SyncInstanceAllowListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncInstanceAllowListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncInstanceAllowListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncInstanceAllowListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncInstanceAllowListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllowListId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllowListId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SyncInstanceAllowListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncInstanceAllowListReq(%+v)", *p)

}

func (p *SyncInstanceAllowListReq) DeepEqual(ano *SyncInstanceAllowListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowListId) {
		return false
	}
	return true
}

func (p *SyncInstanceAllowListReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SyncInstanceAllowListReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AllowListId, src) != 0 {
		return false
	}
	return true
}

type SyncInstanceAllowListResp struct {
}

func NewSyncInstanceAllowListResp() *SyncInstanceAllowListResp {
	return &SyncInstanceAllowListResp{}
}

func (p *SyncInstanceAllowListResp) InitDefault() {
}

var fieldIDToName_SyncInstanceAllowListResp = map[int16]string{}

func (p *SyncInstanceAllowListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncInstanceAllowListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SyncInstanceAllowListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncInstanceAllowListResp")

	if err = oprot.WriteStructBegin("SyncInstanceAllowListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncInstanceAllowListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncInstanceAllowListResp(%+v)", *p)

}

func (p *SyncInstanceAllowListResp) DeepEqual(ano *SyncInstanceAllowListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DBTDEStatus struct {
	DBName string `thrift:"DBName,1,required" frugal:"1,required,string" json:"DBName"`
	Enable bool   `thrift:"Enable,2,required" frugal:"2,required,bool" json:"Enable"`
}

func NewDBTDEStatus() *DBTDEStatus {
	return &DBTDEStatus{}
}

func (p *DBTDEStatus) InitDefault() {
}

func (p *DBTDEStatus) GetDBName() (v string) {
	return p.DBName
}

func (p *DBTDEStatus) GetEnable() (v bool) {
	return p.Enable
}
func (p *DBTDEStatus) SetDBName(val string) {
	p.DBName = val
}
func (p *DBTDEStatus) SetEnable(val bool) {
	p.Enable = val
}

var fieldIDToName_DBTDEStatus = map[int16]string{
	1: "DBName",
	2: "Enable",
}

func (p *DBTDEStatus) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DBTDEStatus")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBName bool = false
	var issetEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DBTDEStatus[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DBTDEStatus[fieldId]))
}

func (p *DBTDEStatus) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *DBTDEStatus) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}

func (p *DBTDEStatus) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DBTDEStatus")

	var fieldId int16
	if err = oprot.WriteStructBegin("DBTDEStatus"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DBTDEStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DBTDEStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DBTDEStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DBTDEStatus(%+v)", *p)

}

func (p *DBTDEStatus) DeepEqual(ano *DBTDEStatus) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Enable) {
		return false
	}
	return true
}

func (p *DBTDEStatus) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *DBTDEStatus) Field2DeepEqual(src bool) bool {

	if p.Enable != src {
		return false
	}
	return true
}

type ModifyDBInstanceTDEReq struct {
	InstanceId  string         `thrift:"InstanceId,1,required" frugal:"1,required,string" json-check:"required"`
	TDEStatus   EnableFlag     `thrift:"TDEStatus,2,required" frugal:"2,required,EnableFlag" json-check:"required"`
	TDEMode     TDEMode        `thrift:"TDEMode,3,required" frugal:"3,required,TDEMode" json:"TDEMode"`
	DBStatus    []*DBTDEStatus `thrift:"DBStatus,4,optional" frugal:"4,optional,list<DBTDEStatus>" json:"DBStatus,omitempty"`
	Certificate *string        `thrift:"Certificate,5,optional" frugal:"5,optional,string" json:"Certificate,omitempty"`
	PrivateKey  *string        `thrift:"PrivateKey,6,optional" frugal:"6,optional,string" json:"PrivateKey,omitempty"`
	Password    *string        `thrift:"Password,7,optional" frugal:"7,optional,string" json:"Password,omitempty"`
}

func NewModifyDBInstanceTDEReq() *ModifyDBInstanceTDEReq {
	return &ModifyDBInstanceTDEReq{}
}

func (p *ModifyDBInstanceTDEReq) InitDefault() {
}

func (p *ModifyDBInstanceTDEReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceTDEReq) GetTDEStatus() (v EnableFlag) {
	return p.TDEStatus
}

func (p *ModifyDBInstanceTDEReq) GetTDEMode() (v TDEMode) {
	return p.TDEMode
}

var ModifyDBInstanceTDEReq_DBStatus_DEFAULT []*DBTDEStatus

func (p *ModifyDBInstanceTDEReq) GetDBStatus() (v []*DBTDEStatus) {
	if !p.IsSetDBStatus() {
		return ModifyDBInstanceTDEReq_DBStatus_DEFAULT
	}
	return p.DBStatus
}

var ModifyDBInstanceTDEReq_Certificate_DEFAULT string

func (p *ModifyDBInstanceTDEReq) GetCertificate() (v string) {
	if !p.IsSetCertificate() {
		return ModifyDBInstanceTDEReq_Certificate_DEFAULT
	}
	return *p.Certificate
}

var ModifyDBInstanceTDEReq_PrivateKey_DEFAULT string

func (p *ModifyDBInstanceTDEReq) GetPrivateKey() (v string) {
	if !p.IsSetPrivateKey() {
		return ModifyDBInstanceTDEReq_PrivateKey_DEFAULT
	}
	return *p.PrivateKey
}

var ModifyDBInstanceTDEReq_Password_DEFAULT string

func (p *ModifyDBInstanceTDEReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return ModifyDBInstanceTDEReq_Password_DEFAULT
	}
	return *p.Password
}
func (p *ModifyDBInstanceTDEReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceTDEReq) SetTDEStatus(val EnableFlag) {
	p.TDEStatus = val
}
func (p *ModifyDBInstanceTDEReq) SetTDEMode(val TDEMode) {
	p.TDEMode = val
}
func (p *ModifyDBInstanceTDEReq) SetDBStatus(val []*DBTDEStatus) {
	p.DBStatus = val
}
func (p *ModifyDBInstanceTDEReq) SetCertificate(val *string) {
	p.Certificate = val
}
func (p *ModifyDBInstanceTDEReq) SetPrivateKey(val *string) {
	p.PrivateKey = val
}
func (p *ModifyDBInstanceTDEReq) SetPassword(val *string) {
	p.Password = val
}

var fieldIDToName_ModifyDBInstanceTDEReq = map[int16]string{
	1: "InstanceId",
	2: "TDEStatus",
	3: "TDEMode",
	4: "DBStatus",
	5: "Certificate",
	6: "PrivateKey",
	7: "Password",
}

func (p *ModifyDBInstanceTDEReq) IsSetDBStatus() bool {
	return p.DBStatus != nil
}

func (p *ModifyDBInstanceTDEReq) IsSetCertificate() bool {
	return p.Certificate != nil
}

func (p *ModifyDBInstanceTDEReq) IsSetPrivateKey() bool {
	return p.PrivateKey != nil
}

func (p *ModifyDBInstanceTDEReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *ModifyDBInstanceTDEReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTDEStatus bool = false
	var issetTDEMode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTDEStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTDEMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTDEStatus {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTDEMode {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceTDEReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBInstanceTDEReq[fieldId]))
}

func (p *ModifyDBInstanceTDEReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableFlag
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableFlag(v)
	}
	p.TDEStatus = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField3(iprot thrift.TProtocol) error {

	var _field TDEMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TDEMode(v)
	}
	p.TDEMode = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBTDEStatus, 0, size)
	values := make([]DBTDEStatus, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DBStatus = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Certificate = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PrivateKey = _field
	return nil
}
func (p *ModifyDBInstanceTDEReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}

func (p *ModifyDBInstanceTDEReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceTDEReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEMode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBStatus() {
		if err = oprot.WriteFieldBegin("DBStatus", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DBStatus)); err != nil {
			return err
		}
		for _, v := range p.DBStatus {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCertificate() {
		if err = oprot.WriteFieldBegin("Certificate", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Certificate); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrivateKey() {
		if err = oprot.WriteFieldBegin("PrivateKey", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PrivateKey); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceTDEReq(%+v)", *p)

}

func (p *ModifyDBInstanceTDEReq) DeepEqual(ano *ModifyDBInstanceTDEReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TDEStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.TDEMode) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBStatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.Certificate) {
		return false
	}
	if !p.Field6DeepEqual(ano.PrivateKey) {
		return false
	}
	if !p.Field7DeepEqual(ano.Password) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceTDEReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field2DeepEqual(src EnableFlag) bool {

	if p.TDEStatus != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field3DeepEqual(src TDEMode) bool {

	if p.TDEMode != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field4DeepEqual(src []*DBTDEStatus) bool {

	if len(p.DBStatus) != len(src) {
		return false
	}
	for i, v := range p.DBStatus {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field5DeepEqual(src *string) bool {

	if p.Certificate == src {
		return true
	} else if p.Certificate == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Certificate, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field6DeepEqual(src *string) bool {

	if p.PrivateKey == src {
		return true
	} else if p.PrivateKey == nil || src == nil {
		return false
	}
	if strings.Compare(*p.PrivateKey, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEReq) Field7DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}

type ModifyDBInstanceTDEResp struct {
	InstanceId string         `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	TDEStatus  EnableFlag     `thrift:"TDEStatus,2" frugal:"2,default,EnableFlag" json:"TDEStatus"`
	TDEMode    TDEMode        `thrift:"TDEMode,3" frugal:"3,default,TDEMode" json:"TDEMode"`
	DBStatus   []*DBTDEStatus `thrift:"DBStatus,4" frugal:"4,default,list<DBTDEStatus>" json:"DBStatus"`
}

func NewModifyDBInstanceTDEResp() *ModifyDBInstanceTDEResp {
	return &ModifyDBInstanceTDEResp{}
}

func (p *ModifyDBInstanceTDEResp) InitDefault() {
}

func (p *ModifyDBInstanceTDEResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBInstanceTDEResp) GetTDEStatus() (v EnableFlag) {
	return p.TDEStatus
}

func (p *ModifyDBInstanceTDEResp) GetTDEMode() (v TDEMode) {
	return p.TDEMode
}

func (p *ModifyDBInstanceTDEResp) GetDBStatus() (v []*DBTDEStatus) {
	return p.DBStatus
}
func (p *ModifyDBInstanceTDEResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBInstanceTDEResp) SetTDEStatus(val EnableFlag) {
	p.TDEStatus = val
}
func (p *ModifyDBInstanceTDEResp) SetTDEMode(val TDEMode) {
	p.TDEMode = val
}
func (p *ModifyDBInstanceTDEResp) SetDBStatus(val []*DBTDEStatus) {
	p.DBStatus = val
}

var fieldIDToName_ModifyDBInstanceTDEResp = map[int16]string{
	1: "InstanceId",
	2: "TDEStatus",
	3: "TDEMode",
	4: "DBStatus",
}

func (p *ModifyDBInstanceTDEResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBInstanceTDEResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBInstanceTDEResp) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableFlag
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableFlag(v)
	}
	p.TDEStatus = _field
	return nil
}
func (p *ModifyDBInstanceTDEResp) ReadField3(iprot thrift.TProtocol) error {

	var _field TDEMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TDEMode(v)
	}
	p.TDEMode = _field
	return nil
}
func (p *ModifyDBInstanceTDEResp) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBTDEStatus, 0, size)
	values := make([]DBTDEStatus, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DBStatus = _field
	return nil
}

func (p *ModifyDBInstanceTDEResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBInstanceTDEResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBInstanceTDEResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEMode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBStatus", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DBStatus)); err != nil {
		return err
	}
	for _, v := range p.DBStatus {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBInstanceTDEResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBInstanceTDEResp(%+v)", *p)

}

func (p *ModifyDBInstanceTDEResp) DeepEqual(ano *ModifyDBInstanceTDEResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TDEStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.TDEMode) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBStatus) {
		return false
	}
	return true
}

func (p *ModifyDBInstanceTDEResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEResp) Field2DeepEqual(src EnableFlag) bool {

	if p.TDEStatus != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEResp) Field3DeepEqual(src TDEMode) bool {

	if p.TDEMode != src {
		return false
	}
	return true
}
func (p *ModifyDBInstanceTDEResp) Field4DeepEqual(src []*DBTDEStatus) bool {

	if len(p.DBStatus) != len(src) {
		return false
	}
	for i, v := range p.DBStatus {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDBInstanceTDEReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json-check:"required"`
}

func NewDescribeDBInstanceTDEReq() *DescribeDBInstanceTDEReq {
	return &DescribeDBInstanceTDEReq{}
}

func (p *DescribeDBInstanceTDEReq) InitDefault() {
}

func (p *DescribeDBInstanceTDEReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeDBInstanceTDEReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeDBInstanceTDEReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeDBInstanceTDEReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceTDEReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInstanceTDEReq[fieldId]))
}

func (p *DescribeDBInstanceTDEReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeDBInstanceTDEReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceTDEReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceTDEReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceTDEReq(%+v)", *p)

}

func (p *DescribeDBInstanceTDEReq) DeepEqual(ano *DescribeDBInstanceTDEReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceTDEReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeDBInstanceTDEResp struct {
	InstanceId string         `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	TDEStatus  EnableFlag     `thrift:"TDEStatus,2" frugal:"2,default,EnableFlag" json:"TDEStatus"`
	TDEMode    TDEMode        `thrift:"TDEMode,3" frugal:"3,default,TDEMode" json:"TDEMode"`
	DBStatus   []*DBTDEStatus `thrift:"DBStatus,4" frugal:"4,default,list<DBTDEStatus>" json:"DBStatus"`
}

func NewDescribeDBInstanceTDEResp() *DescribeDBInstanceTDEResp {
	return &DescribeDBInstanceTDEResp{}
}

func (p *DescribeDBInstanceTDEResp) InitDefault() {
}

func (p *DescribeDBInstanceTDEResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBInstanceTDEResp) GetTDEStatus() (v EnableFlag) {
	return p.TDEStatus
}

func (p *DescribeDBInstanceTDEResp) GetTDEMode() (v TDEMode) {
	return p.TDEMode
}

func (p *DescribeDBInstanceTDEResp) GetDBStatus() (v []*DBTDEStatus) {
	return p.DBStatus
}
func (p *DescribeDBInstanceTDEResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBInstanceTDEResp) SetTDEStatus(val EnableFlag) {
	p.TDEStatus = val
}
func (p *DescribeDBInstanceTDEResp) SetTDEMode(val TDEMode) {
	p.TDEMode = val
}
func (p *DescribeDBInstanceTDEResp) SetDBStatus(val []*DBTDEStatus) {
	p.DBStatus = val
}

var fieldIDToName_DescribeDBInstanceTDEResp = map[int16]string{
	1: "InstanceId",
	2: "TDEStatus",
	3: "TDEMode",
	4: "DBStatus",
}

func (p *DescribeDBInstanceTDEResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInstanceTDEResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBInstanceTDEResp) ReadField2(iprot thrift.TProtocol) error {

	var _field EnableFlag
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EnableFlag(v)
	}
	p.TDEStatus = _field
	return nil
}
func (p *DescribeDBInstanceTDEResp) ReadField3(iprot thrift.TProtocol) error {

	var _field TDEMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TDEMode(v)
	}
	p.TDEMode = _field
	return nil
}
func (p *DescribeDBInstanceTDEResp) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBTDEStatus, 0, size)
	values := make([]DBTDEStatus, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DBStatus = _field
	return nil
}

func (p *DescribeDBInstanceTDEResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInstanceTDEResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInstanceTDEResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TDEMode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TDEMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBStatus", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DBStatus)); err != nil {
		return err
	}
	for _, v := range p.DBStatus {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInstanceTDEResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInstanceTDEResp(%+v)", *p)

}

func (p *DescribeDBInstanceTDEResp) DeepEqual(ano *DescribeDBInstanceTDEResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TDEStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.TDEMode) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBStatus) {
		return false
	}
	return true
}

func (p *DescribeDBInstanceTDEResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInstanceTDEResp) Field2DeepEqual(src EnableFlag) bool {

	if p.TDEStatus != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceTDEResp) Field3DeepEqual(src TDEMode) bool {

	if p.TDEMode != src {
		return false
	}
	return true
}
func (p *DescribeDBInstanceTDEResp) Field4DeepEqual(src []*DBTDEStatus) bool {

	if len(p.DBStatus) != len(src) {
		return false
	}
	for i, v := range p.DBStatus {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

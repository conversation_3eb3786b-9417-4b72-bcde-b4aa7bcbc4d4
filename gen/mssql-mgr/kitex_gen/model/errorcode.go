// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

var (
	ErrorCodeMap = map[ErrorCode]*ErrorDetail{
		ErrorCode_RequestTimeout: &ErrorDetail{
			HttpStatusCode: 408,
			Description:    "The request processing has failed due to timeout.",
			DescriptionZh:  "请求超时。",
		},
		ErrorCode_OperationFailed: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "Opration Failed: %s",
			DescriptionZh:  "操作执行失败：%s",
		},
		ErrorCode_ServiceUnavailable: &ErrorDetail{
			HttpStatusCode: 503,
			Description:    "The request has failed due to a temporary failure of the server.",
			DescriptionZh:  "当前服务暂时不可用。",
		},
		ErrorCode_InternalError: &ErrorDetail{
			HttpStatusCode: 500,
			Description:    "The request processing has failed due to some unknown error, exception or failure.",
			DescriptionZh:  "内部错误。",
		},
		ErrorCode_InternalErrorFormat: &ErrorDetail{
			HttpStatusCode: 500,
			Description:    "The request processing has failed due to some unknown error, exception or failure. %s",
			DescriptionZh:  "内部错误。%s",
		},
		ErrorCode_ServiceBusy: &ErrorDetail{
			HttpStatusCode: 500,
			Description:    "The system is busy, please try again later.",
			DescriptionZh:  "系统繁忙，请稍后重试。",
		},
		ErrorCode_MissingParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The input parameter %s that is mandatory for processing this request is not supplied.",
			DescriptionZh:  "缺少%s参数。",
		},
		ErrorCode_InvalidParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified parameter %s is not valid.",
			DescriptionZh:  "参数%s值无效。",
		},
		ErrorCode_IllegalParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified parameter %s is illegal.",
			DescriptionZh:  "参数%s值非法。",
		},
		ErrorCode_InvalidSubnetId: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified parameter SubnetId is not valid or not the same zone.",
			DescriptionZh:  "子网参数值无效或者和节点不在相同可用区。",
		},
		ErrorCode_InstanceSpecLessThanMaster: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified parameter %s is not valid, should not be less than the primary instance.",
			DescriptionZh:  "参数%s值无效，不能小于主实例值。",
		},
		ErrorCode_OperationUnsupported_MySQL55: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for MySQL 5.5 instance.",
			DescriptionZh:  "MySQL 5.5版本不支持该操作。",
		},
		ErrorCode_OperationUnsupported_MySQL56: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for MySQL 5.6 instance.",
			DescriptionZh:  "MySQL 5.6版本不支持该操作。",
		},
		ErrorCode_OperationUnsupported_MySQL80: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for MySQL 8.0 instance.",
			DescriptionZh:  "MySQL 8.0版本不支持该操作。",
		},
		ErrorCode_OperationUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for the specified instance.",
			DescriptionZh:  "操作不支持。",
		},
		ErrorCode_OperationUnsupportedFormat: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for the specified instance. %s.",
			DescriptionZh:  "操作不支持。%s。",
		},
		ErrorCode_UnsupportedParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified parameter %s is unsupported.",
			DescriptionZh:  "不支持%s参数。",
		},
		ErrorCode_InvalidParameterFormat: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The input parameter is not valid.",
			DescriptionZh:  "异常参数。",
		},
		ErrorCode_InstanceNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified instance does not exist.",
			DescriptionZh:  "未找到该实例。",
		},
		ErrorCode_VpcIDNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified VpcID does not exist.",
			DescriptionZh:  "未找到该VpcId。",
		},
		ErrorCode_AddressNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified endpoint does not exist specified network type's address.",
			DescriptionZh:  "未找到该网络类型的地址。",
		},
		ErrorCode_SubnetIDNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified SubnetID does not exist.",
			DescriptionZh:  "未找到子网Id。",
		},
		ErrorCode_BackupNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified instance does not exist available backup records.",
			DescriptionZh:  "未找到可用的备份记录。",
		},
		ErrorCode_InstanceIsNotRunning: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance is not running.",
			DescriptionZh:  "当前实例状态不在运行中。",
		},
		ErrorCode_OperationDenied_InstanceIsNotRecycled: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance is not recycled.",
			DescriptionZh:  "当前实例状态不在回收中。",
		},
		ErrorCode_InstanceHasClosed: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance has closed.",
			DescriptionZh:  "当前实例已关停。",
		},
		ErrorCode_InstanceUnchangeable: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance cannot be changed during unchangeable period.",
			DescriptionZh:  "当前实例处于不可变更时间段内。",
		},
		ErrorCode_OperationDenied_BackupJobExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist running backup job.",
			DescriptionZh:  "当前实例正在执行备份任务，无法执行该操作。",
		},
		ErrorCode_OperationDenied_AddressExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist same type address in endpoint.",
			DescriptionZh:  "当前Endpoint已存在相同类型的地址，无法执行该操作。",
		},
		ErrorCode_OperationDenied_MgrBusy: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to mgr busy or exist running job.",
			DescriptionZh:  "正在执行其他任务，无法执行该操作。",
		},
		ErrorCode_OperationDenied_ImportJobExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist running import job.",
			DescriptionZh:  "当前实例正在执行数据导入任务，无法执行该操作。",
		},
		ErrorCode_OperationDenied_OnlineDDLJobExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist running online ddl job.",
			DescriptionZh:  "当前实例正在执行Online DDL任务，无法执行该操作。",
		},
		ErrorCode_OperationDenied_NotExistRunningReadOnlyInstance: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to not exist running read only instance.",
			DescriptionZh:  "当前实例不存在运行中的只读实例，无法执行该操作。",
		},
		ErrorCode_OperationDenied_NotExistRunningReadOnlyNode: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to not exist running read only node.",
			DescriptionZh:  "当前实例不存在运行中的只读节点，无法执行该操作。",
		},
		ErrorCode_OperationDenied_DownloadBackupJobExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist running download backup job.",
			DescriptionZh:  "当前实例正在执行备份下载任务，无法执行该操作。",
		},
		ErrorCode_OperationDenied_OnlineDDLJobFinished: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the specified online ddl job has finished.",
			DescriptionZh:  "当前任务已完成，无法执行该操作。",
		},
		ErrorCode_ChargeOrderExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist charge order.",
			DescriptionZh:  "计费订单已存在。",
		},
		ErrorCode_InvalidAccountName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified AccountName already exists.",
			DescriptionZh:  "账号已存在。",
		},
		ErrorCode_InvalidAccountName_Keyword: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified AccountName is a reserved keyword.",
			DescriptionZh:  "账号名称不能是保留关键字。",
		},
		ErrorCode_InvalidAccountType_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The high authority account already exists.",
			DescriptionZh:  "高权限账号已存在。",
		},
		ErrorCode_InvalidDBName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified DBName already exists.",
			DescriptionZh:  "数据库已存在。",
		},
		ErrorCode_InvalidDBName_Keyword: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified DBName is a reserved keyword.",
			DescriptionZh:  "数据库名称不能是保留关键字。",
		},
		ErrorCode_InvalidSecurityIPList_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified GroupName already exists.",
			DescriptionZh:  "白名单组已存在。",
		},
		ErrorCode_InvalidSecurityIPList: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified IPList is not valid. ip: %s",
			DescriptionZh:  "存在无效的IP地址。ip: %s",
		},
		ErrorCode_SecurityIPListGroupNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified GroupName does not exist.",
			DescriptionZh:  "未找到该白名单组。",
		},
		ErrorCode_OperationDenied_InstanceLockMode: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The operation is not permitted due to lock of instance.",
			DescriptionZh:  "实例锁冲突，请稍后重试。",
		},
		ErrorCode_InstanceNameDuplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The instance name already exists.",
			DescriptionZh:  "实例名称已存在。",
		},
		ErrorCode_NameDuplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The name already exists.",
			DescriptionZh:  "名称已存在。",
		},
		ErrorCode_InstanceStorageSpaceTooSmall: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance StorageSpace cannot be less than %sGB.",
			DescriptionZh:  "实例存储空间不能小于%sGB。",
		},
		ErrorCode_InstanceCPUTooSmall: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance CPU cannot be less than %s core.",
			DescriptionZh:  "实例CPU不能小于%s核。",
		},
		ErrorCode_InstanceMemoryTooSmall: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance memory cannot be less than %sGB.",
			DescriptionZh:  "实例内存不能小于%sGB.",
		},
		ErrorCode_InstanceStorageSpaceTooLarge: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified StorageSpace cannot be greater than %sGB.",
			DescriptionZh:  "实例存储空间不能大于%sGB.",
		},
		ErrorCode_InstanceSpecNotMatchStorage: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The InstanceSpec don't match StorageSpace, if StorageSpace is greater than %sGB, InstanceSpec's coreNum must be greater than %svC.",
			DescriptionZh:  "实例规格与存储空间不匹配，当存储空间不小于%vGB时，实例规格的cpu需大于%vC.",
		},
		ErrorCode_NotSatisfiedMasterChangeCondition: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The active/standby handover condition is not satisfied at present.",
			DescriptionZh:  "切换主节点条件不满足，请稍后重试。",
		},
		ErrorCode_ChangeNodeIsMaster: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The node to be switched is already the master node.",
			DescriptionZh:  "当前节点是主节点。",
		},
		ErrorCode_InvalidSecurityIPList_IPExist: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified IPList exist duplicate IP address.",
			DescriptionZh:  "当前白名单组存在重复IP地址。",
		},
		ErrorCode_InvalidSecurityIPList_IPNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified IPList's IP addresses exceeds the limit.",
			DescriptionZh:  "当前白名单组的IP地址个数超过上限。",
		},
		ErrorCode_InvalidSecurityIPList_IPGroupExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance IP group exceeds the limit.",
			DescriptionZh:  "当前实例的白名单组个数超过上限。",
		},
		ErrorCode_InvalidSecurityGroupId: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified security group ID %s is invalid.",
			DescriptionZh:  "指定的安全组%s无效。",
		},
		ErrorCode_InvalidSecurityGroupId_SGNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The number of specified security group cannot exceed %s.",
			DescriptionZh:  "指定的安全组个数不能超过%s。",
		},
		ErrorCode_OperationDenied_AllowListStatus: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the instance status. The instance(s) is: %s.",
			DescriptionZh:  "所涉及的实例状态不满足，无法执行该操作。不满足实例ID：%s。",
		},
		ErrorCode_OperationDenied_AllowListIpListAndSecurityGroupEmpty: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The IP address and security group related IP address cannot be empty at the same time",
			DescriptionZh:  "IP地址和安全组涉及IP不能同时为空",
		},
		ErrorCode_ReadOnlyInstanceNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance's readonly instance exceeds the limit.The current number of read-only instances is %v and the upper limit of read-only instances is %v.",
			DescriptionZh:  "当前实例的只读实例个数超过上限。当前只读实例个数为%v, 只读实例上限为%v。",
		},
		ErrorCode_ReadOnlyCreateNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The number of created readonly instance API exceeded the upper limit. Procedure The number of created readonly instances is %v, and the upper limit for creating readonly instance is %v.",
			DescriptionZh:  "只读实例接口创建个数超过上限。当前创建只读实例个数为%v, 只读实例接口创建上限为%v。",
		},
		ErrorCode_AccountNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance's account number exceeds the limit.",
			DescriptionZh:  "当前实例的账号个数超过上限。",
		},
		ErrorCode_InvalidTemplateName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The parameter template name already exists.",
			DescriptionZh:  "参数模板名称已存在。",
		},
		ErrorCode_TemplateNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified parameter template does not exist.",
			DescriptionZh:  "未找到该参数模板。",
		},
		ErrorCode_TemplateTypeNotMatch: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified parameter template's type is not math to current instance.",
			DescriptionZh:  "当前参数模板类型不匹配。",
		},
		ErrorCode_OperationDenied_SystemTemplate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the specified parameter template is system parameter template.",
			DescriptionZh:  "当前参数模板是系统参数模板，无法执行该操作。",
		},
		ErrorCode_ProjectNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified parameter ProjectID does not exist.",
			DescriptionZh:  "未找到该项目。",
		},
		ErrorCode_OperationDenied_SystemGroup: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the specified group is system default group.",
			DescriptionZh:  "当前白名单组是系统默认组，无法执行该操作。",
		},
		ErrorCode_OperationDenied_TDEEnabled: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the KMS service has not enabled.",
			DescriptionZh:  "已开启TDE功能，无法执行该操作。",
		},
		ErrorCode_OperationDenied_KMSDisabled: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the KMS service has not enabled.",
			DescriptionZh:  "KMS服务未开启，无法执行该操作。",
		},
		ErrorCode_RunningOrderNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified instance's running order num exceeds the limit.",
			DescriptionZh:  "当前运行中的任务数超过上限。",
		},
		ErrorCode_AccountPrivillegeInsufficient: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the account does not have sufficient privillege.",
			DescriptionZh:  "账号权限不足。",
		},
		ErrorCode_SQLSyntaxErrorOrUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified SQL statement have syntax error or unsupported.",
			DescriptionZh:  "当前SQL语句有语法错误，或者不支持该SQL。",
		},
		ErrorCode_DatabaseNotExist: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified database name does not exist.",
			DescriptionZh:  "未找到该数据库。",
		},
		ErrorCode_AccountNotExist: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified account name does not exist.",
			DescriptionZh:  "未找到该账号。",
		},
		ErrorCode_ReadOnlyInstanceNotExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the specified instance not added read only instance.",
			DescriptionZh:  "当前实例不存在只读实例，无法执行该操作。",
		},
		ErrorCode_ReadOnlyNodeNotExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the specified instance not added read only node.",
			DescriptionZh:  "当前实例不存在只读节点，无法执行该操作。",
		},
		ErrorCode_OperationDenied_ExistLargeDelaySlaveNode: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist slave node with large delay time.",
			DescriptionZh:  "备节点延迟时间较大，无法执行该操作。",
		},
		ErrorCode_OperationDenied_NotExistLowDelaySlaveNode: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to not exist slave node with low delay time.",
			DescriptionZh:  "不存在延迟时间较小的备节点，无法执行该操作。",
		},
		ErrorCode_OperationDenied_InstanceMaintaining: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the instance being maintained.",
			DescriptionZh:  "当前实例在维护中，无法执行该操作。",
		},
		ErrorCode_OperationDenied_InstanceClosed: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the instance has overdue or expired.",
			DescriptionZh:  "当前实例已关停，无法执行该操作。",
		},
		ErrorCode_OperationDenied_InstancePrepaid: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the instance charge type is prepaid.",
			DescriptionZh:  "当前实例计费类型是预付费，无法执行该操作。",
		},
		ErrorCode_InsufficientBalance: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to your account does not have enough balance.",
			DescriptionZh:  "账户余额不足。",
		},
		ErrorCode_OperationDenied_ChargeDisabled: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to charge service disabled.",
			DescriptionZh:  "计费服务未开启，无法执行该操作。",
		},
		ErrorCode_Forbidden_Authentication: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "This operation is forbidden by Realname Authentication system.",
			DescriptionZh:  "账户未实名认证。",
		},
		ErrorCode_MaintainTime_ConflictWith_BackupTime: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified maintainable time conflicts with automatic backup time %s.",
			DescriptionZh:  "可维护时间段不可在自动备份时间段%s内。",
		},
		ErrorCode_QuotaExceeded_CreateInstance: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The quota of create instance exceeds.",
			DescriptionZh:  "超过配额限制。",
		},
		ErrorCode_OperationDenied_ResourceSoldOut: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The requested resource is sold out in the specified zone; try other types of resources or other regions and zones.",
			DescriptionZh:  "当前可用区资源不足。",
		},
		ErrorCode_PrecheckFailed_SQLUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to sql unsupported.",
			DescriptionZh:  "SQL语句不支持。",
		},
		ErrorCode_PrecheckFailed_DiskSpaceInsufficient: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to disk space insufficient.",
			DescriptionZh:  "磁盘空间不足。",
		},
		ErrorCode_OperationDenied_DiskSpaceInsufficient: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to disk space insufficient.",
			DescriptionZh:  "磁盘空间不足。",
		},
		ErrorCode_PrecheckFailed_DryRunFailed: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to dry run failed.",
			DescriptionZh:  "预执行失败。",
		},
		ErrorCode_DryRunOperation: &ErrorDetail{
			HttpStatusCode: 412,
			Description:    "The request has been validated by the DryRunOperation.",
			DescriptionZh:  "请求已通过DryRun验证。",
		},
		ErrorCode_PrecheckFailed_NoPrimaryKey: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to table missing primary key.",
			DescriptionZh:  "表缺少主键。",
		},
		ErrorCode_PrecheckFailed_DuplicateKeyOrColumn: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to exist duplicate key or column.",
			DescriptionZh:  "存在重复的索引或列。",
		},
		ErrorCode_PrecheckFailed_KeyOrColumnNotExists: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to key or column not exists.",
			DescriptionZh:  "索引或列不存在。",
		},
		ErrorCode_PrecheckFailed_SQLSyntaxError: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to sql syntax error.",
			DescriptionZh:  "SQL语法错误。",
		},
		ErrorCode_BackupAmountOverTheLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the db or table amount over the limit.",
			DescriptionZh:  "数据库或表数量超过上限。",
		},
		ErrorCode_InvalidAccountPasssword_Weak: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The specified AccountPassword is a weak password.",
			DescriptionZh:  "账号密码强度太弱。",
		},
		ErrorCode_EndpointNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Description:    "The specified endpoint does not exist.",
			DescriptionZh:  "未找到该Endpoint。",
		},
		ErrorCode_UserBackupAmountOverTheLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the user backup amount over the limit.",
			DescriptionZh:  "手动备份的数量超过上限",
		},
		ErrorCode_OperationDenied_IncrBackupUnsupportRecovery: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to incr backup unsupport recovery.",
			DescriptionZh:  "增量备份无法执行该操作。",
		},
		ErrorCode_OperationDenied_AGSyncStatusAbnormal: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to ag sync status abnormal.",
			DescriptionZh:  "实例同步状态异常，无法执行该操作。",
		},
		ErrorCode_OperationDenied_DBStatusAbnormal: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to db status abnormal.",
			DescriptionZh:  "实例数据库状态异常，无法执行该操作。",
		},
		ErrorCode_OperationDenied_LoginSyncStatusAbnormal: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to login synchronization status abnormal.",
			DescriptionZh:  "实例权限同步状态异常，无法执行该操作。",
		},
		ErrorCode_OperationUnsupported_MSSQLDBVersion: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is unsupported for MSSQL version.",
			DescriptionZh:  "MSSQL当前版本不支持该操作。",
		},
		ErrorCode_OperationExceedLimit: &ErrorDetail{
			HttpStatusCode: 500,
			Description:    "please wait %s minutes before performing the operation",
			DescriptionZh:  "请等待%s分钟后操作。",
		},
		ErrorCode_TosRestoreTaskNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 500,
			Description:    "Too many tasks are being executed. Please submit them later",
			DescriptionZh:  "正在执行的任务数量过多,请稍后再提交",
		},
		ErrorCode_TosRestoreTaskDbVerifyFailed: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "db verify failed, reason:%s",
			DescriptionZh:  "%s",
		},
		ErrorCode_OperationDuplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "操作重复",
			DescriptionZh:  "operation duplicate",
		},
		ErrorCode_QuotaExceeded_RestoreDB: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The quota of restore db exceeds",
			DescriptionZh:  "恢复数据库超过配额限制",
		},
		ErrorCode_InstanceStorageSpaceLargeThanReadonly: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The storage space large than that of the read-only node",
			DescriptionZh:  "主实例存储空间不能大于只读实例存储空间",
		},
		ErrorCode_InstanceTypeNotSupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The instance type does not support the current operation, instance type is %s",
			DescriptionZh:  "实例类型不支持当前操作，实例类型为%s",
		},
		ErrorCode_QuotaExceeded_CreateDB: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The quota of create db exceeds",
			DescriptionZh:  "创建数据库超过配额限制",
		},
		ErrorCode_InstanceStatusNotSupported: &ErrorDetail{
			HttpStatusCode: 403,
			Description:    "The instance status does not support the current operation, instance status is %s",
			DescriptionZh:  "实例状态不支持当前操作，实例状态为%s",
		},
		ErrorCode_OperationDenied_TempModify_ChargeTypeUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to charge type not supported.",
			DescriptionZh:  "当前计费类型不支持该操作。",
		},
		ErrorCode_OperationDenied_TempModify_ReduceNodeUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the temporary spec upgrade not yet due.",
			DescriptionZh:  "临时升配期间不能删除/降级只读节点。",
		},
		ErrorCode_OperationDenied_TempModify_ReduceStorageUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the temporary spec upgrade not yet due.",
			DescriptionZh:  "临时升配期间不能降低存储空间。",
		},
		ErrorCode_OperationDenied_TempModify_ReduceSpecUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the temporary spec upgrade not yet due.",
			DescriptionZh:  "临时升配期间不能降低规格。",
		},
		ErrorCode_OperationDenied_TempModify_InstanceDeleteUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the temporary spec upgrade not yet due.",
			DescriptionZh:  "临时升配期间不能退订实例。",
		},
		ErrorCode_OperationDenied_TempModify_InstanceReclaimTimeLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the recovery time larger than due time.",
			DescriptionZh:  "还原时间不能大于到期时间。",
		},
		ErrorCode_OperationDenied_TempModify_InstanceContinueTimeLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to The recovery time must be later than the current recovery time.",
			DescriptionZh:  "还原时间必须比当前还原时间大。",
		},
		ErrorCode_OperationDenied_TempModify_InstanceDurationTimeLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the temporary upgrade duration too long.",
			DescriptionZh:  "临时升配持续时间过长。",
		},
		ErrorCode_OperationDenied_SSLStatus: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to incorrect ssl status.",
			DescriptionZh:  "当前实例的SSL状态不支持该操作。",
		},
		ErrorCode_OperationDenied_SameInstanceAllowListIP: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the same allowlist ip.",
			DescriptionZh:  "白名单IP一致，不需要同步",
		},
		ErrorCode_OperationDenied_InstanceAllowListBindRelationship: &ErrorDetail{
			HttpStatusCode: 400,
			Description:    "The operation is not permitted due to the instance allowlist bind relationship incorrect.",
			DescriptionZh:  "实例白名单绑定关系不正确",
		},
		ErrorCode_OperationDenied_TooManyRequests: &ErrorDetail{
			HttpStatusCode: 429,
			Description:    "The operation is requested too frequently, please wait %s minutes before performing the operation",
			DescriptionZh:  "请求过于频繁，请稍等%s分钟后重试。",
		},
		ErrorCode_OperationDenied_TosRestoreTaskNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 429,
			Description:    "Too many tasks are being executed. Please submit them later",
			DescriptionZh:  "正在执行的任务数量过多,请稍后再提交",
		},
		ErrorCode_OperationDenied_QpsRateLimit: &ErrorDetail{
			HttpStatusCode: 429,
			Description:    "The operation is not permitted due to qps rate limit, Please retry later",
			DescriptionZh:  "请求过于频繁，请稍等后再重试。",
		},
	}
)

type ErrorCode int64

const (
	ErrorCode_InternalError                                        ErrorCode = 50000
	ErrorCode_ServiceUnavailable                                   ErrorCode = 50001
	ErrorCode_ServiceBusy                                          ErrorCode = 50002
	ErrorCode_InternalErrorFormat                                  ErrorCode = 50003
	ErrorCode_MissingParameter                                     ErrorCode = 40001
	ErrorCode_InvalidParameter                                     ErrorCode = 40002
	ErrorCode_UnsupportedParameter                                 ErrorCode = 40003
	ErrorCode_InvalidParameterFormat                               ErrorCode = 40004
	ErrorCode_InstanceNotFound                                     ErrorCode = 40005
	ErrorCode_InstanceIsNotRunning                                 ErrorCode = 40006
	ErrorCode_InstanceUnchangeable                                 ErrorCode = 40007
	ErrorCode_OperationDenied_BackupJobExists                      ErrorCode = 40008
	ErrorCode_OperationDenied_InstanceLockMode                     ErrorCode = 40009
	ErrorCode_InstanceNameDuplicate                                ErrorCode = 40010
	ErrorCode_NameDuplicate                                        ErrorCode = 40011
	ErrorCode_InstanceStorageSpaceTooSmall                         ErrorCode = 40012
	ErrorCode_VpcIDNotFound                                        ErrorCode = 40013
	ErrorCode_InstanceSpecLessThanMaster                           ErrorCode = 40014
	ErrorCode_OperationUnsupported_MySQL55                         ErrorCode = 40015
	ErrorCode_OperationUnsupported                                 ErrorCode = 40016
	ErrorCode_OperationDenied_ImportJobExists                      ErrorCode = 40017
	ErrorCode_OperationUnsupported_MySQL56                         ErrorCode = 40018
	ErrorCode_ReadOnlyInstanceNotExists                            ErrorCode = 40019
	ErrorCode_ReadOnlyInstanceNumExceedLimit                       ErrorCode = 40020
	ErrorCode_OperationDenied_ExistLargeDelaySlaveNode             ErrorCode = 40021
	ErrorCode_OperationDenied_NotExistLowDelaySlaveNode            ErrorCode = 40022
	ErrorCode_InstanceHasClosed                                    ErrorCode = 40023
	ErrorCode_InsufficientBalance                                  ErrorCode = 40024
	ErrorCode_Forbidden_Authentication                             ErrorCode = 40025
	ErrorCode_OperationDenied_InstanceMaintaining                  ErrorCode = 40026
	ErrorCode_OperationDenied_InstanceClosed                       ErrorCode = 40027
	ErrorCode_OperationDenied_InstancePrepaid                      ErrorCode = 40028
	ErrorCode_OperationDenied_InstanceIsNotRecycled                ErrorCode = 40029
	ErrorCode_BackupNotFound                                       ErrorCode = 40030
	ErrorCode_OperationDenied_ChargeDisabled                       ErrorCode = 40031
	ErrorCode_ChargeOrderExists                                    ErrorCode = 40032
	ErrorCode_QuotaExceeded_CreateInstance                         ErrorCode = 40033
	ErrorCode_OperationDenied_ResourceSoldOut                      ErrorCode = 40034
	ErrorCode_OperationDenied_DiskSpaceInsufficient                ErrorCode = 40035
	ErrorCode_InstanceStorageSpaceTooLarge                         ErrorCode = 40036
	ErrorCode_InstanceCPUTooSmall                                  ErrorCode = 40037
	ErrorCode_InstanceMemoryTooSmall                               ErrorCode = 40038
	ErrorCode_OperationDenied_NotExistRunningReadOnlyInstance      ErrorCode = 40039
	ErrorCode_PublicConnectionServiceAlreadyExist                  ErrorCode = 40040
	ErrorCode_SubnetIDNotFound                                     ErrorCode = 40041
	ErrorCode_ReadOnlyNodeNotExists                                ErrorCode = 40042
	ErrorCode_OperationDenied_NotExistRunningReadOnlyNode          ErrorCode = 40043
	ErrorCode_OperationDenied_DownloadBackupJobExists              ErrorCode = 40044
	ErrorCode_InvalidSubnetId                                      ErrorCode = 40045
	ErrorCode_UserBackupAmountOverTheLimit                         ErrorCode = 40046
	ErrorCode_OperationDenied_IncrBackupUnsupportRecovery          ErrorCode = 40047
	ErrorCode_OperationUnsupported_MySQL80                         ErrorCode = 40048
	ErrorCode_OperationDenied_AGSyncStatusAbnormal                 ErrorCode = 40049
	ErrorCode_OperationExceedLimit                                 ErrorCode = 40050
	ErrorCode_TosRestoreTaskNumExceedLimit                         ErrorCode = 40051
	ErrorCode_TosRestoreTaskDbVerifyFailed                         ErrorCode = 40052
	ErrorCode_OperationDuplicate                                   ErrorCode = 40053
	ErrorCode_QuotaExceeded_RestoreDB                              ErrorCode = 40054
	ErrorCode_OperationDenied_DBStatusAbnormal                     ErrorCode = 40055
	ErrorCode_OperationUnsupported_MSSQLDBVersion                  ErrorCode = 40056
	ErrorCode_InstanceStorageSpaceLargeThanReadonly                ErrorCode = 40057
	ErrorCode_InstanceTypeNotSupported                             ErrorCode = 40058
	ErrorCode_OperationUnsupportedFormat                           ErrorCode = 40059
	ErrorCode_ReadOnlyCreateNumExceedLimit                         ErrorCode = 40060
	ErrorCode_QuotaExceeded_CreateDB                               ErrorCode = 40061
	ErrorCode_InstanceStatusNotSupported                           ErrorCode = 40062
	ErrorCode_IllegalParameter                                     ErrorCode = 40063
	ErrorCode_InstanceSpecNotMatchStorage                          ErrorCode = 40064
	ErrorCode_OperationDenied_LoginSyncStatusAbnormal              ErrorCode = 40065
	ErrorCode_OperationDenied_TooManyRequests                      ErrorCode = 40066
	ErrorCode_OperationDenied_TosRestoreTaskNumExceedLimit         ErrorCode = 40067
	ErrorCode_OperationDenied_QpsRateLimit                         ErrorCode = 40068
	ErrorCode_InvalidAccountName_Duplicate                         ErrorCode = 41001
	ErrorCode_InvalidAccountName_Keyword                           ErrorCode = 41002
	ErrorCode_InvalidAccountType_Duplicate                         ErrorCode = 41003
	ErrorCode_InvalidDBName_Duplicate                              ErrorCode = 41004
	ErrorCode_InvalidDBName_Keyword                                ErrorCode = 41005
	ErrorCode_AccountNumExceedLimit                                ErrorCode = 41006
	ErrorCode_InvalidAccountPasssword_Weak                         ErrorCode = 41007
	ErrorCode_InvalidSecurityIPList_Duplicate                      ErrorCode = 42001
	ErrorCode_InvalidSecurityIPList                                ErrorCode = 42002
	ErrorCode_SecurityIPListGroupNotFound                          ErrorCode = 42003
	ErrorCode_InvalidSecurityIPList_IPExist                        ErrorCode = 42004
	ErrorCode_InvalidSecurityIPList_IPNumExceedLimit               ErrorCode = 42005
	ErrorCode_OperationDenied_SystemGroup                          ErrorCode = 42006
	ErrorCode_OperationDenied_TDEEnabled                           ErrorCode = 42007
	ErrorCode_OperationDenied_KMSDisabled                          ErrorCode = 42008
	ErrorCode_InvalidSecurityIPList_IPGroupExceedLimit             ErrorCode = 42009
	ErrorCode_InvalidAllowListVersion                              ErrorCode = 42010
	ErrorCode_InvalidSecurityGroupId                               ErrorCode = 42011
	ErrorCode_InvalidSecurityGroupId_SGNumExceedLimit              ErrorCode = 42012
	ErrorCode_OperationDenied_AllowListStatus                      ErrorCode = 42013
	ErrorCode_OperationDenied_AllowListIpListAndSecurityGroupEmpty ErrorCode = 42014
	ErrorCode_OperationDenied_SSLStatus                            ErrorCode = 42015
	ErrorCode_OperationDenied_InstanceAllowListBindRelationship    ErrorCode = 42016
	ErrorCode_OperationDenied_SameInstanceAllowListIP              ErrorCode = 42017
	ErrorCode_NotSatisfiedMasterChangeCondition                    ErrorCode = 43001
	ErrorCode_ChangeNodeIsMaster                                   ErrorCode = 43002
	ErrorCode_InvalidTemplateName_Duplicate                        ErrorCode = 44001
	ErrorCode_TemplateNotFound                                     ErrorCode = 44002
	ErrorCode_TemplateTypeNotMatch                                 ErrorCode = 44003
	ErrorCode_OperationDenied_SystemTemplate                       ErrorCode = 44004
	ErrorCode_BackupAmountOverTheLimit                             ErrorCode = 44005
	ErrorCode_ProjectNotFound                                      ErrorCode = 44006
	ErrorCode_RunningOrderNumExceedLimit                           ErrorCode = 45001
	ErrorCode_AccountPrivillegeInsufficient                        ErrorCode = 45002
	ErrorCode_SQLSyntaxErrorOrUnsupported                          ErrorCode = 45003
	ErrorCode_DatabaseNotExist                                     ErrorCode = 45004
	ErrorCode_AccountNotExist                                      ErrorCode = 45005
	ErrorCode_PrecheckFailed_SQLUnsupported                        ErrorCode = 45006
	ErrorCode_PrecheckFailed_DiskSpaceInsufficient                 ErrorCode = 45007
	ErrorCode_PrecheckFailed_DryRunFailed                          ErrorCode = 45008
	ErrorCode_OrderNotExist                                        ErrorCode = 45009
	ErrorCode_OperationDenied_OnlineDDLJobExists                   ErrorCode = 45010
	ErrorCode_PrecheckFailed_NoPrimaryKey                          ErrorCode = 45011
	ErrorCode_PrecheckFailed_DuplicateKeyOrColumn                  ErrorCode = 45012
	ErrorCode_PrecheckFailed_KeyOrColumnNotExists                  ErrorCode = 45013
	ErrorCode_OperationDenied_OnlineDDLJobFinished                 ErrorCode = 45014
	ErrorCode_PrecheckFailed_SQLSyntaxError                        ErrorCode = 45015
	ErrorCode_DryRunOperation                                      ErrorCode = 45016
	ErrorCode_OperationDenied_MgrBusy                              ErrorCode = 46001
	ErrorCode_MaintainTime_ConflictWith_BackupTime                 ErrorCode = 46002
	ErrorCode_EndpointNotFound                                     ErrorCode = 47001
	ErrorCode_OperationDenied_AddressExists                        ErrorCode = 47002
	ErrorCode_AddressNotFound                                      ErrorCode = 47003
	ErrorCode_OperationFailed                                      ErrorCode = 47004
	ErrorCode_RequestTimeout                                       ErrorCode = 47005
	ErrorCode_OperationDenied_TempModify_ChargeTypeUnsupported     ErrorCode = 49001
	ErrorCode_OperationDenied_TempModify_ReduceNodeUnsupported     ErrorCode = 49002
	ErrorCode_OperationDenied_TempModify_ReduceStorageUnsupported  ErrorCode = 49003
	ErrorCode_OperationDenied_TempModify_ReduceSpecUnsupported     ErrorCode = 49004
	ErrorCode_OperationDenied_TempModify_InstanceDeleteUnsupported ErrorCode = 49005
	ErrorCode_OperationDenied_TempModify_InstanceReclaimTimeLimit  ErrorCode = 49006
	ErrorCode_OperationDenied_TempModify_InstanceContinueTimeLimit ErrorCode = 49007
	ErrorCode_OperationDenied_TempModify_InstanceDurationTimeLimit ErrorCode = 49008
)

func (p ErrorCode) String() string {
	switch p {
	case ErrorCode_InternalError:
		return "InternalError"
	case ErrorCode_ServiceUnavailable:
		return "ServiceUnavailable"
	case ErrorCode_ServiceBusy:
		return "ServiceBusy"
	case ErrorCode_InternalErrorFormat:
		return "InternalErrorFormat"
	case ErrorCode_MissingParameter:
		return "MissingParameter"
	case ErrorCode_InvalidParameter:
		return "InvalidParameter"
	case ErrorCode_UnsupportedParameter:
		return "UnsupportedParameter"
	case ErrorCode_InvalidParameterFormat:
		return "InvalidParameterFormat"
	case ErrorCode_InstanceNotFound:
		return "InstanceNotFound"
	case ErrorCode_InstanceIsNotRunning:
		return "InstanceIsNotRunning"
	case ErrorCode_InstanceUnchangeable:
		return "InstanceUnchangeable"
	case ErrorCode_OperationDenied_BackupJobExists:
		return "OperationDenied_BackupJobExists"
	case ErrorCode_OperationDenied_InstanceLockMode:
		return "OperationDenied_InstanceLockMode"
	case ErrorCode_InstanceNameDuplicate:
		return "InstanceNameDuplicate"
	case ErrorCode_NameDuplicate:
		return "NameDuplicate"
	case ErrorCode_InstanceStorageSpaceTooSmall:
		return "InstanceStorageSpaceTooSmall"
	case ErrorCode_VpcIDNotFound:
		return "VpcIDNotFound"
	case ErrorCode_InstanceSpecLessThanMaster:
		return "InstanceSpecLessThanMaster"
	case ErrorCode_OperationUnsupported_MySQL55:
		return "OperationUnsupported_MySQL55"
	case ErrorCode_OperationUnsupported:
		return "OperationUnsupported"
	case ErrorCode_OperationDenied_ImportJobExists:
		return "OperationDenied_ImportJobExists"
	case ErrorCode_OperationUnsupported_MySQL56:
		return "OperationUnsupported_MySQL56"
	case ErrorCode_ReadOnlyInstanceNotExists:
		return "ReadOnlyInstanceNotExists"
	case ErrorCode_ReadOnlyInstanceNumExceedLimit:
		return "ReadOnlyInstanceNumExceedLimit"
	case ErrorCode_OperationDenied_ExistLargeDelaySlaveNode:
		return "OperationDenied_ExistLargeDelaySlaveNode"
	case ErrorCode_OperationDenied_NotExistLowDelaySlaveNode:
		return "OperationDenied_NotExistLowDelaySlaveNode"
	case ErrorCode_InstanceHasClosed:
		return "InstanceHasClosed"
	case ErrorCode_InsufficientBalance:
		return "InsufficientBalance"
	case ErrorCode_Forbidden_Authentication:
		return "Forbidden_Authentication"
	case ErrorCode_OperationDenied_InstanceMaintaining:
		return "OperationDenied_InstanceMaintaining"
	case ErrorCode_OperationDenied_InstanceClosed:
		return "OperationDenied_InstanceClosed"
	case ErrorCode_OperationDenied_InstancePrepaid:
		return "OperationDenied_InstancePrepaid"
	case ErrorCode_OperationDenied_InstanceIsNotRecycled:
		return "OperationDenied_InstanceIsNotRecycled"
	case ErrorCode_BackupNotFound:
		return "BackupNotFound"
	case ErrorCode_OperationDenied_ChargeDisabled:
		return "OperationDenied_ChargeDisabled"
	case ErrorCode_ChargeOrderExists:
		return "ChargeOrderExists"
	case ErrorCode_QuotaExceeded_CreateInstance:
		return "QuotaExceeded_CreateInstance"
	case ErrorCode_OperationDenied_ResourceSoldOut:
		return "OperationDenied_ResourceSoldOut"
	case ErrorCode_OperationDenied_DiskSpaceInsufficient:
		return "OperationDenied_DiskSpaceInsufficient"
	case ErrorCode_InstanceStorageSpaceTooLarge:
		return "InstanceStorageSpaceTooLarge"
	case ErrorCode_InstanceCPUTooSmall:
		return "InstanceCPUTooSmall"
	case ErrorCode_InstanceMemoryTooSmall:
		return "InstanceMemoryTooSmall"
	case ErrorCode_OperationDenied_NotExistRunningReadOnlyInstance:
		return "OperationDenied_NotExistRunningReadOnlyInstance"
	case ErrorCode_PublicConnectionServiceAlreadyExist:
		return "PublicConnectionServiceAlreadyExist"
	case ErrorCode_SubnetIDNotFound:
		return "SubnetIDNotFound"
	case ErrorCode_ReadOnlyNodeNotExists:
		return "ReadOnlyNodeNotExists"
	case ErrorCode_OperationDenied_NotExistRunningReadOnlyNode:
		return "OperationDenied_NotExistRunningReadOnlyNode"
	case ErrorCode_OperationDenied_DownloadBackupJobExists:
		return "OperationDenied_DownloadBackupJobExists"
	case ErrorCode_InvalidSubnetId:
		return "InvalidSubnetId"
	case ErrorCode_UserBackupAmountOverTheLimit:
		return "UserBackupAmountOverTheLimit"
	case ErrorCode_OperationDenied_IncrBackupUnsupportRecovery:
		return "OperationDenied_IncrBackupUnsupportRecovery"
	case ErrorCode_OperationUnsupported_MySQL80:
		return "OperationUnsupported_MySQL80"
	case ErrorCode_OperationDenied_AGSyncStatusAbnormal:
		return "OperationDenied_AGSyncStatusAbnormal"
	case ErrorCode_OperationExceedLimit:
		return "OperationExceedLimit"
	case ErrorCode_TosRestoreTaskNumExceedLimit:
		return "TosRestoreTaskNumExceedLimit"
	case ErrorCode_TosRestoreTaskDbVerifyFailed:
		return "TosRestoreTaskDbVerifyFailed"
	case ErrorCode_OperationDuplicate:
		return "OperationDuplicate"
	case ErrorCode_QuotaExceeded_RestoreDB:
		return "QuotaExceeded_RestoreDB"
	case ErrorCode_OperationDenied_DBStatusAbnormal:
		return "OperationDenied_DBStatusAbnormal"
	case ErrorCode_OperationUnsupported_MSSQLDBVersion:
		return "OperationUnsupported_MSSQLDBVersion"
	case ErrorCode_InstanceStorageSpaceLargeThanReadonly:
		return "InstanceStorageSpaceLargeThanReadonly"
	case ErrorCode_InstanceTypeNotSupported:
		return "InstanceTypeNotSupported"
	case ErrorCode_OperationUnsupportedFormat:
		return "OperationUnsupportedFormat"
	case ErrorCode_ReadOnlyCreateNumExceedLimit:
		return "ReadOnlyCreateNumExceedLimit"
	case ErrorCode_QuotaExceeded_CreateDB:
		return "QuotaExceeded_CreateDB"
	case ErrorCode_InstanceStatusNotSupported:
		return "InstanceStatusNotSupported"
	case ErrorCode_IllegalParameter:
		return "IllegalParameter"
	case ErrorCode_InstanceSpecNotMatchStorage:
		return "InstanceSpecNotMatchStorage"
	case ErrorCode_OperationDenied_LoginSyncStatusAbnormal:
		return "OperationDenied_LoginSyncStatusAbnormal"
	case ErrorCode_OperationDenied_TooManyRequests:
		return "OperationDenied_TooManyRequests"
	case ErrorCode_OperationDenied_TosRestoreTaskNumExceedLimit:
		return "OperationDenied_TosRestoreTaskNumExceedLimit"
	case ErrorCode_OperationDenied_QpsRateLimit:
		return "OperationDenied_QpsRateLimit"
	case ErrorCode_InvalidAccountName_Duplicate:
		return "InvalidAccountName_Duplicate"
	case ErrorCode_InvalidAccountName_Keyword:
		return "InvalidAccountName_Keyword"
	case ErrorCode_InvalidAccountType_Duplicate:
		return "InvalidAccountType_Duplicate"
	case ErrorCode_InvalidDBName_Duplicate:
		return "InvalidDBName_Duplicate"
	case ErrorCode_InvalidDBName_Keyword:
		return "InvalidDBName_Keyword"
	case ErrorCode_AccountNumExceedLimit:
		return "AccountNumExceedLimit"
	case ErrorCode_InvalidAccountPasssword_Weak:
		return "InvalidAccountPasssword_Weak"
	case ErrorCode_InvalidSecurityIPList_Duplicate:
		return "InvalidSecurityIPList_Duplicate"
	case ErrorCode_InvalidSecurityIPList:
		return "InvalidSecurityIPList"
	case ErrorCode_SecurityIPListGroupNotFound:
		return "SecurityIPListGroupNotFound"
	case ErrorCode_InvalidSecurityIPList_IPExist:
		return "InvalidSecurityIPList_IPExist"
	case ErrorCode_InvalidSecurityIPList_IPNumExceedLimit:
		return "InvalidSecurityIPList_IPNumExceedLimit"
	case ErrorCode_OperationDenied_SystemGroup:
		return "OperationDenied_SystemGroup"
	case ErrorCode_OperationDenied_TDEEnabled:
		return "OperationDenied_TDEEnabled"
	case ErrorCode_OperationDenied_KMSDisabled:
		return "OperationDenied_KMSDisabled"
	case ErrorCode_InvalidSecurityIPList_IPGroupExceedLimit:
		return "InvalidSecurityIPList_IPGroupExceedLimit"
	case ErrorCode_InvalidAllowListVersion:
		return "InvalidAllowListVersion"
	case ErrorCode_InvalidSecurityGroupId:
		return "InvalidSecurityGroupId"
	case ErrorCode_InvalidSecurityGroupId_SGNumExceedLimit:
		return "InvalidSecurityGroupId_SGNumExceedLimit"
	case ErrorCode_OperationDenied_AllowListStatus:
		return "OperationDenied_AllowListStatus"
	case ErrorCode_OperationDenied_AllowListIpListAndSecurityGroupEmpty:
		return "OperationDenied_AllowListIpListAndSecurityGroupEmpty"
	case ErrorCode_OperationDenied_SSLStatus:
		return "OperationDenied_SSLStatus"
	case ErrorCode_OperationDenied_InstanceAllowListBindRelationship:
		return "OperationDenied_InstanceAllowListBindRelationship"
	case ErrorCode_OperationDenied_SameInstanceAllowListIP:
		return "OperationDenied_SameInstanceAllowListIP"
	case ErrorCode_NotSatisfiedMasterChangeCondition:
		return "NotSatisfiedMasterChangeCondition"
	case ErrorCode_ChangeNodeIsMaster:
		return "ChangeNodeIsMaster"
	case ErrorCode_InvalidTemplateName_Duplicate:
		return "InvalidTemplateName_Duplicate"
	case ErrorCode_TemplateNotFound:
		return "TemplateNotFound"
	case ErrorCode_TemplateTypeNotMatch:
		return "TemplateTypeNotMatch"
	case ErrorCode_OperationDenied_SystemTemplate:
		return "OperationDenied_SystemTemplate"
	case ErrorCode_BackupAmountOverTheLimit:
		return "BackupAmountOverTheLimit"
	case ErrorCode_ProjectNotFound:
		return "ProjectNotFound"
	case ErrorCode_RunningOrderNumExceedLimit:
		return "RunningOrderNumExceedLimit"
	case ErrorCode_AccountPrivillegeInsufficient:
		return "AccountPrivillegeInsufficient"
	case ErrorCode_SQLSyntaxErrorOrUnsupported:
		return "SQLSyntaxErrorOrUnsupported"
	case ErrorCode_DatabaseNotExist:
		return "DatabaseNotExist"
	case ErrorCode_AccountNotExist:
		return "AccountNotExist"
	case ErrorCode_PrecheckFailed_SQLUnsupported:
		return "PrecheckFailed_SQLUnsupported"
	case ErrorCode_PrecheckFailed_DiskSpaceInsufficient:
		return "PrecheckFailed_DiskSpaceInsufficient"
	case ErrorCode_PrecheckFailed_DryRunFailed:
		return "PrecheckFailed_DryRunFailed"
	case ErrorCode_OrderNotExist:
		return "OrderNotExist"
	case ErrorCode_OperationDenied_OnlineDDLJobExists:
		return "OperationDenied_OnlineDDLJobExists"
	case ErrorCode_PrecheckFailed_NoPrimaryKey:
		return "PrecheckFailed_NoPrimaryKey"
	case ErrorCode_PrecheckFailed_DuplicateKeyOrColumn:
		return "PrecheckFailed_DuplicateKeyOrColumn"
	case ErrorCode_PrecheckFailed_KeyOrColumnNotExists:
		return "PrecheckFailed_KeyOrColumnNotExists"
	case ErrorCode_OperationDenied_OnlineDDLJobFinished:
		return "OperationDenied_OnlineDDLJobFinished"
	case ErrorCode_PrecheckFailed_SQLSyntaxError:
		return "PrecheckFailed_SQLSyntaxError"
	case ErrorCode_DryRunOperation:
		return "DryRunOperation"
	case ErrorCode_OperationDenied_MgrBusy:
		return "OperationDenied_MgrBusy"
	case ErrorCode_MaintainTime_ConflictWith_BackupTime:
		return "MaintainTime_ConflictWith_BackupTime"
	case ErrorCode_EndpointNotFound:
		return "EndpointNotFound"
	case ErrorCode_OperationDenied_AddressExists:
		return "OperationDenied_AddressExists"
	case ErrorCode_AddressNotFound:
		return "AddressNotFound"
	case ErrorCode_OperationFailed:
		return "OperationFailed"
	case ErrorCode_RequestTimeout:
		return "RequestTimeout"
	case ErrorCode_OperationDenied_TempModify_ChargeTypeUnsupported:
		return "OperationDenied_TempModify_ChargeTypeUnsupported"
	case ErrorCode_OperationDenied_TempModify_ReduceNodeUnsupported:
		return "OperationDenied_TempModify_ReduceNodeUnsupported"
	case ErrorCode_OperationDenied_TempModify_ReduceStorageUnsupported:
		return "OperationDenied_TempModify_ReduceStorageUnsupported"
	case ErrorCode_OperationDenied_TempModify_ReduceSpecUnsupported:
		return "OperationDenied_TempModify_ReduceSpecUnsupported"
	case ErrorCode_OperationDenied_TempModify_InstanceDeleteUnsupported:
		return "OperationDenied_TempModify_InstanceDeleteUnsupported"
	case ErrorCode_OperationDenied_TempModify_InstanceReclaimTimeLimit:
		return "OperationDenied_TempModify_InstanceReclaimTimeLimit"
	case ErrorCode_OperationDenied_TempModify_InstanceContinueTimeLimit:
		return "OperationDenied_TempModify_InstanceContinueTimeLimit"
	case ErrorCode_OperationDenied_TempModify_InstanceDurationTimeLimit:
		return "OperationDenied_TempModify_InstanceDurationTimeLimit"
	}
	return "<UNSET>"
}

func ErrorCodeFromString(s string) (ErrorCode, error) {
	switch s {
	case "InternalError":
		return ErrorCode_InternalError, nil
	case "ServiceUnavailable":
		return ErrorCode_ServiceUnavailable, nil
	case "ServiceBusy":
		return ErrorCode_ServiceBusy, nil
	case "InternalErrorFormat":
		return ErrorCode_InternalErrorFormat, nil
	case "MissingParameter":
		return ErrorCode_MissingParameter, nil
	case "InvalidParameter":
		return ErrorCode_InvalidParameter, nil
	case "UnsupportedParameter":
		return ErrorCode_UnsupportedParameter, nil
	case "InvalidParameterFormat":
		return ErrorCode_InvalidParameterFormat, nil
	case "InstanceNotFound":
		return ErrorCode_InstanceNotFound, nil
	case "InstanceIsNotRunning":
		return ErrorCode_InstanceIsNotRunning, nil
	case "InstanceUnchangeable":
		return ErrorCode_InstanceUnchangeable, nil
	case "OperationDenied_BackupJobExists":
		return ErrorCode_OperationDenied_BackupJobExists, nil
	case "OperationDenied_InstanceLockMode":
		return ErrorCode_OperationDenied_InstanceLockMode, nil
	case "InstanceNameDuplicate":
		return ErrorCode_InstanceNameDuplicate, nil
	case "NameDuplicate":
		return ErrorCode_NameDuplicate, nil
	case "InstanceStorageSpaceTooSmall":
		return ErrorCode_InstanceStorageSpaceTooSmall, nil
	case "VpcIDNotFound":
		return ErrorCode_VpcIDNotFound, nil
	case "InstanceSpecLessThanMaster":
		return ErrorCode_InstanceSpecLessThanMaster, nil
	case "OperationUnsupported_MySQL55":
		return ErrorCode_OperationUnsupported_MySQL55, nil
	case "OperationUnsupported":
		return ErrorCode_OperationUnsupported, nil
	case "OperationDenied_ImportJobExists":
		return ErrorCode_OperationDenied_ImportJobExists, nil
	case "OperationUnsupported_MySQL56":
		return ErrorCode_OperationUnsupported_MySQL56, nil
	case "ReadOnlyInstanceNotExists":
		return ErrorCode_ReadOnlyInstanceNotExists, nil
	case "ReadOnlyInstanceNumExceedLimit":
		return ErrorCode_ReadOnlyInstanceNumExceedLimit, nil
	case "OperationDenied_ExistLargeDelaySlaveNode":
		return ErrorCode_OperationDenied_ExistLargeDelaySlaveNode, nil
	case "OperationDenied_NotExistLowDelaySlaveNode":
		return ErrorCode_OperationDenied_NotExistLowDelaySlaveNode, nil
	case "InstanceHasClosed":
		return ErrorCode_InstanceHasClosed, nil
	case "InsufficientBalance":
		return ErrorCode_InsufficientBalance, nil
	case "Forbidden_Authentication":
		return ErrorCode_Forbidden_Authentication, nil
	case "OperationDenied_InstanceMaintaining":
		return ErrorCode_OperationDenied_InstanceMaintaining, nil
	case "OperationDenied_InstanceClosed":
		return ErrorCode_OperationDenied_InstanceClosed, nil
	case "OperationDenied_InstancePrepaid":
		return ErrorCode_OperationDenied_InstancePrepaid, nil
	case "OperationDenied_InstanceIsNotRecycled":
		return ErrorCode_OperationDenied_InstanceIsNotRecycled, nil
	case "BackupNotFound":
		return ErrorCode_BackupNotFound, nil
	case "OperationDenied_ChargeDisabled":
		return ErrorCode_OperationDenied_ChargeDisabled, nil
	case "ChargeOrderExists":
		return ErrorCode_ChargeOrderExists, nil
	case "QuotaExceeded_CreateInstance":
		return ErrorCode_QuotaExceeded_CreateInstance, nil
	case "OperationDenied_ResourceSoldOut":
		return ErrorCode_OperationDenied_ResourceSoldOut, nil
	case "OperationDenied_DiskSpaceInsufficient":
		return ErrorCode_OperationDenied_DiskSpaceInsufficient, nil
	case "InstanceStorageSpaceTooLarge":
		return ErrorCode_InstanceStorageSpaceTooLarge, nil
	case "InstanceCPUTooSmall":
		return ErrorCode_InstanceCPUTooSmall, nil
	case "InstanceMemoryTooSmall":
		return ErrorCode_InstanceMemoryTooSmall, nil
	case "OperationDenied_NotExistRunningReadOnlyInstance":
		return ErrorCode_OperationDenied_NotExistRunningReadOnlyInstance, nil
	case "PublicConnectionServiceAlreadyExist":
		return ErrorCode_PublicConnectionServiceAlreadyExist, nil
	case "SubnetIDNotFound":
		return ErrorCode_SubnetIDNotFound, nil
	case "ReadOnlyNodeNotExists":
		return ErrorCode_ReadOnlyNodeNotExists, nil
	case "OperationDenied_NotExistRunningReadOnlyNode":
		return ErrorCode_OperationDenied_NotExistRunningReadOnlyNode, nil
	case "OperationDenied_DownloadBackupJobExists":
		return ErrorCode_OperationDenied_DownloadBackupJobExists, nil
	case "InvalidSubnetId":
		return ErrorCode_InvalidSubnetId, nil
	case "UserBackupAmountOverTheLimit":
		return ErrorCode_UserBackupAmountOverTheLimit, nil
	case "OperationDenied_IncrBackupUnsupportRecovery":
		return ErrorCode_OperationDenied_IncrBackupUnsupportRecovery, nil
	case "OperationUnsupported_MySQL80":
		return ErrorCode_OperationUnsupported_MySQL80, nil
	case "OperationDenied_AGSyncStatusAbnormal":
		return ErrorCode_OperationDenied_AGSyncStatusAbnormal, nil
	case "OperationExceedLimit":
		return ErrorCode_OperationExceedLimit, nil
	case "TosRestoreTaskNumExceedLimit":
		return ErrorCode_TosRestoreTaskNumExceedLimit, nil
	case "TosRestoreTaskDbVerifyFailed":
		return ErrorCode_TosRestoreTaskDbVerifyFailed, nil
	case "OperationDuplicate":
		return ErrorCode_OperationDuplicate, nil
	case "QuotaExceeded_RestoreDB":
		return ErrorCode_QuotaExceeded_RestoreDB, nil
	case "OperationDenied_DBStatusAbnormal":
		return ErrorCode_OperationDenied_DBStatusAbnormal, nil
	case "OperationUnsupported_MSSQLDBVersion":
		return ErrorCode_OperationUnsupported_MSSQLDBVersion, nil
	case "InstanceStorageSpaceLargeThanReadonly":
		return ErrorCode_InstanceStorageSpaceLargeThanReadonly, nil
	case "InstanceTypeNotSupported":
		return ErrorCode_InstanceTypeNotSupported, nil
	case "OperationUnsupportedFormat":
		return ErrorCode_OperationUnsupportedFormat, nil
	case "ReadOnlyCreateNumExceedLimit":
		return ErrorCode_ReadOnlyCreateNumExceedLimit, nil
	case "QuotaExceeded_CreateDB":
		return ErrorCode_QuotaExceeded_CreateDB, nil
	case "InstanceStatusNotSupported":
		return ErrorCode_InstanceStatusNotSupported, nil
	case "IllegalParameter":
		return ErrorCode_IllegalParameter, nil
	case "InstanceSpecNotMatchStorage":
		return ErrorCode_InstanceSpecNotMatchStorage, nil
	case "OperationDenied_LoginSyncStatusAbnormal":
		return ErrorCode_OperationDenied_LoginSyncStatusAbnormal, nil
	case "OperationDenied_TooManyRequests":
		return ErrorCode_OperationDenied_TooManyRequests, nil
	case "OperationDenied_TosRestoreTaskNumExceedLimit":
		return ErrorCode_OperationDenied_TosRestoreTaskNumExceedLimit, nil
	case "OperationDenied_QpsRateLimit":
		return ErrorCode_OperationDenied_QpsRateLimit, nil
	case "InvalidAccountName_Duplicate":
		return ErrorCode_InvalidAccountName_Duplicate, nil
	case "InvalidAccountName_Keyword":
		return ErrorCode_InvalidAccountName_Keyword, nil
	case "InvalidAccountType_Duplicate":
		return ErrorCode_InvalidAccountType_Duplicate, nil
	case "InvalidDBName_Duplicate":
		return ErrorCode_InvalidDBName_Duplicate, nil
	case "InvalidDBName_Keyword":
		return ErrorCode_InvalidDBName_Keyword, nil
	case "AccountNumExceedLimit":
		return ErrorCode_AccountNumExceedLimit, nil
	case "InvalidAccountPasssword_Weak":
		return ErrorCode_InvalidAccountPasssword_Weak, nil
	case "InvalidSecurityIPList_Duplicate":
		return ErrorCode_InvalidSecurityIPList_Duplicate, nil
	case "InvalidSecurityIPList":
		return ErrorCode_InvalidSecurityIPList, nil
	case "SecurityIPListGroupNotFound":
		return ErrorCode_SecurityIPListGroupNotFound, nil
	case "InvalidSecurityIPList_IPExist":
		return ErrorCode_InvalidSecurityIPList_IPExist, nil
	case "InvalidSecurityIPList_IPNumExceedLimit":
		return ErrorCode_InvalidSecurityIPList_IPNumExceedLimit, nil
	case "OperationDenied_SystemGroup":
		return ErrorCode_OperationDenied_SystemGroup, nil
	case "OperationDenied_TDEEnabled":
		return ErrorCode_OperationDenied_TDEEnabled, nil
	case "OperationDenied_KMSDisabled":
		return ErrorCode_OperationDenied_KMSDisabled, nil
	case "InvalidSecurityIPList_IPGroupExceedLimit":
		return ErrorCode_InvalidSecurityIPList_IPGroupExceedLimit, nil
	case "InvalidAllowListVersion":
		return ErrorCode_InvalidAllowListVersion, nil
	case "InvalidSecurityGroupId":
		return ErrorCode_InvalidSecurityGroupId, nil
	case "InvalidSecurityGroupId_SGNumExceedLimit":
		return ErrorCode_InvalidSecurityGroupId_SGNumExceedLimit, nil
	case "OperationDenied_AllowListStatus":
		return ErrorCode_OperationDenied_AllowListStatus, nil
	case "OperationDenied_AllowListIpListAndSecurityGroupEmpty":
		return ErrorCode_OperationDenied_AllowListIpListAndSecurityGroupEmpty, nil
	case "OperationDenied_SSLStatus":
		return ErrorCode_OperationDenied_SSLStatus, nil
	case "OperationDenied_InstanceAllowListBindRelationship":
		return ErrorCode_OperationDenied_InstanceAllowListBindRelationship, nil
	case "OperationDenied_SameInstanceAllowListIP":
		return ErrorCode_OperationDenied_SameInstanceAllowListIP, nil
	case "NotSatisfiedMasterChangeCondition":
		return ErrorCode_NotSatisfiedMasterChangeCondition, nil
	case "ChangeNodeIsMaster":
		return ErrorCode_ChangeNodeIsMaster, nil
	case "InvalidTemplateName_Duplicate":
		return ErrorCode_InvalidTemplateName_Duplicate, nil
	case "TemplateNotFound":
		return ErrorCode_TemplateNotFound, nil
	case "TemplateTypeNotMatch":
		return ErrorCode_TemplateTypeNotMatch, nil
	case "OperationDenied_SystemTemplate":
		return ErrorCode_OperationDenied_SystemTemplate, nil
	case "BackupAmountOverTheLimit":
		return ErrorCode_BackupAmountOverTheLimit, nil
	case "ProjectNotFound":
		return ErrorCode_ProjectNotFound, nil
	case "RunningOrderNumExceedLimit":
		return ErrorCode_RunningOrderNumExceedLimit, nil
	case "AccountPrivillegeInsufficient":
		return ErrorCode_AccountPrivillegeInsufficient, nil
	case "SQLSyntaxErrorOrUnsupported":
		return ErrorCode_SQLSyntaxErrorOrUnsupported, nil
	case "DatabaseNotExist":
		return ErrorCode_DatabaseNotExist, nil
	case "AccountNotExist":
		return ErrorCode_AccountNotExist, nil
	case "PrecheckFailed_SQLUnsupported":
		return ErrorCode_PrecheckFailed_SQLUnsupported, nil
	case "PrecheckFailed_DiskSpaceInsufficient":
		return ErrorCode_PrecheckFailed_DiskSpaceInsufficient, nil
	case "PrecheckFailed_DryRunFailed":
		return ErrorCode_PrecheckFailed_DryRunFailed, nil
	case "OrderNotExist":
		return ErrorCode_OrderNotExist, nil
	case "OperationDenied_OnlineDDLJobExists":
		return ErrorCode_OperationDenied_OnlineDDLJobExists, nil
	case "PrecheckFailed_NoPrimaryKey":
		return ErrorCode_PrecheckFailed_NoPrimaryKey, nil
	case "PrecheckFailed_DuplicateKeyOrColumn":
		return ErrorCode_PrecheckFailed_DuplicateKeyOrColumn, nil
	case "PrecheckFailed_KeyOrColumnNotExists":
		return ErrorCode_PrecheckFailed_KeyOrColumnNotExists, nil
	case "OperationDenied_OnlineDDLJobFinished":
		return ErrorCode_OperationDenied_OnlineDDLJobFinished, nil
	case "PrecheckFailed_SQLSyntaxError":
		return ErrorCode_PrecheckFailed_SQLSyntaxError, nil
	case "DryRunOperation":
		return ErrorCode_DryRunOperation, nil
	case "OperationDenied_MgrBusy":
		return ErrorCode_OperationDenied_MgrBusy, nil
	case "MaintainTime_ConflictWith_BackupTime":
		return ErrorCode_MaintainTime_ConflictWith_BackupTime, nil
	case "EndpointNotFound":
		return ErrorCode_EndpointNotFound, nil
	case "OperationDenied_AddressExists":
		return ErrorCode_OperationDenied_AddressExists, nil
	case "AddressNotFound":
		return ErrorCode_AddressNotFound, nil
	case "OperationFailed":
		return ErrorCode_OperationFailed, nil
	case "RequestTimeout":
		return ErrorCode_RequestTimeout, nil
	case "OperationDenied_TempModify_ChargeTypeUnsupported":
		return ErrorCode_OperationDenied_TempModify_ChargeTypeUnsupported, nil
	case "OperationDenied_TempModify_ReduceNodeUnsupported":
		return ErrorCode_OperationDenied_TempModify_ReduceNodeUnsupported, nil
	case "OperationDenied_TempModify_ReduceStorageUnsupported":
		return ErrorCode_OperationDenied_TempModify_ReduceStorageUnsupported, nil
	case "OperationDenied_TempModify_ReduceSpecUnsupported":
		return ErrorCode_OperationDenied_TempModify_ReduceSpecUnsupported, nil
	case "OperationDenied_TempModify_InstanceDeleteUnsupported":
		return ErrorCode_OperationDenied_TempModify_InstanceDeleteUnsupported, nil
	case "OperationDenied_TempModify_InstanceReclaimTimeLimit":
		return ErrorCode_OperationDenied_TempModify_InstanceReclaimTimeLimit, nil
	case "OperationDenied_TempModify_InstanceContinueTimeLimit":
		return ErrorCode_OperationDenied_TempModify_InstanceContinueTimeLimit, nil
	case "OperationDenied_TempModify_InstanceDurationTimeLimit":
		return ErrorCode_OperationDenied_TempModify_InstanceDurationTimeLimit, nil
	}
	return ErrorCode(0), fmt.Errorf("not a valid ErrorCode string")
}

func ErrorCodePtr(v ErrorCode) *ErrorCode { return &v }

func (p ErrorCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrorCode) UnmarshalText(text []byte) error {
	q, err := ErrorCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrorDetail struct {
	HttpStatusCode int64  `thrift:"HttpStatusCode,1,required" frugal:"1,required,i64" json:"HttpStatusCode"`
	Description    string `thrift:"Description,2,required" frugal:"2,required,string" json:"Description"`
	DescriptionZh  string `thrift:"DescriptionZh,3,required" frugal:"3,required,string" json:"DescriptionZh"`
}

func NewErrorDetail() *ErrorDetail {
	return &ErrorDetail{}
}

func (p *ErrorDetail) InitDefault() {
}

func (p *ErrorDetail) GetHttpStatusCode() (v int64) {
	return p.HttpStatusCode
}

func (p *ErrorDetail) GetDescription() (v string) {
	return p.Description
}

func (p *ErrorDetail) GetDescriptionZh() (v string) {
	return p.DescriptionZh
}
func (p *ErrorDetail) SetHttpStatusCode(val int64) {
	p.HttpStatusCode = val
}
func (p *ErrorDetail) SetDescription(val string) {
	p.Description = val
}
func (p *ErrorDetail) SetDescriptionZh(val string) {
	p.DescriptionZh = val
}

var fieldIDToName_ErrorDetail = map[int16]string{
	1: "HttpStatusCode",
	2: "Description",
	3: "DescriptionZh",
}

func (p *ErrorDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHttpStatusCode bool = false
	var issetDescription bool = false
	var issetDescriptionZh bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHttpStatusCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescriptionZh = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHttpStatusCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescriptionZh {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrorDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ErrorDetail[fieldId]))
}

func (p *ErrorDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HttpStatusCode = _field
	return nil
}
func (p *ErrorDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *ErrorDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescriptionZh = _field
	return nil
}

func (p *ErrorDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrorDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrorDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HttpStatusCode", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.HttpStatusCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ErrorDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrorDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DescriptionZh", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescriptionZh); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrorDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrorDetail(%+v)", *p)

}

func (p *ErrorDetail) DeepEqual(ano *ErrorDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HttpStatusCode) {
		return false
	}
	if !p.Field2DeepEqual(ano.Description) {
		return false
	}
	if !p.Field3DeepEqual(ano.DescriptionZh) {
		return false
	}
	return true
}

func (p *ErrorDetail) Field1DeepEqual(src int64) bool {

	if p.HttpStatusCode != src {
		return false
	}
	return true
}
func (p *ErrorDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *ErrorDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DescriptionZh, src) != 0 {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type InnerInspectDBInstanceReq struct {
	InstanceIds   []string       `thrift:"InstanceIds,1,optional" frugal:"1,optional,list<string>" json:"InstanceIds,omitempty"`
	AccountIds    []string       `thrift:"AccountIds,2,optional" frugal:"2,optional,list<string>" json:"AccountIds,omitempty"`
	InspectItems  []string       `thrift:"InspectItems,3,optional" frugal:"3,optional,list<string>" json:"InspectItems,omitempty"`
	InspectOption *InspectOption `thrift:"InspectOption,4,optional" frugal:"4,optional,InspectOption" json:"InspectOption,omitempty"`
}

func NewInnerInspectDBInstanceReq() *InnerInspectDBInstanceReq {
	return &InnerInspectDBInstanceReq{}
}

func (p *InnerInspectDBInstanceReq) InitDefault() {
}

var InnerInspectDBInstanceReq_InstanceIds_DEFAULT []string

func (p *InnerInspectDBInstanceReq) GetInstanceIds() (v []string) {
	if !p.IsSetInstanceIds() {
		return InnerInspectDBInstanceReq_InstanceIds_DEFAULT
	}
	return p.InstanceIds
}

var InnerInspectDBInstanceReq_AccountIds_DEFAULT []string

func (p *InnerInspectDBInstanceReq) GetAccountIds() (v []string) {
	if !p.IsSetAccountIds() {
		return InnerInspectDBInstanceReq_AccountIds_DEFAULT
	}
	return p.AccountIds
}

var InnerInspectDBInstanceReq_InspectItems_DEFAULT []string

func (p *InnerInspectDBInstanceReq) GetInspectItems() (v []string) {
	if !p.IsSetInspectItems() {
		return InnerInspectDBInstanceReq_InspectItems_DEFAULT
	}
	return p.InspectItems
}

var InnerInspectDBInstanceReq_InspectOption_DEFAULT *InspectOption

func (p *InnerInspectDBInstanceReq) GetInspectOption() (v *InspectOption) {
	if !p.IsSetInspectOption() {
		return InnerInspectDBInstanceReq_InspectOption_DEFAULT
	}
	return p.InspectOption
}
func (p *InnerInspectDBInstanceReq) SetInstanceIds(val []string) {
	p.InstanceIds = val
}
func (p *InnerInspectDBInstanceReq) SetAccountIds(val []string) {
	p.AccountIds = val
}
func (p *InnerInspectDBInstanceReq) SetInspectItems(val []string) {
	p.InspectItems = val
}
func (p *InnerInspectDBInstanceReq) SetInspectOption(val *InspectOption) {
	p.InspectOption = val
}

var fieldIDToName_InnerInspectDBInstanceReq = map[int16]string{
	1: "InstanceIds",
	2: "AccountIds",
	3: "InspectItems",
	4: "InspectOption",
}

func (p *InnerInspectDBInstanceReq) IsSetInstanceIds() bool {
	return p.InstanceIds != nil
}

func (p *InnerInspectDBInstanceReq) IsSetAccountIds() bool {
	return p.AccountIds != nil
}

func (p *InnerInspectDBInstanceReq) IsSetInspectItems() bool {
	return p.InspectItems != nil
}

func (p *InnerInspectDBInstanceReq) IsSetInspectOption() bool {
	return p.InspectOption != nil
}

func (p *InnerInspectDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerInspectDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InnerInspectDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceIds = _field
	return nil
}
func (p *InnerInspectDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AccountIds = _field
	return nil
}
func (p *InnerInspectDBInstanceReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectItems = _field
	return nil
}
func (p *InnerInspectDBInstanceReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewInspectOption()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InspectOption = _field
	return nil
}

func (p *InnerInspectDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerInspectDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("InnerInspectDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceIds() {
		if err = oprot.WriteFieldBegin("InstanceIds", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceIds)); err != nil {
			return err
		}
		for _, v := range p.InstanceIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountIds() {
		if err = oprot.WriteFieldBegin("AccountIds", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AccountIds)); err != nil {
			return err
		}
		for _, v := range p.AccountIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectItems() {
		if err = oprot.WriteFieldBegin("InspectItems", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.InspectItems)); err != nil {
			return err
		}
		for _, v := range p.InspectItems {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectOption() {
		if err = oprot.WriteFieldBegin("InspectOption", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InspectOption.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InnerInspectDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InnerInspectDBInstanceReq(%+v)", *p)

}

func (p *InnerInspectDBInstanceReq) DeepEqual(ano *InnerInspectDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceIds) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountIds) {
		return false
	}
	if !p.Field3DeepEqual(ano.InspectItems) {
		return false
	}
	if !p.Field4DeepEqual(ano.InspectOption) {
		return false
	}
	return true
}

func (p *InnerInspectDBInstanceReq) Field1DeepEqual(src []string) bool {

	if len(p.InstanceIds) != len(src) {
		return false
	}
	for i, v := range p.InstanceIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *InnerInspectDBInstanceReq) Field2DeepEqual(src []string) bool {

	if len(p.AccountIds) != len(src) {
		return false
	}
	for i, v := range p.AccountIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *InnerInspectDBInstanceReq) Field3DeepEqual(src []string) bool {

	if len(p.InspectItems) != len(src) {
		return false
	}
	for i, v := range p.InspectItems {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *InnerInspectDBInstanceReq) Field4DeepEqual(src *InspectOption) bool {

	if !p.InspectOption.DeepEqual(src) {
		return false
	}
	return true
}

type InnerInspectDBInstanceResp struct {
	TaskId   string                    `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	Status   InspectStatus             `thrift:"Status,2,required" frugal:"2,required,InspectStatus" json:"Status"`
	Reason   string                    `thrift:"Reason,3,required" frugal:"3,required,string" json:"Reason"`
	Progress int32                     `thrift:"Progress,4,required" frugal:"4,required,i32" json:"Progress"`
	Summary  *InspectResultSummary     `thrift:"Summary,5,required" frugal:"5,required,InspectResultSummary" json:"Summary"`
	Results  []*InstanceInspectResult_ `thrift:"Results,6,required" frugal:"6,required,list<InstanceInspectResult_>" json:"Results"`
}

func NewInnerInspectDBInstanceResp() *InnerInspectDBInstanceResp {
	return &InnerInspectDBInstanceResp{}
}

func (p *InnerInspectDBInstanceResp) InitDefault() {
}

func (p *InnerInspectDBInstanceResp) GetTaskId() (v string) {
	return p.TaskId
}

func (p *InnerInspectDBInstanceResp) GetStatus() (v InspectStatus) {
	return p.Status
}

func (p *InnerInspectDBInstanceResp) GetReason() (v string) {
	return p.Reason
}

func (p *InnerInspectDBInstanceResp) GetProgress() (v int32) {
	return p.Progress
}

var InnerInspectDBInstanceResp_Summary_DEFAULT *InspectResultSummary

func (p *InnerInspectDBInstanceResp) GetSummary() (v *InspectResultSummary) {
	if !p.IsSetSummary() {
		return InnerInspectDBInstanceResp_Summary_DEFAULT
	}
	return p.Summary
}

func (p *InnerInspectDBInstanceResp) GetResults() (v []*InstanceInspectResult_) {
	return p.Results
}
func (p *InnerInspectDBInstanceResp) SetTaskId(val string) {
	p.TaskId = val
}
func (p *InnerInspectDBInstanceResp) SetStatus(val InspectStatus) {
	p.Status = val
}
func (p *InnerInspectDBInstanceResp) SetReason(val string) {
	p.Reason = val
}
func (p *InnerInspectDBInstanceResp) SetProgress(val int32) {
	p.Progress = val
}
func (p *InnerInspectDBInstanceResp) SetSummary(val *InspectResultSummary) {
	p.Summary = val
}
func (p *InnerInspectDBInstanceResp) SetResults(val []*InstanceInspectResult_) {
	p.Results = val
}

var fieldIDToName_InnerInspectDBInstanceResp = map[int16]string{
	1: "TaskId",
	2: "Status",
	3: "Reason",
	4: "Progress",
	5: "Summary",
	6: "Results",
}

func (p *InnerInspectDBInstanceResp) IsSetSummary() bool {
	return p.Summary != nil
}

func (p *InnerInspectDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerInspectDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetStatus bool = false
	var issetReason bool = false
	var issetProgress bool = false
	var issetSummary bool = false
	var issetResults bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReason = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetProgress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSummary = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetResults = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReason {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetProgress {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSummary {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetResults {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InnerInspectDBInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InnerInspectDBInstanceResp[fieldId]))
}

func (p *InnerInspectDBInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *InnerInspectDBInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field InspectStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *InnerInspectDBInstanceResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}
func (p *InnerInspectDBInstanceResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Progress = _field
	return nil
}
func (p *InnerInspectDBInstanceResp) ReadField5(iprot thrift.TProtocol) error {
	_field := NewInspectResultSummary()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Summary = _field
	return nil
}
func (p *InnerInspectDBInstanceResp) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceInspectResult_, 0, size)
	values := make([]InstanceInspectResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Results = _field
	return nil
}

func (p *InnerInspectDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerInspectDBInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("InnerInspectDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Reason", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Progress", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Progress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Summary", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Summary.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Results", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Results)); err != nil {
		return err
	}
	for _, v := range p.Results {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InnerInspectDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InnerInspectDBInstanceResp(%+v)", *p)

}

func (p *InnerInspectDBInstanceResp) DeepEqual(ano *InnerInspectDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Status) {
		return false
	}
	if !p.Field3DeepEqual(ano.Reason) {
		return false
	}
	if !p.Field4DeepEqual(ano.Progress) {
		return false
	}
	if !p.Field5DeepEqual(ano.Summary) {
		return false
	}
	if !p.Field6DeepEqual(ano.Results) {
		return false
	}
	return true
}

func (p *InnerInspectDBInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *InnerInspectDBInstanceResp) Field2DeepEqual(src InspectStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *InnerInspectDBInstanceResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Reason, src) != 0 {
		return false
	}
	return true
}
func (p *InnerInspectDBInstanceResp) Field4DeepEqual(src int32) bool {

	if p.Progress != src {
		return false
	}
	return true
}
func (p *InnerInspectDBInstanceResp) Field5DeepEqual(src *InspectResultSummary) bool {

	if !p.Summary.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InnerInspectDBInstanceResp) Field6DeepEqual(src []*InstanceInspectResult_) bool {

	if len(p.Results) != len(src) {
		return false
	}
	for i, v := range p.Results {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InnerDescribeInspectItemsReq struct {
}

func NewInnerDescribeInspectItemsReq() *InnerDescribeInspectItemsReq {
	return &InnerDescribeInspectItemsReq{}
}

func (p *InnerDescribeInspectItemsReq) InitDefault() {
}

var fieldIDToName_InnerDescribeInspectItemsReq = map[int16]string{}

func (p *InnerDescribeInspectItemsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerDescribeInspectItemsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InnerDescribeInspectItemsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerDescribeInspectItemsReq")

	if err = oprot.WriteStructBegin("InnerDescribeInspectItemsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InnerDescribeInspectItemsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InnerDescribeInspectItemsReq(%+v)", *p)

}

func (p *InnerDescribeInspectItemsReq) DeepEqual(ano *InnerDescribeInspectItemsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type InnerDescribeInspectItemsResp struct {
	Total int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Datas []*InspectItem `thrift:"Datas,2,required" frugal:"2,required,list<InspectItem>" json:"Datas"`
}

func NewInnerDescribeInspectItemsResp() *InnerDescribeInspectItemsResp {
	return &InnerDescribeInspectItemsResp{}
}

func (p *InnerDescribeInspectItemsResp) InitDefault() {
}

func (p *InnerDescribeInspectItemsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *InnerDescribeInspectItemsResp) GetDatas() (v []*InspectItem) {
	return p.Datas
}
func (p *InnerDescribeInspectItemsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *InnerDescribeInspectItemsResp) SetDatas(val []*InspectItem) {
	p.Datas = val
}

var fieldIDToName_InnerDescribeInspectItemsResp = map[int16]string{
	1: "Total",
	2: "Datas",
}

func (p *InnerDescribeInspectItemsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerDescribeInspectItemsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetDatas bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatas = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatas {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InnerDescribeInspectItemsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InnerDescribeInspectItemsResp[fieldId]))
}

func (p *InnerDescribeInspectItemsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *InnerDescribeInspectItemsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectItem, 0, size)
	values := make([]InspectItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datas = _field
	return nil
}

func (p *InnerDescribeInspectItemsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InnerDescribeInspectItemsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("InnerDescribeInspectItemsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InnerDescribeInspectItemsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InnerDescribeInspectItemsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datas", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datas)); err != nil {
		return err
	}
	for _, v := range p.Datas {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InnerDescribeInspectItemsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InnerDescribeInspectItemsResp(%+v)", *p)

}

func (p *InnerDescribeInspectItemsResp) DeepEqual(ano *InnerDescribeInspectItemsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Datas) {
		return false
	}
	return true
}

func (p *InnerDescribeInspectItemsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *InnerDescribeInspectItemsResp) Field2DeepEqual(src []*InspectItem) bool {

	if len(p.Datas) != len(src) {
		return false
	}
	for i, v := range p.Datas {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

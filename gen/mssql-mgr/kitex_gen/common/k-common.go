// Code generated by Kitex v1.18.1. DO NOT EDIT.

package common

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"

	"code.byted.org/infcs/dbw-mgr/gen/mssql-mgr/kitex_gen/model"
	"code.byted.org/infcs/dbw-mgr/gen/mssql-mgr/kitex_gen/model/v2"
)

var (
	_ = model.KitexUnusedProtection
	_ = v2.KitexUnusedProtection
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SlowLogCandidateField int64

const (
	SlowLogCandidateField_User     SlowLogCandidateField = 1
	SlowLogCandidateField_DB       SlowLogCandidateField = 2
	SlowLogCandidateField_SourceIP SlowLogCandidateField = 3
	SlowLogCandidateField_Table    SlowLogCandidateField = 5
	SlowLogCandidateField_NodeID   SlowLogCandidateField = 6
	SlowLogCandidateField_ErrorNo  SlowLogCandidateField = 7
	SlowLogCandidateField_Method   SlowLogCandidateField = 8
	SlowLogCandidateField_PSM      SlowLogCandidateField = 9
)

func (p SlowLogCandidateField) String() string {
	switch p {
	case SlowLogCandidateField_User:
		return "User"
	case SlowLogCandidateField_DB:
		return "DB"
	case SlowLogCandidateField_SourceIP:
		return "SourceIP"
	case SlowLogCandidateField_Table:
		return "Table"
	case SlowLogCandidateField_NodeID:
		return "NodeID"
	case SlowLogCandidateField_ErrorNo:
		return "ErrorNo"
	case SlowLogCandidateField_Method:
		return "Method"
	case SlowLogCandidateField_PSM:
		return "PSM"
	}
	return "<UNSET>"
}

func SlowLogCandidateFieldFromString(s string) (SlowLogCandidateField, error) {
	switch s {
	case "User":
		return SlowLogCandidateField_User, nil
	case "DB":
		return SlowLogCandidateField_DB, nil
	case "SourceIP":
		return SlowLogCandidateField_SourceIP, nil
	case "Table":
		return SlowLogCandidateField_Table, nil
	case "NodeID":
		return SlowLogCandidateField_NodeID, nil
	case "ErrorNo":
		return SlowLogCandidateField_ErrorNo, nil
	case "Method":
		return SlowLogCandidateField_Method, nil
	case "PSM":
		return SlowLogCandidateField_PSM, nil
	}
	return SlowLogCandidateField(0), fmt.Errorf("not a valid SlowLogCandidateField string")
}

func SlowLogCandidateFieldPtr(v SlowLogCandidateField) *SlowLogCandidateField { return &v }

func (p SlowLogCandidateField) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SlowLogCandidateField) UnmarshalText(text []byte) error {
	q, err := SlowLogCandidateFieldFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeSlowLogTimeSeriesStatsReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType              `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	Interval           *int32              `thrift:"Interval,6,optional" frugal:"6,optional,i32" json:"Interval,omitempty"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,7,optional" frugal:"7,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	Limit              *int32              `thrift:"Limit,8,optional" frugal:"8,optional,i32" json:"Limit,omitempty"`
	NodeId             *string             `thrift:"NodeId,9,optional" frugal:"9,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,10,optional" frugal:"10,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,11,optional" frugal:"11,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeSlowLogTimeSeriesStatsReq() *DescribeSlowLogTimeSeriesStatsReq {
	return &DescribeSlowLogTimeSeriesStatsReq{}
}

func (p *DescribeSlowLogTimeSeriesStatsReq) InitDefault() {
}

func (p *DescribeSlowLogTimeSeriesStatsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeSlowLogTimeSeriesStatsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeSlowLogTimeSeriesStatsReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSlowLogTimeSeriesStatsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSlowLogTimeSeriesStatsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeSlowLogTimeSeriesStatsReq_Interval_DEFAULT int32

func (p *DescribeSlowLogTimeSeriesStatsReq) GetInterval() (v int32) {
	if !p.IsSetInterval() {
		return DescribeSlowLogTimeSeriesStatsReq_Interval_DEFAULT
	}
	return *p.Interval
}

var DescribeSlowLogTimeSeriesStatsReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeSlowLogTimeSeriesStatsReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSlowLogTimeSeriesStatsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSlowLogTimeSeriesStatsReq_Limit_DEFAULT int32

func (p *DescribeSlowLogTimeSeriesStatsReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return DescribeSlowLogTimeSeriesStatsReq_Limit_DEFAULT
	}
	return *p.Limit
}

var DescribeSlowLogTimeSeriesStatsReq_NodeId_DEFAULT string

func (p *DescribeSlowLogTimeSeriesStatsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeSlowLogTimeSeriesStatsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeSlowLogTimeSeriesStatsReq_ShardingInstanceId_DEFAULT string

func (p *DescribeSlowLogTimeSeriesStatsReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeSlowLogTimeSeriesStatsReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeSlowLogTimeSeriesStatsReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeSlowLogTimeSeriesStatsReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeSlowLogTimeSeriesStatsReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetInterval(val *int32) {
	p.Interval = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetLimit(val *int32) {
	p.Limit = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeSlowLogTimeSeriesStatsReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeSlowLogTimeSeriesStatsReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "Interval",
	7:  "SearchParam",
	8:  "Limit",
	9:  "NodeId",
	10: "ShardingInstanceId",
	11: "NodeType",
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetInterval() bool {
	return p.Interval != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogTimeSeriesStatsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogTimeSeriesStatsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogTimeSeriesStatsReq[fieldId]))
}

func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Interval = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField7(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeSlowLogTimeSeriesStatsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogTimeSeriesStatsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogTimeSeriesStatsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInterval() {
		if err = oprot.WriteFieldBegin("Interval", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Interval); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogTimeSeriesStatsReq(%+v)", *p)

}

func (p *DescribeSlowLogTimeSeriesStatsReq) DeepEqual(ano *DescribeSlowLogTimeSeriesStatsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.Interval) {
		return false
	}
	if !p.Field7DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field8DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field11DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeSlowLogTimeSeriesStatsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field6DeepEqual(src *int32) bool {

	if p.Interval == src {
		return true
	} else if p.Interval == nil || src == nil {
		return false
	}
	if *p.Interval != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field7DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field8DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field9DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field10DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsReq) Field11DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeSlowLogTimeSeriesStatsResp struct {
	SlowLogCountStats []*SlowLogCount `thrift:"SlowLogCountStats,1,required" frugal:"1,required,list<SlowLogCount>" json:"SlowLogCountStats"`
	CpuUsageStats     []*NodeCpuUsage `thrift:"CpuUsageStats,2,required" frugal:"2,required,list<NodeCpuUsage>" json:"CpuUsageStats"`
	Interval          int32           `thrift:"Interval,3,required" frugal:"3,required,i32" json:"Interval"`
}

func NewDescribeSlowLogTimeSeriesStatsResp() *DescribeSlowLogTimeSeriesStatsResp {
	return &DescribeSlowLogTimeSeriesStatsResp{}
}

func (p *DescribeSlowLogTimeSeriesStatsResp) InitDefault() {
}

func (p *DescribeSlowLogTimeSeriesStatsResp) GetSlowLogCountStats() (v []*SlowLogCount) {
	return p.SlowLogCountStats
}

func (p *DescribeSlowLogTimeSeriesStatsResp) GetCpuUsageStats() (v []*NodeCpuUsage) {
	return p.CpuUsageStats
}

func (p *DescribeSlowLogTimeSeriesStatsResp) GetInterval() (v int32) {
	return p.Interval
}
func (p *DescribeSlowLogTimeSeriesStatsResp) SetSlowLogCountStats(val []*SlowLogCount) {
	p.SlowLogCountStats = val
}
func (p *DescribeSlowLogTimeSeriesStatsResp) SetCpuUsageStats(val []*NodeCpuUsage) {
	p.CpuUsageStats = val
}
func (p *DescribeSlowLogTimeSeriesStatsResp) SetInterval(val int32) {
	p.Interval = val
}

var fieldIDToName_DescribeSlowLogTimeSeriesStatsResp = map[int16]string{
	1: "SlowLogCountStats",
	2: "CpuUsageStats",
	3: "Interval",
}

func (p *DescribeSlowLogTimeSeriesStatsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogTimeSeriesStatsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSlowLogCountStats bool = false
	var issetCpuUsageStats bool = false
	var issetInterval bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowLogCountStats = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuUsageStats = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInterval = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSlowLogCountStats {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCpuUsageStats {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInterval {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogTimeSeriesStatsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogTimeSeriesStatsResp[fieldId]))
}

func (p *DescribeSlowLogTimeSeriesStatsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowLogCount, 0, size)
	values := make([]SlowLogCount, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowLogCountStats = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeCpuUsage, 0, size)
	values := make([]NodeCpuUsage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CpuUsageStats = _field
	return nil
}
func (p *DescribeSlowLogTimeSeriesStatsResp) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Interval = _field
	return nil
}

func (p *DescribeSlowLogTimeSeriesStatsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogTimeSeriesStatsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogTimeSeriesStatsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowLogCountStats", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowLogCountStats)); err != nil {
		return err
	}
	for _, v := range p.SlowLogCountStats {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuUsageStats", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CpuUsageStats)); err != nil {
		return err
	}
	for _, v := range p.CpuUsageStats {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Interval", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Interval); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogTimeSeriesStatsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogTimeSeriesStatsResp(%+v)", *p)

}

func (p *DescribeSlowLogTimeSeriesStatsResp) DeepEqual(ano *DescribeSlowLogTimeSeriesStatsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SlowLogCountStats) {
		return false
	}
	if !p.Field2DeepEqual(ano.CpuUsageStats) {
		return false
	}
	if !p.Field3DeepEqual(ano.Interval) {
		return false
	}
	return true
}

func (p *DescribeSlowLogTimeSeriesStatsResp) Field1DeepEqual(src []*SlowLogCount) bool {

	if len(p.SlowLogCountStats) != len(src) {
		return false
	}
	for i, v := range p.SlowLogCountStats {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsResp) Field2DeepEqual(src []*NodeCpuUsage) bool {

	if len(p.CpuUsageStats) != len(src) {
		return false
	}
	for i, v := range p.CpuUsageStats {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSlowLogTimeSeriesStatsResp) Field3DeepEqual(src int32) bool {

	if p.Interval != src {
		return false
	}
	return true
}

type NodeCpuUsage struct {
	NodeId   string      `thrift:"NodeId,1,required" frugal:"1,required,string" json:"NodeId"`
	RoleType string      `thrift:"RoleType,2,required" frugal:"2,required,string" json:"RoleType"`
	CpuList  []*CpuUsage `thrift:"CpuList,3,required" frugal:"3,required,list<CpuUsage>" json:"CpuList"`
}

func NewNodeCpuUsage() *NodeCpuUsage {
	return &NodeCpuUsage{}
}

func (p *NodeCpuUsage) InitDefault() {
}

func (p *NodeCpuUsage) GetNodeId() (v string) {
	return p.NodeId
}

func (p *NodeCpuUsage) GetRoleType() (v string) {
	return p.RoleType
}

func (p *NodeCpuUsage) GetCpuList() (v []*CpuUsage) {
	return p.CpuList
}
func (p *NodeCpuUsage) SetNodeId(val string) {
	p.NodeId = val
}
func (p *NodeCpuUsage) SetRoleType(val string) {
	p.RoleType = val
}
func (p *NodeCpuUsage) SetCpuList(val []*CpuUsage) {
	p.CpuList = val
}

var fieldIDToName_NodeCpuUsage = map[int16]string{
	1: "NodeId",
	2: "RoleType",
	3: "CpuList",
}

func (p *NodeCpuUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("NodeCpuUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeId bool = false
	var issetRoleType bool = false
	var issetCpuList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRoleType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRoleType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCpuList {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_NodeCpuUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_NodeCpuUsage[fieldId]))
}

func (p *NodeCpuUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *NodeCpuUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RoleType = _field
	return nil
}
func (p *NodeCpuUsage) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CpuUsage, 0, size)
	values := make([]CpuUsage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CpuList = _field
	return nil
}

func (p *NodeCpuUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("NodeCpuUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("NodeCpuUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *NodeCpuUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *NodeCpuUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RoleType", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RoleType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *NodeCpuUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuList", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CpuList)); err != nil {
		return err
	}
	for _, v := range p.CpuList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *NodeCpuUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeCpuUsage(%+v)", *p)

}

func (p *NodeCpuUsage) DeepEqual(ano *NodeCpuUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field2DeepEqual(ano.RoleType) {
		return false
	}
	if !p.Field3DeepEqual(ano.CpuList) {
		return false
	}
	return true
}

func (p *NodeCpuUsage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *NodeCpuUsage) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RoleType, src) != 0 {
		return false
	}
	return true
}
func (p *NodeCpuUsage) Field3DeepEqual(src []*CpuUsage) bool {

	if len(p.CpuList) != len(src) {
		return false
	}
	for i, v := range p.CpuList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeAggregateSlowLogsReq struct {
	RegionId           string                       `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string                       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             *DSType                      `thrift:"DSType,3,optional" frugal:"3,optional,DSType" json:"DSType,omitempty"`
	StartTime          int32                        `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32                        `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SortBy             *SortBy                      `thrift:"SortBy,6,optional" frugal:"6,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy            *OrderByForAggregateSlowLog  `thrift:"OrderBy,7,optional" frugal:"7,optional,OrderByForAggregateSlowLog" json:"OrderBy,omitempty"`
	PageNumber         *int32                       `thrift:"PageNumber,8,optional" frugal:"8,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32                       `thrift:"PageSize,9,optional" frugal:"9,optional,i32" json:"PageSize,omitempty"`
	SearchParam        *AggregateSlowLogSearchParam `thrift:"SearchParam,10,optional" frugal:"10,optional,AggregateSlowLogSearchParam" json:"SearchParam,omitempty"`
	NodeId             *string                      `thrift:"NodeId,11,optional" frugal:"11,optional,string" json:"NodeId,omitempty"`
	InstanceType       *DSType                      `thrift:"InstanceType,12,optional" frugal:"12,optional,DSType" json:"InstanceType,omitempty"`
	ShardingInstanceId *string                      `thrift:"ShardingInstanceId,13,optional" frugal:"13,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType                 `thrift:"NodeType,14,optional" frugal:"14,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeAggregateSlowLogsReq() *DescribeAggregateSlowLogsReq {
	return &DescribeAggregateSlowLogsReq{}
}

func (p *DescribeAggregateSlowLogsReq) InitDefault() {
}

func (p *DescribeAggregateSlowLogsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAggregateSlowLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeAggregateSlowLogsReq_DSType_DEFAULT DSType

func (p *DescribeAggregateSlowLogsReq) GetDSType() (v DSType) {
	if !p.IsSetDSType() {
		return DescribeAggregateSlowLogsReq_DSType_DEFAULT
	}
	return *p.DSType
}

func (p *DescribeAggregateSlowLogsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeAggregateSlowLogsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeAggregateSlowLogsReq_SortBy_DEFAULT SortBy

func (p *DescribeAggregateSlowLogsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeAggregateSlowLogsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeAggregateSlowLogsReq_OrderBy_DEFAULT OrderByForAggregateSlowLog

func (p *DescribeAggregateSlowLogsReq) GetOrderBy() (v OrderByForAggregateSlowLog) {
	if !p.IsSetOrderBy() {
		return DescribeAggregateSlowLogsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeAggregateSlowLogsReq_PageNumber_DEFAULT int32

func (p *DescribeAggregateSlowLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeAggregateSlowLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeAggregateSlowLogsReq_PageSize_DEFAULT int32

func (p *DescribeAggregateSlowLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeAggregateSlowLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeAggregateSlowLogsReq_SearchParam_DEFAULT *AggregateSlowLogSearchParam

func (p *DescribeAggregateSlowLogsReq) GetSearchParam() (v *AggregateSlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeAggregateSlowLogsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeAggregateSlowLogsReq_NodeId_DEFAULT string

func (p *DescribeAggregateSlowLogsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeAggregateSlowLogsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeAggregateSlowLogsReq_InstanceType_DEFAULT DSType

func (p *DescribeAggregateSlowLogsReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeAggregateSlowLogsReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeAggregateSlowLogsReq_ShardingInstanceId_DEFAULT string

func (p *DescribeAggregateSlowLogsReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeAggregateSlowLogsReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeAggregateSlowLogsReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeAggregateSlowLogsReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeAggregateSlowLogsReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeAggregateSlowLogsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAggregateSlowLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAggregateSlowLogsReq) SetDSType(val *DSType) {
	p.DSType = val
}
func (p *DescribeAggregateSlowLogsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeAggregateSlowLogsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeAggregateSlowLogsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeAggregateSlowLogsReq) SetOrderBy(val *OrderByForAggregateSlowLog) {
	p.OrderBy = val
}
func (p *DescribeAggregateSlowLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeAggregateSlowLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeAggregateSlowLogsReq) SetSearchParam(val *AggregateSlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeAggregateSlowLogsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeAggregateSlowLogsReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeAggregateSlowLogsReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeAggregateSlowLogsReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeAggregateSlowLogsReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SortBy",
	7:  "OrderBy",
	8:  "PageNumber",
	9:  "PageSize",
	10: "SearchParam",
	11: "NodeId",
	12: "InstanceType",
	13: "ShardingInstanceId",
	14: "NodeType",
}

func (p *DescribeAggregateSlowLogsReq) IsSetDSType() bool {
	return p.DSType != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeAggregateSlowLogsReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeAggregateSlowLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateSlowLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAggregateSlowLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAggregateSlowLogsReq[fieldId]))
}

func (p *DescribeAggregateSlowLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.DSType = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *OrderByForAggregateSlowLog
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForAggregateSlowLog(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField10(iprot thrift.TProtocol) error {
	_field := NewAggregateSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeAggregateSlowLogsReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeAggregateSlowLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateSlowLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAggregateSlowLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDSType() {
		if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DSType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAggregateSlowLogsReq(%+v)", *p)

}

func (p *DescribeAggregateSlowLogsReq) DeepEqual(ano *DescribeAggregateSlowLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field9DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field10DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field11DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field12DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field13DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeAggregateSlowLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field3DeepEqual(src *DSType) bool {

	if p.DSType == src {
		return true
	} else if p.DSType == nil || src == nil {
		return false
	}
	if *p.DSType != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field6DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field7DeepEqual(src *OrderByForAggregateSlowLog) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field8DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field9DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field10DeepEqual(src *AggregateSlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field11DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field12DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field13DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateSlowLogsReq) Field14DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeAggregateSlowLogsResp struct {
	AggregateSlowLogs []*AggregateSlowLog `thrift:"AggregateSlowLogs,1,required" frugal:"1,required,list<AggregateSlowLog>" json:"AggregateSlowLogs"`
	Total             int32               `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeAggregateSlowLogsResp() *DescribeAggregateSlowLogsResp {
	return &DescribeAggregateSlowLogsResp{}
}

func (p *DescribeAggregateSlowLogsResp) InitDefault() {
}

func (p *DescribeAggregateSlowLogsResp) GetAggregateSlowLogs() (v []*AggregateSlowLog) {
	return p.AggregateSlowLogs
}

func (p *DescribeAggregateSlowLogsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeAggregateSlowLogsResp) SetAggregateSlowLogs(val []*AggregateSlowLog) {
	p.AggregateSlowLogs = val
}
func (p *DescribeAggregateSlowLogsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeAggregateSlowLogsResp = map[int16]string{
	1: "AggregateSlowLogs",
	2: "Total",
}

func (p *DescribeAggregateSlowLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateSlowLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAggregateSlowLogs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAggregateSlowLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAggregateSlowLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAggregateSlowLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAggregateSlowLogsResp[fieldId]))
}

func (p *DescribeAggregateSlowLogsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AggregateSlowLog, 0, size)
	values := make([]AggregateSlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AggregateSlowLogs = _field
	return nil
}
func (p *DescribeAggregateSlowLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeAggregateSlowLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateSlowLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAggregateSlowLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AggregateSlowLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AggregateSlowLogs)); err != nil {
		return err
	}
	for _, v := range p.AggregateSlowLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAggregateSlowLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAggregateSlowLogsResp(%+v)", *p)

}

func (p *DescribeAggregateSlowLogsResp) DeepEqual(ano *DescribeAggregateSlowLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AggregateSlowLogs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeAggregateSlowLogsResp) Field1DeepEqual(src []*AggregateSlowLog) bool {

	if len(p.AggregateSlowLogs) != len(src) {
		return false
	}
	for i, v := range p.AggregateSlowLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAggregateSlowLogsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeSlowLogsReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             *DSType             `thrift:"DSType,3,optional" frugal:"3,optional,DSType" json:"DSType,omitempty"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	PageNumber         *int32              `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32              `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	SortBy             *SortBy             `thrift:"SortBy,9,optional" frugal:"9,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy            *OrderByForSlowLog  `thrift:"OrderBy,10,optional" frugal:"10,optional,OrderByForSlowLog" json:"OrderBy,omitempty"`
	InstanceType       *DSType             `thrift:"InstanceType,11,optional" frugal:"11,optional,DSType" json:"InstanceType,omitempty"`
	NodeId             *string             `thrift:"NodeId,12,optional" frugal:"12,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,13,optional" frugal:"13,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,14,optional" frugal:"14,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeSlowLogsReq() *DescribeSlowLogsReq {
	return &DescribeSlowLogsReq{}
}

func (p *DescribeSlowLogsReq) InitDefault() {
}

func (p *DescribeSlowLogsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeSlowLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeSlowLogsReq_DSType_DEFAULT DSType

func (p *DescribeSlowLogsReq) GetDSType() (v DSType) {
	if !p.IsSetDSType() {
		return DescribeSlowLogsReq_DSType_DEFAULT
	}
	return *p.DSType
}

func (p *DescribeSlowLogsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSlowLogsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeSlowLogsReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeSlowLogsReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSlowLogsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSlowLogsReq_PageNumber_DEFAULT int32

func (p *DescribeSlowLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSlowLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSlowLogsReq_PageSize_DEFAULT int32

func (p *DescribeSlowLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSlowLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSlowLogsReq_SortBy_DEFAULT SortBy

func (p *DescribeSlowLogsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeSlowLogsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeSlowLogsReq_OrderBy_DEFAULT OrderByForSlowLog

func (p *DescribeSlowLogsReq) GetOrderBy() (v OrderByForSlowLog) {
	if !p.IsSetOrderBy() {
		return DescribeSlowLogsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeSlowLogsReq_InstanceType_DEFAULT DSType

func (p *DescribeSlowLogsReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSlowLogsReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSlowLogsReq_NodeId_DEFAULT string

func (p *DescribeSlowLogsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeSlowLogsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeSlowLogsReq_ShardingInstanceId_DEFAULT string

func (p *DescribeSlowLogsReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeSlowLogsReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeSlowLogsReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeSlowLogsReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeSlowLogsReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeSlowLogsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeSlowLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogsReq) SetDSType(val *DSType) {
	p.DSType = val
}
func (p *DescribeSlowLogsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSlowLogsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSlowLogsReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeSlowLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSlowLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSlowLogsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeSlowLogsReq) SetOrderBy(val *OrderByForSlowLog) {
	p.OrderBy = val
}
func (p *DescribeSlowLogsReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSlowLogsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeSlowLogsReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeSlowLogsReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeSlowLogsReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SearchParam",
	7:  "PageNumber",
	8:  "PageSize",
	9:  "SortBy",
	10: "OrderBy",
	11: "InstanceType",
	12: "NodeId",
	13: "ShardingInstanceId",
	14: "NodeType",
}

func (p *DescribeSlowLogsReq) IsSetDSType() bool {
	return p.DSType != nil
}

func (p *DescribeSlowLogsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSlowLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSlowLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSlowLogsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeSlowLogsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeSlowLogsReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSlowLogsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeSlowLogsReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeSlowLogsReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeSlowLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsReq[fieldId]))
}

func (p *DescribeSlowLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *OrderByForSlowLog
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForSlowLog(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeSlowLogsReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeSlowLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDSType() {
		if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DSType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeSlowLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsReq(%+v)", *p)

}

func (p *DescribeSlowLogsReq) DeepEqual(ano *DescribeSlowLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field9DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field10DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field11DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field12DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field13DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field3DeepEqual(src *DSType) bool {

	if p.DSType == src {
		return true
	} else if p.DSType == nil || src == nil {
		return false
	}
	if *p.DSType != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field9DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field10DeepEqual(src *OrderByForSlowLog) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field11DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field12DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field13DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsReq) Field14DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeSlowLogsResp struct {
	SlowLogs []*SlowLog `thrift:"SlowLogs,1,required" frugal:"1,required,list<SlowLog>" json:"SlowLogs"`
	Total    int32      `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSlowLogsResp() *DescribeSlowLogsResp {
	return &DescribeSlowLogsResp{}
}

func (p *DescribeSlowLogsResp) InitDefault() {
}

func (p *DescribeSlowLogsResp) GetSlowLogs() (v []*SlowLog) {
	return p.SlowLogs
}

func (p *DescribeSlowLogsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSlowLogsResp) SetSlowLogs(val []*SlowLog) {
	p.SlowLogs = val
}
func (p *DescribeSlowLogsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSlowLogsResp = map[int16]string{
	1: "SlowLogs",
	2: "Total",
}

func (p *DescribeSlowLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSlowLogs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSlowLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsResp[fieldId]))
}

func (p *DescribeSlowLogsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowLog, 0, size)
	values := make([]SlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowLogs = _field
	return nil
}
func (p *DescribeSlowLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSlowLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowLogs)); err != nil {
		return err
	}
	for _, v := range p.SlowLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsResp(%+v)", *p)

}

func (p *DescribeSlowLogsResp) DeepEqual(ano *DescribeSlowLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SlowLogs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsResp) Field1DeepEqual(src []*SlowLog) bool {

	if len(p.SlowLogs) != len(src) {
		return false
	}
	for i, v := range p.SlowLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSlowLogsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeSlowLogsDetailReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	Context            *string             `thrift:"Context,7,optional" frugal:"7,optional,string" json:"Context,omitempty"`
	PageSize           *int32              `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	InstanceType       *DSType             `thrift:"InstanceType,11,optional" frugal:"11,optional,DSType" json:"InstanceType,omitempty"`
	NodeId             *string             `thrift:"NodeId,12,optional" frugal:"12,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,13,optional" frugal:"13,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,14,optional" frugal:"14,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeSlowLogsDetailReq() *DescribeSlowLogsDetailReq {
	return &DescribeSlowLogsDetailReq{}
}

func (p *DescribeSlowLogsDetailReq) InitDefault() {
}

func (p *DescribeSlowLogsDetailReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeSlowLogsDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeSlowLogsDetailReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSlowLogsDetailReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeSlowLogsDetailReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeSlowLogsDetailReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSlowLogsDetailReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSlowLogsDetailReq_Context_DEFAULT string

func (p *DescribeSlowLogsDetailReq) GetContext() (v string) {
	if !p.IsSetContext() {
		return DescribeSlowLogsDetailReq_Context_DEFAULT
	}
	return *p.Context
}

var DescribeSlowLogsDetailReq_PageSize_DEFAULT int32

func (p *DescribeSlowLogsDetailReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSlowLogsDetailReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSlowLogsDetailReq_InstanceType_DEFAULT DSType

func (p *DescribeSlowLogsDetailReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSlowLogsDetailReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSlowLogsDetailReq_NodeId_DEFAULT string

func (p *DescribeSlowLogsDetailReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeSlowLogsDetailReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeSlowLogsDetailReq_ShardingInstanceId_DEFAULT string

func (p *DescribeSlowLogsDetailReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeSlowLogsDetailReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeSlowLogsDetailReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeSlowLogsDetailReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeSlowLogsDetailReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeSlowLogsDetailReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeSlowLogsDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogsDetailReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSlowLogsDetailReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSlowLogsDetailReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeSlowLogsDetailReq) SetContext(val *string) {
	p.Context = val
}
func (p *DescribeSlowLogsDetailReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSlowLogsDetailReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSlowLogsDetailReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeSlowLogsDetailReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeSlowLogsDetailReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeSlowLogsDetailReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SearchParam",
	7:  "Context",
	8:  "PageSize",
	11: "InstanceType",
	12: "NodeId",
	13: "ShardingInstanceId",
	14: "NodeType",
}

func (p *DescribeSlowLogsDetailReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetContext() bool {
	return p.Context != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeSlowLogsDetailReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeSlowLogsDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsDetailReq[fieldId]))
}

func (p *DescribeSlowLogsDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Context = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeSlowLogsDetailReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeSlowLogsDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetContext() {
		if err = oprot.WriteFieldBegin("Context", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Context); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsDetailReq(%+v)", *p)

}

func (p *DescribeSlowLogsDetailReq) DeepEqual(ano *DescribeSlowLogsDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.Context) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field11DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field12DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field13DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field7DeepEqual(src *string) bool {

	if p.Context == src {
		return true
	} else if p.Context == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Context, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field11DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field12DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field13DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailReq) Field14DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeSlowLogsDetailResp struct {
	SlowLogs []*SlowLog `thrift:"SlowLogs,1,required" frugal:"1,required,list<SlowLog>" json:"SlowLogs"`
	Total    int32      `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
	ListOver bool       `thrift:"ListOver,3,required" frugal:"3,required,bool" json:"ListOver"`
	Context  *string    `thrift:"Context,4,optional" frugal:"4,optional,string" json:"Context,omitempty"`
}

func NewDescribeSlowLogsDetailResp() *DescribeSlowLogsDetailResp {
	return &DescribeSlowLogsDetailResp{}
}

func (p *DescribeSlowLogsDetailResp) InitDefault() {
}

func (p *DescribeSlowLogsDetailResp) GetSlowLogs() (v []*SlowLog) {
	return p.SlowLogs
}

func (p *DescribeSlowLogsDetailResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeSlowLogsDetailResp) GetListOver() (v bool) {
	return p.ListOver
}

var DescribeSlowLogsDetailResp_Context_DEFAULT string

func (p *DescribeSlowLogsDetailResp) GetContext() (v string) {
	if !p.IsSetContext() {
		return DescribeSlowLogsDetailResp_Context_DEFAULT
	}
	return *p.Context
}
func (p *DescribeSlowLogsDetailResp) SetSlowLogs(val []*SlowLog) {
	p.SlowLogs = val
}
func (p *DescribeSlowLogsDetailResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeSlowLogsDetailResp) SetListOver(val bool) {
	p.ListOver = val
}
func (p *DescribeSlowLogsDetailResp) SetContext(val *string) {
	p.Context = val
}

var fieldIDToName_DescribeSlowLogsDetailResp = map[int16]string{
	1: "SlowLogs",
	2: "Total",
	3: "ListOver",
	4: "Context",
}

func (p *DescribeSlowLogsDetailResp) IsSetContext() bool {
	return p.Context != nil
}

func (p *DescribeSlowLogsDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSlowLogs bool = false
	var issetTotal bool = false
	var issetListOver bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetListOver = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSlowLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetListOver {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsDetailResp[fieldId]))
}

func (p *DescribeSlowLogsDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowLog, 0, size)
	values := make([]SlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowLogs = _field
	return nil
}
func (p *DescribeSlowLogsDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeSlowLogsDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ListOver = _field
	return nil
}
func (p *DescribeSlowLogsDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Context = _field
	return nil
}

func (p *DescribeSlowLogsDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowLogs)); err != nil {
		return err
	}
	for _, v := range p.SlowLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ListOver", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ListOver); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetContext() {
		if err = oprot.WriteFieldBegin("Context", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Context); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogsDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsDetailResp(%+v)", *p)

}

func (p *DescribeSlowLogsDetailResp) DeepEqual(ano *DescribeSlowLogsDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SlowLogs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	if !p.Field3DeepEqual(ano.ListOver) {
		return false
	}
	if !p.Field4DeepEqual(ano.Context) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsDetailResp) Field1DeepEqual(src []*SlowLog) bool {

	if len(p.SlowLogs) != len(src) {
		return false
	}
	for i, v := range p.SlowLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSlowLogsDetailResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailResp) Field3DeepEqual(src bool) bool {

	if p.ListOver != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsDetailResp) Field4DeepEqual(src *string) bool {

	if p.Context == src {
		return true
	} else if p.Context == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Context, *src) != 0 {
		return false
	}
	return true
}

type DescribeUsersReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType              `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	PageNumber         *int32              `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32              `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	NodeId             *string             `thrift:"NodeId,9,optional" frugal:"9,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,10,optional" frugal:"10,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,11,optional" frugal:"11,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeUsersReq() *DescribeUsersReq {
	return &DescribeUsersReq{}
}

func (p *DescribeUsersReq) InitDefault() {
}

func (p *DescribeUsersReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeUsersReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeUsersReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeUsersReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeUsersReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeUsersReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeUsersReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeUsersReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeUsersReq_PageNumber_DEFAULT int32

func (p *DescribeUsersReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeUsersReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeUsersReq_PageSize_DEFAULT int32

func (p *DescribeUsersReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeUsersReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeUsersReq_NodeId_DEFAULT string

func (p *DescribeUsersReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeUsersReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeUsersReq_ShardingInstanceId_DEFAULT string

func (p *DescribeUsersReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeUsersReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeUsersReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeUsersReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeUsersReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeUsersReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeUsersReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeUsersReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeUsersReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeUsersReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeUsersReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeUsersReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeUsersReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeUsersReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeUsersReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeUsersReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeUsersReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SearchParam",
	7:  "PageNumber",
	8:  "PageSize",
	9:  "NodeId",
	10: "ShardingInstanceId",
	11: "NodeType",
}

func (p *DescribeUsersReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeUsersReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeUsersReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeUsersReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeUsersReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeUsersReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeUsersReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeUsersReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeUsersReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeUsersReq[fieldId]))
}

func (p *DescribeUsersReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeUsersReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeUsersReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeUsersReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeUsersReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeUsersReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeUsersReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeUsersReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeUsersReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeUsersReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeUsersReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeUsersReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeUsersReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeUsersReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeUsersReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeUsersReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeUsersReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeUsersReq(%+v)", *p)

}

func (p *DescribeUsersReq) DeepEqual(ano *DescribeUsersReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field11DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeUsersReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field9DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field10DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeUsersReq) Field11DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeUsersResp struct {
	Users []string `thrift:"Users,1,required" frugal:"1,required,list<string>" json:"Users"`
	Total int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeUsersResp() *DescribeUsersResp {
	return &DescribeUsersResp{}
}

func (p *DescribeUsersResp) InitDefault() {
}

func (p *DescribeUsersResp) GetUsers() (v []string) {
	return p.Users
}

func (p *DescribeUsersResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeUsersResp) SetUsers(val []string) {
	p.Users = val
}
func (p *DescribeUsersResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeUsersResp = map[int16]string{
	1: "Users",
	2: "Total",
}

func (p *DescribeUsersResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeUsersResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUsers bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUsers = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUsers {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeUsersResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeUsersResp[fieldId]))
}

func (p *DescribeUsersResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Users = _field
	return nil
}
func (p *DescribeUsersResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeUsersResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeUsersResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeUsersResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeUsersResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Users", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Users)); err != nil {
		return err
	}
	for _, v := range p.Users {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeUsersResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeUsersResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeUsersResp(%+v)", *p)

}

func (p *DescribeUsersResp) DeepEqual(ano *DescribeUsersResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Users) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeUsersResp) Field1DeepEqual(src []string) bool {

	if len(p.Users) != len(src) {
		return false
	}
	for i, v := range p.Users {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeUsersResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeSourceIPsReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType              `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	PageNumber         *int32              `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32              `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	NodeId             *string             `thrift:"NodeId,9,optional" frugal:"9,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,10,optional" frugal:"10,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,11,optional" frugal:"11,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeSourceIPsReq() *DescribeSourceIPsReq {
	return &DescribeSourceIPsReq{}
}

func (p *DescribeSourceIPsReq) InitDefault() {
}

func (p *DescribeSourceIPsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeSourceIPsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeSourceIPsReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSourceIPsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSourceIPsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeSourceIPsReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeSourceIPsReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSourceIPsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSourceIPsReq_PageNumber_DEFAULT int32

func (p *DescribeSourceIPsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSourceIPsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSourceIPsReq_PageSize_DEFAULT int32

func (p *DescribeSourceIPsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSourceIPsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSourceIPsReq_NodeId_DEFAULT string

func (p *DescribeSourceIPsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeSourceIPsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeSourceIPsReq_ShardingInstanceId_DEFAULT string

func (p *DescribeSourceIPsReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeSourceIPsReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeSourceIPsReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeSourceIPsReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeSourceIPsReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeSourceIPsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeSourceIPsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSourceIPsReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSourceIPsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSourceIPsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSourceIPsReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeSourceIPsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSourceIPsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSourceIPsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeSourceIPsReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeSourceIPsReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeSourceIPsReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SearchParam",
	7:  "PageNumber",
	8:  "PageSize",
	9:  "NodeId",
	10: "ShardingInstanceId",
	11: "NodeType",
}

func (p *DescribeSourceIPsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSourceIPsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSourceIPsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSourceIPsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeSourceIPsReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeSourceIPsReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeSourceIPsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSourceIPsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSourceIPsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSourceIPsReq[fieldId]))
}

func (p *DescribeSourceIPsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeSourceIPsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeSourceIPsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSourceIPsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSourceIPsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeSourceIPsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSourceIPsReq(%+v)", *p)

}

func (p *DescribeSourceIPsReq) DeepEqual(ano *DescribeSourceIPsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field11DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeSourceIPsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field9DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field10DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSourceIPsReq) Field11DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeSourceIPsResp struct {
	SourceIPs []string `thrift:"SourceIPs,1,required" frugal:"1,required,list<string>" json:"SourceIPs"`
	Total     int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSourceIPsResp() *DescribeSourceIPsResp {
	return &DescribeSourceIPsResp{}
}

func (p *DescribeSourceIPsResp) InitDefault() {
}

func (p *DescribeSourceIPsResp) GetSourceIPs() (v []string) {
	return p.SourceIPs
}

func (p *DescribeSourceIPsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSourceIPsResp) SetSourceIPs(val []string) {
	p.SourceIPs = val
}
func (p *DescribeSourceIPsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSourceIPsResp = map[int16]string{
	1: "SourceIPs",
	2: "Total",
}

func (p *DescribeSourceIPsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSourceIPsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSourceIPs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSourceIPs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSourceIPs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSourceIPsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSourceIPsResp[fieldId]))
}

func (p *DescribeSourceIPsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SourceIPs = _field
	return nil
}
func (p *DescribeSourceIPsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSourceIPsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSourceIPsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSourceIPsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSourceIPsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SourceIPs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SourceIPs)); err != nil {
		return err
	}
	for _, v := range p.SourceIPs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSourceIPsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSourceIPsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSourceIPsResp(%+v)", *p)

}

func (p *DescribeSourceIPsResp) DeepEqual(ano *DescribeSourceIPsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SourceIPs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSourceIPsResp) Field1DeepEqual(src []string) bool {

	if len(p.SourceIPs) != len(src) {
		return false
	}
	for i, v := range p.SourceIPs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeSourceIPsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeDBsReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType              `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	PageNumber         *int32              `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32              `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	NodeId             *string             `thrift:"NodeId,9,optional" frugal:"9,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,10,optional" frugal:"10,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,11,optional" frugal:"11,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeDBsReq() *DescribeDBsReq {
	return &DescribeDBsReq{}
}

func (p *DescribeDBsReq) InitDefault() {
}

func (p *DescribeDBsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeDBsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBsReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeDBsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeDBsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeDBsReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeDBsReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeDBsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeDBsReq_PageNumber_DEFAULT int32

func (p *DescribeDBsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDBsReq_PageSize_DEFAULT int32

func (p *DescribeDBsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDBsReq_NodeId_DEFAULT string

func (p *DescribeDBsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeDBsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeDBsReq_ShardingInstanceId_DEFAULT string

func (p *DescribeDBsReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeDBsReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeDBsReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeDBsReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeDBsReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeDBsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeDBsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBsReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeDBsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeDBsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeDBsReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeDBsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDBsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDBsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeDBsReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeDBsReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeDBsReq = map[int16]string{
	1:  "RegionId",
	2:  "InstanceId",
	3:  "DSType",
	4:  "StartTime",
	5:  "EndTime",
	6:  "SearchParam",
	7:  "PageNumber",
	8:  "PageSize",
	9:  "NodeId",
	10: "ShardingInstanceId",
	11: "NodeType",
}

func (p *DescribeDBsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeDBsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeDBsReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeDBsReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeDBsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBsReq[fieldId]))
}

func (p *DescribeDBsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeDBsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeDBsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeDBsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeDBsReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeDBsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDBsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDBsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeDBsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeDBsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeDBsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDBsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeDBsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBsReq(%+v)", *p)

}

func (p *DescribeDBsReq) DeepEqual(ano *DescribeDBsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field11DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeDBsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field9DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field10DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBsReq) Field11DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeDBsResp struct {
	DBs   []string `thrift:"DBs,1,required" frugal:"1,required,list<string>" json:"DBs"`
	Total int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeDBsResp() *DescribeDBsResp {
	return &DescribeDBsResp{}
}

func (p *DescribeDBsResp) InitDefault() {
}

func (p *DescribeDBsResp) GetDBs() (v []string) {
	return p.DBs
}

func (p *DescribeDBsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeDBsResp) SetDBs(val []string) {
	p.DBs = val
}
func (p *DescribeDBsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeDBsResp = map[int16]string{
	1: "DBs",
	2: "Total",
}

func (p *DescribeDBsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBsResp[fieldId]))
}

func (p *DescribeDBsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DBs = _field
	return nil
}
func (p *DescribeDBsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeDBsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.DBs)); err != nil {
		return err
	}
	for _, v := range p.DBs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBsResp(%+v)", *p)

}

func (p *DescribeDBsResp) DeepEqual(ano *DescribeDBsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeDBsResp) Field1DeepEqual(src []string) bool {

	if len(p.DBs) != len(src) {
		return false
	}
	for i, v := range p.DBs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeDBsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeAvailableTLSTopicReq struct {
	LabelType          *LabelType   `thrift:"LabelType,1,optional" frugal:"1,optional,LabelType" json:"LabelType,omitempty"`
	DSType             *DSType      `thrift:"DSType,2,optional" frugal:"2,optional,DSType" json:"DSType,omitempty"`
	ShardingInstanceId *string      `thrift:"ShardingInstanceId,3,optional" frugal:"3,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType `thrift:"NodeType,4,optional" frugal:"4,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeAvailableTLSTopicReq() *DescribeAvailableTLSTopicReq {
	return &DescribeAvailableTLSTopicReq{}
}

func (p *DescribeAvailableTLSTopicReq) InitDefault() {
}

var DescribeAvailableTLSTopicReq_LabelType_DEFAULT LabelType

func (p *DescribeAvailableTLSTopicReq) GetLabelType() (v LabelType) {
	if !p.IsSetLabelType() {
		return DescribeAvailableTLSTopicReq_LabelType_DEFAULT
	}
	return *p.LabelType
}

var DescribeAvailableTLSTopicReq_DSType_DEFAULT DSType

func (p *DescribeAvailableTLSTopicReq) GetDSType() (v DSType) {
	if !p.IsSetDSType() {
		return DescribeAvailableTLSTopicReq_DSType_DEFAULT
	}
	return *p.DSType
}

var DescribeAvailableTLSTopicReq_ShardingInstanceId_DEFAULT string

func (p *DescribeAvailableTLSTopicReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeAvailableTLSTopicReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeAvailableTLSTopicReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeAvailableTLSTopicReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeAvailableTLSTopicReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeAvailableTLSTopicReq) SetLabelType(val *LabelType) {
	p.LabelType = val
}
func (p *DescribeAvailableTLSTopicReq) SetDSType(val *DSType) {
	p.DSType = val
}
func (p *DescribeAvailableTLSTopicReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeAvailableTLSTopicReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeAvailableTLSTopicReq = map[int16]string{
	1: "LabelType",
	2: "DSType",
	3: "ShardingInstanceId",
	4: "NodeType",
}

func (p *DescribeAvailableTLSTopicReq) IsSetLabelType() bool {
	return p.LabelType != nil
}

func (p *DescribeAvailableTLSTopicReq) IsSetDSType() bool {
	return p.DSType != nil
}

func (p *DescribeAvailableTLSTopicReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeAvailableTLSTopicReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeAvailableTLSTopicReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableTLSTopicReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableTLSTopicReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *LabelType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LabelType(v)
		_field = &tmp
	}
	p.LabelType = _field
	return nil
}
func (p *DescribeAvailableTLSTopicReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.DSType = _field
	return nil
}
func (p *DescribeAvailableTLSTopicReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeAvailableTLSTopicReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeAvailableTLSTopicReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableTLSTopicReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailableTLSTopicReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetLabelType() {
		if err = oprot.WriteFieldBegin("LabelType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LabelType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDSType() {
		if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DSType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailableTLSTopicReq(%+v)", *p)

}

func (p *DescribeAvailableTLSTopicReq) DeepEqual(ano *DescribeAvailableTLSTopicReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.LabelType) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeAvailableTLSTopicReq) Field1DeepEqual(src *LabelType) bool {

	if p.LabelType == src {
		return true
	} else if p.LabelType == nil || src == nil {
		return false
	}
	if *p.LabelType != *src {
		return false
	}
	return true
}
func (p *DescribeAvailableTLSTopicReq) Field2DeepEqual(src *DSType) bool {

	if p.DSType == src {
		return true
	} else if p.DSType == nil || src == nil {
		return false
	}
	if *p.DSType != *src {
		return false
	}
	return true
}
func (p *DescribeAvailableTLSTopicReq) Field3DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAvailableTLSTopicReq) Field4DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeAvailableTLSTopicResp struct {
	Endpoint string `thrift:"Endpoint,1,required" frugal:"1,required,string" json:"Endpoint"`
	Topic    string `thrift:"Topic,2,required" frugal:"2,required,string" json:"Topic"`
}

func NewDescribeAvailableTLSTopicResp() *DescribeAvailableTLSTopicResp {
	return &DescribeAvailableTLSTopicResp{}
}

func (p *DescribeAvailableTLSTopicResp) InitDefault() {
}

func (p *DescribeAvailableTLSTopicResp) GetEndpoint() (v string) {
	return p.Endpoint
}

func (p *DescribeAvailableTLSTopicResp) GetTopic() (v string) {
	return p.Topic
}
func (p *DescribeAvailableTLSTopicResp) SetEndpoint(val string) {
	p.Endpoint = val
}
func (p *DescribeAvailableTLSTopicResp) SetTopic(val string) {
	p.Topic = val
}

var fieldIDToName_DescribeAvailableTLSTopicResp = map[int16]string{
	1: "Endpoint",
	2: "Topic",
}

func (p *DescribeAvailableTLSTopicResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableTLSTopicResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEndpoint bool = false
	var issetTopic bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndpoint = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTopic = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEndpoint {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTopic {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAvailableTLSTopicResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAvailableTLSTopicResp[fieldId]))
}

func (p *DescribeAvailableTLSTopicResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Endpoint = _field
	return nil
}
func (p *DescribeAvailableTLSTopicResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Topic = _field
	return nil
}

func (p *DescribeAvailableTLSTopicResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAvailableTLSTopicResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAvailableTLSTopicResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Endpoint", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Endpoint); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Topic", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Topic); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAvailableTLSTopicResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAvailableTLSTopicResp(%+v)", *p)

}

func (p *DescribeAvailableTLSTopicResp) DeepEqual(ano *DescribeAvailableTLSTopicResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Endpoint) {
		return false
	}
	if !p.Field2DeepEqual(ano.Topic) {
		return false
	}
	return true
}

func (p *DescribeAvailableTLSTopicResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Endpoint, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAvailableTLSTopicResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Topic, src) != 0 {
		return false
	}
	return true
}

type DescribeExampleSQLReq struct {
	RegionId           string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType              `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime          int32               `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime            int32               `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	SearchParam        *SlowLogSearchParam `thrift:"SearchParam,6,optional" frugal:"6,optional,SlowLogSearchParam" json:"SearchParam,omitempty"`
	NodeId             *string             `thrift:"NodeId,7,optional" frugal:"7,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string             `thrift:"ShardingInstanceId,8,optional" frugal:"8,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType        `thrift:"NodeType,9,optional" frugal:"9,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeExampleSQLReq() *DescribeExampleSQLReq {
	return &DescribeExampleSQLReq{}
}

func (p *DescribeExampleSQLReq) InitDefault() {
}

func (p *DescribeExampleSQLReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeExampleSQLReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeExampleSQLReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeExampleSQLReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeExampleSQLReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeExampleSQLReq_SearchParam_DEFAULT *SlowLogSearchParam

func (p *DescribeExampleSQLReq) GetSearchParam() (v *SlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeExampleSQLReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeExampleSQLReq_NodeId_DEFAULT string

func (p *DescribeExampleSQLReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeExampleSQLReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeExampleSQLReq_ShardingInstanceId_DEFAULT string

func (p *DescribeExampleSQLReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeExampleSQLReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeExampleSQLReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeExampleSQLReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeExampleSQLReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeExampleSQLReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeExampleSQLReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeExampleSQLReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeExampleSQLReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeExampleSQLReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeExampleSQLReq) SetSearchParam(val *SlowLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeExampleSQLReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeExampleSQLReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeExampleSQLReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeExampleSQLReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "DSType",
	4: "StartTime",
	5: "EndTime",
	6: "SearchParam",
	7: "NodeId",
	8: "ShardingInstanceId",
	9: "NodeType",
}

func (p *DescribeExampleSQLReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeExampleSQLReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeExampleSQLReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeExampleSQLReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeExampleSQLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExampleSQLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeExampleSQLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeExampleSQLReq[fieldId]))
}

func (p *DescribeExampleSQLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeExampleSQLReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeExampleSQLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExampleSQLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeExampleSQLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeExampleSQLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeExampleSQLReq(%+v)", *p)

}

func (p *DescribeExampleSQLReq) DeepEqual(ano *DescribeExampleSQLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field8DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeExampleSQLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field6DeepEqual(src *SlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field7DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field8DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeExampleSQLReq) Field9DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeExampleSQLResp struct {
	SQLText string `thrift:"SQLText,1,required" frugal:"1,required,string" json:"SQLText"`
}

func NewDescribeExampleSQLResp() *DescribeExampleSQLResp {
	return &DescribeExampleSQLResp{}
}

func (p *DescribeExampleSQLResp) InitDefault() {
}

func (p *DescribeExampleSQLResp) GetSQLText() (v string) {
	return p.SQLText
}
func (p *DescribeExampleSQLResp) SetSQLText(val string) {
	p.SQLText = val
}

var fieldIDToName_DescribeExampleSQLResp = map[int16]string{
	1: "SQLText",
}

func (p *DescribeExampleSQLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExampleSQLResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLText bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLText {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeExampleSQLResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeExampleSQLResp[fieldId]))
}

func (p *DescribeExampleSQLResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLText = _field
	return nil
}

func (p *DescribeExampleSQLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExampleSQLResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeExampleSQLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeExampleSQLResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLText", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQLText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeExampleSQLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeExampleSQLResp(%+v)", *p)

}

func (p *DescribeExampleSQLResp) DeepEqual(ano *DescribeExampleSQLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLText) {
		return false
	}
	return true
}

func (p *DescribeExampleSQLResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SQLText, src) != 0 {
		return false
	}
	return true
}

type CreateSlowLogsExportTaskReq struct {
	TaskName           string                       `thrift:"TaskName,1,required" frugal:"1,required,string" json:"TaskName"`
	RegionId           string                       `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId         string                       `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	DSType             DSType                       `thrift:"DSType,4,required" frugal:"4,required,DSType" json:"DSType"`
	DataFormat         DataFormat                   `thrift:"DataFormat,5,required" frugal:"5,required,DataFormat" json:"DataFormat"`
	StartTime          int32                        `thrift:"StartTime,6,required" frugal:"6,required,i32" json:"StartTime"`
	EndTime            int32                        `thrift:"EndTime,7,required" frugal:"7,required,i32" json:"EndTime"`
	Compression        Compression                  `thrift:"Compression,8,required" frugal:"8,required,Compression" json:"Compression"`
	SearchParam        *AggregateSlowLogSearchParam `thrift:"SearchParam,9,optional" frugal:"9,optional,AggregateSlowLogSearchParam" json:"SearchParam,omitempty"`
	Sort               *SortBy                      `thrift:"Sort,10,optional" frugal:"10,optional,SortBy" json:"Sort,omitempty"`
	Limit              *int32                       `thrift:"Limit,11,optional" frugal:"11,optional,i32" json:"Limit,omitempty"`
	NodeId             *string                      `thrift:"NodeId,12,optional" frugal:"12,optional,string" json:"NodeId,omitempty"`
	ShardingInstanceId *string                      `thrift:"ShardingInstanceId,13,optional" frugal:"13,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType                 `thrift:"NodeType,14,optional" frugal:"14,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewCreateSlowLogsExportTaskReq() *CreateSlowLogsExportTaskReq {
	return &CreateSlowLogsExportTaskReq{}
}

func (p *CreateSlowLogsExportTaskReq) InitDefault() {
}

func (p *CreateSlowLogsExportTaskReq) GetTaskName() (v string) {
	return p.TaskName
}

func (p *CreateSlowLogsExportTaskReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *CreateSlowLogsExportTaskReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateSlowLogsExportTaskReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *CreateSlowLogsExportTaskReq) GetDataFormat() (v DataFormat) {
	return p.DataFormat
}

func (p *CreateSlowLogsExportTaskReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *CreateSlowLogsExportTaskReq) GetEndTime() (v int32) {
	return p.EndTime
}

func (p *CreateSlowLogsExportTaskReq) GetCompression() (v Compression) {
	return p.Compression
}

var CreateSlowLogsExportTaskReq_SearchParam_DEFAULT *AggregateSlowLogSearchParam

func (p *CreateSlowLogsExportTaskReq) GetSearchParam() (v *AggregateSlowLogSearchParam) {
	if !p.IsSetSearchParam() {
		return CreateSlowLogsExportTaskReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var CreateSlowLogsExportTaskReq_Sort_DEFAULT SortBy

func (p *CreateSlowLogsExportTaskReq) GetSort() (v SortBy) {
	if !p.IsSetSort() {
		return CreateSlowLogsExportTaskReq_Sort_DEFAULT
	}
	return *p.Sort
}

var CreateSlowLogsExportTaskReq_Limit_DEFAULT int32

func (p *CreateSlowLogsExportTaskReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return CreateSlowLogsExportTaskReq_Limit_DEFAULT
	}
	return *p.Limit
}

var CreateSlowLogsExportTaskReq_NodeId_DEFAULT string

func (p *CreateSlowLogsExportTaskReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return CreateSlowLogsExportTaskReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var CreateSlowLogsExportTaskReq_ShardingInstanceId_DEFAULT string

func (p *CreateSlowLogsExportTaskReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return CreateSlowLogsExportTaskReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var CreateSlowLogsExportTaskReq_NodeType_DEFAULT RdsNodeType

func (p *CreateSlowLogsExportTaskReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return CreateSlowLogsExportTaskReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *CreateSlowLogsExportTaskReq) SetTaskName(val string) {
	p.TaskName = val
}
func (p *CreateSlowLogsExportTaskReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *CreateSlowLogsExportTaskReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateSlowLogsExportTaskReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *CreateSlowLogsExportTaskReq) SetDataFormat(val DataFormat) {
	p.DataFormat = val
}
func (p *CreateSlowLogsExportTaskReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *CreateSlowLogsExportTaskReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *CreateSlowLogsExportTaskReq) SetCompression(val Compression) {
	p.Compression = val
}
func (p *CreateSlowLogsExportTaskReq) SetSearchParam(val *AggregateSlowLogSearchParam) {
	p.SearchParam = val
}
func (p *CreateSlowLogsExportTaskReq) SetSort(val *SortBy) {
	p.Sort = val
}
func (p *CreateSlowLogsExportTaskReq) SetLimit(val *int32) {
	p.Limit = val
}
func (p *CreateSlowLogsExportTaskReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *CreateSlowLogsExportTaskReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *CreateSlowLogsExportTaskReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_CreateSlowLogsExportTaskReq = map[int16]string{
	1:  "TaskName",
	2:  "RegionId",
	3:  "InstanceId",
	4:  "DSType",
	5:  "DataFormat",
	6:  "StartTime",
	7:  "EndTime",
	8:  "Compression",
	9:  "SearchParam",
	10: "Sort",
	11: "Limit",
	12: "NodeId",
	13: "ShardingInstanceId",
	14: "NodeType",
}

func (p *CreateSlowLogsExportTaskReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *CreateSlowLogsExportTaskReq) IsSetSort() bool {
	return p.Sort != nil
}

func (p *CreateSlowLogsExportTaskReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *CreateSlowLogsExportTaskReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *CreateSlowLogsExportTaskReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *CreateSlowLogsExportTaskReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *CreateSlowLogsExportTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSlowLogsExportTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskName bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetDataFormat bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetCompression bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFormat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetCompression = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDataFormat {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetCompression {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSlowLogsExportTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSlowLogsExportTaskReq[fieldId]))
}

func (p *CreateSlowLogsExportTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskName = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field DataFormat
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DataFormat(v)
	}
	p.DataFormat = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField8(iprot thrift.TProtocol) error {

	var _field Compression
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = Compression(v)
	}
	p.Compression = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField9(iprot thrift.TProtocol) error {
	_field := NewAggregateSlowLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.Sort = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *CreateSlowLogsExportTaskReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *CreateSlowLogsExportTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSlowLogsExportTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSlowLogsExportTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFormat", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataFormat)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Compression", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Compression)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSort() {
		if err = oprot.WriteFieldBegin("Sort", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Sort)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSlowLogsExportTaskReq(%+v)", *p)

}

func (p *CreateSlowLogsExportTaskReq) DeepEqual(ano *CreateSlowLogsExportTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskName) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field5DeepEqual(ano.DataFormat) {
		return false
	}
	if !p.Field6DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.Compression) {
		return false
	}
	if !p.Field9DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field10DeepEqual(ano.Sort) {
		return false
	}
	if !p.Field11DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field12DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field13DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *CreateSlowLogsExportTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field4DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field5DeepEqual(src DataFormat) bool {

	if p.DataFormat != src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field6DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field7DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field8DeepEqual(src Compression) bool {

	if p.Compression != src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field9DeepEqual(src *AggregateSlowLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field10DeepEqual(src *SortBy) bool {

	if p.Sort == src {
		return true
	} else if p.Sort == nil || src == nil {
		return false
	}
	if *p.Sort != *src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field11DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field12DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field13DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSlowLogsExportTaskReq) Field14DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type CreateSlowLogsExportTaskResp struct {
	TaskId string `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
}

func NewCreateSlowLogsExportTaskResp() *CreateSlowLogsExportTaskResp {
	return &CreateSlowLogsExportTaskResp{}
}

func (p *CreateSlowLogsExportTaskResp) InitDefault() {
}

func (p *CreateSlowLogsExportTaskResp) GetTaskId() (v string) {
	return p.TaskId
}
func (p *CreateSlowLogsExportTaskResp) SetTaskId(val string) {
	p.TaskId = val
}

var fieldIDToName_CreateSlowLogsExportTaskResp = map[int16]string{
	1: "TaskId",
}

func (p *CreateSlowLogsExportTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSlowLogsExportTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSlowLogsExportTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSlowLogsExportTaskResp[fieldId]))
}

func (p *CreateSlowLogsExportTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}

func (p *CreateSlowLogsExportTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSlowLogsExportTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSlowLogsExportTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSlowLogsExportTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSlowLogsExportTaskResp(%+v)", *p)

}

func (p *CreateSlowLogsExportTaskResp) DeepEqual(ano *CreateSlowLogsExportTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *CreateSlowLogsExportTaskResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}

type DescribeSlowLogsExportTasksReq struct {
	RegionId           string       `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType       `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	PageNumber         *int32       `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize           *int32       `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
	NodeId             *string      `thrift:"NodeId,6,optional" frugal:"6,optional,string" json:"NodeId,omitempty"`
	Keyword            *string      `thrift:"Keyword,7,optional" frugal:"7,optional,string" json:"Keyword,omitempty"`
	ShardingInstanceId *string      `thrift:"ShardingInstanceId,8,optional" frugal:"8,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType `thrift:"NodeType,9,optional" frugal:"9,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeSlowLogsExportTasksReq() *DescribeSlowLogsExportTasksReq {
	return &DescribeSlowLogsExportTasksReq{}
}

func (p *DescribeSlowLogsExportTasksReq) InitDefault() {
}

func (p *DescribeSlowLogsExportTasksReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeSlowLogsExportTasksReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeSlowLogsExportTasksReq) GetDSType() (v DSType) {
	return p.DSType
}

var DescribeSlowLogsExportTasksReq_PageNumber_DEFAULT int32

func (p *DescribeSlowLogsExportTasksReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSlowLogsExportTasksReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSlowLogsExportTasksReq_PageSize_DEFAULT int32

func (p *DescribeSlowLogsExportTasksReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSlowLogsExportTasksReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSlowLogsExportTasksReq_NodeId_DEFAULT string

func (p *DescribeSlowLogsExportTasksReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeSlowLogsExportTasksReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeSlowLogsExportTasksReq_Keyword_DEFAULT string

func (p *DescribeSlowLogsExportTasksReq) GetKeyword() (v string) {
	if !p.IsSetKeyword() {
		return DescribeSlowLogsExportTasksReq_Keyword_DEFAULT
	}
	return *p.Keyword
}

var DescribeSlowLogsExportTasksReq_ShardingInstanceId_DEFAULT string

func (p *DescribeSlowLogsExportTasksReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeSlowLogsExportTasksReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeSlowLogsExportTasksReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeSlowLogsExportTasksReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeSlowLogsExportTasksReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeSlowLogsExportTasksReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeSlowLogsExportTasksReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeSlowLogsExportTasksReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSlowLogsExportTasksReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSlowLogsExportTasksReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSlowLogsExportTasksReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeSlowLogsExportTasksReq) SetKeyword(val *string) {
	p.Keyword = val
}
func (p *DescribeSlowLogsExportTasksReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeSlowLogsExportTasksReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeSlowLogsExportTasksReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "DSType",
	4: "PageNumber",
	5: "PageSize",
	6: "NodeId",
	7: "Keyword",
	8: "ShardingInstanceId",
	9: "NodeType",
}

func (p *DescribeSlowLogsExportTasksReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSlowLogsExportTasksReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSlowLogsExportTasksReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeSlowLogsExportTasksReq) IsSetKeyword() bool {
	return p.Keyword != nil
}

func (p *DescribeSlowLogsExportTasksReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeSlowLogsExportTasksReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeSlowLogsExportTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsExportTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsExportTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsExportTasksReq[fieldId]))
}

func (p *DescribeSlowLogsExportTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Keyword = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeSlowLogsExportTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsExportTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsExportTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyword() {
		if err = oprot.WriteFieldBegin("Keyword", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Keyword); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsExportTasksReq(%+v)", *p)

}

func (p *DescribeSlowLogsExportTasksReq) DeepEqual(ano *DescribeSlowLogsExportTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field7DeepEqual(ano.Keyword) {
		return false
	}
	if !p.Field8DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsExportTasksReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field6DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field7DeepEqual(src *string) bool {

	if p.Keyword == src {
		return true
	} else if p.Keyword == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Keyword, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field8DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSlowLogsExportTasksReq) Field9DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeSlowLogsExportTasksResp struct {
	TaskInfos []*TaskInfo `thrift:"TaskInfos,1,required" frugal:"1,required,list<TaskInfo>" json:"TaskInfos"`
	Total     int32       `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSlowLogsExportTasksResp() *DescribeSlowLogsExportTasksResp {
	return &DescribeSlowLogsExportTasksResp{}
}

func (p *DescribeSlowLogsExportTasksResp) InitDefault() {
}

func (p *DescribeSlowLogsExportTasksResp) GetTaskInfos() (v []*TaskInfo) {
	return p.TaskInfos
}

func (p *DescribeSlowLogsExportTasksResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSlowLogsExportTasksResp) SetTaskInfos(val []*TaskInfo) {
	p.TaskInfos = val
}
func (p *DescribeSlowLogsExportTasksResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSlowLogsExportTasksResp = map[int16]string{
	1: "TaskInfos",
	2: "Total",
}

func (p *DescribeSlowLogsExportTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsExportTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskInfos bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSlowLogsExportTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSlowLogsExportTasksResp[fieldId]))
}

func (p *DescribeSlowLogsExportTasksResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TaskInfo, 0, size)
	values := make([]TaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskInfos = _field
	return nil
}
func (p *DescribeSlowLogsExportTasksResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSlowLogsExportTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSlowLogsExportTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSlowLogsExportTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskInfos)); err != nil {
		return err
	}
	for _, v := range p.TaskInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSlowLogsExportTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSlowLogsExportTasksResp(%+v)", *p)

}

func (p *DescribeSlowLogsExportTasksResp) DeepEqual(ano *DescribeSlowLogsExportTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskInfos) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSlowLogsExportTasksResp) Field1DeepEqual(src []*TaskInfo) bool {

	if len(p.TaskInfos) != len(src) {
		return false
	}
	for i, v := range p.TaskInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSlowLogsExportTasksResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeLogsDownloadUrlReq struct {
	RegionId           string       `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId         string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType             DSType       `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	TaskId             string       `thrift:"TaskId,4,required" frugal:"4,required,string" json:"TaskId"`
	ShardingInstanceId *string      `thrift:"ShardingInstanceId,5,optional" frugal:"5,optional,string" json:"ShardingInstanceId,omitempty"`
	NodeType           *RdsNodeType `thrift:"NodeType,6,optional" frugal:"6,optional,RdsNodeType" json:"NodeType,omitempty"`
}

func NewDescribeLogsDownloadUrlReq() *DescribeLogsDownloadUrlReq {
	return &DescribeLogsDownloadUrlReq{}
}

func (p *DescribeLogsDownloadUrlReq) InitDefault() {
}

func (p *DescribeLogsDownloadUrlReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeLogsDownloadUrlReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeLogsDownloadUrlReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeLogsDownloadUrlReq) GetTaskId() (v string) {
	return p.TaskId
}

var DescribeLogsDownloadUrlReq_ShardingInstanceId_DEFAULT string

func (p *DescribeLogsDownloadUrlReq) GetShardingInstanceId() (v string) {
	if !p.IsSetShardingInstanceId() {
		return DescribeLogsDownloadUrlReq_ShardingInstanceId_DEFAULT
	}
	return *p.ShardingInstanceId
}

var DescribeLogsDownloadUrlReq_NodeType_DEFAULT RdsNodeType

func (p *DescribeLogsDownloadUrlReq) GetNodeType() (v RdsNodeType) {
	if !p.IsSetNodeType() {
		return DescribeLogsDownloadUrlReq_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeLogsDownloadUrlReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeLogsDownloadUrlReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeLogsDownloadUrlReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeLogsDownloadUrlReq) SetTaskId(val string) {
	p.TaskId = val
}
func (p *DescribeLogsDownloadUrlReq) SetShardingInstanceId(val *string) {
	p.ShardingInstanceId = val
}
func (p *DescribeLogsDownloadUrlReq) SetNodeType(val *RdsNodeType) {
	p.NodeType = val
}

var fieldIDToName_DescribeLogsDownloadUrlReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "DSType",
	4: "TaskId",
	5: "ShardingInstanceId",
	6: "NodeType",
}

func (p *DescribeLogsDownloadUrlReq) IsSetShardingInstanceId() bool {
	return p.ShardingInstanceId != nil
}

func (p *DescribeLogsDownloadUrlReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeLogsDownloadUrlReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogsDownloadUrlReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLogsDownloadUrlReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLogsDownloadUrlReq[fieldId]))
}

func (p *DescribeLogsDownloadUrlReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeLogsDownloadUrlReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeLogsDownloadUrlReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeLogsDownloadUrlReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *DescribeLogsDownloadUrlReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingInstanceId = _field
	return nil
}
func (p *DescribeLogsDownloadUrlReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *RdsNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RdsNodeType(v)
		_field = &tmp
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeLogsDownloadUrlReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogsDownloadUrlReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLogsDownloadUrlReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingInstanceId() {
		if err = oprot.WriteFieldBegin("ShardingInstanceId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingInstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NodeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogsDownloadUrlReq(%+v)", *p)

}

func (p *DescribeLogsDownloadUrlReq) DeepEqual(ano *DescribeLogsDownloadUrlReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field5DeepEqual(ano.ShardingInstanceId) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeLogsDownloadUrlReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogsDownloadUrlReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogsDownloadUrlReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeLogsDownloadUrlReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogsDownloadUrlReq) Field5DeepEqual(src *string) bool {

	if p.ShardingInstanceId == src {
		return true
	} else if p.ShardingInstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingInstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogsDownloadUrlReq) Field6DeepEqual(src *RdsNodeType) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if *p.NodeType != *src {
		return false
	}
	return true
}

type DescribeLogsDownloadUrlResp struct {
	FileUrl string `thrift:"FileUrl,1,required" frugal:"1,required,string" json:"FileUrl"`
}

func NewDescribeLogsDownloadUrlResp() *DescribeLogsDownloadUrlResp {
	return &DescribeLogsDownloadUrlResp{}
}

func (p *DescribeLogsDownloadUrlResp) InitDefault() {
}

func (p *DescribeLogsDownloadUrlResp) GetFileUrl() (v string) {
	return p.FileUrl
}
func (p *DescribeLogsDownloadUrlResp) SetFileUrl(val string) {
	p.FileUrl = val
}

var fieldIDToName_DescribeLogsDownloadUrlResp = map[int16]string{
	1: "FileUrl",
}

func (p *DescribeLogsDownloadUrlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogsDownloadUrlResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFileUrl bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFileUrl {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLogsDownloadUrlResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLogsDownloadUrlResp[fieldId]))
}

func (p *DescribeLogsDownloadUrlResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileUrl = _field
	return nil
}

func (p *DescribeLogsDownloadUrlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogsDownloadUrlResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLogsDownloadUrlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileUrl", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileUrl); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLogsDownloadUrlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogsDownloadUrlResp(%+v)", *p)

}

func (p *DescribeLogsDownloadUrlResp) DeepEqual(ano *DescribeLogsDownloadUrlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FileUrl) {
		return false
	}
	return true
}

func (p *DescribeLogsDownloadUrlResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FileUrl, src) != 0 {
		return false
	}
	return true
}

type DescribeAggregateDiagSlowQueryReq struct {
	FieldSchema      string        `thrift:"FieldSchema,1,required" frugal:"1,required,string" json:"FieldSchema"`
	FieldIdc         *string       `thrift:"FieldIdc,2,optional" frugal:"2,optional,string" json:"FieldIdc,omitempty"`
	FieldIp          *string       `thrift:"FieldIp,3,optional" frugal:"3,optional,string" json:"FieldIp,omitempty"`
	FieldPort        *int32        `thrift:"FieldPort,4,optional" frugal:"4,optional,i32" json:"FieldPort,omitempty"`
	From             int32         `thrift:"From,5,required" frugal:"5,required,i32" json:"From"`
	To               int32         `thrift:"To,6,required" frugal:"6,required,i32" json:"To"`
	FieldPsm         *string       `thrift:"FieldPsm,7,optional" frugal:"7,optional,string" json:"FieldPsm,omitempty"`
	FieldAction      *string       `thrift:"FieldAction,8,optional" frugal:"8,optional,string" json:"FieldAction,omitempty"`
	FieldFingerprint *string       `thrift:"FieldFingerprint,9,optional" frugal:"9,optional,string" json:"FieldFingerprint,omitempty"`
	RangeQueryTime   *string       `thrift:"RangeQueryTime,10,optional" frugal:"10,optional,string" json:"RangeQueryTime,omitempty"`
	OrderBy          *string       `thrift:"OrderBy,11,optional" frugal:"11,optional,string" json:"OrderBy,omitempty"`
	NodeIds          []string      `thrift:"nodeIds,12,optional" frugal:"12,optional,list<string>" json:"nodeIds,omitempty"`
	InstanceType     *InstanceType `thrift:"InstanceType,13,optional" frugal:"13,optional,InstanceType" json:"InstanceType,omitempty"`
	RegionId         *string       `thrift:"RegionId,14,optional" frugal:"14,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeAggregateDiagSlowQueryReq() *DescribeAggregateDiagSlowQueryReq {
	return &DescribeAggregateDiagSlowQueryReq{}
}

func (p *DescribeAggregateDiagSlowQueryReq) InitDefault() {
}

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldSchema() (v string) {
	return p.FieldSchema
}

var DescribeAggregateDiagSlowQueryReq_FieldIdc_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldIdc() (v string) {
	if !p.IsSetFieldIdc() {
		return DescribeAggregateDiagSlowQueryReq_FieldIdc_DEFAULT
	}
	return *p.FieldIdc
}

var DescribeAggregateDiagSlowQueryReq_FieldIp_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldIp() (v string) {
	if !p.IsSetFieldIp() {
		return DescribeAggregateDiagSlowQueryReq_FieldIp_DEFAULT
	}
	return *p.FieldIp
}

var DescribeAggregateDiagSlowQueryReq_FieldPort_DEFAULT int32

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldPort() (v int32) {
	if !p.IsSetFieldPort() {
		return DescribeAggregateDiagSlowQueryReq_FieldPort_DEFAULT
	}
	return *p.FieldPort
}

func (p *DescribeAggregateDiagSlowQueryReq) GetFrom() (v int32) {
	return p.From
}

func (p *DescribeAggregateDiagSlowQueryReq) GetTo() (v int32) {
	return p.To
}

var DescribeAggregateDiagSlowQueryReq_FieldPsm_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldPsm() (v string) {
	if !p.IsSetFieldPsm() {
		return DescribeAggregateDiagSlowQueryReq_FieldPsm_DEFAULT
	}
	return *p.FieldPsm
}

var DescribeAggregateDiagSlowQueryReq_FieldAction_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldAction() (v string) {
	if !p.IsSetFieldAction() {
		return DescribeAggregateDiagSlowQueryReq_FieldAction_DEFAULT
	}
	return *p.FieldAction
}

var DescribeAggregateDiagSlowQueryReq_FieldFingerprint_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetFieldFingerprint() (v string) {
	if !p.IsSetFieldFingerprint() {
		return DescribeAggregateDiagSlowQueryReq_FieldFingerprint_DEFAULT
	}
	return *p.FieldFingerprint
}

var DescribeAggregateDiagSlowQueryReq_RangeQueryTime_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetRangeQueryTime() (v string) {
	if !p.IsSetRangeQueryTime() {
		return DescribeAggregateDiagSlowQueryReq_RangeQueryTime_DEFAULT
	}
	return *p.RangeQueryTime
}

var DescribeAggregateDiagSlowQueryReq_OrderBy_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetOrderBy() (v string) {
	if !p.IsSetOrderBy() {
		return DescribeAggregateDiagSlowQueryReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeAggregateDiagSlowQueryReq_NodeIds_DEFAULT []string

func (p *DescribeAggregateDiagSlowQueryReq) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return DescribeAggregateDiagSlowQueryReq_NodeIds_DEFAULT
	}
	return p.NodeIds
}

var DescribeAggregateDiagSlowQueryReq_InstanceType_DEFAULT InstanceType

func (p *DescribeAggregateDiagSlowQueryReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DescribeAggregateDiagSlowQueryReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeAggregateDiagSlowQueryReq_RegionId_DEFAULT string

func (p *DescribeAggregateDiagSlowQueryReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeAggregateDiagSlowQueryReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldSchema(val string) {
	p.FieldSchema = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldIdc(val *string) {
	p.FieldIdc = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldIp(val *string) {
	p.FieldIp = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldPort(val *int32) {
	p.FieldPort = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFrom(val int32) {
	p.From = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetTo(val int32) {
	p.To = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldPsm(val *string) {
	p.FieldPsm = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldAction(val *string) {
	p.FieldAction = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetFieldFingerprint(val *string) {
	p.FieldFingerprint = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetRangeQueryTime(val *string) {
	p.RangeQueryTime = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetOrderBy(val *string) {
	p.OrderBy = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetNodeIds(val []string) {
	p.NodeIds = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAggregateDiagSlowQueryReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeAggregateDiagSlowQueryReq = map[int16]string{
	1:  "FieldSchema",
	2:  "FieldIdc",
	3:  "FieldIp",
	4:  "FieldPort",
	5:  "From",
	6:  "To",
	7:  "FieldPsm",
	8:  "FieldAction",
	9:  "FieldFingerprint",
	10: "RangeQueryTime",
	11: "OrderBy",
	12: "nodeIds",
	13: "InstanceType",
	14: "RegionId",
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldIdc() bool {
	return p.FieldIdc != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldIp() bool {
	return p.FieldIp != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldPort() bool {
	return p.FieldPort != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldPsm() bool {
	return p.FieldPsm != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldAction() bool {
	return p.FieldAction != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetFieldFingerprint() bool {
	return p.FieldFingerprint != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetRangeQueryTime() bool {
	return p.RangeQueryTime != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeAggregateDiagSlowQueryReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateDiagSlowQueryReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFieldSchema bool = false
	var issetFrom bool = false
	var issetTo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFieldSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetFrom = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFieldSchema {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetFrom {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTo {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAggregateDiagSlowQueryReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAggregateDiagSlowQueryReq[fieldId]))
}

func (p *DescribeAggregateDiagSlowQueryReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FieldSchema = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldIdc = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldIp = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldPort = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.From = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.To = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldPsm = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldAction = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FieldFingerprint = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RangeQueryTime = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeAggregateDiagSlowQueryReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateDiagSlowQueryReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAggregateDiagSlowQueryReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FieldSchema", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FieldSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldIdc() {
		if err = oprot.WriteFieldBegin("FieldIdc", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldIdc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldIp() {
		if err = oprot.WriteFieldBegin("FieldIp", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldIp); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldPort() {
		if err = oprot.WriteFieldBegin("FieldPort", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.FieldPort); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("From", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.From); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("To", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.To); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldPsm() {
		if err = oprot.WriteFieldBegin("FieldPsm", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldPsm); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldAction() {
		if err = oprot.WriteFieldBegin("FieldAction", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldAction); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldFingerprint() {
		if err = oprot.WriteFieldBegin("FieldFingerprint", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FieldFingerprint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetRangeQueryTime() {
		if err = oprot.WriteFieldBegin("RangeQueryTime", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RangeQueryTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderBy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("nodeIds", thrift.LIST, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAggregateDiagSlowQueryReq(%+v)", *p)

}

func (p *DescribeAggregateDiagSlowQueryReq) DeepEqual(ano *DescribeAggregateDiagSlowQueryReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FieldSchema) {
		return false
	}
	if !p.Field2DeepEqual(ano.FieldIdc) {
		return false
	}
	if !p.Field3DeepEqual(ano.FieldIp) {
		return false
	}
	if !p.Field4DeepEqual(ano.FieldPort) {
		return false
	}
	if !p.Field5DeepEqual(ano.From) {
		return false
	}
	if !p.Field6DeepEqual(ano.To) {
		return false
	}
	if !p.Field7DeepEqual(ano.FieldPsm) {
		return false
	}
	if !p.Field8DeepEqual(ano.FieldAction) {
		return false
	}
	if !p.Field9DeepEqual(ano.FieldFingerprint) {
		return false
	}
	if !p.Field10DeepEqual(ano.RangeQueryTime) {
		return false
	}
	if !p.Field11DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field12DeepEqual(ano.NodeIds) {
		return false
	}
	if !p.Field13DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field14DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeAggregateDiagSlowQueryReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FieldSchema, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field2DeepEqual(src *string) bool {

	if p.FieldIdc == src {
		return true
	} else if p.FieldIdc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldIdc, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field3DeepEqual(src *string) bool {

	if p.FieldIp == src {
		return true
	} else if p.FieldIp == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldIp, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field4DeepEqual(src *int32) bool {

	if p.FieldPort == src {
		return true
	} else if p.FieldPort == nil || src == nil {
		return false
	}
	if *p.FieldPort != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field5DeepEqual(src int32) bool {

	if p.From != src {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field6DeepEqual(src int32) bool {

	if p.To != src {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field7DeepEqual(src *string) bool {

	if p.FieldPsm == src {
		return true
	} else if p.FieldPsm == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldPsm, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field8DeepEqual(src *string) bool {

	if p.FieldAction == src {
		return true
	} else if p.FieldAction == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldAction, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field9DeepEqual(src *string) bool {

	if p.FieldFingerprint == src {
		return true
	} else if p.FieldFingerprint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FieldFingerprint, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field10DeepEqual(src *string) bool {

	if p.RangeQueryTime == src {
		return true
	} else if p.RangeQueryTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RangeQueryTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field11DeepEqual(src *string) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OrderBy, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field12DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field13DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryReq) Field14DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeAggregateDiagSlowQueryResp struct {
	AggregateSlowLogs []*AggregateDiagSlowLog `thrift:"AggregateSlowLogs,1,required" frugal:"1,required,list<AggregateDiagSlowLog>" json:"AggregateSlowLogs"`
	Total             int32                   `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeAggregateDiagSlowQueryResp() *DescribeAggregateDiagSlowQueryResp {
	return &DescribeAggregateDiagSlowQueryResp{}
}

func (p *DescribeAggregateDiagSlowQueryResp) InitDefault() {
}

func (p *DescribeAggregateDiagSlowQueryResp) GetAggregateSlowLogs() (v []*AggregateDiagSlowLog) {
	return p.AggregateSlowLogs
}

func (p *DescribeAggregateDiagSlowQueryResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeAggregateDiagSlowQueryResp) SetAggregateSlowLogs(val []*AggregateDiagSlowLog) {
	p.AggregateSlowLogs = val
}
func (p *DescribeAggregateDiagSlowQueryResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeAggregateDiagSlowQueryResp = map[int16]string{
	1: "AggregateSlowLogs",
	2: "Total",
}

func (p *DescribeAggregateDiagSlowQueryResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateDiagSlowQueryResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAggregateSlowLogs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAggregateSlowLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAggregateSlowLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAggregateDiagSlowQueryResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAggregateDiagSlowQueryResp[fieldId]))
}

func (p *DescribeAggregateDiagSlowQueryResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AggregateDiagSlowLog, 0, size)
	values := make([]AggregateDiagSlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AggregateSlowLogs = _field
	return nil
}
func (p *DescribeAggregateDiagSlowQueryResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeAggregateDiagSlowQueryResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAggregateDiagSlowQueryResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAggregateDiagSlowQueryResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AggregateSlowLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AggregateSlowLogs)); err != nil {
		return err
	}
	for _, v := range p.AggregateSlowLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAggregateDiagSlowQueryResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAggregateDiagSlowQueryResp(%+v)", *p)

}

func (p *DescribeAggregateDiagSlowQueryResp) DeepEqual(ano *DescribeAggregateDiagSlowQueryResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AggregateSlowLogs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeAggregateDiagSlowQueryResp) Field1DeepEqual(src []*AggregateDiagSlowLog) bool {

	if len(p.AggregateSlowLogs) != len(src) {
		return false
	}
	for i, v := range p.AggregateSlowLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAggregateDiagSlowQueryResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type AggregateDiagSlowLog struct {
	Fingerprint   string   `thrift:"Fingerprint,1,required" frugal:"1,required,string" json:"Fingerprint"`
	Example       string   `thrift:"Example,2,required" frugal:"2,required,string" json:"Example"`
	ExampleTime   *float64 `thrift:"ExampleTime,3,optional" frugal:"3,optional,double" json:"ExampleTime,omitempty"`
	ExampleSchema string   `thrift:"ExampleSchema,4,required" frugal:"4,required,string" json:"ExampleSchema"`
	Count         float64  `thrift:"Count,5,required" frugal:"5,required,double" json:"Count"`
	AvgQueryTime  float64  `thrift:"AvgQueryTime,6,required" frugal:"6,required,double" json:"AvgQueryTime"`
	Address       *string  `thrift:"Address,7,optional" frugal:"7,optional,string" json:"Address,omitempty"`
	User          *string  `thrift:"User,8,optional" frugal:"8,optional,string" json:"User,omitempty"`
}

func NewAggregateDiagSlowLog() *AggregateDiagSlowLog {
	return &AggregateDiagSlowLog{}
}

func (p *AggregateDiagSlowLog) InitDefault() {
}

func (p *AggregateDiagSlowLog) GetFingerprint() (v string) {
	return p.Fingerprint
}

func (p *AggregateDiagSlowLog) GetExample() (v string) {
	return p.Example
}

var AggregateDiagSlowLog_ExampleTime_DEFAULT float64

func (p *AggregateDiagSlowLog) GetExampleTime() (v float64) {
	if !p.IsSetExampleTime() {
		return AggregateDiagSlowLog_ExampleTime_DEFAULT
	}
	return *p.ExampleTime
}

func (p *AggregateDiagSlowLog) GetExampleSchema() (v string) {
	return p.ExampleSchema
}

func (p *AggregateDiagSlowLog) GetCount() (v float64) {
	return p.Count
}

func (p *AggregateDiagSlowLog) GetAvgQueryTime() (v float64) {
	return p.AvgQueryTime
}

var AggregateDiagSlowLog_Address_DEFAULT string

func (p *AggregateDiagSlowLog) GetAddress() (v string) {
	if !p.IsSetAddress() {
		return AggregateDiagSlowLog_Address_DEFAULT
	}
	return *p.Address
}

var AggregateDiagSlowLog_User_DEFAULT string

func (p *AggregateDiagSlowLog) GetUser() (v string) {
	if !p.IsSetUser() {
		return AggregateDiagSlowLog_User_DEFAULT
	}
	return *p.User
}
func (p *AggregateDiagSlowLog) SetFingerprint(val string) {
	p.Fingerprint = val
}
func (p *AggregateDiagSlowLog) SetExample(val string) {
	p.Example = val
}
func (p *AggregateDiagSlowLog) SetExampleTime(val *float64) {
	p.ExampleTime = val
}
func (p *AggregateDiagSlowLog) SetExampleSchema(val string) {
	p.ExampleSchema = val
}
func (p *AggregateDiagSlowLog) SetCount(val float64) {
	p.Count = val
}
func (p *AggregateDiagSlowLog) SetAvgQueryTime(val float64) {
	p.AvgQueryTime = val
}
func (p *AggregateDiagSlowLog) SetAddress(val *string) {
	p.Address = val
}
func (p *AggregateDiagSlowLog) SetUser(val *string) {
	p.User = val
}

var fieldIDToName_AggregateDiagSlowLog = map[int16]string{
	1: "Fingerprint",
	2: "Example",
	3: "ExampleTime",
	4: "ExampleSchema",
	5: "Count",
	6: "AvgQueryTime",
	7: "Address",
	8: "User",
}

func (p *AggregateDiagSlowLog) IsSetExampleTime() bool {
	return p.ExampleTime != nil
}

func (p *AggregateDiagSlowLog) IsSetAddress() bool {
	return p.Address != nil
}

func (p *AggregateDiagSlowLog) IsSetUser() bool {
	return p.User != nil
}

func (p *AggregateDiagSlowLog) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AggregateDiagSlowLog")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFingerprint bool = false
	var issetExample bool = false
	var issetExampleSchema bool = false
	var issetCount bool = false
	var issetAvgQueryTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFingerprint = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetExample = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetExampleSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvgQueryTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFingerprint {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetExample {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetExampleSchema {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCount {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAvgQueryTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AggregateDiagSlowLog[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AggregateDiagSlowLog[fieldId]))
}

func (p *AggregateDiagSlowLog) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Fingerprint = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Example = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExampleTime = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExampleSchema = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField5(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Count = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField6(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvgQueryTime = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Address = _field
	return nil
}
func (p *AggregateDiagSlowLog) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.User = _field
	return nil
}

func (p *AggregateDiagSlowLog) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AggregateDiagSlowLog")

	var fieldId int16
	if err = oprot.WriteStructBegin("AggregateDiagSlowLog"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Fingerprint", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Fingerprint); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Example", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Example); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetExampleTime() {
		if err = oprot.WriteFieldBegin("ExampleTime", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.ExampleTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExampleSchema", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExampleSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Count", thrift.DOUBLE, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Count); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvgQueryTime", thrift.DOUBLE, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AvgQueryTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetAddress() {
		if err = oprot.WriteFieldBegin("Address", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Address); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetUser() {
		if err = oprot.WriteFieldBegin("User", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.User); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AggregateDiagSlowLog) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AggregateDiagSlowLog(%+v)", *p)

}

func (p *AggregateDiagSlowLog) DeepEqual(ano *AggregateDiagSlowLog) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Fingerprint) {
		return false
	}
	if !p.Field2DeepEqual(ano.Example) {
		return false
	}
	if !p.Field3DeepEqual(ano.ExampleTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExampleSchema) {
		return false
	}
	if !p.Field5DeepEqual(ano.Count) {
		return false
	}
	if !p.Field6DeepEqual(ano.AvgQueryTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.Address) {
		return false
	}
	if !p.Field8DeepEqual(ano.User) {
		return false
	}
	return true
}

func (p *AggregateDiagSlowLog) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Fingerprint, src) != 0 {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Example, src) != 0 {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field3DeepEqual(src *float64) bool {

	if p.ExampleTime == src {
		return true
	} else if p.ExampleTime == nil || src == nil {
		return false
	}
	if *p.ExampleTime != *src {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ExampleSchema, src) != 0 {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field5DeepEqual(src float64) bool {

	if p.Count != src {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field6DeepEqual(src float64) bool {

	if p.AvgQueryTime != src {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field7DeepEqual(src *string) bool {

	if p.Address == src {
		return true
	} else if p.Address == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Address, *src) != 0 {
		return false
	}
	return true
}
func (p *AggregateDiagSlowLog) Field8DeepEqual(src *string) bool {

	if p.User == src {
		return true
	} else if p.User == nil || src == nil {
		return false
	}
	if strings.Compare(*p.User, *src) != 0 {
		return false
	}
	return true
}

type DescribeCandidateReq struct {
	RegionId       string                `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId     string                `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType         DSType                `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	StartTime      int32                 `thrift:"StartTime,4,required" frugal:"4,required,i32" json:"StartTime"`
	EndTime        int32                 `thrift:"EndTime,5,required" frugal:"5,required,i32" json:"EndTime"`
	CandidateField SlowLogCandidateField `thrift:"CandidateField,6,required" frugal:"6,required,SlowLogCandidateField" json:"CandidateField"`
	NodeId         *string               `thrift:"NodeId,7,optional" frugal:"7,optional,string" json:"NodeId,omitempty"`
}

func NewDescribeCandidateReq() *DescribeCandidateReq {
	return &DescribeCandidateReq{}
}

func (p *DescribeCandidateReq) InitDefault() {
}

func (p *DescribeCandidateReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeCandidateReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeCandidateReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeCandidateReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeCandidateReq) GetEndTime() (v int32) {
	return p.EndTime
}

func (p *DescribeCandidateReq) GetCandidateField() (v SlowLogCandidateField) {
	return p.CandidateField
}

var DescribeCandidateReq_NodeId_DEFAULT string

func (p *DescribeCandidateReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeCandidateReq_NodeId_DEFAULT
	}
	return *p.NodeId
}
func (p *DescribeCandidateReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeCandidateReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeCandidateReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeCandidateReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeCandidateReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeCandidateReq) SetCandidateField(val SlowLogCandidateField) {
	p.CandidateField = val
}
func (p *DescribeCandidateReq) SetNodeId(val *string) {
	p.NodeId = val
}

var fieldIDToName_DescribeCandidateReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "DSType",
	4: "StartTime",
	5: "EndTime",
	6: "CandidateField",
	7: "NodeId",
}

func (p *DescribeCandidateReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeCandidateReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCandidateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetCandidateField bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCandidateField = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCandidateField {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCandidateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCandidateReq[fieldId]))
}

func (p *DescribeCandidateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField6(iprot thrift.TProtocol) error {

	var _field SlowLogCandidateField
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SlowLogCandidateField(v)
	}
	p.CandidateField = _field
	return nil
}
func (p *DescribeCandidateReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}

func (p *DescribeCandidateReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCandidateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCandidateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CandidateField", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CandidateField)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeCandidateReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeCandidateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCandidateReq(%+v)", *p)

}

func (p *DescribeCandidateReq) DeepEqual(ano *DescribeCandidateReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.CandidateField) {
		return false
	}
	if !p.Field7DeepEqual(ano.NodeId) {
		return false
	}
	return true
}

func (p *DescribeCandidateReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field4DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field5DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field6DeepEqual(src SlowLogCandidateField) bool {

	if p.CandidateField != src {
		return false
	}
	return true
}
func (p *DescribeCandidateReq) Field7DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}

type DescribeCandidateResp struct {
	Candidates []string `thrift:"Candidates,1,required" frugal:"1,required,list<string>" json:"Candidates"`
}

func NewDescribeCandidateResp() *DescribeCandidateResp {
	return &DescribeCandidateResp{}
}

func (p *DescribeCandidateResp) InitDefault() {
}

func (p *DescribeCandidateResp) GetCandidates() (v []string) {
	return p.Candidates
}
func (p *DescribeCandidateResp) SetCandidates(val []string) {
	p.Candidates = val
}

var fieldIDToName_DescribeCandidateResp = map[int16]string{
	1: "Candidates",
}

func (p *DescribeCandidateResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCandidateResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCandidates bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCandidates = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCandidates {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCandidateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCandidateResp[fieldId]))
}

func (p *DescribeCandidateResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Candidates = _field
	return nil
}

func (p *DescribeCandidateResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCandidateResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCandidateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCandidateResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Candidates", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Candidates)); err != nil {
		return err
	}
	for _, v := range p.Candidates {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCandidateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCandidateResp(%+v)", *p)

}

func (p *DescribeCandidateResp) DeepEqual(ano *DescribeCandidateResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Candidates) {
		return false
	}
	return true
}

func (p *DescribeCandidateResp) Field1DeepEqual(src []string) bool {

	if len(p.Candidates) != len(src) {
		return false
	}
	for i, v := range p.Candidates {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

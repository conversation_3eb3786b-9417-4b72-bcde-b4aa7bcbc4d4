// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CopilotSceneType int64

const (
	CopilotSceneType_None                         CopilotSceneType = 0
	CopilotSceneType_INTELLIGENT_TROUBLESHOOTING  CopilotSceneType = 1
	CopilotSceneType_INTELLIGENT_CUSTOMER_SERVICE CopilotSceneType = 2
)

func (p CopilotSceneType) String() string {
	switch p {
	case CopilotSceneType_None:
		return "None"
	case CopilotSceneType_INTELLIGENT_TROUBLESHOOTING:
		return "INTELLIGENT_TROUBLESHOOTING"
	case CopilotSceneType_INTELLIGENT_CUSTOMER_SERVICE:
		return "INTELLIGENT_CUSTOMER_SERVICE"
	}
	return "<UNSET>"
}

func CopilotSceneTypeFromString(s string) (CopilotSceneType, error) {
	switch s {
	case "None":
		return CopilotSceneType_None, nil
	case "INTELLIGENT_TROUBLESHOOTING":
		return CopilotSceneType_INTELLIGENT_TROUBLESHOOTING, nil
	case "INTELLIGENT_CUSTOMER_SERVICE":
		return CopilotSceneType_INTELLIGENT_CUSTOMER_SERVICE, nil
	}
	return CopilotSceneType(0), fmt.Errorf("not a valid CopilotSceneType string")
}

func CopilotSceneTypePtr(v CopilotSceneType) *CopilotSceneType { return &v }

func (p CopilotSceneType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CopilotSceneType) UnmarshalText(text []byte) error {
	q, err := CopilotSceneTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CopilotProtocolType int64

const (
	CopilotProtocolType_SqlAssistant CopilotProtocolType = 1
	CopilotProtocolType_SqlAdvisor   CopilotProtocolType = 2
)

func (p CopilotProtocolType) String() string {
	switch p {
	case CopilotProtocolType_SqlAssistant:
		return "SqlAssistant"
	case CopilotProtocolType_SqlAdvisor:
		return "SqlAdvisor"
	}
	return "<UNSET>"
}

func CopilotProtocolTypeFromString(s string) (CopilotProtocolType, error) {
	switch s {
	case "SqlAssistant":
		return CopilotProtocolType_SqlAssistant, nil
	case "SqlAdvisor":
		return CopilotProtocolType_SqlAdvisor, nil
	}
	return CopilotProtocolType(0), fmt.Errorf("not a valid CopilotProtocolType string")
}

func CopilotProtocolTypePtr(v CopilotProtocolType) *CopilotProtocolType { return &v }

func (p CopilotProtocolType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CopilotProtocolType) UnmarshalText(text []byte) error {
	q, err := CopilotProtocolTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type StreamResponseState int64

const (
	StreamResponseState_SEMANTIC_ANALYZER     StreamResponseState = 0
	StreamResponseState_FUNCTION_CALL         StreamResponseState = 1
	StreamResponseState_AGENT_RESULT_ANALYZER StreamResponseState = 2
	StreamResponseState_RESULT_OUTPUT         StreamResponseState = 3
	StreamResponseState_AGENT_CALL            StreamResponseState = 4
	StreamResponseState_AGENT_CALL_FINISHED   StreamResponseState = 5
)

func (p StreamResponseState) String() string {
	switch p {
	case StreamResponseState_SEMANTIC_ANALYZER:
		return "SEMANTIC_ANALYZER"
	case StreamResponseState_FUNCTION_CALL:
		return "FUNCTION_CALL"
	case StreamResponseState_AGENT_RESULT_ANALYZER:
		return "AGENT_RESULT_ANALYZER"
	case StreamResponseState_RESULT_OUTPUT:
		return "RESULT_OUTPUT"
	case StreamResponseState_AGENT_CALL:
		return "AGENT_CALL"
	case StreamResponseState_AGENT_CALL_FINISHED:
		return "AGENT_CALL_FINISHED"
	}
	return "<UNSET>"
}

func StreamResponseStateFromString(s string) (StreamResponseState, error) {
	switch s {
	case "SEMANTIC_ANALYZER":
		return StreamResponseState_SEMANTIC_ANALYZER, nil
	case "FUNCTION_CALL":
		return StreamResponseState_FUNCTION_CALL, nil
	case "AGENT_RESULT_ANALYZER":
		return StreamResponseState_AGENT_RESULT_ANALYZER, nil
	case "RESULT_OUTPUT":
		return StreamResponseState_RESULT_OUTPUT, nil
	case "AGENT_CALL":
		return StreamResponseState_AGENT_CALL, nil
	case "AGENT_CALL_FINISHED":
		return StreamResponseState_AGENT_CALL_FINISHED, nil
	}
	return StreamResponseState(0), fmt.Errorf("not a valid StreamResponseState string")
}

func StreamResponseStatePtr(v StreamResponseState) *StreamResponseState { return &v }

func (p StreamResponseState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *StreamResponseState) UnmarshalText(text []byte) error {
	q, err := StreamResponseStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type RateModelReplyEnum int64

const (
	RateModelReplyEnum_POSITIVE_RATE RateModelReplyEnum = 0
	RateModelReplyEnum_NEGATIVE_RATE RateModelReplyEnum = 1
)

func (p RateModelReplyEnum) String() string {
	switch p {
	case RateModelReplyEnum_POSITIVE_RATE:
		return "POSITIVE_RATE"
	case RateModelReplyEnum_NEGATIVE_RATE:
		return "NEGATIVE_RATE"
	}
	return "<UNSET>"
}

func RateModelReplyEnumFromString(s string) (RateModelReplyEnum, error) {
	switch s {
	case "POSITIVE_RATE":
		return RateModelReplyEnum_POSITIVE_RATE, nil
	case "NEGATIVE_RATE":
		return RateModelReplyEnum_NEGATIVE_RATE, nil
	}
	return RateModelReplyEnum(0), fmt.Errorf("not a valid RateModelReplyEnum string")
}

func RateModelReplyEnumPtr(v RateModelReplyEnum) *RateModelReplyEnum { return &v }

func (p RateModelReplyEnum) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *RateModelReplyEnum) UnmarshalText(text []byte) error {
	q, err := RateModelReplyEnumFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AgentNameEnum int64

const (
	AgentNameEnum_None           AgentNameEnum = 0
	AgentNameEnum_ProxyAgent     AgentNameEnum = 1
	AgentNameEnum_DiagnosisAgent AgentNameEnum = 2
	AgentNameEnum_RetrievalAgent AgentNameEnum = 3
	AgentNameEnum_MonitorAgent   AgentNameEnum = 4
)

func (p AgentNameEnum) String() string {
	switch p {
	case AgentNameEnum_None:
		return "None"
	case AgentNameEnum_ProxyAgent:
		return "ProxyAgent"
	case AgentNameEnum_DiagnosisAgent:
		return "DiagnosisAgent"
	case AgentNameEnum_RetrievalAgent:
		return "RetrievalAgent"
	case AgentNameEnum_MonitorAgent:
		return "MonitorAgent"
	}
	return "<UNSET>"
}

func AgentNameEnumFromString(s string) (AgentNameEnum, error) {
	switch s {
	case "None":
		return AgentNameEnum_None, nil
	case "ProxyAgent":
		return AgentNameEnum_ProxyAgent, nil
	case "DiagnosisAgent":
		return AgentNameEnum_DiagnosisAgent, nil
	case "RetrievalAgent":
		return AgentNameEnum_RetrievalAgent, nil
	case "MonitorAgent":
		return AgentNameEnum_MonitorAgent, nil
	}
	return AgentNameEnum(0), fmt.Errorf("not a valid AgentNameEnum string")
}

func AgentNameEnumPtr(v AgentNameEnum) *AgentNameEnum { return &v }

func (p AgentNameEnum) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AgentNameEnum) UnmarshalText(text []byte) error {
	q, err := AgentNameEnumFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ExecutePromptReq struct {
	ChatID       *string       `thrift:"chatID,1,optional" frugal:"1,optional,string" json:"chatID,omitempty"`
	Content      string        `thrift:"content,2,required" frugal:"2,required,string" json:"content"`
	IsStream     bool          `thrift:"IsStream,3,required" frugal:"3,required,bool" json:"IsStream"`
	InstanceID   *string       `thrift:"instanceID,4,optional" frugal:"4,optional,string" json:"instanceID,omitempty"`
	InstanceType *InstanceType `thrift:"InstanceType,5,optional" frugal:"5,optional,InstanceType" json:"InstanceType,omitempty"`
	RegionId     *string       `thrift:"RegionId,6,optional" frugal:"6,optional,string" json:"RegionId,omitempty"`
}

func NewExecutePromptReq() *ExecutePromptReq {
	return &ExecutePromptReq{}
}

func (p *ExecutePromptReq) InitDefault() {
}

var ExecutePromptReq_ChatID_DEFAULT string

func (p *ExecutePromptReq) GetChatID() (v string) {
	if !p.IsSetChatID() {
		return ExecutePromptReq_ChatID_DEFAULT
	}
	return *p.ChatID
}

func (p *ExecutePromptReq) GetContent() (v string) {
	return p.Content
}

func (p *ExecutePromptReq) GetIsStream() (v bool) {
	return p.IsStream
}

var ExecutePromptReq_InstanceID_DEFAULT string

func (p *ExecutePromptReq) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return ExecutePromptReq_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var ExecutePromptReq_InstanceType_DEFAULT InstanceType

func (p *ExecutePromptReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return ExecutePromptReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ExecutePromptReq_RegionId_DEFAULT string

func (p *ExecutePromptReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return ExecutePromptReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *ExecutePromptReq) SetChatID(val *string) {
	p.ChatID = val
}
func (p *ExecutePromptReq) SetContent(val string) {
	p.Content = val
}
func (p *ExecutePromptReq) SetIsStream(val bool) {
	p.IsStream = val
}
func (p *ExecutePromptReq) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *ExecutePromptReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *ExecutePromptReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_ExecutePromptReq = map[int16]string{
	1: "chatID",
	2: "content",
	3: "IsStream",
	4: "instanceID",
	5: "InstanceType",
	6: "RegionId",
}

func (p *ExecutePromptReq) IsSetChatID() bool {
	return p.ChatID != nil
}

func (p *ExecutePromptReq) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *ExecutePromptReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ExecutePromptReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *ExecutePromptReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecutePromptReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContent bool = false
	var issetIsStream bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsStream = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContent {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIsStream {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecutePromptReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecutePromptReq[fieldId]))
}

func (p *ExecutePromptReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatID = _field
	return nil
}
func (p *ExecutePromptReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ExecutePromptReq) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsStream = _field
	return nil
}
func (p *ExecutePromptReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *ExecutePromptReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *ExecutePromptReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *ExecutePromptReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecutePromptReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecutePromptReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecutePromptReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatID() {
		if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChatID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecutePromptReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecutePromptReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsStream", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsStream); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExecutePromptReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("instanceID", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExecutePromptReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecutePromptReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ExecutePromptReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecutePromptReq(%+v)", *p)

}

func (p *ExecutePromptReq) DeepEqual(ano *ExecutePromptReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsStream) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *ExecutePromptReq) Field1DeepEqual(src *string) bool {

	if p.ChatID == src {
		return true
	} else if p.ChatID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChatID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecutePromptReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ExecutePromptReq) Field3DeepEqual(src bool) bool {

	if p.IsStream != src {
		return false
	}
	return true
}
func (p *ExecutePromptReq) Field4DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecutePromptReq) Field5DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *ExecutePromptReq) Field6DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type ExecutePromptResp struct {
	MessageID string `thrift:"messageID,1,required" frugal:"1,required,string" json:"messageID"`
	ChatID    string `thrift:"chatID,2,required" frugal:"2,required,string" json:"chatID"`
}

func NewExecutePromptResp() *ExecutePromptResp {
	return &ExecutePromptResp{}
}

func (p *ExecutePromptResp) InitDefault() {
}

func (p *ExecutePromptResp) GetMessageID() (v string) {
	return p.MessageID
}

func (p *ExecutePromptResp) GetChatID() (v string) {
	return p.ChatID
}
func (p *ExecutePromptResp) SetMessageID(val string) {
	p.MessageID = val
}
func (p *ExecutePromptResp) SetChatID(val string) {
	p.ChatID = val
}

var fieldIDToName_ExecutePromptResp = map[int16]string{
	1: "messageID",
	2: "chatID",
}

func (p *ExecutePromptResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecutePromptResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageID bool = false
	var issetChatID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChatID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecutePromptResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecutePromptResp[fieldId]))
}

func (p *ExecutePromptResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *ExecutePromptResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}

func (p *ExecutePromptResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecutePromptResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecutePromptResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecutePromptResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecutePromptResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecutePromptResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecutePromptResp(%+v)", *p)

}

func (p *ExecutePromptResp) DeepEqual(ano *ExecutePromptResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChatID) {
		return false
	}
	return true
}

func (p *ExecutePromptResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecutePromptResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}

type ExecuteProxyAgentReq struct {
	ChatID          *string       `thrift:"chatID,1,optional" frugal:"1,optional,string" json:"chatID,omitempty"`
	MessageID       *string       `thrift:"messageID,2,optional" frugal:"2,optional,string" json:"messageID,omitempty"`
	Content         string        `thrift:"content,3,required" frugal:"3,required,string" json:"content"`
	InstanceID      *string       `thrift:"instanceID,4,optional" frugal:"4,optional,string" json:"instanceID,omitempty"`
	EnableReasoning *bool         `thrift:"enableReasoning,5,optional" frugal:"5,optional,bool" json:"enableReasoning,omitempty"`
	InstanceType    *InstanceType `thrift:"InstanceType,6,optional" frugal:"6,optional,InstanceType" json:"InstanceType,omitempty"`
	RegionId        *string       `thrift:"RegionId,7,optional" frugal:"7,optional,string" json:"RegionId,omitempty"`
	VRegion         *string       `thrift:"VRegion,8,optional" frugal:"8,optional,string" json:"VRegion,omitempty"`
}

func NewExecuteProxyAgentReq() *ExecuteProxyAgentReq {
	return &ExecuteProxyAgentReq{}
}

func (p *ExecuteProxyAgentReq) InitDefault() {
}

var ExecuteProxyAgentReq_ChatID_DEFAULT string

func (p *ExecuteProxyAgentReq) GetChatID() (v string) {
	if !p.IsSetChatID() {
		return ExecuteProxyAgentReq_ChatID_DEFAULT
	}
	return *p.ChatID
}

var ExecuteProxyAgentReq_MessageID_DEFAULT string

func (p *ExecuteProxyAgentReq) GetMessageID() (v string) {
	if !p.IsSetMessageID() {
		return ExecuteProxyAgentReq_MessageID_DEFAULT
	}
	return *p.MessageID
}

func (p *ExecuteProxyAgentReq) GetContent() (v string) {
	return p.Content
}

var ExecuteProxyAgentReq_InstanceID_DEFAULT string

func (p *ExecuteProxyAgentReq) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return ExecuteProxyAgentReq_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var ExecuteProxyAgentReq_EnableReasoning_DEFAULT bool

func (p *ExecuteProxyAgentReq) GetEnableReasoning() (v bool) {
	if !p.IsSetEnableReasoning() {
		return ExecuteProxyAgentReq_EnableReasoning_DEFAULT
	}
	return *p.EnableReasoning
}

var ExecuteProxyAgentReq_InstanceType_DEFAULT InstanceType

func (p *ExecuteProxyAgentReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return ExecuteProxyAgentReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ExecuteProxyAgentReq_RegionId_DEFAULT string

func (p *ExecuteProxyAgentReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return ExecuteProxyAgentReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var ExecuteProxyAgentReq_VRegion_DEFAULT string

func (p *ExecuteProxyAgentReq) GetVRegion() (v string) {
	if !p.IsSetVRegion() {
		return ExecuteProxyAgentReq_VRegion_DEFAULT
	}
	return *p.VRegion
}
func (p *ExecuteProxyAgentReq) SetChatID(val *string) {
	p.ChatID = val
}
func (p *ExecuteProxyAgentReq) SetMessageID(val *string) {
	p.MessageID = val
}
func (p *ExecuteProxyAgentReq) SetContent(val string) {
	p.Content = val
}
func (p *ExecuteProxyAgentReq) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *ExecuteProxyAgentReq) SetEnableReasoning(val *bool) {
	p.EnableReasoning = val
}
func (p *ExecuteProxyAgentReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *ExecuteProxyAgentReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *ExecuteProxyAgentReq) SetVRegion(val *string) {
	p.VRegion = val
}

var fieldIDToName_ExecuteProxyAgentReq = map[int16]string{
	1: "chatID",
	2: "messageID",
	3: "content",
	4: "instanceID",
	5: "enableReasoning",
	6: "InstanceType",
	7: "RegionId",
	8: "VRegion",
}

func (p *ExecuteProxyAgentReq) IsSetChatID() bool {
	return p.ChatID != nil
}

func (p *ExecuteProxyAgentReq) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *ExecuteProxyAgentReq) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *ExecuteProxyAgentReq) IsSetEnableReasoning() bool {
	return p.EnableReasoning != nil
}

func (p *ExecuteProxyAgentReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ExecuteProxyAgentReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *ExecuteProxyAgentReq) IsSetVRegion() bool {
	return p.VRegion != nil
}

func (p *ExecuteProxyAgentReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteProxyAgentReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteProxyAgentReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteProxyAgentReq[fieldId]))
}

func (p *ExecuteProxyAgentReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatID = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableReasoning = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *ExecuteProxyAgentReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VRegion = _field
	return nil
}

func (p *ExecuteProxyAgentReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteProxyAgentReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteProxyAgentReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatID() {
		if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChatID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("instanceID", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableReasoning() {
		if err = oprot.WriteFieldBegin("enableReasoning", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableReasoning); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetVRegion() {
		if err = oprot.WriteFieldBegin("VRegion", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VRegion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ExecuteProxyAgentReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteProxyAgentReq(%+v)", *p)

}

func (p *ExecuteProxyAgentReq) DeepEqual(ano *ExecuteProxyAgentReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field5DeepEqual(ano.EnableReasoning) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field7DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field8DeepEqual(ano.VRegion) {
		return false
	}
	return true
}

func (p *ExecuteProxyAgentReq) Field1DeepEqual(src *string) bool {

	if p.ChatID == src {
		return true
	} else if p.ChatID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChatID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field2DeepEqual(src *string) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MessageID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field4DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field5DeepEqual(src *bool) bool {

	if p.EnableReasoning == src {
		return true
	} else if p.EnableReasoning == nil || src == nil {
		return false
	}
	if *p.EnableReasoning != *src {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field6DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field7DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteProxyAgentReq) Field8DeepEqual(src *string) bool {

	if p.VRegion == src {
		return true
	} else if p.VRegion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.VRegion, *src) != 0 {
		return false
	}
	return true
}

type ExecuteProxyAgentResp struct {
}

func NewExecuteProxyAgentResp() *ExecuteProxyAgentResp {
	return &ExecuteProxyAgentResp{}
}

func (p *ExecuteProxyAgentResp) InitDefault() {
}

var fieldIDToName_ExecuteProxyAgentResp = map[int16]string{}

func (p *ExecuteProxyAgentResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteProxyAgentResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteProxyAgentResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteProxyAgentResp")

	if err = oprot.WriteStructBegin("ExecuteProxyAgentResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteProxyAgentResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteProxyAgentResp(%+v)", *p)

}

func (p *ExecuteProxyAgentResp) DeepEqual(ano *ExecuteProxyAgentResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ExecuteSlaveAgentReq struct {
	ChatID       string        `thrift:"chatID,1,required" frugal:"1,required,string" json:"chatID"`
	MessageID    string        `thrift:"messageID,2,required" frugal:"2,required,string" json:"messageID"`
	Content      string        `thrift:"content,3,required" frugal:"3,required,string" json:"content"`
	AgentName    string        `thrift:"AgentName,4,required" frugal:"4,required,string" json:"AgentName"`
	ToolCallID   string        `thrift:"toolCallID,5,required" frugal:"5,required,string" json:"toolCallID"`
	InstanceType *InstanceType `thrift:"InstanceType,6,optional" frugal:"6,optional,InstanceType" json:"InstanceType,omitempty"`
	RegionId     *string       `thrift:"RegionId,7,optional" frugal:"7,optional,string" json:"RegionId,omitempty"`
	VRegion      *string       `thrift:"VRegion,8,optional" frugal:"8,optional,string" json:"VRegion,omitempty"`
}

func NewExecuteSlaveAgentReq() *ExecuteSlaveAgentReq {
	return &ExecuteSlaveAgentReq{}
}

func (p *ExecuteSlaveAgentReq) InitDefault() {
}

func (p *ExecuteSlaveAgentReq) GetChatID() (v string) {
	return p.ChatID
}

func (p *ExecuteSlaveAgentReq) GetMessageID() (v string) {
	return p.MessageID
}

func (p *ExecuteSlaveAgentReq) GetContent() (v string) {
	return p.Content
}

func (p *ExecuteSlaveAgentReq) GetAgentName() (v string) {
	return p.AgentName
}

func (p *ExecuteSlaveAgentReq) GetToolCallID() (v string) {
	return p.ToolCallID
}

var ExecuteSlaveAgentReq_InstanceType_DEFAULT InstanceType

func (p *ExecuteSlaveAgentReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return ExecuteSlaveAgentReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ExecuteSlaveAgentReq_RegionId_DEFAULT string

func (p *ExecuteSlaveAgentReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return ExecuteSlaveAgentReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var ExecuteSlaveAgentReq_VRegion_DEFAULT string

func (p *ExecuteSlaveAgentReq) GetVRegion() (v string) {
	if !p.IsSetVRegion() {
		return ExecuteSlaveAgentReq_VRegion_DEFAULT
	}
	return *p.VRegion
}
func (p *ExecuteSlaveAgentReq) SetChatID(val string) {
	p.ChatID = val
}
func (p *ExecuteSlaveAgentReq) SetMessageID(val string) {
	p.MessageID = val
}
func (p *ExecuteSlaveAgentReq) SetContent(val string) {
	p.Content = val
}
func (p *ExecuteSlaveAgentReq) SetAgentName(val string) {
	p.AgentName = val
}
func (p *ExecuteSlaveAgentReq) SetToolCallID(val string) {
	p.ToolCallID = val
}
func (p *ExecuteSlaveAgentReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *ExecuteSlaveAgentReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *ExecuteSlaveAgentReq) SetVRegion(val *string) {
	p.VRegion = val
}

var fieldIDToName_ExecuteSlaveAgentReq = map[int16]string{
	1: "chatID",
	2: "messageID",
	3: "content",
	4: "AgentName",
	5: "toolCallID",
	6: "InstanceType",
	7: "RegionId",
	8: "VRegion",
}

func (p *ExecuteSlaveAgentReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ExecuteSlaveAgentReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *ExecuteSlaveAgentReq) IsSetVRegion() bool {
	return p.VRegion != nil
}

func (p *ExecuteSlaveAgentReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteSlaveAgentReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatID bool = false
	var issetMessageID bool = false
	var issetContent bool = false
	var issetAgentName bool = false
	var issetToolCallID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgentName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetToolCallID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessageID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAgentName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetToolCallID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteSlaveAgentReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteSlaveAgentReq[fieldId]))
}

func (p *ExecuteSlaveAgentReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AgentName = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ToolCallID = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *ExecuteSlaveAgentReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VRegion = _field
	return nil
}

func (p *ExecuteSlaveAgentReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteSlaveAgentReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteSlaveAgentReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AgentName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AgentName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("toolCallID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ToolCallID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetVRegion() {
		if err = oprot.WriteFieldBegin("VRegion", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VRegion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ExecuteSlaveAgentReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteSlaveAgentReq(%+v)", *p)

}

func (p *ExecuteSlaveAgentReq) DeepEqual(ano *ExecuteSlaveAgentReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.AgentName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ToolCallID) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field7DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field8DeepEqual(ano.VRegion) {
		return false
	}
	return true
}

func (p *ExecuteSlaveAgentReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.MessageID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AgentName, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ToolCallID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field6DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field7DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteSlaveAgentReq) Field8DeepEqual(src *string) bool {

	if p.VRegion == src {
		return true
	} else if p.VRegion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.VRegion, *src) != 0 {
		return false
	}
	return true
}

type ExecuteSlaveAgentResp struct {
}

func NewExecuteSlaveAgentResp() *ExecuteSlaveAgentResp {
	return &ExecuteSlaveAgentResp{}
}

func (p *ExecuteSlaveAgentResp) InitDefault() {
}

var fieldIDToName_ExecuteSlaveAgentResp = map[int16]string{}

func (p *ExecuteSlaveAgentResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteSlaveAgentResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteSlaveAgentResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteSlaveAgentResp")

	if err = oprot.WriteStructBegin("ExecuteSlaveAgentResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteSlaveAgentResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteSlaveAgentResp(%+v)", *p)

}

func (p *ExecuteSlaveAgentResp) DeepEqual(ano *ExecuteSlaveAgentResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeChatMessagesReq struct {
	MessageID *string `thrift:"messageID,1,optional" frugal:"1,optional,string" json:"messageID,omitempty"`
	ChatID    *string `thrift:"chatID,2,optional" frugal:"2,optional,string" json:"chatID,omitempty"`
}

func NewDescribeChatMessagesReq() *DescribeChatMessagesReq {
	return &DescribeChatMessagesReq{}
}

func (p *DescribeChatMessagesReq) InitDefault() {
}

var DescribeChatMessagesReq_MessageID_DEFAULT string

func (p *DescribeChatMessagesReq) GetMessageID() (v string) {
	if !p.IsSetMessageID() {
		return DescribeChatMessagesReq_MessageID_DEFAULT
	}
	return *p.MessageID
}

var DescribeChatMessagesReq_ChatID_DEFAULT string

func (p *DescribeChatMessagesReq) GetChatID() (v string) {
	if !p.IsSetChatID() {
		return DescribeChatMessagesReq_ChatID_DEFAULT
	}
	return *p.ChatID
}
func (p *DescribeChatMessagesReq) SetMessageID(val *string) {
	p.MessageID = val
}
func (p *DescribeChatMessagesReq) SetChatID(val *string) {
	p.ChatID = val
}

var fieldIDToName_DescribeChatMessagesReq = map[int16]string{
	1: "messageID",
	2: "chatID",
}

func (p *DescribeChatMessagesReq) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *DescribeChatMessagesReq) IsSetChatID() bool {
	return p.ChatID != nil
}

func (p *DescribeChatMessagesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChatMessagesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChatMessagesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeChatMessagesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *DescribeChatMessagesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatID = _field
	return nil
}

func (p *DescribeChatMessagesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChatMessagesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChatMessagesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeChatMessagesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeChatMessagesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatID() {
		if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChatID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeChatMessagesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeChatMessagesReq(%+v)", *p)

}

func (p *DescribeChatMessagesReq) DeepEqual(ano *DescribeChatMessagesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChatID) {
		return false
	}
	return true
}

func (p *DescribeChatMessagesReq) Field1DeepEqual(src *string) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MessageID, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeChatMessagesReq) Field2DeepEqual(src *string) bool {

	if p.ChatID == src {
		return true
	} else if p.ChatID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChatID, *src) != 0 {
		return false
	}
	return true
}

type DescribeChatMessagesResp struct {
	MessageList []*ChatMessage `thrift:"messageList,1,optional" frugal:"1,optional,list<ChatMessage>" json:"messageList,omitempty"`
}

func NewDescribeChatMessagesResp() *DescribeChatMessagesResp {
	return &DescribeChatMessagesResp{}
}

func (p *DescribeChatMessagesResp) InitDefault() {
}

var DescribeChatMessagesResp_MessageList_DEFAULT []*ChatMessage

func (p *DescribeChatMessagesResp) GetMessageList() (v []*ChatMessage) {
	if !p.IsSetMessageList() {
		return DescribeChatMessagesResp_MessageList_DEFAULT
	}
	return p.MessageList
}
func (p *DescribeChatMessagesResp) SetMessageList(val []*ChatMessage) {
	p.MessageList = val
}

var fieldIDToName_DescribeChatMessagesResp = map[int16]string{
	1: "messageList",
}

func (p *DescribeChatMessagesResp) IsSetMessageList() bool {
	return p.MessageList != nil
}

func (p *DescribeChatMessagesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChatMessagesResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChatMessagesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeChatMessagesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChatMessage, 0, size)
	values := make([]ChatMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MessageList = _field
	return nil
}

func (p *DescribeChatMessagesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChatMessagesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChatMessagesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeChatMessagesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageList() {
		if err = oprot.WriteFieldBegin("messageList", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MessageList)); err != nil {
			return err
		}
		for _, v := range p.MessageList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeChatMessagesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeChatMessagesResp(%+v)", *p)

}

func (p *DescribeChatMessagesResp) DeepEqual(ano *DescribeChatMessagesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageList) {
		return false
	}
	return true
}

func (p *DescribeChatMessagesResp) Field1DeepEqual(src []*ChatMessage) bool {

	if len(p.MessageList) != len(src) {
		return false
	}
	for i, v := range p.MessageList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeCopilotTaskListReq struct {
	TaskIDList []string `thrift:"taskIDList,1,optional" frugal:"1,optional,list<string>" json:"taskIDList,omitempty"`
}

func NewDescribeCopilotTaskListReq() *DescribeCopilotTaskListReq {
	return &DescribeCopilotTaskListReq{}
}

func (p *DescribeCopilotTaskListReq) InitDefault() {
}

var DescribeCopilotTaskListReq_TaskIDList_DEFAULT []string

func (p *DescribeCopilotTaskListReq) GetTaskIDList() (v []string) {
	if !p.IsSetTaskIDList() {
		return DescribeCopilotTaskListReq_TaskIDList_DEFAULT
	}
	return p.TaskIDList
}
func (p *DescribeCopilotTaskListReq) SetTaskIDList(val []string) {
	p.TaskIDList = val
}

var fieldIDToName_DescribeCopilotTaskListReq = map[int16]string{
	1: "taskIDList",
}

func (p *DescribeCopilotTaskListReq) IsSetTaskIDList() bool {
	return p.TaskIDList != nil
}

func (p *DescribeCopilotTaskListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotTaskListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCopilotTaskListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeCopilotTaskListReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskIDList = _field
	return nil
}

func (p *DescribeCopilotTaskListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotTaskListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCopilotTaskListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCopilotTaskListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskIDList() {
		if err = oprot.WriteFieldBegin("taskIDList", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TaskIDList)); err != nil {
			return err
		}
		for _, v := range p.TaskIDList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCopilotTaskListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCopilotTaskListReq(%+v)", *p)

}

func (p *DescribeCopilotTaskListReq) DeepEqual(ano *DescribeCopilotTaskListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskIDList) {
		return false
	}
	return true
}

func (p *DescribeCopilotTaskListReq) Field1DeepEqual(src []string) bool {

	if len(p.TaskIDList) != len(src) {
		return false
	}
	for i, v := range p.TaskIDList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeCopilotTaskListResp struct {
	MessageList []*ChatMessage `thrift:"messageList,1,optional" frugal:"1,optional,list<ChatMessage>" json:"messageList,omitempty"`
}

func NewDescribeCopilotTaskListResp() *DescribeCopilotTaskListResp {
	return &DescribeCopilotTaskListResp{}
}

func (p *DescribeCopilotTaskListResp) InitDefault() {
}

var DescribeCopilotTaskListResp_MessageList_DEFAULT []*ChatMessage

func (p *DescribeCopilotTaskListResp) GetMessageList() (v []*ChatMessage) {
	if !p.IsSetMessageList() {
		return DescribeCopilotTaskListResp_MessageList_DEFAULT
	}
	return p.MessageList
}
func (p *DescribeCopilotTaskListResp) SetMessageList(val []*ChatMessage) {
	p.MessageList = val
}

var fieldIDToName_DescribeCopilotTaskListResp = map[int16]string{
	1: "messageList",
}

func (p *DescribeCopilotTaskListResp) IsSetMessageList() bool {
	return p.MessageList != nil
}

func (p *DescribeCopilotTaskListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotTaskListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCopilotTaskListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeCopilotTaskListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChatMessage, 0, size)
	values := make([]ChatMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MessageList = _field
	return nil
}

func (p *DescribeCopilotTaskListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotTaskListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCopilotTaskListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCopilotTaskListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageList() {
		if err = oprot.WriteFieldBegin("messageList", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MessageList)); err != nil {
			return err
		}
		for _, v := range p.MessageList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCopilotTaskListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCopilotTaskListResp(%+v)", *p)

}

func (p *DescribeCopilotTaskListResp) DeepEqual(ano *DescribeCopilotTaskListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageList) {
		return false
	}
	return true
}

func (p *DescribeCopilotTaskListResp) Field1DeepEqual(src []*ChatMessage) bool {

	if len(p.MessageList) != len(src) {
		return false
	}
	for i, v := range p.MessageList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type TriggerButtonReq struct {
	MessageID  string  `thrift:"MessageID,1,required" frugal:"1,required,string" json:"MessageID"`
	ButtonName string  `thrift:"ButtonName,2,required" frugal:"2,required,string" json:"ButtonName"`
	OrderID    *string `thrift:"OrderID,3,optional" frugal:"3,optional,string" json:"OrderID,omitempty"`
}

func NewTriggerButtonReq() *TriggerButtonReq {
	return &TriggerButtonReq{}
}

func (p *TriggerButtonReq) InitDefault() {
}

func (p *TriggerButtonReq) GetMessageID() (v string) {
	return p.MessageID
}

func (p *TriggerButtonReq) GetButtonName() (v string) {
	return p.ButtonName
}

var TriggerButtonReq_OrderID_DEFAULT string

func (p *TriggerButtonReq) GetOrderID() (v string) {
	if !p.IsSetOrderID() {
		return TriggerButtonReq_OrderID_DEFAULT
	}
	return *p.OrderID
}
func (p *TriggerButtonReq) SetMessageID(val string) {
	p.MessageID = val
}
func (p *TriggerButtonReq) SetButtonName(val string) {
	p.ButtonName = val
}
func (p *TriggerButtonReq) SetOrderID(val *string) {
	p.OrderID = val
}

var fieldIDToName_TriggerButtonReq = map[int16]string{
	1: "MessageID",
	2: "ButtonName",
	3: "OrderID",
}

func (p *TriggerButtonReq) IsSetOrderID() bool {
	return p.OrderID != nil
}

func (p *TriggerButtonReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerButtonReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageID bool = false
	var issetButtonName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetButtonName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetButtonName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TriggerButtonReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TriggerButtonReq[fieldId]))
}

func (p *TriggerButtonReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *TriggerButtonReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ButtonName = _field
	return nil
}
func (p *TriggerButtonReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderID = _field
	return nil
}

func (p *TriggerButtonReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerButtonReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("TriggerButtonReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerButtonReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TriggerButtonReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ButtonName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ButtonName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TriggerButtonReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderID() {
		if err = oprot.WriteFieldBegin("OrderID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TriggerButtonReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerButtonReq(%+v)", *p)

}

func (p *TriggerButtonReq) DeepEqual(ano *TriggerButtonReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ButtonName) {
		return false
	}
	if !p.Field3DeepEqual(ano.OrderID) {
		return false
	}
	return true
}

func (p *TriggerButtonReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageID, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerButtonReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ButtonName, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerButtonReq) Field3DeepEqual(src *string) bool {

	if p.OrderID == src {
		return true
	} else if p.OrderID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OrderID, *src) != 0 {
		return false
	}
	return true
}

type TriggerButtonResp struct {
}

func NewTriggerButtonResp() *TriggerButtonResp {
	return &TriggerButtonResp{}
}

func (p *TriggerButtonResp) InitDefault() {
}

var fieldIDToName_TriggerButtonResp = map[int16]string{}

func (p *TriggerButtonResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerButtonResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TriggerButtonResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerButtonResp")

	if err = oprot.WriteStructBegin("TriggerButtonResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerButtonResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerButtonResp(%+v)", *p)

}

func (p *TriggerButtonResp) DeepEqual(ano *TriggerButtonResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ChatMessage struct {
	MessageID    string              `thrift:"messageID,1,required" frugal:"1,required,string" json:"messageID"`
	ChatID       *string             `thrift:"chatID,2,optional" frugal:"2,optional,string" json:"chatID,omitempty"`
	TaskID       *string             `thrift:"taskID,3,optional" frugal:"3,optional,string" json:"taskID,omitempty"`
	Content      string              `thrift:"content,4,required" frugal:"4,required,string" json:"content"`
	ChatType     string              `thrift:"chatType,5,required" frugal:"5,required,string" json:"chatType"`
	FunctionName *string             `thrift:"functionName,6,optional" frugal:"6,optional,string" json:"functionName,omitempty"`
	ExtraInfo    *CopilotExtraInfo   `thrift:"extraInfo,7,optional" frugal:"7,optional,CopilotExtraInfo" json:"extraInfo,omitempty"`
	Reply        *RateModelReplyEnum `thrift:"reply,8,optional" frugal:"8,optional,RateModelReplyEnum" json:"reply,omitempty"`
	AgentName    *AgentNameEnum      `thrift:"agentName,9,optional" frugal:"9,optional,AgentNameEnum" json:"agentName,omitempty"`
	UpdateTime   *int64              `thrift:"updateTime,10,optional" frugal:"10,optional,i64" json:"updateTime,omitempty"`
}

func NewChatMessage() *ChatMessage {
	return &ChatMessage{}
}

func (p *ChatMessage) InitDefault() {
}

func (p *ChatMessage) GetMessageID() (v string) {
	return p.MessageID
}

var ChatMessage_ChatID_DEFAULT string

func (p *ChatMessage) GetChatID() (v string) {
	if !p.IsSetChatID() {
		return ChatMessage_ChatID_DEFAULT
	}
	return *p.ChatID
}

var ChatMessage_TaskID_DEFAULT string

func (p *ChatMessage) GetTaskID() (v string) {
	if !p.IsSetTaskID() {
		return ChatMessage_TaskID_DEFAULT
	}
	return *p.TaskID
}

func (p *ChatMessage) GetContent() (v string) {
	return p.Content
}

func (p *ChatMessage) GetChatType() (v string) {
	return p.ChatType
}

var ChatMessage_FunctionName_DEFAULT string

func (p *ChatMessage) GetFunctionName() (v string) {
	if !p.IsSetFunctionName() {
		return ChatMessage_FunctionName_DEFAULT
	}
	return *p.FunctionName
}

var ChatMessage_ExtraInfo_DEFAULT *CopilotExtraInfo

func (p *ChatMessage) GetExtraInfo() (v *CopilotExtraInfo) {
	if !p.IsSetExtraInfo() {
		return ChatMessage_ExtraInfo_DEFAULT
	}
	return p.ExtraInfo
}

var ChatMessage_Reply_DEFAULT RateModelReplyEnum

func (p *ChatMessage) GetReply() (v RateModelReplyEnum) {
	if !p.IsSetReply() {
		return ChatMessage_Reply_DEFAULT
	}
	return *p.Reply
}

var ChatMessage_AgentName_DEFAULT AgentNameEnum

func (p *ChatMessage) GetAgentName() (v AgentNameEnum) {
	if !p.IsSetAgentName() {
		return ChatMessage_AgentName_DEFAULT
	}
	return *p.AgentName
}

var ChatMessage_UpdateTime_DEFAULT int64

func (p *ChatMessage) GetUpdateTime() (v int64) {
	if !p.IsSetUpdateTime() {
		return ChatMessage_UpdateTime_DEFAULT
	}
	return *p.UpdateTime
}
func (p *ChatMessage) SetMessageID(val string) {
	p.MessageID = val
}
func (p *ChatMessage) SetChatID(val *string) {
	p.ChatID = val
}
func (p *ChatMessage) SetTaskID(val *string) {
	p.TaskID = val
}
func (p *ChatMessage) SetContent(val string) {
	p.Content = val
}
func (p *ChatMessage) SetChatType(val string) {
	p.ChatType = val
}
func (p *ChatMessage) SetFunctionName(val *string) {
	p.FunctionName = val
}
func (p *ChatMessage) SetExtraInfo(val *CopilotExtraInfo) {
	p.ExtraInfo = val
}
func (p *ChatMessage) SetReply(val *RateModelReplyEnum) {
	p.Reply = val
}
func (p *ChatMessage) SetAgentName(val *AgentNameEnum) {
	p.AgentName = val
}
func (p *ChatMessage) SetUpdateTime(val *int64) {
	p.UpdateTime = val
}

var fieldIDToName_ChatMessage = map[int16]string{
	1:  "messageID",
	2:  "chatID",
	3:  "taskID",
	4:  "content",
	5:  "chatType",
	6:  "functionName",
	7:  "extraInfo",
	8:  "reply",
	9:  "agentName",
	10: "updateTime",
}

func (p *ChatMessage) IsSetChatID() bool {
	return p.ChatID != nil
}

func (p *ChatMessage) IsSetTaskID() bool {
	return p.TaskID != nil
}

func (p *ChatMessage) IsSetFunctionName() bool {
	return p.FunctionName != nil
}

func (p *ChatMessage) IsSetExtraInfo() bool {
	return p.ExtraInfo != nil
}

func (p *ChatMessage) IsSetReply() bool {
	return p.Reply != nil
}

func (p *ChatMessage) IsSetAgentName() bool {
	return p.AgentName != nil
}

func (p *ChatMessage) IsSetUpdateTime() bool {
	return p.UpdateTime != nil
}

func (p *ChatMessage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatMessage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageID bool = false
	var issetContent bool = false
	var issetChatType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetChatType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatMessage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChatMessage[fieldId]))
}

func (p *ChatMessage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *ChatMessage) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatID = _field
	return nil
}
func (p *ChatMessage) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskID = _field
	return nil
}
func (p *ChatMessage) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ChatMessage) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatType = _field
	return nil
}
func (p *ChatMessage) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FunctionName = _field
	return nil
}
func (p *ChatMessage) ReadField7(iprot thrift.TProtocol) error {
	_field := NewCopilotExtraInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExtraInfo = _field
	return nil
}
func (p *ChatMessage) ReadField8(iprot thrift.TProtocol) error {

	var _field *RateModelReplyEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RateModelReplyEnum(v)
		_field = &tmp
	}
	p.Reply = _field
	return nil
}
func (p *ChatMessage) ReadField9(iprot thrift.TProtocol) error {

	var _field *AgentNameEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AgentNameEnum(v)
		_field = &tmp
	}
	p.AgentName = _field
	return nil
}
func (p *ChatMessage) ReadField10(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdateTime = _field
	return nil
}

func (p *ChatMessage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatMessage")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChatMessage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatMessage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChatMessage) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatID() {
		if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChatID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChatMessage) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskID() {
		if err = oprot.WriteFieldBegin("taskID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ChatMessage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ChatMessage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ChatMessage) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFunctionName() {
		if err = oprot.WriteFieldBegin("functionName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FunctionName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ChatMessage) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtraInfo() {
		if err = oprot.WriteFieldBegin("extraInfo", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ExtraInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ChatMessage) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetReply() {
		if err = oprot.WriteFieldBegin("reply", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Reply)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ChatMessage) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentName() {
		if err = oprot.WriteFieldBegin("agentName", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AgentName)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ChatMessage) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateTime() {
		if err = oprot.WriteFieldBegin("updateTime", thrift.I64, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.UpdateTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ChatMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatMessage(%+v)", *p)

}

func (p *ChatMessage) DeepEqual(ano *ChatMessage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskID) {
		return false
	}
	if !p.Field4DeepEqual(ano.Content) {
		return false
	}
	if !p.Field5DeepEqual(ano.ChatType) {
		return false
	}
	if !p.Field6DeepEqual(ano.FunctionName) {
		return false
	}
	if !p.Field7DeepEqual(ano.ExtraInfo) {
		return false
	}
	if !p.Field8DeepEqual(ano.Reply) {
		return false
	}
	if !p.Field9DeepEqual(ano.AgentName) {
		return false
	}
	if !p.Field10DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *ChatMessage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageID, src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field2DeepEqual(src *string) bool {

	if p.ChatID == src {
		return true
	} else if p.ChatID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChatID, *src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field3DeepEqual(src *string) bool {

	if p.TaskID == src {
		return true
	} else if p.TaskID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskID, *src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ChatType, src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field6DeepEqual(src *string) bool {

	if p.FunctionName == src {
		return true
	} else if p.FunctionName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FunctionName, *src) != 0 {
		return false
	}
	return true
}
func (p *ChatMessage) Field7DeepEqual(src *CopilotExtraInfo) bool {

	if !p.ExtraInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ChatMessage) Field8DeepEqual(src *RateModelReplyEnum) bool {

	if p.Reply == src {
		return true
	} else if p.Reply == nil || src == nil {
		return false
	}
	if *p.Reply != *src {
		return false
	}
	return true
}
func (p *ChatMessage) Field9DeepEqual(src *AgentNameEnum) bool {

	if p.AgentName == src {
		return true
	} else if p.AgentName == nil || src == nil {
		return false
	}
	if *p.AgentName != *src {
		return false
	}
	return true
}
func (p *ChatMessage) Field10DeepEqual(src *int64) bool {

	if p.UpdateTime == src {
		return true
	} else if p.UpdateTime == nil || src == nil {
		return false
	}
	if *p.UpdateTime != *src {
		return false
	}
	return true
}

type CopilotExtraInfo struct {
	InstanceID   *string            `thrift:"InstanceID,1,optional" frugal:"1,optional,string" json:"InstanceID,omitempty"`
	InstanceType *InstanceType      `thrift:"InstanceType,2,optional" frugal:"2,optional,InstanceType" json:"InstanceType,omitempty"`
	RelatedDocs  []*RelatedDocInfo  `thrift:"RelatedDocs,3,optional" frugal:"3,optional,list<RelatedDocInfo>" json:"RelatedDocs,omitempty"`
	ExtraButtons []*ExtraButtonInfo `thrift:"ExtraButtons,4,optional" frugal:"4,optional,list<ExtraButtonInfo>" json:"ExtraButtons,omitempty"`
}

func NewCopilotExtraInfo() *CopilotExtraInfo {
	return &CopilotExtraInfo{}
}

func (p *CopilotExtraInfo) InitDefault() {
}

var CopilotExtraInfo_InstanceID_DEFAULT string

func (p *CopilotExtraInfo) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return CopilotExtraInfo_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var CopilotExtraInfo_InstanceType_DEFAULT InstanceType

func (p *CopilotExtraInfo) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return CopilotExtraInfo_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var CopilotExtraInfo_RelatedDocs_DEFAULT []*RelatedDocInfo

func (p *CopilotExtraInfo) GetRelatedDocs() (v []*RelatedDocInfo) {
	if !p.IsSetRelatedDocs() {
		return CopilotExtraInfo_RelatedDocs_DEFAULT
	}
	return p.RelatedDocs
}

var CopilotExtraInfo_ExtraButtons_DEFAULT []*ExtraButtonInfo

func (p *CopilotExtraInfo) GetExtraButtons() (v []*ExtraButtonInfo) {
	if !p.IsSetExtraButtons() {
		return CopilotExtraInfo_ExtraButtons_DEFAULT
	}
	return p.ExtraButtons
}
func (p *CopilotExtraInfo) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *CopilotExtraInfo) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *CopilotExtraInfo) SetRelatedDocs(val []*RelatedDocInfo) {
	p.RelatedDocs = val
}
func (p *CopilotExtraInfo) SetExtraButtons(val []*ExtraButtonInfo) {
	p.ExtraButtons = val
}

var fieldIDToName_CopilotExtraInfo = map[int16]string{
	1: "InstanceID",
	2: "InstanceType",
	3: "RelatedDocs",
	4: "ExtraButtons",
}

func (p *CopilotExtraInfo) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *CopilotExtraInfo) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *CopilotExtraInfo) IsSetRelatedDocs() bool {
	return p.RelatedDocs != nil
}

func (p *CopilotExtraInfo) IsSetExtraButtons() bool {
	return p.ExtraButtons != nil
}

func (p *CopilotExtraInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotExtraInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotExtraInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CopilotExtraInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *CopilotExtraInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *CopilotExtraInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RelatedDocInfo, 0, size)
	values := make([]RelatedDocInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RelatedDocs = _field
	return nil
}
func (p *CopilotExtraInfo) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExtraButtonInfo, 0, size)
	values := make([]ExtraButtonInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExtraButtons = _field
	return nil
}

func (p *CopilotExtraInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotExtraInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotExtraInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotExtraInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotExtraInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotExtraInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelatedDocs() {
		if err = oprot.WriteFieldBegin("RelatedDocs", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelatedDocs)); err != nil {
			return err
		}
		for _, v := range p.RelatedDocs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CopilotExtraInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtraButtons() {
		if err = oprot.WriteFieldBegin("ExtraButtons", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ExtraButtons)); err != nil {
			return err
		}
		for _, v := range p.ExtraButtons {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CopilotExtraInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotExtraInfo(%+v)", *p)

}

func (p *CopilotExtraInfo) DeepEqual(ano *CopilotExtraInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RelatedDocs) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExtraButtons) {
		return false
	}
	return true
}

func (p *CopilotExtraInfo) Field1DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *CopilotExtraInfo) Field2DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *CopilotExtraInfo) Field3DeepEqual(src []*RelatedDocInfo) bool {

	if len(p.RelatedDocs) != len(src) {
		return false
	}
	for i, v := range p.RelatedDocs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CopilotExtraInfo) Field4DeepEqual(src []*ExtraButtonInfo) bool {

	if len(p.ExtraButtons) != len(src) {
		return false
	}
	for i, v := range p.ExtraButtons {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ExtraButtonInfo struct {
	ButtonName *string `thrift:"ButtonName,1,optional" frugal:"1,optional,string" json:"ButtonName,omitempty"`
	Arguments  *string `thrift:"Arguments,2,optional" frugal:"2,optional,string" json:"Arguments,omitempty"`
	IsClick    bool    `thrift:"IsClick,3,required" frugal:"3,required,bool" json:"IsClick"`
	OrderID    *string `thrift:"OrderID,4,optional" frugal:"4,optional,string" json:"OrderID,omitempty"`
}

func NewExtraButtonInfo() *ExtraButtonInfo {
	return &ExtraButtonInfo{}
}

func (p *ExtraButtonInfo) InitDefault() {
}

var ExtraButtonInfo_ButtonName_DEFAULT string

func (p *ExtraButtonInfo) GetButtonName() (v string) {
	if !p.IsSetButtonName() {
		return ExtraButtonInfo_ButtonName_DEFAULT
	}
	return *p.ButtonName
}

var ExtraButtonInfo_Arguments_DEFAULT string

func (p *ExtraButtonInfo) GetArguments() (v string) {
	if !p.IsSetArguments() {
		return ExtraButtonInfo_Arguments_DEFAULT
	}
	return *p.Arguments
}

func (p *ExtraButtonInfo) GetIsClick() (v bool) {
	return p.IsClick
}

var ExtraButtonInfo_OrderID_DEFAULT string

func (p *ExtraButtonInfo) GetOrderID() (v string) {
	if !p.IsSetOrderID() {
		return ExtraButtonInfo_OrderID_DEFAULT
	}
	return *p.OrderID
}
func (p *ExtraButtonInfo) SetButtonName(val *string) {
	p.ButtonName = val
}
func (p *ExtraButtonInfo) SetArguments(val *string) {
	p.Arguments = val
}
func (p *ExtraButtonInfo) SetIsClick(val bool) {
	p.IsClick = val
}
func (p *ExtraButtonInfo) SetOrderID(val *string) {
	p.OrderID = val
}

var fieldIDToName_ExtraButtonInfo = map[int16]string{
	1: "ButtonName",
	2: "Arguments",
	3: "IsClick",
	4: "OrderID",
}

func (p *ExtraButtonInfo) IsSetButtonName() bool {
	return p.ButtonName != nil
}

func (p *ExtraButtonInfo) IsSetArguments() bool {
	return p.Arguments != nil
}

func (p *ExtraButtonInfo) IsSetOrderID() bool {
	return p.OrderID != nil
}

func (p *ExtraButtonInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExtraButtonInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIsClick bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsClick = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIsClick {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExtraButtonInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExtraButtonInfo[fieldId]))
}

func (p *ExtraButtonInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ButtonName = _field
	return nil
}
func (p *ExtraButtonInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Arguments = _field
	return nil
}
func (p *ExtraButtonInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsClick = _field
	return nil
}
func (p *ExtraButtonInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderID = _field
	return nil
}

func (p *ExtraButtonInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExtraButtonInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExtraButtonInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExtraButtonInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetButtonName() {
		if err = oprot.WriteFieldBegin("ButtonName", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ButtonName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExtraButtonInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetArguments() {
		if err = oprot.WriteFieldBegin("Arguments", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Arguments); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExtraButtonInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsClick", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsClick); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExtraButtonInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderID() {
		if err = oprot.WriteFieldBegin("OrderID", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExtraButtonInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraButtonInfo(%+v)", *p)

}

func (p *ExtraButtonInfo) DeepEqual(ano *ExtraButtonInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ButtonName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Arguments) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsClick) {
		return false
	}
	if !p.Field4DeepEqual(ano.OrderID) {
		return false
	}
	return true
}

func (p *ExtraButtonInfo) Field1DeepEqual(src *string) bool {

	if p.ButtonName == src {
		return true
	} else if p.ButtonName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ButtonName, *src) != 0 {
		return false
	}
	return true
}
func (p *ExtraButtonInfo) Field2DeepEqual(src *string) bool {

	if p.Arguments == src {
		return true
	} else if p.Arguments == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Arguments, *src) != 0 {
		return false
	}
	return true
}
func (p *ExtraButtonInfo) Field3DeepEqual(src bool) bool {

	if p.IsClick != src {
		return false
	}
	return true
}
func (p *ExtraButtonInfo) Field4DeepEqual(src *string) bool {

	if p.OrderID == src {
		return true
	} else if p.OrderID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OrderID, *src) != 0 {
		return false
	}
	return true
}

type CopilotDataPoint struct {
	Time  string `thrift:"Time,1,required" frugal:"1,required,string" json:"Time"`
	Value string `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
}

func NewCopilotDataPoint() *CopilotDataPoint {
	return &CopilotDataPoint{}
}

func (p *CopilotDataPoint) InitDefault() {
}

func (p *CopilotDataPoint) GetTime() (v string) {
	return p.Time
}

func (p *CopilotDataPoint) GetValue() (v string) {
	return p.Value
}
func (p *CopilotDataPoint) SetTime(val string) {
	p.Time = val
}
func (p *CopilotDataPoint) SetValue(val string) {
	p.Value = val
}

var fieldIDToName_CopilotDataPoint = map[int16]string{
	1: "Time",
	2: "Value",
}

func (p *CopilotDataPoint) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotDataPoint")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTime bool = false
	var issetValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotDataPoint[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotDataPoint[fieldId]))
}

func (p *CopilotDataPoint) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Time = _field
	return nil
}
func (p *CopilotDataPoint) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *CopilotDataPoint) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotDataPoint")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotDataPoint"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotDataPoint) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Time", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Time); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotDataPoint) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotDataPoint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotDataPoint(%+v)", *p)

}

func (p *CopilotDataPoint) DeepEqual(ano *CopilotDataPoint) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Time) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *CopilotDataPoint) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Time, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotDataPoint) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}

type CopilotMetricDataResults struct {
	FunctionDescription string              `thrift:"FunctionDescription,1,required" frugal:"1,required,string" json:"FunctionDescription"`
	InstanceId          string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	NodeId              string              `thrift:"NodeId,3,required" frugal:"3,required,string" json:"NodeId"`
	DataPoints          []*CopilotDataPoint `thrift:"DataPoints,4,required" frugal:"4,required,list<CopilotDataPoint>" json:"DataPoints"`
}

func NewCopilotMetricDataResults() *CopilotMetricDataResults {
	return &CopilotMetricDataResults{}
}

func (p *CopilotMetricDataResults) InitDefault() {
}

func (p *CopilotMetricDataResults) GetFunctionDescription() (v string) {
	return p.FunctionDescription
}

func (p *CopilotMetricDataResults) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CopilotMetricDataResults) GetNodeId() (v string) {
	return p.NodeId
}

func (p *CopilotMetricDataResults) GetDataPoints() (v []*CopilotDataPoint) {
	return p.DataPoints
}
func (p *CopilotMetricDataResults) SetFunctionDescription(val string) {
	p.FunctionDescription = val
}
func (p *CopilotMetricDataResults) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CopilotMetricDataResults) SetNodeId(val string) {
	p.NodeId = val
}
func (p *CopilotMetricDataResults) SetDataPoints(val []*CopilotDataPoint) {
	p.DataPoints = val
}

var fieldIDToName_CopilotMetricDataResults = map[int16]string{
	1: "FunctionDescription",
	2: "InstanceId",
	3: "NodeId",
	4: "DataPoints",
}

func (p *CopilotMetricDataResults) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotMetricDataResults")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFunctionDescription bool = false
	var issetInstanceId bool = false
	var issetNodeId bool = false
	var issetDataPoints bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFunctionDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFunctionDescription {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataPoints {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotMetricDataResults[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotMetricDataResults[fieldId]))
}

func (p *CopilotMetricDataResults) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FunctionDescription = _field
	return nil
}
func (p *CopilotMetricDataResults) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CopilotMetricDataResults) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *CopilotMetricDataResults) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CopilotDataPoint, 0, size)
	values := make([]CopilotDataPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataPoints = _field
	return nil
}

func (p *CopilotMetricDataResults) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotMetricDataResults")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotMetricDataResults"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotMetricDataResults) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FunctionDescription", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FunctionDescription); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotMetricDataResults) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotMetricDataResults) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CopilotMetricDataResults) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataPoints", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataPoints)); err != nil {
		return err
	}
	for _, v := range p.DataPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CopilotMetricDataResults) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotMetricDataResults(%+v)", *p)

}

func (p *CopilotMetricDataResults) DeepEqual(ano *CopilotMetricDataResults) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FunctionDescription) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataPoints) {
		return false
	}
	return true
}

func (p *CopilotMetricDataResults) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FunctionDescription, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotMetricDataResults) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotMetricDataResults) Field3DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotMetricDataResults) Field4DeepEqual(src []*CopilotDataPoint) bool {

	if len(p.DataPoints) != len(src) {
		return false
	}
	for i, v := range p.DataPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CopilotButtonGenerationReq struct {
	ChatId    string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	MessageId string `thrift:"MessageId,2,required" frugal:"2,required,string" json:"MessageId"`
}

func NewCopilotButtonGenerationReq() *CopilotButtonGenerationReq {
	return &CopilotButtonGenerationReq{}
}

func (p *CopilotButtonGenerationReq) InitDefault() {
}

func (p *CopilotButtonGenerationReq) GetChatId() (v string) {
	return p.ChatId
}

func (p *CopilotButtonGenerationReq) GetMessageId() (v string) {
	return p.MessageId
}
func (p *CopilotButtonGenerationReq) SetChatId(val string) {
	p.ChatId = val
}
func (p *CopilotButtonGenerationReq) SetMessageId(val string) {
	p.MessageId = val
}

var fieldIDToName_CopilotButtonGenerationReq = map[int16]string{
	1: "ChatId",
	2: "MessageId",
}

func (p *CopilotButtonGenerationReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotButtonGenerationReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetMessageId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessageId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotButtonGenerationReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotButtonGenerationReq[fieldId]))
}

func (p *CopilotButtonGenerationReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *CopilotButtonGenerationReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}

func (p *CopilotButtonGenerationReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotButtonGenerationReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotButtonGenerationReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotButtonGenerationReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotButtonGenerationReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotButtonGenerationReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotButtonGenerationReq(%+v)", *p)

}

func (p *CopilotButtonGenerationReq) DeepEqual(ano *CopilotButtonGenerationReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageId) {
		return false
	}
	return true
}

func (p *CopilotButtonGenerationReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotButtonGenerationReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}

type CopilotButtonGenerationResp struct {
	ExtraInfo *CopilotExtraInfo `thrift:"ExtraInfo,1,required" frugal:"1,required,CopilotExtraInfo" json:"ExtraInfo"`
}

func NewCopilotButtonGenerationResp() *CopilotButtonGenerationResp {
	return &CopilotButtonGenerationResp{}
}

func (p *CopilotButtonGenerationResp) InitDefault() {
}

var CopilotButtonGenerationResp_ExtraInfo_DEFAULT *CopilotExtraInfo

func (p *CopilotButtonGenerationResp) GetExtraInfo() (v *CopilotExtraInfo) {
	if !p.IsSetExtraInfo() {
		return CopilotButtonGenerationResp_ExtraInfo_DEFAULT
	}
	return p.ExtraInfo
}
func (p *CopilotButtonGenerationResp) SetExtraInfo(val *CopilotExtraInfo) {
	p.ExtraInfo = val
}

var fieldIDToName_CopilotButtonGenerationResp = map[int16]string{
	1: "ExtraInfo",
}

func (p *CopilotButtonGenerationResp) IsSetExtraInfo() bool {
	return p.ExtraInfo != nil
}

func (p *CopilotButtonGenerationResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotButtonGenerationResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExtraInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExtraInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExtraInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotButtonGenerationResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotButtonGenerationResp[fieldId]))
}

func (p *CopilotButtonGenerationResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCopilotExtraInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExtraInfo = _field
	return nil
}

func (p *CopilotButtonGenerationResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotButtonGenerationResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotButtonGenerationResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotButtonGenerationResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExtraInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExtraInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotButtonGenerationResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotButtonGenerationResp(%+v)", *p)

}

func (p *CopilotButtonGenerationResp) DeepEqual(ano *CopilotButtonGenerationResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExtraInfo) {
		return false
	}
	return true
}

func (p *CopilotButtonGenerationResp) Field1DeepEqual(src *CopilotExtraInfo) bool {

	if !p.ExtraInfo.DeepEqual(src) {
		return false
	}
	return true
}

type CreateCopilotChatReq struct {
	SceneType  int8    `thrift:"sceneType,1,required" frugal:"1,required,i8" json:"sceneType"`
	InstanceID *string `thrift:"InstanceID,2,optional" frugal:"2,optional,string" json:"InstanceID,omitempty"`
}

func NewCreateCopilotChatReq() *CreateCopilotChatReq {
	return &CreateCopilotChatReq{}
}

func (p *CreateCopilotChatReq) InitDefault() {
}

func (p *CreateCopilotChatReq) GetSceneType() (v int8) {
	return p.SceneType
}

var CreateCopilotChatReq_InstanceID_DEFAULT string

func (p *CreateCopilotChatReq) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return CreateCopilotChatReq_InstanceID_DEFAULT
	}
	return *p.InstanceID
}
func (p *CreateCopilotChatReq) SetSceneType(val int8) {
	p.SceneType = val
}
func (p *CreateCopilotChatReq) SetInstanceID(val *string) {
	p.InstanceID = val
}

var fieldIDToName_CreateCopilotChatReq = map[int16]string{
	1: "sceneType",
	2: "InstanceID",
}

func (p *CreateCopilotChatReq) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *CreateCopilotChatReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCopilotChatReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSceneType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSceneType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSceneType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateCopilotChatReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateCopilotChatReq[fieldId]))
}

func (p *CreateCopilotChatReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SceneType = _field
	return nil
}
func (p *CreateCopilotChatReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}

func (p *CreateCopilotChatReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCopilotChatReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCopilotChatReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateCopilotChatReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sceneType", thrift.BYTE, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.SceneType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateCopilotChatReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateCopilotChatReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCopilotChatReq(%+v)", *p)

}

func (p *CreateCopilotChatReq) DeepEqual(ano *CreateCopilotChatReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SceneType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	return true
}

func (p *CreateCopilotChatReq) Field1DeepEqual(src int8) bool {

	if p.SceneType != src {
		return false
	}
	return true
}
func (p *CreateCopilotChatReq) Field2DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}

type CreateCopilotChatResp struct {
	ChatID string `thrift:"chatID,1,required" frugal:"1,required,string" json:"chatID"`
}

func NewCreateCopilotChatResp() *CreateCopilotChatResp {
	return &CreateCopilotChatResp{}
}

func (p *CreateCopilotChatResp) InitDefault() {
}

func (p *CreateCopilotChatResp) GetChatID() (v string) {
	return p.ChatID
}
func (p *CreateCopilotChatResp) SetChatID(val string) {
	p.ChatID = val
}

var fieldIDToName_CreateCopilotChatResp = map[int16]string{
	1: "chatID",
}

func (p *CreateCopilotChatResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCopilotChatResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateCopilotChatResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateCopilotChatResp[fieldId]))
}

func (p *CreateCopilotChatResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}

func (p *CreateCopilotChatResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateCopilotChatResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCopilotChatResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateCopilotChatResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateCopilotChatResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCopilotChatResp(%+v)", *p)

}

func (p *CreateCopilotChatResp) DeepEqual(ano *CreateCopilotChatResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	return true
}

func (p *CreateCopilotChatResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}

type UpdateCopilotChatNameReq struct {
	ChatID       string  `thrift:"chatID,1,required" frugal:"1,required,string" json:"chatID"`
	ChatName     *string `thrift:"chatName,2,optional" frugal:"2,optional,string" json:"chatName,omitempty"`
	FirstContent *string `thrift:"firstContent,3,optional" frugal:"3,optional,string" json:"firstContent,omitempty"`
}

func NewUpdateCopilotChatNameReq() *UpdateCopilotChatNameReq {
	return &UpdateCopilotChatNameReq{}
}

func (p *UpdateCopilotChatNameReq) InitDefault() {
}

func (p *UpdateCopilotChatNameReq) GetChatID() (v string) {
	return p.ChatID
}

var UpdateCopilotChatNameReq_ChatName_DEFAULT string

func (p *UpdateCopilotChatNameReq) GetChatName() (v string) {
	if !p.IsSetChatName() {
		return UpdateCopilotChatNameReq_ChatName_DEFAULT
	}
	return *p.ChatName
}

var UpdateCopilotChatNameReq_FirstContent_DEFAULT string

func (p *UpdateCopilotChatNameReq) GetFirstContent() (v string) {
	if !p.IsSetFirstContent() {
		return UpdateCopilotChatNameReq_FirstContent_DEFAULT
	}
	return *p.FirstContent
}
func (p *UpdateCopilotChatNameReq) SetChatID(val string) {
	p.ChatID = val
}
func (p *UpdateCopilotChatNameReq) SetChatName(val *string) {
	p.ChatName = val
}
func (p *UpdateCopilotChatNameReq) SetFirstContent(val *string) {
	p.FirstContent = val
}

var fieldIDToName_UpdateCopilotChatNameReq = map[int16]string{
	1: "chatID",
	2: "chatName",
	3: "firstContent",
}

func (p *UpdateCopilotChatNameReq) IsSetChatName() bool {
	return p.ChatName != nil
}

func (p *UpdateCopilotChatNameReq) IsSetFirstContent() bool {
	return p.FirstContent != nil
}

func (p *UpdateCopilotChatNameReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateCopilotChatNameReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateCopilotChatNameReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateCopilotChatNameReq[fieldId]))
}

func (p *UpdateCopilotChatNameReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}
func (p *UpdateCopilotChatNameReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatName = _field
	return nil
}
func (p *UpdateCopilotChatNameReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FirstContent = _field
	return nil
}

func (p *UpdateCopilotChatNameReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateCopilotChatNameReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateCopilotChatNameReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateCopilotChatNameReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateCopilotChatNameReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatName() {
		if err = oprot.WriteFieldBegin("chatName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChatName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateCopilotChatNameReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFirstContent() {
		if err = oprot.WriteFieldBegin("firstContent", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FirstContent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateCopilotChatNameReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCopilotChatNameReq(%+v)", *p)

}

func (p *UpdateCopilotChatNameReq) DeepEqual(ano *UpdateCopilotChatNameReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChatName) {
		return false
	}
	if !p.Field3DeepEqual(ano.FirstContent) {
		return false
	}
	return true
}

func (p *UpdateCopilotChatNameReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateCopilotChatNameReq) Field2DeepEqual(src *string) bool {

	if p.ChatName == src {
		return true
	} else if p.ChatName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChatName, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateCopilotChatNameReq) Field3DeepEqual(src *string) bool {

	if p.FirstContent == src {
		return true
	} else if p.FirstContent == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FirstContent, *src) != 0 {
		return false
	}
	return true
}

type UpdateCopilotChatNameResp struct {
	ChatName string `thrift:"chatName,1,required" frugal:"1,required,string" json:"chatName"`
}

func NewUpdateCopilotChatNameResp() *UpdateCopilotChatNameResp {
	return &UpdateCopilotChatNameResp{}
}

func (p *UpdateCopilotChatNameResp) InitDefault() {
}

func (p *UpdateCopilotChatNameResp) GetChatName() (v string) {
	return p.ChatName
}
func (p *UpdateCopilotChatNameResp) SetChatName(val string) {
	p.ChatName = val
}

var fieldIDToName_UpdateCopilotChatNameResp = map[int16]string{
	1: "chatName",
}

func (p *UpdateCopilotChatNameResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateCopilotChatNameResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateCopilotChatNameResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateCopilotChatNameResp[fieldId]))
}

func (p *UpdateCopilotChatNameResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatName = _field
	return nil
}

func (p *UpdateCopilotChatNameResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateCopilotChatNameResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateCopilotChatNameResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateCopilotChatNameResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateCopilotChatNameResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCopilotChatNameResp(%+v)", *p)

}

func (p *UpdateCopilotChatNameResp) DeepEqual(ano *UpdateCopilotChatNameResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatName) {
		return false
	}
	return true
}

func (p *UpdateCopilotChatNameResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatName, src) != 0 {
		return false
	}
	return true
}

type DeleteCopilotChatReq struct {
	ChatID string `thrift:"chatID,1,required" frugal:"1,required,string" json:"chatID"`
}

func NewDeleteCopilotChatReq() *DeleteCopilotChatReq {
	return &DeleteCopilotChatReq{}
}

func (p *DeleteCopilotChatReq) InitDefault() {
}

func (p *DeleteCopilotChatReq) GetChatID() (v string) {
	return p.ChatID
}
func (p *DeleteCopilotChatReq) SetChatID(val string) {
	p.ChatID = val
}

var fieldIDToName_DeleteCopilotChatReq = map[int16]string{
	1: "chatID",
}

func (p *DeleteCopilotChatReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCopilotChatReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteCopilotChatReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteCopilotChatReq[fieldId]))
}

func (p *DeleteCopilotChatReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}

func (p *DeleteCopilotChatReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCopilotChatReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCopilotChatReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteCopilotChatReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteCopilotChatReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCopilotChatReq(%+v)", *p)

}

func (p *DeleteCopilotChatReq) DeepEqual(ano *DeleteCopilotChatReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	return true
}

func (p *DeleteCopilotChatReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}

type DeleteCopilotChatResp struct {
}

func NewDeleteCopilotChatResp() *DeleteCopilotChatResp {
	return &DeleteCopilotChatResp{}
}

func (p *DeleteCopilotChatResp) InitDefault() {
}

var fieldIDToName_DeleteCopilotChatResp = map[int16]string{}

func (p *DeleteCopilotChatResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCopilotChatResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteCopilotChatResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteCopilotChatResp")

	if err = oprot.WriteStructBegin("DeleteCopilotChatResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteCopilotChatResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCopilotChatResp(%+v)", *p)

}

func (p *DeleteCopilotChatResp) DeepEqual(ano *DeleteCopilotChatResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ChatInfo struct {
	ChatID   string `thrift:"chatID,1,required" frugal:"1,required,string" json:"chatID"`
	ChatName string `thrift:"chatName,2,required" frugal:"2,required,string" json:"chatName"`
}

func NewChatInfo() *ChatInfo {
	return &ChatInfo{}
}

func (p *ChatInfo) InitDefault() {
}

func (p *ChatInfo) GetChatID() (v string) {
	return p.ChatID
}

func (p *ChatInfo) GetChatName() (v string) {
	return p.ChatName
}
func (p *ChatInfo) SetChatID(val string) {
	p.ChatID = val
}
func (p *ChatInfo) SetChatName(val string) {
	p.ChatName = val
}

var fieldIDToName_ChatInfo = map[int16]string{
	1: "chatID",
	2: "chatName",
}

func (p *ChatInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatID bool = false
	var issetChatName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChatName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChatInfo[fieldId]))
}

func (p *ChatInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}
func (p *ChatInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatName = _field
	return nil
}

func (p *ChatInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChatInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChatInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChatInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatInfo(%+v)", *p)

}

func (p *ChatInfo) DeepEqual(ano *ChatInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChatName) {
		return false
	}
	return true
}

func (p *ChatInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatID, src) != 0 {
		return false
	}
	return true
}
func (p *ChatInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ChatName, src) != 0 {
		return false
	}
	return true
}

type DescribeCopilotChatListReq struct {
	SceneType  int8    `thrift:"sceneType,1,required" frugal:"1,required,i8" json:"sceneType"`
	InstanceID *string `thrift:"instanceID,2,optional" frugal:"2,optional,string" json:"instanceID,omitempty"`
}

func NewDescribeCopilotChatListReq() *DescribeCopilotChatListReq {
	return &DescribeCopilotChatListReq{}
}

func (p *DescribeCopilotChatListReq) InitDefault() {
}

func (p *DescribeCopilotChatListReq) GetSceneType() (v int8) {
	return p.SceneType
}

var DescribeCopilotChatListReq_InstanceID_DEFAULT string

func (p *DescribeCopilotChatListReq) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return DescribeCopilotChatListReq_InstanceID_DEFAULT
	}
	return *p.InstanceID
}
func (p *DescribeCopilotChatListReq) SetSceneType(val int8) {
	p.SceneType = val
}
func (p *DescribeCopilotChatListReq) SetInstanceID(val *string) {
	p.InstanceID = val
}

var fieldIDToName_DescribeCopilotChatListReq = map[int16]string{
	1: "sceneType",
	2: "instanceID",
}

func (p *DescribeCopilotChatListReq) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *DescribeCopilotChatListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotChatListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSceneType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSceneType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSceneType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCopilotChatListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCopilotChatListReq[fieldId]))
}

func (p *DescribeCopilotChatListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SceneType = _field
	return nil
}
func (p *DescribeCopilotChatListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}

func (p *DescribeCopilotChatListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotChatListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCopilotChatListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCopilotChatListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sceneType", thrift.BYTE, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.SceneType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCopilotChatListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("instanceID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCopilotChatListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCopilotChatListReq(%+v)", *p)

}

func (p *DescribeCopilotChatListReq) DeepEqual(ano *DescribeCopilotChatListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SceneType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	return true
}

func (p *DescribeCopilotChatListReq) Field1DeepEqual(src int8) bool {

	if p.SceneType != src {
		return false
	}
	return true
}
func (p *DescribeCopilotChatListReq) Field2DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}

type DescribeCopilotChatListResp struct {
	ChatList []*ChatInfo `thrift:"chatList,1,required" frugal:"1,required,list<ChatInfo>" json:"chatList"`
}

func NewDescribeCopilotChatListResp() *DescribeCopilotChatListResp {
	return &DescribeCopilotChatListResp{}
}

func (p *DescribeCopilotChatListResp) InitDefault() {
}

func (p *DescribeCopilotChatListResp) GetChatList() (v []*ChatInfo) {
	return p.ChatList
}
func (p *DescribeCopilotChatListResp) SetChatList(val []*ChatInfo) {
	p.ChatList = val
}

var fieldIDToName_DescribeCopilotChatListResp = map[int16]string{
	1: "chatList",
}

func (p *DescribeCopilotChatListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotChatListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCopilotChatListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCopilotChatListResp[fieldId]))
}

func (p *DescribeCopilotChatListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChatInfo, 0, size)
	values := make([]ChatInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChatList = _field
	return nil
}

func (p *DescribeCopilotChatListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCopilotChatListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCopilotChatListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCopilotChatListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chatList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChatList)); err != nil {
		return err
	}
	for _, v := range p.ChatList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCopilotChatListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCopilotChatListResp(%+v)", *p)

}

func (p *DescribeCopilotChatListResp) DeepEqual(ano *DescribeCopilotChatListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatList) {
		return false
	}
	return true
}

func (p *DescribeCopilotChatListResp) Field1DeepEqual(src []*ChatInfo) bool {

	if len(p.ChatList) != len(src) {
		return false
	}
	for i, v := range p.ChatList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CopilotStreamResponse struct {
	RoleType         string              `thrift:"roleType,1,required" frugal:"1,required,string" json:"roleType"`
	State            StreamResponseState `thrift:"state,2,required" frugal:"2,required,StreamResponseState" json:"state"`
	Content          string              `thrift:"content,3,required" frugal:"3,required,string" json:"content"`
	ReasoningContent string              `thrift:"reasoningContent,4,required" frugal:"4,required,string" json:"reasoningContent"`
	MessageID        string              `thrift:"messageID,5,required" frugal:"5,required,string" json:"messageID"`
	AgentName        AgentNameEnum       `thrift:"agentName,6,required" frugal:"6,required,AgentNameEnum" json:"agentName"`
	Tools            []*FunctionCall     `thrift:"tools,7,optional" frugal:"7,optional,list<FunctionCall>" json:"tools,omitempty"`
}

func NewCopilotStreamResponse() *CopilotStreamResponse {
	return &CopilotStreamResponse{}
}

func (p *CopilotStreamResponse) InitDefault() {
}

func (p *CopilotStreamResponse) GetRoleType() (v string) {
	return p.RoleType
}

func (p *CopilotStreamResponse) GetState() (v StreamResponseState) {
	return p.State
}

func (p *CopilotStreamResponse) GetContent() (v string) {
	return p.Content
}

func (p *CopilotStreamResponse) GetReasoningContent() (v string) {
	return p.ReasoningContent
}

func (p *CopilotStreamResponse) GetMessageID() (v string) {
	return p.MessageID
}

func (p *CopilotStreamResponse) GetAgentName() (v AgentNameEnum) {
	return p.AgentName
}

var CopilotStreamResponse_Tools_DEFAULT []*FunctionCall

func (p *CopilotStreamResponse) GetTools() (v []*FunctionCall) {
	if !p.IsSetTools() {
		return CopilotStreamResponse_Tools_DEFAULT
	}
	return p.Tools
}
func (p *CopilotStreamResponse) SetRoleType(val string) {
	p.RoleType = val
}
func (p *CopilotStreamResponse) SetState(val StreamResponseState) {
	p.State = val
}
func (p *CopilotStreamResponse) SetContent(val string) {
	p.Content = val
}
func (p *CopilotStreamResponse) SetReasoningContent(val string) {
	p.ReasoningContent = val
}
func (p *CopilotStreamResponse) SetMessageID(val string) {
	p.MessageID = val
}
func (p *CopilotStreamResponse) SetAgentName(val AgentNameEnum) {
	p.AgentName = val
}
func (p *CopilotStreamResponse) SetTools(val []*FunctionCall) {
	p.Tools = val
}

var fieldIDToName_CopilotStreamResponse = map[int16]string{
	1: "roleType",
	2: "state",
	3: "content",
	4: "reasoningContent",
	5: "messageID",
	6: "agentName",
	7: "tools",
}

func (p *CopilotStreamResponse) IsSetTools() bool {
	return p.Tools != nil
}

func (p *CopilotStreamResponse) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotStreamResponse")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRoleType bool = false
	var issetState bool = false
	var issetContent bool = false
	var issetReasoningContent bool = false
	var issetMessageID bool = false
	var issetAgentName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRoleType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetReasoningContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgentName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRoleType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetState {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetReasoningContent {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetMessageID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAgentName {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotStreamResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotStreamResponse[fieldId]))
}

func (p *CopilotStreamResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RoleType = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field StreamResponseState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StreamResponseState(v)
	}
	p.State = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReasoningContent = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField6(iprot thrift.TProtocol) error {

	var _field AgentNameEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AgentNameEnum(v)
	}
	p.AgentName = _field
	return nil
}
func (p *CopilotStreamResponse) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FunctionCall, 0, size)
	values := make([]FunctionCall, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tools = _field
	return nil
}

func (p *CopilotStreamResponse) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotStreamResponse")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotStreamResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("roleType", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RoleType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("state", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.State)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reasoningContent", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReasoningContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("messageID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("agentName", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AgentName)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CopilotStreamResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTools() {
		if err = oprot.WriteFieldBegin("tools", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tools)); err != nil {
			return err
		}
		for _, v := range p.Tools {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CopilotStreamResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotStreamResponse(%+v)", *p)

}

func (p *CopilotStreamResponse) DeepEqual(ano *CopilotStreamResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RoleType) {
		return false
	}
	if !p.Field2DeepEqual(ano.State) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.ReasoningContent) {
		return false
	}
	if !p.Field5DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field6DeepEqual(ano.AgentName) {
		return false
	}
	if !p.Field7DeepEqual(ano.Tools) {
		return false
	}
	return true
}

func (p *CopilotStreamResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RoleType, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field2DeepEqual(src StreamResponseState) bool {

	if p.State != src {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ReasoningContent, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field5DeepEqual(src string) bool {

	if strings.Compare(p.MessageID, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field6DeepEqual(src AgentNameEnum) bool {

	if p.AgentName != src {
		return false
	}
	return true
}
func (p *CopilotStreamResponse) Field7DeepEqual(src []*FunctionCall) bool {

	if len(p.Tools) != len(src) {
		return false
	}
	for i, v := range p.Tools {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type FunctionCall struct {
	Name       string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Arguments  string `thrift:"Arguments,2,required" frugal:"2,required,string" json:"Arguments"`
	ToolCallID string `thrift:"ToolCallID,3,required" frugal:"3,required,string" json:"ToolCallID"`
}

func NewFunctionCall() *FunctionCall {
	return &FunctionCall{}
}

func (p *FunctionCall) InitDefault() {
}

func (p *FunctionCall) GetName() (v string) {
	return p.Name
}

func (p *FunctionCall) GetArguments() (v string) {
	return p.Arguments
}

func (p *FunctionCall) GetToolCallID() (v string) {
	return p.ToolCallID
}
func (p *FunctionCall) SetName(val string) {
	p.Name = val
}
func (p *FunctionCall) SetArguments(val string) {
	p.Arguments = val
}
func (p *FunctionCall) SetToolCallID(val string) {
	p.ToolCallID = val
}

var fieldIDToName_FunctionCall = map[int16]string{
	1: "Name",
	2: "Arguments",
	3: "ToolCallID",
}

func (p *FunctionCall) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FunctionCall")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetArguments bool = false
	var issetToolCallID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetArguments = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetToolCallID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetArguments {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetToolCallID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FunctionCall[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_FunctionCall[fieldId]))
}

func (p *FunctionCall) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *FunctionCall) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Arguments = _field
	return nil
}
func (p *FunctionCall) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ToolCallID = _field
	return nil
}

func (p *FunctionCall) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FunctionCall")

	var fieldId int16
	if err = oprot.WriteStructBegin("FunctionCall"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FunctionCall) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FunctionCall) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Arguments", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Arguments); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FunctionCall) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ToolCallID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ToolCallID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *FunctionCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FunctionCall(%+v)", *p)

}

func (p *FunctionCall) DeepEqual(ano *FunctionCall) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Arguments) {
		return false
	}
	if !p.Field3DeepEqual(ano.ToolCallID) {
		return false
	}
	return true
}

func (p *FunctionCall) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *FunctionCall) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Arguments, src) != 0 {
		return false
	}
	return true
}
func (p *FunctionCall) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ToolCallID, src) != 0 {
		return false
	}
	return true
}

type ExecuteAssistantPromptReq struct {
	ChatId   string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Message  string `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
	IsStream bool   `thrift:"IsStream,3,required" frugal:"3,required,bool" json:"IsStream"`
}

func NewExecuteAssistantPromptReq() *ExecuteAssistantPromptReq {
	return &ExecuteAssistantPromptReq{}
}

func (p *ExecuteAssistantPromptReq) InitDefault() {
}

func (p *ExecuteAssistantPromptReq) GetChatId() (v string) {
	return p.ChatId
}

func (p *ExecuteAssistantPromptReq) GetMessage() (v string) {
	return p.Message
}

func (p *ExecuteAssistantPromptReq) GetIsStream() (v bool) {
	return p.IsStream
}
func (p *ExecuteAssistantPromptReq) SetChatId(val string) {
	p.ChatId = val
}
func (p *ExecuteAssistantPromptReq) SetMessage(val string) {
	p.Message = val
}
func (p *ExecuteAssistantPromptReq) SetIsStream(val bool) {
	p.IsStream = val
}

var fieldIDToName_ExecuteAssistantPromptReq = map[int16]string{
	1: "ChatId",
	2: "Message",
	3: "IsStream",
}

func (p *ExecuteAssistantPromptReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteAssistantPromptReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetMessage bool = false
	var issetIsStream bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsStream = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIsStream {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteAssistantPromptReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteAssistantPromptReq[fieldId]))
}

func (p *ExecuteAssistantPromptReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *ExecuteAssistantPromptReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *ExecuteAssistantPromptReq) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsStream = _field
	return nil
}

func (p *ExecuteAssistantPromptReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteAssistantPromptReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteAssistantPromptReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteAssistantPromptReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteAssistantPromptReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecuteAssistantPromptReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsStream", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsStream); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExecuteAssistantPromptReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteAssistantPromptReq(%+v)", *p)

}

func (p *ExecuteAssistantPromptReq) DeepEqual(ano *ExecuteAssistantPromptReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsStream) {
		return false
	}
	return true
}

func (p *ExecuteAssistantPromptReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteAssistantPromptReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteAssistantPromptReq) Field3DeepEqual(src bool) bool {

	if p.IsStream != src {
		return false
	}
	return true
}

type ExecuteAssistantPromptResp struct {
}

func NewExecuteAssistantPromptResp() *ExecuteAssistantPromptResp {
	return &ExecuteAssistantPromptResp{}
}

func (p *ExecuteAssistantPromptResp) InitDefault() {
}

var fieldIDToName_ExecuteAssistantPromptResp = map[int16]string{}

func (p *ExecuteAssistantPromptResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteAssistantPromptResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteAssistantPromptResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteAssistantPromptResp")

	if err = oprot.WriteStructBegin("ExecuteAssistantPromptResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteAssistantPromptResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteAssistantPromptResp(%+v)", *p)

}

func (p *ExecuteAssistantPromptResp) DeepEqual(ano *ExecuteAssistantPromptResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CopilotAssistantResponse struct {
	ChatId      string            `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Content     string            `thrift:"content,2,required" frugal:"2,required,string" json:"content"`
	RelatedDocs []*RelatedDocInfo `thrift:"RelatedDocs,3,optional" frugal:"3,optional,list<RelatedDocInfo>" json:"RelatedDocs,omitempty"`
}

func NewCopilotAssistantResponse() *CopilotAssistantResponse {
	return &CopilotAssistantResponse{}
}

func (p *CopilotAssistantResponse) InitDefault() {
}

func (p *CopilotAssistantResponse) GetChatId() (v string) {
	return p.ChatId
}

func (p *CopilotAssistantResponse) GetContent() (v string) {
	return p.Content
}

var CopilotAssistantResponse_RelatedDocs_DEFAULT []*RelatedDocInfo

func (p *CopilotAssistantResponse) GetRelatedDocs() (v []*RelatedDocInfo) {
	if !p.IsSetRelatedDocs() {
		return CopilotAssistantResponse_RelatedDocs_DEFAULT
	}
	return p.RelatedDocs
}
func (p *CopilotAssistantResponse) SetChatId(val string) {
	p.ChatId = val
}
func (p *CopilotAssistantResponse) SetContent(val string) {
	p.Content = val
}
func (p *CopilotAssistantResponse) SetRelatedDocs(val []*RelatedDocInfo) {
	p.RelatedDocs = val
}

var fieldIDToName_CopilotAssistantResponse = map[int16]string{
	1: "ChatId",
	2: "content",
	3: "RelatedDocs",
}

func (p *CopilotAssistantResponse) IsSetRelatedDocs() bool {
	return p.RelatedDocs != nil
}

func (p *CopilotAssistantResponse) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotAssistantResponse")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CopilotAssistantResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CopilotAssistantResponse[fieldId]))
}

func (p *CopilotAssistantResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *CopilotAssistantResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *CopilotAssistantResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RelatedDocInfo, 0, size)
	values := make([]RelatedDocInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RelatedDocs = _field
	return nil
}

func (p *CopilotAssistantResponse) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CopilotAssistantResponse")

	var fieldId int16
	if err = oprot.WriteStructBegin("CopilotAssistantResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CopilotAssistantResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CopilotAssistantResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CopilotAssistantResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelatedDocs() {
		if err = oprot.WriteFieldBegin("RelatedDocs", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelatedDocs)); err != nil {
			return err
		}
		for _, v := range p.RelatedDocs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CopilotAssistantResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopilotAssistantResponse(%+v)", *p)

}

func (p *CopilotAssistantResponse) DeepEqual(ano *CopilotAssistantResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.RelatedDocs) {
		return false
	}
	return true
}

func (p *CopilotAssistantResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotAssistantResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *CopilotAssistantResponse) Field3DeepEqual(src []*RelatedDocInfo) bool {

	if len(p.RelatedDocs) != len(src) {
		return false
	}
	for i, v := range p.RelatedDocs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RelatedDocInfo struct {
	Name string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Url  string `thrift:"url,2,required" frugal:"2,required,string" json:"url"`
}

func NewRelatedDocInfo() *RelatedDocInfo {
	return &RelatedDocInfo{}
}

func (p *RelatedDocInfo) InitDefault() {
}

func (p *RelatedDocInfo) GetName() (v string) {
	return p.Name
}

func (p *RelatedDocInfo) GetUrl() (v string) {
	return p.Url
}
func (p *RelatedDocInfo) SetName(val string) {
	p.Name = val
}
func (p *RelatedDocInfo) SetUrl(val string) {
	p.Url = val
}

var fieldIDToName_RelatedDocInfo = map[int16]string{
	1: "Name",
	2: "url",
}

func (p *RelatedDocInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RelatedDocInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetUrl bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUrl {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RelatedDocInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RelatedDocInfo[fieldId]))
}

func (p *RelatedDocInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *RelatedDocInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Url = _field
	return nil
}

func (p *RelatedDocInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RelatedDocInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("RelatedDocInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RelatedDocInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RelatedDocInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Url); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RelatedDocInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RelatedDocInfo(%+v)", *p)

}

func (p *RelatedDocInfo) DeepEqual(ano *RelatedDocInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Url) {
		return false
	}
	return true
}

func (p *RelatedDocInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *RelatedDocInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Url, src) != 0 {
		return false
	}
	return true
}

type RateModelReplyReq struct {
	MessageId string             `thrift:"MessageId,1,required" frugal:"1,required,string" json:"MessageId"`
	Reply     RateModelReplyEnum `thrift:"reply,2,required" frugal:"2,required,RateModelReplyEnum" json:"reply"`
}

func NewRateModelReplyReq() *RateModelReplyReq {
	return &RateModelReplyReq{}
}

func (p *RateModelReplyReq) InitDefault() {
}

func (p *RateModelReplyReq) GetMessageId() (v string) {
	return p.MessageId
}

func (p *RateModelReplyReq) GetReply() (v RateModelReplyEnum) {
	return p.Reply
}
func (p *RateModelReplyReq) SetMessageId(val string) {
	p.MessageId = val
}
func (p *RateModelReplyReq) SetReply(val RateModelReplyEnum) {
	p.Reply = val
}

var fieldIDToName_RateModelReplyReq = map[int16]string{
	1: "MessageId",
	2: "reply",
}

func (p *RateModelReplyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RateModelReplyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageId bool = false
	var issetReply bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetReply = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetReply {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RateModelReplyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RateModelReplyReq[fieldId]))
}

func (p *RateModelReplyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}
func (p *RateModelReplyReq) ReadField2(iprot thrift.TProtocol) error {

	var _field RateModelReplyEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RateModelReplyEnum(v)
	}
	p.Reply = _field
	return nil
}

func (p *RateModelReplyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RateModelReplyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RateModelReplyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RateModelReplyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RateModelReplyReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reply", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Reply)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RateModelReplyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RateModelReplyReq(%+v)", *p)

}

func (p *RateModelReplyReq) DeepEqual(ano *RateModelReplyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Reply) {
		return false
	}
	return true
}

func (p *RateModelReplyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}
func (p *RateModelReplyReq) Field2DeepEqual(src RateModelReplyEnum) bool {

	if p.Reply != src {
		return false
	}
	return true
}

type RateModelReplyResp struct {
}

func NewRateModelReplyResp() *RateModelReplyResp {
	return &RateModelReplyResp{}
}

func (p *RateModelReplyResp) InitDefault() {
}

var fieldIDToName_RateModelReplyResp = map[int16]string{}

func (p *RateModelReplyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RateModelReplyResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RateModelReplyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RateModelReplyResp")

	if err = oprot.WriteStructBegin("RateModelReplyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RateModelReplyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RateModelReplyResp(%+v)", *p)

}

func (p *RateModelReplyResp) DeepEqual(ano *RateModelReplyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateOrderContentFromMessagesReq struct {
	ChatId    string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	MessageId string `thrift:"MessageId,2,required" frugal:"2,required,string" json:"MessageId"`
}

func NewCreateOrderContentFromMessagesReq() *CreateOrderContentFromMessagesReq {
	return &CreateOrderContentFromMessagesReq{}
}

func (p *CreateOrderContentFromMessagesReq) InitDefault() {
}

func (p *CreateOrderContentFromMessagesReq) GetChatId() (v string) {
	return p.ChatId
}

func (p *CreateOrderContentFromMessagesReq) GetMessageId() (v string) {
	return p.MessageId
}
func (p *CreateOrderContentFromMessagesReq) SetChatId(val string) {
	p.ChatId = val
}
func (p *CreateOrderContentFromMessagesReq) SetMessageId(val string) {
	p.MessageId = val
}

var fieldIDToName_CreateOrderContentFromMessagesReq = map[int16]string{
	1: "ChatId",
	2: "MessageId",
}

func (p *CreateOrderContentFromMessagesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateOrderContentFromMessagesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetMessageId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessageId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderContentFromMessagesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateOrderContentFromMessagesReq[fieldId]))
}

func (p *CreateOrderContentFromMessagesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *CreateOrderContentFromMessagesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}

func (p *CreateOrderContentFromMessagesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateOrderContentFromMessagesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrderContentFromMessagesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrderContentFromMessagesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateOrderContentFromMessagesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateOrderContentFromMessagesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrderContentFromMessagesReq(%+v)", *p)

}

func (p *CreateOrderContentFromMessagesReq) DeepEqual(ano *CreateOrderContentFromMessagesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageId) {
		return false
	}
	return true
}

func (p *CreateOrderContentFromMessagesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateOrderContentFromMessagesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}

type CreateOrderContentFromMessagesResp struct {
	Content string `thrift:"Content,1,required" frugal:"1,required,string" json:"Content"`
}

func NewCreateOrderContentFromMessagesResp() *CreateOrderContentFromMessagesResp {
	return &CreateOrderContentFromMessagesResp{}
}

func (p *CreateOrderContentFromMessagesResp) InitDefault() {
}

func (p *CreateOrderContentFromMessagesResp) GetContent() (v string) {
	return p.Content
}
func (p *CreateOrderContentFromMessagesResp) SetContent(val string) {
	p.Content = val
}

var fieldIDToName_CreateOrderContentFromMessagesResp = map[int16]string{
	1: "Content",
}

func (p *CreateOrderContentFromMessagesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateOrderContentFromMessagesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetContent {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderContentFromMessagesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateOrderContentFromMessagesResp[fieldId]))
}

func (p *CreateOrderContentFromMessagesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}

func (p *CreateOrderContentFromMessagesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateOrderContentFromMessagesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrderContentFromMessagesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrderContentFromMessagesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateOrderContentFromMessagesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrderContentFromMessagesResp(%+v)", *p)

}

func (p *CreateOrderContentFromMessagesResp) DeepEqual(ano *CreateOrderContentFromMessagesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	return true
}

func (p *CreateOrderContentFromMessagesResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SQLScope int64

const (
	SQLScope_Instance SQLScope = 1
	SQLScope_DB       SQLScope = 2
)

func (p SQLScope) String() string {
	switch p {
	case SQLScope_Instance:
		return "Instance"
	case SQLScope_DB:
		return "DB"
	}
	return "<UNSET>"
}

func SQLScopeFromString(s string) (SQLScope, error) {
	switch s {
	case "Instance":
		return SQLScope_Instance, nil
	case "DB":
		return SQLScope_DB, nil
	}
	return SQLScope(0), fmt.Errorf("not a valid SQLScope string")
}

func SQLScopePtr(v SQLScope) *SQLScope { return &v }

func (p SQLScope) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SQLScope) UnmarshalText(text []byte) error {
	q, err := SQLScopeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AddMyFavouriteSQLReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	Scope        SQLScope     `thrift:"Scope,3,required" frugal:"3,required,SQLScope" json:"Scope"`
	Title        string       `thrift:"Title,4,required" frugal:"4,required,string" json:"Title"`
	SQLText      string       `thrift:"SQLText,5,required" frugal:"5,required,string" json:"SQLText"`
	DBName       *string      `thrift:"DBName,6,optional" frugal:"6,optional,string" json:"DBName,omitempty"`
}

func NewAddMyFavouriteSQLReq() *AddMyFavouriteSQLReq {
	return &AddMyFavouriteSQLReq{}
}

func (p *AddMyFavouriteSQLReq) InitDefault() {
}

func (p *AddMyFavouriteSQLReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *AddMyFavouriteSQLReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AddMyFavouriteSQLReq) GetScope() (v SQLScope) {
	return p.Scope
}

func (p *AddMyFavouriteSQLReq) GetTitle() (v string) {
	return p.Title
}

func (p *AddMyFavouriteSQLReq) GetSQLText() (v string) {
	return p.SQLText
}

var AddMyFavouriteSQLReq_DBName_DEFAULT string

func (p *AddMyFavouriteSQLReq) GetDBName() (v string) {
	if !p.IsSetDBName() {
		return AddMyFavouriteSQLReq_DBName_DEFAULT
	}
	return *p.DBName
}
func (p *AddMyFavouriteSQLReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *AddMyFavouriteSQLReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AddMyFavouriteSQLReq) SetScope(val SQLScope) {
	p.Scope = val
}
func (p *AddMyFavouriteSQLReq) SetTitle(val string) {
	p.Title = val
}
func (p *AddMyFavouriteSQLReq) SetSQLText(val string) {
	p.SQLText = val
}
func (p *AddMyFavouriteSQLReq) SetDBName(val *string) {
	p.DBName = val
}

var fieldIDToName_AddMyFavouriteSQLReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Scope",
	4: "Title",
	5: "SQLText",
	6: "DBName",
}

func (p *AddMyFavouriteSQLReq) IsSetDBName() bool {
	return p.DBName != nil
}

func (p *AddMyFavouriteSQLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddMyFavouriteSQLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetScope bool = false
	var issetTitle bool = false
	var issetSQLText bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetScope = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTitle = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetScope {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTitle {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSQLText {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddMyFavouriteSQLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AddMyFavouriteSQLReq[fieldId]))
}

func (p *AddMyFavouriteSQLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *AddMyFavouriteSQLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AddMyFavouriteSQLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field SQLScope
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SQLScope(v)
	}
	p.Scope = _field
	return nil
}
func (p *AddMyFavouriteSQLReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}
func (p *AddMyFavouriteSQLReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLText = _field
	return nil
}
func (p *AddMyFavouriteSQLReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBName = _field
	return nil
}

func (p *AddMyFavouriteSQLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddMyFavouriteSQLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AddMyFavouriteSQLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Scope", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Scope)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Title", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLText", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQLText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBName() {
		if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AddMyFavouriteSQLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddMyFavouriteSQLReq(%+v)", *p)

}

func (p *AddMyFavouriteSQLReq) DeepEqual(ano *AddMyFavouriteSQLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Scope) {
		return false
	}
	if !p.Field4DeepEqual(ano.Title) {
		return false
	}
	if !p.Field5DeepEqual(ano.SQLText) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBName) {
		return false
	}
	return true
}

func (p *AddMyFavouriteSQLReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *AddMyFavouriteSQLReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AddMyFavouriteSQLReq) Field3DeepEqual(src SQLScope) bool {

	if p.Scope != src {
		return false
	}
	return true
}
func (p *AddMyFavouriteSQLReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Title, src) != 0 {
		return false
	}
	return true
}
func (p *AddMyFavouriteSQLReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SQLText, src) != 0 {
		return false
	}
	return true
}
func (p *AddMyFavouriteSQLReq) Field6DeepEqual(src *string) bool {

	if p.DBName == src {
		return true
	} else if p.DBName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBName, *src) != 0 {
		return false
	}
	return true
}

type AddMyFavouriteSQLResp struct {
}

func NewAddMyFavouriteSQLResp() *AddMyFavouriteSQLResp {
	return &AddMyFavouriteSQLResp{}
}

func (p *AddMyFavouriteSQLResp) InitDefault() {
}

var fieldIDToName_AddMyFavouriteSQLResp = map[int16]string{}

func (p *AddMyFavouriteSQLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddMyFavouriteSQLResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AddMyFavouriteSQLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddMyFavouriteSQLResp")

	if err = oprot.WriteStructBegin("AddMyFavouriteSQLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddMyFavouriteSQLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddMyFavouriteSQLResp(%+v)", *p)

}

func (p *AddMyFavouriteSQLResp) DeepEqual(ano *AddMyFavouriteSQLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeMyFavouriteSQLReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DBName       string       `thrift:"DBName,3,required" frugal:"3,required,string" json:"DBName"`
}

func NewDescribeMyFavouriteSQLReq() *DescribeMyFavouriteSQLReq {
	return &DescribeMyFavouriteSQLReq{}
}

func (p *DescribeMyFavouriteSQLReq) InitDefault() {
}

func (p *DescribeMyFavouriteSQLReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeMyFavouriteSQLReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeMyFavouriteSQLReq) GetDBName() (v string) {
	return p.DBName
}
func (p *DescribeMyFavouriteSQLReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeMyFavouriteSQLReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeMyFavouriteSQLReq) SetDBName(val string) {
	p.DBName = val
}

var fieldIDToName_DescribeMyFavouriteSQLReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "DBName",
}

func (p *DescribeMyFavouriteSQLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMyFavouriteSQLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetDBName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMyFavouriteSQLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMyFavouriteSQLReq[fieldId]))
}

func (p *DescribeMyFavouriteSQLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeMyFavouriteSQLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeMyFavouriteSQLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}

func (p *DescribeMyFavouriteSQLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMyFavouriteSQLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMyFavouriteSQLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMyFavouriteSQLReq(%+v)", *p)

}

func (p *DescribeMyFavouriteSQLReq) DeepEqual(ano *DescribeMyFavouriteSQLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBName) {
		return false
	}
	return true
}

func (p *DescribeMyFavouriteSQLReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeMyFavouriteSQLReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeMyFavouriteSQLReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}

type DescribeMyFavouriteSQLResp struct {
	SQLs []*FavouriteSQL `thrift:"SQLs,1,required" frugal:"1,required,list<FavouriteSQL>" json:"SQLs"`
}

func NewDescribeMyFavouriteSQLResp() *DescribeMyFavouriteSQLResp {
	return &DescribeMyFavouriteSQLResp{}
}

func (p *DescribeMyFavouriteSQLResp) InitDefault() {
}

func (p *DescribeMyFavouriteSQLResp) GetSQLs() (v []*FavouriteSQL) {
	return p.SQLs
}
func (p *DescribeMyFavouriteSQLResp) SetSQLs(val []*FavouriteSQL) {
	p.SQLs = val
}

var fieldIDToName_DescribeMyFavouriteSQLResp = map[int16]string{
	1: "SQLs",
}

func (p *DescribeMyFavouriteSQLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMyFavouriteSQLResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMyFavouriteSQLResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMyFavouriteSQLResp[fieldId]))
}

func (p *DescribeMyFavouriteSQLResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FavouriteSQL, 0, size)
	values := make([]FavouriteSQL, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLs = _field
	return nil
}

func (p *DescribeMyFavouriteSQLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMyFavouriteSQLResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMyFavouriteSQLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SQLs)); err != nil {
		return err
	}
	for _, v := range p.SQLs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMyFavouriteSQLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMyFavouriteSQLResp(%+v)", *p)

}

func (p *DescribeMyFavouriteSQLResp) DeepEqual(ano *DescribeMyFavouriteSQLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLs) {
		return false
	}
	return true
}

func (p *DescribeMyFavouriteSQLResp) Field1DeepEqual(src []*FavouriteSQL) bool {

	if len(p.SQLs) != len(src) {
		return false
	}
	for i, v := range p.SQLs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type FavouriteSQL struct {
	ID         string   `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	Title      string   `thrift:"Title,2,required" frugal:"2,required,string" json:"Title"`
	SQLText    string   `thrift:"SQLText,3,required" frugal:"3,required,string" json:"SQLText"`
	Scope      SQLScope `thrift:"Scope,4,required" frugal:"4,required,SQLScope" json:"Scope"`
	CreateTime string   `thrift:"CreateTime,5,required" frugal:"5,required,string" json:"CreateTime"`
	ModifyTime string   `thrift:"ModifyTime,6,required" frugal:"6,required,string" json:"ModifyTime"`
	UseTimes   int32    `thrift:"UseTimes,7,required" frugal:"7,required,i32" json:"UseTimes"`
}

func NewFavouriteSQL() *FavouriteSQL {
	return &FavouriteSQL{}
}

func (p *FavouriteSQL) InitDefault() {
}

func (p *FavouriteSQL) GetID() (v string) {
	return p.ID
}

func (p *FavouriteSQL) GetTitle() (v string) {
	return p.Title
}

func (p *FavouriteSQL) GetSQLText() (v string) {
	return p.SQLText
}

func (p *FavouriteSQL) GetScope() (v SQLScope) {
	return p.Scope
}

func (p *FavouriteSQL) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *FavouriteSQL) GetModifyTime() (v string) {
	return p.ModifyTime
}

func (p *FavouriteSQL) GetUseTimes() (v int32) {
	return p.UseTimes
}
func (p *FavouriteSQL) SetID(val string) {
	p.ID = val
}
func (p *FavouriteSQL) SetTitle(val string) {
	p.Title = val
}
func (p *FavouriteSQL) SetSQLText(val string) {
	p.SQLText = val
}
func (p *FavouriteSQL) SetScope(val SQLScope) {
	p.Scope = val
}
func (p *FavouriteSQL) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *FavouriteSQL) SetModifyTime(val string) {
	p.ModifyTime = val
}
func (p *FavouriteSQL) SetUseTimes(val int32) {
	p.UseTimes = val
}

var fieldIDToName_FavouriteSQL = map[int16]string{
	1: "ID",
	2: "Title",
	3: "SQLText",
	4: "Scope",
	5: "CreateTime",
	6: "ModifyTime",
	7: "UseTimes",
}

func (p *FavouriteSQL) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FavouriteSQL")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetTitle bool = false
	var issetSQLText bool = false
	var issetScope bool = false
	var issetCreateTime bool = false
	var issetModifyTime bool = false
	var issetUseTimes bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTitle = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetScope = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetModifyTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetUseTimes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTitle {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSQLText {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetScope {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetModifyTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUseTimes {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FavouriteSQL[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_FavouriteSQL[fieldId]))
}

func (p *FavouriteSQL) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *FavouriteSQL) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}
func (p *FavouriteSQL) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLText = _field
	return nil
}
func (p *FavouriteSQL) ReadField4(iprot thrift.TProtocol) error {

	var _field SQLScope
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SQLScope(v)
	}
	p.Scope = _field
	return nil
}
func (p *FavouriteSQL) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *FavouriteSQL) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ModifyTime = _field
	return nil
}
func (p *FavouriteSQL) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UseTimes = _field
	return nil
}

func (p *FavouriteSQL) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FavouriteSQL")

	var fieldId int16
	if err = oprot.WriteStructBegin("FavouriteSQL"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FavouriteSQL) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FavouriteSQL) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Title", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FavouriteSQL) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLText", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQLText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *FavouriteSQL) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Scope", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Scope)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *FavouriteSQL) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *FavouriteSQL) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ModifyTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ModifyTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *FavouriteSQL) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UseTimes", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.UseTimes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *FavouriteSQL) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FavouriteSQL(%+v)", *p)

}

func (p *FavouriteSQL) DeepEqual(ano *FavouriteSQL) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Title) {
		return false
	}
	if !p.Field3DeepEqual(ano.SQLText) {
		return false
	}
	if !p.Field4DeepEqual(ano.Scope) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.ModifyTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.UseTimes) {
		return false
	}
	return true
}

func (p *FavouriteSQL) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Title, src) != 0 {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SQLText, src) != 0 {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field4DeepEqual(src SQLScope) bool {

	if p.Scope != src {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field6DeepEqual(src string) bool {

	if strings.Compare(p.ModifyTime, src) != 0 {
		return false
	}
	return true
}
func (p *FavouriteSQL) Field7DeepEqual(src int32) bool {

	if p.UseTimes != src {
		return false
	}
	return true
}

type UpdateMyFavouriteSQLReq struct {
	ID      string    `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	Scope   *SQLScope `thrift:"Scope,2,optional" frugal:"2,optional,SQLScope" json:"Scope,omitempty"`
	SQLText *string   `thrift:"SQLText,3,optional" frugal:"3,optional,string" json:"SQLText,omitempty"`
	Title   *string   `thrift:"Title,4,optional" frugal:"4,optional,string" json:"Title,omitempty"`
}

func NewUpdateMyFavouriteSQLReq() *UpdateMyFavouriteSQLReq {
	return &UpdateMyFavouriteSQLReq{}
}

func (p *UpdateMyFavouriteSQLReq) InitDefault() {
}

func (p *UpdateMyFavouriteSQLReq) GetID() (v string) {
	return p.ID
}

var UpdateMyFavouriteSQLReq_Scope_DEFAULT SQLScope

func (p *UpdateMyFavouriteSQLReq) GetScope() (v SQLScope) {
	if !p.IsSetScope() {
		return UpdateMyFavouriteSQLReq_Scope_DEFAULT
	}
	return *p.Scope
}

var UpdateMyFavouriteSQLReq_SQLText_DEFAULT string

func (p *UpdateMyFavouriteSQLReq) GetSQLText() (v string) {
	if !p.IsSetSQLText() {
		return UpdateMyFavouriteSQLReq_SQLText_DEFAULT
	}
	return *p.SQLText
}

var UpdateMyFavouriteSQLReq_Title_DEFAULT string

func (p *UpdateMyFavouriteSQLReq) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return UpdateMyFavouriteSQLReq_Title_DEFAULT
	}
	return *p.Title
}
func (p *UpdateMyFavouriteSQLReq) SetID(val string) {
	p.ID = val
}
func (p *UpdateMyFavouriteSQLReq) SetScope(val *SQLScope) {
	p.Scope = val
}
func (p *UpdateMyFavouriteSQLReq) SetSQLText(val *string) {
	p.SQLText = val
}
func (p *UpdateMyFavouriteSQLReq) SetTitle(val *string) {
	p.Title = val
}

var fieldIDToName_UpdateMyFavouriteSQLReq = map[int16]string{
	1: "ID",
	2: "Scope",
	3: "SQLText",
	4: "Title",
}

func (p *UpdateMyFavouriteSQLReq) IsSetScope() bool {
	return p.Scope != nil
}

func (p *UpdateMyFavouriteSQLReq) IsSetSQLText() bool {
	return p.SQLText != nil
}

func (p *UpdateMyFavouriteSQLReq) IsSetTitle() bool {
	return p.Title != nil
}

func (p *UpdateMyFavouriteSQLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMyFavouriteSQLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateMyFavouriteSQLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateMyFavouriteSQLReq[fieldId]))
}

func (p *UpdateMyFavouriteSQLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *UpdateMyFavouriteSQLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *SQLScope
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SQLScope(v)
		_field = &tmp
	}
	p.Scope = _field
	return nil
}
func (p *UpdateMyFavouriteSQLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SQLText = _field
	return nil
}
func (p *UpdateMyFavouriteSQLReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Title = _field
	return nil
}

func (p *UpdateMyFavouriteSQLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMyFavouriteSQLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateMyFavouriteSQLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetScope() {
		if err = oprot.WriteFieldBegin("Scope", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Scope)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSQLText() {
		if err = oprot.WriteFieldBegin("SQLText", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SQLText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err = oprot.WriteFieldBegin("Title", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Title); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMyFavouriteSQLReq(%+v)", *p)

}

func (p *UpdateMyFavouriteSQLReq) DeepEqual(ano *UpdateMyFavouriteSQLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Scope) {
		return false
	}
	if !p.Field3DeepEqual(ano.SQLText) {
		return false
	}
	if !p.Field4DeepEqual(ano.Title) {
		return false
	}
	return true
}

func (p *UpdateMyFavouriteSQLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateMyFavouriteSQLReq) Field2DeepEqual(src *SQLScope) bool {

	if p.Scope == src {
		return true
	} else if p.Scope == nil || src == nil {
		return false
	}
	if *p.Scope != *src {
		return false
	}
	return true
}
func (p *UpdateMyFavouriteSQLReq) Field3DeepEqual(src *string) bool {

	if p.SQLText == src {
		return true
	} else if p.SQLText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SQLText, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateMyFavouriteSQLReq) Field4DeepEqual(src *string) bool {

	if p.Title == src {
		return true
	} else if p.Title == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Title, *src) != 0 {
		return false
	}
	return true
}

type UpdateMyFavouriteSQLResp struct {
}

func NewUpdateMyFavouriteSQLResp() *UpdateMyFavouriteSQLResp {
	return &UpdateMyFavouriteSQLResp{}
}

func (p *UpdateMyFavouriteSQLResp) InitDefault() {
}

var fieldIDToName_UpdateMyFavouriteSQLResp = map[int16]string{}

func (p *UpdateMyFavouriteSQLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMyFavouriteSQLResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMyFavouriteSQLResp")

	if err = oprot.WriteStructBegin("UpdateMyFavouriteSQLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateMyFavouriteSQLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMyFavouriteSQLResp(%+v)", *p)

}

func (p *UpdateMyFavouriteSQLResp) DeepEqual(ano *UpdateMyFavouriteSQLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteMyFavouriteSQLReq struct {
	ID string `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
}

func NewDeleteMyFavouriteSQLReq() *DeleteMyFavouriteSQLReq {
	return &DeleteMyFavouriteSQLReq{}
}

func (p *DeleteMyFavouriteSQLReq) InitDefault() {
}

func (p *DeleteMyFavouriteSQLReq) GetID() (v string) {
	return p.ID
}
func (p *DeleteMyFavouriteSQLReq) SetID(val string) {
	p.ID = val
}

var fieldIDToName_DeleteMyFavouriteSQLReq = map[int16]string{
	1: "ID",
}

func (p *DeleteMyFavouriteSQLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteMyFavouriteSQLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteMyFavouriteSQLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteMyFavouriteSQLReq[fieldId]))
}

func (p *DeleteMyFavouriteSQLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}

func (p *DeleteMyFavouriteSQLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteMyFavouriteSQLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteMyFavouriteSQLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteMyFavouriteSQLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteMyFavouriteSQLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteMyFavouriteSQLReq(%+v)", *p)

}

func (p *DeleteMyFavouriteSQLReq) DeepEqual(ano *DeleteMyFavouriteSQLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	return true
}

func (p *DeleteMyFavouriteSQLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}

type DeleteMyFavouriteSQLResp struct {
}

func NewDeleteMyFavouriteSQLResp() *DeleteMyFavouriteSQLResp {
	return &DeleteMyFavouriteSQLResp{}
}

func (p *DeleteMyFavouriteSQLResp) InitDefault() {
}

var fieldIDToName_DeleteMyFavouriteSQLResp = map[int16]string{}

func (p *DeleteMyFavouriteSQLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteMyFavouriteSQLResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteMyFavouriteSQLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteMyFavouriteSQLResp")

	if err = oprot.WriteStructBegin("DeleteMyFavouriteSQLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteMyFavouriteSQLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteMyFavouriteSQLResp(%+v)", *p)

}

func (p *DeleteMyFavouriteSQLResp) DeepEqual(ano *DeleteMyFavouriteSQLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DataConnectInstanceReq struct {
	InstanceType      string  `thrift:"InstanceType,1,required" frugal:"1,required,string" json:"InstanceType"`
	InstanceId        string  `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	Username          string  `thrift:"Username,3,required" frugal:"3,required,string" json:"Username"`
	Password          string  `thrift:"Password,4,required" frugal:"4,required,string" json:"Password"`
	DatabaseName      *string `thrift:"DatabaseName,5,optional" frugal:"5,optional,string" json:"DatabaseName,omitempty"`
	KeepAliveOverTime *int64  `thrift:"KeepAliveOverTime,6,optional" frugal:"6,optional,i64" json:"KeepAliveOverTime,omitempty"`
	MongoNodeId       *string `thrift:"MongoNodeId,7,optional" frugal:"7,optional,string" json:"MongoNodeId,omitempty"`
}

func NewDataConnectInstanceReq() *DataConnectInstanceReq {
	return &DataConnectInstanceReq{}
}

func (p *DataConnectInstanceReq) InitDefault() {
}

func (p *DataConnectInstanceReq) GetInstanceType() (v string) {
	return p.InstanceType
}

func (p *DataConnectInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DataConnectInstanceReq) GetUsername() (v string) {
	return p.Username
}

func (p *DataConnectInstanceReq) GetPassword() (v string) {
	return p.Password
}

var DataConnectInstanceReq_DatabaseName_DEFAULT string

func (p *DataConnectInstanceReq) GetDatabaseName() (v string) {
	if !p.IsSetDatabaseName() {
		return DataConnectInstanceReq_DatabaseName_DEFAULT
	}
	return *p.DatabaseName
}

var DataConnectInstanceReq_KeepAliveOverTime_DEFAULT int64

func (p *DataConnectInstanceReq) GetKeepAliveOverTime() (v int64) {
	if !p.IsSetKeepAliveOverTime() {
		return DataConnectInstanceReq_KeepAliveOverTime_DEFAULT
	}
	return *p.KeepAliveOverTime
}

var DataConnectInstanceReq_MongoNodeId_DEFAULT string

func (p *DataConnectInstanceReq) GetMongoNodeId() (v string) {
	if !p.IsSetMongoNodeId() {
		return DataConnectInstanceReq_MongoNodeId_DEFAULT
	}
	return *p.MongoNodeId
}
func (p *DataConnectInstanceReq) SetInstanceType(val string) {
	p.InstanceType = val
}
func (p *DataConnectInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DataConnectInstanceReq) SetUsername(val string) {
	p.Username = val
}
func (p *DataConnectInstanceReq) SetPassword(val string) {
	p.Password = val
}
func (p *DataConnectInstanceReq) SetDatabaseName(val *string) {
	p.DatabaseName = val
}
func (p *DataConnectInstanceReq) SetKeepAliveOverTime(val *int64) {
	p.KeepAliveOverTime = val
}
func (p *DataConnectInstanceReq) SetMongoNodeId(val *string) {
	p.MongoNodeId = val
}

var fieldIDToName_DataConnectInstanceReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Username",
	4: "Password",
	5: "DatabaseName",
	6: "KeepAliveOverTime",
	7: "MongoNodeId",
}

func (p *DataConnectInstanceReq) IsSetDatabaseName() bool {
	return p.DatabaseName != nil
}

func (p *DataConnectInstanceReq) IsSetKeepAliveOverTime() bool {
	return p.KeepAliveOverTime != nil
}

func (p *DataConnectInstanceReq) IsSetMongoNodeId() bool {
	return p.MongoNodeId != nil
}

func (p *DataConnectInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataConnectInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetUsername bool = false
	var issetPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUsername = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUsername {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPassword {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataConnectInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataConnectInstanceReq[fieldId]))
}

func (p *DataConnectInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Username = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseName = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeepAliveOverTime = _field
	return nil
}
func (p *DataConnectInstanceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MongoNodeId = _field
	return nil
}

func (p *DataConnectInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataConnectInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataConnectInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Username", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Username); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseName() {
		if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeepAliveOverTime() {
		if err = oprot.WriteFieldBegin("KeepAliveOverTime", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.KeepAliveOverTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataConnectInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMongoNodeId() {
		if err = oprot.WriteFieldBegin("MongoNodeId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MongoNodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DataConnectInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataConnectInstanceReq(%+v)", *p)

}

func (p *DataConnectInstanceReq) DeepEqual(ano *DataConnectInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Username) {
		return false
	}
	if !p.Field4DeepEqual(ano.Password) {
		return false
	}
	if !p.Field5DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field6DeepEqual(ano.KeepAliveOverTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.MongoNodeId) {
		return false
	}
	return true
}

func (p *DataConnectInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Username, src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field5DeepEqual(src *string) bool {

	if p.DatabaseName == src {
		return true
	} else if p.DatabaseName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseName, *src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field6DeepEqual(src *int64) bool {

	if p.KeepAliveOverTime == src {
		return true
	} else if p.KeepAliveOverTime == nil || src == nil {
		return false
	}
	if *p.KeepAliveOverTime != *src {
		return false
	}
	return true
}
func (p *DataConnectInstanceReq) Field7DeepEqual(src *string) bool {

	if p.MongoNodeId == src {
		return true
	} else if p.MongoNodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MongoNodeId, *src) != 0 {
		return false
	}
	return true
}

type DataConnectInstanceResp struct {
	SessionId    string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DatabaseName string `thrift:"DatabaseName,2,required" frugal:"2,required,string" json:"DatabaseName"`
}

func NewDataConnectInstanceResp() *DataConnectInstanceResp {
	return &DataConnectInstanceResp{}
}

func (p *DataConnectInstanceResp) InitDefault() {
}

func (p *DataConnectInstanceResp) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DataConnectInstanceResp) GetDatabaseName() (v string) {
	return p.DatabaseName
}
func (p *DataConnectInstanceResp) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DataConnectInstanceResp) SetDatabaseName(val string) {
	p.DatabaseName = val
}

var fieldIDToName_DataConnectInstanceResp = map[int16]string{
	1: "SessionId",
	2: "DatabaseName",
}

func (p *DataConnectInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataConnectInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDatabaseName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabaseName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabaseName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataConnectInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataConnectInstanceResp[fieldId]))
}

func (p *DataConnectInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DataConnectInstanceResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatabaseName = _field
	return nil
}

func (p *DataConnectInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataConnectInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataConnectInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataConnectInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataConnectInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatabaseName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataConnectInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataConnectInstanceResp(%+v)", *p)

}

func (p *DataConnectInstanceResp) DeepEqual(ano *DataConnectInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatabaseName) {
		return false
	}
	return true
}

func (p *DataConnectInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DataConnectInstanceResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DatabaseName, src) != 0 {
		return false
	}
	return true
}

type DataExecCommandsReq struct {
	SessionId      string  `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	Commands       string  `thrift:"Commands,2,required" frugal:"2,required,string" json:"Commands"`
	DatabaseName   *string `thrift:"DatabaseName,3,optional" frugal:"3,optional,string" json:"DatabaseName,omitempty"`
	TimeOutSeconds *int64  `thrift:"TimeOutSeconds,4,optional" frugal:"4,optional,i64" json:"TimeOutSeconds,omitempty"`
	Force          *bool   `thrift:"Force,5,optional" frugal:"5,optional,bool" json:"Force,omitempty"`
}

func NewDataExecCommandsReq() *DataExecCommandsReq {
	return &DataExecCommandsReq{}
}

func (p *DataExecCommandsReq) InitDefault() {
}

func (p *DataExecCommandsReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DataExecCommandsReq) GetCommands() (v string) {
	return p.Commands
}

var DataExecCommandsReq_DatabaseName_DEFAULT string

func (p *DataExecCommandsReq) GetDatabaseName() (v string) {
	if !p.IsSetDatabaseName() {
		return DataExecCommandsReq_DatabaseName_DEFAULT
	}
	return *p.DatabaseName
}

var DataExecCommandsReq_TimeOutSeconds_DEFAULT int64

func (p *DataExecCommandsReq) GetTimeOutSeconds() (v int64) {
	if !p.IsSetTimeOutSeconds() {
		return DataExecCommandsReq_TimeOutSeconds_DEFAULT
	}
	return *p.TimeOutSeconds
}

var DataExecCommandsReq_Force_DEFAULT bool

func (p *DataExecCommandsReq) GetForce() (v bool) {
	if !p.IsSetForce() {
		return DataExecCommandsReq_Force_DEFAULT
	}
	return *p.Force
}
func (p *DataExecCommandsReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DataExecCommandsReq) SetCommands(val string) {
	p.Commands = val
}
func (p *DataExecCommandsReq) SetDatabaseName(val *string) {
	p.DatabaseName = val
}
func (p *DataExecCommandsReq) SetTimeOutSeconds(val *int64) {
	p.TimeOutSeconds = val
}
func (p *DataExecCommandsReq) SetForce(val *bool) {
	p.Force = val
}

var fieldIDToName_DataExecCommandsReq = map[int16]string{
	1: "SessionId",
	2: "Commands",
	3: "DatabaseName",
	4: "TimeOutSeconds",
	5: "Force",
}

func (p *DataExecCommandsReq) IsSetDatabaseName() bool {
	return p.DatabaseName != nil
}

func (p *DataExecCommandsReq) IsSetTimeOutSeconds() bool {
	return p.TimeOutSeconds != nil
}

func (p *DataExecCommandsReq) IsSetForce() bool {
	return p.Force != nil
}

func (p *DataExecCommandsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetCommands bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCommands = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCommands {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataExecCommandsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataExecCommandsReq[fieldId]))
}

func (p *DataExecCommandsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DataExecCommandsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Commands = _field
	return nil
}
func (p *DataExecCommandsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseName = _field
	return nil
}
func (p *DataExecCommandsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TimeOutSeconds = _field
	return nil
}
func (p *DataExecCommandsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Force = _field
	return nil
}

func (p *DataExecCommandsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataExecCommandsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataExecCommandsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataExecCommandsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Commands", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Commands); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataExecCommandsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseName() {
		if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataExecCommandsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeOutSeconds() {
		if err = oprot.WriteFieldBegin("TimeOutSeconds", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TimeOutSeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DataExecCommandsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetForce() {
		if err = oprot.WriteFieldBegin("Force", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Force); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataExecCommandsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataExecCommandsReq(%+v)", *p)

}

func (p *DataExecCommandsReq) DeepEqual(ano *DataExecCommandsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Commands) {
		return false
	}
	if !p.Field3DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field4DeepEqual(ano.TimeOutSeconds) {
		return false
	}
	if !p.Field5DeepEqual(ano.Force) {
		return false
	}
	return true
}

func (p *DataExecCommandsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Commands, src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandsReq) Field3DeepEqual(src *string) bool {

	if p.DatabaseName == src {
		return true
	} else if p.DatabaseName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseName, *src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandsReq) Field4DeepEqual(src *int64) bool {

	if p.TimeOutSeconds == src {
		return true
	} else if p.TimeOutSeconds == nil || src == nil {
		return false
	}
	if *p.TimeOutSeconds != *src {
		return false
	}
	return true
}
func (p *DataExecCommandsReq) Field5DeepEqual(src *bool) bool {

	if p.Force == src {
		return true
	} else if p.Force == nil || src == nil {
		return false
	}
	if *p.Force != *src {
		return false
	}
	return true
}

type DataExecCommandsResp struct {
	Results []*ResultObject `thrift:"Results,1,optional" frugal:"1,optional,list<ResultObject>" json:"Results,omitempty"`
}

func NewDataExecCommandsResp() *DataExecCommandsResp {
	return &DataExecCommandsResp{}
}

func (p *DataExecCommandsResp) InitDefault() {
}

var DataExecCommandsResp_Results_DEFAULT []*ResultObject

func (p *DataExecCommandsResp) GetResults() (v []*ResultObject) {
	if !p.IsSetResults() {
		return DataExecCommandsResp_Results_DEFAULT
	}
	return p.Results
}
func (p *DataExecCommandsResp) SetResults(val []*ResultObject) {
	p.Results = val
}

var fieldIDToName_DataExecCommandsResp = map[int16]string{
	1: "Results",
}

func (p *DataExecCommandsResp) IsSetResults() bool {
	return p.Results != nil
}

func (p *DataExecCommandsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandsResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataExecCommandsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataExecCommandsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ResultObject, 0, size)
	values := make([]ResultObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Results = _field
	return nil
}

func (p *DataExecCommandsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataExecCommandsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataExecCommandsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResults() {
		if err = oprot.WriteFieldBegin("Results", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Results)); err != nil {
			return err
		}
		for _, v := range p.Results {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataExecCommandsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataExecCommandsResp(%+v)", *p)

}

func (p *DataExecCommandsResp) DeepEqual(ano *DataExecCommandsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Results) {
		return false
	}
	return true
}

func (p *DataExecCommandsResp) Field1DeepEqual(src []*ResultObject) bool {

	if len(p.Results) != len(src) {
		return false
	}
	for i, v := range p.Results {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DataExecCommandSetAsyncReq struct {
	SessionId         string       `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	CommandSetContent string       `thrift:"CommandSetContent,2,required" frugal:"2,required,string" json:"CommandSetContent"`
	DatabaseName      *string      `thrift:"DatabaseName,3,optional" frugal:"3,optional,string" json:"DatabaseName,omitempty"`
	AutoOnlineDDL     *bool        `thrift:"AutoOnlineDDL,4,optional" frugal:"4,optional,bool" json:"AutoOnlineDDL,omitempty"`
	Force             *bool        `thrift:"Force,5,optional" frugal:"5,optional,bool" json:"Force,omitempty"`
	CommandInfo       *CommandInfo `thrift:"CommandInfo,6,optional" frugal:"6,optional,CommandInfo" json:"CommandInfo,omitempty"`
}

func NewDataExecCommandSetAsyncReq() *DataExecCommandSetAsyncReq {
	return &DataExecCommandSetAsyncReq{}
}

func (p *DataExecCommandSetAsyncReq) InitDefault() {
}

func (p *DataExecCommandSetAsyncReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DataExecCommandSetAsyncReq) GetCommandSetContent() (v string) {
	return p.CommandSetContent
}

var DataExecCommandSetAsyncReq_DatabaseName_DEFAULT string

func (p *DataExecCommandSetAsyncReq) GetDatabaseName() (v string) {
	if !p.IsSetDatabaseName() {
		return DataExecCommandSetAsyncReq_DatabaseName_DEFAULT
	}
	return *p.DatabaseName
}

var DataExecCommandSetAsyncReq_AutoOnlineDDL_DEFAULT bool

func (p *DataExecCommandSetAsyncReq) GetAutoOnlineDDL() (v bool) {
	if !p.IsSetAutoOnlineDDL() {
		return DataExecCommandSetAsyncReq_AutoOnlineDDL_DEFAULT
	}
	return *p.AutoOnlineDDL
}

var DataExecCommandSetAsyncReq_Force_DEFAULT bool

func (p *DataExecCommandSetAsyncReq) GetForce() (v bool) {
	if !p.IsSetForce() {
		return DataExecCommandSetAsyncReq_Force_DEFAULT
	}
	return *p.Force
}

var DataExecCommandSetAsyncReq_CommandInfo_DEFAULT *CommandInfo

func (p *DataExecCommandSetAsyncReq) GetCommandInfo() (v *CommandInfo) {
	if !p.IsSetCommandInfo() {
		return DataExecCommandSetAsyncReq_CommandInfo_DEFAULT
	}
	return p.CommandInfo
}
func (p *DataExecCommandSetAsyncReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DataExecCommandSetAsyncReq) SetCommandSetContent(val string) {
	p.CommandSetContent = val
}
func (p *DataExecCommandSetAsyncReq) SetDatabaseName(val *string) {
	p.DatabaseName = val
}
func (p *DataExecCommandSetAsyncReq) SetAutoOnlineDDL(val *bool) {
	p.AutoOnlineDDL = val
}
func (p *DataExecCommandSetAsyncReq) SetForce(val *bool) {
	p.Force = val
}
func (p *DataExecCommandSetAsyncReq) SetCommandInfo(val *CommandInfo) {
	p.CommandInfo = val
}

var fieldIDToName_DataExecCommandSetAsyncReq = map[int16]string{
	1: "SessionId",
	2: "CommandSetContent",
	3: "DatabaseName",
	4: "AutoOnlineDDL",
	5: "Force",
	6: "CommandInfo",
}

func (p *DataExecCommandSetAsyncReq) IsSetDatabaseName() bool {
	return p.DatabaseName != nil
}

func (p *DataExecCommandSetAsyncReq) IsSetAutoOnlineDDL() bool {
	return p.AutoOnlineDDL != nil
}

func (p *DataExecCommandSetAsyncReq) IsSetForce() bool {
	return p.Force != nil
}

func (p *DataExecCommandSetAsyncReq) IsSetCommandInfo() bool {
	return p.CommandInfo != nil
}

func (p *DataExecCommandSetAsyncReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandSetAsyncReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetCommandSetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCommandSetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCommandSetContent {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataExecCommandSetAsyncReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataExecCommandSetAsyncReq[fieldId]))
}

func (p *DataExecCommandSetAsyncReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DataExecCommandSetAsyncReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CommandSetContent = _field
	return nil
}
func (p *DataExecCommandSetAsyncReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseName = _field
	return nil
}
func (p *DataExecCommandSetAsyncReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoOnlineDDL = _field
	return nil
}
func (p *DataExecCommandSetAsyncReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Force = _field
	return nil
}
func (p *DataExecCommandSetAsyncReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewCommandInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CommandInfo = _field
	return nil
}

func (p *DataExecCommandSetAsyncReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandSetAsyncReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataExecCommandSetAsyncReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CommandSetContent", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CommandSetContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseName() {
		if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoOnlineDDL() {
		if err = oprot.WriteFieldBegin("AutoOnlineDDL", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AutoOnlineDDL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetForce() {
		if err = oprot.WriteFieldBegin("Force", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Force); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommandInfo() {
		if err = oprot.WriteFieldBegin("CommandInfo", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CommandInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataExecCommandSetAsyncReq(%+v)", *p)

}

func (p *DataExecCommandSetAsyncReq) DeepEqual(ano *DataExecCommandSetAsyncReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.CommandSetContent) {
		return false
	}
	if !p.Field3DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field4DeepEqual(ano.AutoOnlineDDL) {
		return false
	}
	if !p.Field5DeepEqual(ano.Force) {
		return false
	}
	if !p.Field6DeepEqual(ano.CommandInfo) {
		return false
	}
	return true
}

func (p *DataExecCommandSetAsyncReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandSetAsyncReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CommandSetContent, src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandSetAsyncReq) Field3DeepEqual(src *string) bool {

	if p.DatabaseName == src {
		return true
	} else if p.DatabaseName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseName, *src) != 0 {
		return false
	}
	return true
}
func (p *DataExecCommandSetAsyncReq) Field4DeepEqual(src *bool) bool {

	if p.AutoOnlineDDL == src {
		return true
	} else if p.AutoOnlineDDL == nil || src == nil {
		return false
	}
	if *p.AutoOnlineDDL != *src {
		return false
	}
	return true
}
func (p *DataExecCommandSetAsyncReq) Field5DeepEqual(src *bool) bool {

	if p.Force == src {
		return true
	} else if p.Force == nil || src == nil {
		return false
	}
	if *p.Force != *src {
		return false
	}
	return true
}
func (p *DataExecCommandSetAsyncReq) Field6DeepEqual(src *CommandInfo) bool {

	if !p.CommandInfo.DeepEqual(src) {
		return false
	}
	return true
}

type CommandInfo struct {
	IsCreateSharding     *bool   `thrift:"IsCreateSharding,1,optional" frugal:"1,optional,bool" json:"IsCreateSharding,omitempty"`
	ShardingKeyName      *string `thrift:"ShardingKeyName,2,optional" frugal:"2,optional,string" json:"ShardingKeyName,omitempty"`
	ShardingKeyType      *string `thrift:"ShardingKeyType,3,optional" frugal:"3,optional,string" json:"ShardingKeyType,omitempty"`
	KillLongTxn          *bool   `thrift:"KillLongTxn,4,optional" frugal:"4,optional,bool" json:"KillLongTxn,omitempty"`
	RenameDisallowWindow *string `thrift:"RenameDisallowWindow,5,optional" frugal:"5,optional,string" json:"RenameDisallowWindow,omitempty"`
	RplDelayCheckRule    *string `thrift:"RplDelayCheckRule,6,optional" frugal:"6,optional,string" json:"RplDelayCheckRule,omitempty"`
	ReplicaDelaySeconds  *string `thrift:"ReplicaDelaySeconds,7,optional" frugal:"7,optional,string" json:"ReplicaDelaySeconds,omitempty"`
}

func NewCommandInfo() *CommandInfo {
	return &CommandInfo{}
}

func (p *CommandInfo) InitDefault() {
}

var CommandInfo_IsCreateSharding_DEFAULT bool

func (p *CommandInfo) GetIsCreateSharding() (v bool) {
	if !p.IsSetIsCreateSharding() {
		return CommandInfo_IsCreateSharding_DEFAULT
	}
	return *p.IsCreateSharding
}

var CommandInfo_ShardingKeyName_DEFAULT string

func (p *CommandInfo) GetShardingKeyName() (v string) {
	if !p.IsSetShardingKeyName() {
		return CommandInfo_ShardingKeyName_DEFAULT
	}
	return *p.ShardingKeyName
}

var CommandInfo_ShardingKeyType_DEFAULT string

func (p *CommandInfo) GetShardingKeyType() (v string) {
	if !p.IsSetShardingKeyType() {
		return CommandInfo_ShardingKeyType_DEFAULT
	}
	return *p.ShardingKeyType
}

var CommandInfo_KillLongTxn_DEFAULT bool

func (p *CommandInfo) GetKillLongTxn() (v bool) {
	if !p.IsSetKillLongTxn() {
		return CommandInfo_KillLongTxn_DEFAULT
	}
	return *p.KillLongTxn
}

var CommandInfo_RenameDisallowWindow_DEFAULT string

func (p *CommandInfo) GetRenameDisallowWindow() (v string) {
	if !p.IsSetRenameDisallowWindow() {
		return CommandInfo_RenameDisallowWindow_DEFAULT
	}
	return *p.RenameDisallowWindow
}

var CommandInfo_RplDelayCheckRule_DEFAULT string

func (p *CommandInfo) GetRplDelayCheckRule() (v string) {
	if !p.IsSetRplDelayCheckRule() {
		return CommandInfo_RplDelayCheckRule_DEFAULT
	}
	return *p.RplDelayCheckRule
}

var CommandInfo_ReplicaDelaySeconds_DEFAULT string

func (p *CommandInfo) GetReplicaDelaySeconds() (v string) {
	if !p.IsSetReplicaDelaySeconds() {
		return CommandInfo_ReplicaDelaySeconds_DEFAULT
	}
	return *p.ReplicaDelaySeconds
}
func (p *CommandInfo) SetIsCreateSharding(val *bool) {
	p.IsCreateSharding = val
}
func (p *CommandInfo) SetShardingKeyName(val *string) {
	p.ShardingKeyName = val
}
func (p *CommandInfo) SetShardingKeyType(val *string) {
	p.ShardingKeyType = val
}
func (p *CommandInfo) SetKillLongTxn(val *bool) {
	p.KillLongTxn = val
}
func (p *CommandInfo) SetRenameDisallowWindow(val *string) {
	p.RenameDisallowWindow = val
}
func (p *CommandInfo) SetRplDelayCheckRule(val *string) {
	p.RplDelayCheckRule = val
}
func (p *CommandInfo) SetReplicaDelaySeconds(val *string) {
	p.ReplicaDelaySeconds = val
}

var fieldIDToName_CommandInfo = map[int16]string{
	1: "IsCreateSharding",
	2: "ShardingKeyName",
	3: "ShardingKeyType",
	4: "KillLongTxn",
	5: "RenameDisallowWindow",
	6: "RplDelayCheckRule",
	7: "ReplicaDelaySeconds",
}

func (p *CommandInfo) IsSetIsCreateSharding() bool {
	return p.IsCreateSharding != nil
}

func (p *CommandInfo) IsSetShardingKeyName() bool {
	return p.ShardingKeyName != nil
}

func (p *CommandInfo) IsSetShardingKeyType() bool {
	return p.ShardingKeyType != nil
}

func (p *CommandInfo) IsSetKillLongTxn() bool {
	return p.KillLongTxn != nil
}

func (p *CommandInfo) IsSetRenameDisallowWindow() bool {
	return p.RenameDisallowWindow != nil
}

func (p *CommandInfo) IsSetRplDelayCheckRule() bool {
	return p.RplDelayCheckRule != nil
}

func (p *CommandInfo) IsSetReplicaDelaySeconds() bool {
	return p.ReplicaDelaySeconds != nil
}

func (p *CommandInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CommandInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommandInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CommandInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsCreateSharding = _field
	return nil
}
func (p *CommandInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingKeyName = _field
	return nil
}
func (p *CommandInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingKeyType = _field
	return nil
}
func (p *CommandInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KillLongTxn = _field
	return nil
}
func (p *CommandInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RenameDisallowWindow = _field
	return nil
}
func (p *CommandInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RplDelayCheckRule = _field
	return nil
}
func (p *CommandInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReplicaDelaySeconds = _field
	return nil
}

func (p *CommandInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CommandInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CommandInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommandInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsCreateSharding() {
		if err = oprot.WriteFieldBegin("IsCreateSharding", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsCreateSharding); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CommandInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingKeyName() {
		if err = oprot.WriteFieldBegin("ShardingKeyName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingKeyName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CommandInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingKeyType() {
		if err = oprot.WriteFieldBegin("ShardingKeyType", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingKeyType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CommandInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetKillLongTxn() {
		if err = oprot.WriteFieldBegin("KillLongTxn", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.KillLongTxn); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CommandInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRenameDisallowWindow() {
		if err = oprot.WriteFieldBegin("RenameDisallowWindow", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RenameDisallowWindow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CommandInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRplDelayCheckRule() {
		if err = oprot.WriteFieldBegin("RplDelayCheckRule", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RplDelayCheckRule); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CommandInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetReplicaDelaySeconds() {
		if err = oprot.WriteFieldBegin("ReplicaDelaySeconds", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReplicaDelaySeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CommandInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommandInfo(%+v)", *p)

}

func (p *CommandInfo) DeepEqual(ano *CommandInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IsCreateSharding) {
		return false
	}
	if !p.Field2DeepEqual(ano.ShardingKeyName) {
		return false
	}
	if !p.Field3DeepEqual(ano.ShardingKeyType) {
		return false
	}
	if !p.Field4DeepEqual(ano.KillLongTxn) {
		return false
	}
	if !p.Field5DeepEqual(ano.RenameDisallowWindow) {
		return false
	}
	if !p.Field6DeepEqual(ano.RplDelayCheckRule) {
		return false
	}
	if !p.Field7DeepEqual(ano.ReplicaDelaySeconds) {
		return false
	}
	return true
}

func (p *CommandInfo) Field1DeepEqual(src *bool) bool {

	if p.IsCreateSharding == src {
		return true
	} else if p.IsCreateSharding == nil || src == nil {
		return false
	}
	if *p.IsCreateSharding != *src {
		return false
	}
	return true
}
func (p *CommandInfo) Field2DeepEqual(src *string) bool {

	if p.ShardingKeyName == src {
		return true
	} else if p.ShardingKeyName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingKeyName, *src) != 0 {
		return false
	}
	return true
}
func (p *CommandInfo) Field3DeepEqual(src *string) bool {

	if p.ShardingKeyType == src {
		return true
	} else if p.ShardingKeyType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingKeyType, *src) != 0 {
		return false
	}
	return true
}
func (p *CommandInfo) Field4DeepEqual(src *bool) bool {

	if p.KillLongTxn == src {
		return true
	} else if p.KillLongTxn == nil || src == nil {
		return false
	}
	if *p.KillLongTxn != *src {
		return false
	}
	return true
}
func (p *CommandInfo) Field5DeepEqual(src *string) bool {

	if p.RenameDisallowWindow == src {
		return true
	} else if p.RenameDisallowWindow == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RenameDisallowWindow, *src) != 0 {
		return false
	}
	return true
}
func (p *CommandInfo) Field6DeepEqual(src *string) bool {

	if p.RplDelayCheckRule == src {
		return true
	} else if p.RplDelayCheckRule == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RplDelayCheckRule, *src) != 0 {
		return false
	}
	return true
}
func (p *CommandInfo) Field7DeepEqual(src *string) bool {

	if p.ReplicaDelaySeconds == src {
		return true
	} else if p.ReplicaDelaySeconds == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReplicaDelaySeconds, *src) != 0 {
		return false
	}
	return true
}

type DataExecCommandSetAsyncResp struct {
	CommandSetId *string `thrift:"CommandSetId,1,optional" frugal:"1,optional,string" json:"CommandSetId,omitempty"`
}

func NewDataExecCommandSetAsyncResp() *DataExecCommandSetAsyncResp {
	return &DataExecCommandSetAsyncResp{}
}

func (p *DataExecCommandSetAsyncResp) InitDefault() {
}

var DataExecCommandSetAsyncResp_CommandSetId_DEFAULT string

func (p *DataExecCommandSetAsyncResp) GetCommandSetId() (v string) {
	if !p.IsSetCommandSetId() {
		return DataExecCommandSetAsyncResp_CommandSetId_DEFAULT
	}
	return *p.CommandSetId
}
func (p *DataExecCommandSetAsyncResp) SetCommandSetId(val *string) {
	p.CommandSetId = val
}

var fieldIDToName_DataExecCommandSetAsyncResp = map[int16]string{
	1: "CommandSetId",
}

func (p *DataExecCommandSetAsyncResp) IsSetCommandSetId() bool {
	return p.CommandSetId != nil
}

func (p *DataExecCommandSetAsyncResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandSetAsyncResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataExecCommandSetAsyncResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataExecCommandSetAsyncResp) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CommandSetId = _field
	return nil
}

func (p *DataExecCommandSetAsyncResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataExecCommandSetAsyncResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataExecCommandSetAsyncResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataExecCommandSetAsyncResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommandSetId() {
		if err = oprot.WriteFieldBegin("CommandSetId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CommandSetId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataExecCommandSetAsyncResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataExecCommandSetAsyncResp(%+v)", *p)

}

func (p *DataExecCommandSetAsyncResp) DeepEqual(ano *DataExecCommandSetAsyncResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CommandSetId) {
		return false
	}
	return true
}

func (p *DataExecCommandSetAsyncResp) Field1DeepEqual(src *string) bool {

	if p.CommandSetId == src {
		return true
	} else if p.CommandSetId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CommandSetId, *src) != 0 {
		return false
	}
	return true
}

type DataDescribeCommandSetReq struct {
	CommandSetId *string `thrift:"CommandSetId,1,optional" frugal:"1,optional,string" json:"CommandSetId,omitempty"`
}

func NewDataDescribeCommandSetReq() *DataDescribeCommandSetReq {
	return &DataDescribeCommandSetReq{}
}

func (p *DataDescribeCommandSetReq) InitDefault() {
}

var DataDescribeCommandSetReq_CommandSetId_DEFAULT string

func (p *DataDescribeCommandSetReq) GetCommandSetId() (v string) {
	if !p.IsSetCommandSetId() {
		return DataDescribeCommandSetReq_CommandSetId_DEFAULT
	}
	return *p.CommandSetId
}
func (p *DataDescribeCommandSetReq) SetCommandSetId(val *string) {
	p.CommandSetId = val
}

var fieldIDToName_DataDescribeCommandSetReq = map[int16]string{
	1: "CommandSetId",
}

func (p *DataDescribeCommandSetReq) IsSetCommandSetId() bool {
	return p.CommandSetId != nil
}

func (p *DataDescribeCommandSetReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataDescribeCommandSetReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataDescribeCommandSetReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataDescribeCommandSetReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CommandSetId = _field
	return nil
}

func (p *DataDescribeCommandSetReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataDescribeCommandSetReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataDescribeCommandSetReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataDescribeCommandSetReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommandSetId() {
		if err = oprot.WriteFieldBegin("CommandSetId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CommandSetId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataDescribeCommandSetReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataDescribeCommandSetReq(%+v)", *p)

}

func (p *DataDescribeCommandSetReq) DeepEqual(ano *DataDescribeCommandSetReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CommandSetId) {
		return false
	}
	return true
}

func (p *DataDescribeCommandSetReq) Field1DeepEqual(src *string) bool {

	if p.CommandSetId == src {
		return true
	} else if p.CommandSetId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CommandSetId, *src) != 0 {
		return false
	}
	return true
}

type DataDescribeCommandSetResp struct {
	StartTime         *int64           `thrift:"StartTime,1,optional" frugal:"1,optional,i64" json:"StartTime,omitempty"`
	EndTime           *int64           `thrift:"EndTime,2,optional" frugal:"2,optional,i64" json:"EndTime,omitempty"`
	Progress          *int32           `thrift:"Progress,3,optional" frugal:"3,optional,i32" json:"Progress,omitempty"`
	SetState          *CommandSetState `thrift:"SetState,4,optional" frugal:"4,optional,CommandSetState" json:"SetState,omitempty"`
	CommandSuccessNum *int32           `thrift:"CommandSuccessNum,5,optional" frugal:"5,optional,i32" json:"CommandSuccessNum,omitempty"`
	SetContent        *string          `thrift:"SetContent,6,optional" frugal:"6,optional,string" json:"SetContent,omitempty"`
}

func NewDataDescribeCommandSetResp() *DataDescribeCommandSetResp {
	return &DataDescribeCommandSetResp{}
}

func (p *DataDescribeCommandSetResp) InitDefault() {
}

var DataDescribeCommandSetResp_StartTime_DEFAULT int64

func (p *DataDescribeCommandSetResp) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return DataDescribeCommandSetResp_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DataDescribeCommandSetResp_EndTime_DEFAULT int64

func (p *DataDescribeCommandSetResp) GetEndTime() (v int64) {
	if !p.IsSetEndTime() {
		return DataDescribeCommandSetResp_EndTime_DEFAULT
	}
	return *p.EndTime
}

var DataDescribeCommandSetResp_Progress_DEFAULT int32

func (p *DataDescribeCommandSetResp) GetProgress() (v int32) {
	if !p.IsSetProgress() {
		return DataDescribeCommandSetResp_Progress_DEFAULT
	}
	return *p.Progress
}

var DataDescribeCommandSetResp_SetState_DEFAULT CommandSetState

func (p *DataDescribeCommandSetResp) GetSetState() (v CommandSetState) {
	if !p.IsSetSetState() {
		return DataDescribeCommandSetResp_SetState_DEFAULT
	}
	return *p.SetState
}

var DataDescribeCommandSetResp_CommandSuccessNum_DEFAULT int32

func (p *DataDescribeCommandSetResp) GetCommandSuccessNum() (v int32) {
	if !p.IsSetCommandSuccessNum() {
		return DataDescribeCommandSetResp_CommandSuccessNum_DEFAULT
	}
	return *p.CommandSuccessNum
}

var DataDescribeCommandSetResp_SetContent_DEFAULT string

func (p *DataDescribeCommandSetResp) GetSetContent() (v string) {
	if !p.IsSetSetContent() {
		return DataDescribeCommandSetResp_SetContent_DEFAULT
	}
	return *p.SetContent
}
func (p *DataDescribeCommandSetResp) SetStartTime(val *int64) {
	p.StartTime = val
}
func (p *DataDescribeCommandSetResp) SetEndTime(val *int64) {
	p.EndTime = val
}
func (p *DataDescribeCommandSetResp) SetProgress(val *int32) {
	p.Progress = val
}
func (p *DataDescribeCommandSetResp) SetSetState(val *CommandSetState) {
	p.SetState = val
}
func (p *DataDescribeCommandSetResp) SetCommandSuccessNum(val *int32) {
	p.CommandSuccessNum = val
}
func (p *DataDescribeCommandSetResp) SetSetContent(val *string) {
	p.SetContent = val
}

var fieldIDToName_DataDescribeCommandSetResp = map[int16]string{
	1: "StartTime",
	2: "EndTime",
	3: "Progress",
	4: "SetState",
	5: "CommandSuccessNum",
	6: "SetContent",
}

func (p *DataDescribeCommandSetResp) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DataDescribeCommandSetResp) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DataDescribeCommandSetResp) IsSetProgress() bool {
	return p.Progress != nil
}

func (p *DataDescribeCommandSetResp) IsSetSetState() bool {
	return p.SetState != nil
}

func (p *DataDescribeCommandSetResp) IsSetCommandSuccessNum() bool {
	return p.CommandSuccessNum != nil
}

func (p *DataDescribeCommandSetResp) IsSetSetContent() bool {
	return p.SetContent != nil
}

func (p *DataDescribeCommandSetResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataDescribeCommandSetResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataDescribeCommandSetResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DataDescribeCommandSetResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *DataDescribeCommandSetResp) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Progress = _field
	return nil
}
func (p *DataDescribeCommandSetResp) ReadField4(iprot thrift.TProtocol) error {

	var _field *CommandSetState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CommandSetState(v)
		_field = &tmp
	}
	p.SetState = _field
	return nil
}
func (p *DataDescribeCommandSetResp) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CommandSuccessNum = _field
	return nil
}
func (p *DataDescribeCommandSetResp) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SetContent = _field
	return nil
}

func (p *DataDescribeCommandSetResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataDescribeCommandSetResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataDescribeCommandSetResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetProgress() {
		if err = oprot.WriteFieldBegin("Progress", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Progress); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSetState() {
		if err = oprot.WriteFieldBegin("SetState", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SetState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommandSuccessNum() {
		if err = oprot.WriteFieldBegin("CommandSuccessNum", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.CommandSuccessNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSetContent() {
		if err = oprot.WriteFieldBegin("SetContent", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SetContent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataDescribeCommandSetResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataDescribeCommandSetResp(%+v)", *p)

}

func (p *DataDescribeCommandSetResp) DeepEqual(ano *DataDescribeCommandSetResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.Progress) {
		return false
	}
	if !p.Field4DeepEqual(ano.SetState) {
		return false
	}
	if !p.Field5DeepEqual(ano.CommandSuccessNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.SetContent) {
		return false
	}
	return true
}

func (p *DataDescribeCommandSetResp) Field1DeepEqual(src *int64) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if *p.StartTime != *src {
		return false
	}
	return true
}
func (p *DataDescribeCommandSetResp) Field2DeepEqual(src *int64) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if *p.EndTime != *src {
		return false
	}
	return true
}
func (p *DataDescribeCommandSetResp) Field3DeepEqual(src *int32) bool {

	if p.Progress == src {
		return true
	} else if p.Progress == nil || src == nil {
		return false
	}
	if *p.Progress != *src {
		return false
	}
	return true
}
func (p *DataDescribeCommandSetResp) Field4DeepEqual(src *CommandSetState) bool {

	if p.SetState == src {
		return true
	} else if p.SetState == nil || src == nil {
		return false
	}
	if *p.SetState != *src {
		return false
	}
	return true
}
func (p *DataDescribeCommandSetResp) Field5DeepEqual(src *int32) bool {

	if p.CommandSuccessNum == src {
		return true
	} else if p.CommandSuccessNum == nil || src == nil {
		return false
	}
	if *p.CommandSuccessNum != *src {
		return false
	}
	return true
}
func (p *DataDescribeCommandSetResp) Field6DeepEqual(src *string) bool {

	if p.SetContent == src {
		return true
	} else if p.SetContent == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SetContent, *src) != 0 {
		return false
	}
	return true
}

type DataGetCommandSetResultReq struct {
	CommandSetId *string     `thrift:"CommandSetId,1,optional" frugal:"1,optional,string" json:"CommandSetId,omitempty"`
	FilterType   *FilterType `thrift:"FilterType,2,optional" frugal:"2,optional,FilterType" json:"FilterType,omitempty"`
}

func NewDataGetCommandSetResultReq() *DataGetCommandSetResultReq {
	return &DataGetCommandSetResultReq{}
}

func (p *DataGetCommandSetResultReq) InitDefault() {
}

var DataGetCommandSetResultReq_CommandSetId_DEFAULT string

func (p *DataGetCommandSetResultReq) GetCommandSetId() (v string) {
	if !p.IsSetCommandSetId() {
		return DataGetCommandSetResultReq_CommandSetId_DEFAULT
	}
	return *p.CommandSetId
}

var DataGetCommandSetResultReq_FilterType_DEFAULT FilterType

func (p *DataGetCommandSetResultReq) GetFilterType() (v FilterType) {
	if !p.IsSetFilterType() {
		return DataGetCommandSetResultReq_FilterType_DEFAULT
	}
	return *p.FilterType
}
func (p *DataGetCommandSetResultReq) SetCommandSetId(val *string) {
	p.CommandSetId = val
}
func (p *DataGetCommandSetResultReq) SetFilterType(val *FilterType) {
	p.FilterType = val
}

var fieldIDToName_DataGetCommandSetResultReq = map[int16]string{
	1: "CommandSetId",
	2: "FilterType",
}

func (p *DataGetCommandSetResultReq) IsSetCommandSetId() bool {
	return p.CommandSetId != nil
}

func (p *DataGetCommandSetResultReq) IsSetFilterType() bool {
	return p.FilterType != nil
}

func (p *DataGetCommandSetResultReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataGetCommandSetResultReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataGetCommandSetResultReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataGetCommandSetResultReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CommandSetId = _field
	return nil
}
func (p *DataGetCommandSetResultReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *FilterType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FilterType(v)
		_field = &tmp
	}
	p.FilterType = _field
	return nil
}

func (p *DataGetCommandSetResultReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataGetCommandSetResultReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataGetCommandSetResultReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataGetCommandSetResultReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommandSetId() {
		if err = oprot.WriteFieldBegin("CommandSetId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CommandSetId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataGetCommandSetResultReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterType() {
		if err = oprot.WriteFieldBegin("FilterType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.FilterType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataGetCommandSetResultReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataGetCommandSetResultReq(%+v)", *p)

}

func (p *DataGetCommandSetResultReq) DeepEqual(ano *DataGetCommandSetResultReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CommandSetId) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilterType) {
		return false
	}
	return true
}

func (p *DataGetCommandSetResultReq) Field1DeepEqual(src *string) bool {

	if p.CommandSetId == src {
		return true
	} else if p.CommandSetId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CommandSetId, *src) != 0 {
		return false
	}
	return true
}
func (p *DataGetCommandSetResultReq) Field2DeepEqual(src *FilterType) bool {

	if p.FilterType == src {
		return true
	} else if p.FilterType == nil || src == nil {
		return false
	}
	if *p.FilterType != *src {
		return false
	}
	return true
}

type DataGetCommandSetResultResp struct {
	Results    []*ResultObject `thrift:"Results,1,optional" frugal:"1,optional,list<ResultObject>" json:"Results,omitempty"`
	FilterType *FilterType     `thrift:"FilterType,2,optional" frugal:"2,optional,FilterType" json:"FilterType,omitempty"`
}

func NewDataGetCommandSetResultResp() *DataGetCommandSetResultResp {
	return &DataGetCommandSetResultResp{}
}

func (p *DataGetCommandSetResultResp) InitDefault() {
}

var DataGetCommandSetResultResp_Results_DEFAULT []*ResultObject

func (p *DataGetCommandSetResultResp) GetResults() (v []*ResultObject) {
	if !p.IsSetResults() {
		return DataGetCommandSetResultResp_Results_DEFAULT
	}
	return p.Results
}

var DataGetCommandSetResultResp_FilterType_DEFAULT FilterType

func (p *DataGetCommandSetResultResp) GetFilterType() (v FilterType) {
	if !p.IsSetFilterType() {
		return DataGetCommandSetResultResp_FilterType_DEFAULT
	}
	return *p.FilterType
}
func (p *DataGetCommandSetResultResp) SetResults(val []*ResultObject) {
	p.Results = val
}
func (p *DataGetCommandSetResultResp) SetFilterType(val *FilterType) {
	p.FilterType = val
}

var fieldIDToName_DataGetCommandSetResultResp = map[int16]string{
	1: "Results",
	2: "FilterType",
}

func (p *DataGetCommandSetResultResp) IsSetResults() bool {
	return p.Results != nil
}

func (p *DataGetCommandSetResultResp) IsSetFilterType() bool {
	return p.FilterType != nil
}

func (p *DataGetCommandSetResultResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataGetCommandSetResultResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataGetCommandSetResultResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataGetCommandSetResultResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ResultObject, 0, size)
	values := make([]ResultObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Results = _field
	return nil
}
func (p *DataGetCommandSetResultResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *FilterType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FilterType(v)
		_field = &tmp
	}
	p.FilterType = _field
	return nil
}

func (p *DataGetCommandSetResultResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataGetCommandSetResultResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataGetCommandSetResultResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataGetCommandSetResultResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResults() {
		if err = oprot.WriteFieldBegin("Results", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Results)); err != nil {
			return err
		}
		for _, v := range p.Results {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataGetCommandSetResultResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterType() {
		if err = oprot.WriteFieldBegin("FilterType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.FilterType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataGetCommandSetResultResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataGetCommandSetResultResp(%+v)", *p)

}

func (p *DataGetCommandSetResultResp) DeepEqual(ano *DataGetCommandSetResultResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Results) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilterType) {
		return false
	}
	return true
}

func (p *DataGetCommandSetResultResp) Field1DeepEqual(src []*ResultObject) bool {

	if len(p.Results) != len(src) {
		return false
	}
	for i, v := range p.Results {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DataGetCommandSetResultResp) Field2DeepEqual(src *FilterType) bool {

	if p.FilterType == src {
		return true
	} else if p.FilterType == nil || src == nil {
		return false
	}
	if *p.FilterType != *src {
		return false
	}
	return true
}

type DataSessionKeepAliveReq struct {
	SessionId string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
}

func NewDataSessionKeepAliveReq() *DataSessionKeepAliveReq {
	return &DataSessionKeepAliveReq{}
}

func (p *DataSessionKeepAliveReq) InitDefault() {
}

func (p *DataSessionKeepAliveReq) GetSessionId() (v string) {
	return p.SessionId
}
func (p *DataSessionKeepAliveReq) SetSessionId(val string) {
	p.SessionId = val
}

var fieldIDToName_DataSessionKeepAliveReq = map[int16]string{
	1: "SessionId",
}

func (p *DataSessionKeepAliveReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataSessionKeepAliveReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataSessionKeepAliveReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataSessionKeepAliveReq[fieldId]))
}

func (p *DataSessionKeepAliveReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}

func (p *DataSessionKeepAliveReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataSessionKeepAliveReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataSessionKeepAliveReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataSessionKeepAliveReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataSessionKeepAliveReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataSessionKeepAliveReq(%+v)", *p)

}

func (p *DataSessionKeepAliveReq) DeepEqual(ano *DataSessionKeepAliveReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	return true
}

func (p *DataSessionKeepAliveReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}

type DataSessionKeepAliveResp struct {
}

func NewDataSessionKeepAliveResp() *DataSessionKeepAliveResp {
	return &DataSessionKeepAliveResp{}
}

func (p *DataSessionKeepAliveResp) InitDefault() {
}

var fieldIDToName_DataSessionKeepAliveResp = map[int16]string{}

func (p *DataSessionKeepAliveResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataSessionKeepAliveResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataSessionKeepAliveResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataSessionKeepAliveResp")

	if err = oprot.WriteStructBegin("DataSessionKeepAliveResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataSessionKeepAliveResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataSessionKeepAliveResp(%+v)", *p)

}

func (p *DataSessionKeepAliveResp) DeepEqual(ano *DataSessionKeepAliveResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DataCloseSessionReq struct {
	SessionId string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
}

func NewDataCloseSessionReq() *DataCloseSessionReq {
	return &DataCloseSessionReq{}
}

func (p *DataCloseSessionReq) InitDefault() {
}

func (p *DataCloseSessionReq) GetSessionId() (v string) {
	return p.SessionId
}
func (p *DataCloseSessionReq) SetSessionId(val string) {
	p.SessionId = val
}

var fieldIDToName_DataCloseSessionReq = map[int16]string{
	1: "SessionId",
}

func (p *DataCloseSessionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCloseSessionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataCloseSessionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataCloseSessionReq[fieldId]))
}

func (p *DataCloseSessionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}

func (p *DataCloseSessionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCloseSessionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataCloseSessionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataCloseSessionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataCloseSessionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataCloseSessionReq(%+v)", *p)

}

func (p *DataCloseSessionReq) DeepEqual(ano *DataCloseSessionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	return true
}

func (p *DataCloseSessionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}

type DataCloseSessionResp struct {
}

func NewDataCloseSessionResp() *DataCloseSessionResp {
	return &DataCloseSessionResp{}
}

func (p *DataCloseSessionResp) InitDefault() {
}

var fieldIDToName_DataCloseSessionResp = map[int16]string{}

func (p *DataCloseSessionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCloseSessionResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataCloseSessionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCloseSessionResp")

	if err = oprot.WriteStructBegin("DataCloseSessionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataCloseSessionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataCloseSessionResp(%+v)", *p)

}

func (p *DataCloseSessionResp) DeepEqual(ano *DataCloseSessionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DataCancelExecReq struct {
	SessionId *string `thrift:"SessionId,1,optional" frugal:"1,optional,string" json:"SessionId,omitempty"`
}

func NewDataCancelExecReq() *DataCancelExecReq {
	return &DataCancelExecReq{}
}

func (p *DataCancelExecReq) InitDefault() {
}

var DataCancelExecReq_SessionId_DEFAULT string

func (p *DataCancelExecReq) GetSessionId() (v string) {
	if !p.IsSetSessionId() {
		return DataCancelExecReq_SessionId_DEFAULT
	}
	return *p.SessionId
}
func (p *DataCancelExecReq) SetSessionId(val *string) {
	p.SessionId = val
}

var fieldIDToName_DataCancelExecReq = map[int16]string{
	1: "SessionId",
}

func (p *DataCancelExecReq) IsSetSessionId() bool {
	return p.SessionId != nil
}

func (p *DataCancelExecReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCancelExecReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataCancelExecReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataCancelExecReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SessionId = _field
	return nil
}

func (p *DataCancelExecReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCancelExecReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataCancelExecReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataCancelExecReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSessionId() {
		if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SessionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataCancelExecReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataCancelExecReq(%+v)", *p)

}

func (p *DataCancelExecReq) DeepEqual(ano *DataCancelExecReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	return true
}

func (p *DataCancelExecReq) Field1DeepEqual(src *string) bool {

	if p.SessionId == src {
		return true
	} else if p.SessionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SessionId, *src) != 0 {
		return false
	}
	return true
}

type DataCancelExecResp struct {
}

func NewDataCancelExecResp() *DataCancelExecResp {
	return &DataCancelExecResp{}
}

func (p *DataCancelExecResp) InitDefault() {
}

var fieldIDToName_DataCancelExecResp = map[int16]string{}

func (p *DataCancelExecResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCancelExecResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataCancelExecResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataCancelExecResp")

	if err = oprot.WriteStructBegin("DataCancelExecResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataCancelExecResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataCancelExecResp(%+v)", *p)

}

func (p *DataCancelExecResp) DeepEqual(ano *DataCancelExecResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeAuditLogConfigReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:"required"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" validate:"required"`
	LogVersion   *string      `thrift:"LogVersion,3,optional" frugal:"3,optional,string" json:"LogVersion,omitempty"`
}

func NewDescribeAuditLogConfigReq() *DescribeAuditLogConfigReq {
	return &DescribeAuditLogConfigReq{}
}

func (p *DescribeAuditLogConfigReq) InitDefault() {
}

func (p *DescribeAuditLogConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAuditLogConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var DescribeAuditLogConfigReq_LogVersion_DEFAULT string

func (p *DescribeAuditLogConfigReq) GetLogVersion() (v string) {
	if !p.IsSetLogVersion() {
		return DescribeAuditLogConfigReq_LogVersion_DEFAULT
	}
	return *p.LogVersion
}
func (p *DescribeAuditLogConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAuditLogConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAuditLogConfigReq) SetLogVersion(val *string) {
	p.LogVersion = val
}

var fieldIDToName_DescribeAuditLogConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "LogVersion",
}

func (p *DescribeAuditLogConfigReq) IsSetLogVersion() bool {
	return p.LogVersion != nil
}

func (p *DescribeAuditLogConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAuditLogConfigReq[fieldId]))
}

func (p *DescribeAuditLogConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAuditLogConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAuditLogConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LogVersion = _field
	return nil
}

func (p *DescribeAuditLogConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogVersion() {
		if err = oprot.WriteFieldBegin("LogVersion", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LogVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAuditLogConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogConfigReq(%+v)", *p)

}

func (p *DescribeAuditLogConfigReq) DeepEqual(ano *DescribeAuditLogConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.LogVersion) {
		return false
	}
	return true
}

func (p *DescribeAuditLogConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAuditLogConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAuditLogConfigReq) Field3DeepEqual(src *string) bool {

	if p.LogVersion == src {
		return true
	} else if p.LogVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.LogVersion, *src) != 0 {
		return false
	}
	return true
}

type DescribeAuditLogConfigResp struct {
	SqlRetentionDay int16 `thrift:"SqlRetentionDay,2,required" frugal:"2,required,i16" json:"SqlRetentionDay"`
}

func NewDescribeAuditLogConfigResp() *DescribeAuditLogConfigResp {
	return &DescribeAuditLogConfigResp{}
}

func (p *DescribeAuditLogConfigResp) InitDefault() {
}

func (p *DescribeAuditLogConfigResp) GetSqlRetentionDay() (v int16) {
	return p.SqlRetentionDay
}
func (p *DescribeAuditLogConfigResp) SetSqlRetentionDay(val int16) {
	p.SqlRetentionDay = val
}

var fieldIDToName_DescribeAuditLogConfigResp = map[int16]string{
	2: "SqlRetentionDay",
}

func (p *DescribeAuditLogConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlRetentionDay bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I16 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlRetentionDay = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlRetentionDay {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAuditLogConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAuditLogConfigResp[fieldId]))
}

func (p *DescribeAuditLogConfigResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int16
	if v, err := iprot.ReadI16(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlRetentionDay = _field
	return nil
}

func (p *DescribeAuditLogConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAuditLogConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAuditLogConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAuditLogConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlRetentionDay", thrift.I16, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI16(p.SqlRetentionDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAuditLogConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAuditLogConfigResp(%+v)", *p)

}

func (p *DescribeAuditLogConfigResp) DeepEqual(ano *DescribeAuditLogConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlRetentionDay) {
		return false
	}
	return true
}

func (p *DescribeAuditLogConfigResp) Field2DeepEqual(src int16) bool {

	if p.SqlRetentionDay != src {
		return false
	}
	return true
}

type ModifyAuditLogConfigReq struct {
	InstanceId      string       `thrift:"InstanceId,1,required" frugal:"1,required,string" validate:required`
	InstanceType    InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" validate:required`
	SqlRetentionDay *int16       `thrift:"SqlRetentionDay,3,optional" frugal:"3,optional,i16" validate:gte=0,lte=3651`
	LogVersion      *string      `thrift:"LogVersion,4,optional" frugal:"4,optional,string" json:"LogVersion,omitempty"`
}

func NewModifyAuditLogConfigReq() *ModifyAuditLogConfigReq {
	return &ModifyAuditLogConfigReq{}
}

func (p *ModifyAuditLogConfigReq) InitDefault() {
}

func (p *ModifyAuditLogConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyAuditLogConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var ModifyAuditLogConfigReq_SqlRetentionDay_DEFAULT int16

func (p *ModifyAuditLogConfigReq) GetSqlRetentionDay() (v int16) {
	if !p.IsSetSqlRetentionDay() {
		return ModifyAuditLogConfigReq_SqlRetentionDay_DEFAULT
	}
	return *p.SqlRetentionDay
}

var ModifyAuditLogConfigReq_LogVersion_DEFAULT string

func (p *ModifyAuditLogConfigReq) GetLogVersion() (v string) {
	if !p.IsSetLogVersion() {
		return ModifyAuditLogConfigReq_LogVersion_DEFAULT
	}
	return *p.LogVersion
}
func (p *ModifyAuditLogConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyAuditLogConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyAuditLogConfigReq) SetSqlRetentionDay(val *int16) {
	p.SqlRetentionDay = val
}
func (p *ModifyAuditLogConfigReq) SetLogVersion(val *string) {
	p.LogVersion = val
}

var fieldIDToName_ModifyAuditLogConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "SqlRetentionDay",
	4: "LogVersion",
}

func (p *ModifyAuditLogConfigReq) IsSetSqlRetentionDay() bool {
	return p.SqlRetentionDay != nil
}

func (p *ModifyAuditLogConfigReq) IsSetLogVersion() bool {
	return p.LogVersion != nil
}

func (p *ModifyAuditLogConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAuditLogConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyAuditLogConfigReq[fieldId]))
}

func (p *ModifyAuditLogConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyAuditLogConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyAuditLogConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int16
	if v, err := iprot.ReadI16(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlRetentionDay = _field
	return nil
}
func (p *ModifyAuditLogConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LogVersion = _field
	return nil
}

func (p *ModifyAuditLogConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAuditLogConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlRetentionDay() {
		if err = oprot.WriteFieldBegin("SqlRetentionDay", thrift.I16, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI16(*p.SqlRetentionDay); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogVersion() {
		if err = oprot.WriteFieldBegin("LogVersion", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LogVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyAuditLogConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAuditLogConfigReq(%+v)", *p)

}

func (p *ModifyAuditLogConfigReq) DeepEqual(ano *ModifyAuditLogConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlRetentionDay) {
		return false
	}
	if !p.Field4DeepEqual(ano.LogVersion) {
		return false
	}
	return true
}

func (p *ModifyAuditLogConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAuditLogConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyAuditLogConfigReq) Field3DeepEqual(src *int16) bool {

	if p.SqlRetentionDay == src {
		return true
	} else if p.SqlRetentionDay == nil || src == nil {
		return false
	}
	if *p.SqlRetentionDay != *src {
		return false
	}
	return true
}
func (p *ModifyAuditLogConfigReq) Field4DeepEqual(src *string) bool {

	if p.LogVersion == src {
		return true
	} else if p.LogVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.LogVersion, *src) != 0 {
		return false
	}
	return true
}

type ModifyAuditLogConfigResp struct {
}

func NewModifyAuditLogConfigResp() *ModifyAuditLogConfigResp {
	return &ModifyAuditLogConfigResp{}
}

func (p *ModifyAuditLogConfigResp) InitDefault() {
}

var fieldIDToName_ModifyAuditLogConfigResp = map[int16]string{}

func (p *ModifyAuditLogConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAuditLogConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAuditLogConfigResp")

	if err = oprot.WriteStructBegin("ModifyAuditLogConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAuditLogConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAuditLogConfigResp(%+v)", *p)

}

func (p *ModifyAuditLogConfigResp) DeepEqual(ano *ModifyAuditLogConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type SlowQueryAdviceTaskHistoryApiReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" validate:"required"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" validate:"required"`
	PageNumber   int32        `thrift:"PageNumber,4,required" frugal:"4,required,i32" validate:"gte=1"`
	PageSize     int32        `thrift:"PageSize,5,required" frugal:"5,required,i32" validate:"gte=1,lte=100"`
}

func NewSlowQueryAdviceTaskHistoryApiReq() *SlowQueryAdviceTaskHistoryApiReq {
	return &SlowQueryAdviceTaskHistoryApiReq{}
}

func (p *SlowQueryAdviceTaskHistoryApiReq) InitDefault() {
}

func (p *SlowQueryAdviceTaskHistoryApiReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *SlowQueryAdviceTaskHistoryApiReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SlowQueryAdviceTaskHistoryApiReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *SlowQueryAdviceTaskHistoryApiReq) GetPageSize() (v int32) {
	return p.PageSize
}
func (p *SlowQueryAdviceTaskHistoryApiReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *SlowQueryAdviceTaskHistoryApiReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SlowQueryAdviceTaskHistoryApiReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *SlowQueryAdviceTaskHistoryApiReq) SetPageSize(val int32) {
	p.PageSize = val
}

var fieldIDToName_SlowQueryAdviceTaskHistoryApiReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	4: "PageNumber",
	5: "PageSize",
}

func (p *SlowQueryAdviceTaskHistoryApiReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryApiReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryApiReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryApiReq[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryApiReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}

func (p *SlowQueryAdviceTaskHistoryApiReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryApiReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceTaskHistoryApiReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceTaskHistoryApiReq(%+v)", *p)

}

func (p *SlowQueryAdviceTaskHistoryApiReq) DeepEqual(ano *SlowQueryAdviceTaskHistoryApiReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceTaskHistoryApiReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryApiReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryApiReq) Field4DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryApiReq) Field5DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}

type SlowQueryAdviceTaskHistoryApiResp struct {
	Total   int32                     `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	ResList []*SlowQueryAdviceTaskAgg `thrift:"ResList,2,required" frugal:"2,required,list<SlowQueryAdviceTaskAgg>" json:"ResList"`
}

func NewSlowQueryAdviceTaskHistoryApiResp() *SlowQueryAdviceTaskHistoryApiResp {
	return &SlowQueryAdviceTaskHistoryApiResp{}
}

func (p *SlowQueryAdviceTaskHistoryApiResp) InitDefault() {
}

func (p *SlowQueryAdviceTaskHistoryApiResp) GetTotal() (v int32) {
	return p.Total
}

func (p *SlowQueryAdviceTaskHistoryApiResp) GetResList() (v []*SlowQueryAdviceTaskAgg) {
	return p.ResList
}
func (p *SlowQueryAdviceTaskHistoryApiResp) SetTotal(val int32) {
	p.Total = val
}
func (p *SlowQueryAdviceTaskHistoryApiResp) SetResList(val []*SlowQueryAdviceTaskAgg) {
	p.ResList = val
}

var fieldIDToName_SlowQueryAdviceTaskHistoryApiResp = map[int16]string{
	1: "Total",
	2: "ResList",
}

func (p *SlowQueryAdviceTaskHistoryApiResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryApiResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetResList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResList {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryApiResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryApiResp[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryApiResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowQueryAdviceTaskAgg, 0, size)
	values := make([]SlowQueryAdviceTaskAgg, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResList = _field
	return nil
}

func (p *SlowQueryAdviceTaskHistoryApiResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryApiResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceTaskHistoryApiResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResList", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResList)); err != nil {
		return err
	}
	for _, v := range p.ResList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryApiResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceTaskHistoryApiResp(%+v)", *p)

}

func (p *SlowQueryAdviceTaskHistoryApiResp) DeepEqual(ano *SlowQueryAdviceTaskHistoryApiResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.ResList) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceTaskHistoryApiResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryApiResp) Field2DeepEqual(src []*SlowQueryAdviceTaskAgg) bool {

	if len(p.ResList) != len(src) {
		return false
	}
	for i, v := range p.ResList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListSlowQueryAdviceApiReq struct {
	InstanceType InstanceType               `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" validate:"required"`
	InstanceId   string                     `thrift:"InstanceId,2,required" frugal:"2,required,string" validate:"required"`
	AdviceType   AdviceType                 `thrift:"AdviceType,4,required" frugal:"4,required,AdviceType" json:"AdviceType"`
	SummaryId    string                     `thrift:"SummaryId,5,required" frugal:"5,required,string" validate:"required"`
	GroupBy      ListSlowQueryAdviceGroupBy `thrift:"GroupBy,6,required" frugal:"6,required,ListSlowQueryAdviceGroupBy" json:"GroupBy"`
	OrderBy      ListSlowQueryAdviceOrderBy `thrift:"OrderBy,7,required" frugal:"7,required,ListSlowQueryAdviceOrderBy" json:"OrderBy"`
	PageNumber   int32                      `thrift:"PageNumber,8,required" frugal:"8,required,i32" validate:"gte=1"`
	PageSize     int32                      `thrift:"PageSize,9,required" frugal:"9,required,i32" validate:"gte=1,lte=100"`
}

func NewListSlowQueryAdviceApiReq() *ListSlowQueryAdviceApiReq {
	return &ListSlowQueryAdviceApiReq{}
}

func (p *ListSlowQueryAdviceApiReq) InitDefault() {
}

func (p *ListSlowQueryAdviceApiReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ListSlowQueryAdviceApiReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListSlowQueryAdviceApiReq) GetAdviceType() (v AdviceType) {
	return p.AdviceType
}

func (p *ListSlowQueryAdviceApiReq) GetSummaryId() (v string) {
	return p.SummaryId
}

func (p *ListSlowQueryAdviceApiReq) GetGroupBy() (v ListSlowQueryAdviceGroupBy) {
	return p.GroupBy
}

func (p *ListSlowQueryAdviceApiReq) GetOrderBy() (v ListSlowQueryAdviceOrderBy) {
	return p.OrderBy
}

func (p *ListSlowQueryAdviceApiReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *ListSlowQueryAdviceApiReq) GetPageSize() (v int32) {
	return p.PageSize
}
func (p *ListSlowQueryAdviceApiReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ListSlowQueryAdviceApiReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListSlowQueryAdviceApiReq) SetAdviceType(val AdviceType) {
	p.AdviceType = val
}
func (p *ListSlowQueryAdviceApiReq) SetSummaryId(val string) {
	p.SummaryId = val
}
func (p *ListSlowQueryAdviceApiReq) SetGroupBy(val ListSlowQueryAdviceGroupBy) {
	p.GroupBy = val
}
func (p *ListSlowQueryAdviceApiReq) SetOrderBy(val ListSlowQueryAdviceOrderBy) {
	p.OrderBy = val
}
func (p *ListSlowQueryAdviceApiReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *ListSlowQueryAdviceApiReq) SetPageSize(val int32) {
	p.PageSize = val
}

var fieldIDToName_ListSlowQueryAdviceApiReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	4: "AdviceType",
	5: "SummaryId",
	6: "GroupBy",
	7: "OrderBy",
	8: "PageNumber",
	9: "PageSize",
}

func (p *ListSlowQueryAdviceApiReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceApiReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetAdviceType bool = false
	var issetSummaryId bool = false
	var issetGroupBy bool = false
	var issetOrderBy bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSummaryId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroupBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSummaryId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetGroupBy {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceApiReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceApiReq[fieldId]))
}

func (p *ListSlowQueryAdviceApiReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AdviceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AdviceType(v)
	}
	p.AdviceType = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SummaryId = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField6(iprot thrift.TProtocol) error {

	var _field ListSlowQueryAdviceGroupBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ListSlowQueryAdviceGroupBy(v)
	}
	p.GroupBy = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField7(iprot thrift.TProtocol) error {

	var _field ListSlowQueryAdviceOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ListSlowQueryAdviceOrderBy(v)
	}
	p.OrderBy = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *ListSlowQueryAdviceApiReq) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}

func (p *ListSlowQueryAdviceApiReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceApiReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceApiReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AdviceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SummaryId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SummaryId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupBy", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.GroupBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceApiReq(%+v)", *p)

}

func (p *ListSlowQueryAdviceApiReq) DeepEqual(ano *ListSlowQueryAdviceApiReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.AdviceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.SummaryId) {
		return false
	}
	if !p.Field6DeepEqual(ano.GroupBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field9DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceApiReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field4DeepEqual(src AdviceType) bool {

	if p.AdviceType != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SummaryId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field6DeepEqual(src ListSlowQueryAdviceGroupBy) bool {

	if p.GroupBy != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field7DeepEqual(src ListSlowQueryAdviceOrderBy) bool {

	if p.OrderBy != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field8DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiReq) Field9DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}

type ListSlowQueryAdviceApiResp struct {
	Total          int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	AdvicesByGroup []*AdviceByGroup `thrift:"AdvicesByGroup,2,optional" frugal:"2,optional,list<AdviceByGroup>" json:"AdvicesByGroup,omitempty"`
	Advices        []*Advice        `thrift:"Advices,3,optional" frugal:"3,optional,list<Advice>" json:"Advices,omitempty"`
}

func NewListSlowQueryAdviceApiResp() *ListSlowQueryAdviceApiResp {
	return &ListSlowQueryAdviceApiResp{}
}

func (p *ListSlowQueryAdviceApiResp) InitDefault() {
}

func (p *ListSlowQueryAdviceApiResp) GetTotal() (v int32) {
	return p.Total
}

var ListSlowQueryAdviceApiResp_AdvicesByGroup_DEFAULT []*AdviceByGroup

func (p *ListSlowQueryAdviceApiResp) GetAdvicesByGroup() (v []*AdviceByGroup) {
	if !p.IsSetAdvicesByGroup() {
		return ListSlowQueryAdviceApiResp_AdvicesByGroup_DEFAULT
	}
	return p.AdvicesByGroup
}

var ListSlowQueryAdviceApiResp_Advices_DEFAULT []*Advice

func (p *ListSlowQueryAdviceApiResp) GetAdvices() (v []*Advice) {
	if !p.IsSetAdvices() {
		return ListSlowQueryAdviceApiResp_Advices_DEFAULT
	}
	return p.Advices
}
func (p *ListSlowQueryAdviceApiResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListSlowQueryAdviceApiResp) SetAdvicesByGroup(val []*AdviceByGroup) {
	p.AdvicesByGroup = val
}
func (p *ListSlowQueryAdviceApiResp) SetAdvices(val []*Advice) {
	p.Advices = val
}

var fieldIDToName_ListSlowQueryAdviceApiResp = map[int16]string{
	1: "Total",
	2: "AdvicesByGroup",
	3: "Advices",
}

func (p *ListSlowQueryAdviceApiResp) IsSetAdvicesByGroup() bool {
	return p.AdvicesByGroup != nil
}

func (p *ListSlowQueryAdviceApiResp) IsSetAdvices() bool {
	return p.Advices != nil
}

func (p *ListSlowQueryAdviceApiResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceApiResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceApiResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceApiResp[fieldId]))
}

func (p *ListSlowQueryAdviceApiResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListSlowQueryAdviceApiResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AdviceByGroup, 0, size)
	values := make([]AdviceByGroup, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AdvicesByGroup = _field
	return nil
}
func (p *ListSlowQueryAdviceApiResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Advice, 0, size)
	values := make([]Advice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Advices = _field
	return nil
}

func (p *ListSlowQueryAdviceApiResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceApiResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceApiResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdvicesByGroup() {
		if err = oprot.WriteFieldBegin("AdvicesByGroup", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdvicesByGroup)); err != nil {
			return err
		}
		for _, v := range p.AdvicesByGroup {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdvices() {
		if err = oprot.WriteFieldBegin("Advices", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Advices)); err != nil {
			return err
		}
		for _, v := range p.Advices {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowQueryAdviceApiResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceApiResp(%+v)", *p)

}

func (p *ListSlowQueryAdviceApiResp) DeepEqual(ano *ListSlowQueryAdviceApiResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.AdvicesByGroup) {
		return false
	}
	if !p.Field3DeepEqual(ano.Advices) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceApiResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceApiResp) Field2DeepEqual(src []*AdviceByGroup) bool {

	if len(p.AdvicesByGroup) != len(src) {
		return false
	}
	for i, v := range p.AdvicesByGroup {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ListSlowQueryAdviceApiResp) Field3DeepEqual(src []*Advice) bool {

	if len(p.Advices) != len(src) {
		return false
	}
	for i, v := range p.Advices {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type GenerateSQLFromNLReq struct {
	InstanceID   string   `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	InstanceType string   `thrift:"InstanceType,2,required" frugal:"2,required,string" json:"InstanceType"`
	Database     string   `thrift:"Database,3,required" frugal:"3,required,string" json:"Database"`
	Query        string   `thrift:"Query,4,required" frugal:"4,required,string" json:"Query"`
	Tables       []string `thrift:"Tables,5,optional" frugal:"5,optional,list<string>" json:"Tables,omitempty"`
	IsStream     *bool    `thrift:"IsStream,6,optional" frugal:"6,optional,bool" json:"IsStream,omitempty"`
}

func NewGenerateSQLFromNLReq() *GenerateSQLFromNLReq {
	return &GenerateSQLFromNLReq{}
}

func (p *GenerateSQLFromNLReq) InitDefault() {
}

func (p *GenerateSQLFromNLReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *GenerateSQLFromNLReq) GetInstanceType() (v string) {
	return p.InstanceType
}

func (p *GenerateSQLFromNLReq) GetDatabase() (v string) {
	return p.Database
}

func (p *GenerateSQLFromNLReq) GetQuery() (v string) {
	return p.Query
}

var GenerateSQLFromNLReq_Tables_DEFAULT []string

func (p *GenerateSQLFromNLReq) GetTables() (v []string) {
	if !p.IsSetTables() {
		return GenerateSQLFromNLReq_Tables_DEFAULT
	}
	return p.Tables
}

var GenerateSQLFromNLReq_IsStream_DEFAULT bool

func (p *GenerateSQLFromNLReq) GetIsStream() (v bool) {
	if !p.IsSetIsStream() {
		return GenerateSQLFromNLReq_IsStream_DEFAULT
	}
	return *p.IsStream
}
func (p *GenerateSQLFromNLReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *GenerateSQLFromNLReq) SetInstanceType(val string) {
	p.InstanceType = val
}
func (p *GenerateSQLFromNLReq) SetDatabase(val string) {
	p.Database = val
}
func (p *GenerateSQLFromNLReq) SetQuery(val string) {
	p.Query = val
}
func (p *GenerateSQLFromNLReq) SetTables(val []string) {
	p.Tables = val
}
func (p *GenerateSQLFromNLReq) SetIsStream(val *bool) {
	p.IsStream = val
}

var fieldIDToName_GenerateSQLFromNLReq = map[int16]string{
	1: "InstanceID",
	2: "InstanceType",
	3: "Database",
	4: "Query",
	5: "Tables",
	6: "IsStream",
}

func (p *GenerateSQLFromNLReq) IsSetTables() bool {
	return p.Tables != nil
}

func (p *GenerateSQLFromNLReq) IsSetIsStream() bool {
	return p.IsStream != nil
}

func (p *GenerateSQLFromNLReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GenerateSQLFromNLReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetInstanceType bool = false
	var issetDatabase bool = false
	var issetQuery bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabase = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDatabase {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GenerateSQLFromNLReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GenerateSQLFromNLReq[fieldId]))
}

func (p *GenerateSQLFromNLReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *GenerateSQLFromNLReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}
func (p *GenerateSQLFromNLReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Database = _field
	return nil
}
func (p *GenerateSQLFromNLReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *GenerateSQLFromNLReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tables = _field
	return nil
}
func (p *GenerateSQLFromNLReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsStream = _field
	return nil
}

func (p *GenerateSQLFromNLReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GenerateSQLFromNLReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GenerateSQLFromNLReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Database", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Database); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTables() {
		if err = oprot.WriteFieldBegin("Tables", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Tables)); err != nil {
			return err
		}
		for _, v := range p.Tables {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsStream() {
		if err = oprot.WriteFieldBegin("IsStream", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsStream); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GenerateSQLFromNLReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateSQLFromNLReq(%+v)", *p)

}

func (p *GenerateSQLFromNLReq) DeepEqual(ano *GenerateSQLFromNLReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Database) {
		return false
	}
	if !p.Field4DeepEqual(ano.Query) {
		return false
	}
	if !p.Field5DeepEqual(ano.Tables) {
		return false
	}
	if !p.Field6DeepEqual(ano.IsStream) {
		return false
	}
	return true
}

func (p *GenerateSQLFromNLReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *GenerateSQLFromNLReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}
func (p *GenerateSQLFromNLReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Database, src) != 0 {
		return false
	}
	return true
}
func (p *GenerateSQLFromNLReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *GenerateSQLFromNLReq) Field5DeepEqual(src []string) bool {

	if len(p.Tables) != len(src) {
		return false
	}
	for i, v := range p.Tables {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *GenerateSQLFromNLReq) Field6DeepEqual(src *bool) bool {

	if p.IsStream == src {
		return true
	} else if p.IsStream == nil || src == nil {
		return false
	}
	if *p.IsStream != *src {
		return false
	}
	return true
}

type GenerateSQLFromNLResp struct {
	SQL string `thrift:"SQL,1,required" frugal:"1,required,string" json:"SQL"`
}

func NewGenerateSQLFromNLResp() *GenerateSQLFromNLResp {
	return &GenerateSQLFromNLResp{}
}

func (p *GenerateSQLFromNLResp) InitDefault() {
}

func (p *GenerateSQLFromNLResp) GetSQL() (v string) {
	return p.SQL
}
func (p *GenerateSQLFromNLResp) SetSQL(val string) {
	p.SQL = val
}

var fieldIDToName_GenerateSQLFromNLResp = map[int16]string{
	1: "SQL",
}

func (p *GenerateSQLFromNLResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GenerateSQLFromNLResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQL {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GenerateSQLFromNLResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GenerateSQLFromNLResp[fieldId]))
}

func (p *GenerateSQLFromNLResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQL = _field
	return nil
}

func (p *GenerateSQLFromNLResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GenerateSQLFromNLResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GenerateSQLFromNLResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GenerateSQLFromNLResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GenerateSQLFromNLResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateSQLFromNLResp(%+v)", *p)

}

func (p *GenerateSQLFromNLResp) DeepEqual(ano *GenerateSQLFromNLResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQL) {
		return false
	}
	return true
}

func (p *GenerateSQLFromNLResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SQL, src) != 0 {
		return false
	}
	return true
}

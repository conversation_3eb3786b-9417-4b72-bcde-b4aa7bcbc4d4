// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SlowQuerySummaryStatus int64

const (
	SlowQuerySummaryStatus_INIT      SlowQuerySummaryStatus = 0
	SlowQuerySummaryStatus_SUCCESS   SlowQuerySummaryStatus = 1
	SlowQuerySummaryStatus_EXCEPTION SlowQuerySummaryStatus = 2
)

func (p SlowQuerySummaryStatus) String() string {
	switch p {
	case SlowQuerySummaryStatus_INIT:
		return "INIT"
	case SlowQuerySummaryStatus_SUCCESS:
		return "SUCCESS"
	case SlowQuerySummaryStatus_EXCEPTION:
		return "EXCEPTION"
	}
	return "<UNSET>"
}

func SlowQuerySummaryStatusFromString(s string) (SlowQuerySummaryStatus, error) {
	switch s {
	case "INIT":
		return SlowQuerySummaryStatus_INIT, nil
	case "SUCCESS":
		return SlowQuerySummaryStatus_SUCCESS, nil
	case "EXCEPTION":
		return SlowQuerySummaryStatus_EXCEPTION, nil
	}
	return SlowQuerySummaryStatus(0), fmt.Errorf("not a valid SlowQuerySummaryStatus string")
}

func SlowQuerySummaryStatusPtr(v SlowQuerySummaryStatus) *SlowQuerySummaryStatus { return &v }

func (p SlowQuerySummaryStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SlowQuerySummaryStatus) UnmarshalText(text []byte) error {
	q, err := SlowQuerySummaryStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AdviceLevel int64

const (
	AdviceLevel_Low      AdviceLevel = 0
	AdviceLevel_Middle   AdviceLevel = 1
	AdviceLevel_High     AdviceLevel = 2
	AdviceLevel_VeryHigh AdviceLevel = 3
)

func (p AdviceLevel) String() string {
	switch p {
	case AdviceLevel_Low:
		return "Low"
	case AdviceLevel_Middle:
		return "Middle"
	case AdviceLevel_High:
		return "High"
	case AdviceLevel_VeryHigh:
		return "VeryHigh"
	}
	return "<UNSET>"
}

func AdviceLevelFromString(s string) (AdviceLevel, error) {
	switch s {
	case "Low":
		return AdviceLevel_Low, nil
	case "Middle":
		return AdviceLevel_Middle, nil
	case "High":
		return AdviceLevel_High, nil
	case "VeryHigh":
		return AdviceLevel_VeryHigh, nil
	}
	return AdviceLevel(0), fmt.Errorf("not a valid AdviceLevel string")
}

func AdviceLevelPtr(v AdviceLevel) *AdviceLevel { return &v }

func (p AdviceLevel) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AdviceLevel) UnmarshalText(text []byte) error {
	q, err := AdviceLevelFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AdviceType int64

const (
	AdviceType_no_advice          AdviceType = 0
	AdviceType_index_advice       AdviceType = 1
	AdviceType_rewrite_sql_advice AdviceType = 2
)

func (p AdviceType) String() string {
	switch p {
	case AdviceType_no_advice:
		return "no_advice"
	case AdviceType_index_advice:
		return "index_advice"
	case AdviceType_rewrite_sql_advice:
		return "rewrite_sql_advice"
	}
	return "<UNSET>"
}

func AdviceTypeFromString(s string) (AdviceType, error) {
	switch s {
	case "no_advice":
		return AdviceType_no_advice, nil
	case "index_advice":
		return AdviceType_index_advice, nil
	case "rewrite_sql_advice":
		return AdviceType_rewrite_sql_advice, nil
	}
	return AdviceType(0), fmt.Errorf("not a valid AdviceType string")
}

func AdviceTypePtr(v AdviceType) *AdviceType { return &v }

func (p AdviceType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AdviceType) UnmarshalText(text []byte) error {
	q, err := AdviceTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ListSlowQueryAdviceGroupBy int64

const (
	ListSlowQueryAdviceGroupBy_Advice ListSlowQueryAdviceGroupBy = 0
	ListSlowQueryAdviceGroupBy_Module ListSlowQueryAdviceGroupBy = 1
)

func (p ListSlowQueryAdviceGroupBy) String() string {
	switch p {
	case ListSlowQueryAdviceGroupBy_Advice:
		return "Advice"
	case ListSlowQueryAdviceGroupBy_Module:
		return "Module"
	}
	return "<UNSET>"
}

func ListSlowQueryAdviceGroupByFromString(s string) (ListSlowQueryAdviceGroupBy, error) {
	switch s {
	case "Advice":
		return ListSlowQueryAdviceGroupBy_Advice, nil
	case "Module":
		return ListSlowQueryAdviceGroupBy_Module, nil
	}
	return ListSlowQueryAdviceGroupBy(0), fmt.Errorf("not a valid ListSlowQueryAdviceGroupBy string")
}

func ListSlowQueryAdviceGroupByPtr(v ListSlowQueryAdviceGroupBy) *ListSlowQueryAdviceGroupBy {
	return &v
}

func (p ListSlowQueryAdviceGroupBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ListSlowQueryAdviceGroupBy) UnmarshalText(text []byte) error {
	q, err := ListSlowQueryAdviceGroupByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ListSlowQueryAdviceOrderBy int64

const (
	ListSlowQueryAdviceOrderBy_QueryTimeRatioNow ListSlowQueryAdviceOrderBy = 0
	ListSlowQueryAdviceOrderBy_Benefit           ListSlowQueryAdviceOrderBy = 1
)

func (p ListSlowQueryAdviceOrderBy) String() string {
	switch p {
	case ListSlowQueryAdviceOrderBy_QueryTimeRatioNow:
		return "QueryTimeRatioNow"
	case ListSlowQueryAdviceOrderBy_Benefit:
		return "Benefit"
	}
	return "<UNSET>"
}

func ListSlowQueryAdviceOrderByFromString(s string) (ListSlowQueryAdviceOrderBy, error) {
	switch s {
	case "QueryTimeRatioNow":
		return ListSlowQueryAdviceOrderBy_QueryTimeRatioNow, nil
	case "Benefit":
		return ListSlowQueryAdviceOrderBy_Benefit, nil
	}
	return ListSlowQueryAdviceOrderBy(0), fmt.Errorf("not a valid ListSlowQueryAdviceOrderBy string")
}

func ListSlowQueryAdviceOrderByPtr(v ListSlowQueryAdviceOrderBy) *ListSlowQueryAdviceOrderBy {
	return &v
}

func (p ListSlowQueryAdviceOrderBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ListSlowQueryAdviceOrderBy) UnmarshalText(text []byte) error {
	q, err := ListSlowQueryAdviceOrderByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AdviceConfig struct {
	Enable               bool  `thrift:"Enable,1,required" frugal:"1,required,bool" json:"Enable"`
	AnalysisResKeepDays  int32 `thrift:"AnalysisResKeepDays,2,required" frugal:"2,required,i32" json:"AnalysisResKeepDays"`
	OptimizeResTrackDays int32 `thrift:"OptimizeResTrackDays,3,required" frugal:"3,required,i32" json:"OptimizeResTrackDays"`
}

func NewAdviceConfig() *AdviceConfig {
	return &AdviceConfig{}
}

func (p *AdviceConfig) InitDefault() {
}

func (p *AdviceConfig) GetEnable() (v bool) {
	return p.Enable
}

func (p *AdviceConfig) GetAnalysisResKeepDays() (v int32) {
	return p.AnalysisResKeepDays
}

func (p *AdviceConfig) GetOptimizeResTrackDays() (v int32) {
	return p.OptimizeResTrackDays
}
func (p *AdviceConfig) SetEnable(val bool) {
	p.Enable = val
}
func (p *AdviceConfig) SetAnalysisResKeepDays(val int32) {
	p.AnalysisResKeepDays = val
}
func (p *AdviceConfig) SetOptimizeResTrackDays(val int32) {
	p.OptimizeResTrackDays = val
}

var fieldIDToName_AdviceConfig = map[int16]string{
	1: "Enable",
	2: "AnalysisResKeepDays",
	3: "OptimizeResTrackDays",
}

func (p *AdviceConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AdviceConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEnable bool = false
	var issetAnalysisResKeepDays bool = false
	var issetOptimizeResTrackDays bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAnalysisResKeepDays = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetOptimizeResTrackDays = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEnable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAnalysisResKeepDays {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOptimizeResTrackDays {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviceConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AdviceConfig[fieldId]))
}

func (p *AdviceConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}
func (p *AdviceConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AnalysisResKeepDays = _field
	return nil
}
func (p *AdviceConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OptimizeResTrackDays = _field
	return nil
}

func (p *AdviceConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AdviceConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("AdviceConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AdviceConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AdviceConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AnalysisResKeepDays", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AnalysisResKeepDays); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AdviceConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OptimizeResTrackDays", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.OptimizeResTrackDays); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AdviceConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdviceConfig(%+v)", *p)

}

func (p *AdviceConfig) DeepEqual(ano *AdviceConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Enable) {
		return false
	}
	if !p.Field2DeepEqual(ano.AnalysisResKeepDays) {
		return false
	}
	if !p.Field3DeepEqual(ano.OptimizeResTrackDays) {
		return false
	}
	return true
}

func (p *AdviceConfig) Field1DeepEqual(src bool) bool {

	if p.Enable != src {
		return false
	}
	return true
}
func (p *AdviceConfig) Field2DeepEqual(src int32) bool {

	if p.AnalysisResKeepDays != src {
		return false
	}
	return true
}
func (p *AdviceConfig) Field3DeepEqual(src int32) bool {

	if p.OptimizeResTrackDays != src {
		return false
	}
	return true
}

type SlowQueryAdviceConfig struct {
	SlowQueryAdviceConfigId *int64        `thrift:"SlowQueryAdviceConfigId,1,optional" frugal:"1,optional,i64" json:"SlowQueryAdviceConfigId,omitempty"`
	InstanceId              string        `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	RegionId                string        `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	InstanceType            InstanceType  `thrift:"InstanceType,5,required" frugal:"5,required,InstanceType" json:"InstanceType"`
	TenantID                *string       `thrift:"TenantID,6,optional" frugal:"6,optional,string" json:"TenantID,omitempty"`
	Config                  *AdviceConfig `thrift:"config,7,required" frugal:"7,required,AdviceConfig" json:"config"`
}

func NewSlowQueryAdviceConfig() *SlowQueryAdviceConfig {
	return &SlowQueryAdviceConfig{}
}

func (p *SlowQueryAdviceConfig) InitDefault() {
}

var SlowQueryAdviceConfig_SlowQueryAdviceConfigId_DEFAULT int64

func (p *SlowQueryAdviceConfig) GetSlowQueryAdviceConfigId() (v int64) {
	if !p.IsSetSlowQueryAdviceConfigId() {
		return SlowQueryAdviceConfig_SlowQueryAdviceConfigId_DEFAULT
	}
	return *p.SlowQueryAdviceConfigId
}

func (p *SlowQueryAdviceConfig) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SlowQueryAdviceConfig) GetRegionId() (v string) {
	return p.RegionId
}

func (p *SlowQueryAdviceConfig) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var SlowQueryAdviceConfig_TenantID_DEFAULT string

func (p *SlowQueryAdviceConfig) GetTenantID() (v string) {
	if !p.IsSetTenantID() {
		return SlowQueryAdviceConfig_TenantID_DEFAULT
	}
	return *p.TenantID
}

var SlowQueryAdviceConfig_Config_DEFAULT *AdviceConfig

func (p *SlowQueryAdviceConfig) GetConfig() (v *AdviceConfig) {
	if !p.IsSetConfig() {
		return SlowQueryAdviceConfig_Config_DEFAULT
	}
	return p.Config
}
func (p *SlowQueryAdviceConfig) SetSlowQueryAdviceConfigId(val *int64) {
	p.SlowQueryAdviceConfigId = val
}
func (p *SlowQueryAdviceConfig) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SlowQueryAdviceConfig) SetRegionId(val string) {
	p.RegionId = val
}
func (p *SlowQueryAdviceConfig) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *SlowQueryAdviceConfig) SetTenantID(val *string) {
	p.TenantID = val
}
func (p *SlowQueryAdviceConfig) SetConfig(val *AdviceConfig) {
	p.Config = val
}

var fieldIDToName_SlowQueryAdviceConfig = map[int16]string{
	1: "SlowQueryAdviceConfigId",
	2: "InstanceId",
	3: "RegionId",
	5: "InstanceType",
	6: "TenantID",
	7: "config",
}

func (p *SlowQueryAdviceConfig) IsSetSlowQueryAdviceConfigId() bool {
	return p.SlowQueryAdviceConfigId != nil
}

func (p *SlowQueryAdviceConfig) IsSetTenantID() bool {
	return p.TenantID != nil
}

func (p *SlowQueryAdviceConfig) IsSetConfig() bool {
	return p.Config != nil
}

func (p *SlowQueryAdviceConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetRegionId bool = false
	var issetInstanceType bool = false
	var issetConfig bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetConfig {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceConfig[fieldId]))
}

func (p *SlowQueryAdviceConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SlowQueryAdviceConfigId = _field
	return nil
}
func (p *SlowQueryAdviceConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SlowQueryAdviceConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *SlowQueryAdviceConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *SlowQueryAdviceConfig) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TenantID = _field
	return nil
}
func (p *SlowQueryAdviceConfig) ReadField7(iprot thrift.TProtocol) error {
	_field := NewAdviceConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Config = _field
	return nil
}

func (p *SlowQueryAdviceConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSlowQueryAdviceConfigId() {
		if err = oprot.WriteFieldBegin("SlowQueryAdviceConfigId", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SlowQueryAdviceConfigId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTenantID() {
		if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TenantID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("config", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Config.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SlowQueryAdviceConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceConfig(%+v)", *p)

}

func (p *SlowQueryAdviceConfig) DeepEqual(ano *SlowQueryAdviceConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SlowQueryAdviceConfigId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.TenantID) {
		return false
	}
	if !p.Field7DeepEqual(ano.Config) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceConfig) Field1DeepEqual(src *int64) bool {

	if p.SlowQueryAdviceConfigId == src {
		return true
	} else if p.SlowQueryAdviceConfigId == nil || src == nil {
		return false
	}
	if *p.SlowQueryAdviceConfigId != *src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceConfig) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceConfig) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceConfig) Field5DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceConfig) Field6DeepEqual(src *string) bool {

	if p.TenantID == src {
		return true
	} else if p.TenantID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TenantID, *src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceConfig) Field7DeepEqual(src *AdviceConfig) bool {

	if !p.Config.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateSlowQueryAnalysisReq struct {
	Configs []*SlowQueryAdviceConfig `thrift:"configs,1,required" frugal:"1,required,list<SlowQueryAdviceConfig>" json:"configs"`
}

func NewUpdateSlowQueryAnalysisReq() *UpdateSlowQueryAnalysisReq {
	return &UpdateSlowQueryAnalysisReq{}
}

func (p *UpdateSlowQueryAnalysisReq) InitDefault() {
}

func (p *UpdateSlowQueryAnalysisReq) GetConfigs() (v []*SlowQueryAdviceConfig) {
	return p.Configs
}
func (p *UpdateSlowQueryAnalysisReq) SetConfigs(val []*SlowQueryAdviceConfig) {
	p.Configs = val
}

var fieldIDToName_UpdateSlowQueryAnalysisReq = map[int16]string{
	1: "configs",
}

func (p *UpdateSlowQueryAnalysisReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateSlowQueryAnalysisReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConfigs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConfigs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConfigs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateSlowQueryAnalysisReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateSlowQueryAnalysisReq[fieldId]))
}

func (p *UpdateSlowQueryAnalysisReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowQueryAdviceConfig, 0, size)
	values := make([]SlowQueryAdviceConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Configs = _field
	return nil
}

func (p *UpdateSlowQueryAnalysisReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateSlowQueryAnalysisReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateSlowQueryAnalysisReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateSlowQueryAnalysisReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("configs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Configs)); err != nil {
		return err
	}
	for _, v := range p.Configs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateSlowQueryAnalysisReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSlowQueryAnalysisReq(%+v)", *p)

}

func (p *UpdateSlowQueryAnalysisReq) DeepEqual(ano *UpdateSlowQueryAnalysisReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Configs) {
		return false
	}
	return true
}

func (p *UpdateSlowQueryAnalysisReq) Field1DeepEqual(src []*SlowQueryAdviceConfig) bool {

	if len(p.Configs) != len(src) {
		return false
	}
	for i, v := range p.Configs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type UpdateSlowQueryAnalysisResp struct {
}

func NewUpdateSlowQueryAnalysisResp() *UpdateSlowQueryAnalysisResp {
	return &UpdateSlowQueryAnalysisResp{}
}

func (p *UpdateSlowQueryAnalysisResp) InitDefault() {
}

var fieldIDToName_UpdateSlowQueryAnalysisResp = map[int16]string{}

func (p *UpdateSlowQueryAnalysisResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateSlowQueryAnalysisResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateSlowQueryAnalysisResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateSlowQueryAnalysisResp")

	if err = oprot.WriteStructBegin("UpdateSlowQueryAnalysisResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateSlowQueryAnalysisResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSlowQueryAnalysisResp(%+v)", *p)

}

func (p *UpdateSlowQueryAnalysisResp) DeepEqual(ano *UpdateSlowQueryAnalysisResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ListSlowQueryAdviceConfigReq struct {
	InstanceType            InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId                string       `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId              *string      `thrift:"InstanceId,3,optional" frugal:"3,optional,string" json:"InstanceId,omitempty"`
	PageNumber              int32        `thrift:"PageNumber,4,required" frugal:"4,required,i32" json:"PageNumber"`
	PageSize                int32        `thrift:"PageSize,5,required" frugal:"5,required,i32" json:"PageSize"`
	SlowQueryAdviceConfigId *int64       `thrift:"SlowQueryAdviceConfigId,6,optional" frugal:"6,optional,i64" json:"SlowQueryAdviceConfigId,omitempty"`
}

func NewListSlowQueryAdviceConfigReq() *ListSlowQueryAdviceConfigReq {
	return &ListSlowQueryAdviceConfigReq{}
}

func (p *ListSlowQueryAdviceConfigReq) InitDefault() {
}

func (p *ListSlowQueryAdviceConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ListSlowQueryAdviceConfigReq) GetRegionId() (v string) {
	return p.RegionId
}

var ListSlowQueryAdviceConfigReq_InstanceId_DEFAULT string

func (p *ListSlowQueryAdviceConfigReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return ListSlowQueryAdviceConfigReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

func (p *ListSlowQueryAdviceConfigReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *ListSlowQueryAdviceConfigReq) GetPageSize() (v int32) {
	return p.PageSize
}

var ListSlowQueryAdviceConfigReq_SlowQueryAdviceConfigId_DEFAULT int64

func (p *ListSlowQueryAdviceConfigReq) GetSlowQueryAdviceConfigId() (v int64) {
	if !p.IsSetSlowQueryAdviceConfigId() {
		return ListSlowQueryAdviceConfigReq_SlowQueryAdviceConfigId_DEFAULT
	}
	return *p.SlowQueryAdviceConfigId
}
func (p *ListSlowQueryAdviceConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ListSlowQueryAdviceConfigReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ListSlowQueryAdviceConfigReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *ListSlowQueryAdviceConfigReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *ListSlowQueryAdviceConfigReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *ListSlowQueryAdviceConfigReq) SetSlowQueryAdviceConfigId(val *int64) {
	p.SlowQueryAdviceConfigId = val
}

var fieldIDToName_ListSlowQueryAdviceConfigReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceId",
	4: "PageNumber",
	5: "PageSize",
	6: "SlowQueryAdviceConfigId",
}

func (p *ListSlowQueryAdviceConfigReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *ListSlowQueryAdviceConfigReq) IsSetSlowQueryAdviceConfigId() bool {
	return p.SlowQueryAdviceConfigId != nil
}

func (p *ListSlowQueryAdviceConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceConfigReq[fieldId]))
}

func (p *ListSlowQueryAdviceConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SlowQueryAdviceConfigId = _field
	return nil
}

func (p *ListSlowQueryAdviceConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSlowQueryAdviceConfigId() {
		if err = oprot.WriteFieldBegin("SlowQueryAdviceConfigId", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SlowQueryAdviceConfigId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceConfigReq(%+v)", *p)

}

func (p *ListSlowQueryAdviceConfigReq) DeepEqual(ano *ListSlowQueryAdviceConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.SlowQueryAdviceConfigId) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceConfigReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigReq) Field3DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigReq) Field4DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigReq) Field5DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigReq) Field6DeepEqual(src *int64) bool {

	if p.SlowQueryAdviceConfigId == src {
		return true
	} else if p.SlowQueryAdviceConfigId == nil || src == nil {
		return false
	}
	if *p.SlowQueryAdviceConfigId != *src {
		return false
	}
	return true
}

type ListSlowQueryAdviceConfigResp struct {
	Total   int32                    `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Configs []*SlowQueryAdviceConfig `thrift:"Configs,2,required" frugal:"2,required,list<SlowQueryAdviceConfig>" json:"Configs"`
}

func NewListSlowQueryAdviceConfigResp() *ListSlowQueryAdviceConfigResp {
	return &ListSlowQueryAdviceConfigResp{}
}

func (p *ListSlowQueryAdviceConfigResp) InitDefault() {
}

func (p *ListSlowQueryAdviceConfigResp) GetTotal() (v int32) {
	return p.Total
}

func (p *ListSlowQueryAdviceConfigResp) GetConfigs() (v []*SlowQueryAdviceConfig) {
	return p.Configs
}
func (p *ListSlowQueryAdviceConfigResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListSlowQueryAdviceConfigResp) SetConfigs(val []*SlowQueryAdviceConfig) {
	p.Configs = val
}

var fieldIDToName_ListSlowQueryAdviceConfigResp = map[int16]string{
	1: "Total",
	2: "Configs",
}

func (p *ListSlowQueryAdviceConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetConfigs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetConfigs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetConfigs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceConfigResp[fieldId]))
}

func (p *ListSlowQueryAdviceConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListSlowQueryAdviceConfigResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowQueryAdviceConfig, 0, size)
	values := make([]SlowQueryAdviceConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Configs = _field
	return nil
}

func (p *ListSlowQueryAdviceConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Configs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Configs)); err != nil {
		return err
	}
	for _, v := range p.Configs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceConfigResp(%+v)", *p)

}

func (p *ListSlowQueryAdviceConfigResp) DeepEqual(ano *ListSlowQueryAdviceConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Configs) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceConfigResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceConfigResp) Field2DeepEqual(src []*SlowQueryAdviceConfig) bool {

	if len(p.Configs) != len(src) {
		return false
	}
	for i, v := range p.Configs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SlowQueryAdviceTaskAgg struct {
	Date             string                 `thrift:"Date,1,required" frugal:"1,required,string" json:"Date"`
	SlowQueryNum     int32                  `thrift:"SlowQueryNum,2,required" frugal:"2,required,i32" json:"SlowQueryNum"`
	AdviceNum        int32                  `thrift:"AdviceNum,3,required" frugal:"3,required,i32" json:"AdviceNum"`
	AdviceIndexNum   int32                  `thrift:"AdviceIndexNum,4,required" frugal:"4,required,i32" json:"AdviceIndexNum"`
	AdviceRewriteNum int32                  `thrift:"AdviceRewriteNum,5,required" frugal:"5,required,i32" json:"AdviceRewriteNum"`
	NoAdviceSQLNum   int32                  `thrift:"NoAdviceSQLNum,6,required" frugal:"6,required,i32" json:"NoAdviceSQLNum"`
	Status           SlowQuerySummaryStatus `thrift:"Status,7,required" frugal:"7,required,SlowQuerySummaryStatus" json:"Status"`
	SummaryId        string                 `thrift:"SummaryId,8,required" frugal:"8,required,string" json:"SummaryId"`
	Db               string                 `thrift:"Db,9,required" frugal:"9,required,string" json:"Db"`
}

func NewSlowQueryAdviceTaskAgg() *SlowQueryAdviceTaskAgg {
	return &SlowQueryAdviceTaskAgg{}
}

func (p *SlowQueryAdviceTaskAgg) InitDefault() {
}

func (p *SlowQueryAdviceTaskAgg) GetDate() (v string) {
	return p.Date
}

func (p *SlowQueryAdviceTaskAgg) GetSlowQueryNum() (v int32) {
	return p.SlowQueryNum
}

func (p *SlowQueryAdviceTaskAgg) GetAdviceNum() (v int32) {
	return p.AdviceNum
}

func (p *SlowQueryAdviceTaskAgg) GetAdviceIndexNum() (v int32) {
	return p.AdviceIndexNum
}

func (p *SlowQueryAdviceTaskAgg) GetAdviceRewriteNum() (v int32) {
	return p.AdviceRewriteNum
}

func (p *SlowQueryAdviceTaskAgg) GetNoAdviceSQLNum() (v int32) {
	return p.NoAdviceSQLNum
}

func (p *SlowQueryAdviceTaskAgg) GetStatus() (v SlowQuerySummaryStatus) {
	return p.Status
}

func (p *SlowQueryAdviceTaskAgg) GetSummaryId() (v string) {
	return p.SummaryId
}

func (p *SlowQueryAdviceTaskAgg) GetDb() (v string) {
	return p.Db
}
func (p *SlowQueryAdviceTaskAgg) SetDate(val string) {
	p.Date = val
}
func (p *SlowQueryAdviceTaskAgg) SetSlowQueryNum(val int32) {
	p.SlowQueryNum = val
}
func (p *SlowQueryAdviceTaskAgg) SetAdviceNum(val int32) {
	p.AdviceNum = val
}
func (p *SlowQueryAdviceTaskAgg) SetAdviceIndexNum(val int32) {
	p.AdviceIndexNum = val
}
func (p *SlowQueryAdviceTaskAgg) SetAdviceRewriteNum(val int32) {
	p.AdviceRewriteNum = val
}
func (p *SlowQueryAdviceTaskAgg) SetNoAdviceSQLNum(val int32) {
	p.NoAdviceSQLNum = val
}
func (p *SlowQueryAdviceTaskAgg) SetStatus(val SlowQuerySummaryStatus) {
	p.Status = val
}
func (p *SlowQueryAdviceTaskAgg) SetSummaryId(val string) {
	p.SummaryId = val
}
func (p *SlowQueryAdviceTaskAgg) SetDb(val string) {
	p.Db = val
}

var fieldIDToName_SlowQueryAdviceTaskAgg = map[int16]string{
	1: "Date",
	2: "SlowQueryNum",
	3: "AdviceNum",
	4: "AdviceIndexNum",
	5: "AdviceRewriteNum",
	6: "NoAdviceSQLNum",
	7: "Status",
	8: "SummaryId",
	9: "Db",
}

func (p *SlowQueryAdviceTaskAgg) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskAgg")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDate bool = false
	var issetSlowQueryNum bool = false
	var issetAdviceNum bool = false
	var issetAdviceIndexNum bool = false
	var issetAdviceRewriteNum bool = false
	var issetNoAdviceSQLNum bool = false
	var issetStatus bool = false
	var issetSummaryId bool = false
	var issetDb bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowQueryNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceRewriteNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNoAdviceSQLNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetSummaryId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetDb = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDate {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSlowQueryNum {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceNum {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexNum {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetAdviceRewriteNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNoAdviceSQLNum {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetSummaryId {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetDb {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskAgg[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskAgg[fieldId]))
}

func (p *SlowQueryAdviceTaskAgg) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Date = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SlowQueryNum = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceNum = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceIndexNum = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceRewriteNum = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NoAdviceSQLNum = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField7(iprot thrift.TProtocol) error {

	var _field SlowQuerySummaryStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SlowQuerySummaryStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SummaryId = _field
	return nil
}
func (p *SlowQueryAdviceTaskAgg) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Db = _field
	return nil
}

func (p *SlowQueryAdviceTaskAgg) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskAgg")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceTaskAgg"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Date", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Date); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowQueryNum", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SlowQueryNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceNum", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AdviceNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceIndexNum", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AdviceIndexNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceRewriteNum", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AdviceRewriteNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NoAdviceSQLNum", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.NoAdviceSQLNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SummaryId", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SummaryId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Db", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Db); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskAgg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceTaskAgg(%+v)", *p)

}

func (p *SlowQueryAdviceTaskAgg) DeepEqual(ano *SlowQueryAdviceTaskAgg) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Date) {
		return false
	}
	if !p.Field2DeepEqual(ano.SlowQueryNum) {
		return false
	}
	if !p.Field3DeepEqual(ano.AdviceNum) {
		return false
	}
	if !p.Field4DeepEqual(ano.AdviceIndexNum) {
		return false
	}
	if !p.Field5DeepEqual(ano.AdviceRewriteNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.NoAdviceSQLNum) {
		return false
	}
	if !p.Field7DeepEqual(ano.Status) {
		return false
	}
	if !p.Field8DeepEqual(ano.SummaryId) {
		return false
	}
	if !p.Field9DeepEqual(ano.Db) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceTaskAgg) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Date, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field2DeepEqual(src int32) bool {

	if p.SlowQueryNum != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field3DeepEqual(src int32) bool {

	if p.AdviceNum != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field4DeepEqual(src int32) bool {

	if p.AdviceIndexNum != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field5DeepEqual(src int32) bool {

	if p.AdviceRewriteNum != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field6DeepEqual(src int32) bool {

	if p.NoAdviceSQLNum != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field7DeepEqual(src SlowQuerySummaryStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field8DeepEqual(src string) bool {

	if strings.Compare(p.SummaryId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskAgg) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Db, src) != 0 {
		return false
	}
	return true
}

type SlowQueryAdviceTaskHistoryReq struct {
	InstanceType     InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId         string       `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId       string       `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	PageNumber       int32        `thrift:"PageNumber,4,required" frugal:"4,required,i32" json:"PageNumber"`
	PageSize         int32        `thrift:"PageSize,5,required" frugal:"5,required,i32" json:"PageSize"`
	SqlDateTimeStamp *int64       `thrift:"SqlDateTimeStamp,6,optional" frugal:"6,optional,i64" json:"SqlDateTimeStamp,omitempty"`
}

func NewSlowQueryAdviceTaskHistoryReq() *SlowQueryAdviceTaskHistoryReq {
	return &SlowQueryAdviceTaskHistoryReq{}
}

func (p *SlowQueryAdviceTaskHistoryReq) InitDefault() {
}

func (p *SlowQueryAdviceTaskHistoryReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *SlowQueryAdviceTaskHistoryReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *SlowQueryAdviceTaskHistoryReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SlowQueryAdviceTaskHistoryReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *SlowQueryAdviceTaskHistoryReq) GetPageSize() (v int32) {
	return p.PageSize
}

var SlowQueryAdviceTaskHistoryReq_SqlDateTimeStamp_DEFAULT int64

func (p *SlowQueryAdviceTaskHistoryReq) GetSqlDateTimeStamp() (v int64) {
	if !p.IsSetSqlDateTimeStamp() {
		return SlowQueryAdviceTaskHistoryReq_SqlDateTimeStamp_DEFAULT
	}
	return *p.SqlDateTimeStamp
}
func (p *SlowQueryAdviceTaskHistoryReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *SlowQueryAdviceTaskHistoryReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *SlowQueryAdviceTaskHistoryReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SlowQueryAdviceTaskHistoryReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *SlowQueryAdviceTaskHistoryReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *SlowQueryAdviceTaskHistoryReq) SetSqlDateTimeStamp(val *int64) {
	p.SqlDateTimeStamp = val
}

var fieldIDToName_SlowQueryAdviceTaskHistoryReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceId",
	4: "PageNumber",
	5: "PageSize",
	6: "SqlDateTimeStamp",
}

func (p *SlowQueryAdviceTaskHistoryReq) IsSetSqlDateTimeStamp() bool {
	return p.SqlDateTimeStamp != nil
}

func (p *SlowQueryAdviceTaskHistoryReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryReq[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlDateTimeStamp = _field
	return nil
}

func (p *SlowQueryAdviceTaskHistoryReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceTaskHistoryReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlDateTimeStamp() {
		if err = oprot.WriteFieldBegin("SqlDateTimeStamp", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SqlDateTimeStamp); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceTaskHistoryReq(%+v)", *p)

}

func (p *SlowQueryAdviceTaskHistoryReq) DeepEqual(ano *SlowQueryAdviceTaskHistoryReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.SqlDateTimeStamp) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceTaskHistoryReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryReq) Field4DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryReq) Field5DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryReq) Field6DeepEqual(src *int64) bool {

	if p.SqlDateTimeStamp == src {
		return true
	} else if p.SqlDateTimeStamp == nil || src == nil {
		return false
	}
	if *p.SqlDateTimeStamp != *src {
		return false
	}
	return true
}

type SlowQueryAdviceTaskHistoryResp struct {
	Total   int32                     `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	ResList []*SlowQueryAdviceTaskAgg `thrift:"ResList,2,required" frugal:"2,required,list<SlowQueryAdviceTaskAgg>" json:"ResList"`
}

func NewSlowQueryAdviceTaskHistoryResp() *SlowQueryAdviceTaskHistoryResp {
	return &SlowQueryAdviceTaskHistoryResp{}
}

func (p *SlowQueryAdviceTaskHistoryResp) InitDefault() {
}

func (p *SlowQueryAdviceTaskHistoryResp) GetTotal() (v int32) {
	return p.Total
}

func (p *SlowQueryAdviceTaskHistoryResp) GetResList() (v []*SlowQueryAdviceTaskAgg) {
	return p.ResList
}
func (p *SlowQueryAdviceTaskHistoryResp) SetTotal(val int32) {
	p.Total = val
}
func (p *SlowQueryAdviceTaskHistoryResp) SetResList(val []*SlowQueryAdviceTaskAgg) {
	p.ResList = val
}

var fieldIDToName_SlowQueryAdviceTaskHistoryResp = map[int16]string{
	1: "Total",
	2: "ResList",
}

func (p *SlowQueryAdviceTaskHistoryResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetResList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResList {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryResp[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *SlowQueryAdviceTaskHistoryResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SlowQueryAdviceTaskAgg, 0, size)
	values := make([]SlowQueryAdviceTaskAgg, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResList = _field
	return nil
}

func (p *SlowQueryAdviceTaskHistoryResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SlowQueryAdviceTaskHistoryResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SlowQueryAdviceTaskHistoryResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResList", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResList)); err != nil {
		return err
	}
	for _, v := range p.ResList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SlowQueryAdviceTaskHistoryResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SlowQueryAdviceTaskHistoryResp(%+v)", *p)

}

func (p *SlowQueryAdviceTaskHistoryResp) DeepEqual(ano *SlowQueryAdviceTaskHistoryResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.ResList) {
		return false
	}
	return true
}

func (p *SlowQueryAdviceTaskHistoryResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *SlowQueryAdviceTaskHistoryResp) Field2DeepEqual(src []*SlowQueryAdviceTaskAgg) bool {

	if len(p.ResList) != len(src) {
		return false
	}
	for i, v := range p.ResList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ListSlowQueryAdviceReq struct {
	InstanceType InstanceType               `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId     string                     `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId   string                     `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	AdviceType   AdviceType                 `thrift:"AdviceType,4,required" frugal:"4,required,AdviceType" json:"AdviceType"`
	SummaryId    string                     `thrift:"SummaryId,5,required" frugal:"5,required,string" json:"SummaryId"`
	GroupBy      ListSlowQueryAdviceGroupBy `thrift:"GroupBy,6,required" frugal:"6,required,ListSlowQueryAdviceGroupBy" json:"GroupBy"`
	OrderBy      ListSlowQueryAdviceOrderBy `thrift:"OrderBy,7,required" frugal:"7,required,ListSlowQueryAdviceOrderBy" json:"OrderBy"`
	PageNumber   int32                      `thrift:"PageNumber,8,required" frugal:"8,required,i32" json:"PageNumber"`
	PageSize     int32                      `thrift:"PageSize,9,required" frugal:"9,required,i32" json:"PageSize"`
	AdviceLevel  []AdviceLevel              `thrift:"AdviceLevel,10,optional" frugal:"10,optional,list<AdviceLevel>" json:"AdviceLevel,omitempty"`
}

func NewListSlowQueryAdviceReq() *ListSlowQueryAdviceReq {
	return &ListSlowQueryAdviceReq{}
}

func (p *ListSlowQueryAdviceReq) InitDefault() {
}

func (p *ListSlowQueryAdviceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ListSlowQueryAdviceReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *ListSlowQueryAdviceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ListSlowQueryAdviceReq) GetAdviceType() (v AdviceType) {
	return p.AdviceType
}

func (p *ListSlowQueryAdviceReq) GetSummaryId() (v string) {
	return p.SummaryId
}

func (p *ListSlowQueryAdviceReq) GetGroupBy() (v ListSlowQueryAdviceGroupBy) {
	return p.GroupBy
}

func (p *ListSlowQueryAdviceReq) GetOrderBy() (v ListSlowQueryAdviceOrderBy) {
	return p.OrderBy
}

func (p *ListSlowQueryAdviceReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *ListSlowQueryAdviceReq) GetPageSize() (v int32) {
	return p.PageSize
}

var ListSlowQueryAdviceReq_AdviceLevel_DEFAULT []AdviceLevel

func (p *ListSlowQueryAdviceReq) GetAdviceLevel() (v []AdviceLevel) {
	if !p.IsSetAdviceLevel() {
		return ListSlowQueryAdviceReq_AdviceLevel_DEFAULT
	}
	return p.AdviceLevel
}
func (p *ListSlowQueryAdviceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ListSlowQueryAdviceReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ListSlowQueryAdviceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ListSlowQueryAdviceReq) SetAdviceType(val AdviceType) {
	p.AdviceType = val
}
func (p *ListSlowQueryAdviceReq) SetSummaryId(val string) {
	p.SummaryId = val
}
func (p *ListSlowQueryAdviceReq) SetGroupBy(val ListSlowQueryAdviceGroupBy) {
	p.GroupBy = val
}
func (p *ListSlowQueryAdviceReq) SetOrderBy(val ListSlowQueryAdviceOrderBy) {
	p.OrderBy = val
}
func (p *ListSlowQueryAdviceReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *ListSlowQueryAdviceReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *ListSlowQueryAdviceReq) SetAdviceLevel(val []AdviceLevel) {
	p.AdviceLevel = val
}

var fieldIDToName_ListSlowQueryAdviceReq = map[int16]string{
	1:  "InstanceType",
	2:  "RegionId",
	3:  "InstanceId",
	4:  "AdviceType",
	5:  "SummaryId",
	6:  "GroupBy",
	7:  "OrderBy",
	8:  "PageNumber",
	9:  "PageSize",
	10: "AdviceLevel",
}

func (p *ListSlowQueryAdviceReq) IsSetAdviceLevel() bool {
	return p.AdviceLevel != nil
}

func (p *ListSlowQueryAdviceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetAdviceType bool = false
	var issetSummaryId bool = false
	var issetGroupBy bool = false
	var issetOrderBy bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSummaryId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroupBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAdviceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSummaryId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetGroupBy {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceReq[fieldId]))
}

func (p *ListSlowQueryAdviceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AdviceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AdviceType(v)
	}
	p.AdviceType = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SummaryId = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field ListSlowQueryAdviceGroupBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ListSlowQueryAdviceGroupBy(v)
	}
	p.GroupBy = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field ListSlowQueryAdviceOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ListSlowQueryAdviceOrderBy(v)
	}
	p.OrderBy = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *ListSlowQueryAdviceReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]AdviceLevel, 0, size)
	for i := 0; i < size; i++ {

		var _elem AdviceLevel
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = AdviceLevel(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AdviceLevel = _field
	return nil
}

func (p *ListSlowQueryAdviceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AdviceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SummaryId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SummaryId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupBy", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.GroupBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdviceLevel() {
		if err = oprot.WriteFieldBegin("AdviceLevel", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdviceLevel)); err != nil {
			return err
		}
		for _, v := range p.AdviceLevel {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ListSlowQueryAdviceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceReq(%+v)", *p)

}

func (p *ListSlowQueryAdviceReq) DeepEqual(ano *ListSlowQueryAdviceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.AdviceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.SummaryId) {
		return false
	}
	if !p.Field6DeepEqual(ano.GroupBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field9DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field10DeepEqual(ano.AdviceLevel) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field4DeepEqual(src AdviceType) bool {

	if p.AdviceType != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SummaryId, src) != 0 {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field6DeepEqual(src ListSlowQueryAdviceGroupBy) bool {

	if p.GroupBy != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field7DeepEqual(src ListSlowQueryAdviceOrderBy) bool {

	if p.OrderBy != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field8DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field9DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceReq) Field10DeepEqual(src []AdviceLevel) bool {

	if len(p.AdviceLevel) != len(src) {
		return false
	}
	for i, v := range p.AdviceLevel {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}

type ListSlowQueryAdviceResp struct {
	Total          int32            `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	AdvicesByGroup []*AdviceByGroup `thrift:"AdvicesByGroup,2,optional" frugal:"2,optional,list<AdviceByGroup>" json:"AdvicesByGroup,omitempty"`
	Advices        []*Advice        `thrift:"Advices,3,optional" frugal:"3,optional,list<Advice>" json:"Advices,omitempty"`
}

func NewListSlowQueryAdviceResp() *ListSlowQueryAdviceResp {
	return &ListSlowQueryAdviceResp{}
}

func (p *ListSlowQueryAdviceResp) InitDefault() {
}

func (p *ListSlowQueryAdviceResp) GetTotal() (v int32) {
	return p.Total
}

var ListSlowQueryAdviceResp_AdvicesByGroup_DEFAULT []*AdviceByGroup

func (p *ListSlowQueryAdviceResp) GetAdvicesByGroup() (v []*AdviceByGroup) {
	if !p.IsSetAdvicesByGroup() {
		return ListSlowQueryAdviceResp_AdvicesByGroup_DEFAULT
	}
	return p.AdvicesByGroup
}

var ListSlowQueryAdviceResp_Advices_DEFAULT []*Advice

func (p *ListSlowQueryAdviceResp) GetAdvices() (v []*Advice) {
	if !p.IsSetAdvices() {
		return ListSlowQueryAdviceResp_Advices_DEFAULT
	}
	return p.Advices
}
func (p *ListSlowQueryAdviceResp) SetTotal(val int32) {
	p.Total = val
}
func (p *ListSlowQueryAdviceResp) SetAdvicesByGroup(val []*AdviceByGroup) {
	p.AdvicesByGroup = val
}
func (p *ListSlowQueryAdviceResp) SetAdvices(val []*Advice) {
	p.Advices = val
}

var fieldIDToName_ListSlowQueryAdviceResp = map[int16]string{
	1: "Total",
	2: "AdvicesByGroup",
	3: "Advices",
}

func (p *ListSlowQueryAdviceResp) IsSetAdvicesByGroup() bool {
	return p.AdvicesByGroup != nil
}

func (p *ListSlowQueryAdviceResp) IsSetAdvices() bool {
	return p.Advices != nil
}

func (p *ListSlowQueryAdviceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceResp[fieldId]))
}

func (p *ListSlowQueryAdviceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *ListSlowQueryAdviceResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AdviceByGroup, 0, size)
	values := make([]AdviceByGroup, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AdvicesByGroup = _field
	return nil
}
func (p *ListSlowQueryAdviceResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Advice, 0, size)
	values := make([]Advice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Advices = _field
	return nil
}

func (p *ListSlowQueryAdviceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListSlowQueryAdviceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlowQueryAdviceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListSlowQueryAdviceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListSlowQueryAdviceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdvicesByGroup() {
		if err = oprot.WriteFieldBegin("AdvicesByGroup", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdvicesByGroup)); err != nil {
			return err
		}
		for _, v := range p.AdvicesByGroup {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListSlowQueryAdviceResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdvices() {
		if err = oprot.WriteFieldBegin("Advices", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Advices)); err != nil {
			return err
		}
		for _, v := range p.Advices {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListSlowQueryAdviceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSlowQueryAdviceResp(%+v)", *p)

}

func (p *ListSlowQueryAdviceResp) DeepEqual(ano *ListSlowQueryAdviceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.AdvicesByGroup) {
		return false
	}
	if !p.Field3DeepEqual(ano.Advices) {
		return false
	}
	return true
}

func (p *ListSlowQueryAdviceResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *ListSlowQueryAdviceResp) Field2DeepEqual(src []*AdviceByGroup) bool {

	if len(p.AdvicesByGroup) != len(src) {
		return false
	}
	for i, v := range p.AdvicesByGroup {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ListSlowQueryAdviceResp) Field3DeepEqual(src []*Advice) bool {

	if len(p.Advices) != len(src) {
		return false
	}
	for i, v := range p.Advices {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AdviceByGroup struct {
	Advice  string    `thrift:"Advice,1,required" frugal:"1,required,string" json:"Advice"`
	Advices []*Advice `thrift:"advices,2,required" frugal:"2,required,list<Advice>" json:"advices"`
}

func NewAdviceByGroup() *AdviceByGroup {
	return &AdviceByGroup{}
}

func (p *AdviceByGroup) InitDefault() {
}

func (p *AdviceByGroup) GetAdvice() (v string) {
	return p.Advice
}

func (p *AdviceByGroup) GetAdvices() (v []*Advice) {
	return p.Advices
}
func (p *AdviceByGroup) SetAdvice(val string) {
	p.Advice = val
}
func (p *AdviceByGroup) SetAdvices(val []*Advice) {
	p.Advices = val
}

var fieldIDToName_AdviceByGroup = map[int16]string{
	1: "Advice",
	2: "advices",
}

func (p *AdviceByGroup) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AdviceByGroup")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAdvice bool = false
	var issetAdvices bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdvice = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdvices = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAdvice {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAdvices {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviceByGroup[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AdviceByGroup[fieldId]))
}

func (p *AdviceByGroup) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Advice = _field
	return nil
}
func (p *AdviceByGroup) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Advice, 0, size)
	values := make([]Advice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Advices = _field
	return nil
}

func (p *AdviceByGroup) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AdviceByGroup")

	var fieldId int16
	if err = oprot.WriteStructBegin("AdviceByGroup"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AdviceByGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Advice", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Advice); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AdviceByGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("advices", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Advices)); err != nil {
		return err
	}
	for _, v := range p.Advices {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AdviceByGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdviceByGroup(%+v)", *p)

}

func (p *AdviceByGroup) DeepEqual(ano *AdviceByGroup) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Advice) {
		return false
	}
	if !p.Field2DeepEqual(ano.Advices) {
		return false
	}
	return true
}

func (p *AdviceByGroup) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Advice, src) != 0 {
		return false
	}
	return true
}
func (p *AdviceByGroup) Field2DeepEqual(src []*Advice) bool {

	if len(p.Advices) != len(src) {
		return false
	}
	for i, v := range p.Advices {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type Advice struct {
	SQLModule         string            `thrift:"SQLModule,1,required" frugal:"1,required,string" json:"SQLModule"`
	Advice            string            `thrift:"Advice,2,required" frugal:"2,required,string" json:"Advice"`
	SpeedUp           float64           `thrift:"SpeedUp,3,required" frugal:"3,required,double" json:"SpeedUp"`
	DB                string            `thrift:"DB,4,required" frugal:"4,required,string" json:"DB"`
	TableName         string            `thrift:"TableName,5,required" frugal:"5,required,string" json:"TableName"`
	SourceIPs         []string          `thrift:"SourceIPs,6,required" frugal:"6,required,list<string>" json:"SourceIPs"`
	User              []string          `thrift:"User,7,required" frugal:"7,required,list<string>" json:"User"`
	QueryTimeAvgAfter float64           `thrift:"QueryTimeAvgAfter,12,required" frugal:"12,required,double" json:"QueryTimeAvgAfter"`
	AdviceIndexSize   float64           `thrift:"AdviceIndexSize,13,required" frugal:"13,required,double" json:"AdviceIndexSize"`
	Benefit           float64           `thrift:"Benefit,14,required" frugal:"14,required,double" json:"Benefit"`
	AdviceLevel       AdviceLevel       `thrift:"AdviceLevel,15,required" frugal:"15,required,AdviceLevel" json:"AdviceLevel"`
	Risk              string            `thrift:"Risk,17,required" frugal:"17,required,string" json:"Risk"`
	Agg               *AggregateSlowLog `thrift:"agg,18,required" frugal:"18,required,AggregateSlowLog" json:"agg"`
}

func NewAdvice() *Advice {
	return &Advice{}
}

func (p *Advice) InitDefault() {
}

func (p *Advice) GetSQLModule() (v string) {
	return p.SQLModule
}

func (p *Advice) GetAdvice() (v string) {
	return p.Advice
}

func (p *Advice) GetSpeedUp() (v float64) {
	return p.SpeedUp
}

func (p *Advice) GetDB() (v string) {
	return p.DB
}

func (p *Advice) GetTableName() (v string) {
	return p.TableName
}

func (p *Advice) GetSourceIPs() (v []string) {
	return p.SourceIPs
}

func (p *Advice) GetUser() (v []string) {
	return p.User
}

func (p *Advice) GetQueryTimeAvgAfter() (v float64) {
	return p.QueryTimeAvgAfter
}

func (p *Advice) GetAdviceIndexSize() (v float64) {
	return p.AdviceIndexSize
}

func (p *Advice) GetBenefit() (v float64) {
	return p.Benefit
}

func (p *Advice) GetAdviceLevel() (v AdviceLevel) {
	return p.AdviceLevel
}

func (p *Advice) GetRisk() (v string) {
	return p.Risk
}

var Advice_Agg_DEFAULT *AggregateSlowLog

func (p *Advice) GetAgg() (v *AggregateSlowLog) {
	if !p.IsSetAgg() {
		return Advice_Agg_DEFAULT
	}
	return p.Agg
}
func (p *Advice) SetSQLModule(val string) {
	p.SQLModule = val
}
func (p *Advice) SetAdvice(val string) {
	p.Advice = val
}
func (p *Advice) SetSpeedUp(val float64) {
	p.SpeedUp = val
}
func (p *Advice) SetDB(val string) {
	p.DB = val
}
func (p *Advice) SetTableName(val string) {
	p.TableName = val
}
func (p *Advice) SetSourceIPs(val []string) {
	p.SourceIPs = val
}
func (p *Advice) SetUser(val []string) {
	p.User = val
}
func (p *Advice) SetQueryTimeAvgAfter(val float64) {
	p.QueryTimeAvgAfter = val
}
func (p *Advice) SetAdviceIndexSize(val float64) {
	p.AdviceIndexSize = val
}
func (p *Advice) SetBenefit(val float64) {
	p.Benefit = val
}
func (p *Advice) SetAdviceLevel(val AdviceLevel) {
	p.AdviceLevel = val
}
func (p *Advice) SetRisk(val string) {
	p.Risk = val
}
func (p *Advice) SetAgg(val *AggregateSlowLog) {
	p.Agg = val
}

var fieldIDToName_Advice = map[int16]string{
	1:  "SQLModule",
	2:  "Advice",
	3:  "SpeedUp",
	4:  "DB",
	5:  "TableName",
	6:  "SourceIPs",
	7:  "User",
	12: "QueryTimeAvgAfter",
	13: "AdviceIndexSize",
	14: "Benefit",
	15: "AdviceLevel",
	17: "Risk",
	18: "agg",
}

func (p *Advice) IsSetAgg() bool {
	return p.Agg != nil
}

func (p *Advice) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Advice")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLModule bool = false
	var issetAdvice bool = false
	var issetSpeedUp bool = false
	var issetDB bool = false
	var issetTableName bool = false
	var issetSourceIPs bool = false
	var issetUser bool = false
	var issetQueryTimeAvgAfter bool = false
	var issetAdviceIndexSize bool = false
	var issetBenefit bool = false
	var issetAdviceLevel bool = false
	var issetRisk bool = false
	var issetAgg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLModule = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdvice = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSpeedUp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetSourceIPs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryTimeAvgAfter = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetBenefit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceLevel = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetRisk = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLModule {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAdvice {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSpeedUp {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSourceIPs {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUser {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetQueryTimeAvgAfter {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexSize {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetBenefit {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetAdviceLevel {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetRisk {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetAgg {
		fieldId = 18
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Advice[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Advice[fieldId]))
}

func (p *Advice) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLModule = _field
	return nil
}
func (p *Advice) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Advice = _field
	return nil
}
func (p *Advice) ReadField3(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpeedUp = _field
	return nil
}
func (p *Advice) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *Advice) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *Advice) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SourceIPs = _field
	return nil
}
func (p *Advice) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.User = _field
	return nil
}
func (p *Advice) ReadField12(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryTimeAvgAfter = _field
	return nil
}
func (p *Advice) ReadField13(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceIndexSize = _field
	return nil
}
func (p *Advice) ReadField14(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Benefit = _field
	return nil
}
func (p *Advice) ReadField15(iprot thrift.TProtocol) error {

	var _field AdviceLevel
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AdviceLevel(v)
	}
	p.AdviceLevel = _field
	return nil
}
func (p *Advice) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Risk = _field
	return nil
}
func (p *Advice) ReadField18(iprot thrift.TProtocol) error {
	_field := NewAggregateSlowLog()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Agg = _field
	return nil
}

func (p *Advice) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Advice")

	var fieldId int16
	if err = oprot.WriteStructBegin("Advice"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Advice) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLModule", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQLModule); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Advice) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Advice", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Advice); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Advice) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SpeedUp", thrift.DOUBLE, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.SpeedUp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Advice) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Advice) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Advice) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SourceIPs", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SourceIPs)); err != nil {
		return err
	}
	for _, v := range p.SourceIPs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Advice) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("User", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.User)); err != nil {
		return err
	}
	for _, v := range p.User {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Advice) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryTimeAvgAfter", thrift.DOUBLE, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.QueryTimeAvgAfter); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Advice) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceIndexSize", thrift.DOUBLE, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AdviceIndexSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Advice) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Benefit", thrift.DOUBLE, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Benefit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *Advice) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceLevel", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AdviceLevel)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *Advice) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Risk", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Risk); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *Advice) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("agg", thrift.STRUCT, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Agg.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *Advice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Advice(%+v)", *p)

}

func (p *Advice) DeepEqual(ano *Advice) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLModule) {
		return false
	}
	if !p.Field2DeepEqual(ano.Advice) {
		return false
	}
	if !p.Field3DeepEqual(ano.SpeedUp) {
		return false
	}
	if !p.Field4DeepEqual(ano.DB) {
		return false
	}
	if !p.Field5DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field6DeepEqual(ano.SourceIPs) {
		return false
	}
	if !p.Field7DeepEqual(ano.User) {
		return false
	}
	if !p.Field12DeepEqual(ano.QueryTimeAvgAfter) {
		return false
	}
	if !p.Field13DeepEqual(ano.AdviceIndexSize) {
		return false
	}
	if !p.Field14DeepEqual(ano.Benefit) {
		return false
	}
	if !p.Field15DeepEqual(ano.AdviceLevel) {
		return false
	}
	if !p.Field17DeepEqual(ano.Risk) {
		return false
	}
	if !p.Field18DeepEqual(ano.Agg) {
		return false
	}
	return true
}

func (p *Advice) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SQLModule, src) != 0 {
		return false
	}
	return true
}
func (p *Advice) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Advice, src) != 0 {
		return false
	}
	return true
}
func (p *Advice) Field3DeepEqual(src float64) bool {

	if p.SpeedUp != src {
		return false
	}
	return true
}
func (p *Advice) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *Advice) Field5DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *Advice) Field6DeepEqual(src []string) bool {

	if len(p.SourceIPs) != len(src) {
		return false
	}
	for i, v := range p.SourceIPs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *Advice) Field7DeepEqual(src []string) bool {

	if len(p.User) != len(src) {
		return false
	}
	for i, v := range p.User {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *Advice) Field12DeepEqual(src float64) bool {

	if p.QueryTimeAvgAfter != src {
		return false
	}
	return true
}
func (p *Advice) Field13DeepEqual(src float64) bool {

	if p.AdviceIndexSize != src {
		return false
	}
	return true
}
func (p *Advice) Field14DeepEqual(src float64) bool {

	if p.Benefit != src {
		return false
	}
	return true
}
func (p *Advice) Field15DeepEqual(src AdviceLevel) bool {

	if p.AdviceLevel != src {
		return false
	}
	return true
}
func (p *Advice) Field17DeepEqual(src string) bool {

	if strings.Compare(p.Risk, src) != 0 {
		return false
	}
	return true
}
func (p *Advice) Field18DeepEqual(src *AggregateSlowLog) bool {

	if !p.Agg.DeepEqual(src) {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type MetricDataItem int64

const (
	MetricDataItem_CpuUsage        MetricDataItem = 1
	MetricDataItem_MemUsage        MetricDataItem = 2
	MetricDataItem_DiskUsage       MetricDataItem = 3
	MetricDataItem_ConnectionRatio MetricDataItem = 4
)

func (p MetricDataItem) String() string {
	switch p {
	case MetricDataItem_CpuUsage:
		return "CpuUsage"
	case MetricDataItem_MemUsage:
		return "MemUsage"
	case MetricDataItem_DiskUsage:
		return "DiskUsage"
	case MetricDataItem_ConnectionRatio:
		return "ConnectionRatio"
	}
	return "<UNSET>"
}

func MetricDataItemFromString(s string) (MetricDataItem, error) {
	switch s {
	case "CpuUsage":
		return MetricDataItem_CpuUsage, nil
	case "MemUsage":
		return MetricDataItem_MemUsage, nil
	case "DiskUsage":
		return MetricDataItem_DiskUsage, nil
	case "ConnectionRatio":
		return MetricDataItem_ConnectionRatio, nil
	}
	return MetricDataItem(0), fmt.Errorf("not a valid MetricDataItem string")
}

func MetricDataItemPtr(v MetricDataItem) *MetricDataItem { return &v }

func (p MetricDataItem) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *MetricDataItem) UnmarshalText(text []byte) error {
	q, err := MetricDataItemFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeAbnormalDetectionConfigReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	RegionId     string       `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
}

func NewDescribeAbnormalDetectionConfigReq() *DescribeAbnormalDetectionConfigReq {
	return &DescribeAbnormalDetectionConfigReq{}
}

func (p *DescribeAbnormalDetectionConfigReq) InitDefault() {
}

func (p *DescribeAbnormalDetectionConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAbnormalDetectionConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAbnormalDetectionConfigReq) GetRegionId() (v string) {
	return p.RegionId
}
func (p *DescribeAbnormalDetectionConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAbnormalDetectionConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAbnormalDetectionConfigReq) SetRegionId(val string) {
	p.RegionId = val
}

var fieldIDToName_DescribeAbnormalDetectionConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "RegionId",
}

func (p *DescribeAbnormalDetectionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionConfigReq[fieldId]))
}

func (p *DescribeAbnormalDetectionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAbnormalDetectionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAbnormalDetectionConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeAbnormalDetectionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionConfigReq(%+v)", *p)

}

func (p *DescribeAbnormalDetectionConfigReq) DeepEqual(ano *DescribeAbnormalDetectionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionConfigReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}

type DescribeAbnormalDetectionConfigResp struct {
	Enable bool `thrift:"Enable,1,required" frugal:"1,required,bool" json:"Enable"`
}

func NewDescribeAbnormalDetectionConfigResp() *DescribeAbnormalDetectionConfigResp {
	return &DescribeAbnormalDetectionConfigResp{}
}

func (p *DescribeAbnormalDetectionConfigResp) InitDefault() {
}

func (p *DescribeAbnormalDetectionConfigResp) GetEnable() (v bool) {
	return p.Enable
}
func (p *DescribeAbnormalDetectionConfigResp) SetEnable(val bool) {
	p.Enable = val
}

var fieldIDToName_DescribeAbnormalDetectionConfigResp = map[int16]string{
	1: "Enable",
}

func (p *DescribeAbnormalDetectionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEnable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionConfigResp[fieldId]))
}

func (p *DescribeAbnormalDetectionConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}

func (p *DescribeAbnormalDetectionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionConfigResp(%+v)", *p)

}

func (p *DescribeAbnormalDetectionConfigResp) DeepEqual(ano *DescribeAbnormalDetectionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Enable) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionConfigResp) Field1DeepEqual(src bool) bool {

	if p.Enable != src {
		return false
	}
	return true
}

type ModifyAbnormalDetectionConfigReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	RegionId     string       `thrift:"RegionId,4,required" frugal:"4,required,string" json:"RegionId"`
	Enable       bool         `thrift:"Enable,3,required" frugal:"3,required,bool" json:"Enable"`
}

func NewModifyAbnormalDetectionConfigReq() *ModifyAbnormalDetectionConfigReq {
	return &ModifyAbnormalDetectionConfigReq{}
}

func (p *ModifyAbnormalDetectionConfigReq) InitDefault() {
}

func (p *ModifyAbnormalDetectionConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyAbnormalDetectionConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ModifyAbnormalDetectionConfigReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *ModifyAbnormalDetectionConfigReq) GetEnable() (v bool) {
	return p.Enable
}
func (p *ModifyAbnormalDetectionConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyAbnormalDetectionConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyAbnormalDetectionConfigReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ModifyAbnormalDetectionConfigReq) SetEnable(val bool) {
	p.Enable = val
}

var fieldIDToName_ModifyAbnormalDetectionConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	4: "RegionId",
	3: "Enable",
}

func (p *ModifyAbnormalDetectionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAbnormalDetectionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEnable {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAbnormalDetectionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyAbnormalDetectionConfigReq[fieldId]))
}

func (p *ModifyAbnormalDetectionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyAbnormalDetectionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyAbnormalDetectionConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ModifyAbnormalDetectionConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}

func (p *ModifyAbnormalDetectionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAbnormalDetectionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAbnormalDetectionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAbnormalDetectionConfigReq(%+v)", *p)

}

func (p *ModifyAbnormalDetectionConfigReq) DeepEqual(ano *ModifyAbnormalDetectionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Enable) {
		return false
	}
	return true
}

func (p *ModifyAbnormalDetectionConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAbnormalDetectionConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyAbnormalDetectionConfigReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAbnormalDetectionConfigReq) Field3DeepEqual(src bool) bool {

	if p.Enable != src {
		return false
	}
	return true
}

type ModifyAbnormalDetectionConfigResp struct {
}

func NewModifyAbnormalDetectionConfigResp() *ModifyAbnormalDetectionConfigResp {
	return &ModifyAbnormalDetectionConfigResp{}
}

func (p *ModifyAbnormalDetectionConfigResp) InitDefault() {
}

var fieldIDToName_ModifyAbnormalDetectionConfigResp = map[int16]string{}

func (p *ModifyAbnormalDetectionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAbnormalDetectionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAbnormalDetectionConfigResp")

	if err = oprot.WriteStructBegin("ModifyAbnormalDetectionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAbnormalDetectionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAbnormalDetectionConfigResp(%+v)", *p)

}

func (p *ModifyAbnormalDetectionConfigResp) DeepEqual(ano *ModifyAbnormalDetectionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeAbnormalDetectionDetailReq struct {
	InstanceId   string           `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType     `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	RegionId     string           `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	StartTime    string           `thrift:"StartTime,4,required" frugal:"4,required,string" json:"StartTime"`
	EndTime      string           `thrift:"EndTime,5,required" frugal:"5,required,string" json:"EndTime"`
	Items        []MetricDataItem `thrift:"Items,6,optional" frugal:"6,optional,list<MetricDataItem>" json:"Items,omitempty"`
}

func NewDescribeAbnormalDetectionDetailReq() *DescribeAbnormalDetectionDetailReq {
	return &DescribeAbnormalDetectionDetailReq{}
}

func (p *DescribeAbnormalDetectionDetailReq) InitDefault() {
}

func (p *DescribeAbnormalDetectionDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAbnormalDetectionDetailReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAbnormalDetectionDetailReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAbnormalDetectionDetailReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *DescribeAbnormalDetectionDetailReq) GetEndTime() (v string) {
	return p.EndTime
}

var DescribeAbnormalDetectionDetailReq_Items_DEFAULT []MetricDataItem

func (p *DescribeAbnormalDetectionDetailReq) GetItems() (v []MetricDataItem) {
	if !p.IsSetItems() {
		return DescribeAbnormalDetectionDetailReq_Items_DEFAULT
	}
	return p.Items
}
func (p *DescribeAbnormalDetectionDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAbnormalDetectionDetailReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAbnormalDetectionDetailReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAbnormalDetectionDetailReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *DescribeAbnormalDetectionDetailReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *DescribeAbnormalDetectionDetailReq) SetItems(val []MetricDataItem) {
	p.Items = val
}

var fieldIDToName_DescribeAbnormalDetectionDetailReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "RegionId",
	4: "StartTime",
	5: "EndTime",
	6: "Items",
}

func (p *DescribeAbnormalDetectionDetailReq) IsSetItems() bool {
	return p.Items != nil
}

func (p *DescribeAbnormalDetectionDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionDetailReq[fieldId]))
}

func (p *DescribeAbnormalDetectionDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAbnormalDetectionDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAbnormalDetectionDetailReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAbnormalDetectionDetailReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeAbnormalDetectionDetailReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeAbnormalDetectionDetailReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]MetricDataItem, 0, size)
	for i := 0; i < size; i++ {

		var _elem MetricDataItem
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = MetricDataItem(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}

func (p *DescribeAbnormalDetectionDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetItems() {
		if err = oprot.WriteFieldBegin("Items", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Items)); err != nil {
			return err
		}
		for _, v := range p.Items {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionDetailReq(%+v)", *p)

}

func (p *DescribeAbnormalDetectionDetailReq) DeepEqual(ano *DescribeAbnormalDetectionDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.Items) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionDetailReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionDetailReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionDetailReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionDetailReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionDetailReq) Field6DeepEqual(src []MetricDataItem) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}

type DescribeAbnormalDetectionDetailResp struct {
	ItemMetrics []*ItemMetric `thrift:"ItemMetrics,1,required" frugal:"1,required,list<ItemMetric>" json:"ItemMetrics"`
}

func NewDescribeAbnormalDetectionDetailResp() *DescribeAbnormalDetectionDetailResp {
	return &DescribeAbnormalDetectionDetailResp{}
}

func (p *DescribeAbnormalDetectionDetailResp) InitDefault() {
}

func (p *DescribeAbnormalDetectionDetailResp) GetItemMetrics() (v []*ItemMetric) {
	return p.ItemMetrics
}
func (p *DescribeAbnormalDetectionDetailResp) SetItemMetrics(val []*ItemMetric) {
	p.ItemMetrics = val
}

var fieldIDToName_DescribeAbnormalDetectionDetailResp = map[int16]string{
	1: "ItemMetrics",
}

func (p *DescribeAbnormalDetectionDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItemMetrics bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemMetrics = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItemMetrics {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionDetailResp[fieldId]))
}

func (p *DescribeAbnormalDetectionDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemMetric, 0, size)
	values := make([]ItemMetric, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemMetrics = _field
	return nil
}

func (p *DescribeAbnormalDetectionDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemMetrics", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemMetrics)); err != nil {
		return err
	}
	for _, v := range p.ItemMetrics {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionDetailResp(%+v)", *p)

}

func (p *DescribeAbnormalDetectionDetailResp) DeepEqual(ano *DescribeAbnormalDetectionDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ItemMetrics) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionDetailResp) Field1DeepEqual(src []*ItemMetric) bool {

	if len(p.ItemMetrics) != len(src) {
		return false
	}
	for i, v := range p.ItemMetrics {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ItemMetric struct {
	ItemName           string       `thrift:"ItemName,1,required" frugal:"1,required,string" json:"ItemName"`
	Group              string       `thrift:"Group,2,required" frugal:"2,required,string" json:"Group"`
	Unit               string       `thrift:"Unit,3,required" frugal:"3,required,string" json:"Unit"`
	DataPoints         []*DataPoint `thrift:"DataPoints,4,required" frugal:"4,required,list<DataPoint>" json:"DataPoints"`
	AbnormalDataPoints []*DataPoint `thrift:"AbnormalDataPoints,5,required" frugal:"5,required,list<DataPoint>" json:"AbnormalDataPoints"`
}

func NewItemMetric() *ItemMetric {
	return &ItemMetric{}
}

func (p *ItemMetric) InitDefault() {
}

func (p *ItemMetric) GetItemName() (v string) {
	return p.ItemName
}

func (p *ItemMetric) GetGroup() (v string) {
	return p.Group
}

func (p *ItemMetric) GetUnit() (v string) {
	return p.Unit
}

func (p *ItemMetric) GetDataPoints() (v []*DataPoint) {
	return p.DataPoints
}

func (p *ItemMetric) GetAbnormalDataPoints() (v []*DataPoint) {
	return p.AbnormalDataPoints
}
func (p *ItemMetric) SetItemName(val string) {
	p.ItemName = val
}
func (p *ItemMetric) SetGroup(val string) {
	p.Group = val
}
func (p *ItemMetric) SetUnit(val string) {
	p.Unit = val
}
func (p *ItemMetric) SetDataPoints(val []*DataPoint) {
	p.DataPoints = val
}
func (p *ItemMetric) SetAbnormalDataPoints(val []*DataPoint) {
	p.AbnormalDataPoints = val
}

var fieldIDToName_ItemMetric = map[int16]string{
	1: "ItemName",
	2: "Group",
	3: "Unit",
	4: "DataPoints",
	5: "AbnormalDataPoints",
}

func (p *ItemMetric) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ItemMetric")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItemName bool = false
	var issetGroup bool = false
	var issetUnit bool = false
	var issetDataPoints bool = false
	var issetAbnormalDataPoints bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroup = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetAbnormalDataPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItemName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetGroup {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataPoints {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetAbnormalDataPoints {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemMetric[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ItemMetric[fieldId]))
}

func (p *ItemMetric) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemName = _field
	return nil
}
func (p *ItemMetric) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Group = _field
	return nil
}
func (p *ItemMetric) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}
func (p *ItemMetric) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataPoint, 0, size)
	values := make([]DataPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataPoints = _field
	return nil
}
func (p *ItemMetric) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataPoint, 0, size)
	values := make([]DataPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AbnormalDataPoints = _field
	return nil
}

func (p *ItemMetric) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ItemMetric")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemMetric"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemMetric) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ItemMetric) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Group", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Group); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ItemMetric) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ItemMetric) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataPoints", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataPoints)); err != nil {
		return err
	}
	for _, v := range p.DataPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ItemMetric) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AbnormalDataPoints", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AbnormalDataPoints)); err != nil {
		return err
	}
	for _, v := range p.AbnormalDataPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ItemMetric) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemMetric(%+v)", *p)

}

func (p *ItemMetric) DeepEqual(ano *ItemMetric) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ItemName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Group) {
		return false
	}
	if !p.Field3DeepEqual(ano.Unit) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataPoints) {
		return false
	}
	if !p.Field5DeepEqual(ano.AbnormalDataPoints) {
		return false
	}
	return true
}

func (p *ItemMetric) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ItemName, src) != 0 {
		return false
	}
	return true
}
func (p *ItemMetric) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Group, src) != 0 {
		return false
	}
	return true
}
func (p *ItemMetric) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}
func (p *ItemMetric) Field4DeepEqual(src []*DataPoint) bool {

	if len(p.DataPoints) != len(src) {
		return false
	}
	for i, v := range p.DataPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ItemMetric) Field5DeepEqual(src []*DataPoint) bool {

	if len(p.AbnormalDataPoints) != len(src) {
		return false
	}
	for i, v := range p.AbnormalDataPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AbnormalPointDetail struct {
	ItemName    string  `thrift:"ItemName,1,required" frugal:"1,required,string" json:"ItemName"`
	Group       string  `thrift:"Group,2,required" frugal:"2,required,string" json:"Group"`
	TimeStamp   int32   `thrift:"TimeStamp,3,required" frugal:"3,required,i32" json:"TimeStamp"`
	Value       float64 `thrift:"Value,4,required" frugal:"4,required,double" json:"Value"`
	ErrorDetail string  `thrift:"ErrorDetail,5,required" frugal:"5,required,string" json:"ErrorDetail"`
}

func NewAbnormalPointDetail() *AbnormalPointDetail {
	return &AbnormalPointDetail{}
}

func (p *AbnormalPointDetail) InitDefault() {
}

func (p *AbnormalPointDetail) GetItemName() (v string) {
	return p.ItemName
}

func (p *AbnormalPointDetail) GetGroup() (v string) {
	return p.Group
}

func (p *AbnormalPointDetail) GetTimeStamp() (v int32) {
	return p.TimeStamp
}

func (p *AbnormalPointDetail) GetValue() (v float64) {
	return p.Value
}

func (p *AbnormalPointDetail) GetErrorDetail() (v string) {
	return p.ErrorDetail
}
func (p *AbnormalPointDetail) SetItemName(val string) {
	p.ItemName = val
}
func (p *AbnormalPointDetail) SetGroup(val string) {
	p.Group = val
}
func (p *AbnormalPointDetail) SetTimeStamp(val int32) {
	p.TimeStamp = val
}
func (p *AbnormalPointDetail) SetValue(val float64) {
	p.Value = val
}
func (p *AbnormalPointDetail) SetErrorDetail(val string) {
	p.ErrorDetail = val
}

var fieldIDToName_AbnormalPointDetail = map[int16]string{
	1: "ItemName",
	2: "Group",
	3: "TimeStamp",
	4: "Value",
	5: "ErrorDetail",
}

func (p *AbnormalPointDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AbnormalPointDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItemName bool = false
	var issetGroup bool = false
	var issetTimeStamp bool = false
	var issetValue bool = false
	var issetErrorDetail bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetGroup = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeStamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrorDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItemName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetGroup {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTimeStamp {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetErrorDetail {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AbnormalPointDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AbnormalPointDetail[fieldId]))
}

func (p *AbnormalPointDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemName = _field
	return nil
}
func (p *AbnormalPointDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Group = _field
	return nil
}
func (p *AbnormalPointDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeStamp = _field
	return nil
}
func (p *AbnormalPointDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *AbnormalPointDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorDetail = _field
	return nil
}

func (p *AbnormalPointDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AbnormalPointDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("AbnormalPointDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AbnormalPointDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AbnormalPointDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Group", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Group); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AbnormalPointDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeStamp", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TimeStamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AbnormalPointDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.DOUBLE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AbnormalPointDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrorDetail", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrorDetail); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AbnormalPointDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AbnormalPointDetail(%+v)", *p)

}

func (p *AbnormalPointDetail) DeepEqual(ano *AbnormalPointDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ItemName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Group) {
		return false
	}
	if !p.Field3DeepEqual(ano.TimeStamp) {
		return false
	}
	if !p.Field4DeepEqual(ano.Value) {
		return false
	}
	if !p.Field5DeepEqual(ano.ErrorDetail) {
		return false
	}
	return true
}

func (p *AbnormalPointDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ItemName, src) != 0 {
		return false
	}
	return true
}
func (p *AbnormalPointDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Group, src) != 0 {
		return false
	}
	return true
}
func (p *AbnormalPointDetail) Field3DeepEqual(src int32) bool {

	if p.TimeStamp != src {
		return false
	}
	return true
}
func (p *AbnormalPointDetail) Field4DeepEqual(src float64) bool {

	if p.Value != src {
		return false
	}
	return true
}
func (p *AbnormalPointDetail) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ErrorDetail, src) != 0 {
		return false
	}
	return true
}

type DescribeMetricDataItemDetailReq struct {
	InstanceType InstanceType   `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string         `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	Region       string         `thrift:"Region,3,required" frugal:"3,required,string" json:"Region"`
	TenantId     string         `thrift:"TenantId,4,required" frugal:"4,required,string" json:"TenantId"`
	StartTime    int32          `thrift:"StartTime,5,required" frugal:"5,required,i32" json:"StartTime"`
	EndTime      int32          `thrift:"EndTime,6,required" frugal:"6,required,i32" json:"EndTime"`
	Item         MetricDataItem `thrift:"Item,7,required" frugal:"7,required,MetricDataItem" json:"Item"`
}

func NewDescribeMetricDataItemDetailReq() *DescribeMetricDataItemDetailReq {
	return &DescribeMetricDataItemDetailReq{}
}

func (p *DescribeMetricDataItemDetailReq) InitDefault() {
}

func (p *DescribeMetricDataItemDetailReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeMetricDataItemDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeMetricDataItemDetailReq) GetRegion() (v string) {
	return p.Region
}

func (p *DescribeMetricDataItemDetailReq) GetTenantId() (v string) {
	return p.TenantId
}

func (p *DescribeMetricDataItemDetailReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeMetricDataItemDetailReq) GetEndTime() (v int32) {
	return p.EndTime
}

func (p *DescribeMetricDataItemDetailReq) GetItem() (v MetricDataItem) {
	return p.Item
}
func (p *DescribeMetricDataItemDetailReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeMetricDataItemDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeMetricDataItemDetailReq) SetRegion(val string) {
	p.Region = val
}
func (p *DescribeMetricDataItemDetailReq) SetTenantId(val string) {
	p.TenantId = val
}
func (p *DescribeMetricDataItemDetailReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeMetricDataItemDetailReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeMetricDataItemDetailReq) SetItem(val MetricDataItem) {
	p.Item = val
}

var fieldIDToName_DescribeMetricDataItemDetailReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Region",
	4: "TenantId",
	5: "StartTime",
	6: "EndTime",
	7: "Item",
}

func (p *DescribeMetricDataItemDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMetricDataItemDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetRegion bool = false
	var issetTenantId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetItem bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTenantId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetItem {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMetricDataItemDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMetricDataItemDetailReq[fieldId]))
}

func (p *DescribeMetricDataItemDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeMetricDataItemDetailReq) ReadField7(iprot thrift.TProtocol) error {

	var _field MetricDataItem
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = MetricDataItem(v)
	}
	p.Item = _field
	return nil
}

func (p *DescribeMetricDataItemDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMetricDataItemDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMetricDataItemDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Item", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Item)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMetricDataItemDetailReq(%+v)", *p)

}

func (p *DescribeMetricDataItemDetailReq) DeepEqual(ano *DescribeMetricDataItemDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Region) {
		return false
	}
	if !p.Field4DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.Item) {
		return false
	}
	return true
}

func (p *DescribeMetricDataItemDetailReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field5DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field6DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeMetricDataItemDetailReq) Field7DeepEqual(src MetricDataItem) bool {

	if p.Item != src {
		return false
	}
	return true
}

type DescribeMetricDataItemDetailResp struct {
	DataPoints []*DataPoint `thrift:"DataPoints,1,required" frugal:"1,required,list<DataPoint>" json:"DataPoints"`
}

func NewDescribeMetricDataItemDetailResp() *DescribeMetricDataItemDetailResp {
	return &DescribeMetricDataItemDetailResp{}
}

func (p *DescribeMetricDataItemDetailResp) InitDefault() {
}

func (p *DescribeMetricDataItemDetailResp) GetDataPoints() (v []*DataPoint) {
	return p.DataPoints
}
func (p *DescribeMetricDataItemDetailResp) SetDataPoints(val []*DataPoint) {
	p.DataPoints = val
}

var fieldIDToName_DescribeMetricDataItemDetailResp = map[int16]string{
	1: "DataPoints",
}

func (p *DescribeMetricDataItemDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMetricDataItemDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataPoints bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataPoints {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMetricDataItemDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMetricDataItemDetailResp[fieldId]))
}

func (p *DescribeMetricDataItemDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataPoint, 0, size)
	values := make([]DataPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataPoints = _field
	return nil
}

func (p *DescribeMetricDataItemDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMetricDataItemDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMetricDataItemDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataPoints", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataPoints)); err != nil {
		return err
	}
	for _, v := range p.DataPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMetricDataItemDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMetricDataItemDetailResp(%+v)", *p)

}

func (p *DescribeMetricDataItemDetailResp) DeepEqual(ano *DescribeMetricDataItemDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataPoints) {
		return false
	}
	return true
}

func (p *DescribeMetricDataItemDetailResp) Field1DeepEqual(src []*DataPoint) bool {

	if len(p.DataPoints) != len(src) {
		return false
	}
	for i, v := range p.DataPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeAbnormalDetectionInfoReq struct {
	InstanceId   string           `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType     `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	RegionId     string           `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	StartTime    string           `thrift:"StartTime,4,required" frugal:"4,required,string" json:"StartTime"`
	EndTime      string           `thrift:"EndTime,5,required" frugal:"5,required,string" json:"EndTime"`
	Items        []MetricDataItem `thrift:"Items,6,optional" frugal:"6,optional,list<MetricDataItem>" json:"Items,omitempty"`
	PageNumber   int32            `thrift:"PageNumber,7,required" frugal:"7,required,i32" json:"PageNumber"`
	PageSize     int32            `thrift:"PageSize,8,required" frugal:"8,required,i32" json:"PageSize"`
}

func NewDescribeAbnormalDetectionInfoReq() *DescribeAbnormalDetectionInfoReq {
	return &DescribeAbnormalDetectionInfoReq{}
}

func (p *DescribeAbnormalDetectionInfoReq) InitDefault() {
}

func (p *DescribeAbnormalDetectionInfoReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAbnormalDetectionInfoReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAbnormalDetectionInfoReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAbnormalDetectionInfoReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *DescribeAbnormalDetectionInfoReq) GetEndTime() (v string) {
	return p.EndTime
}

var DescribeAbnormalDetectionInfoReq_Items_DEFAULT []MetricDataItem

func (p *DescribeAbnormalDetectionInfoReq) GetItems() (v []MetricDataItem) {
	if !p.IsSetItems() {
		return DescribeAbnormalDetectionInfoReq_Items_DEFAULT
	}
	return p.Items
}

func (p *DescribeAbnormalDetectionInfoReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *DescribeAbnormalDetectionInfoReq) GetPageSize() (v int32) {
	return p.PageSize
}
func (p *DescribeAbnormalDetectionInfoReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetItems(val []MetricDataItem) {
	p.Items = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *DescribeAbnormalDetectionInfoReq) SetPageSize(val int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeAbnormalDetectionInfoReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "RegionId",
	4: "StartTime",
	5: "EndTime",
	6: "Items",
	7: "PageNumber",
	8: "PageSize",
}

func (p *DescribeAbnormalDetectionInfoReq) IsSetItems() bool {
	return p.Items != nil
}

func (p *DescribeAbnormalDetectionInfoReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionInfoReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionInfoReq[fieldId]))
}

func (p *DescribeAbnormalDetectionInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]MetricDataItem, 0, size)
	for i := 0; i < size; i++ {

		var _elem MetricDataItem
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = MetricDataItem(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeAbnormalDetectionInfoReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionInfoReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetItems() {
		if err = oprot.WriteFieldBegin("Items", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Items)); err != nil {
			return err
		}
		for _, v := range p.Items {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionInfoReq(%+v)", *p)

}

func (p *DescribeAbnormalDetectionInfoReq) DeepEqual(ano *DescribeAbnormalDetectionInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.Items) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionInfoReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field6DeepEqual(src []MetricDataItem) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field7DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoReq) Field8DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}

type DescribeAbnormalDetectionInfoResp struct {
	AbnormalDetails []*AbnormalPointDetail `thrift:"AbnormalDetails,1,required" frugal:"1,required,list<AbnormalPointDetail>" json:"AbnormalDetails"`
	Total           int32                  `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeAbnormalDetectionInfoResp() *DescribeAbnormalDetectionInfoResp {
	return &DescribeAbnormalDetectionInfoResp{}
}

func (p *DescribeAbnormalDetectionInfoResp) InitDefault() {
}

func (p *DescribeAbnormalDetectionInfoResp) GetAbnormalDetails() (v []*AbnormalPointDetail) {
	return p.AbnormalDetails
}

func (p *DescribeAbnormalDetectionInfoResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeAbnormalDetectionInfoResp) SetAbnormalDetails(val []*AbnormalPointDetail) {
	p.AbnormalDetails = val
}
func (p *DescribeAbnormalDetectionInfoResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeAbnormalDetectionInfoResp = map[int16]string{
	1: "AbnormalDetails",
	2: "Total",
}

func (p *DescribeAbnormalDetectionInfoResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionInfoResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAbnormalDetails bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAbnormalDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAbnormalDetails {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAbnormalDetectionInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAbnormalDetectionInfoResp[fieldId]))
}

func (p *DescribeAbnormalDetectionInfoResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AbnormalPointDetail, 0, size)
	values := make([]AbnormalPointDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AbnormalDetails = _field
	return nil
}
func (p *DescribeAbnormalDetectionInfoResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeAbnormalDetectionInfoResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAbnormalDetectionInfoResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAbnormalDetectionInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AbnormalDetails", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AbnormalDetails)); err != nil {
		return err
	}
	for _, v := range p.AbnormalDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAbnormalDetectionInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAbnormalDetectionInfoResp(%+v)", *p)

}

func (p *DescribeAbnormalDetectionInfoResp) DeepEqual(ano *DescribeAbnormalDetectionInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AbnormalDetails) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeAbnormalDetectionInfoResp) Field1DeepEqual(src []*AbnormalPointDetail) bool {

	if len(p.AbnormalDetails) != len(src) {
		return false
	}
	for i, v := range p.AbnormalDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeAbnormalDetectionInfoResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

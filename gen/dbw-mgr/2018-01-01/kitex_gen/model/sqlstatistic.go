// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type TimeElapse int64

const (
	TimeElapse_L1    TimeElapse = 1
	TimeElapse_L2    TimeElapse = 2
	TimeElapse_L3    TimeElapse = 3
	TimeElapse_L10   TimeElapse = 4
	TimeElapse_L100  TimeElapse = 5
	TimeElapse_L1000 TimeElapse = 6
	TimeElapse_G1000 TimeElapse = 7
	TimeElapse_TOTAL TimeElapse = 8
)

func (p TimeElapse) String() string {
	switch p {
	case TimeElapse_L1:
		return "L1"
	case TimeElapse_L2:
		return "L2"
	case TimeElapse_L3:
		return "L3"
	case TimeElapse_L10:
		return "L10"
	case TimeElapse_L100:
		return "L100"
	case TimeElapse_L1000:
		return "L1000"
	case TimeElapse_G1000:
		return "G1000"
	case TimeElapse_TOTAL:
		return "TOTAL"
	}
	return "<UNSET>"
}

func TimeElapseFromString(s string) (TimeElapse, error) {
	switch s {
	case "L1":
		return TimeElapse_L1, nil
	case "L2":
		return TimeElapse_L2, nil
	case "L3":
		return TimeElapse_L3, nil
	case "L10":
		return TimeElapse_L10, nil
	case "L100":
		return TimeElapse_L100, nil
	case "L1000":
		return TimeElapse_L1000, nil
	case "G1000":
		return TimeElapse_G1000, nil
	case "TOTAL":
		return TimeElapse_TOTAL, nil
	}
	return TimeElapse(0), fmt.Errorf("not a valid TimeElapse string")
}

func TimeElapsePtr(v TimeElapse) *TimeElapse { return &v }

func (p TimeElapse) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TimeElapse) UnmarshalText(text []byte) error {
	q, err := TimeElapseFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SQLTemplateStatisticOrderBy int64

const (
	SQLTemplateStatisticOrderBy_ExecCount     SQLTemplateStatisticOrderBy = 1
	SQLTemplateStatisticOrderBy_TimeElapseAVG SQLTemplateStatisticOrderBy = 2
	SQLTemplateStatisticOrderBy_TimeElapseSUM SQLTemplateStatisticOrderBy = 3
	SQLTemplateStatisticOrderBy_AffectRowAVG  SQLTemplateStatisticOrderBy = 4
	SQLTemplateStatisticOrderBy_ReturnRowAVG  SQLTemplateStatisticOrderBy = 5
)

func (p SQLTemplateStatisticOrderBy) String() string {
	switch p {
	case SQLTemplateStatisticOrderBy_ExecCount:
		return "ExecCount"
	case SQLTemplateStatisticOrderBy_TimeElapseAVG:
		return "TimeElapseAVG"
	case SQLTemplateStatisticOrderBy_TimeElapseSUM:
		return "TimeElapseSUM"
	case SQLTemplateStatisticOrderBy_AffectRowAVG:
		return "AffectRowAVG"
	case SQLTemplateStatisticOrderBy_ReturnRowAVG:
		return "ReturnRowAVG"
	}
	return "<UNSET>"
}

func SQLTemplateStatisticOrderByFromString(s string) (SQLTemplateStatisticOrderBy, error) {
	switch s {
	case "ExecCount":
		return SQLTemplateStatisticOrderBy_ExecCount, nil
	case "TimeElapseAVG":
		return SQLTemplateStatisticOrderBy_TimeElapseAVG, nil
	case "TimeElapseSUM":
		return SQLTemplateStatisticOrderBy_TimeElapseSUM, nil
	case "AffectRowAVG":
		return SQLTemplateStatisticOrderBy_AffectRowAVG, nil
	case "ReturnRowAVG":
		return SQLTemplateStatisticOrderBy_ReturnRowAVG, nil
	}
	return SQLTemplateStatisticOrderBy(0), fmt.Errorf("not a valid SQLTemplateStatisticOrderBy string")
}

func SQLTemplateStatisticOrderByPtr(v SQLTemplateStatisticOrderBy) *SQLTemplateStatisticOrderBy {
	return &v
}

func (p SQLTemplateStatisticOrderBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SQLTemplateStatisticOrderBy) UnmarshalText(text []byte) error {
	q, err := SQLTemplateStatisticOrderByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type MetricsType int64

const (
	MetricsType_ExecCount     MetricsType = 1
	MetricsType_TimeElapseAVG MetricsType = 2
	MetricsType_TimeElapseSUM MetricsType = 3
	MetricsType_AffectRowAVG  MetricsType = 4
	MetricsType_ReturnRowAVG  MetricsType = 5
)

func (p MetricsType) String() string {
	switch p {
	case MetricsType_ExecCount:
		return "ExecCount"
	case MetricsType_TimeElapseAVG:
		return "TimeElapseAVG"
	case MetricsType_TimeElapseSUM:
		return "TimeElapseSUM"
	case MetricsType_AffectRowAVG:
		return "AffectRowAVG"
	case MetricsType_ReturnRowAVG:
		return "ReturnRowAVG"
	}
	return "<UNSET>"
}

func MetricsTypeFromString(s string) (MetricsType, error) {
	switch s {
	case "ExecCount":
		return MetricsType_ExecCount, nil
	case "TimeElapseAVG":
		return MetricsType_TimeElapseAVG, nil
	case "TimeElapseSUM":
		return MetricsType_TimeElapseSUM, nil
	case "AffectRowAVG":
		return MetricsType_AffectRowAVG, nil
	case "ReturnRowAVG":
		return MetricsType_ReturnRowAVG, nil
	}
	return MetricsType(0), fmt.Errorf("not a valid MetricsType string")
}

func MetricsTypePtr(v MetricsType) *MetricsType { return &v }

func (p MetricsType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *MetricsType) UnmarshalText(text []byte) error {
	q, err := MetricsTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type StatisticDataType int64

const (
	StatisticDataType_SqlDetail            StatisticDataType = 1
	StatisticDataType_SqlExecCount         StatisticDataType = 2
	StatisticDataType_SqlTemplateAggregate StatisticDataType = 3
	StatisticDataType_FullSqlDetail        StatisticDataType = 4
	StatisticDataType_FullSqlTemplateAggr  StatisticDataType = 5
	StatisticDataType_FullSqlExecCount     StatisticDataType = 6
	StatisticDataType_FullSqlTableAggr     StatisticDataType = 7
	StatisticDataType_FullSqlTopSql        StatisticDataType = 8
	StatisticDataType_SlowLog              StatisticDataType = 9
)

func (p StatisticDataType) String() string {
	switch p {
	case StatisticDataType_SqlDetail:
		return "SqlDetail"
	case StatisticDataType_SqlExecCount:
		return "SqlExecCount"
	case StatisticDataType_SqlTemplateAggregate:
		return "SqlTemplateAggregate"
	case StatisticDataType_FullSqlDetail:
		return "FullSqlDetail"
	case StatisticDataType_FullSqlTemplateAggr:
		return "FullSqlTemplateAggr"
	case StatisticDataType_FullSqlExecCount:
		return "FullSqlExecCount"
	case StatisticDataType_FullSqlTableAggr:
		return "FullSqlTableAggr"
	case StatisticDataType_FullSqlTopSql:
		return "FullSqlTopSql"
	case StatisticDataType_SlowLog:
		return "SlowLog"
	}
	return "<UNSET>"
}

func StatisticDataTypeFromString(s string) (StatisticDataType, error) {
	switch s {
	case "SqlDetail":
		return StatisticDataType_SqlDetail, nil
	case "SqlExecCount":
		return StatisticDataType_SqlExecCount, nil
	case "SqlTemplateAggregate":
		return StatisticDataType_SqlTemplateAggregate, nil
	case "FullSqlDetail":
		return StatisticDataType_FullSqlDetail, nil
	case "FullSqlTemplateAggr":
		return StatisticDataType_FullSqlTemplateAggr, nil
	case "FullSqlExecCount":
		return StatisticDataType_FullSqlExecCount, nil
	case "FullSqlTableAggr":
		return StatisticDataType_FullSqlTableAggr, nil
	case "FullSqlTopSql":
		return StatisticDataType_FullSqlTopSql, nil
	case "SlowLog":
		return StatisticDataType_SlowLog, nil
	}
	return StatisticDataType(0), fmt.Errorf("not a valid StatisticDataType string")
}

func StatisticDataTypePtr(v StatisticDataType) *StatisticDataType { return &v }

func (p StatisticDataType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *StatisticDataType) UnmarshalText(text []byte) error {
	q, err := StatisticDataTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ScheduleSqlTaskType int64

const (
	ScheduleSqlTaskType_SqlTemplateAggregate ScheduleSqlTaskType = 1
	ScheduleSqlTaskType_SqlExecTotolCount    ScheduleSqlTaskType = 2
	ScheduleSqlTaskType_SqlExecL1            ScheduleSqlTaskType = 3
	ScheduleSqlTaskType_SqlExecL2            ScheduleSqlTaskType = 4
	ScheduleSqlTaskType_SqlExecL3            ScheduleSqlTaskType = 5
	ScheduleSqlTaskType_SqlExecL10           ScheduleSqlTaskType = 6
	ScheduleSqlTaskType_SqlExecL100          ScheduleSqlTaskType = 7
	ScheduleSqlTaskType_SqlExecL1000         ScheduleSqlTaskType = 8
	ScheduleSqlTaskType_SqlExecG1000         ScheduleSqlTaskType = 9
	ScheduleSqlTaskType_SqlExecDistribution  ScheduleSqlTaskType = 10
)

func (p ScheduleSqlTaskType) String() string {
	switch p {
	case ScheduleSqlTaskType_SqlTemplateAggregate:
		return "SqlTemplateAggregate"
	case ScheduleSqlTaskType_SqlExecTotolCount:
		return "SqlExecTotolCount"
	case ScheduleSqlTaskType_SqlExecL1:
		return "SqlExecL1"
	case ScheduleSqlTaskType_SqlExecL2:
		return "SqlExecL2"
	case ScheduleSqlTaskType_SqlExecL3:
		return "SqlExecL3"
	case ScheduleSqlTaskType_SqlExecL10:
		return "SqlExecL10"
	case ScheduleSqlTaskType_SqlExecL100:
		return "SqlExecL100"
	case ScheduleSqlTaskType_SqlExecL1000:
		return "SqlExecL1000"
	case ScheduleSqlTaskType_SqlExecG1000:
		return "SqlExecG1000"
	case ScheduleSqlTaskType_SqlExecDistribution:
		return "SqlExecDistribution"
	}
	return "<UNSET>"
}

func ScheduleSqlTaskTypeFromString(s string) (ScheduleSqlTaskType, error) {
	switch s {
	case "SqlTemplateAggregate":
		return ScheduleSqlTaskType_SqlTemplateAggregate, nil
	case "SqlExecTotolCount":
		return ScheduleSqlTaskType_SqlExecTotolCount, nil
	case "SqlExecL1":
		return ScheduleSqlTaskType_SqlExecL1, nil
	case "SqlExecL2":
		return ScheduleSqlTaskType_SqlExecL2, nil
	case "SqlExecL3":
		return ScheduleSqlTaskType_SqlExecL3, nil
	case "SqlExecL10":
		return ScheduleSqlTaskType_SqlExecL10, nil
	case "SqlExecL100":
		return ScheduleSqlTaskType_SqlExecL100, nil
	case "SqlExecL1000":
		return ScheduleSqlTaskType_SqlExecL1000, nil
	case "SqlExecG1000":
		return ScheduleSqlTaskType_SqlExecG1000, nil
	case "SqlExecDistribution":
		return ScheduleSqlTaskType_SqlExecDistribution, nil
	}
	return ScheduleSqlTaskType(0), fmt.Errorf("not a valid ScheduleSqlTaskType string")
}

func ScheduleSqlTaskTypePtr(v ScheduleSqlTaskType) *ScheduleSqlTaskType { return &v }

func (p ScheduleSqlTaskType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ScheduleSqlTaskType) UnmarshalText(text []byte) error {
	q, err := ScheduleSqlTaskTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeSQLTimeElapseDistributionReq struct {
	InstanceID string `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	DSType     DSType `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	StartTime  int32  `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime    int32  `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
}

func NewDescribeSQLTimeElapseDistributionReq() *DescribeSQLTimeElapseDistributionReq {
	return &DescribeSQLTimeElapseDistributionReq{}
}

func (p *DescribeSQLTimeElapseDistributionReq) InitDefault() {
}

func (p *DescribeSQLTimeElapseDistributionReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSQLTimeElapseDistributionReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSQLTimeElapseDistributionReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSQLTimeElapseDistributionReq) GetEndTime() (v int32) {
	return p.EndTime
}
func (p *DescribeSQLTimeElapseDistributionReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLTimeElapseDistributionReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSQLTimeElapseDistributionReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSQLTimeElapseDistributionReq) SetEndTime(val int32) {
	p.EndTime = val
}

var fieldIDToName_DescribeSQLTimeElapseDistributionReq = map[int16]string{
	1: "InstanceID",
	2: "DSType",
	3: "StartTime",
	4: "EndTime",
}

func (p *DescribeSQLTimeElapseDistributionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseDistributionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTimeElapseDistributionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTimeElapseDistributionReq[fieldId]))
}

func (p *DescribeSQLTimeElapseDistributionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLTimeElapseDistributionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSQLTimeElapseDistributionReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSQLTimeElapseDistributionReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *DescribeSQLTimeElapseDistributionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseDistributionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTimeElapseDistributionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTimeElapseDistributionReq(%+v)", *p)

}

func (p *DescribeSQLTimeElapseDistributionReq) DeepEqual(ano *DescribeSQLTimeElapseDistributionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *DescribeSQLTimeElapseDistributionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseDistributionReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseDistributionReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseDistributionReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}

type DescribeSQLTimeElapseDistributionResp struct {
	TimeElapseDistributions []*TimeElapseDistribution `thrift:"TimeElapseDistributions,1,required" frugal:"1,required,list<TimeElapseDistribution>" json:"TimeElapseDistributions"`
}

func NewDescribeSQLTimeElapseDistributionResp() *DescribeSQLTimeElapseDistributionResp {
	return &DescribeSQLTimeElapseDistributionResp{}
}

func (p *DescribeSQLTimeElapseDistributionResp) InitDefault() {
}

func (p *DescribeSQLTimeElapseDistributionResp) GetTimeElapseDistributions() (v []*TimeElapseDistribution) {
	return p.TimeElapseDistributions
}
func (p *DescribeSQLTimeElapseDistributionResp) SetTimeElapseDistributions(val []*TimeElapseDistribution) {
	p.TimeElapseDistributions = val
}

var fieldIDToName_DescribeSQLTimeElapseDistributionResp = map[int16]string{
	1: "TimeElapseDistributions",
}

func (p *DescribeSQLTimeElapseDistributionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseDistributionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimeElapseDistributions bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeElapseDistributions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimeElapseDistributions {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTimeElapseDistributionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTimeElapseDistributionResp[fieldId]))
}

func (p *DescribeSQLTimeElapseDistributionResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TimeElapseDistribution, 0, size)
	values := make([]TimeElapseDistribution, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TimeElapseDistributions = _field
	return nil
}

func (p *DescribeSQLTimeElapseDistributionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseDistributionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTimeElapseDistributionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeElapseDistributions", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TimeElapseDistributions)); err != nil {
		return err
	}
	for _, v := range p.TimeElapseDistributions {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseDistributionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTimeElapseDistributionResp(%+v)", *p)

}

func (p *DescribeSQLTimeElapseDistributionResp) DeepEqual(ano *DescribeSQLTimeElapseDistributionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TimeElapseDistributions) {
		return false
	}
	return true
}

func (p *DescribeSQLTimeElapseDistributionResp) Field1DeepEqual(src []*TimeElapseDistribution) bool {

	if len(p.TimeElapseDistributions) != len(src) {
		return false
	}
	for i, v := range p.TimeElapseDistributions {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type TimeElapseDistribution struct {
	Timestamp    int32                `thrift:"Timestamp,1,required" frugal:"1,required,i32" json:"Timestamp"`
	Distribution []*TimeElapseMetrics `thrift:"Distribution,2,required" frugal:"2,required,list<TimeElapseMetrics>" json:"Distribution"`
}

func NewTimeElapseDistribution() *TimeElapseDistribution {
	return &TimeElapseDistribution{}
}

func (p *TimeElapseDistribution) InitDefault() {
}

func (p *TimeElapseDistribution) GetTimestamp() (v int32) {
	return p.Timestamp
}

func (p *TimeElapseDistribution) GetDistribution() (v []*TimeElapseMetrics) {
	return p.Distribution
}
func (p *TimeElapseDistribution) SetTimestamp(val int32) {
	p.Timestamp = val
}
func (p *TimeElapseDistribution) SetDistribution(val []*TimeElapseMetrics) {
	p.Distribution = val
}

var fieldIDToName_TimeElapseDistribution = map[int16]string{
	1: "Timestamp",
	2: "Distribution",
}

func (p *TimeElapseDistribution) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeElapseDistribution")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimestamp bool = false
	var issetDistribution bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimestamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDistribution = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimestamp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDistribution {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TimeElapseDistribution[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TimeElapseDistribution[fieldId]))
}

func (p *TimeElapseDistribution) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Timestamp = _field
	return nil
}
func (p *TimeElapseDistribution) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TimeElapseMetrics, 0, size)
	values := make([]TimeElapseMetrics, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Distribution = _field
	return nil
}

func (p *TimeElapseDistribution) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeElapseDistribution")

	var fieldId int16
	if err = oprot.WriteStructBegin("TimeElapseDistribution"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TimeElapseDistribution) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Timestamp", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Timestamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TimeElapseDistribution) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Distribution", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Distribution)); err != nil {
		return err
	}
	for _, v := range p.Distribution {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TimeElapseDistribution) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeElapseDistribution(%+v)", *p)

}

func (p *TimeElapseDistribution) DeepEqual(ano *TimeElapseDistribution) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Timestamp) {
		return false
	}
	if !p.Field2DeepEqual(ano.Distribution) {
		return false
	}
	return true
}

func (p *TimeElapseDistribution) Field1DeepEqual(src int32) bool {

	if p.Timestamp != src {
		return false
	}
	return true
}
func (p *TimeElapseDistribution) Field2DeepEqual(src []*TimeElapseMetrics) bool {

	if len(p.Distribution) != len(src) {
		return false
	}
	for i, v := range p.Distribution {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type TimeElapseMetrics struct {
	TimeElapse TimeElapse `thrift:"TimeElapse,1,required" frugal:"1,required,TimeElapse" json:"TimeElapse"`
	Value      float64    `thrift:"Value,2,required" frugal:"2,required,double" json:"Value"`
	Unit       string     `thrift:"Unit,3,required" frugal:"3,required,string" json:"Unit"`
}

func NewTimeElapseMetrics() *TimeElapseMetrics {
	return &TimeElapseMetrics{}
}

func (p *TimeElapseMetrics) InitDefault() {
}

func (p *TimeElapseMetrics) GetTimeElapse() (v TimeElapse) {
	return p.TimeElapse
}

func (p *TimeElapseMetrics) GetValue() (v float64) {
	return p.Value
}

func (p *TimeElapseMetrics) GetUnit() (v string) {
	return p.Unit
}
func (p *TimeElapseMetrics) SetTimeElapse(val TimeElapse) {
	p.TimeElapse = val
}
func (p *TimeElapseMetrics) SetValue(val float64) {
	p.Value = val
}
func (p *TimeElapseMetrics) SetUnit(val string) {
	p.Unit = val
}

var fieldIDToName_TimeElapseMetrics = map[int16]string{
	1: "TimeElapse",
	2: "Value",
	3: "Unit",
}

func (p *TimeElapseMetrics) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeElapseMetrics")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimeElapse bool = false
	var issetValue bool = false
	var issetUnit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeElapse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimeElapse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TimeElapseMetrics[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TimeElapseMetrics[fieldId]))
}

func (p *TimeElapseMetrics) ReadField1(iprot thrift.TProtocol) error {

	var _field TimeElapse
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TimeElapse(v)
	}
	p.TimeElapse = _field
	return nil
}
func (p *TimeElapseMetrics) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *TimeElapseMetrics) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}

func (p *TimeElapseMetrics) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeElapseMetrics")

	var fieldId int16
	if err = oprot.WriteStructBegin("TimeElapseMetrics"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TimeElapseMetrics) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeElapse", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TimeElapse)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TimeElapseMetrics) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TimeElapseMetrics) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TimeElapseMetrics) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeElapseMetrics(%+v)", *p)

}

func (p *TimeElapseMetrics) DeepEqual(ano *TimeElapseMetrics) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TimeElapse) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Unit) {
		return false
	}
	return true
}

func (p *TimeElapseMetrics) Field1DeepEqual(src TimeElapse) bool {

	if p.TimeElapse != src {
		return false
	}
	return true
}
func (p *TimeElapseMetrics) Field2DeepEqual(src float64) bool {

	if p.Value != src {
		return false
	}
	return true
}
func (p *TimeElapseMetrics) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}

type DescribeSQLTimeElapseTotalReq struct {
	InstanceID string `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	DSType     DSType `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	StartTime  int32  `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime    int32  `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
}

func NewDescribeSQLTimeElapseTotalReq() *DescribeSQLTimeElapseTotalReq {
	return &DescribeSQLTimeElapseTotalReq{}
}

func (p *DescribeSQLTimeElapseTotalReq) InitDefault() {
}

func (p *DescribeSQLTimeElapseTotalReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSQLTimeElapseTotalReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSQLTimeElapseTotalReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSQLTimeElapseTotalReq) GetEndTime() (v int32) {
	return p.EndTime
}
func (p *DescribeSQLTimeElapseTotalReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLTimeElapseTotalReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSQLTimeElapseTotalReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSQLTimeElapseTotalReq) SetEndTime(val int32) {
	p.EndTime = val
}

var fieldIDToName_DescribeSQLTimeElapseTotalReq = map[int16]string{
	1: "InstanceID",
	2: "DSType",
	3: "StartTime",
	4: "EndTime",
}

func (p *DescribeSQLTimeElapseTotalReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseTotalReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTimeElapseTotalReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTimeElapseTotalReq[fieldId]))
}

func (p *DescribeSQLTimeElapseTotalReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLTimeElapseTotalReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSQLTimeElapseTotalReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSQLTimeElapseTotalReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *DescribeSQLTimeElapseTotalReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseTotalReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTimeElapseTotalReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTimeElapseTotalReq(%+v)", *p)

}

func (p *DescribeSQLTimeElapseTotalReq) DeepEqual(ano *DescribeSQLTimeElapseTotalReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *DescribeSQLTimeElapseTotalReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseTotalReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseTotalReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLTimeElapseTotalReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}

type DescribeSQLTimeElapseTotalResp struct {
	SQLTimeElapseTotals []*SQLTimeElapseTotal `thrift:"SQLTimeElapseTotals,1,required" frugal:"1,required,list<SQLTimeElapseTotal>" json:"SQLTimeElapseTotals"`
}

func NewDescribeSQLTimeElapseTotalResp() *DescribeSQLTimeElapseTotalResp {
	return &DescribeSQLTimeElapseTotalResp{}
}

func (p *DescribeSQLTimeElapseTotalResp) InitDefault() {
}

func (p *DescribeSQLTimeElapseTotalResp) GetSQLTimeElapseTotals() (v []*SQLTimeElapseTotal) {
	return p.SQLTimeElapseTotals
}
func (p *DescribeSQLTimeElapseTotalResp) SetSQLTimeElapseTotals(val []*SQLTimeElapseTotal) {
	p.SQLTimeElapseTotals = val
}

var fieldIDToName_DescribeSQLTimeElapseTotalResp = map[int16]string{
	1: "SQLTimeElapseTotals",
}

func (p *DescribeSQLTimeElapseTotalResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseTotalResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLTimeElapseTotals bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLTimeElapseTotals = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLTimeElapseTotals {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTimeElapseTotalResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTimeElapseTotalResp[fieldId]))
}

func (p *DescribeSQLTimeElapseTotalResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLTimeElapseTotal, 0, size)
	values := make([]SQLTimeElapseTotal, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLTimeElapseTotals = _field
	return nil
}

func (p *DescribeSQLTimeElapseTotalResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTimeElapseTotalResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTimeElapseTotalResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLTimeElapseTotals", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SQLTimeElapseTotals)); err != nil {
		return err
	}
	for _, v := range p.SQLTimeElapseTotals {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTimeElapseTotalResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTimeElapseTotalResp(%+v)", *p)

}

func (p *DescribeSQLTimeElapseTotalResp) DeepEqual(ano *DescribeSQLTimeElapseTotalResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLTimeElapseTotals) {
		return false
	}
	return true
}

func (p *DescribeSQLTimeElapseTotalResp) Field1DeepEqual(src []*SQLTimeElapseTotal) bool {

	if len(p.SQLTimeElapseTotals) != len(src) {
		return false
	}
	for i, v := range p.SQLTimeElapseTotals {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SQLTimeElapseTotal struct {
	Timestamp int32   `thrift:"Timestamp,1,required" frugal:"1,required,i32" json:"Timestamp"`
	Value     float64 `thrift:"Value,2,required" frugal:"2,required,double" json:"Value"`
	Unit      string  `thrift:"Unit,3,required" frugal:"3,required,string" json:"Unit"`
}

func NewSQLTimeElapseTotal() *SQLTimeElapseTotal {
	return &SQLTimeElapseTotal{}
}

func (p *SQLTimeElapseTotal) InitDefault() {
}

func (p *SQLTimeElapseTotal) GetTimestamp() (v int32) {
	return p.Timestamp
}

func (p *SQLTimeElapseTotal) GetValue() (v float64) {
	return p.Value
}

func (p *SQLTimeElapseTotal) GetUnit() (v string) {
	return p.Unit
}
func (p *SQLTimeElapseTotal) SetTimestamp(val int32) {
	p.Timestamp = val
}
func (p *SQLTimeElapseTotal) SetValue(val float64) {
	p.Value = val
}
func (p *SQLTimeElapseTotal) SetUnit(val string) {
	p.Unit = val
}

var fieldIDToName_SQLTimeElapseTotal = map[int16]string{
	1: "Timestamp",
	2: "Value",
	3: "Unit",
}

func (p *SQLTimeElapseTotal) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLTimeElapseTotal")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimestamp bool = false
	var issetValue bool = false
	var issetUnit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimestamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimestamp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLTimeElapseTotal[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLTimeElapseTotal[fieldId]))
}

func (p *SQLTimeElapseTotal) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Timestamp = _field
	return nil
}
func (p *SQLTimeElapseTotal) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *SQLTimeElapseTotal) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}

func (p *SQLTimeElapseTotal) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLTimeElapseTotal")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLTimeElapseTotal"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLTimeElapseTotal) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Timestamp", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Timestamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLTimeElapseTotal) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLTimeElapseTotal) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLTimeElapseTotal) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLTimeElapseTotal(%+v)", *p)

}

func (p *SQLTimeElapseTotal) DeepEqual(ano *SQLTimeElapseTotal) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Timestamp) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Unit) {
		return false
	}
	return true
}

func (p *SQLTimeElapseTotal) Field1DeepEqual(src int32) bool {

	if p.Timestamp != src {
		return false
	}
	return true
}
func (p *SQLTimeElapseTotal) Field2DeepEqual(src float64) bool {

	if p.Value != src {
		return false
	}
	return true
}
func (p *SQLTimeElapseTotal) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}

type DescribeSQLTemplateStatisticReq struct {
	InstanceID string                       `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	DSType     DSType                       `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	StartTime  int32                        `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime    int32                        `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
	SortField  *SQLTemplateStatisticOrderBy `thrift:"SortField,6,optional" frugal:"6,optional,SQLTemplateStatisticOrderBy" json:"SortField,omitempty"`
	Sort       *SortBy                      `thrift:"Sort,7,optional" frugal:"7,optional,SortBy" json:"Sort,omitempty"`
	PageNumber *int32                       `thrift:"PageNumber,8,optional" frugal:"8,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32                       `thrift:"PageSize,9,optional" frugal:"9,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeSQLTemplateStatisticReq() *DescribeSQLTemplateStatisticReq {
	return &DescribeSQLTemplateStatisticReq{}
}

func (p *DescribeSQLTemplateStatisticReq) InitDefault() {
}

func (p *DescribeSQLTemplateStatisticReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSQLTemplateStatisticReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSQLTemplateStatisticReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSQLTemplateStatisticReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeSQLTemplateStatisticReq_SortField_DEFAULT SQLTemplateStatisticOrderBy

func (p *DescribeSQLTemplateStatisticReq) GetSortField() (v SQLTemplateStatisticOrderBy) {
	if !p.IsSetSortField() {
		return DescribeSQLTemplateStatisticReq_SortField_DEFAULT
	}
	return *p.SortField
}

var DescribeSQLTemplateStatisticReq_Sort_DEFAULT SortBy

func (p *DescribeSQLTemplateStatisticReq) GetSort() (v SortBy) {
	if !p.IsSetSort() {
		return DescribeSQLTemplateStatisticReq_Sort_DEFAULT
	}
	return *p.Sort
}

var DescribeSQLTemplateStatisticReq_PageNumber_DEFAULT int32

func (p *DescribeSQLTemplateStatisticReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSQLTemplateStatisticReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSQLTemplateStatisticReq_PageSize_DEFAULT int32

func (p *DescribeSQLTemplateStatisticReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSQLTemplateStatisticReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeSQLTemplateStatisticReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLTemplateStatisticReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSQLTemplateStatisticReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSQLTemplateStatisticReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSQLTemplateStatisticReq) SetSortField(val *SQLTemplateStatisticOrderBy) {
	p.SortField = val
}
func (p *DescribeSQLTemplateStatisticReq) SetSort(val *SortBy) {
	p.Sort = val
}
func (p *DescribeSQLTemplateStatisticReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSQLTemplateStatisticReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeSQLTemplateStatisticReq = map[int16]string{
	1: "InstanceID",
	2: "DSType",
	3: "StartTime",
	4: "EndTime",
	6: "SortField",
	7: "Sort",
	8: "PageNumber",
	9: "PageSize",
}

func (p *DescribeSQLTemplateStatisticReq) IsSetSortField() bool {
	return p.SortField != nil
}

func (p *DescribeSQLTemplateStatisticReq) IsSetSort() bool {
	return p.Sort != nil
}

func (p *DescribeSQLTemplateStatisticReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSQLTemplateStatisticReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSQLTemplateStatisticReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTemplateStatisticReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTemplateStatisticReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTemplateStatisticReq[fieldId]))
}

func (p *DescribeSQLTemplateStatisticReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *SQLTemplateStatisticOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SQLTemplateStatisticOrderBy(v)
		_field = &tmp
	}
	p.SortField = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.Sort = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeSQLTemplateStatisticReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTemplateStatisticReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTemplateStatisticReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortField() {
		if err = oprot.WriteFieldBegin("SortField", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortField)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSort() {
		if err = oprot.WriteFieldBegin("Sort", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Sort)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTemplateStatisticReq(%+v)", *p)

}

func (p *DescribeSQLTemplateStatisticReq) DeepEqual(ano *DescribeSQLTemplateStatisticReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.SortField) {
		return false
	}
	if !p.Field7DeepEqual(ano.Sort) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field9DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeSQLTemplateStatisticReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field6DeepEqual(src *SQLTemplateStatisticOrderBy) bool {

	if p.SortField == src {
		return true
	} else if p.SortField == nil || src == nil {
		return false
	}
	if *p.SortField != *src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field7DeepEqual(src *SortBy) bool {

	if p.Sort == src {
		return true
	} else if p.Sort == nil || src == nil {
		return false
	}
	if *p.Sort != *src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field8DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSQLTemplateStatisticReq) Field9DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeSQLTemplateStatisticResp struct {
	SQLTemplateStatisticRows []*SQLTemplateStatisticRow `thrift:"SQLTemplateStatisticRows,1,required" frugal:"1,required,list<SQLTemplateStatisticRow>" json:"SQLTemplateStatisticRows"`
	Total                    int32                      `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSQLTemplateStatisticResp() *DescribeSQLTemplateStatisticResp {
	return &DescribeSQLTemplateStatisticResp{}
}

func (p *DescribeSQLTemplateStatisticResp) InitDefault() {
}

func (p *DescribeSQLTemplateStatisticResp) GetSQLTemplateStatisticRows() (v []*SQLTemplateStatisticRow) {
	return p.SQLTemplateStatisticRows
}

func (p *DescribeSQLTemplateStatisticResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSQLTemplateStatisticResp) SetSQLTemplateStatisticRows(val []*SQLTemplateStatisticRow) {
	p.SQLTemplateStatisticRows = val
}
func (p *DescribeSQLTemplateStatisticResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSQLTemplateStatisticResp = map[int16]string{
	1: "SQLTemplateStatisticRows",
	2: "Total",
}

func (p *DescribeSQLTemplateStatisticResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTemplateStatisticResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLTemplateStatisticRows bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLTemplateStatisticRows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLTemplateStatisticRows {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLTemplateStatisticResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLTemplateStatisticResp[fieldId]))
}

func (p *DescribeSQLTemplateStatisticResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLTemplateStatisticRow, 0, size)
	values := make([]SQLTemplateStatisticRow, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLTemplateStatisticRows = _field
	return nil
}
func (p *DescribeSQLTemplateStatisticResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSQLTemplateStatisticResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLTemplateStatisticResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLTemplateStatisticResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLTemplateStatisticRows", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SQLTemplateStatisticRows)); err != nil {
		return err
	}
	for _, v := range p.SQLTemplateStatisticRows {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLTemplateStatisticResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLTemplateStatisticResp(%+v)", *p)

}

func (p *DescribeSQLTemplateStatisticResp) DeepEqual(ano *DescribeSQLTemplateStatisticResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLTemplateStatisticRows) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSQLTemplateStatisticResp) Field1DeepEqual(src []*SQLTemplateStatisticRow) bool {

	if len(p.SQLTemplateStatisticRows) != len(src) {
		return false
	}
	for i, v := range p.SQLTemplateStatisticRows {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSQLTemplateStatisticResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type SQLTemplateStatisticRow struct {
	Fingerprint   *string `thrift:"Fingerprint,1,optional" frugal:"1,optional,string" json:"Fingerprint,omitempty"`
	Template      *string `thrift:"Template,2,optional" frugal:"2,optional,string" json:"Template,omitempty"`
	DB            *string `thrift:"DB,3,optional" frugal:"3,optional,string" json:"DB,omitempty"`
	SqlMethod     *string `thrift:"SqlMethod,4,optional" frugal:"4,optional,string" json:"SqlMethod,omitempty"`
	Count         *int64  `thrift:"Count,5,optional" frugal:"5,optional,i64" json:"Count,omitempty"`
	TimeElapseAVG *int64  `thrift:"TimeElapseAVG,6,optional" frugal:"6,optional,i64" json:"TimeElapseAVG,omitempty"`
	TimeElapseSUM *int64  `thrift:"TimeElapseSUM,7,optional" frugal:"7,optional,i64" json:"TimeElapseSUM,omitempty"`
	AffectRowAVG  *int64  `thrift:"AffectRowAVG,8,optional" frugal:"8,optional,i64" json:"AffectRowAVG,omitempty"`
	ReturnRowAVG  *int64  `thrift:"ReturnRowAVG,9,optional" frugal:"9,optional,i64" json:"ReturnRowAVG,omitempty"`
}

func NewSQLTemplateStatisticRow() *SQLTemplateStatisticRow {
	return &SQLTemplateStatisticRow{}
}

func (p *SQLTemplateStatisticRow) InitDefault() {
}

var SQLTemplateStatisticRow_Fingerprint_DEFAULT string

func (p *SQLTemplateStatisticRow) GetFingerprint() (v string) {
	if !p.IsSetFingerprint() {
		return SQLTemplateStatisticRow_Fingerprint_DEFAULT
	}
	return *p.Fingerprint
}

var SQLTemplateStatisticRow_Template_DEFAULT string

func (p *SQLTemplateStatisticRow) GetTemplate() (v string) {
	if !p.IsSetTemplate() {
		return SQLTemplateStatisticRow_Template_DEFAULT
	}
	return *p.Template
}

var SQLTemplateStatisticRow_DB_DEFAULT string

func (p *SQLTemplateStatisticRow) GetDB() (v string) {
	if !p.IsSetDB() {
		return SQLTemplateStatisticRow_DB_DEFAULT
	}
	return *p.DB
}

var SQLTemplateStatisticRow_SqlMethod_DEFAULT string

func (p *SQLTemplateStatisticRow) GetSqlMethod() (v string) {
	if !p.IsSetSqlMethod() {
		return SQLTemplateStatisticRow_SqlMethod_DEFAULT
	}
	return *p.SqlMethod
}

var SQLTemplateStatisticRow_Count_DEFAULT int64

func (p *SQLTemplateStatisticRow) GetCount() (v int64) {
	if !p.IsSetCount() {
		return SQLTemplateStatisticRow_Count_DEFAULT
	}
	return *p.Count
}

var SQLTemplateStatisticRow_TimeElapseAVG_DEFAULT int64

func (p *SQLTemplateStatisticRow) GetTimeElapseAVG() (v int64) {
	if !p.IsSetTimeElapseAVG() {
		return SQLTemplateStatisticRow_TimeElapseAVG_DEFAULT
	}
	return *p.TimeElapseAVG
}

var SQLTemplateStatisticRow_TimeElapseSUM_DEFAULT int64

func (p *SQLTemplateStatisticRow) GetTimeElapseSUM() (v int64) {
	if !p.IsSetTimeElapseSUM() {
		return SQLTemplateStatisticRow_TimeElapseSUM_DEFAULT
	}
	return *p.TimeElapseSUM
}

var SQLTemplateStatisticRow_AffectRowAVG_DEFAULT int64

func (p *SQLTemplateStatisticRow) GetAffectRowAVG() (v int64) {
	if !p.IsSetAffectRowAVG() {
		return SQLTemplateStatisticRow_AffectRowAVG_DEFAULT
	}
	return *p.AffectRowAVG
}

var SQLTemplateStatisticRow_ReturnRowAVG_DEFAULT int64

func (p *SQLTemplateStatisticRow) GetReturnRowAVG() (v int64) {
	if !p.IsSetReturnRowAVG() {
		return SQLTemplateStatisticRow_ReturnRowAVG_DEFAULT
	}
	return *p.ReturnRowAVG
}
func (p *SQLTemplateStatisticRow) SetFingerprint(val *string) {
	p.Fingerprint = val
}
func (p *SQLTemplateStatisticRow) SetTemplate(val *string) {
	p.Template = val
}
func (p *SQLTemplateStatisticRow) SetDB(val *string) {
	p.DB = val
}
func (p *SQLTemplateStatisticRow) SetSqlMethod(val *string) {
	p.SqlMethod = val
}
func (p *SQLTemplateStatisticRow) SetCount(val *int64) {
	p.Count = val
}
func (p *SQLTemplateStatisticRow) SetTimeElapseAVG(val *int64) {
	p.TimeElapseAVG = val
}
func (p *SQLTemplateStatisticRow) SetTimeElapseSUM(val *int64) {
	p.TimeElapseSUM = val
}
func (p *SQLTemplateStatisticRow) SetAffectRowAVG(val *int64) {
	p.AffectRowAVG = val
}
func (p *SQLTemplateStatisticRow) SetReturnRowAVG(val *int64) {
	p.ReturnRowAVG = val
}

var fieldIDToName_SQLTemplateStatisticRow = map[int16]string{
	1: "Fingerprint",
	2: "Template",
	3: "DB",
	4: "SqlMethod",
	5: "Count",
	6: "TimeElapseAVG",
	7: "TimeElapseSUM",
	8: "AffectRowAVG",
	9: "ReturnRowAVG",
}

func (p *SQLTemplateStatisticRow) IsSetFingerprint() bool {
	return p.Fingerprint != nil
}

func (p *SQLTemplateStatisticRow) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *SQLTemplateStatisticRow) IsSetDB() bool {
	return p.DB != nil
}

func (p *SQLTemplateStatisticRow) IsSetSqlMethod() bool {
	return p.SqlMethod != nil
}

func (p *SQLTemplateStatisticRow) IsSetCount() bool {
	return p.Count != nil
}

func (p *SQLTemplateStatisticRow) IsSetTimeElapseAVG() bool {
	return p.TimeElapseAVG != nil
}

func (p *SQLTemplateStatisticRow) IsSetTimeElapseSUM() bool {
	return p.TimeElapseSUM != nil
}

func (p *SQLTemplateStatisticRow) IsSetAffectRowAVG() bool {
	return p.AffectRowAVG != nil
}

func (p *SQLTemplateStatisticRow) IsSetReturnRowAVG() bool {
	return p.ReturnRowAVG != nil
}

func (p *SQLTemplateStatisticRow) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLTemplateStatisticRow")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLTemplateStatisticRow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Fingerprint = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Template = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DB = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlMethod = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Count = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TimeElapseAVG = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TimeElapseSUM = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AffectRowAVG = _field
	return nil
}
func (p *SQLTemplateStatisticRow) ReadField9(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReturnRowAVG = _field
	return nil
}

func (p *SQLTemplateStatisticRow) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLTemplateStatisticRow")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLTemplateStatisticRow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFingerprint() {
		if err = oprot.WriteFieldBegin("Fingerprint", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Fingerprint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplate() {
		if err = oprot.WriteFieldBegin("Template", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Template); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDB() {
		if err = oprot.WriteFieldBegin("DB", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DB); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlMethod() {
		if err = oprot.WriteFieldBegin("SqlMethod", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlMethod); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCount() {
		if err = oprot.WriteFieldBegin("Count", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Count); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeElapseAVG() {
		if err = oprot.WriteFieldBegin("TimeElapseAVG", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TimeElapseAVG); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeElapseSUM() {
		if err = oprot.WriteFieldBegin("TimeElapseSUM", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TimeElapseSUM); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAffectRowAVG() {
		if err = oprot.WriteFieldBegin("AffectRowAVG", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AffectRowAVG); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetReturnRowAVG() {
		if err = oprot.WriteFieldBegin("ReturnRowAVG", thrift.I64, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ReturnRowAVG); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SQLTemplateStatisticRow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLTemplateStatisticRow(%+v)", *p)

}

func (p *SQLTemplateStatisticRow) DeepEqual(ano *SQLTemplateStatisticRow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Fingerprint) {
		return false
	}
	if !p.Field2DeepEqual(ano.Template) {
		return false
	}
	if !p.Field3DeepEqual(ano.DB) {
		return false
	}
	if !p.Field4DeepEqual(ano.SqlMethod) {
		return false
	}
	if !p.Field5DeepEqual(ano.Count) {
		return false
	}
	if !p.Field6DeepEqual(ano.TimeElapseAVG) {
		return false
	}
	if !p.Field7DeepEqual(ano.TimeElapseSUM) {
		return false
	}
	if !p.Field8DeepEqual(ano.AffectRowAVG) {
		return false
	}
	if !p.Field9DeepEqual(ano.ReturnRowAVG) {
		return false
	}
	return true
}

func (p *SQLTemplateStatisticRow) Field1DeepEqual(src *string) bool {

	if p.Fingerprint == src {
		return true
	} else if p.Fingerprint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Fingerprint, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field2DeepEqual(src *string) bool {

	if p.Template == src {
		return true
	} else if p.Template == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Template, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field3DeepEqual(src *string) bool {

	if p.DB == src {
		return true
	} else if p.DB == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DB, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field4DeepEqual(src *string) bool {

	if p.SqlMethod == src {
		return true
	} else if p.SqlMethod == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlMethod, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field5DeepEqual(src *int64) bool {

	if p.Count == src {
		return true
	} else if p.Count == nil || src == nil {
		return false
	}
	if *p.Count != *src {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field6DeepEqual(src *int64) bool {

	if p.TimeElapseAVG == src {
		return true
	} else if p.TimeElapseAVG == nil || src == nil {
		return false
	}
	if *p.TimeElapseAVG != *src {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field7DeepEqual(src *int64) bool {

	if p.TimeElapseSUM == src {
		return true
	} else if p.TimeElapseSUM == nil || src == nil {
		return false
	}
	if *p.TimeElapseSUM != *src {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field8DeepEqual(src *int64) bool {

	if p.AffectRowAVG == src {
		return true
	} else if p.AffectRowAVG == nil || src == nil {
		return false
	}
	if *p.AffectRowAVG != *src {
		return false
	}
	return true
}
func (p *SQLTemplateStatisticRow) Field9DeepEqual(src *int64) bool {

	if p.ReturnRowAVG == src {
		return true
	} else if p.ReturnRowAVG == nil || src == nil {
		return false
	}
	if *p.ReturnRowAVG != *src {
		return false
	}
	return true
}

type DescribeSQLStatisticMetricsReq struct {
	InstanceID     string      `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	DSType         DSType      `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	StartTime      int32       `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime        int32       `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
	SqlFingerprint string      `thrift:"SqlFingerprint,5,required" frugal:"5,required,string" json:"SqlFingerprint"`
	MetricsType    MetricsType `thrift:"MetricsType,6,required" frugal:"6,required,MetricsType" json:"MetricsType"`
}

func NewDescribeSQLStatisticMetricsReq() *DescribeSQLStatisticMetricsReq {
	return &DescribeSQLStatisticMetricsReq{}
}

func (p *DescribeSQLStatisticMetricsReq) InitDefault() {
}

func (p *DescribeSQLStatisticMetricsReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSQLStatisticMetricsReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeSQLStatisticMetricsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeSQLStatisticMetricsReq) GetEndTime() (v int32) {
	return p.EndTime
}

func (p *DescribeSQLStatisticMetricsReq) GetSqlFingerprint() (v string) {
	return p.SqlFingerprint
}

func (p *DescribeSQLStatisticMetricsReq) GetMetricsType() (v MetricsType) {
	return p.MetricsType
}
func (p *DescribeSQLStatisticMetricsReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLStatisticMetricsReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeSQLStatisticMetricsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeSQLStatisticMetricsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeSQLStatisticMetricsReq) SetSqlFingerprint(val string) {
	p.SqlFingerprint = val
}
func (p *DescribeSQLStatisticMetricsReq) SetMetricsType(val MetricsType) {
	p.MetricsType = val
}

var fieldIDToName_DescribeSQLStatisticMetricsReq = map[int16]string{
	1: "InstanceID",
	2: "DSType",
	3: "StartTime",
	4: "EndTime",
	5: "SqlFingerprint",
	6: "MetricsType",
}

func (p *DescribeSQLStatisticMetricsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLStatisticMetricsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetDSType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetSqlFingerprint bool = false
	var issetMetricsType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlFingerprint = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetricsType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSqlFingerprint {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetMetricsType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLStatisticMetricsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLStatisticMetricsReq[fieldId]))
}

func (p *DescribeSQLStatisticMetricsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLStatisticMetricsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeSQLStatisticMetricsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSQLStatisticMetricsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSQLStatisticMetricsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlFingerprint = _field
	return nil
}
func (p *DescribeSQLStatisticMetricsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field MetricsType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = MetricsType(v)
	}
	p.MetricsType = _field
	return nil
}

func (p *DescribeSQLStatisticMetricsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLStatisticMetricsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLStatisticMetricsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlFingerprint", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlFingerprint); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MetricsType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.MetricsType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLStatisticMetricsReq(%+v)", *p)

}

func (p *DescribeSQLStatisticMetricsReq) DeepEqual(ano *DescribeSQLStatisticMetricsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.SqlFingerprint) {
		return false
	}
	if !p.Field6DeepEqual(ano.MetricsType) {
		return false
	}
	return true
}

func (p *DescribeSQLStatisticMetricsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLStatisticMetricsReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeSQLStatisticMetricsReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLStatisticMetricsReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeSQLStatisticMetricsReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SqlFingerprint, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLStatisticMetricsReq) Field6DeepEqual(src MetricsType) bool {

	if p.MetricsType != src {
		return false
	}
	return true
}

type DescribeSQLStatisticMetricsResp struct {
	MetricsPoints []*MetricsPoint `thrift:"MetricsPoints,1,required" frugal:"1,required,list<MetricsPoint>" json:"MetricsPoints"`
}

func NewDescribeSQLStatisticMetricsResp() *DescribeSQLStatisticMetricsResp {
	return &DescribeSQLStatisticMetricsResp{}
}

func (p *DescribeSQLStatisticMetricsResp) InitDefault() {
}

func (p *DescribeSQLStatisticMetricsResp) GetMetricsPoints() (v []*MetricsPoint) {
	return p.MetricsPoints
}
func (p *DescribeSQLStatisticMetricsResp) SetMetricsPoints(val []*MetricsPoint) {
	p.MetricsPoints = val
}

var fieldIDToName_DescribeSQLStatisticMetricsResp = map[int16]string{
	1: "MetricsPoints",
}

func (p *DescribeSQLStatisticMetricsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLStatisticMetricsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMetricsPoints bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetricsPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMetricsPoints {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLStatisticMetricsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLStatisticMetricsResp[fieldId]))
}

func (p *DescribeSQLStatisticMetricsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MetricsPoint, 0, size)
	values := make([]MetricsPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MetricsPoints = _field
	return nil
}

func (p *DescribeSQLStatisticMetricsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLStatisticMetricsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLStatisticMetricsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MetricsPoints", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MetricsPoints)); err != nil {
		return err
	}
	for _, v := range p.MetricsPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLStatisticMetricsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLStatisticMetricsResp(%+v)", *p)

}

func (p *DescribeSQLStatisticMetricsResp) DeepEqual(ano *DescribeSQLStatisticMetricsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MetricsPoints) {
		return false
	}
	return true
}

func (p *DescribeSQLStatisticMetricsResp) Field1DeepEqual(src []*MetricsPoint) bool {

	if len(p.MetricsPoints) != len(src) {
		return false
	}
	for i, v := range p.MetricsPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type MetricsPoint struct {
	Timestamp int32   `thrift:"Timestamp,1,required" frugal:"1,required,i32" json:"Timestamp"`
	Value     float64 `thrift:"Value,2,required" frugal:"2,required,double" json:"Value"`
	Unit      string  `thrift:"Unit,3,required" frugal:"3,required,string" json:"Unit"`
}

func NewMetricsPoint() *MetricsPoint {
	return &MetricsPoint{}
}

func (p *MetricsPoint) InitDefault() {
}

func (p *MetricsPoint) GetTimestamp() (v int32) {
	return p.Timestamp
}

func (p *MetricsPoint) GetValue() (v float64) {
	return p.Value
}

func (p *MetricsPoint) GetUnit() (v string) {
	return p.Unit
}
func (p *MetricsPoint) SetTimestamp(val int32) {
	p.Timestamp = val
}
func (p *MetricsPoint) SetValue(val float64) {
	p.Value = val
}
func (p *MetricsPoint) SetUnit(val string) {
	p.Unit = val
}

var fieldIDToName_MetricsPoint = map[int16]string{
	1: "Timestamp",
	2: "Value",
	3: "Unit",
}

func (p *MetricsPoint) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MetricsPoint")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimestamp bool = false
	var issetValue bool = false
	var issetUnit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimestamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimestamp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MetricsPoint[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MetricsPoint[fieldId]))
}

func (p *MetricsPoint) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Timestamp = _field
	return nil
}
func (p *MetricsPoint) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *MetricsPoint) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}

func (p *MetricsPoint) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MetricsPoint")

	var fieldId int16
	if err = oprot.WriteStructBegin("MetricsPoint"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MetricsPoint) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Timestamp", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Timestamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MetricsPoint) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MetricsPoint) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *MetricsPoint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MetricsPoint(%+v)", *p)

}

func (p *MetricsPoint) DeepEqual(ano *MetricsPoint) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Timestamp) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Unit) {
		return false
	}
	return true
}

func (p *MetricsPoint) Field1DeepEqual(src int32) bool {

	if p.Timestamp != src {
		return false
	}
	return true
}
func (p *MetricsPoint) Field2DeepEqual(src float64) bool {

	if p.Value != src {
		return false
	}
	return true
}
func (p *MetricsPoint) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}

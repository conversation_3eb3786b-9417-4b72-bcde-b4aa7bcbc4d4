// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *CreateSessionResp) IsValid() error {
	if p.DefaultConnection != nil {
		if err := p.DefaultConnection.IsValid(); err != nil {
			return fmt.Errorf("field DefaultConnection not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateSessionReq) IsValid() error {
	if p.DataSource != nil {
		if err := p.DataSource.IsValid(); err != nil {
			return fmt.Errorf("field DataSource not valid, %w", err)
		}
	}
	return nil
}
func (p *CheckConnReq) IsValid() error {
	if p.DataSource != nil {
		if err := p.DataSource.IsValid(); err != nil {
			return fmt.Errorf("field DataSource not valid, %w", err)
		}
	}
	return nil
}
func (p *CheckConnResp) IsValid() error {
	return nil
}
func (p *CloseSessionReq) IsValid() error {
	return nil
}
func (p *CloseSessionResp) IsValid() error {
	return nil
}
func (p *DescribeSessionReq) IsValid() error {
	return nil
}
func (p *ConnectionInfo) IsValid() error {
	return nil
}
func (p *DescribeSessionResp) IsValid() error {
	return nil
}
func (p *CreateConnectionReq) IsValid() error {
	return nil
}
func (p *CreateConnectionResp) IsValid() error {
	if p.Connection != nil {
		if err := p.Connection.IsValid(); err != nil {
			return fmt.Errorf("field Connection not valid, %w", err)
		}
	}
	return nil
}
func (p *CloseConnectionReq) IsValid() error {
	return nil
}
func (p *CloseConnectionResp) IsValid() error {
	return nil
}
func (p *SessionKeepAliveReq) IsValid() error {
	return nil
}
func (p *SessionKeepAliveResp) IsValid() error {
	return nil
}
func (p *CommandItem) IsValid() error {
	return nil
}
func (p *CommandResultLimitInfo) IsValid() error {
	return nil
}
func (p *LimitConfig) IsValid() error {
	return nil
}
func (p *ExecuteCommandSetReq) IsValid() error {
	return nil
}
func (p *ExecuteCommandSetResp) IsValid() error {
	return nil
}
func (p *CommandSetStruct) IsValid() error {
	if p.SqlTaskConfig != nil {
		if err := p.SqlTaskConfig.IsValid(); err != nil {
			return fmt.Errorf("field SqlTaskConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *SqlTaskConfig) IsValid() error {
	if p.ShardingConfig != nil {
		if err := p.ShardingConfig.IsValid(); err != nil {
			return fmt.Errorf("field ShardingConfig not valid, %w", err)
		}
	}
	if p.GhostConfig != nil {
		if err := p.GhostConfig.IsValid(); err != nil {
			return fmt.Errorf("field GhostConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *ShardingConfig) IsValid() error {
	if p.DryRunResult_ != nil {
		if err := p.DryRunResult_.IsValid(); err != nil {
			return fmt.Errorf("field DryRunResult_ not valid, %w", err)
		}
	}
	return nil
}
func (p *DryRunResult_) IsValid() error {
	return nil
}
func (p *GhostConfig) IsValid() error {
	return nil
}
func (p *InstanceInfo) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	if p.InstanceInspectionInfo != nil {
		if err := p.InstanceInspectionInfo.IsValid(); err != nil {
			return fmt.Errorf("field InstanceInspectionInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CancelCommandSetReq) IsValid() error {
	return nil
}
func (p *CancelCommandSetResp) IsValid() error {
	return nil
}
func (p *DescribeCommandSetReq) IsValid() error {
	return nil
}
func (p *DescribeCommandSetResp) IsValid() error {
	return nil
}
func (p *DescribeCommandReq) IsValid() error {
	return nil
}
func (p *DescribeCommandResp) IsValid() error {
	if p.Command != nil {
		if err := p.Command.IsValid(); err != nil {
			return fmt.Errorf("field Command not valid, %w", err)
		}
	}
	if p.LimitConfig != nil {
		if err := p.LimitConfig.IsValid(); err != nil {
			return fmt.Errorf("field LimitConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *AlterRowsReq) IsValid() error {
	return nil
}
func (p *AlterRowsResp) IsValid() error {
	return nil
}
func (p *DescribeDataSourceTypesReq) IsValid() error {
	return nil
}
func (p *DescribeDataSourceTypesResp) IsValid() error {
	return nil
}
func (p *DescribeLinkTypesReq) IsValid() error {
	return nil
}
func (p *DescribeLinkTypesResp) IsValid() error {
	return nil
}
func (p *DescribeRegionsReq) IsValid() error {
	return nil
}
func (p *DescribeRegionsResp) IsValid() error {
	return nil
}
func (p *QueryInstanceFilter) IsValid() error {
	return nil
}
func (p *DescribeInstancesReq) IsValid() error {
	if p.QueryInstanceFilter != nil {
		if err := p.QueryInstanceFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryInstanceFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeRealTimeInstancesReq) IsValid() error {
	return nil
}
func (p *DescribeRealTimeInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeDatabasesReq) IsValid() error {
	return nil
}
func (p *DescribeDatabasesResp) IsValid() error {
	return nil
}
func (p *DatabaseInfo) IsValid() error {
	return nil
}
func (p *CreateDatabaseReq) IsValid() error {
	return nil
}
func (p *CreateDatabaseResp) IsValid() error {
	return nil
}
func (p *DropDatabaseReq) IsValid() error {
	return nil
}
func (p *DropDatabaseResp) IsValid() error {
	return nil
}
func (p *AlterDatabaseReq) IsValid() error {
	return nil
}
func (p *AlterDatabaseResp) IsValid() error {
	return nil
}
func (p *DescribeTablesReq) IsValid() error {
	return nil
}
func (p *DescribeTablesResp) IsValid() error {
	return nil
}
func (p *SearchTableReq) IsValid() error {
	return nil
}
func (p *SearchTableResp) IsValid() error {
	return nil
}
func (p *DescribeTableReq) IsValid() error {
	return nil
}
func (p *DescribeTableResp) IsValid() error {
	if p.TableMeta != nil {
		if err := p.TableMeta.IsValid(); err != nil {
			return fmt.Errorf("field TableMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateTableReq) IsValid() error {
	if p.TableMeta != nil {
		if err := p.TableMeta.IsValid(); err != nil {
			return fmt.Errorf("field TableMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateTableResp) IsValid() error {
	return nil
}
func (p *RenameTableReq) IsValid() error {
	return nil
}
func (p *RenameTableResp) IsValid() error {
	return nil
}
func (p *CopyTableSchemaOnlyReq) IsValid() error {
	return nil
}
func (p *CopyTableSchemaOnlyResp) IsValid() error {
	return nil
}
func (p *CopyTableReq) IsValid() error {
	return nil
}
func (p *CopyTableResp) IsValid() error {
	return nil
}
func (p *DropTableReq) IsValid() error {
	return nil
}
func (p *DropTableResp) IsValid() error {
	return nil
}
func (p *DescribeCharsetsReq) IsValid() error {
	return nil
}
func (p *DescribeCharsetsResp) IsValid() error {
	return nil
}
func (p *DescribeCollationsReq) IsValid() error {
	return nil
}
func (p *DescribeCollationsResp) IsValid() error {
	return nil
}
func (p *DescribeViewsReq) IsValid() error {
	return nil
}
func (p *DescribeViewsResp) IsValid() error {
	return nil
}
func (p *SearchViewReq) IsValid() error {
	return nil
}
func (p *SearchViewResp) IsValid() error {
	return nil
}
func (p *DescribeViewReq) IsValid() error {
	return nil
}
func (p *DescribeViewResp) IsValid() error {
	if p.ViewMeta != nil {
		if err := p.ViewMeta.IsValid(); err != nil {
			return fmt.Errorf("field ViewMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateViewReq) IsValid() error {
	if p.ViewMeta != nil {
		if err := p.ViewMeta.IsValid(); err != nil {
			return fmt.Errorf("field ViewMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateViewResp) IsValid() error {
	return nil
}
func (p *DropViewReq) IsValid() error {
	return nil
}
func (p *DropViewResp) IsValid() error {
	return nil
}
func (p *DescribeFunctionsReq) IsValid() error {
	return nil
}
func (p *DescribeFunctionsResp) IsValid() error {
	return nil
}
func (p *DescribeFunctionReq) IsValid() error {
	return nil
}
func (p *DescribeFunctionResp) IsValid() error {
	if p.FunctionInfo != nil {
		if err := p.FunctionInfo.IsValid(); err != nil {
			return fmt.Errorf("field FunctionInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *SearchFunctionReq) IsValid() error {
	return nil
}
func (p *SearchFunctionResp) IsValid() error {
	return nil
}
func (p *ExecuteFunctionReq) IsValid() error {
	return nil
}
func (p *ExecuteFunctionResp) IsValid() error {
	return nil
}
func (p *CreateFunctionReq) IsValid() error {
	if p.FunctionMeta != nil {
		if err := p.FunctionMeta.IsValid(); err != nil {
			return fmt.Errorf("field FunctionMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateFunctionResp) IsValid() error {
	return nil
}
func (p *DropFunctionReq) IsValid() error {
	return nil
}
func (p *DropFunctionResp) IsValid() error {
	return nil
}
func (p *DescribeDataTypesReq) IsValid() error {
	return nil
}
func (p *DescribeDataTypesResp) IsValid() error {
	return nil
}
func (p *CreateProcedureReq) IsValid() error {
	if p.ProcedureMeta != nil {
		if err := p.ProcedureMeta.IsValid(); err != nil {
			return fmt.Errorf("field ProcedureMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateProcedureResp) IsValid() error {
	return nil
}
func (p *ExecuteProcedureReq) IsValid() error {
	return nil
}
func (p *ExecuteProcedureResp) IsValid() error {
	return nil
}
func (p *DropProcedureReq) IsValid() error {
	return nil
}
func (p *DropProcedureResp) IsValid() error {
	return nil
}
func (p *DescribeProceduresReq) IsValid() error {
	return nil
}
func (p *DescribeProceduresResp) IsValid() error {
	return nil
}
func (p *DescribeProcedureReq) IsValid() error {
	return nil
}
func (p *DescribeProcedureResp) IsValid() error {
	if p.ProcedureInfo != nil {
		if err := p.ProcedureInfo.IsValid(); err != nil {
			return fmt.Errorf("field ProcedureInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *SearchProcedureReq) IsValid() error {
	return nil
}
func (p *SearchProcedureResp) IsValid() error {
	return nil
}
func (p *CreateTriggerReq) IsValid() error {
	if p.TriggerMeta != nil {
		if err := p.TriggerMeta.IsValid(); err != nil {
			return fmt.Errorf("field TriggerMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateTriggerResp) IsValid() error {
	return nil
}
func (p *DescribeTriggersReq) IsValid() error {
	return nil
}
func (p *DescribeTriggersResp) IsValid() error {
	return nil
}
func (p *DescribeTriggerReq) IsValid() error {
	return nil
}
func (p *DescribeTriggerResp) IsValid() error {
	if p.TriggerInfo != nil {
		if err := p.TriggerInfo.IsValid(); err != nil {
			return fmt.Errorf("field TriggerInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *SearchTriggerReq) IsValid() error {
	return nil
}
func (p *SearchTriggerResp) IsValid() error {
	return nil
}
func (p *DropTriggerReq) IsValid() error {
	return nil
}
func (p *DropTriggerResp) IsValid() error {
	return nil
}
func (p *ChangeDBReq) IsValid() error {
	return nil
}
func (p *ChangeDBResp) IsValid() error {
	return nil
}
func (p *GetUserProtocolStateReq) IsValid() error {
	return nil
}
func (p *GetUserProtocolStateResp) IsValid() error {
	return nil
}
func (p *AgreeUserProtocolReq) IsValid() error {
	return nil
}
func (p *AgreeUserProtocolResp) IsValid() error {
	return nil
}
func (p *AgreeDataCollectionReq) IsValid() error {
	return nil
}
func (p *AgreeDataCollectionResp) IsValid() error {
	return nil
}
func (p *GetTotalKeyNumberReq) IsValid() error {
	return nil
}
func (p *GetTotalKeyNumberResp) IsValid() error {
	return nil
}
func (p *DescribeKeysReq) IsValid() error {
	return nil
}
func (p *DescribeKeysResp) IsValid() error {
	return nil
}
func (p *AlterKVsReq) IsValid() error {
	return nil
}
func (p *AlterKVsResp) IsValid() error {
	return nil
}
func (p *GetKeyMembersReq) IsValid() error {
	return nil
}
func (p *GetKeyMembersResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceNodesReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceNodesResp) IsValid() error {
	return nil
}
func (p *NodeInfoObject) IsValid() error {
	return nil
}
func (p *DescribePgTableReq) IsValid() error {
	return nil
}
func (p *DescribePgTableResp) IsValid() error {
	if p.TableMeta != nil {
		if err := p.TableMeta.IsValid(); err != nil {
			return fmt.Errorf("field TableMeta not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeSchemasReq) IsValid() error {
	return nil
}
func (p *DescribeSchemasResp) IsValid() error {
	return nil
}
func (p *DescribeSequencesReq) IsValid() error {
	return nil
}
func (p *DescribeSequencesResp) IsValid() error {
	return nil
}
func (p *DescribeTableSpacesReq) IsValid() error {
	return nil
}
func (p *DescribeTableSpacesResp) IsValid() error {
	return nil
}
func (p *DescribePgUsersReq) IsValid() error {
	return nil
}
func (p *DescribePgUsersResp) IsValid() error {
	return nil
}
func (p *DescribePgCollationsReq) IsValid() error {
	return nil
}
func (p *DescribePgCollationsResp) IsValid() error {
	return nil
}
func (p *DBRangeObject) IsValid() error {
	return nil
}
func (p *ShardNodeInfo) IsValid() error {
	return nil
}
func (p *ShardInfoObject) IsValid() error {
	return nil
}
func (p *DescribeInstanceShardsReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	return nil
}
func (p *DescribeInstanceShardsResp) IsValid() error {
	return nil
}
func (p *DbInstanceInfo) IsValid() error {
	return nil
}
func (p *DescribeInstanceInfoReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceInfoResp) IsValid() error {
	if p.Detail != nil {
		if err := p.Detail.IsValid(); err != nil {
			return fmt.Errorf("field Detail not valid, %w", err)
		}
	}
	return nil
}

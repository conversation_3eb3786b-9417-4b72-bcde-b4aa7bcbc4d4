// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type Trxstatus int64

const (
	Trxstatus_RUNNING      Trxstatus = 1
	Trxstatus_LOCKWAIT     Trxstatus = 2
	Trxstatus_ROLLING_BACK Trxstatus = 3
	Trxstatus_COMMITTING   Trxstatus = 4
)

func (p Trxstatus) String() string {
	switch p {
	case Trxstatus_RUNNING:
		return "RUNNING"
	case Trxstatus_LOCKWAIT:
		return "LOCKWAIT"
	case Trxstatus_ROLLING_BACK:
		return "ROLLING_BACK"
	case Trxstatus_COMMITTING:
		return "COMMITTING"
	}
	return "<UNSET>"
}

func TrxstatusFromString(s string) (Trxstatus, error) {
	switch s {
	case "RUNNING":
		return Trxstatus_RUNNING, nil
	case "LOCKWAIT":
		return Trxstatus_LOCKWAIT, nil
	case "ROLLING_BACK":
		return Trxstatus_ROLLING_BACK, nil
	case "COMMITTING":
		return Trxstatus_COMMITTING, nil
	}
	return Trxstatus(0), fmt.Errorf("not a valid Trxstatus string")
}

func TrxstatusPtr(v Trxstatus) *Trxstatus { return &v }

func (p Trxstatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *Trxstatus) UnmarshalText(text []byte) error {
	q, err := TrxstatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type Lockstatus int64

const (
	Lockstatus_LockHold        Lockstatus = 1
	Lockstatus_LockWait        Lockstatus = 2
	Lockstatus_LockHoldAndWait Lockstatus = 3
	Lockstatus_None            Lockstatus = 4
)

func (p Lockstatus) String() string {
	switch p {
	case Lockstatus_LockHold:
		return "LockHold"
	case Lockstatus_LockWait:
		return "LockWait"
	case Lockstatus_LockHoldAndWait:
		return "LockHoldAndWait"
	case Lockstatus_None:
		return "None"
	}
	return "<UNSET>"
}

func LockstatusFromString(s string) (Lockstatus, error) {
	switch s {
	case "LockHold":
		return Lockstatus_LockHold, nil
	case "LockWait":
		return Lockstatus_LockWait, nil
	case "LockHoldAndWait":
		return Lockstatus_LockHoldAndWait, nil
	case "None":
		return Lockstatus_None, nil
	}
	return Lockstatus(0), fmt.Errorf("not a valid Lockstatus string")
}

func LockstatusPtr(v Lockstatus) *Lockstatus { return &v }

func (p Lockstatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *Lockstatus) UnmarshalText(text []byte) error {
	q, err := LockstatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SortParam int64

const (
	SortParam_TrxStartTime     SortParam = 1
	SortParam_TrxWaitStartTime SortParam = 2
	SortParam_TrxTablesLocked  SortParam = 3
	SortParam_TrxRowsLocked    SortParam = 4
	SortParam_TrxRowsModified  SortParam = 5
	SortParam_TrxExecTime      SortParam = 6
)

func (p SortParam) String() string {
	switch p {
	case SortParam_TrxStartTime:
		return "TrxStartTime"
	case SortParam_TrxWaitStartTime:
		return "TrxWaitStartTime"
	case SortParam_TrxTablesLocked:
		return "TrxTablesLocked"
	case SortParam_TrxRowsLocked:
		return "TrxRowsLocked"
	case SortParam_TrxRowsModified:
		return "TrxRowsModified"
	case SortParam_TrxExecTime:
		return "TrxExecTime"
	}
	return "<UNSET>"
}

func SortParamFromString(s string) (SortParam, error) {
	switch s {
	case "TrxStartTime":
		return SortParam_TrxStartTime, nil
	case "TrxWaitStartTime":
		return SortParam_TrxWaitStartTime, nil
	case "TrxTablesLocked":
		return SortParam_TrxTablesLocked, nil
	case "TrxRowsLocked":
		return SortParam_TrxRowsLocked, nil
	case "TrxRowsModified":
		return SortParam_TrxRowsModified, nil
	case "TrxExecTime":
		return SortParam_TrxExecTime, nil
	}
	return SortParam(0), fmt.Errorf("not a valid SortParam string")
}

func SortParamPtr(v SortParam) *SortParam { return &v }

func (p SortParam) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SortParam) UnmarshalText(text []byte) error {
	q, err := SortParamFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type WaitLockSortParam int64

const (
	WaitLockSortParam_RTrxStarted      WaitLockSortParam = 1
	WaitLockSortParam_RTrxWaitStarted  WaitLockSortParam = 2
	WaitLockSortParam_RTrxRowsLocked   WaitLockSortParam = 3
	WaitLockSortParam_RTrxRowsModified WaitLockSortParam = 4
	WaitLockSortParam_RBlockedWaitSecs WaitLockSortParam = 5
)

func (p WaitLockSortParam) String() string {
	switch p {
	case WaitLockSortParam_RTrxStarted:
		return "RTrxStarted"
	case WaitLockSortParam_RTrxWaitStarted:
		return "RTrxWaitStarted"
	case WaitLockSortParam_RTrxRowsLocked:
		return "RTrxRowsLocked"
	case WaitLockSortParam_RTrxRowsModified:
		return "RTrxRowsModified"
	case WaitLockSortParam_RBlockedWaitSecs:
		return "RBlockedWaitSecs"
	}
	return "<UNSET>"
}

func WaitLockSortParamFromString(s string) (WaitLockSortParam, error) {
	switch s {
	case "RTrxStarted":
		return WaitLockSortParam_RTrxStarted, nil
	case "RTrxWaitStarted":
		return WaitLockSortParam_RTrxWaitStarted, nil
	case "RTrxRowsLocked":
		return WaitLockSortParam_RTrxRowsLocked, nil
	case "RTrxRowsModified":
		return WaitLockSortParam_RTrxRowsModified, nil
	case "RBlockedWaitSecs":
		return WaitLockSortParam_RBlockedWaitSecs, nil
	}
	return WaitLockSortParam(0), fmt.Errorf("not a valid WaitLockSortParam string")
}

func WaitLockSortParamPtr(v WaitLockSortParam) *WaitLockSortParam { return &v }

func (p WaitLockSortParam) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *WaitLockSortParam) UnmarshalText(text []byte) error {
	q, err := WaitLockSortParamFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type Order int64

const (
	Order_Asc  Order = 1
	Order_Desc Order = 2
)

func (p Order) String() string {
	switch p {
	case Order_Asc:
		return "Asc"
	case Order_Desc:
		return "Desc"
	}
	return "<UNSET>"
}

func OrderFromString(s string) (Order, error) {
	switch s {
	case "Asc":
		return Order_Asc, nil
	case "Desc":
		return Order_Desc, nil
	}
	return Order(0), fmt.Errorf("not a valid Order string")
}

func OrderPtr(v Order) *Order { return &v }

func (p Order) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *Order) UnmarshalText(text []byte) error {
	q, err := OrderFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TrxQueryFilter struct {
	TrxExecTime *int32      `thrift:"TrxExecTime,1,optional" frugal:"1,optional,i32" json:"TrxExecTime,omitempty"`
	TrxStatus   *string     `thrift:"TrxStatus,2,optional" frugal:"2,optional,string" json:"TrxStatus,omitempty"`
	LockStatus  *Lockstatus `thrift:"LockStatus,3,optional" frugal:"3,optional,Lockstatus" json:"LockStatus,omitempty"`
	ProcessId   *string     `thrift:"ProcessId,4,optional" frugal:"4,optional,string" json:"ProcessId,omitempty"`
	TrxId       *string     `thrift:"TrxId,5,optional" frugal:"5,optional,string" json:"TrxId,omitempty"`
	SqlBlocked  *string     `thrift:"SqlBlocked,6,optional" frugal:"6,optional,string" json:"SqlBlocked,omitempty"`
	TrxIsoLevel *string     `thrift:"TrxIsoLevel,7,optional" frugal:"7,optional,string" json:"TrxIsoLevel,omitempty"`
	BlockTrxId  *string     `thrift:"BlockTrxId,8,optional" frugal:"8,optional,string" json:"BlockTrxId,omitempty"`
}

func NewTrxQueryFilter() *TrxQueryFilter {
	return &TrxQueryFilter{}
}

func (p *TrxQueryFilter) InitDefault() {
}

var TrxQueryFilter_TrxExecTime_DEFAULT int32

func (p *TrxQueryFilter) GetTrxExecTime() (v int32) {
	if !p.IsSetTrxExecTime() {
		return TrxQueryFilter_TrxExecTime_DEFAULT
	}
	return *p.TrxExecTime
}

var TrxQueryFilter_TrxStatus_DEFAULT string

func (p *TrxQueryFilter) GetTrxStatus() (v string) {
	if !p.IsSetTrxStatus() {
		return TrxQueryFilter_TrxStatus_DEFAULT
	}
	return *p.TrxStatus
}

var TrxQueryFilter_LockStatus_DEFAULT Lockstatus

func (p *TrxQueryFilter) GetLockStatus() (v Lockstatus) {
	if !p.IsSetLockStatus() {
		return TrxQueryFilter_LockStatus_DEFAULT
	}
	return *p.LockStatus
}

var TrxQueryFilter_ProcessId_DEFAULT string

func (p *TrxQueryFilter) GetProcessId() (v string) {
	if !p.IsSetProcessId() {
		return TrxQueryFilter_ProcessId_DEFAULT
	}
	return *p.ProcessId
}

var TrxQueryFilter_TrxId_DEFAULT string

func (p *TrxQueryFilter) GetTrxId() (v string) {
	if !p.IsSetTrxId() {
		return TrxQueryFilter_TrxId_DEFAULT
	}
	return *p.TrxId
}

var TrxQueryFilter_SqlBlocked_DEFAULT string

func (p *TrxQueryFilter) GetSqlBlocked() (v string) {
	if !p.IsSetSqlBlocked() {
		return TrxQueryFilter_SqlBlocked_DEFAULT
	}
	return *p.SqlBlocked
}

var TrxQueryFilter_TrxIsoLevel_DEFAULT string

func (p *TrxQueryFilter) GetTrxIsoLevel() (v string) {
	if !p.IsSetTrxIsoLevel() {
		return TrxQueryFilter_TrxIsoLevel_DEFAULT
	}
	return *p.TrxIsoLevel
}

var TrxQueryFilter_BlockTrxId_DEFAULT string

func (p *TrxQueryFilter) GetBlockTrxId() (v string) {
	if !p.IsSetBlockTrxId() {
		return TrxQueryFilter_BlockTrxId_DEFAULT
	}
	return *p.BlockTrxId
}
func (p *TrxQueryFilter) SetTrxExecTime(val *int32) {
	p.TrxExecTime = val
}
func (p *TrxQueryFilter) SetTrxStatus(val *string) {
	p.TrxStatus = val
}
func (p *TrxQueryFilter) SetLockStatus(val *Lockstatus) {
	p.LockStatus = val
}
func (p *TrxQueryFilter) SetProcessId(val *string) {
	p.ProcessId = val
}
func (p *TrxQueryFilter) SetTrxId(val *string) {
	p.TrxId = val
}
func (p *TrxQueryFilter) SetSqlBlocked(val *string) {
	p.SqlBlocked = val
}
func (p *TrxQueryFilter) SetTrxIsoLevel(val *string) {
	p.TrxIsoLevel = val
}
func (p *TrxQueryFilter) SetBlockTrxId(val *string) {
	p.BlockTrxId = val
}

var fieldIDToName_TrxQueryFilter = map[int16]string{
	1: "TrxExecTime",
	2: "TrxStatus",
	3: "LockStatus",
	4: "ProcessId",
	5: "TrxId",
	6: "SqlBlocked",
	7: "TrxIsoLevel",
	8: "BlockTrxId",
}

func (p *TrxQueryFilter) IsSetTrxExecTime() bool {
	return p.TrxExecTime != nil
}

func (p *TrxQueryFilter) IsSetTrxStatus() bool {
	return p.TrxStatus != nil
}

func (p *TrxQueryFilter) IsSetLockStatus() bool {
	return p.LockStatus != nil
}

func (p *TrxQueryFilter) IsSetProcessId() bool {
	return p.ProcessId != nil
}

func (p *TrxQueryFilter) IsSetTrxId() bool {
	return p.TrxId != nil
}

func (p *TrxQueryFilter) IsSetSqlBlocked() bool {
	return p.SqlBlocked != nil
}

func (p *TrxQueryFilter) IsSetTrxIsoLevel() bool {
	return p.TrxIsoLevel != nil
}

func (p *TrxQueryFilter) IsSetBlockTrxId() bool {
	return p.BlockTrxId != nil
}

func (p *TrxQueryFilter) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxQueryFilter")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxQueryFilter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TrxQueryFilter) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxExecTime = _field
	return nil
}
func (p *TrxQueryFilter) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxStatus = _field
	return nil
}
func (p *TrxQueryFilter) ReadField3(iprot thrift.TProtocol) error {

	var _field *Lockstatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.LockStatus = _field
	return nil
}
func (p *TrxQueryFilter) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProcessId = _field
	return nil
}
func (p *TrxQueryFilter) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxId = _field
	return nil
}
func (p *TrxQueryFilter) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlBlocked = _field
	return nil
}
func (p *TrxQueryFilter) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxIsoLevel = _field
	return nil
}
func (p *TrxQueryFilter) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BlockTrxId = _field
	return nil
}

func (p *TrxQueryFilter) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxQueryFilter")

	var fieldId int16
	if err = oprot.WriteStructBegin("TrxQueryFilter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TrxQueryFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxExecTime() {
		if err = oprot.WriteFieldBegin("TrxExecTime", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.TrxExecTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxStatus() {
		if err = oprot.WriteFieldBegin("TrxStatus", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TrxStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLockStatus() {
		if err = oprot.WriteFieldBegin("LockStatus", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LockStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetProcessId() {
		if err = oprot.WriteFieldBegin("ProcessId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProcessId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxId() {
		if err = oprot.WriteFieldBegin("TrxId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TrxId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlBlocked() {
		if err = oprot.WriteFieldBegin("SqlBlocked", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlBlocked); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxIsoLevel() {
		if err = oprot.WriteFieldBegin("TrxIsoLevel", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TrxIsoLevel); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TrxQueryFilter) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBlockTrxId() {
		if err = oprot.WriteFieldBegin("BlockTrxId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BlockTrxId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TrxQueryFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrxQueryFilter(%+v)", *p)

}

func (p *TrxQueryFilter) DeepEqual(ano *TrxQueryFilter) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TrxExecTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.TrxStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.LockStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.ProcessId) {
		return false
	}
	if !p.Field5DeepEqual(ano.TrxId) {
		return false
	}
	if !p.Field6DeepEqual(ano.SqlBlocked) {
		return false
	}
	if !p.Field7DeepEqual(ano.TrxIsoLevel) {
		return false
	}
	if !p.Field8DeepEqual(ano.BlockTrxId) {
		return false
	}
	return true
}

func (p *TrxQueryFilter) Field1DeepEqual(src *int32) bool {

	if p.TrxExecTime == src {
		return true
	} else if p.TrxExecTime == nil || src == nil {
		return false
	}
	if *p.TrxExecTime != *src {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field2DeepEqual(src *string) bool {

	if p.TrxStatus == src {
		return true
	} else if p.TrxStatus == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TrxStatus, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field3DeepEqual(src *Lockstatus) bool {

	if p.LockStatus == src {
		return true
	} else if p.LockStatus == nil || src == nil {
		return false
	}
	if *p.LockStatus != *src {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field4DeepEqual(src *string) bool {

	if p.ProcessId == src {
		return true
	} else if p.ProcessId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ProcessId, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field5DeepEqual(src *string) bool {

	if p.TrxId == src {
		return true
	} else if p.TrxId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TrxId, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field6DeepEqual(src *string) bool {

	if p.SqlBlocked == src {
		return true
	} else if p.SqlBlocked == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlBlocked, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field7DeepEqual(src *string) bool {

	if p.TrxIsoLevel == src {
		return true
	} else if p.TrxIsoLevel == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TrxIsoLevel, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxQueryFilter) Field8DeepEqual(src *string) bool {

	if p.BlockTrxId == src {
		return true
	} else if p.BlockTrxId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BlockTrxId, *src) != 0 {
		return false
	}
	return true
}

type WaitLockQueryFilter struct {
	RTrxState          *string     `thrift:"RTrxState,1,optional" frugal:"1,optional,string" json:"RTrxState,omitempty"`
	RLockState         *Lockstatus `thrift:"RLockState,2,optional" frugal:"2,optional,Lockstatus" json:"RLockState,omitempty"`
	RTrxId             *string     `thrift:"RTrxId,3,optional" frugal:"3,optional,string" json:"RTrxId,omitempty"`
	RWaitingQuery      *string     `thrift:"RWaitingQuery,4,optional" frugal:"4,optional,string" json:"RWaitingQuery,omitempty"`
	RTrxStarted        *string     `thrift:"RTrxStarted,5,optional" frugal:"5,optional,string" json:"RTrxStarted,omitempty"`
	RTrxWaitStarted    *string     `thrift:"RTrxWaitStarted,6,optional" frugal:"6,optional,string" json:"RTrxWaitStarted,omitempty"`
	RBlockedWaitSecs   *string     `thrift:"RBlockedWaitSecs,7,optional" frugal:"7,optional,string" json:"RBlockedWaitSecs,omitempty"`
	RTrxRowsModified   *int32      `thrift:"RTrxRowsModified,8,optional" frugal:"8,optional,i32" json:"RTrxRowsModified,omitempty"`
	RTrxRowsLocked     *int32      `thrift:"RTrxRowsLocked,9,optional" frugal:"9,optional,i32" json:"RTrxRowsLocked,omitempty"`
	RTrxOperationState *string     `thrift:"RTrxOperationState,10,optional" frugal:"10,optional,string" json:"RTrxOperationState,omitempty"`
	BTrxId             *string     `thrift:"BTrxId,11,optional" frugal:"11,optional,string" json:"BTrxId,omitempty"`
	BTrxState          *string     `thrift:"BTrxState,12,optional" frugal:"12,optional,string" json:"BTrxState,omitempty"`
	BLockState         *Lockstatus `thrift:"BLockState,13,optional" frugal:"13,optional,Lockstatus" json:"BLockState,omitempty"`
	BBlockingQuery     *string     `thrift:"BBlockingQuery,14,optional" frugal:"14,optional,string" json:"BBlockingQuery,omitempty"`
	BTrxStarted        *string     `thrift:"BTrxStarted,15,optional" frugal:"15,optional,string" json:"BTrxStarted,omitempty"`
	BTrxWaitStarted    *string     `thrift:"BTrxWaitStarted,16,optional" frugal:"16,optional,string" json:"BTrxWaitStarted,omitempty"`
	BBlockingWaitSecs  *string     `thrift:"BBlockingWaitSecs,17,optional" frugal:"17,optional,string" json:"BBlockingWaitSecs,omitempty"`
	BTrxRowsModified   *int32      `thrift:"BTrxRowsModified,18,optional" frugal:"18,optional,i32" json:"BTrxRowsModified,omitempty"`
	BTrxRowsLocked     *int32      `thrift:"BTrxRowsLocked,19,optional" frugal:"19,optional,i32" json:"BTrxRowsLocked,omitempty"`
	BTrxOperationState *string     `thrift:"BTrxOperationState,20,optional" frugal:"20,optional,string" json:"BTrxOperationState,omitempty"`
}

func NewWaitLockQueryFilter() *WaitLockQueryFilter {
	return &WaitLockQueryFilter{}
}

func (p *WaitLockQueryFilter) InitDefault() {
}

var WaitLockQueryFilter_RTrxState_DEFAULT string

func (p *WaitLockQueryFilter) GetRTrxState() (v string) {
	if !p.IsSetRTrxState() {
		return WaitLockQueryFilter_RTrxState_DEFAULT
	}
	return *p.RTrxState
}

var WaitLockQueryFilter_RLockState_DEFAULT Lockstatus

func (p *WaitLockQueryFilter) GetRLockState() (v Lockstatus) {
	if !p.IsSetRLockState() {
		return WaitLockQueryFilter_RLockState_DEFAULT
	}
	return *p.RLockState
}

var WaitLockQueryFilter_RTrxId_DEFAULT string

func (p *WaitLockQueryFilter) GetRTrxId() (v string) {
	if !p.IsSetRTrxId() {
		return WaitLockQueryFilter_RTrxId_DEFAULT
	}
	return *p.RTrxId
}

var WaitLockQueryFilter_RWaitingQuery_DEFAULT string

func (p *WaitLockQueryFilter) GetRWaitingQuery() (v string) {
	if !p.IsSetRWaitingQuery() {
		return WaitLockQueryFilter_RWaitingQuery_DEFAULT
	}
	return *p.RWaitingQuery
}

var WaitLockQueryFilter_RTrxStarted_DEFAULT string

func (p *WaitLockQueryFilter) GetRTrxStarted() (v string) {
	if !p.IsSetRTrxStarted() {
		return WaitLockQueryFilter_RTrxStarted_DEFAULT
	}
	return *p.RTrxStarted
}

var WaitLockQueryFilter_RTrxWaitStarted_DEFAULT string

func (p *WaitLockQueryFilter) GetRTrxWaitStarted() (v string) {
	if !p.IsSetRTrxWaitStarted() {
		return WaitLockQueryFilter_RTrxWaitStarted_DEFAULT
	}
	return *p.RTrxWaitStarted
}

var WaitLockQueryFilter_RBlockedWaitSecs_DEFAULT string

func (p *WaitLockQueryFilter) GetRBlockedWaitSecs() (v string) {
	if !p.IsSetRBlockedWaitSecs() {
		return WaitLockQueryFilter_RBlockedWaitSecs_DEFAULT
	}
	return *p.RBlockedWaitSecs
}

var WaitLockQueryFilter_RTrxRowsModified_DEFAULT int32

func (p *WaitLockQueryFilter) GetRTrxRowsModified() (v int32) {
	if !p.IsSetRTrxRowsModified() {
		return WaitLockQueryFilter_RTrxRowsModified_DEFAULT
	}
	return *p.RTrxRowsModified
}

var WaitLockQueryFilter_RTrxRowsLocked_DEFAULT int32

func (p *WaitLockQueryFilter) GetRTrxRowsLocked() (v int32) {
	if !p.IsSetRTrxRowsLocked() {
		return WaitLockQueryFilter_RTrxRowsLocked_DEFAULT
	}
	return *p.RTrxRowsLocked
}

var WaitLockQueryFilter_RTrxOperationState_DEFAULT string

func (p *WaitLockQueryFilter) GetRTrxOperationState() (v string) {
	if !p.IsSetRTrxOperationState() {
		return WaitLockQueryFilter_RTrxOperationState_DEFAULT
	}
	return *p.RTrxOperationState
}

var WaitLockQueryFilter_BTrxId_DEFAULT string

func (p *WaitLockQueryFilter) GetBTrxId() (v string) {
	if !p.IsSetBTrxId() {
		return WaitLockQueryFilter_BTrxId_DEFAULT
	}
	return *p.BTrxId
}

var WaitLockQueryFilter_BTrxState_DEFAULT string

func (p *WaitLockQueryFilter) GetBTrxState() (v string) {
	if !p.IsSetBTrxState() {
		return WaitLockQueryFilter_BTrxState_DEFAULT
	}
	return *p.BTrxState
}

var WaitLockQueryFilter_BLockState_DEFAULT Lockstatus

func (p *WaitLockQueryFilter) GetBLockState() (v Lockstatus) {
	if !p.IsSetBLockState() {
		return WaitLockQueryFilter_BLockState_DEFAULT
	}
	return *p.BLockState
}

var WaitLockQueryFilter_BBlockingQuery_DEFAULT string

func (p *WaitLockQueryFilter) GetBBlockingQuery() (v string) {
	if !p.IsSetBBlockingQuery() {
		return WaitLockQueryFilter_BBlockingQuery_DEFAULT
	}
	return *p.BBlockingQuery
}

var WaitLockQueryFilter_BTrxStarted_DEFAULT string

func (p *WaitLockQueryFilter) GetBTrxStarted() (v string) {
	if !p.IsSetBTrxStarted() {
		return WaitLockQueryFilter_BTrxStarted_DEFAULT
	}
	return *p.BTrxStarted
}

var WaitLockQueryFilter_BTrxWaitStarted_DEFAULT string

func (p *WaitLockQueryFilter) GetBTrxWaitStarted() (v string) {
	if !p.IsSetBTrxWaitStarted() {
		return WaitLockQueryFilter_BTrxWaitStarted_DEFAULT
	}
	return *p.BTrxWaitStarted
}

var WaitLockQueryFilter_BBlockingWaitSecs_DEFAULT string

func (p *WaitLockQueryFilter) GetBBlockingWaitSecs() (v string) {
	if !p.IsSetBBlockingWaitSecs() {
		return WaitLockQueryFilter_BBlockingWaitSecs_DEFAULT
	}
	return *p.BBlockingWaitSecs
}

var WaitLockQueryFilter_BTrxRowsModified_DEFAULT int32

func (p *WaitLockQueryFilter) GetBTrxRowsModified() (v int32) {
	if !p.IsSetBTrxRowsModified() {
		return WaitLockQueryFilter_BTrxRowsModified_DEFAULT
	}
	return *p.BTrxRowsModified
}

var WaitLockQueryFilter_BTrxRowsLocked_DEFAULT int32

func (p *WaitLockQueryFilter) GetBTrxRowsLocked() (v int32) {
	if !p.IsSetBTrxRowsLocked() {
		return WaitLockQueryFilter_BTrxRowsLocked_DEFAULT
	}
	return *p.BTrxRowsLocked
}

var WaitLockQueryFilter_BTrxOperationState_DEFAULT string

func (p *WaitLockQueryFilter) GetBTrxOperationState() (v string) {
	if !p.IsSetBTrxOperationState() {
		return WaitLockQueryFilter_BTrxOperationState_DEFAULT
	}
	return *p.BTrxOperationState
}
func (p *WaitLockQueryFilter) SetRTrxState(val *string) {
	p.RTrxState = val
}
func (p *WaitLockQueryFilter) SetRLockState(val *Lockstatus) {
	p.RLockState = val
}
func (p *WaitLockQueryFilter) SetRTrxId(val *string) {
	p.RTrxId = val
}
func (p *WaitLockQueryFilter) SetRWaitingQuery(val *string) {
	p.RWaitingQuery = val
}
func (p *WaitLockQueryFilter) SetRTrxStarted(val *string) {
	p.RTrxStarted = val
}
func (p *WaitLockQueryFilter) SetRTrxWaitStarted(val *string) {
	p.RTrxWaitStarted = val
}
func (p *WaitLockQueryFilter) SetRBlockedWaitSecs(val *string) {
	p.RBlockedWaitSecs = val
}
func (p *WaitLockQueryFilter) SetRTrxRowsModified(val *int32) {
	p.RTrxRowsModified = val
}
func (p *WaitLockQueryFilter) SetRTrxRowsLocked(val *int32) {
	p.RTrxRowsLocked = val
}
func (p *WaitLockQueryFilter) SetRTrxOperationState(val *string) {
	p.RTrxOperationState = val
}
func (p *WaitLockQueryFilter) SetBTrxId(val *string) {
	p.BTrxId = val
}
func (p *WaitLockQueryFilter) SetBTrxState(val *string) {
	p.BTrxState = val
}
func (p *WaitLockQueryFilter) SetBLockState(val *Lockstatus) {
	p.BLockState = val
}
func (p *WaitLockQueryFilter) SetBBlockingQuery(val *string) {
	p.BBlockingQuery = val
}
func (p *WaitLockQueryFilter) SetBTrxStarted(val *string) {
	p.BTrxStarted = val
}
func (p *WaitLockQueryFilter) SetBTrxWaitStarted(val *string) {
	p.BTrxWaitStarted = val
}
func (p *WaitLockQueryFilter) SetBBlockingWaitSecs(val *string) {
	p.BBlockingWaitSecs = val
}
func (p *WaitLockQueryFilter) SetBTrxRowsModified(val *int32) {
	p.BTrxRowsModified = val
}
func (p *WaitLockQueryFilter) SetBTrxRowsLocked(val *int32) {
	p.BTrxRowsLocked = val
}
func (p *WaitLockQueryFilter) SetBTrxOperationState(val *string) {
	p.BTrxOperationState = val
}

var fieldIDToName_WaitLockQueryFilter = map[int16]string{
	1:  "RTrxState",
	2:  "RLockState",
	3:  "RTrxId",
	4:  "RWaitingQuery",
	5:  "RTrxStarted",
	6:  "RTrxWaitStarted",
	7:  "RBlockedWaitSecs",
	8:  "RTrxRowsModified",
	9:  "RTrxRowsLocked",
	10: "RTrxOperationState",
	11: "BTrxId",
	12: "BTrxState",
	13: "BLockState",
	14: "BBlockingQuery",
	15: "BTrxStarted",
	16: "BTrxWaitStarted",
	17: "BBlockingWaitSecs",
	18: "BTrxRowsModified",
	19: "BTrxRowsLocked",
	20: "BTrxOperationState",
}

func (p *WaitLockQueryFilter) IsSetRTrxState() bool {
	return p.RTrxState != nil
}

func (p *WaitLockQueryFilter) IsSetRLockState() bool {
	return p.RLockState != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxId() bool {
	return p.RTrxId != nil
}

func (p *WaitLockQueryFilter) IsSetRWaitingQuery() bool {
	return p.RWaitingQuery != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxStarted() bool {
	return p.RTrxStarted != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxWaitStarted() bool {
	return p.RTrxWaitStarted != nil
}

func (p *WaitLockQueryFilter) IsSetRBlockedWaitSecs() bool {
	return p.RBlockedWaitSecs != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxRowsModified() bool {
	return p.RTrxRowsModified != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxRowsLocked() bool {
	return p.RTrxRowsLocked != nil
}

func (p *WaitLockQueryFilter) IsSetRTrxOperationState() bool {
	return p.RTrxOperationState != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxId() bool {
	return p.BTrxId != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxState() bool {
	return p.BTrxState != nil
}

func (p *WaitLockQueryFilter) IsSetBLockState() bool {
	return p.BLockState != nil
}

func (p *WaitLockQueryFilter) IsSetBBlockingQuery() bool {
	return p.BBlockingQuery != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxStarted() bool {
	return p.BTrxStarted != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxWaitStarted() bool {
	return p.BTrxWaitStarted != nil
}

func (p *WaitLockQueryFilter) IsSetBBlockingWaitSecs() bool {
	return p.BBlockingWaitSecs != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxRowsModified() bool {
	return p.BTrxRowsModified != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxRowsLocked() bool {
	return p.BTrxRowsLocked != nil
}

func (p *WaitLockQueryFilter) IsSetBTrxOperationState() bool {
	return p.BTrxOperationState != nil
}

func (p *WaitLockQueryFilter) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLockQueryFilter")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLockQueryFilter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WaitLockQueryFilter) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxState = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField2(iprot thrift.TProtocol) error {

	var _field *Lockstatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.RLockState = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxId = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RWaitingQuery = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxStarted = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxWaitStarted = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RBlockedWaitSecs = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxRowsModified = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField9(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxRowsLocked = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RTrxOperationState = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxId = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxState = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField13(iprot thrift.TProtocol) error {

	var _field *Lockstatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.BLockState = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BBlockingQuery = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxStarted = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxWaitStarted = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField17(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BBlockingWaitSecs = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField18(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxRowsModified = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField19(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxRowsLocked = _field
	return nil
}
func (p *WaitLockQueryFilter) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BTrxOperationState = _field
	return nil
}

func (p *WaitLockQueryFilter) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLockQueryFilter")

	var fieldId int16
	if err = oprot.WriteStructBegin("WaitLockQueryFilter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxState() {
		if err = oprot.WriteFieldBegin("RTrxState", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RTrxState); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRLockState() {
		if err = oprot.WriteFieldBegin("RLockState", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RLockState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxId() {
		if err = oprot.WriteFieldBegin("RTrxId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RTrxId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRWaitingQuery() {
		if err = oprot.WriteFieldBegin("RWaitingQuery", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RWaitingQuery); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxStarted() {
		if err = oprot.WriteFieldBegin("RTrxStarted", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RTrxStarted); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxWaitStarted() {
		if err = oprot.WriteFieldBegin("RTrxWaitStarted", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RTrxWaitStarted); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRBlockedWaitSecs() {
		if err = oprot.WriteFieldBegin("RBlockedWaitSecs", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RBlockedWaitSecs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxRowsModified() {
		if err = oprot.WriteFieldBegin("RTrxRowsModified", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.RTrxRowsModified); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxRowsLocked() {
		if err = oprot.WriteFieldBegin("RTrxRowsLocked", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.RTrxRowsLocked); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetRTrxOperationState() {
		if err = oprot.WriteFieldBegin("RTrxOperationState", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RTrxOperationState); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxId() {
		if err = oprot.WriteFieldBegin("BTrxId", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BTrxId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxState() {
		if err = oprot.WriteFieldBegin("BTrxState", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BTrxState); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetBLockState() {
		if err = oprot.WriteFieldBegin("BLockState", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BLockState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetBBlockingQuery() {
		if err = oprot.WriteFieldBegin("BBlockingQuery", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BBlockingQuery); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxStarted() {
		if err = oprot.WriteFieldBegin("BTrxStarted", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BTrxStarted); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxWaitStarted() {
		if err = oprot.WriteFieldBegin("BTrxWaitStarted", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BTrxWaitStarted); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetBBlockingWaitSecs() {
		if err = oprot.WriteFieldBegin("BBlockingWaitSecs", thrift.STRING, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BBlockingWaitSecs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxRowsModified() {
		if err = oprot.WriteFieldBegin("BTrxRowsModified", thrift.I32, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BTrxRowsModified); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxRowsLocked() {
		if err = oprot.WriteFieldBegin("BTrxRowsLocked", thrift.I32, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BTrxRowsLocked); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *WaitLockQueryFilter) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetBTrxOperationState() {
		if err = oprot.WriteFieldBegin("BTrxOperationState", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BTrxOperationState); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *WaitLockQueryFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WaitLockQueryFilter(%+v)", *p)

}

func (p *WaitLockQueryFilter) DeepEqual(ano *WaitLockQueryFilter) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RTrxState) {
		return false
	}
	if !p.Field2DeepEqual(ano.RLockState) {
		return false
	}
	if !p.Field3DeepEqual(ano.RTrxId) {
		return false
	}
	if !p.Field4DeepEqual(ano.RWaitingQuery) {
		return false
	}
	if !p.Field5DeepEqual(ano.RTrxStarted) {
		return false
	}
	if !p.Field6DeepEqual(ano.RTrxWaitStarted) {
		return false
	}
	if !p.Field7DeepEqual(ano.RBlockedWaitSecs) {
		return false
	}
	if !p.Field8DeepEqual(ano.RTrxRowsModified) {
		return false
	}
	if !p.Field9DeepEqual(ano.RTrxRowsLocked) {
		return false
	}
	if !p.Field10DeepEqual(ano.RTrxOperationState) {
		return false
	}
	if !p.Field11DeepEqual(ano.BTrxId) {
		return false
	}
	if !p.Field12DeepEqual(ano.BTrxState) {
		return false
	}
	if !p.Field13DeepEqual(ano.BLockState) {
		return false
	}
	if !p.Field14DeepEqual(ano.BBlockingQuery) {
		return false
	}
	if !p.Field15DeepEqual(ano.BTrxStarted) {
		return false
	}
	if !p.Field16DeepEqual(ano.BTrxWaitStarted) {
		return false
	}
	if !p.Field17DeepEqual(ano.BBlockingWaitSecs) {
		return false
	}
	if !p.Field18DeepEqual(ano.BTrxRowsModified) {
		return false
	}
	if !p.Field19DeepEqual(ano.BTrxRowsLocked) {
		return false
	}
	if !p.Field20DeepEqual(ano.BTrxOperationState) {
		return false
	}
	return true
}

func (p *WaitLockQueryFilter) Field1DeepEqual(src *string) bool {

	if p.RTrxState == src {
		return true
	} else if p.RTrxState == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RTrxState, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field2DeepEqual(src *Lockstatus) bool {

	if p.RLockState == src {
		return true
	} else if p.RLockState == nil || src == nil {
		return false
	}
	if *p.RLockState != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field3DeepEqual(src *string) bool {

	if p.RTrxId == src {
		return true
	} else if p.RTrxId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RTrxId, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field4DeepEqual(src *string) bool {

	if p.RWaitingQuery == src {
		return true
	} else if p.RWaitingQuery == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RWaitingQuery, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field5DeepEqual(src *string) bool {

	if p.RTrxStarted == src {
		return true
	} else if p.RTrxStarted == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RTrxStarted, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field6DeepEqual(src *string) bool {

	if p.RTrxWaitStarted == src {
		return true
	} else if p.RTrxWaitStarted == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RTrxWaitStarted, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field7DeepEqual(src *string) bool {

	if p.RBlockedWaitSecs == src {
		return true
	} else if p.RBlockedWaitSecs == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RBlockedWaitSecs, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field8DeepEqual(src *int32) bool {

	if p.RTrxRowsModified == src {
		return true
	} else if p.RTrxRowsModified == nil || src == nil {
		return false
	}
	if *p.RTrxRowsModified != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field9DeepEqual(src *int32) bool {

	if p.RTrxRowsLocked == src {
		return true
	} else if p.RTrxRowsLocked == nil || src == nil {
		return false
	}
	if *p.RTrxRowsLocked != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field10DeepEqual(src *string) bool {

	if p.RTrxOperationState == src {
		return true
	} else if p.RTrxOperationState == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RTrxOperationState, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field11DeepEqual(src *string) bool {

	if p.BTrxId == src {
		return true
	} else if p.BTrxId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BTrxId, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field12DeepEqual(src *string) bool {

	if p.BTrxState == src {
		return true
	} else if p.BTrxState == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BTrxState, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field13DeepEqual(src *Lockstatus) bool {

	if p.BLockState == src {
		return true
	} else if p.BLockState == nil || src == nil {
		return false
	}
	if *p.BLockState != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field14DeepEqual(src *string) bool {

	if p.BBlockingQuery == src {
		return true
	} else if p.BBlockingQuery == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BBlockingQuery, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field15DeepEqual(src *string) bool {

	if p.BTrxStarted == src {
		return true
	} else if p.BTrxStarted == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BTrxStarted, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field16DeepEqual(src *string) bool {

	if p.BTrxWaitStarted == src {
		return true
	} else if p.BTrxWaitStarted == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BTrxWaitStarted, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field17DeepEqual(src *string) bool {

	if p.BBlockingWaitSecs == src {
		return true
	} else if p.BBlockingWaitSecs == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BBlockingWaitSecs, *src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field18DeepEqual(src *int32) bool {

	if p.BTrxRowsModified == src {
		return true
	} else if p.BTrxRowsModified == nil || src == nil {
		return false
	}
	if *p.BTrxRowsModified != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field19DeepEqual(src *int32) bool {

	if p.BTrxRowsLocked == src {
		return true
	} else if p.BTrxRowsLocked == nil || src == nil {
		return false
	}
	if *p.BTrxRowsLocked != *src {
		return false
	}
	return true
}
func (p *WaitLockQueryFilter) Field20DeepEqual(src *string) bool {

	if p.BTrxOperationState == src {
		return true
	} else if p.BTrxOperationState == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BTrxOperationState, *src) != 0 {
		return false
	}
	return true
}

type DescribeTrxAndLocksReq struct {
	DSType      DSType          `thrift:"DSType,1,required" frugal:"1,required,DSType" json:"DSType"`
	InstanceId  string          `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	PageNumber  int32           `thrift:"PageNumber,3,required" frugal:"3,required,i32" json:"PageNumber"`
	PageSize    int32           `thrift:"PageSize,4,required" frugal:"4,required,i32" json:"PageSize"`
	QueryFilter *TrxQueryFilter `thrift:"QueryFilter,5,optional" frugal:"5,optional,TrxQueryFilter" json:"QueryFilter,omitempty"`
	SortParam   *SortParam      `thrift:"SortParam,6,optional" frugal:"6,optional,SortParam" json:"SortParam,omitempty"`
	Order       *Order          `thrift:"Order,7,optional" frugal:"7,optional,Order" json:"Order,omitempty"`
	RegionId    *string         `thrift:"RegionId,8,optional" frugal:"8,optional,string" json:"RegionId,omitempty"`
	NodeIds     []string        `thrift:"NodeIds,9,optional" frugal:"9,optional,list<string>" json:"NodeIds,omitempty"`
	ShardId     *string         `thrift:"ShardId,10,optional" frugal:"10,optional,string" json:"ShardId,omitempty"`
}

func NewDescribeTrxAndLocksReq() *DescribeTrxAndLocksReq {
	return &DescribeTrxAndLocksReq{}
}

func (p *DescribeTrxAndLocksReq) InitDefault() {
}

func (p *DescribeTrxAndLocksReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *DescribeTrxAndLocksReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTrxAndLocksReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *DescribeTrxAndLocksReq) GetPageSize() (v int32) {
	return p.PageSize
}

var DescribeTrxAndLocksReq_QueryFilter_DEFAULT *TrxQueryFilter

func (p *DescribeTrxAndLocksReq) GetQueryFilter() (v *TrxQueryFilter) {
	if !p.IsSetQueryFilter() {
		return DescribeTrxAndLocksReq_QueryFilter_DEFAULT
	}
	return p.QueryFilter
}

var DescribeTrxAndLocksReq_SortParam_DEFAULT SortParam

func (p *DescribeTrxAndLocksReq) GetSortParam() (v SortParam) {
	if !p.IsSetSortParam() {
		return DescribeTrxAndLocksReq_SortParam_DEFAULT
	}
	return *p.SortParam
}

var DescribeTrxAndLocksReq_Order_DEFAULT Order

func (p *DescribeTrxAndLocksReq) GetOrder() (v Order) {
	if !p.IsSetOrder() {
		return DescribeTrxAndLocksReq_Order_DEFAULT
	}
	return *p.Order
}

var DescribeTrxAndLocksReq_RegionId_DEFAULT string

func (p *DescribeTrxAndLocksReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeTrxAndLocksReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeTrxAndLocksReq_NodeIds_DEFAULT []string

func (p *DescribeTrxAndLocksReq) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return DescribeTrxAndLocksReq_NodeIds_DEFAULT
	}
	return p.NodeIds
}

var DescribeTrxAndLocksReq_ShardId_DEFAULT string

func (p *DescribeTrxAndLocksReq) GetShardId() (v string) {
	if !p.IsSetShardId() {
		return DescribeTrxAndLocksReq_ShardId_DEFAULT
	}
	return *p.ShardId
}
func (p *DescribeTrxAndLocksReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeTrxAndLocksReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTrxAndLocksReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *DescribeTrxAndLocksReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *DescribeTrxAndLocksReq) SetQueryFilter(val *TrxQueryFilter) {
	p.QueryFilter = val
}
func (p *DescribeTrxAndLocksReq) SetSortParam(val *SortParam) {
	p.SortParam = val
}
func (p *DescribeTrxAndLocksReq) SetOrder(val *Order) {
	p.Order = val
}
func (p *DescribeTrxAndLocksReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeTrxAndLocksReq) SetNodeIds(val []string) {
	p.NodeIds = val
}
func (p *DescribeTrxAndLocksReq) SetShardId(val *string) {
	p.ShardId = val
}

var fieldIDToName_DescribeTrxAndLocksReq = map[int16]string{
	1:  "DSType",
	2:  "InstanceId",
	3:  "PageNumber",
	4:  "PageSize",
	5:  "QueryFilter",
	6:  "SortParam",
	7:  "Order",
	8:  "RegionId",
	9:  "NodeIds",
	10: "ShardId",
}

func (p *DescribeTrxAndLocksReq) IsSetQueryFilter() bool {
	return p.QueryFilter != nil
}

func (p *DescribeTrxAndLocksReq) IsSetSortParam() bool {
	return p.SortParam != nil
}

func (p *DescribeTrxAndLocksReq) IsSetOrder() bool {
	return p.Order != nil
}

func (p *DescribeTrxAndLocksReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeTrxAndLocksReq) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *DescribeTrxAndLocksReq) IsSetShardId() bool {
	return p.ShardId != nil
}

func (p *DescribeTrxAndLocksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxAndLocksReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDSType bool = false
	var issetInstanceId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDSType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxAndLocksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxAndLocksReq[fieldId]))
}

func (p *DescribeTrxAndLocksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField5(iprot thrift.TProtocol) error {
	_field := NewTrxQueryFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.QueryFilter = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *SortParam
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortParam(v)
		_field = &tmp
	}
	p.SortParam = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *Order
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := Order(v)
		_field = &tmp
	}
	p.Order = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}
func (p *DescribeTrxAndLocksReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardId = _field
	return nil
}

func (p *DescribeTrxAndLocksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxAndLocksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxAndLocksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryFilter() {
		if err = oprot.WriteFieldBegin("QueryFilter", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.QueryFilter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortParam() {
		if err = oprot.WriteFieldBegin("SortParam", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortParam)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrder() {
		if err = oprot.WriteFieldBegin("Order", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Order)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardId() {
		if err = oprot.WriteFieldBegin("ShardId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeTrxAndLocksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxAndLocksReq(%+v)", *p)

}

func (p *DescribeTrxAndLocksReq) DeepEqual(ano *DescribeTrxAndLocksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.QueryFilter) {
		return false
	}
	if !p.Field6DeepEqual(ano.SortParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.Order) {
		return false
	}
	if !p.Field8DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeIds) {
		return false
	}
	if !p.Field10DeepEqual(ano.ShardId) {
		return false
	}
	return true
}

func (p *DescribeTrxAndLocksReq) Field1DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field3DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field4DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field5DeepEqual(src *TrxQueryFilter) bool {

	if !p.QueryFilter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field6DeepEqual(src *SortParam) bool {

	if p.SortParam == src {
		return true
	} else if p.SortParam == nil || src == nil {
		return false
	}
	if *p.SortParam != *src {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field7DeepEqual(src *Order) bool {

	if p.Order == src {
		return true
	} else if p.Order == nil || src == nil {
		return false
	}
	if *p.Order != *src {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field8DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field9DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeTrxAndLocksReq) Field10DeepEqual(src *string) bool {

	if p.ShardId == src {
		return true
	} else if p.ShardId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardId, *src) != 0 {
		return false
	}
	return true
}

type DescribeTrxAndLocksResp struct {
	TrxAndLockList []*TrxAndLock `thrift:"TrxAndLockList,1,required" frugal:"1,required,list<TrxAndLock>" json:"TrxAndLockList"`
	Total          int32         `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeTrxAndLocksResp() *DescribeTrxAndLocksResp {
	return &DescribeTrxAndLocksResp{}
}

func (p *DescribeTrxAndLocksResp) InitDefault() {
}

func (p *DescribeTrxAndLocksResp) GetTrxAndLockList() (v []*TrxAndLock) {
	return p.TrxAndLockList
}

func (p *DescribeTrxAndLocksResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTrxAndLocksResp) SetTrxAndLockList(val []*TrxAndLock) {
	p.TrxAndLockList = val
}
func (p *DescribeTrxAndLocksResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTrxAndLocksResp = map[int16]string{
	1: "TrxAndLockList",
	2: "Total",
}

func (p *DescribeTrxAndLocksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxAndLocksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxAndLockList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxAndLockList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTrxAndLockList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxAndLocksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxAndLocksResp[fieldId]))
}

func (p *DescribeTrxAndLocksResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TrxAndLock, 0, size)
	values := make([]TrxAndLock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TrxAndLockList = _field
	return nil
}
func (p *DescribeTrxAndLocksResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTrxAndLocksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxAndLocksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxAndLocksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxAndLocksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxAndLockList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TrxAndLockList)); err != nil {
		return err
	}
	for _, v := range p.TrxAndLockList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxAndLocksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxAndLocksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxAndLocksResp(%+v)", *p)

}

func (p *DescribeTrxAndLocksResp) DeepEqual(ano *DescribeTrxAndLocksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TrxAndLockList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTrxAndLocksResp) Field1DeepEqual(src []*TrxAndLock) bool {

	if len(p.TrxAndLockList) != len(src) {
		return false
	}
	for i, v := range p.TrxAndLockList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTrxAndLocksResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type LockStatistics struct {
	InnoDBLockOverview *TrxAndLockOverview `thrift:"InnoDBLockOverview,1,required" frugal:"1,required,TrxAndLockOverview" json:"InnoDBLockOverview"`
	MetaDBLockOverview *TrxAndLockOverview `thrift:"MetaDBLockOverview,2,required" frugal:"2,required,TrxAndLockOverview" json:"MetaDBLockOverview"`
}

func NewLockStatistics() *LockStatistics {
	return &LockStatistics{}
}

func (p *LockStatistics) InitDefault() {
}

var LockStatistics_InnoDBLockOverview_DEFAULT *TrxAndLockOverview

func (p *LockStatistics) GetInnoDBLockOverview() (v *TrxAndLockOverview) {
	if !p.IsSetInnoDBLockOverview() {
		return LockStatistics_InnoDBLockOverview_DEFAULT
	}
	return p.InnoDBLockOverview
}

var LockStatistics_MetaDBLockOverview_DEFAULT *TrxAndLockOverview

func (p *LockStatistics) GetMetaDBLockOverview() (v *TrxAndLockOverview) {
	if !p.IsSetMetaDBLockOverview() {
		return LockStatistics_MetaDBLockOverview_DEFAULT
	}
	return p.MetaDBLockOverview
}
func (p *LockStatistics) SetInnoDBLockOverview(val *TrxAndLockOverview) {
	p.InnoDBLockOverview = val
}
func (p *LockStatistics) SetMetaDBLockOverview(val *TrxAndLockOverview) {
	p.MetaDBLockOverview = val
}

var fieldIDToName_LockStatistics = map[int16]string{
	1: "InnoDBLockOverview",
	2: "MetaDBLockOverview",
}

func (p *LockStatistics) IsSetInnoDBLockOverview() bool {
	return p.InnoDBLockOverview != nil
}

func (p *LockStatistics) IsSetMetaDBLockOverview() bool {
	return p.MetaDBLockOverview != nil
}

func (p *LockStatistics) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LockStatistics")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInnoDBLockOverview bool = false
	var issetMetaDBLockOverview bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInnoDBLockOverview = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetaDBLockOverview = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInnoDBLockOverview {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMetaDBLockOverview {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LockStatistics[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LockStatistics[fieldId]))
}

func (p *LockStatistics) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTrxAndLockOverview()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InnoDBLockOverview = _field
	return nil
}
func (p *LockStatistics) ReadField2(iprot thrift.TProtocol) error {
	_field := NewTrxAndLockOverview()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.MetaDBLockOverview = _field
	return nil
}

func (p *LockStatistics) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LockStatistics")

	var fieldId int16
	if err = oprot.WriteStructBegin("LockStatistics"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LockStatistics) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InnoDBLockOverview", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InnoDBLockOverview.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LockStatistics) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MetaDBLockOverview", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.MetaDBLockOverview.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LockStatistics) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LockStatistics(%+v)", *p)

}

func (p *LockStatistics) DeepEqual(ano *LockStatistics) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InnoDBLockOverview) {
		return false
	}
	if !p.Field2DeepEqual(ano.MetaDBLockOverview) {
		return false
	}
	return true
}

func (p *LockStatistics) Field1DeepEqual(src *TrxAndLockOverview) bool {

	if !p.InnoDBLockOverview.DeepEqual(src) {
		return false
	}
	return true
}
func (p *LockStatistics) Field2DeepEqual(src *TrxAndLockOverview) bool {

	if !p.MetaDBLockOverview.DeepEqual(src) {
		return false
	}
	return true
}

type WaitLockDetail struct {
	RequestedLockId string `thrift:"RequestedLockId,1,required" frugal:"1,required,string" json:"RequestedLockId"`
	RequestedTrxId  string `thrift:"RequestedTrxId,2,required" frugal:"2,required,string" json:"RequestedTrxId"`
	SqlText         string `thrift:"SqlText,3,required" frugal:"3,required,string" json:"SqlText"`
	LockInfo        *Lock  `thrift:"LockInfo,4,required" frugal:"4,required,Lock" json:"LockInfo"`
}

func NewWaitLockDetail() *WaitLockDetail {
	return &WaitLockDetail{}
}

func (p *WaitLockDetail) InitDefault() {
}

func (p *WaitLockDetail) GetRequestedLockId() (v string) {
	return p.RequestedLockId
}

func (p *WaitLockDetail) GetRequestedTrxId() (v string) {
	return p.RequestedTrxId
}

func (p *WaitLockDetail) GetSqlText() (v string) {
	return p.SqlText
}

var WaitLockDetail_LockInfo_DEFAULT *Lock

func (p *WaitLockDetail) GetLockInfo() (v *Lock) {
	if !p.IsSetLockInfo() {
		return WaitLockDetail_LockInfo_DEFAULT
	}
	return p.LockInfo
}
func (p *WaitLockDetail) SetRequestedLockId(val string) {
	p.RequestedLockId = val
}
func (p *WaitLockDetail) SetRequestedTrxId(val string) {
	p.RequestedTrxId = val
}
func (p *WaitLockDetail) SetSqlText(val string) {
	p.SqlText = val
}
func (p *WaitLockDetail) SetLockInfo(val *Lock) {
	p.LockInfo = val
}

var fieldIDToName_WaitLockDetail = map[int16]string{
	1: "RequestedLockId",
	2: "RequestedTrxId",
	3: "SqlText",
	4: "LockInfo",
}

func (p *WaitLockDetail) IsSetLockInfo() bool {
	return p.LockInfo != nil
}

func (p *WaitLockDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLockDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRequestedLockId bool = false
	var issetRequestedTrxId bool = false
	var issetSqlText bool = false
	var issetLockInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRequestedLockId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRequestedTrxId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRequestedLockId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRequestedTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlText {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockInfo {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLockDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WaitLockDetail[fieldId]))
}

func (p *WaitLockDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RequestedLockId = _field
	return nil
}
func (p *WaitLockDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RequestedTrxId = _field
	return nil
}
func (p *WaitLockDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlText = _field
	return nil
}
func (p *WaitLockDetail) ReadField4(iprot thrift.TProtocol) error {
	_field := NewLock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LockInfo = _field
	return nil
}

func (p *WaitLockDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLockDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("WaitLockDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WaitLockDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RequestedLockId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RequestedLockId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WaitLockDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RequestedTrxId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RequestedTrxId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WaitLockDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlText", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WaitLockDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockInfo", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.LockInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *WaitLockDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WaitLockDetail(%+v)", *p)

}

func (p *WaitLockDetail) DeepEqual(ano *WaitLockDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RequestedLockId) {
		return false
	}
	if !p.Field2DeepEqual(ano.RequestedTrxId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlText) {
		return false
	}
	if !p.Field4DeepEqual(ano.LockInfo) {
		return false
	}
	return true
}

func (p *WaitLockDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RequestedLockId, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RequestedTrxId, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SqlText, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLockDetail) Field4DeepEqual(src *Lock) bool {

	if !p.LockInfo.DeepEqual(src) {
		return false
	}
	return true
}

type TrxAndLockOverview struct {
	TotalHoldLock        int32 `thrift:"TotalHoldLock,1,required" frugal:"1,required,i32" json:"TotalHoldLock"`
	TotalWaitLock        int32 `thrift:"TotalWaitLock,2,required" frugal:"2,required,i32" json:"TotalWaitLock"`
	TimeGreaterThanCount int32 `thrift:"timeGreaterThanCount,3,required" frugal:"3,required,i32" json:"timeGreaterThanCount"`
}

func NewTrxAndLockOverview() *TrxAndLockOverview {
	return &TrxAndLockOverview{}
}

func (p *TrxAndLockOverview) InitDefault() {
}

func (p *TrxAndLockOverview) GetTotalHoldLock() (v int32) {
	return p.TotalHoldLock
}

func (p *TrxAndLockOverview) GetTotalWaitLock() (v int32) {
	return p.TotalWaitLock
}

func (p *TrxAndLockOverview) GetTimeGreaterThanCount() (v int32) {
	return p.TimeGreaterThanCount
}
func (p *TrxAndLockOverview) SetTotalHoldLock(val int32) {
	p.TotalHoldLock = val
}
func (p *TrxAndLockOverview) SetTotalWaitLock(val int32) {
	p.TotalWaitLock = val
}
func (p *TrxAndLockOverview) SetTimeGreaterThanCount(val int32) {
	p.TimeGreaterThanCount = val
}

var fieldIDToName_TrxAndLockOverview = map[int16]string{
	1: "TotalHoldLock",
	2: "TotalWaitLock",
	3: "timeGreaterThanCount",
}

func (p *TrxAndLockOverview) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxAndLockOverview")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotalHoldLock bool = false
	var issetTotalWaitLock bool = false
	var issetTimeGreaterThanCount bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalHoldLock = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalWaitLock = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeGreaterThanCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotalHoldLock {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotalWaitLock {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTimeGreaterThanCount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxAndLockOverview[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TrxAndLockOverview[fieldId]))
}

func (p *TrxAndLockOverview) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalHoldLock = _field
	return nil
}
func (p *TrxAndLockOverview) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalWaitLock = _field
	return nil
}
func (p *TrxAndLockOverview) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeGreaterThanCount = _field
	return nil
}

func (p *TrxAndLockOverview) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxAndLockOverview")

	var fieldId int16
	if err = oprot.WriteStructBegin("TrxAndLockOverview"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TrxAndLockOverview) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TotalHoldLock", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TotalHoldLock); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TrxAndLockOverview) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TotalWaitLock", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TotalWaitLock); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TrxAndLockOverview) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("timeGreaterThanCount", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TimeGreaterThanCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TrxAndLockOverview) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrxAndLockOverview(%+v)", *p)

}

func (p *TrxAndLockOverview) DeepEqual(ano *TrxAndLockOverview) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TotalHoldLock) {
		return false
	}
	if !p.Field2DeepEqual(ano.TotalWaitLock) {
		return false
	}
	if !p.Field3DeepEqual(ano.TimeGreaterThanCount) {
		return false
	}
	return true
}

func (p *TrxAndLockOverview) Field1DeepEqual(src int32) bool {

	if p.TotalHoldLock != src {
		return false
	}
	return true
}
func (p *TrxAndLockOverview) Field2DeepEqual(src int32) bool {

	if p.TotalWaitLock != src {
		return false
	}
	return true
}
func (p *TrxAndLockOverview) Field3DeepEqual(src int32) bool {

	if p.TimeGreaterThanCount != src {
		return false
	}
	return true
}

type TrxAndLock struct {
	ProcessId        string          `thrift:"ProcessId,1,required" frugal:"1,required,string" json:"ProcessId"`
	TrxId            string          `thrift:"TrxId,2,required" frugal:"2,required,string" json:"TrxId"`
	Trxstatus        string          `thrift:"Trxstatus,3,required" frugal:"3,required,string" json:"Trxstatus"`
	Lockstatus       Lockstatus      `thrift:"Lockstatus,4,required" frugal:"4,required,Lockstatus" json:"Lockstatus"`
	TrxIsoLevel      string          `thrift:"TrxIsoLevel,5,required" frugal:"5,required,string" json:"TrxIsoLevel"`
	TrxStartTime     string          `thrift:"TrxStartTime,6,required" frugal:"6,required,string" json:"TrxStartTime"`
	TrxWaitStartTime *string         `thrift:"TrxWaitStartTime,7,optional" frugal:"7,optional,string" json:"TrxWaitStartTime,omitempty"`
	SqlBlocked       *string         `thrift:"SqlBlocked,8,optional" frugal:"8,optional,string" json:"SqlBlocked,omitempty"`
	TrxTablesLocked  int32           `thrift:"TrxTablesLocked,9,required" frugal:"9,required,i32" json:"TrxTablesLocked"`
	TrxRowsLocked    int32           `thrift:"TrxRowsLocked,10,required" frugal:"10,required,i32" json:"TrxRowsLocked"`
	TrxRowsModified  int32           `thrift:"TrxRowsModified,11,required" frugal:"11,required,i32" json:"TrxRowsModified"`
	LockList         []*Lock         `thrift:"LockList,12,required" frugal:"12,required,list<Lock>" json:"LockList"`
	NodeId           *string         `thrift:"NodeId,13,optional" frugal:"13,optional,string" json:"NodeId,omitempty"`
	NodeType         *string         `thrift:"NodeType,14,optional" frugal:"14,optional,string" json:"NodeType,omitempty"`
	TrxExecTime      int32           `thrift:"TrxExecTime,15,required" frugal:"15,required,i32" json:"TrxExecTime"`
	WaitLockDetail   *WaitLockDetail `thrift:"WaitLockDetail,16,optional" frugal:"16,optional,WaitLockDetail" json:"WaitLockDetail,omitempty"`
	BlockTrxId       *string         `thrift:"BlockTrxId,17,optional" frugal:"17,optional,string" json:"BlockTrxId,omitempty"`
	SnapshotTime     int64           `thrift:"SnapshotTime,18,required" frugal:"18,required,i64" json:"SnapshotTime"`
	TrxChangeTime    *string         `thrift:"TrxChangeTime,19,optional" frugal:"19,optional,string" json:"TrxChangeTime,omitempty"`
}

func NewTrxAndLock() *TrxAndLock {
	return &TrxAndLock{}
}

func (p *TrxAndLock) InitDefault() {
}

func (p *TrxAndLock) GetProcessId() (v string) {
	return p.ProcessId
}

func (p *TrxAndLock) GetTrxId() (v string) {
	return p.TrxId
}

func (p *TrxAndLock) GetTrxstatus() (v string) {
	return p.Trxstatus
}

func (p *TrxAndLock) GetLockstatus() (v Lockstatus) {
	return p.Lockstatus
}

func (p *TrxAndLock) GetTrxIsoLevel() (v string) {
	return p.TrxIsoLevel
}

func (p *TrxAndLock) GetTrxStartTime() (v string) {
	return p.TrxStartTime
}

var TrxAndLock_TrxWaitStartTime_DEFAULT string

func (p *TrxAndLock) GetTrxWaitStartTime() (v string) {
	if !p.IsSetTrxWaitStartTime() {
		return TrxAndLock_TrxWaitStartTime_DEFAULT
	}
	return *p.TrxWaitStartTime
}

var TrxAndLock_SqlBlocked_DEFAULT string

func (p *TrxAndLock) GetSqlBlocked() (v string) {
	if !p.IsSetSqlBlocked() {
		return TrxAndLock_SqlBlocked_DEFAULT
	}
	return *p.SqlBlocked
}

func (p *TrxAndLock) GetTrxTablesLocked() (v int32) {
	return p.TrxTablesLocked
}

func (p *TrxAndLock) GetTrxRowsLocked() (v int32) {
	return p.TrxRowsLocked
}

func (p *TrxAndLock) GetTrxRowsModified() (v int32) {
	return p.TrxRowsModified
}

func (p *TrxAndLock) GetLockList() (v []*Lock) {
	return p.LockList
}

var TrxAndLock_NodeId_DEFAULT string

func (p *TrxAndLock) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return TrxAndLock_NodeId_DEFAULT
	}
	return *p.NodeId
}

var TrxAndLock_NodeType_DEFAULT string

func (p *TrxAndLock) GetNodeType() (v string) {
	if !p.IsSetNodeType() {
		return TrxAndLock_NodeType_DEFAULT
	}
	return *p.NodeType
}

func (p *TrxAndLock) GetTrxExecTime() (v int32) {
	return p.TrxExecTime
}

var TrxAndLock_WaitLockDetail_DEFAULT *WaitLockDetail

func (p *TrxAndLock) GetWaitLockDetail() (v *WaitLockDetail) {
	if !p.IsSetWaitLockDetail() {
		return TrxAndLock_WaitLockDetail_DEFAULT
	}
	return p.WaitLockDetail
}

var TrxAndLock_BlockTrxId_DEFAULT string

func (p *TrxAndLock) GetBlockTrxId() (v string) {
	if !p.IsSetBlockTrxId() {
		return TrxAndLock_BlockTrxId_DEFAULT
	}
	return *p.BlockTrxId
}

func (p *TrxAndLock) GetSnapshotTime() (v int64) {
	return p.SnapshotTime
}

var TrxAndLock_TrxChangeTime_DEFAULT string

func (p *TrxAndLock) GetTrxChangeTime() (v string) {
	if !p.IsSetTrxChangeTime() {
		return TrxAndLock_TrxChangeTime_DEFAULT
	}
	return *p.TrxChangeTime
}
func (p *TrxAndLock) SetProcessId(val string) {
	p.ProcessId = val
}
func (p *TrxAndLock) SetTrxId(val string) {
	p.TrxId = val
}
func (p *TrxAndLock) SetTrxstatus(val string) {
	p.Trxstatus = val
}
func (p *TrxAndLock) SetLockstatus(val Lockstatus) {
	p.Lockstatus = val
}
func (p *TrxAndLock) SetTrxIsoLevel(val string) {
	p.TrxIsoLevel = val
}
func (p *TrxAndLock) SetTrxStartTime(val string) {
	p.TrxStartTime = val
}
func (p *TrxAndLock) SetTrxWaitStartTime(val *string) {
	p.TrxWaitStartTime = val
}
func (p *TrxAndLock) SetSqlBlocked(val *string) {
	p.SqlBlocked = val
}
func (p *TrxAndLock) SetTrxTablesLocked(val int32) {
	p.TrxTablesLocked = val
}
func (p *TrxAndLock) SetTrxRowsLocked(val int32) {
	p.TrxRowsLocked = val
}
func (p *TrxAndLock) SetTrxRowsModified(val int32) {
	p.TrxRowsModified = val
}
func (p *TrxAndLock) SetLockList(val []*Lock) {
	p.LockList = val
}
func (p *TrxAndLock) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *TrxAndLock) SetNodeType(val *string) {
	p.NodeType = val
}
func (p *TrxAndLock) SetTrxExecTime(val int32) {
	p.TrxExecTime = val
}
func (p *TrxAndLock) SetWaitLockDetail(val *WaitLockDetail) {
	p.WaitLockDetail = val
}
func (p *TrxAndLock) SetBlockTrxId(val *string) {
	p.BlockTrxId = val
}
func (p *TrxAndLock) SetSnapshotTime(val int64) {
	p.SnapshotTime = val
}
func (p *TrxAndLock) SetTrxChangeTime(val *string) {
	p.TrxChangeTime = val
}

var fieldIDToName_TrxAndLock = map[int16]string{
	1:  "ProcessId",
	2:  "TrxId",
	3:  "Trxstatus",
	4:  "Lockstatus",
	5:  "TrxIsoLevel",
	6:  "TrxStartTime",
	7:  "TrxWaitStartTime",
	8:  "SqlBlocked",
	9:  "TrxTablesLocked",
	10: "TrxRowsLocked",
	11: "TrxRowsModified",
	12: "LockList",
	13: "NodeId",
	14: "NodeType",
	15: "TrxExecTime",
	16: "WaitLockDetail",
	17: "BlockTrxId",
	18: "SnapshotTime",
	19: "TrxChangeTime",
}

func (p *TrxAndLock) IsSetTrxWaitStartTime() bool {
	return p.TrxWaitStartTime != nil
}

func (p *TrxAndLock) IsSetSqlBlocked() bool {
	return p.SqlBlocked != nil
}

func (p *TrxAndLock) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *TrxAndLock) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *TrxAndLock) IsSetWaitLockDetail() bool {
	return p.WaitLockDetail != nil
}

func (p *TrxAndLock) IsSetBlockTrxId() bool {
	return p.BlockTrxId != nil
}

func (p *TrxAndLock) IsSetTrxChangeTime() bool {
	return p.TrxChangeTime != nil
}

func (p *TrxAndLock) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxAndLock")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProcessId bool = false
	var issetTrxId bool = false
	var issetTrxstatus bool = false
	var issetLockstatus bool = false
	var issetTrxIsoLevel bool = false
	var issetTrxStartTime bool = false
	var issetTrxTablesLocked bool = false
	var issetTrxRowsLocked bool = false
	var issetTrxRowsModified bool = false
	var issetLockList bool = false
	var issetTrxExecTime bool = false
	var issetSnapshotTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxstatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockstatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxIsoLevel = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxTablesLocked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxRowsLocked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxRowsModified = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxExecTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProcessId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTrxstatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockstatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTrxIsoLevel {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTrxStartTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTrxTablesLocked {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetTrxRowsLocked {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetTrxRowsModified {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetLockList {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetTrxExecTime {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetSnapshotTime {
		fieldId = 18
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxAndLock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TrxAndLock[fieldId]))
}

func (p *TrxAndLock) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProcessId = _field
	return nil
}
func (p *TrxAndLock) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxId = _field
	return nil
}
func (p *TrxAndLock) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Trxstatus = _field
	return nil
}
func (p *TrxAndLock) ReadField4(iprot thrift.TProtocol) error {

	var _field Lockstatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = Lockstatus(v)
	}
	p.Lockstatus = _field
	return nil
}
func (p *TrxAndLock) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxIsoLevel = _field
	return nil
}
func (p *TrxAndLock) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxStartTime = _field
	return nil
}
func (p *TrxAndLock) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxWaitStartTime = _field
	return nil
}
func (p *TrxAndLock) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlBlocked = _field
	return nil
}
func (p *TrxAndLock) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxTablesLocked = _field
	return nil
}
func (p *TrxAndLock) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxRowsLocked = _field
	return nil
}
func (p *TrxAndLock) ReadField11(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxRowsModified = _field
	return nil
}
func (p *TrxAndLock) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Lock, 0, size)
	values := make([]Lock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.LockList = _field
	return nil
}
func (p *TrxAndLock) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *TrxAndLock) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeType = _field
	return nil
}
func (p *TrxAndLock) ReadField15(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxExecTime = _field
	return nil
}
func (p *TrxAndLock) ReadField16(iprot thrift.TProtocol) error {
	_field := NewWaitLockDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.WaitLockDetail = _field
	return nil
}
func (p *TrxAndLock) ReadField17(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BlockTrxId = _field
	return nil
}
func (p *TrxAndLock) ReadField18(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SnapshotTime = _field
	return nil
}
func (p *TrxAndLock) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TrxChangeTime = _field
	return nil
}

func (p *TrxAndLock) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxAndLock")

	var fieldId int16
	if err = oprot.WriteStructBegin("TrxAndLock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TrxAndLock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProcessId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProcessId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TrxAndLock) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TrxAndLock) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Trxstatus", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Trxstatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TrxAndLock) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Lockstatus", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Lockstatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TrxAndLock) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxIsoLevel", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxIsoLevel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TrxAndLock) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxStartTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TrxAndLock) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxWaitStartTime() {
		if err = oprot.WriteFieldBegin("TrxWaitStartTime", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TrxWaitStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TrxAndLock) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlBlocked() {
		if err = oprot.WriteFieldBegin("SqlBlocked", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlBlocked); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TrxAndLock) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxTablesLocked", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TrxTablesLocked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *TrxAndLock) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxRowsLocked", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TrxRowsLocked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *TrxAndLock) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxRowsModified", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TrxRowsModified); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *TrxAndLock) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockList", thrift.LIST, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.LockList)); err != nil {
		return err
	}
	for _, v := range p.LockList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *TrxAndLock) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *TrxAndLock) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *TrxAndLock) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxExecTime", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TrxExecTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *TrxAndLock) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetWaitLockDetail() {
		if err = oprot.WriteFieldBegin("WaitLockDetail", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.WaitLockDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *TrxAndLock) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetBlockTrxId() {
		if err = oprot.WriteFieldBegin("BlockTrxId", thrift.STRING, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BlockTrxId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *TrxAndLock) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SnapshotTime", thrift.I64, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SnapshotTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *TrxAndLock) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrxChangeTime() {
		if err = oprot.WriteFieldBegin("TrxChangeTime", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TrxChangeTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *TrxAndLock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrxAndLock(%+v)", *p)

}

func (p *TrxAndLock) DeepEqual(ano *TrxAndLock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProcessId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TrxId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Trxstatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.Lockstatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.TrxIsoLevel) {
		return false
	}
	if !p.Field6DeepEqual(ano.TrxStartTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.TrxWaitStartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.SqlBlocked) {
		return false
	}
	if !p.Field9DeepEqual(ano.TrxTablesLocked) {
		return false
	}
	if !p.Field10DeepEqual(ano.TrxRowsLocked) {
		return false
	}
	if !p.Field11DeepEqual(ano.TrxRowsModified) {
		return false
	}
	if !p.Field12DeepEqual(ano.LockList) {
		return false
	}
	if !p.Field13DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field15DeepEqual(ano.TrxExecTime) {
		return false
	}
	if !p.Field16DeepEqual(ano.WaitLockDetail) {
		return false
	}
	if !p.Field17DeepEqual(ano.BlockTrxId) {
		return false
	}
	if !p.Field18DeepEqual(ano.SnapshotTime) {
		return false
	}
	if !p.Field19DeepEqual(ano.TrxChangeTime) {
		return false
	}
	return true
}

func (p *TrxAndLock) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ProcessId, src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TrxId, src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Trxstatus, src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field4DeepEqual(src Lockstatus) bool {

	if p.Lockstatus != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field5DeepEqual(src string) bool {

	if strings.Compare(p.TrxIsoLevel, src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field6DeepEqual(src string) bool {

	if strings.Compare(p.TrxStartTime, src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field7DeepEqual(src *string) bool {

	if p.TrxWaitStartTime == src {
		return true
	} else if p.TrxWaitStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TrxWaitStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field8DeepEqual(src *string) bool {

	if p.SqlBlocked == src {
		return true
	} else if p.SqlBlocked == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlBlocked, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field9DeepEqual(src int32) bool {

	if p.TrxTablesLocked != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field10DeepEqual(src int32) bool {

	if p.TrxRowsLocked != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field11DeepEqual(src int32) bool {

	if p.TrxRowsModified != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field12DeepEqual(src []*Lock) bool {

	if len(p.LockList) != len(src) {
		return false
	}
	for i, v := range p.LockList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *TrxAndLock) Field13DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field14DeepEqual(src *string) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeType, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field15DeepEqual(src int32) bool {

	if p.TrxExecTime != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field16DeepEqual(src *WaitLockDetail) bool {

	if !p.WaitLockDetail.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TrxAndLock) Field17DeepEqual(src *string) bool {

	if p.BlockTrxId == src {
		return true
	} else if p.BlockTrxId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BlockTrxId, *src) != 0 {
		return false
	}
	return true
}
func (p *TrxAndLock) Field18DeepEqual(src int64) bool {

	if p.SnapshotTime != src {
		return false
	}
	return true
}
func (p *TrxAndLock) Field19DeepEqual(src *string) bool {

	if p.TrxChangeTime == src {
		return true
	} else if p.TrxChangeTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TrxChangeTime, *src) != 0 {
		return false
	}
	return true
}

type Lock struct {
	LockProperty       string `thrift:"LockProperty,1,required" frugal:"1,required,string" json:"LockProperty"`
	LockId             string `thrift:"LockId,2,required" frugal:"2,required,string" json:"LockId"`
	LockAssociateIndex string `thrift:"LockAssociateIndex,3,required" frugal:"3,required,string" json:"LockAssociateIndex"`
	LockAssociateTable string `thrift:"LockAssociateTable,4,required" frugal:"4,required,string" json:"LockAssociateTable"`
	LockType           string `thrift:"LockType,5,required" frugal:"5,required,string" json:"LockType"`
	LockModel          string `thrift:"LockModel,6,required" frugal:"6,required,string" json:"LockModel"`
}

func NewLock() *Lock {
	return &Lock{}
}

func (p *Lock) InitDefault() {
}

func (p *Lock) GetLockProperty() (v string) {
	return p.LockProperty
}

func (p *Lock) GetLockId() (v string) {
	return p.LockId
}

func (p *Lock) GetLockAssociateIndex() (v string) {
	return p.LockAssociateIndex
}

func (p *Lock) GetLockAssociateTable() (v string) {
	return p.LockAssociateTable
}

func (p *Lock) GetLockType() (v string) {
	return p.LockType
}

func (p *Lock) GetLockModel() (v string) {
	return p.LockModel
}
func (p *Lock) SetLockProperty(val string) {
	p.LockProperty = val
}
func (p *Lock) SetLockId(val string) {
	p.LockId = val
}
func (p *Lock) SetLockAssociateIndex(val string) {
	p.LockAssociateIndex = val
}
func (p *Lock) SetLockAssociateTable(val string) {
	p.LockAssociateTable = val
}
func (p *Lock) SetLockType(val string) {
	p.LockType = val
}
func (p *Lock) SetLockModel(val string) {
	p.LockModel = val
}

var fieldIDToName_Lock = map[int16]string{
	1: "LockProperty",
	2: "LockId",
	3: "LockAssociateIndex",
	4: "LockAssociateTable",
	5: "LockType",
	6: "LockModel",
}

func (p *Lock) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Lock")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLockProperty bool = false
	var issetLockId bool = false
	var issetLockAssociateIndex bool = false
	var issetLockAssociateTable bool = false
	var issetLockType bool = false
	var issetLockModel bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockProperty = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockAssociateIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockAssociateTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockModel = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLockProperty {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLockId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateIndex {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLockType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLockModel {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Lock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Lock[fieldId]))
}

func (p *Lock) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockProperty = _field
	return nil
}
func (p *Lock) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockId = _field
	return nil
}
func (p *Lock) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockAssociateIndex = _field
	return nil
}
func (p *Lock) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockAssociateTable = _field
	return nil
}
func (p *Lock) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockType = _field
	return nil
}
func (p *Lock) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockModel = _field
	return nil
}

func (p *Lock) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Lock")

	var fieldId int16
	if err = oprot.WriteStructBegin("Lock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Lock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockProperty", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockProperty); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Lock) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Lock) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockAssociateIndex", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockAssociateIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Lock) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockAssociateTable", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockAssociateTable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Lock) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Lock) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockModel", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockModel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Lock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Lock(%+v)", *p)

}

func (p *Lock) DeepEqual(ano *Lock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.LockProperty) {
		return false
	}
	if !p.Field2DeepEqual(ano.LockId) {
		return false
	}
	if !p.Field3DeepEqual(ano.LockAssociateIndex) {
		return false
	}
	if !p.Field4DeepEqual(ano.LockAssociateTable) {
		return false
	}
	if !p.Field5DeepEqual(ano.LockType) {
		return false
	}
	if !p.Field6DeepEqual(ano.LockModel) {
		return false
	}
	return true
}

func (p *Lock) Field1DeepEqual(src string) bool {

	if strings.Compare(p.LockProperty, src) != 0 {
		return false
	}
	return true
}
func (p *Lock) Field2DeepEqual(src string) bool {

	if strings.Compare(p.LockId, src) != 0 {
		return false
	}
	return true
}
func (p *Lock) Field3DeepEqual(src string) bool {

	if strings.Compare(p.LockAssociateIndex, src) != 0 {
		return false
	}
	return true
}
func (p *Lock) Field4DeepEqual(src string) bool {

	if strings.Compare(p.LockAssociateTable, src) != 0 {
		return false
	}
	return true
}
func (p *Lock) Field5DeepEqual(src string) bool {

	if strings.Compare(p.LockType, src) != 0 {
		return false
	}
	return true
}
func (p *Lock) Field6DeepEqual(src string) bool {

	if strings.Compare(p.LockModel, src) != 0 {
		return false
	}
	return true
}

type WaitLock struct {
	ProcessId          string `thrift:"ProcessId,1,required" frugal:"1,required,string" json:"ProcessId"`
	TrxId              string `thrift:"TrxId,2,required" frugal:"2,required,string" json:"TrxId"`
	SqlBlocked         string `thrift:"SqlBlocked,3,required" frugal:"3,required,string" json:"SqlBlocked"`
	LockAssociateTable string `thrift:"LockAssociateTable,4,required" frugal:"4,required,string" json:"LockAssociateTable"`
	LockType           string `thrift:"LockType,5,required" frugal:"5,required,string" json:"LockType"`
	LockModel          string `thrift:"LockModel,6,required" frugal:"6,required,string" json:"LockModel"`
}

func NewWaitLock() *WaitLock {
	return &WaitLock{}
}

func (p *WaitLock) InitDefault() {
}

func (p *WaitLock) GetProcessId() (v string) {
	return p.ProcessId
}

func (p *WaitLock) GetTrxId() (v string) {
	return p.TrxId
}

func (p *WaitLock) GetSqlBlocked() (v string) {
	return p.SqlBlocked
}

func (p *WaitLock) GetLockAssociateTable() (v string) {
	return p.LockAssociateTable
}

func (p *WaitLock) GetLockType() (v string) {
	return p.LockType
}

func (p *WaitLock) GetLockModel() (v string) {
	return p.LockModel
}
func (p *WaitLock) SetProcessId(val string) {
	p.ProcessId = val
}
func (p *WaitLock) SetTrxId(val string) {
	p.TrxId = val
}
func (p *WaitLock) SetSqlBlocked(val string) {
	p.SqlBlocked = val
}
func (p *WaitLock) SetLockAssociateTable(val string) {
	p.LockAssociateTable = val
}
func (p *WaitLock) SetLockType(val string) {
	p.LockType = val
}
func (p *WaitLock) SetLockModel(val string) {
	p.LockModel = val
}

var fieldIDToName_WaitLock = map[int16]string{
	1: "ProcessId",
	2: "TrxId",
	3: "SqlBlocked",
	4: "LockAssociateTable",
	5: "LockType",
	6: "LockModel",
}

func (p *WaitLock) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLock")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProcessId bool = false
	var issetTrxId bool = false
	var issetSqlBlocked bool = false
	var issetLockAssociateTable bool = false
	var issetLockType bool = false
	var issetLockModel bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlBlocked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockAssociateTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLockModel = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProcessId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlBlocked {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLockType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLockModel {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WaitLock[fieldId]))
}

func (p *WaitLock) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProcessId = _field
	return nil
}
func (p *WaitLock) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxId = _field
	return nil
}
func (p *WaitLock) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlBlocked = _field
	return nil
}
func (p *WaitLock) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockAssociateTable = _field
	return nil
}
func (p *WaitLock) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockType = _field
	return nil
}
func (p *WaitLock) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LockModel = _field
	return nil
}

func (p *WaitLock) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WaitLock")

	var fieldId int16
	if err = oprot.WriteStructBegin("WaitLock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WaitLock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProcessId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProcessId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WaitLock) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WaitLock) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlBlocked", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlBlocked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WaitLock) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockAssociateTable", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockAssociateTable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *WaitLock) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *WaitLock) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LockModel", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LockModel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *WaitLock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WaitLock(%+v)", *p)

}

func (p *WaitLock) DeepEqual(ano *WaitLock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProcessId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TrxId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlBlocked) {
		return false
	}
	if !p.Field4DeepEqual(ano.LockAssociateTable) {
		return false
	}
	if !p.Field5DeepEqual(ano.LockType) {
		return false
	}
	if !p.Field6DeepEqual(ano.LockModel) {
		return false
	}
	return true
}

func (p *WaitLock) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ProcessId, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLock) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TrxId, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLock) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SqlBlocked, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLock) Field4DeepEqual(src string) bool {

	if strings.Compare(p.LockAssociateTable, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLock) Field5DeepEqual(src string) bool {

	if strings.Compare(p.LockType, src) != 0 {
		return false
	}
	return true
}
func (p *WaitLock) Field6DeepEqual(src string) bool {

	if strings.Compare(p.LockModel, src) != 0 {
		return false
	}
	return true
}

type DescribeDeadlockReq struct {
	InstanceId string   `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DSType     DSType   `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	RegionId   *string  `thrift:"RegionId,3,optional" frugal:"3,optional,string" json:"RegionId,omitempty"`
	NodeId     []string `thrift:"NodeId,4,optional" frugal:"4,optional,list<string>" json:"NodeId,omitempty"`
}

func NewDescribeDeadlockReq() *DescribeDeadlockReq {
	return &DescribeDeadlockReq{}
}

func (p *DescribeDeadlockReq) InitDefault() {
}

func (p *DescribeDeadlockReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDeadlockReq) GetDSType() (v DSType) {
	return p.DSType
}

var DescribeDeadlockReq_RegionId_DEFAULT string

func (p *DescribeDeadlockReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeDeadlockReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeDeadlockReq_NodeId_DEFAULT []string

func (p *DescribeDeadlockReq) GetNodeId() (v []string) {
	if !p.IsSetNodeId() {
		return DescribeDeadlockReq_NodeId_DEFAULT
	}
	return p.NodeId
}
func (p *DescribeDeadlockReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDeadlockReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeDeadlockReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeDeadlockReq) SetNodeId(val []string) {
	p.NodeId = val
}

var fieldIDToName_DescribeDeadlockReq = map[int16]string{
	1: "InstanceId",
	2: "DSType",
	3: "RegionId",
	4: "NodeId",
}

func (p *DescribeDeadlockReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeDeadlockReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeDeadlockReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDSType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDeadlockReq[fieldId]))
}

func (p *DescribeDeadlockReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDeadlockReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeDeadlockReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeDeadlockReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeId = _field
	return nil
}

func (p *DescribeDeadlockReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeadlockReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeadlockReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeadlockReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDeadlockReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDeadlockReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeId)); err != nil {
			return err
		}
		for _, v := range p.NodeId {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDeadlockReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeadlockReq(%+v)", *p)

}

func (p *DescribeDeadlockReq) DeepEqual(ano *DescribeDeadlockReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeId) {
		return false
	}
	return true
}

func (p *DescribeDeadlockReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDeadlockReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeDeadlockReq) Field3DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDeadlockReq) Field4DeepEqual(src []string) bool {

	if len(p.NodeId) != len(src) {
		return false
	}
	for i, v := range p.NodeId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeDeadlockResp struct {
	DeadlockInfoList []*DeadlockInfo `thrift:"DeadlockInfoList,1,required" frugal:"1,required,list<DeadlockInfo>" json:"DeadlockInfoList"`
}

func NewDescribeDeadlockResp() *DescribeDeadlockResp {
	return &DescribeDeadlockResp{}
}

func (p *DescribeDeadlockResp) InitDefault() {
}

func (p *DescribeDeadlockResp) GetDeadlockInfoList() (v []*DeadlockInfo) {
	return p.DeadlockInfoList
}
func (p *DescribeDeadlockResp) SetDeadlockInfoList(val []*DeadlockInfo) {
	p.DeadlockInfoList = val
}

var fieldIDToName_DescribeDeadlockResp = map[int16]string{
	1: "DeadlockInfoList",
}

func (p *DescribeDeadlockResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDeadlockInfoList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeadlockInfoList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDeadlockInfoList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDeadlockResp[fieldId]))
}

func (p *DescribeDeadlockResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DeadlockInfo, 0, size)
	values := make([]DeadlockInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeadlockInfoList = _field
	return nil
}

func (p *DescribeDeadlockResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeadlockResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeadlockResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeadlockInfoList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DeadlockInfoList)); err != nil {
		return err
	}
	for _, v := range p.DeadlockInfoList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeadlockResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeadlockResp(%+v)", *p)

}

func (p *DescribeDeadlockResp) DeepEqual(ano *DescribeDeadlockResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DeadlockInfoList) {
		return false
	}
	return true
}

func (p *DescribeDeadlockResp) Field1DeepEqual(src []*DeadlockInfo) bool {

	if len(p.DeadlockInfoList) != len(src) {
		return false
	}
	for i, v := range p.DeadlockInfoList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DeadlockInfo struct {
	DeadlockCollectionTime string      `thrift:"DeadlockCollectionTime,1,required" frugal:"1,required,string" json:"DeadlockCollectionTime"`
	DeadlockTime           string      `thrift:"DeadlockTime,2,required" frugal:"2,required,string" json:"DeadlockTime"`
	DeadlockList           []*Deadlock `thrift:"DeadlockList,3,required" frugal:"3,required,list<Deadlock>" json:"DeadlockList"`
	NodeId                 *string     `thrift:"NodeId,4,optional" frugal:"4,optional,string" json:"NodeId,omitempty"`
}

func NewDeadlockInfo() *DeadlockInfo {
	return &DeadlockInfo{}
}

func (p *DeadlockInfo) InitDefault() {
}

func (p *DeadlockInfo) GetDeadlockCollectionTime() (v string) {
	return p.DeadlockCollectionTime
}

func (p *DeadlockInfo) GetDeadlockTime() (v string) {
	return p.DeadlockTime
}

func (p *DeadlockInfo) GetDeadlockList() (v []*Deadlock) {
	return p.DeadlockList
}

var DeadlockInfo_NodeId_DEFAULT string

func (p *DeadlockInfo) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DeadlockInfo_NodeId_DEFAULT
	}
	return *p.NodeId
}
func (p *DeadlockInfo) SetDeadlockCollectionTime(val string) {
	p.DeadlockCollectionTime = val
}
func (p *DeadlockInfo) SetDeadlockTime(val string) {
	p.DeadlockTime = val
}
func (p *DeadlockInfo) SetDeadlockList(val []*Deadlock) {
	p.DeadlockList = val
}
func (p *DeadlockInfo) SetNodeId(val *string) {
	p.NodeId = val
}

var fieldIDToName_DeadlockInfo = map[int16]string{
	1: "DeadlockCollectionTime",
	2: "DeadlockTime",
	3: "DeadlockList",
	4: "NodeId",
}

func (p *DeadlockInfo) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DeadlockInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeadlockInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDeadlockCollectionTime bool = false
	var issetDeadlockTime bool = false
	var issetDeadlockList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeadlockCollectionTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeadlockTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeadlockList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDeadlockCollectionTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDeadlockTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDeadlockList {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeadlockInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeadlockInfo[fieldId]))
}

func (p *DeadlockInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DeadlockCollectionTime = _field
	return nil
}
func (p *DeadlockInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DeadlockTime = _field
	return nil
}
func (p *DeadlockInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Deadlock, 0, size)
	values := make([]Deadlock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeadlockList = _field
	return nil
}
func (p *DeadlockInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}

func (p *DeadlockInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeadlockInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeadlockInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeadlockInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeadlockCollectionTime", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DeadlockCollectionTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeadlockInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeadlockTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DeadlockTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeadlockInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeadlockList", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DeadlockList)); err != nil {
		return err
	}
	for _, v := range p.DeadlockList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeadlockInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DeadlockInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeadlockInfo(%+v)", *p)

}

func (p *DeadlockInfo) DeepEqual(ano *DeadlockInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DeadlockCollectionTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.DeadlockTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.DeadlockList) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeId) {
		return false
	}
	return true
}

func (p *DeadlockInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DeadlockCollectionTime, src) != 0 {
		return false
	}
	return true
}
func (p *DeadlockInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DeadlockTime, src) != 0 {
		return false
	}
	return true
}
func (p *DeadlockInfo) Field3DeepEqual(src []*Deadlock) bool {

	if len(p.DeadlockList) != len(src) {
		return false
	}
	for i, v := range p.DeadlockList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DeadlockInfo) Field4DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}

type Deadlock struct {
	TrxInfo       string `thrift:"TrxInfo,1,required" frugal:"1,required,string" json:"TrxInfo"`
	ProcessId     string `thrift:"ProcessId,2,required" frugal:"2,required,string" json:"ProcessId"`
	ReqType       string `thrift:"ReqType,3,required" frugal:"3,required,string" json:"ReqType"`
	RelateTable   string `thrift:"RelateTable,4,required" frugal:"4,required,string" json:"RelateTable"`
	WaitLock      string `thrift:"WaitLock,5,required" frugal:"5,required,string" json:"WaitLock"`
	WaitIndex     string `thrift:"WaitIndex,6,required" frugal:"6,required,string" json:"WaitIndex"`
	WaitLockMode  string `thrift:"WaitLockMode,7,required" frugal:"7,required,string" json:"WaitLockMode"`
	HoldLock      string `thrift:"HoldLock,8,required" frugal:"8,required,string" json:"HoldLock"`
	HoldLockIndex string `thrift:"HoldLockIndex,9,required" frugal:"9,required,string" json:"HoldLockIndex"`
	HoldLockMode  string `thrift:"HoldLockMode,10,required" frugal:"10,required,string" json:"HoldLockMode"`
	Sql           string `thrift:"Sql,11,required" frugal:"11,required,string" json:"Sql"`
	TrxTreat      string `thrift:"TrxTreat,12,required" frugal:"12,required,string" json:"TrxTreat"`
}

func NewDeadlock() *Deadlock {
	return &Deadlock{}
}

func (p *Deadlock) InitDefault() {
}

func (p *Deadlock) GetTrxInfo() (v string) {
	return p.TrxInfo
}

func (p *Deadlock) GetProcessId() (v string) {
	return p.ProcessId
}

func (p *Deadlock) GetReqType() (v string) {
	return p.ReqType
}

func (p *Deadlock) GetRelateTable() (v string) {
	return p.RelateTable
}

func (p *Deadlock) GetWaitLock() (v string) {
	return p.WaitLock
}

func (p *Deadlock) GetWaitIndex() (v string) {
	return p.WaitIndex
}

func (p *Deadlock) GetWaitLockMode() (v string) {
	return p.WaitLockMode
}

func (p *Deadlock) GetHoldLock() (v string) {
	return p.HoldLock
}

func (p *Deadlock) GetHoldLockIndex() (v string) {
	return p.HoldLockIndex
}

func (p *Deadlock) GetHoldLockMode() (v string) {
	return p.HoldLockMode
}

func (p *Deadlock) GetSql() (v string) {
	return p.Sql
}

func (p *Deadlock) GetTrxTreat() (v string) {
	return p.TrxTreat
}
func (p *Deadlock) SetTrxInfo(val string) {
	p.TrxInfo = val
}
func (p *Deadlock) SetProcessId(val string) {
	p.ProcessId = val
}
func (p *Deadlock) SetReqType(val string) {
	p.ReqType = val
}
func (p *Deadlock) SetRelateTable(val string) {
	p.RelateTable = val
}
func (p *Deadlock) SetWaitLock(val string) {
	p.WaitLock = val
}
func (p *Deadlock) SetWaitIndex(val string) {
	p.WaitIndex = val
}
func (p *Deadlock) SetWaitLockMode(val string) {
	p.WaitLockMode = val
}
func (p *Deadlock) SetHoldLock(val string) {
	p.HoldLock = val
}
func (p *Deadlock) SetHoldLockIndex(val string) {
	p.HoldLockIndex = val
}
func (p *Deadlock) SetHoldLockMode(val string) {
	p.HoldLockMode = val
}
func (p *Deadlock) SetSql(val string) {
	p.Sql = val
}
func (p *Deadlock) SetTrxTreat(val string) {
	p.TrxTreat = val
}

var fieldIDToName_Deadlock = map[int16]string{
	1:  "TrxInfo",
	2:  "ProcessId",
	3:  "ReqType",
	4:  "RelateTable",
	5:  "WaitLock",
	6:  "WaitIndex",
	7:  "WaitLockMode",
	8:  "HoldLock",
	9:  "HoldLockIndex",
	10: "HoldLockMode",
	11: "Sql",
	12: "TrxTreat",
}

func (p *Deadlock) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Deadlock")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxInfo bool = false
	var issetProcessId bool = false
	var issetReqType bool = false
	var issetRelateTable bool = false
	var issetWaitLock bool = false
	var issetWaitIndex bool = false
	var issetWaitLockMode bool = false
	var issetHoldLock bool = false
	var issetHoldLockIndex bool = false
	var issetHoldLockMode bool = false
	var issetSql bool = false
	var issetTrxTreat bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReqType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRelateTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetWaitLock = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetWaitIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetWaitLockMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetHoldLock = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetHoldLockIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetHoldLockMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetSql = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxTreat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTrxInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProcessId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReqType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRelateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetWaitLock {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetWaitIndex {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetWaitLockMode {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetHoldLock {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetHoldLockIndex {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetHoldLockMode {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetSql {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetTrxTreat {
		fieldId = 12
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Deadlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Deadlock[fieldId]))
}

func (p *Deadlock) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxInfo = _field
	return nil
}
func (p *Deadlock) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProcessId = _field
	return nil
}
func (p *Deadlock) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReqType = _field
	return nil
}
func (p *Deadlock) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RelateTable = _field
	return nil
}
func (p *Deadlock) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WaitLock = _field
	return nil
}
func (p *Deadlock) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WaitIndex = _field
	return nil
}
func (p *Deadlock) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WaitLockMode = _field
	return nil
}
func (p *Deadlock) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HoldLock = _field
	return nil
}
func (p *Deadlock) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HoldLockIndex = _field
	return nil
}
func (p *Deadlock) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HoldLockMode = _field
	return nil
}
func (p *Deadlock) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Sql = _field
	return nil
}
func (p *Deadlock) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TrxTreat = _field
	return nil
}

func (p *Deadlock) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Deadlock")

	var fieldId int16
	if err = oprot.WriteStructBegin("Deadlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Deadlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxInfo", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Deadlock) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProcessId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProcessId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Deadlock) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReqType", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReqType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Deadlock) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RelateTable", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RelateTable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Deadlock) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WaitLock", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WaitLock); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Deadlock) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WaitIndex", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WaitIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Deadlock) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WaitLockMode", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WaitLockMode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Deadlock) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HoldLock", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.HoldLock); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Deadlock) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HoldLockIndex", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.HoldLockIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Deadlock) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HoldLockMode", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.HoldLockMode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Deadlock) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Sql", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Sql); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *Deadlock) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxTreat", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TrxTreat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Deadlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Deadlock(%+v)", *p)

}

func (p *Deadlock) DeepEqual(ano *Deadlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TrxInfo) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProcessId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReqType) {
		return false
	}
	if !p.Field4DeepEqual(ano.RelateTable) {
		return false
	}
	if !p.Field5DeepEqual(ano.WaitLock) {
		return false
	}
	if !p.Field6DeepEqual(ano.WaitIndex) {
		return false
	}
	if !p.Field7DeepEqual(ano.WaitLockMode) {
		return false
	}
	if !p.Field8DeepEqual(ano.HoldLock) {
		return false
	}
	if !p.Field9DeepEqual(ano.HoldLockIndex) {
		return false
	}
	if !p.Field10DeepEqual(ano.HoldLockMode) {
		return false
	}
	if !p.Field11DeepEqual(ano.Sql) {
		return false
	}
	if !p.Field12DeepEqual(ano.TrxTreat) {
		return false
	}
	return true
}

func (p *Deadlock) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TrxInfo, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ProcessId, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ReqType, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RelateTable, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field5DeepEqual(src string) bool {

	if strings.Compare(p.WaitLock, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field6DeepEqual(src string) bool {

	if strings.Compare(p.WaitIndex, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field7DeepEqual(src string) bool {

	if strings.Compare(p.WaitLockMode, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field8DeepEqual(src string) bool {

	if strings.Compare(p.HoldLock, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field9DeepEqual(src string) bool {

	if strings.Compare(p.HoldLockIndex, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field10DeepEqual(src string) bool {

	if strings.Compare(p.HoldLockMode, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field11DeepEqual(src string) bool {

	if strings.Compare(p.Sql, src) != 0 {
		return false
	}
	return true
}
func (p *Deadlock) Field12DeepEqual(src string) bool {

	if strings.Compare(p.TrxTreat, src) != 0 {
		return false
	}
	return true
}

type DescribeDeadlockDetectReq struct {
	InstanceId string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DSType     DSType  `thrift:"DSType,2,required" frugal:"2,required,DSType" json:"DSType"`
	RegionId   *string `thrift:"RegionId,3,optional" frugal:"3,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeDeadlockDetectReq() *DescribeDeadlockDetectReq {
	return &DescribeDeadlockDetectReq{}
}

func (p *DescribeDeadlockDetectReq) InitDefault() {
}

func (p *DescribeDeadlockDetectReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDeadlockDetectReq) GetDSType() (v DSType) {
	return p.DSType
}

var DescribeDeadlockDetectReq_RegionId_DEFAULT string

func (p *DescribeDeadlockDetectReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeDeadlockDetectReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeDeadlockDetectReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDeadlockDetectReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *DescribeDeadlockDetectReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeDeadlockDetectReq = map[int16]string{
	1: "InstanceId",
	2: "DSType",
	3: "RegionId",
}

func (p *DescribeDeadlockDetectReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeDeadlockDetectReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockDetectReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDSType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockDetectReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDeadlockDetectReq[fieldId]))
}

func (p *DescribeDeadlockDetectReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDeadlockDetectReq) ReadField2(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *DescribeDeadlockDetectReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeDeadlockDetectReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockDetectReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeadlockDetectReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeadlockDetectReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeadlockDetectReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDeadlockDetectReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDeadlockDetectReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeadlockDetectReq(%+v)", *p)

}

func (p *DescribeDeadlockDetectReq) DeepEqual(ano *DescribeDeadlockDetectReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeDeadlockDetectReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDeadlockDetectReq) Field2DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *DescribeDeadlockDetectReq) Field3DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeDeadlockDetectResp struct {
	OnOff bool `thrift:"OnOff,1,required" frugal:"1,required,bool" json:"OnOff"`
}

func NewDescribeDeadlockDetectResp() *DescribeDeadlockDetectResp {
	return &DescribeDeadlockDetectResp{}
}

func (p *DescribeDeadlockDetectResp) InitDefault() {
}

func (p *DescribeDeadlockDetectResp) GetOnOff() (v bool) {
	return p.OnOff
}
func (p *DescribeDeadlockDetectResp) SetOnOff(val bool) {
	p.OnOff = val
}

var fieldIDToName_DescribeDeadlockDetectResp = map[int16]string{
	1: "OnOff",
}

func (p *DescribeDeadlockDetectResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockDetectResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOnOff bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOnOff = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOnOff {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockDetectResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDeadlockDetectResp[fieldId]))
}

func (p *DescribeDeadlockDetectResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OnOff = _field
	return nil
}

func (p *DescribeDeadlockDetectResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDeadlockDetectResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDeadlockDetectResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDeadlockDetectResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OnOff", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.OnOff); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDeadlockDetectResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDeadlockDetectResp(%+v)", *p)

}

func (p *DescribeDeadlockDetectResp) DeepEqual(ano *DescribeDeadlockDetectResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.OnOff) {
		return false
	}
	return true
}

func (p *DescribeDeadlockDetectResp) Field1DeepEqual(src bool) bool {

	if p.OnOff != src {
		return false
	}
	return true
}

type TrxSnapshot struct {
	SnapshotTime int64 `thrift:"SnapshotTime,1,required" frugal:"1,required,i64" json:"SnapshotTime"`
}

func NewTrxSnapshot() *TrxSnapshot {
	return &TrxSnapshot{}
}

func (p *TrxSnapshot) InitDefault() {
}

func (p *TrxSnapshot) GetSnapshotTime() (v int64) {
	return p.SnapshotTime
}
func (p *TrxSnapshot) SetSnapshotTime(val int64) {
	p.SnapshotTime = val
}

var fieldIDToName_TrxSnapshot = map[int16]string{
	1: "SnapshotTime",
}

func (p *TrxSnapshot) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxSnapshot")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSnapshotTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSnapshotTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxSnapshot[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TrxSnapshot[fieldId]))
}

func (p *TrxSnapshot) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SnapshotTime = _field
	return nil
}

func (p *TrxSnapshot) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TrxSnapshot")

	var fieldId int16
	if err = oprot.WriteStructBegin("TrxSnapshot"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TrxSnapshot) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SnapshotTime", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SnapshotTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TrxSnapshot) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrxSnapshot(%+v)", *p)

}

func (p *TrxSnapshot) DeepEqual(ano *TrxSnapshot) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SnapshotTime) {
		return false
	}
	return true
}

func (p *TrxSnapshot) Field1DeepEqual(src int64) bool {

	if p.SnapshotTime != src {
		return false
	}
	return true
}

type LockWaitSnapshot struct {
	SnapshotTime int64 `thrift:"SnapshotTime,1,required" frugal:"1,required,i64" json:"SnapshotTime"`
}

func NewLockWaitSnapshot() *LockWaitSnapshot {
	return &LockWaitSnapshot{}
}

func (p *LockWaitSnapshot) InitDefault() {
}

func (p *LockWaitSnapshot) GetSnapshotTime() (v int64) {
	return p.SnapshotTime
}
func (p *LockWaitSnapshot) SetSnapshotTime(val int64) {
	p.SnapshotTime = val
}

var fieldIDToName_LockWaitSnapshot = map[int16]string{
	1: "SnapshotTime",
}

func (p *LockWaitSnapshot) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LockWaitSnapshot")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSnapshotTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSnapshotTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LockWaitSnapshot[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LockWaitSnapshot[fieldId]))
}

func (p *LockWaitSnapshot) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SnapshotTime = _field
	return nil
}

func (p *LockWaitSnapshot) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LockWaitSnapshot")

	var fieldId int16
	if err = oprot.WriteStructBegin("LockWaitSnapshot"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LockWaitSnapshot) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SnapshotTime", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SnapshotTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LockWaitSnapshot) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LockWaitSnapshot(%+v)", *p)

}

func (p *LockWaitSnapshot) DeepEqual(ano *LockWaitSnapshot) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SnapshotTime) {
		return false
	}
	return true
}

func (p *LockWaitSnapshot) Field1DeepEqual(src int64) bool {

	if p.SnapshotTime != src {
		return false
	}
	return true
}

type DescribeTrxSnapshotsReq struct {
	InstanceType DSType  `thrift:"InstanceType,1,required" frugal:"1,required,DSType" json:"InstanceType"`
	InstanceId   string  `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	StartTime    int32   `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime      int32   `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
	PageSize     *int32  `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
	PageNumber   *int32  `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	NodeId       *string `thrift:"NodeId,7,optional" frugal:"7,optional,string" json:"NodeId,omitempty"`
	RegionId     *string `thrift:"RegionId,8,optional" frugal:"8,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeTrxSnapshotsReq() *DescribeTrxSnapshotsReq {
	return &DescribeTrxSnapshotsReq{}
}

func (p *DescribeTrxSnapshotsReq) InitDefault() {
}

func (p *DescribeTrxSnapshotsReq) GetInstanceType() (v DSType) {
	return p.InstanceType
}

func (p *DescribeTrxSnapshotsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTrxSnapshotsReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeTrxSnapshotsReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeTrxSnapshotsReq_PageSize_DEFAULT int32

func (p *DescribeTrxSnapshotsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTrxSnapshotsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTrxSnapshotsReq_PageNumber_DEFAULT int32

func (p *DescribeTrxSnapshotsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTrxSnapshotsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTrxSnapshotsReq_NodeId_DEFAULT string

func (p *DescribeTrxSnapshotsReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeTrxSnapshotsReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeTrxSnapshotsReq_RegionId_DEFAULT string

func (p *DescribeTrxSnapshotsReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeTrxSnapshotsReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeTrxSnapshotsReq) SetInstanceType(val DSType) {
	p.InstanceType = val
}
func (p *DescribeTrxSnapshotsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTrxSnapshotsReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeTrxSnapshotsReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeTrxSnapshotsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTrxSnapshotsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTrxSnapshotsReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeTrxSnapshotsReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeTrxSnapshotsReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "StartTime",
	4: "EndTime",
	5: "PageSize",
	6: "PageNumber",
	7: "NodeId",
	8: "RegionId",
}

func (p *DescribeTrxSnapshotsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTrxSnapshotsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTrxSnapshotsReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeTrxSnapshotsReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeTrxSnapshotsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxSnapshotsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxSnapshotsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxSnapshotsReq[fieldId]))
}

func (p *DescribeTrxSnapshotsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeTrxSnapshotsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeTrxSnapshotsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxSnapshotsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxSnapshotsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxSnapshotsReq(%+v)", *p)

}

func (p *DescribeTrxSnapshotsReq) DeepEqual(ano *DescribeTrxSnapshotsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field8DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeTrxSnapshotsReq) Field1DeepEqual(src DSType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field7DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxSnapshotsReq) Field8DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeTrxSnapshotsResp struct {
	TrxSnapshots []*TrxSnapshot `thrift:"TrxSnapshots,1,required" frugal:"1,required,list<TrxSnapshot>" json:"TrxSnapshots"`
	Total        int32          `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeTrxSnapshotsResp() *DescribeTrxSnapshotsResp {
	return &DescribeTrxSnapshotsResp{}
}

func (p *DescribeTrxSnapshotsResp) InitDefault() {
}

func (p *DescribeTrxSnapshotsResp) GetTrxSnapshots() (v []*TrxSnapshot) {
	return p.TrxSnapshots
}

func (p *DescribeTrxSnapshotsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTrxSnapshotsResp) SetTrxSnapshots(val []*TrxSnapshot) {
	p.TrxSnapshots = val
}
func (p *DescribeTrxSnapshotsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTrxSnapshotsResp = map[int16]string{
	1: "TrxSnapshots",
	2: "Total",
}

func (p *DescribeTrxSnapshotsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxSnapshotsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxSnapshots bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxSnapshots = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTrxSnapshots {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxSnapshotsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxSnapshotsResp[fieldId]))
}

func (p *DescribeTrxSnapshotsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TrxSnapshot, 0, size)
	values := make([]TrxSnapshot, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TrxSnapshots = _field
	return nil
}
func (p *DescribeTrxSnapshotsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTrxSnapshotsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxSnapshotsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxSnapshotsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxSnapshotsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxSnapshots", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TrxSnapshots)); err != nil {
		return err
	}
	for _, v := range p.TrxSnapshots {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxSnapshotsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxSnapshotsResp(%+v)", *p)

}

func (p *DescribeTrxSnapshotsResp) DeepEqual(ano *DescribeTrxSnapshotsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TrxSnapshots) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTrxSnapshotsResp) Field1DeepEqual(src []*TrxSnapshot) bool {

	if len(p.TrxSnapshots) != len(src) {
		return false
	}
	for i, v := range p.TrxSnapshots {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTrxSnapshotsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeTrxDetailSnapshotReq struct {
	InstanceType DSType          `thrift:"InstanceType,1,required" frugal:"1,required,DSType" json:"InstanceType"`
	InstanceId   string          `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	SnapshotTime int32           `thrift:"SnapshotTime,3,required" frugal:"3,required,i32" json:"SnapshotTime"`
	PageSize     *int32          `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	PageNumber   *int32          `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
	NodeId       *string         `thrift:"NodeId,6,optional" frugal:"6,optional,string" json:"NodeId,omitempty"`
	StartTime    *int64          `thrift:"StartTime,7,optional" frugal:"7,optional,i64" json:"StartTime,omitempty"`
	EndTime      *int64          `thrift:"EndTime,8,optional" frugal:"8,optional,i64" json:"EndTime,omitempty"`
	RegionId     *string         `thrift:"RegionId,9,optional" frugal:"9,optional,string" json:"RegionId,omitempty"`
	QueryFilter  *TrxQueryFilter `thrift:"QueryFilter,10,optional" frugal:"10,optional,TrxQueryFilter" json:"QueryFilter,omitempty"`
	OrderBy      *SortParam      `thrift:"OrderBy,11,optional" frugal:"11,optional,SortParam" json:"OrderBy,omitempty"`
	SortBy       *SortBy         `thrift:"SortBy,12,optional" frugal:"12,optional,SortBy" json:"SortBy,omitempty"`
	NodeIds      []string        `thrift:"NodeIds,13,optional" frugal:"13,optional,list<string>" json:"NodeIds,omitempty"`
}

func NewDescribeTrxDetailSnapshotReq() *DescribeTrxDetailSnapshotReq {
	return &DescribeTrxDetailSnapshotReq{}
}

func (p *DescribeTrxDetailSnapshotReq) InitDefault() {
}

func (p *DescribeTrxDetailSnapshotReq) GetInstanceType() (v DSType) {
	return p.InstanceType
}

func (p *DescribeTrxDetailSnapshotReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTrxDetailSnapshotReq) GetSnapshotTime() (v int32) {
	return p.SnapshotTime
}

var DescribeTrxDetailSnapshotReq_PageSize_DEFAULT int32

func (p *DescribeTrxDetailSnapshotReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTrxDetailSnapshotReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTrxDetailSnapshotReq_PageNumber_DEFAULT int32

func (p *DescribeTrxDetailSnapshotReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTrxDetailSnapshotReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTrxDetailSnapshotReq_NodeId_DEFAULT string

func (p *DescribeTrxDetailSnapshotReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeTrxDetailSnapshotReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeTrxDetailSnapshotReq_StartTime_DEFAULT int64

func (p *DescribeTrxDetailSnapshotReq) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return DescribeTrxDetailSnapshotReq_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DescribeTrxDetailSnapshotReq_EndTime_DEFAULT int64

func (p *DescribeTrxDetailSnapshotReq) GetEndTime() (v int64) {
	if !p.IsSetEndTime() {
		return DescribeTrxDetailSnapshotReq_EndTime_DEFAULT
	}
	return *p.EndTime
}

var DescribeTrxDetailSnapshotReq_RegionId_DEFAULT string

func (p *DescribeTrxDetailSnapshotReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeTrxDetailSnapshotReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeTrxDetailSnapshotReq_QueryFilter_DEFAULT *TrxQueryFilter

func (p *DescribeTrxDetailSnapshotReq) GetQueryFilter() (v *TrxQueryFilter) {
	if !p.IsSetQueryFilter() {
		return DescribeTrxDetailSnapshotReq_QueryFilter_DEFAULT
	}
	return p.QueryFilter
}

var DescribeTrxDetailSnapshotReq_OrderBy_DEFAULT SortParam

func (p *DescribeTrxDetailSnapshotReq) GetOrderBy() (v SortParam) {
	if !p.IsSetOrderBy() {
		return DescribeTrxDetailSnapshotReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeTrxDetailSnapshotReq_SortBy_DEFAULT SortBy

func (p *DescribeTrxDetailSnapshotReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeTrxDetailSnapshotReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeTrxDetailSnapshotReq_NodeIds_DEFAULT []string

func (p *DescribeTrxDetailSnapshotReq) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return DescribeTrxDetailSnapshotReq_NodeIds_DEFAULT
	}
	return p.NodeIds
}
func (p *DescribeTrxDetailSnapshotReq) SetInstanceType(val DSType) {
	p.InstanceType = val
}
func (p *DescribeTrxDetailSnapshotReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTrxDetailSnapshotReq) SetSnapshotTime(val int32) {
	p.SnapshotTime = val
}
func (p *DescribeTrxDetailSnapshotReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTrxDetailSnapshotReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTrxDetailSnapshotReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeTrxDetailSnapshotReq) SetStartTime(val *int64) {
	p.StartTime = val
}
func (p *DescribeTrxDetailSnapshotReq) SetEndTime(val *int64) {
	p.EndTime = val
}
func (p *DescribeTrxDetailSnapshotReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeTrxDetailSnapshotReq) SetQueryFilter(val *TrxQueryFilter) {
	p.QueryFilter = val
}
func (p *DescribeTrxDetailSnapshotReq) SetOrderBy(val *SortParam) {
	p.OrderBy = val
}
func (p *DescribeTrxDetailSnapshotReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeTrxDetailSnapshotReq) SetNodeIds(val []string) {
	p.NodeIds = val
}

var fieldIDToName_DescribeTrxDetailSnapshotReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "SnapshotTime",
	4:  "PageSize",
	5:  "PageNumber",
	6:  "NodeId",
	7:  "StartTime",
	8:  "EndTime",
	9:  "RegionId",
	10: "QueryFilter",
	11: "OrderBy",
	12: "SortBy",
	13: "NodeIds",
}

func (p *DescribeTrxDetailSnapshotReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetQueryFilter() bool {
	return p.QueryFilter != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeTrxDetailSnapshotReq) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *DescribeTrxDetailSnapshotReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxDetailSnapshotReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetSnapshotTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSnapshotTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxDetailSnapshotReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxDetailSnapshotReq[fieldId]))
}

func (p *DescribeTrxDetailSnapshotReq) ReadField1(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SnapshotTime = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField10(iprot thrift.TProtocol) error {
	_field := NewTrxQueryFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.QueryFilter = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *SortParam
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotReq) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}

func (p *DescribeTrxDetailSnapshotReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxDetailSnapshotReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxDetailSnapshotReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SnapshotTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SnapshotTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryFilter() {
		if err = oprot.WriteFieldBegin("QueryFilter", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.QueryFilter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxDetailSnapshotReq(%+v)", *p)

}

func (p *DescribeTrxDetailSnapshotReq) DeepEqual(ano *DescribeTrxDetailSnapshotReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SnapshotTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field7DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field10DeepEqual(ano.QueryFilter) {
		return false
	}
	if !p.Field11DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field12DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field13DeepEqual(ano.NodeIds) {
		return false
	}
	return true
}

func (p *DescribeTrxDetailSnapshotReq) Field1DeepEqual(src DSType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field3DeepEqual(src int32) bool {

	if p.SnapshotTime != src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field6DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field7DeepEqual(src *int64) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if *p.StartTime != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field8DeepEqual(src *int64) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if *p.EndTime != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field9DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field10DeepEqual(src *TrxQueryFilter) bool {

	if !p.QueryFilter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field11DeepEqual(src *SortParam) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field12DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeTrxDetailSnapshotReq) Field13DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeTrxDetailSnapshotResp struct {
	TrxAndLocks []*TrxAndLock `thrift:"TrxAndLocks,1,required" frugal:"1,required,list<TrxAndLock>" json:"TrxAndLocks"`
	Total       int32         `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeTrxDetailSnapshotResp() *DescribeTrxDetailSnapshotResp {
	return &DescribeTrxDetailSnapshotResp{}
}

func (p *DescribeTrxDetailSnapshotResp) InitDefault() {
}

func (p *DescribeTrxDetailSnapshotResp) GetTrxAndLocks() (v []*TrxAndLock) {
	return p.TrxAndLocks
}

func (p *DescribeTrxDetailSnapshotResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTrxDetailSnapshotResp) SetTrxAndLocks(val []*TrxAndLock) {
	p.TrxAndLocks = val
}
func (p *DescribeTrxDetailSnapshotResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTrxDetailSnapshotResp = map[int16]string{
	1: "TrxAndLocks",
	2: "Total",
}

func (p *DescribeTrxDetailSnapshotResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxDetailSnapshotResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxAndLocks bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTrxAndLocks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTrxAndLocks {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxDetailSnapshotResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTrxDetailSnapshotResp[fieldId]))
}

func (p *DescribeTrxDetailSnapshotResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TrxAndLock, 0, size)
	values := make([]TrxAndLock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TrxAndLocks = _field
	return nil
}
func (p *DescribeTrxDetailSnapshotResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTrxDetailSnapshotResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTrxDetailSnapshotResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTrxDetailSnapshotResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TrxAndLocks", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TrxAndLocks)); err != nil {
		return err
	}
	for _, v := range p.TrxAndLocks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTrxDetailSnapshotResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTrxDetailSnapshotResp(%+v)", *p)

}

func (p *DescribeTrxDetailSnapshotResp) DeepEqual(ano *DescribeTrxDetailSnapshotResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TrxAndLocks) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTrxDetailSnapshotResp) Field1DeepEqual(src []*TrxAndLock) bool {

	if len(p.TrxAndLocks) != len(src) {
		return false
	}
	for i, v := range p.TrxAndLocks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTrxDetailSnapshotResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeLockCurrentWaitsReq struct {
	InstanceId   string               `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType         `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	NodeIds      []string             `thrift:"NodeIds,3,optional" frugal:"3,optional,list<string>" json:"NodeIds,omitempty"`
	QueryFilter  *WaitLockQueryFilter `thrift:"QueryFilter,4,optional" frugal:"4,optional,WaitLockQueryFilter" json:"QueryFilter,omitempty"`
	RegionId     *string              `thrift:"RegionId,5,optional" frugal:"5,optional,string" json:"RegionId,omitempty"`
	OrderBy      *WaitLockSortParam   `thrift:"OrderBy,6,optional" frugal:"6,optional,WaitLockSortParam" json:"OrderBy,omitempty"`
	SortBy       *SortBy              `thrift:"SortBy,7,optional" frugal:"7,optional,SortBy" json:"SortBy,omitempty"`
}

func NewDescribeLockCurrentWaitsReq() *DescribeLockCurrentWaitsReq {
	return &DescribeLockCurrentWaitsReq{}
}

func (p *DescribeLockCurrentWaitsReq) InitDefault() {
}

func (p *DescribeLockCurrentWaitsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeLockCurrentWaitsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var DescribeLockCurrentWaitsReq_NodeIds_DEFAULT []string

func (p *DescribeLockCurrentWaitsReq) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return DescribeLockCurrentWaitsReq_NodeIds_DEFAULT
	}
	return p.NodeIds
}

var DescribeLockCurrentWaitsReq_QueryFilter_DEFAULT *WaitLockQueryFilter

func (p *DescribeLockCurrentWaitsReq) GetQueryFilter() (v *WaitLockQueryFilter) {
	if !p.IsSetQueryFilter() {
		return DescribeLockCurrentWaitsReq_QueryFilter_DEFAULT
	}
	return p.QueryFilter
}

var DescribeLockCurrentWaitsReq_RegionId_DEFAULT string

func (p *DescribeLockCurrentWaitsReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeLockCurrentWaitsReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeLockCurrentWaitsReq_OrderBy_DEFAULT WaitLockSortParam

func (p *DescribeLockCurrentWaitsReq) GetOrderBy() (v WaitLockSortParam) {
	if !p.IsSetOrderBy() {
		return DescribeLockCurrentWaitsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeLockCurrentWaitsReq_SortBy_DEFAULT SortBy

func (p *DescribeLockCurrentWaitsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeLockCurrentWaitsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}
func (p *DescribeLockCurrentWaitsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeLockCurrentWaitsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeLockCurrentWaitsReq) SetNodeIds(val []string) {
	p.NodeIds = val
}
func (p *DescribeLockCurrentWaitsReq) SetQueryFilter(val *WaitLockQueryFilter) {
	p.QueryFilter = val
}
func (p *DescribeLockCurrentWaitsReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeLockCurrentWaitsReq) SetOrderBy(val *WaitLockSortParam) {
	p.OrderBy = val
}
func (p *DescribeLockCurrentWaitsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}

var fieldIDToName_DescribeLockCurrentWaitsReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "NodeIds",
	4: "QueryFilter",
	5: "RegionId",
	6: "OrderBy",
	7: "SortBy",
}

func (p *DescribeLockCurrentWaitsReq) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *DescribeLockCurrentWaitsReq) IsSetQueryFilter() bool {
	return p.QueryFilter != nil
}

func (p *DescribeLockCurrentWaitsReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeLockCurrentWaitsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeLockCurrentWaitsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeLockCurrentWaitsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsReq[fieldId]))
}

func (p *DescribeLockCurrentWaitsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewWaitLockQueryFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.QueryFilter = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *WaitLockSortParam
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := WaitLockSortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeLockCurrentWaitsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}

func (p *DescribeLockCurrentWaitsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLockCurrentWaitsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryFilter() {
		if err = oprot.WriteFieldBegin("QueryFilter", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.QueryFilter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLockCurrentWaitsReq(%+v)", *p)

}

func (p *DescribeLockCurrentWaitsReq) DeepEqual(ano *DescribeLockCurrentWaitsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeIds) {
		return false
	}
	if !p.Field4DeepEqual(ano.QueryFilter) {
		return false
	}
	if !p.Field5DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.SortBy) {
		return false
	}
	return true
}

func (p *DescribeLockCurrentWaitsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field3DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field4DeepEqual(src *WaitLockQueryFilter) bool {

	if !p.QueryFilter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field5DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field6DeepEqual(src *WaitLockSortParam) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsReq) Field7DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}

type DescribeLockCurrentWaitsResp struct {
	DetailList []*DescribeLockCurrentWaitsDetail `thrift:"DetailList,1,required" frugal:"1,required,list<DescribeLockCurrentWaitsDetail>" json:"DetailList"`
	Total      int32                             `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeLockCurrentWaitsResp() *DescribeLockCurrentWaitsResp {
	return &DescribeLockCurrentWaitsResp{}
}

func (p *DescribeLockCurrentWaitsResp) InitDefault() {
}

func (p *DescribeLockCurrentWaitsResp) GetDetailList() (v []*DescribeLockCurrentWaitsDetail) {
	return p.DetailList
}

func (p *DescribeLockCurrentWaitsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeLockCurrentWaitsResp) SetDetailList(val []*DescribeLockCurrentWaitsDetail) {
	p.DetailList = val
}
func (p *DescribeLockCurrentWaitsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeLockCurrentWaitsResp = map[int16]string{
	1: "DetailList",
	2: "Total",
}

func (p *DescribeLockCurrentWaitsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDetailList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDetailList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsResp[fieldId]))
}

func (p *DescribeLockCurrentWaitsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DescribeLockCurrentWaitsDetail, 0, size)
	values := make([]DescribeLockCurrentWaitsDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DetailList = _field
	return nil
}
func (p *DescribeLockCurrentWaitsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeLockCurrentWaitsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLockCurrentWaitsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DetailList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DetailList)); err != nil {
		return err
	}
	for _, v := range p.DetailList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLockCurrentWaitsResp(%+v)", *p)

}

func (p *DescribeLockCurrentWaitsResp) DeepEqual(ano *DescribeLockCurrentWaitsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DetailList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeLockCurrentWaitsResp) Field1DeepEqual(src []*DescribeLockCurrentWaitsDetail) bool {

	if len(p.DetailList) != len(src) {
		return false
	}
	for i, v := range p.DetailList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeLockCurrentWaitsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeLockWaitsDetailSnapshotReq struct {
	InstanceId   string               `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType         `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	StartTime    int32                `thrift:"StartTime,3,required" frugal:"3,required,i32" json:"StartTime"`
	EndTime      int32                `thrift:"EndTime,4,required" frugal:"4,required,i32" json:"EndTime"`
	NodeId       *string              `thrift:"NodeId,5,optional" frugal:"5,optional,string" json:"NodeId,omitempty"`
	PageSize     *int32               `thrift:"PageSize,6,optional" frugal:"6,optional,i32" json:"PageSize,omitempty"`
	PageNumber   *int32               `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	QueryFilter  *WaitLockQueryFilter `thrift:"QueryFilter,8,optional" frugal:"8,optional,WaitLockQueryFilter" json:"QueryFilter,omitempty"`
	OrderBy      *WaitLockSortParam   `thrift:"OrderBy,9,optional" frugal:"9,optional,WaitLockSortParam" json:"OrderBy,omitempty"`
	SortBy       *SortBy              `thrift:"SortBy,10,optional" frugal:"10,optional,SortBy" json:"SortBy,omitempty"`
	RegionId     *string              `thrift:"RegionId,11,optional" frugal:"11,optional,string" json:"RegionId,omitempty"`
	NodeIds      []string             `thrift:"NodeIds,12,optional" frugal:"12,optional,list<string>" json:"NodeIds,omitempty"`
}

func NewDescribeLockWaitsDetailSnapshotReq() *DescribeLockWaitsDetailSnapshotReq {
	return &DescribeLockWaitsDetailSnapshotReq{}
}

func (p *DescribeLockWaitsDetailSnapshotReq) InitDefault() {
}

func (p *DescribeLockWaitsDetailSnapshotReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeLockWaitsDetailSnapshotReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeLockWaitsDetailSnapshotReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *DescribeLockWaitsDetailSnapshotReq) GetEndTime() (v int32) {
	return p.EndTime
}

var DescribeLockWaitsDetailSnapshotReq_NodeId_DEFAULT string

func (p *DescribeLockWaitsDetailSnapshotReq) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return DescribeLockWaitsDetailSnapshotReq_NodeId_DEFAULT
	}
	return *p.NodeId
}

var DescribeLockWaitsDetailSnapshotReq_PageSize_DEFAULT int32

func (p *DescribeLockWaitsDetailSnapshotReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeLockWaitsDetailSnapshotReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeLockWaitsDetailSnapshotReq_PageNumber_DEFAULT int32

func (p *DescribeLockWaitsDetailSnapshotReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeLockWaitsDetailSnapshotReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeLockWaitsDetailSnapshotReq_QueryFilter_DEFAULT *WaitLockQueryFilter

func (p *DescribeLockWaitsDetailSnapshotReq) GetQueryFilter() (v *WaitLockQueryFilter) {
	if !p.IsSetQueryFilter() {
		return DescribeLockWaitsDetailSnapshotReq_QueryFilter_DEFAULT
	}
	return p.QueryFilter
}

var DescribeLockWaitsDetailSnapshotReq_OrderBy_DEFAULT WaitLockSortParam

func (p *DescribeLockWaitsDetailSnapshotReq) GetOrderBy() (v WaitLockSortParam) {
	if !p.IsSetOrderBy() {
		return DescribeLockWaitsDetailSnapshotReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeLockWaitsDetailSnapshotReq_SortBy_DEFAULT SortBy

func (p *DescribeLockWaitsDetailSnapshotReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeLockWaitsDetailSnapshotReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeLockWaitsDetailSnapshotReq_RegionId_DEFAULT string

func (p *DescribeLockWaitsDetailSnapshotReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeLockWaitsDetailSnapshotReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeLockWaitsDetailSnapshotReq_NodeIds_DEFAULT []string

func (p *DescribeLockWaitsDetailSnapshotReq) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return DescribeLockWaitsDetailSnapshotReq_NodeIds_DEFAULT
	}
	return p.NodeIds
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetQueryFilter(val *WaitLockQueryFilter) {
	p.QueryFilter = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetOrderBy(val *WaitLockSortParam) {
	p.OrderBy = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeLockWaitsDetailSnapshotReq) SetNodeIds(val []string) {
	p.NodeIds = val
}

var fieldIDToName_DescribeLockWaitsDetailSnapshotReq = map[int16]string{
	1:  "InstanceId",
	2:  "InstanceType",
	3:  "StartTime",
	4:  "EndTime",
	5:  "NodeId",
	6:  "PageSize",
	7:  "PageNumber",
	8:  "QueryFilter",
	9:  "OrderBy",
	10: "SortBy",
	11: "RegionId",
	12: "NodeIds",
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetQueryFilter() bool {
	return p.QueryFilter != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockWaitsDetailSnapshotReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockWaitsDetailSnapshotReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLockWaitsDetailSnapshotReq[fieldId]))
}

func (p *DescribeLockWaitsDetailSnapshotReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField8(iprot thrift.TProtocol) error {
	_field := NewWaitLockQueryFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.QueryFilter = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *WaitLockSortParam
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := WaitLockSortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotReq) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockWaitsDetailSnapshotReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLockWaitsDetailSnapshotReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryFilter() {
		if err = oprot.WriteFieldBegin("QueryFilter", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.QueryFilter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLockWaitsDetailSnapshotReq(%+v)", *p)

}

func (p *DescribeLockWaitsDetailSnapshotReq) DeepEqual(ano *DescribeLockWaitsDetailSnapshotReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.QueryFilter) {
		return false
	}
	if !p.Field9DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field10DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field11DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field12DeepEqual(ano.NodeIds) {
		return false
	}
	return true
}

func (p *DescribeLockWaitsDetailSnapshotReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field3DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field4DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field5DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field6DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field8DeepEqual(src *WaitLockQueryFilter) bool {

	if !p.QueryFilter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field9DeepEqual(src *WaitLockSortParam) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field10DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field11DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotReq) Field12DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeLockWaitsDetailSnapshotResp struct {
	DetailList []*DescribeLockCurrentWaitsDetail `thrift:"DetailList,1,required" frugal:"1,required,list<DescribeLockCurrentWaitsDetail>" json:"DetailList"`
	Total      int32                             `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeLockWaitsDetailSnapshotResp() *DescribeLockWaitsDetailSnapshotResp {
	return &DescribeLockWaitsDetailSnapshotResp{}
}

func (p *DescribeLockWaitsDetailSnapshotResp) InitDefault() {
}

func (p *DescribeLockWaitsDetailSnapshotResp) GetDetailList() (v []*DescribeLockCurrentWaitsDetail) {
	return p.DetailList
}

func (p *DescribeLockWaitsDetailSnapshotResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeLockWaitsDetailSnapshotResp) SetDetailList(val []*DescribeLockCurrentWaitsDetail) {
	p.DetailList = val
}
func (p *DescribeLockWaitsDetailSnapshotResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeLockWaitsDetailSnapshotResp = map[int16]string{
	1: "DetailList",
	2: "Total",
}

func (p *DescribeLockWaitsDetailSnapshotResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockWaitsDetailSnapshotResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDetailList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDetailList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockWaitsDetailSnapshotResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLockWaitsDetailSnapshotResp[fieldId]))
}

func (p *DescribeLockWaitsDetailSnapshotResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DescribeLockCurrentWaitsDetail, 0, size)
	values := make([]DescribeLockCurrentWaitsDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DetailList = _field
	return nil
}
func (p *DescribeLockWaitsDetailSnapshotResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeLockWaitsDetailSnapshotResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockWaitsDetailSnapshotResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLockWaitsDetailSnapshotResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DetailList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DetailList)); err != nil {
		return err
	}
	for _, v := range p.DetailList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLockWaitsDetailSnapshotResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLockWaitsDetailSnapshotResp(%+v)", *p)

}

func (p *DescribeLockWaitsDetailSnapshotResp) DeepEqual(ano *DescribeLockWaitsDetailSnapshotResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DetailList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeLockWaitsDetailSnapshotResp) Field1DeepEqual(src []*DescribeLockCurrentWaitsDetail) bool {

	if len(p.DetailList) != len(src) {
		return false
	}
	for i, v := range p.DetailList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeLockWaitsDetailSnapshotResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeLockCurrentWaitsDetail struct {
	NodeId             string `thrift:"NodeId,1,required" frugal:"1,required,string" json:"NodeId"`
	InstanceId         string `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	CollectTime        string `thrift:"CollectTime,3,required" frugal:"3,required,string" json:"CollectTime"`
	DbName             string `thrift:"DbName,4,required" frugal:"4,required,string" json:"DbName"`
	RTrxMysqlThreadId  string `thrift:"RTrxMysqlThreadId,5,required" frugal:"5,required,string" json:"RTrxMysqlThreadId"`
	RTrxId             string `thrift:"RTrxId,6,required" frugal:"6,required,string" json:"RTrxId"`
	RTrxState          string `thrift:"RTrxState,7,required" frugal:"7,required,string" json:"RTrxState"`
	RWaitingQuery      string `thrift:"RWaitingQuery,8,required" frugal:"8,required,string" json:"RWaitingQuery"`
	RTrxStarted        string `thrift:"RTrxStarted,9,required" frugal:"9,required,string" json:"RTrxStarted"`
	RTrxWaitStarted    string `thrift:"RTrxWaitStarted,10,required" frugal:"10,required,string" json:"RTrxWaitStarted"`
	RBlockedWaitSecs   string `thrift:"RBlockedWaitSecs,11,required" frugal:"11,required,string" json:"RBlockedWaitSecs"`
	RTrxRowsModified   string `thrift:"RTrxRowsModified,12,required" frugal:"12,required,string" json:"RTrxRowsModified"`
	RTrxRowsLocked     string `thrift:"RTrxRowsLocked,13,required" frugal:"13,required,string" json:"RTrxRowsLocked"`
	RTrxOperationState string `thrift:"RTrxOperationState,14,required" frugal:"14,required,string" json:"RTrxOperationState"`
	BTrxMysqlThreadId  string `thrift:"BTrxMysqlThreadId,15,required" frugal:"15,required,string" json:"BTrxMysqlThreadId"`
	BTrxId             string `thrift:"BTrxId,16,required" frugal:"16,required,string" json:"BTrxId"`
	BTrxState          string `thrift:"BTrxState,17,required" frugal:"17,required,string" json:"BTrxState"`
	BBlockingQuery     string `thrift:"BBlockingQuery,18,required" frugal:"18,required,string" json:"BBlockingQuery"`
	BTrxStarted        string `thrift:"BTrxStarted,19,required" frugal:"19,required,string" json:"BTrxStarted"`
	BTrxWaitStarted    string `thrift:"BTrxWaitStarted,20,required" frugal:"20,required,string" json:"BTrxWaitStarted"`
	BBlockingWaitSecs  string `thrift:"BBlockingWaitSecs,21,required" frugal:"21,required,string" json:"BBlockingWaitSecs"`
	BTrxRowsModified   string `thrift:"BTrxRowsModified,22,required" frugal:"22,required,string" json:"BTrxRowsModified"`
	BTrxRowsLocked     string `thrift:"BTrxRowsLocked,23,required" frugal:"23,required,string" json:"BTrxRowsLocked"`
	BTrxOperationState string `thrift:"BTrxOperationState,24,required" frugal:"24,required,string" json:"BTrxOperationState"`
}

func NewDescribeLockCurrentWaitsDetail() *DescribeLockCurrentWaitsDetail {
	return &DescribeLockCurrentWaitsDetail{}
}

func (p *DescribeLockCurrentWaitsDetail) InitDefault() {
}

func (p *DescribeLockCurrentWaitsDetail) GetNodeId() (v string) {
	return p.NodeId
}

func (p *DescribeLockCurrentWaitsDetail) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeLockCurrentWaitsDetail) GetCollectTime() (v string) {
	return p.CollectTime
}

func (p *DescribeLockCurrentWaitsDetail) GetDbName() (v string) {
	return p.DbName
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxMysqlThreadId() (v string) {
	return p.RTrxMysqlThreadId
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxId() (v string) {
	return p.RTrxId
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxState() (v string) {
	return p.RTrxState
}

func (p *DescribeLockCurrentWaitsDetail) GetRWaitingQuery() (v string) {
	return p.RWaitingQuery
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxStarted() (v string) {
	return p.RTrxStarted
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxWaitStarted() (v string) {
	return p.RTrxWaitStarted
}

func (p *DescribeLockCurrentWaitsDetail) GetRBlockedWaitSecs() (v string) {
	return p.RBlockedWaitSecs
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxRowsModified() (v string) {
	return p.RTrxRowsModified
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxRowsLocked() (v string) {
	return p.RTrxRowsLocked
}

func (p *DescribeLockCurrentWaitsDetail) GetRTrxOperationState() (v string) {
	return p.RTrxOperationState
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxMysqlThreadId() (v string) {
	return p.BTrxMysqlThreadId
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxId() (v string) {
	return p.BTrxId
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxState() (v string) {
	return p.BTrxState
}

func (p *DescribeLockCurrentWaitsDetail) GetBBlockingQuery() (v string) {
	return p.BBlockingQuery
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxStarted() (v string) {
	return p.BTrxStarted
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxWaitStarted() (v string) {
	return p.BTrxWaitStarted
}

func (p *DescribeLockCurrentWaitsDetail) GetBBlockingWaitSecs() (v string) {
	return p.BBlockingWaitSecs
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxRowsModified() (v string) {
	return p.BTrxRowsModified
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxRowsLocked() (v string) {
	return p.BTrxRowsLocked
}

func (p *DescribeLockCurrentWaitsDetail) GetBTrxOperationState() (v string) {
	return p.BTrxOperationState
}
func (p *DescribeLockCurrentWaitsDetail) SetNodeId(val string) {
	p.NodeId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetCollectTime(val string) {
	p.CollectTime = val
}
func (p *DescribeLockCurrentWaitsDetail) SetDbName(val string) {
	p.DbName = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxMysqlThreadId(val string) {
	p.RTrxMysqlThreadId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxId(val string) {
	p.RTrxId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxState(val string) {
	p.RTrxState = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRWaitingQuery(val string) {
	p.RWaitingQuery = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxStarted(val string) {
	p.RTrxStarted = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxWaitStarted(val string) {
	p.RTrxWaitStarted = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRBlockedWaitSecs(val string) {
	p.RBlockedWaitSecs = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxRowsModified(val string) {
	p.RTrxRowsModified = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxRowsLocked(val string) {
	p.RTrxRowsLocked = val
}
func (p *DescribeLockCurrentWaitsDetail) SetRTrxOperationState(val string) {
	p.RTrxOperationState = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxMysqlThreadId(val string) {
	p.BTrxMysqlThreadId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxId(val string) {
	p.BTrxId = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxState(val string) {
	p.BTrxState = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBBlockingQuery(val string) {
	p.BBlockingQuery = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxStarted(val string) {
	p.BTrxStarted = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxWaitStarted(val string) {
	p.BTrxWaitStarted = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBBlockingWaitSecs(val string) {
	p.BBlockingWaitSecs = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxRowsModified(val string) {
	p.BTrxRowsModified = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxRowsLocked(val string) {
	p.BTrxRowsLocked = val
}
func (p *DescribeLockCurrentWaitsDetail) SetBTrxOperationState(val string) {
	p.BTrxOperationState = val
}

var fieldIDToName_DescribeLockCurrentWaitsDetail = map[int16]string{
	1:  "NodeId",
	2:  "InstanceId",
	3:  "CollectTime",
	4:  "DbName",
	5:  "RTrxMysqlThreadId",
	6:  "RTrxId",
	7:  "RTrxState",
	8:  "RWaitingQuery",
	9:  "RTrxStarted",
	10: "RTrxWaitStarted",
	11: "RBlockedWaitSecs",
	12: "RTrxRowsModified",
	13: "RTrxRowsLocked",
	14: "RTrxOperationState",
	15: "BTrxMysqlThreadId",
	16: "BTrxId",
	17: "BTrxState",
	18: "BBlockingQuery",
	19: "BTrxStarted",
	20: "BTrxWaitStarted",
	21: "BBlockingWaitSecs",
	22: "BTrxRowsModified",
	23: "BTrxRowsLocked",
	24: "BTrxOperationState",
}

func (p *DescribeLockCurrentWaitsDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeId bool = false
	var issetInstanceId bool = false
	var issetCollectTime bool = false
	var issetDbName bool = false
	var issetRTrxMysqlThreadId bool = false
	var issetRTrxId bool = false
	var issetRTrxState bool = false
	var issetRWaitingQuery bool = false
	var issetRTrxStarted bool = false
	var issetRTrxWaitStarted bool = false
	var issetRBlockedWaitSecs bool = false
	var issetRTrxRowsModified bool = false
	var issetRTrxRowsLocked bool = false
	var issetRTrxOperationState bool = false
	var issetBTrxMysqlThreadId bool = false
	var issetBTrxId bool = false
	var issetBTrxState bool = false
	var issetBBlockingQuery bool = false
	var issetBTrxStarted bool = false
	var issetBTrxWaitStarted bool = false
	var issetBBlockingWaitSecs bool = false
	var issetBTrxRowsModified bool = false
	var issetBTrxRowsLocked bool = false
	var issetBTrxOperationState bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCollectTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxMysqlThreadId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetRWaitingQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxStarted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxWaitStarted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetRBlockedWaitSecs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxRowsModified = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxRowsLocked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetRTrxOperationState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxMysqlThreadId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetBBlockingQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxStarted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxWaitStarted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
				issetBBlockingWaitSecs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxRowsModified = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxRowsLocked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
				issetBTrxOperationState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCollectTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRTrxMysqlThreadId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetRTrxId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRTrxState {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetRWaitingQuery {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetRTrxStarted {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetRTrxWaitStarted {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetRBlockedWaitSecs {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetRTrxRowsModified {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetRTrxRowsLocked {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetRTrxOperationState {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetBTrxMysqlThreadId {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetBTrxId {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetBTrxState {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetBBlockingQuery {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetBTrxStarted {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetBTrxWaitStarted {
		fieldId = 20
		goto RequiredFieldNotSetError
	}

	if !issetBBlockingWaitSecs {
		fieldId = 21
		goto RequiredFieldNotSetError
	}

	if !issetBTrxRowsModified {
		fieldId = 22
		goto RequiredFieldNotSetError
	}

	if !issetBTrxRowsLocked {
		fieldId = 23
		goto RequiredFieldNotSetError
	}

	if !issetBTrxOperationState {
		fieldId = 24
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsDetail[fieldId]))
}

func (p *DescribeLockCurrentWaitsDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CollectTime = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxMysqlThreadId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxState = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RWaitingQuery = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxStarted = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxWaitStarted = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RBlockedWaitSecs = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxRowsModified = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxRowsLocked = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RTrxOperationState = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxMysqlThreadId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxId = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxState = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BBlockingQuery = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxStarted = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxWaitStarted = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BBlockingWaitSecs = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField22(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxRowsModified = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxRowsLocked = _field
	return nil
}
func (p *DescribeLockCurrentWaitsDetail) ReadField24(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BTrxOperationState = _field
	return nil
}

func (p *DescribeLockCurrentWaitsDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLockCurrentWaitsDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLockCurrentWaitsDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CollectTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CollectTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxMysqlThreadId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxMysqlThreadId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxId", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxState", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RWaitingQuery", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RWaitingQuery); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxStarted", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxStarted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxWaitStarted", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxWaitStarted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RBlockedWaitSecs", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RBlockedWaitSecs); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxRowsModified", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxRowsModified); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxRowsLocked", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxRowsLocked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RTrxOperationState", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RTrxOperationState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxMysqlThreadId", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxMysqlThreadId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxId", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxState", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BBlockingQuery", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BBlockingQuery); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxStarted", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxStarted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxWaitStarted", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxWaitStarted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BBlockingWaitSecs", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BBlockingWaitSecs); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxRowsModified", thrift.STRING, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxRowsModified); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxRowsLocked", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxRowsLocked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) writeField24(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BTrxOperationState", thrift.STRING, 24); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BTrxOperationState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}

func (p *DescribeLockCurrentWaitsDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLockCurrentWaitsDetail(%+v)", *p)

}

func (p *DescribeLockCurrentWaitsDetail) DeepEqual(ano *DescribeLockCurrentWaitsDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.CollectTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field5DeepEqual(ano.RTrxMysqlThreadId) {
		return false
	}
	if !p.Field6DeepEqual(ano.RTrxId) {
		return false
	}
	if !p.Field7DeepEqual(ano.RTrxState) {
		return false
	}
	if !p.Field8DeepEqual(ano.RWaitingQuery) {
		return false
	}
	if !p.Field9DeepEqual(ano.RTrxStarted) {
		return false
	}
	if !p.Field10DeepEqual(ano.RTrxWaitStarted) {
		return false
	}
	if !p.Field11DeepEqual(ano.RBlockedWaitSecs) {
		return false
	}
	if !p.Field12DeepEqual(ano.RTrxRowsModified) {
		return false
	}
	if !p.Field13DeepEqual(ano.RTrxRowsLocked) {
		return false
	}
	if !p.Field14DeepEqual(ano.RTrxOperationState) {
		return false
	}
	if !p.Field15DeepEqual(ano.BTrxMysqlThreadId) {
		return false
	}
	if !p.Field16DeepEqual(ano.BTrxId) {
		return false
	}
	if !p.Field17DeepEqual(ano.BTrxState) {
		return false
	}
	if !p.Field18DeepEqual(ano.BBlockingQuery) {
		return false
	}
	if !p.Field19DeepEqual(ano.BTrxStarted) {
		return false
	}
	if !p.Field20DeepEqual(ano.BTrxWaitStarted) {
		return false
	}
	if !p.Field21DeepEqual(ano.BBlockingWaitSecs) {
		return false
	}
	if !p.Field22DeepEqual(ano.BTrxRowsModified) {
		return false
	}
	if !p.Field23DeepEqual(ano.BTrxRowsLocked) {
		return false
	}
	if !p.Field24DeepEqual(ano.BTrxOperationState) {
		return false
	}
	return true
}

func (p *DescribeLockCurrentWaitsDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CollectTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field5DeepEqual(src string) bool {

	if strings.Compare(p.RTrxMysqlThreadId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field6DeepEqual(src string) bool {

	if strings.Compare(p.RTrxId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field7DeepEqual(src string) bool {

	if strings.Compare(p.RTrxState, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field8DeepEqual(src string) bool {

	if strings.Compare(p.RWaitingQuery, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field9DeepEqual(src string) bool {

	if strings.Compare(p.RTrxStarted, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field10DeepEqual(src string) bool {

	if strings.Compare(p.RTrxWaitStarted, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field11DeepEqual(src string) bool {

	if strings.Compare(p.RBlockedWaitSecs, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field12DeepEqual(src string) bool {

	if strings.Compare(p.RTrxRowsModified, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field13DeepEqual(src string) bool {

	if strings.Compare(p.RTrxRowsLocked, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field14DeepEqual(src string) bool {

	if strings.Compare(p.RTrxOperationState, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field15DeepEqual(src string) bool {

	if strings.Compare(p.BTrxMysqlThreadId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field16DeepEqual(src string) bool {

	if strings.Compare(p.BTrxId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field17DeepEqual(src string) bool {

	if strings.Compare(p.BTrxState, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field18DeepEqual(src string) bool {

	if strings.Compare(p.BBlockingQuery, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field19DeepEqual(src string) bool {

	if strings.Compare(p.BTrxStarted, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field20DeepEqual(src string) bool {

	if strings.Compare(p.BTrxWaitStarted, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field21DeepEqual(src string) bool {

	if strings.Compare(p.BBlockingWaitSecs, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field22DeepEqual(src string) bool {

	if strings.Compare(p.BTrxRowsModified, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field23DeepEqual(src string) bool {

	if strings.Compare(p.BTrxRowsLocked, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLockCurrentWaitsDetail) Field24DeepEqual(src string) bool {

	if strings.Compare(p.BTrxOperationState, src) != 0 {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeInstanceListReq struct {
	IsPreHour    bool         `thrift:"IsPreHour,1,required" frugal:"1,required,bool" json:"IsPreHour"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	RegionId     *string      `thrift:"RegionId,3,optional" frugal:"3,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeInstanceListReq() *DescribeInstanceListReq {
	return &DescribeInstanceListReq{}
}

func (p *DescribeInstanceListReq) InitDefault() {
}

func (p *DescribeInstanceListReq) GetIsPreHour() (v bool) {
	return p.IsPreHour
}

func (p *DescribeInstanceListReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var DescribeInstanceListReq_RegionId_DEFAULT string

func (p *DescribeInstanceListReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeInstanceListReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeInstanceListReq) SetIsPreHour(val bool) {
	p.IsPreHour = val
}
func (p *DescribeInstanceListReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeInstanceListReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeInstanceListReq = map[int16]string{
	1: "IsPreHour",
	2: "InstanceType",
	3: "RegionId",
}

func (p *DescribeInstanceListReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeInstanceListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceListReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIsPreHour bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsPreHour = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIsPreHour {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceListReq[fieldId]))
}

func (p *DescribeInstanceListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsPreHour = _field
	return nil
}
func (p *DescribeInstanceListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeInstanceListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeInstanceListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsPreHour", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsPreHour); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceListReq(%+v)", *p)

}

func (p *DescribeInstanceListReq) DeepEqual(ano *DescribeInstanceListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IsPreHour) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeInstanceListReq) Field1DeepEqual(src bool) bool {

	if p.IsPreHour != src {
		return false
	}
	return true
}
func (p *DescribeInstanceListReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeInstanceListReq) Field3DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type CollectorInstanceInfo struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	TenantId     string       `thrift:"TenantId,3,required" frugal:"3,required,string" json:"TenantId"`
	RegionId     *string      `thrift:"RegionId,4,optional" frugal:"4,optional,string" json:"RegionId,omitempty"`
}

func NewCollectorInstanceInfo() *CollectorInstanceInfo {
	return &CollectorInstanceInfo{}
}

func (p *CollectorInstanceInfo) InitDefault() {
}

func (p *CollectorInstanceInfo) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CollectorInstanceInfo) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CollectorInstanceInfo) GetTenantId() (v string) {
	return p.TenantId
}

var CollectorInstanceInfo_RegionId_DEFAULT string

func (p *CollectorInstanceInfo) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return CollectorInstanceInfo_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *CollectorInstanceInfo) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CollectorInstanceInfo) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CollectorInstanceInfo) SetTenantId(val string) {
	p.TenantId = val
}
func (p *CollectorInstanceInfo) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_CollectorInstanceInfo = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "TenantId",
	4: "RegionId",
}

func (p *CollectorInstanceInfo) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *CollectorInstanceInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CollectorInstanceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetTenantId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTenantId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CollectorInstanceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CollectorInstanceInfo[fieldId]))
}

func (p *CollectorInstanceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CollectorInstanceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CollectorInstanceInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *CollectorInstanceInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *CollectorInstanceInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CollectorInstanceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CollectorInstanceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CollectorInstanceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CollectorInstanceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CollectorInstanceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CollectorInstanceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CollectorInstanceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CollectorInstanceInfo(%+v)", *p)

}

func (p *CollectorInstanceInfo) DeepEqual(ano *CollectorInstanceInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *CollectorInstanceInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CollectorInstanceInfo) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CollectorInstanceInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *CollectorInstanceInfo) Field4DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceListResp struct {
	InstanceList []*CollectorInstanceInfo `thrift:"instanceList,1,required" frugal:"1,required,list<CollectorInstanceInfo>" json:"instanceList"`
	Total        int32                    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeInstanceListResp() *DescribeInstanceListResp {
	return &DescribeInstanceListResp{}
}

func (p *DescribeInstanceListResp) InitDefault() {
}

func (p *DescribeInstanceListResp) GetInstanceList() (v []*CollectorInstanceInfo) {
	return p.InstanceList
}

func (p *DescribeInstanceListResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeInstanceListResp) SetInstanceList(val []*CollectorInstanceInfo) {
	p.InstanceList = val
}
func (p *DescribeInstanceListResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeInstanceListResp = map[int16]string{
	1: "instanceList",
	2: "Total",
}

func (p *DescribeInstanceListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceListResp[fieldId]))
}

func (p *DescribeInstanceListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CollectorInstanceInfo, 0, size)
	values := make([]CollectorInstanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceList = _field
	return nil
}
func (p *DescribeInstanceListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeInstanceListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("instanceList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceList)); err != nil {
		return err
	}
	for _, v := range p.InstanceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceListResp(%+v)", *p)

}

func (p *DescribeInstanceListResp) DeepEqual(ano *DescribeInstanceListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeInstanceListResp) Field1DeepEqual(src []*CollectorInstanceInfo) bool {

	if len(p.InstanceList) != len(src) {
		return false
	}
	for i, v := range p.InstanceList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeInstanceListResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeDataSourceReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	HostIP       string       `thrift:"HostIP,3,required" frugal:"3,required,string" json:"HostIP"`
	RegionId     *string      `thrift:"RegionId,4,optional" frugal:"4,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeDataSourceReq() *DescribeDataSourceReq {
	return &DescribeDataSourceReq{}
}

func (p *DescribeDataSourceReq) InitDefault() {
}

func (p *DescribeDataSourceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDataSourceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDataSourceReq) GetHostIP() (v string) {
	return p.HostIP
}

var DescribeDataSourceReq_RegionId_DEFAULT string

func (p *DescribeDataSourceReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeDataSourceReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeDataSourceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDataSourceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDataSourceReq) SetHostIP(val string) {
	p.HostIP = val
}
func (p *DescribeDataSourceReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeDataSourceReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "HostIP",
	4: "RegionId",
}

func (p *DescribeDataSourceReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeDataSourceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetHostIP bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetHostIP = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetHostIP {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDataSourceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDataSourceReq[fieldId]))
}

func (p *DescribeDataSourceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDataSourceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDataSourceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HostIP = _field
	return nil
}
func (p *DescribeDataSourceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeDataSourceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDataSourceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDataSourceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDataSourceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDataSourceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HostIP", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.HostIP); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDataSourceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDataSourceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDataSourceReq(%+v)", *p)

}

func (p *DescribeDataSourceReq) DeepEqual(ano *DescribeDataSourceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.HostIP) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeDataSourceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDataSourceReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDataSourceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.HostIP, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDataSourceReq) Field4DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeDataSourceResp struct {
	DataSourceList []*DescribeDataSourceInfo `thrift:"dataSourceList,1,required" frugal:"1,required,list<DescribeDataSourceInfo>" json:"dataSourceList"`
}

func NewDescribeDataSourceResp() *DescribeDataSourceResp {
	return &DescribeDataSourceResp{}
}

func (p *DescribeDataSourceResp) InitDefault() {
}

func (p *DescribeDataSourceResp) GetDataSourceList() (v []*DescribeDataSourceInfo) {
	return p.DataSourceList
}
func (p *DescribeDataSourceResp) SetDataSourceList(val []*DescribeDataSourceInfo) {
	p.DataSourceList = val
}

var fieldIDToName_DescribeDataSourceResp = map[int16]string{
	1: "dataSourceList",
}

func (p *DescribeDataSourceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSourceList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSourceList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataSourceList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDataSourceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDataSourceResp[fieldId]))
}

func (p *DescribeDataSourceResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DescribeDataSourceInfo, 0, size)
	values := make([]DescribeDataSourceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataSourceList = _field
	return nil
}

func (p *DescribeDataSourceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDataSourceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDataSourceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("dataSourceList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataSourceList)); err != nil {
		return err
	}
	for _, v := range p.DataSourceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDataSourceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDataSourceResp(%+v)", *p)

}

func (p *DescribeDataSourceResp) DeepEqual(ano *DescribeDataSourceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataSourceList) {
		return false
	}
	return true
}

func (p *DescribeDataSourceResp) Field1DeepEqual(src []*DescribeDataSourceInfo) bool {

	if len(p.DataSourceList) != len(src) {
		return false
	}
	for i, v := range p.DataSourceList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDataSourceInfo struct {
	DataSource *DataSource `thrift:"DataSource,1,required" frugal:"1,required,DataSource" json:"DataSource"`
	NodeId     string      `thrift:"NodeId,2,required" frugal:"2,required,string" json:"NodeId"`
	Component  string      `thrift:"Component,3,required" frugal:"3,required,string" json:"Component"`
	NodeType   *string     `thrift:"NodeType,4,optional" frugal:"4,optional,string" json:"NodeType,omitempty"`
}

func NewDescribeDataSourceInfo() *DescribeDataSourceInfo {
	return &DescribeDataSourceInfo{}
}

func (p *DescribeDataSourceInfo) InitDefault() {
}

var DescribeDataSourceInfo_DataSource_DEFAULT *DataSource

func (p *DescribeDataSourceInfo) GetDataSource() (v *DataSource) {
	if !p.IsSetDataSource() {
		return DescribeDataSourceInfo_DataSource_DEFAULT
	}
	return p.DataSource
}

func (p *DescribeDataSourceInfo) GetNodeId() (v string) {
	return p.NodeId
}

func (p *DescribeDataSourceInfo) GetComponent() (v string) {
	return p.Component
}

var DescribeDataSourceInfo_NodeType_DEFAULT string

func (p *DescribeDataSourceInfo) GetNodeType() (v string) {
	if !p.IsSetNodeType() {
		return DescribeDataSourceInfo_NodeType_DEFAULT
	}
	return *p.NodeType
}
func (p *DescribeDataSourceInfo) SetDataSource(val *DataSource) {
	p.DataSource = val
}
func (p *DescribeDataSourceInfo) SetNodeId(val string) {
	p.NodeId = val
}
func (p *DescribeDataSourceInfo) SetComponent(val string) {
	p.Component = val
}
func (p *DescribeDataSourceInfo) SetNodeType(val *string) {
	p.NodeType = val
}

var fieldIDToName_DescribeDataSourceInfo = map[int16]string{
	1: "DataSource",
	2: "NodeId",
	3: "Component",
	4: "NodeType",
}

func (p *DescribeDataSourceInfo) IsSetDataSource() bool {
	return p.DataSource != nil
}

func (p *DescribeDataSourceInfo) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *DescribeDataSourceInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSource bool = false
	var issetNodeId bool = false
	var issetComponent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetComponent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataSource {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComponent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDataSourceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDataSourceInfo[fieldId]))
}

func (p *DescribeDataSourceInfo) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDataSource()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DataSource = _field
	return nil
}
func (p *DescribeDataSourceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *DescribeDataSourceInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Component = _field
	return nil
}
func (p *DescribeDataSourceInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeType = _field
	return nil
}

func (p *DescribeDataSourceInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDataSourceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDataSourceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDataSourceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSource", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.DataSource.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDataSourceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDataSourceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Component", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Component); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDataSourceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDataSourceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDataSourceInfo(%+v)", *p)

}

func (p *DescribeDataSourceInfo) DeepEqual(ano *DescribeDataSourceInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataSource) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Component) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *DescribeDataSourceInfo) Field1DeepEqual(src *DataSource) bool {

	if !p.DataSource.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDataSourceInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDataSourceInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Component, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDataSourceInfo) Field4DeepEqual(src *string) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeType, *src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceDetailReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
}

func NewDescribeInstanceDetailReq() *DescribeInstanceDetailReq {
	return &DescribeInstanceDetailReq{}
}

func (p *DescribeInstanceDetailReq) InitDefault() {
}

func (p *DescribeInstanceDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeInstanceDetailReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}
func (p *DescribeInstanceDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeInstanceDetailReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeInstanceDetailReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
}

func (p *DescribeInstanceDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceDetailReq[fieldId]))
}

func (p *DescribeInstanceDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeInstanceDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeInstanceDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDetailReq(%+v)", *p)

}

func (p *DescribeInstanceDetailReq) DeepEqual(ano *DescribeInstanceDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeInstanceDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}

type DescribeInstanceDetailResp struct {
	InstanceId      string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceName    string       `thrift:"InstanceName,2,required" frugal:"2,required,string" json:"InstanceName"`
	InstanceStatus  string       `thrift:"InstanceStatus,3,required" frugal:"3,required,string" json:"InstanceStatus"`
	RegionId        string       `thrift:"RegionId,4,required" frugal:"4,required,string" json:"RegionId"`
	ZoneId          string       `thrift:"ZoneId,5,required" frugal:"5,required,string" json:"ZoneId"`
	DBEngine        string       `thrift:"DBEngine,6,required" frugal:"6,required,string" json:"DBEngine"`
	DBEngineVersion string       `thrift:"DBEngineVersion,7,required" frugal:"7,required,string" json:"DBEngineVersion"`
	InstanceType    InstanceType `thrift:"InstanceType,8,required" frugal:"8,required,InstanceType" json:"InstanceType"`
}

func NewDescribeInstanceDetailResp() *DescribeInstanceDetailResp {
	return &DescribeInstanceDetailResp{}
}

func (p *DescribeInstanceDetailResp) InitDefault() {
}

func (p *DescribeInstanceDetailResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeInstanceDetailResp) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *DescribeInstanceDetailResp) GetInstanceStatus() (v string) {
	return p.InstanceStatus
}

func (p *DescribeInstanceDetailResp) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeInstanceDetailResp) GetZoneId() (v string) {
	return p.ZoneId
}

func (p *DescribeInstanceDetailResp) GetDBEngine() (v string) {
	return p.DBEngine
}

func (p *DescribeInstanceDetailResp) GetDBEngineVersion() (v string) {
	return p.DBEngineVersion
}

func (p *DescribeInstanceDetailResp) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}
func (p *DescribeInstanceDetailResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeInstanceDetailResp) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *DescribeInstanceDetailResp) SetInstanceStatus(val string) {
	p.InstanceStatus = val
}
func (p *DescribeInstanceDetailResp) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeInstanceDetailResp) SetZoneId(val string) {
	p.ZoneId = val
}
func (p *DescribeInstanceDetailResp) SetDBEngine(val string) {
	p.DBEngine = val
}
func (p *DescribeInstanceDetailResp) SetDBEngineVersion(val string) {
	p.DBEngineVersion = val
}
func (p *DescribeInstanceDetailResp) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeInstanceDetailResp = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "InstanceStatus",
	4: "RegionId",
	5: "ZoneId",
	6: "DBEngine",
	7: "DBEngineVersion",
	8: "InstanceType",
}

func (p *DescribeInstanceDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetInstanceStatus bool = false
	var issetRegionId bool = false
	var issetZoneId bool = false
	var issetDBEngine bool = false
	var issetDBEngineVersion bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetZoneId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngine = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetZoneId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDBEngine {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineVersion {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceDetailResp[fieldId]))
}

func (p *DescribeInstanceDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceStatus = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ZoneId = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBEngine = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *DescribeInstanceDetailResp) ReadField8(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeInstanceDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceStatus", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ZoneId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngine", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBEngine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBEngineVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDetailResp(%+v)", *p)

}

func (p *DescribeInstanceDetailResp) DeepEqual(ano *DescribeInstanceDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field5DeepEqual(ano.ZoneId) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBEngine) {
		return false
	}
	if !p.Field7DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field8DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeInstanceDetailResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceStatus, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ZoneId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.DBEngine, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field7DeepEqual(src string) bool {

	if strings.Compare(p.DBEngineVersion, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDetailResp) Field8DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}

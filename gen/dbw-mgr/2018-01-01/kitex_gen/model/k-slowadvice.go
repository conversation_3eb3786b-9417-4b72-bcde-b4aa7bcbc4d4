// Code generated by Kitex v1.18.1. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *AdviceConfig) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEnable bool = false
	var issetAnalysisResKeepDays bool = false
	var issetOptimizeResTrackDays bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAnalysisResKeepDays = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOptimizeResTrackDays = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetEnable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAnalysisResKeepDays {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOptimizeResTrackDays {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviceConfig[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_AdviceConfig[fieldId]))
}

func (p *AdviceConfig) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Enable = _field
	return offset, nil
}

func (p *AdviceConfig) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AnalysisResKeepDays = _field
	return offset, nil
}

func (p *AdviceConfig) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OptimizeResTrackDays = _field
	return offset, nil
}

func (p *AdviceConfig) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AdviceConfig) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AdviceConfig) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AdviceConfig) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Enable)
	return offset
}

func (p *AdviceConfig) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.AnalysisResKeepDays)
	return offset
}

func (p *AdviceConfig) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.OptimizeResTrackDays)
	return offset
}

func (p *AdviceConfig) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *AdviceConfig) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *AdviceConfig) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *AdviceConfig) DeepCopy(s interface{}) error {
	src, ok := s.(*AdviceConfig)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Enable = src.Enable

	p.AnalysisResKeepDays = src.AnalysisResKeepDays

	p.OptimizeResTrackDays = src.OptimizeResTrackDays

	return nil
}

func (p *SlowQueryAdviceConfig) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetRegionId bool = false
	var issetInstanceType bool = false
	var issetConfig bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetConfig = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetConfig {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceConfig[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SlowQueryAdviceConfig[fieldId]))
}

func (p *SlowQueryAdviceConfig) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SlowQueryAdviceConfigId = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TenantID = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastReadField7(buf []byte) (int, error) {
	offset := 0
	_field := NewAdviceConfig()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Config = _field
	return offset, nil
}

func (p *SlowQueryAdviceConfig) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SlowQueryAdviceConfig) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SlowQueryAdviceConfig) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SlowQueryAdviceConfig) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSlowQueryAdviceConfigId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.SlowQueryAdviceConfigId)
	}
	return offset
}

func (p *SlowQueryAdviceConfig) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *SlowQueryAdviceConfig) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *SlowQueryAdviceConfig) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *SlowQueryAdviceConfig) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTenantID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TenantID)
	}
	return offset
}

func (p *SlowQueryAdviceConfig) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 7)
	offset += p.Config.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SlowQueryAdviceConfig) field1Length() int {
	l := 0
	if p.IsSetSlowQueryAdviceConfigId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *SlowQueryAdviceConfig) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *SlowQueryAdviceConfig) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *SlowQueryAdviceConfig) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceConfig) field6Length() int {
	l := 0
	if p.IsSetTenantID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TenantID)
	}
	return l
}

func (p *SlowQueryAdviceConfig) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Config.BLength()
	return l
}

func (p *SlowQueryAdviceConfig) DeepCopy(s interface{}) error {
	src, ok := s.(*SlowQueryAdviceConfig)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SlowQueryAdviceConfigId != nil {
		tmp := *src.SlowQueryAdviceConfigId
		p.SlowQueryAdviceConfigId = &tmp
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	p.InstanceType = src.InstanceType

	if src.TenantID != nil {
		var tmp string
		if *src.TenantID != "" {
			tmp = kutils.StringDeepCopy(*src.TenantID)
		}
		p.TenantID = &tmp
	}

	var _config *AdviceConfig
	if src.Config != nil {
		_config = &AdviceConfig{}
		if err := _config.DeepCopy(src.Config); err != nil {
			return err
		}
	}
	p.Config = _config

	return nil
}

func (p *UpdateSlowQueryAnalysisReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConfigs bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetConfigs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetConfigs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateSlowQueryAnalysisReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpdateSlowQueryAnalysisReq[fieldId]))
}

func (p *UpdateSlowQueryAnalysisReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SlowQueryAdviceConfig, 0, size)
	values := make([]SlowQueryAdviceConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Configs = _field
	return offset, nil
}

func (p *UpdateSlowQueryAnalysisReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateSlowQueryAnalysisReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateSlowQueryAnalysisReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateSlowQueryAnalysisReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Configs {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *UpdateSlowQueryAnalysisReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Configs {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *UpdateSlowQueryAnalysisReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateSlowQueryAnalysisReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Configs != nil {
		p.Configs = make([]*SlowQueryAdviceConfig, 0, len(src.Configs))
		for _, elem := range src.Configs {
			var _elem *SlowQueryAdviceConfig
			if elem != nil {
				_elem = &SlowQueryAdviceConfig{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Configs = append(p.Configs, _elem)
		}
	}

	return nil
}

func (p *UpdateSlowQueryAnalysisResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpdateSlowQueryAnalysisResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateSlowQueryAnalysisResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateSlowQueryAnalysisResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateSlowQueryAnalysisResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *ListSlowQueryAdviceConfigReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceConfigReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceConfigReq[fieldId]))
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SlowQueryAdviceConfigId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ListSlowQueryAdviceConfigReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageNumber)
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageSize)
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSlowQueryAdviceConfigId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.SlowQueryAdviceConfigId)
	}
	return offset
}

func (p *ListSlowQueryAdviceConfigReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceConfigReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *ListSlowQueryAdviceConfigReq) field3Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *ListSlowQueryAdviceConfigReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceConfigReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceConfigReq) field6Length() int {
	l := 0
	if p.IsSetSlowQueryAdviceConfigId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *ListSlowQueryAdviceConfigReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ListSlowQueryAdviceConfigReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InstanceType = src.InstanceType

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	p.PageNumber = src.PageNumber

	p.PageSize = src.PageSize

	if src.SlowQueryAdviceConfigId != nil {
		tmp := *src.SlowQueryAdviceConfigId
		p.SlowQueryAdviceConfigId = &tmp
	}

	return nil
}

func (p *ListSlowQueryAdviceConfigResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetConfigs bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetConfigs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetConfigs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceConfigResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceConfigResp[fieldId]))
}

func (p *ListSlowQueryAdviceConfigResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SlowQueryAdviceConfig, 0, size)
	values := make([]SlowQueryAdviceConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Configs = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceConfigResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ListSlowQueryAdviceConfigResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ListSlowQueryAdviceConfigResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ListSlowQueryAdviceConfigResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *ListSlowQueryAdviceConfigResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Configs {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ListSlowQueryAdviceConfigResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceConfigResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Configs {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ListSlowQueryAdviceConfigResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ListSlowQueryAdviceConfigResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.Configs != nil {
		p.Configs = make([]*SlowQueryAdviceConfig, 0, len(src.Configs))
		for _, elem := range src.Configs {
			var _elem *SlowQueryAdviceConfig
			if elem != nil {
				_elem = &SlowQueryAdviceConfig{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Configs = append(p.Configs, _elem)
		}
	}

	return nil
}

func (p *SlowQueryAdviceTaskAgg) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDate bool = false
	var issetSlowQueryNum bool = false
	var issetAdviceNum bool = false
	var issetAdviceIndexNum bool = false
	var issetAdviceRewriteNum bool = false
	var issetNoAdviceSQLNum bool = false
	var issetStatus bool = false
	var issetSummaryId bool = false
	var issetDb bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDate = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSlowQueryNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceRewriteNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetNoAdviceSQLNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSummaryId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDb = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDate {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSlowQueryNum {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceNum {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexNum {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetAdviceRewriteNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNoAdviceSQLNum {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetSummaryId {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetDb {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskAgg[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskAgg[fieldId]))
}

func (p *SlowQueryAdviceTaskAgg) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Date = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SlowQueryNum = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AdviceNum = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AdviceIndexNum = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AdviceRewriteNum = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NoAdviceSQLNum = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field SlowQuerySummaryStatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = SlowQuerySummaryStatus(v)
	}
	p.Status = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SummaryId = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Db = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskAgg) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SlowQueryAdviceTaskAgg) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SlowQueryAdviceTaskAgg) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Date)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.SlowQueryNum)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.AdviceNum)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.AdviceIndexNum)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.AdviceRewriteNum)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], p.NoAdviceSQLNum)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Status))
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SummaryId)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Db)
	return offset
}

func (p *SlowQueryAdviceTaskAgg) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Date)
	return l
}

func (p *SlowQueryAdviceTaskAgg) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskAgg) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SummaryId)
	return l
}

func (p *SlowQueryAdviceTaskAgg) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Db)
	return l
}

func (p *SlowQueryAdviceTaskAgg) DeepCopy(s interface{}) error {
	src, ok := s.(*SlowQueryAdviceTaskAgg)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Date != "" {
		p.Date = kutils.StringDeepCopy(src.Date)
	}

	p.SlowQueryNum = src.SlowQueryNum

	p.AdviceNum = src.AdviceNum

	p.AdviceIndexNum = src.AdviceIndexNum

	p.AdviceRewriteNum = src.AdviceRewriteNum

	p.NoAdviceSQLNum = src.NoAdviceSQLNum

	p.Status = src.Status

	if src.SummaryId != "" {
		p.SummaryId = kutils.StringDeepCopy(src.SummaryId)
	}

	if src.Db != "" {
		p.Db = kutils.StringDeepCopy(src.Db)
	}

	return nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryReq[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SqlDateTimeStamp = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SlowQueryAdviceTaskHistoryReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageNumber)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageSize)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlDateTimeStamp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.SqlDateTimeStamp)
	}
	return offset
}

func (p *SlowQueryAdviceTaskHistoryReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) field6Length() int {
	l := 0
	if p.IsSetSqlDateTimeStamp() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *SlowQueryAdviceTaskHistoryReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SlowQueryAdviceTaskHistoryReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InstanceType = src.InstanceType

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.PageNumber = src.PageNumber

	p.PageSize = src.PageSize

	if src.SqlDateTimeStamp != nil {
		tmp := *src.SqlDateTimeStamp
		p.SqlDateTimeStamp = &tmp
	}

	return nil
}

func (p *SlowQueryAdviceTaskHistoryResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetResList bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetResList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResList {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SlowQueryAdviceTaskHistoryResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SlowQueryAdviceTaskHistoryResp[fieldId]))
}

func (p *SlowQueryAdviceTaskHistoryResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SlowQueryAdviceTaskAgg, 0, size)
	values := make([]SlowQueryAdviceTaskAgg, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ResList = _field
	return offset, nil
}

func (p *SlowQueryAdviceTaskHistoryResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SlowQueryAdviceTaskHistoryResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SlowQueryAdviceTaskHistoryResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SlowQueryAdviceTaskHistoryResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ResList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *SlowQueryAdviceTaskHistoryResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SlowQueryAdviceTaskHistoryResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ResList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *SlowQueryAdviceTaskHistoryResp) DeepCopy(s interface{}) error {
	src, ok := s.(*SlowQueryAdviceTaskHistoryResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.ResList != nil {
		p.ResList = make([]*SlowQueryAdviceTaskAgg, 0, len(src.ResList))
		for _, elem := range src.ResList {
			var _elem *SlowQueryAdviceTaskAgg
			if elem != nil {
				_elem = &SlowQueryAdviceTaskAgg{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ResList = append(p.ResList, _elem)
		}
	}

	return nil
}

func (p *ListSlowQueryAdviceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetAdviceType bool = false
	var issetSummaryId bool = false
	var issetGroupBy bool = false
	var issetOrderBy bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSummaryId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetGroupBy = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAdviceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSummaryId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetGroupBy {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceReq[fieldId]))
}

func (p *ListSlowQueryAdviceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field AdviceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = AdviceType(v)
	}
	p.AdviceType = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SummaryId = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field ListSlowQueryAdviceGroupBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = ListSlowQueryAdviceGroupBy(v)
	}
	p.GroupBy = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field ListSlowQueryAdviceOrderBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = ListSlowQueryAdviceOrderBy(v)
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]AdviceLevel, 0, size)
	for i := 0; i < size; i++ {
		var _elem AdviceLevel
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = AdviceLevel(v)
		}

		_field = append(_field, _elem)
	}
	p.AdviceLevel = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ListSlowQueryAdviceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ListSlowQueryAdviceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ListSlowQueryAdviceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.AdviceType))
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SummaryId)
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.GroupBy))
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.OrderBy))
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageNumber)
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageSize)
	return offset
}

func (p *ListSlowQueryAdviceReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAdviceLevel() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 10)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.AdviceLevel {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *ListSlowQueryAdviceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *ListSlowQueryAdviceReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ListSlowQueryAdviceReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SummaryId)
	return l
}

func (p *ListSlowQueryAdviceReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceReq) field10Length() int {
	l := 0
	if p.IsSetAdviceLevel() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.AdviceLevel {
			_ = v
			l += thrift.Binary.I32Length()
		}
	}
	return l
}

func (p *ListSlowQueryAdviceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ListSlowQueryAdviceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InstanceType = src.InstanceType

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.AdviceType = src.AdviceType

	if src.SummaryId != "" {
		p.SummaryId = kutils.StringDeepCopy(src.SummaryId)
	}

	p.GroupBy = src.GroupBy

	p.OrderBy = src.OrderBy

	p.PageNumber = src.PageNumber

	p.PageSize = src.PageSize

	if src.AdviceLevel != nil {
		p.AdviceLevel = make([]AdviceLevel, 0, len(src.AdviceLevel))
		for _, elem := range src.AdviceLevel {
			var _elem AdviceLevel
			_elem = elem
			p.AdviceLevel = append(p.AdviceLevel, _elem)
		}
	}

	return nil
}

func (p *ListSlowQueryAdviceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListSlowQueryAdviceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ListSlowQueryAdviceResp[fieldId]))
}

func (p *ListSlowQueryAdviceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*AdviceByGroup, 0, size)
	values := make([]AdviceByGroup, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.AdvicesByGroup = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Advice, 0, size)
	values := make([]Advice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Advices = _field
	return offset, nil
}

func (p *ListSlowQueryAdviceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ListSlowQueryAdviceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ListSlowQueryAdviceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ListSlowQueryAdviceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *ListSlowQueryAdviceResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAdvicesByGroup() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.AdvicesByGroup {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ListSlowQueryAdviceResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAdvices() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Advices {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ListSlowQueryAdviceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ListSlowQueryAdviceResp) field2Length() int {
	l := 0
	if p.IsSetAdvicesByGroup() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.AdvicesByGroup {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ListSlowQueryAdviceResp) field3Length() int {
	l := 0
	if p.IsSetAdvices() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Advices {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ListSlowQueryAdviceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ListSlowQueryAdviceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.AdvicesByGroup != nil {
		p.AdvicesByGroup = make([]*AdviceByGroup, 0, len(src.AdvicesByGroup))
		for _, elem := range src.AdvicesByGroup {
			var _elem *AdviceByGroup
			if elem != nil {
				_elem = &AdviceByGroup{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.AdvicesByGroup = append(p.AdvicesByGroup, _elem)
		}
	}

	if src.Advices != nil {
		p.Advices = make([]*Advice, 0, len(src.Advices))
		for _, elem := range src.Advices {
			var _elem *Advice
			if elem != nil {
				_elem = &Advice{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Advices = append(p.Advices, _elem)
		}
	}

	return nil
}

func (p *AdviceByGroup) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAdvice bool = false
	var issetAdvices bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdvice = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdvices = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetAdvice {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAdvices {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviceByGroup[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_AdviceByGroup[fieldId]))
}

func (p *AdviceByGroup) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Advice = _field
	return offset, nil
}

func (p *AdviceByGroup) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Advice, 0, size)
	values := make([]Advice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Advices = _field
	return offset, nil
}

func (p *AdviceByGroup) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AdviceByGroup) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AdviceByGroup) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AdviceByGroup) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Advice)
	return offset
}

func (p *AdviceByGroup) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Advices {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *AdviceByGroup) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Advice)
	return l
}

func (p *AdviceByGroup) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Advices {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *AdviceByGroup) DeepCopy(s interface{}) error {
	src, ok := s.(*AdviceByGroup)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Advice != "" {
		p.Advice = kutils.StringDeepCopy(src.Advice)
	}

	if src.Advices != nil {
		p.Advices = make([]*Advice, 0, len(src.Advices))
		for _, elem := range src.Advices {
			var _elem *Advice
			if elem != nil {
				_elem = &Advice{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Advices = append(p.Advices, _elem)
		}
	}

	return nil
}

func (p *Advice) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLModule bool = false
	var issetAdvice bool = false
	var issetSpeedUp bool = false
	var issetDB bool = false
	var issetTableName bool = false
	var issetSourceIPs bool = false
	var issetUser bool = false
	var issetQueryTimeAvgAfter bool = false
	var issetAdviceIndexSize bool = false
	var issetBenefit bool = false
	var issetAdviceLevel bool = false
	var issetRisk bool = false
	var issetAgg bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSQLModule = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdvice = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSpeedUp = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSourceIPs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUser = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetQueryTimeAvgAfter = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBenefit = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceLevel = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRisk = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAgg = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSQLModule {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAdvice {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSpeedUp {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSourceIPs {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUser {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetQueryTimeAvgAfter {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexSize {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetBenefit {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetAdviceLevel {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetRisk {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetAgg {
		fieldId = 18
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Advice[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_Advice[fieldId]))
}

func (p *Advice) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SQLModule = _field
	return offset, nil
}

func (p *Advice) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Advice = _field
	return offset, nil
}

func (p *Advice) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SpeedUp = _field
	return offset, nil
}

func (p *Advice) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DB = _field
	return offset, nil
}

func (p *Advice) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TableName = _field
	return offset, nil
}

func (p *Advice) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.SourceIPs = _field
	return offset, nil
}

func (p *Advice) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.User = _field
	return offset, nil
}

func (p *Advice) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.QueryTimeAvgAfter = _field
	return offset, nil
}

func (p *Advice) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AdviceIndexSize = _field
	return offset, nil
}

func (p *Advice) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Benefit = _field
	return offset, nil
}

func (p *Advice) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field AdviceLevel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = AdviceLevel(v)
	}
	p.AdviceLevel = _field
	return offset, nil
}

func (p *Advice) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Risk = _field
	return offset, nil
}

func (p *Advice) FastReadField18(buf []byte) (int, error) {
	offset := 0
	_field := NewAggregateSlowLog()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Agg = _field
	return offset, nil
}

func (p *Advice) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Advice) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Advice) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field17Length()
		l += p.field18Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Advice) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SQLModule)
	return offset
}

func (p *Advice) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Advice)
	return offset
}

func (p *Advice) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 3)
	offset += thrift.Binary.WriteDouble(buf[offset:], p.SpeedUp)
	return offset
}

func (p *Advice) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DB)
	return offset
}

func (p *Advice) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TableName)
	return offset
}

func (p *Advice) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 6)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SourceIPs {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *Advice) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 7)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.User {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *Advice) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 12)
	offset += thrift.Binary.WriteDouble(buf[offset:], p.QueryTimeAvgAfter)
	return offset
}

func (p *Advice) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 13)
	offset += thrift.Binary.WriteDouble(buf[offset:], p.AdviceIndexSize)
	return offset
}

func (p *Advice) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 14)
	offset += thrift.Binary.WriteDouble(buf[offset:], p.Benefit)
	return offset
}

func (p *Advice) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.AdviceLevel))
	return offset
}

func (p *Advice) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Risk)
	return offset
}

func (p *Advice) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 18)
	offset += p.Agg.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *Advice) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SQLModule)
	return l
}

func (p *Advice) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Advice)
	return l
}

func (p *Advice) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.DoubleLength()
	return l
}

func (p *Advice) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DB)
	return l
}

func (p *Advice) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TableName)
	return l
}

func (p *Advice) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SourceIPs {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *Advice) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.User {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *Advice) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.DoubleLength()
	return l
}

func (p *Advice) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.DoubleLength()
	return l
}

func (p *Advice) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.DoubleLength()
	return l
}

func (p *Advice) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Advice) field17Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Risk)
	return l
}

func (p *Advice) field18Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Agg.BLength()
	return l
}

func (p *Advice) DeepCopy(s interface{}) error {
	src, ok := s.(*Advice)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SQLModule != "" {
		p.SQLModule = kutils.StringDeepCopy(src.SQLModule)
	}

	if src.Advice != "" {
		p.Advice = kutils.StringDeepCopy(src.Advice)
	}

	p.SpeedUp = src.SpeedUp

	if src.DB != "" {
		p.DB = kutils.StringDeepCopy(src.DB)
	}

	if src.TableName != "" {
		p.TableName = kutils.StringDeepCopy(src.TableName)
	}

	if src.SourceIPs != nil {
		p.SourceIPs = make([]string, 0, len(src.SourceIPs))
		for _, elem := range src.SourceIPs {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.SourceIPs = append(p.SourceIPs, _elem)
		}
	}

	if src.User != nil {
		p.User = make([]string, 0, len(src.User))
		for _, elem := range src.User {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.User = append(p.User, _elem)
		}
	}

	p.QueryTimeAvgAfter = src.QueryTimeAvgAfter

	p.AdviceIndexSize = src.AdviceIndexSize

	p.Benefit = src.Benefit

	p.AdviceLevel = src.AdviceLevel

	if src.Risk != "" {
		p.Risk = kutils.StringDeepCopy(src.Risk)
	}

	var _agg *AggregateSlowLog
	if src.Agg != nil {
		_agg = &AggregateSlowLog{}
		if err := _agg.DeepCopy(src.Agg); err != nil {
			return err
		}
	}
	p.Agg = _agg

	return nil
}

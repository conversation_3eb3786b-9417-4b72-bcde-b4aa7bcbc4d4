// Code generated by Kitex v1.18.1. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *TrxQueryFilter) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxQueryFilter[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TrxQueryFilter) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxExecTime = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxStatus = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *Lockstatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.LockStatus = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ProcessId = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxId = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SqlBlocked = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxIsoLevel = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BlockTrxId = _field
	return offset, nil
}

func (p *TrxQueryFilter) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TrxQueryFilter) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TrxQueryFilter) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TrxQueryFilter) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxExecTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.TrxExecTime)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxStatus() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TrxStatus)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLockStatus() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.LockStatus))
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProcessId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ProcessId)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TrxId)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlBlocked() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SqlBlocked)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxIsoLevel() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TrxIsoLevel)
	}
	return offset
}

func (p *TrxQueryFilter) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBlockTrxId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BlockTrxId)
	}
	return offset
}

func (p *TrxQueryFilter) field1Length() int {
	l := 0
	if p.IsSetTrxExecTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *TrxQueryFilter) field2Length() int {
	l := 0
	if p.IsSetTrxStatus() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TrxStatus)
	}
	return l
}

func (p *TrxQueryFilter) field3Length() int {
	l := 0
	if p.IsSetLockStatus() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *TrxQueryFilter) field4Length() int {
	l := 0
	if p.IsSetProcessId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ProcessId)
	}
	return l
}

func (p *TrxQueryFilter) field5Length() int {
	l := 0
	if p.IsSetTrxId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TrxId)
	}
	return l
}

func (p *TrxQueryFilter) field6Length() int {
	l := 0
	if p.IsSetSqlBlocked() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SqlBlocked)
	}
	return l
}

func (p *TrxQueryFilter) field7Length() int {
	l := 0
	if p.IsSetTrxIsoLevel() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TrxIsoLevel)
	}
	return l
}

func (p *TrxQueryFilter) field8Length() int {
	l := 0
	if p.IsSetBlockTrxId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BlockTrxId)
	}
	return l
}

func (p *TrxQueryFilter) DeepCopy(s interface{}) error {
	src, ok := s.(*TrxQueryFilter)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TrxExecTime != nil {
		tmp := *src.TrxExecTime
		p.TrxExecTime = &tmp
	}

	if src.TrxStatus != nil {
		var tmp string
		if *src.TrxStatus != "" {
			tmp = kutils.StringDeepCopy(*src.TrxStatus)
		}
		p.TrxStatus = &tmp
	}

	if src.LockStatus != nil {
		tmp := *src.LockStatus
		p.LockStatus = &tmp
	}

	if src.ProcessId != nil {
		var tmp string
		if *src.ProcessId != "" {
			tmp = kutils.StringDeepCopy(*src.ProcessId)
		}
		p.ProcessId = &tmp
	}

	if src.TrxId != nil {
		var tmp string
		if *src.TrxId != "" {
			tmp = kutils.StringDeepCopy(*src.TrxId)
		}
		p.TrxId = &tmp
	}

	if src.SqlBlocked != nil {
		var tmp string
		if *src.SqlBlocked != "" {
			tmp = kutils.StringDeepCopy(*src.SqlBlocked)
		}
		p.SqlBlocked = &tmp
	}

	if src.TrxIsoLevel != nil {
		var tmp string
		if *src.TrxIsoLevel != "" {
			tmp = kutils.StringDeepCopy(*src.TrxIsoLevel)
		}
		p.TrxIsoLevel = &tmp
	}

	if src.BlockTrxId != nil {
		var tmp string
		if *src.BlockTrxId != "" {
			tmp = kutils.StringDeepCopy(*src.BlockTrxId)
		}
		p.BlockTrxId = &tmp
	}

	return nil
}

func (p *WaitLockQueryFilter) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLockQueryFilter[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WaitLockQueryFilter) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *Lockstatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.RLockState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxId = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RWaitingQuery = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxStarted = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxWaitStarted = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RBlockedWaitSecs = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxRowsModified = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxRowsLocked = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RTrxOperationState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxId = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *Lockstatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := Lockstatus(v)
		_field = &tmp
	}
	p.BLockState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BBlockingQuery = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxStarted = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxWaitStarted = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BBlockingWaitSecs = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxRowsModified = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxRowsLocked = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BTrxOperationState = _field
	return offset, nil
}

func (p *WaitLockQueryFilter) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WaitLockQueryFilter) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WaitLockQueryFilter) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WaitLockQueryFilter) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RTrxState)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRLockState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.RLockState))
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RTrxId)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRWaitingQuery() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RWaitingQuery)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxStarted() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RTrxStarted)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxWaitStarted() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RTrxWaitStarted)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRBlockedWaitSecs() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RBlockedWaitSecs)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxRowsModified() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.RTrxRowsModified)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxRowsLocked() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.RTrxRowsLocked)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRTrxOperationState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RTrxOperationState)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BTrxId)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BTrxState)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBLockState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 13)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.BLockState))
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBBlockingQuery() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BBlockingQuery)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxStarted() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BTrxStarted)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxWaitStarted() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 16)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BTrxWaitStarted)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBBlockingWaitSecs() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BBlockingWaitSecs)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxRowsModified() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 18)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.BTrxRowsModified)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxRowsLocked() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 19)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.BTrxRowsLocked)
	}
	return offset
}

func (p *WaitLockQueryFilter) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBTrxOperationState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BTrxOperationState)
	}
	return offset
}

func (p *WaitLockQueryFilter) field1Length() int {
	l := 0
	if p.IsSetRTrxState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RTrxState)
	}
	return l
}

func (p *WaitLockQueryFilter) field2Length() int {
	l := 0
	if p.IsSetRLockState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field3Length() int {
	l := 0
	if p.IsSetRTrxId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RTrxId)
	}
	return l
}

func (p *WaitLockQueryFilter) field4Length() int {
	l := 0
	if p.IsSetRWaitingQuery() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RWaitingQuery)
	}
	return l
}

func (p *WaitLockQueryFilter) field5Length() int {
	l := 0
	if p.IsSetRTrxStarted() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RTrxStarted)
	}
	return l
}

func (p *WaitLockQueryFilter) field6Length() int {
	l := 0
	if p.IsSetRTrxWaitStarted() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RTrxWaitStarted)
	}
	return l
}

func (p *WaitLockQueryFilter) field7Length() int {
	l := 0
	if p.IsSetRBlockedWaitSecs() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RBlockedWaitSecs)
	}
	return l
}

func (p *WaitLockQueryFilter) field8Length() int {
	l := 0
	if p.IsSetRTrxRowsModified() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field9Length() int {
	l := 0
	if p.IsSetRTrxRowsLocked() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field10Length() int {
	l := 0
	if p.IsSetRTrxOperationState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RTrxOperationState)
	}
	return l
}

func (p *WaitLockQueryFilter) field11Length() int {
	l := 0
	if p.IsSetBTrxId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BTrxId)
	}
	return l
}

func (p *WaitLockQueryFilter) field12Length() int {
	l := 0
	if p.IsSetBTrxState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BTrxState)
	}
	return l
}

func (p *WaitLockQueryFilter) field13Length() int {
	l := 0
	if p.IsSetBLockState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field14Length() int {
	l := 0
	if p.IsSetBBlockingQuery() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BBlockingQuery)
	}
	return l
}

func (p *WaitLockQueryFilter) field15Length() int {
	l := 0
	if p.IsSetBTrxStarted() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BTrxStarted)
	}
	return l
}

func (p *WaitLockQueryFilter) field16Length() int {
	l := 0
	if p.IsSetBTrxWaitStarted() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BTrxWaitStarted)
	}
	return l
}

func (p *WaitLockQueryFilter) field17Length() int {
	l := 0
	if p.IsSetBBlockingWaitSecs() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BBlockingWaitSecs)
	}
	return l
}

func (p *WaitLockQueryFilter) field18Length() int {
	l := 0
	if p.IsSetBTrxRowsModified() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field19Length() int {
	l := 0
	if p.IsSetBTrxRowsLocked() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *WaitLockQueryFilter) field20Length() int {
	l := 0
	if p.IsSetBTrxOperationState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BTrxOperationState)
	}
	return l
}

func (p *WaitLockQueryFilter) DeepCopy(s interface{}) error {
	src, ok := s.(*WaitLockQueryFilter)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RTrxState != nil {
		var tmp string
		if *src.RTrxState != "" {
			tmp = kutils.StringDeepCopy(*src.RTrxState)
		}
		p.RTrxState = &tmp
	}

	if src.RLockState != nil {
		tmp := *src.RLockState
		p.RLockState = &tmp
	}

	if src.RTrxId != nil {
		var tmp string
		if *src.RTrxId != "" {
			tmp = kutils.StringDeepCopy(*src.RTrxId)
		}
		p.RTrxId = &tmp
	}

	if src.RWaitingQuery != nil {
		var tmp string
		if *src.RWaitingQuery != "" {
			tmp = kutils.StringDeepCopy(*src.RWaitingQuery)
		}
		p.RWaitingQuery = &tmp
	}

	if src.RTrxStarted != nil {
		var tmp string
		if *src.RTrxStarted != "" {
			tmp = kutils.StringDeepCopy(*src.RTrxStarted)
		}
		p.RTrxStarted = &tmp
	}

	if src.RTrxWaitStarted != nil {
		var tmp string
		if *src.RTrxWaitStarted != "" {
			tmp = kutils.StringDeepCopy(*src.RTrxWaitStarted)
		}
		p.RTrxWaitStarted = &tmp
	}

	if src.RBlockedWaitSecs != nil {
		var tmp string
		if *src.RBlockedWaitSecs != "" {
			tmp = kutils.StringDeepCopy(*src.RBlockedWaitSecs)
		}
		p.RBlockedWaitSecs = &tmp
	}

	if src.RTrxRowsModified != nil {
		tmp := *src.RTrxRowsModified
		p.RTrxRowsModified = &tmp
	}

	if src.RTrxRowsLocked != nil {
		tmp := *src.RTrxRowsLocked
		p.RTrxRowsLocked = &tmp
	}

	if src.RTrxOperationState != nil {
		var tmp string
		if *src.RTrxOperationState != "" {
			tmp = kutils.StringDeepCopy(*src.RTrxOperationState)
		}
		p.RTrxOperationState = &tmp
	}

	if src.BTrxId != nil {
		var tmp string
		if *src.BTrxId != "" {
			tmp = kutils.StringDeepCopy(*src.BTrxId)
		}
		p.BTrxId = &tmp
	}

	if src.BTrxState != nil {
		var tmp string
		if *src.BTrxState != "" {
			tmp = kutils.StringDeepCopy(*src.BTrxState)
		}
		p.BTrxState = &tmp
	}

	if src.BLockState != nil {
		tmp := *src.BLockState
		p.BLockState = &tmp
	}

	if src.BBlockingQuery != nil {
		var tmp string
		if *src.BBlockingQuery != "" {
			tmp = kutils.StringDeepCopy(*src.BBlockingQuery)
		}
		p.BBlockingQuery = &tmp
	}

	if src.BTrxStarted != nil {
		var tmp string
		if *src.BTrxStarted != "" {
			tmp = kutils.StringDeepCopy(*src.BTrxStarted)
		}
		p.BTrxStarted = &tmp
	}

	if src.BTrxWaitStarted != nil {
		var tmp string
		if *src.BTrxWaitStarted != "" {
			tmp = kutils.StringDeepCopy(*src.BTrxWaitStarted)
		}
		p.BTrxWaitStarted = &tmp
	}

	if src.BBlockingWaitSecs != nil {
		var tmp string
		if *src.BBlockingWaitSecs != "" {
			tmp = kutils.StringDeepCopy(*src.BBlockingWaitSecs)
		}
		p.BBlockingWaitSecs = &tmp
	}

	if src.BTrxRowsModified != nil {
		tmp := *src.BTrxRowsModified
		p.BTrxRowsModified = &tmp
	}

	if src.BTrxRowsLocked != nil {
		tmp := *src.BTrxRowsLocked
		p.BTrxRowsLocked = &tmp
	}

	if src.BTrxOperationState != nil {
		var tmp string
		if *src.BTrxOperationState != "" {
			tmp = kutils.StringDeepCopy(*src.BTrxOperationState)
		}
		p.BTrxOperationState = &tmp
	}

	return nil
}

func (p *DescribeTrxAndLocksReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDSType bool = false
	var issetInstanceId bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDSType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxAndLocksReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxAndLocksReq[fieldId]))
}

func (p *DescribeTrxAndLocksReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DSType(v)
	}
	p.DSType = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField5(buf []byte) (int, error) {
	offset := 0
	_field := NewTrxQueryFilter()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.QueryFilter = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *SortParam
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortParam(v)
		_field = &tmp
	}
	p.SortParam = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *Order
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := Order(v)
		_field = &tmp
	}
	p.Order = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.NodeIds = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ShardId = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxAndLocksReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxAndLocksReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxAndLocksReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DSType))
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageNumber)
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PageSize)
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryFilter() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 5)
		offset += p.QueryFilter.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortParam))
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrder() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.Order))
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 9)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetShardId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ShardId)
	}
	return offset
}

func (p *DescribeTrxAndLocksReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxAndLocksReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeTrxAndLocksReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxAndLocksReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxAndLocksReq) field5Length() int {
	l := 0
	if p.IsSetQueryFilter() {
		l += thrift.Binary.FieldBeginLength()
		l += p.QueryFilter.BLength()
	}
	return l
}

func (p *DescribeTrxAndLocksReq) field6Length() int {
	l := 0
	if p.IsSetSortParam() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxAndLocksReq) field7Length() int {
	l := 0
	if p.IsSetOrder() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxAndLocksReq) field8Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeTrxAndLocksReq) field9Length() int {
	l := 0
	if p.IsSetNodeIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeTrxAndLocksReq) field10Length() int {
	l := 0
	if p.IsSetShardId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ShardId)
	}
	return l
}

func (p *DescribeTrxAndLocksReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxAndLocksReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.DSType = src.DSType

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.PageNumber = src.PageNumber

	p.PageSize = src.PageSize

	var _queryFilter *TrxQueryFilter
	if src.QueryFilter != nil {
		_queryFilter = &TrxQueryFilter{}
		if err := _queryFilter.DeepCopy(src.QueryFilter); err != nil {
			return err
		}
	}
	p.QueryFilter = _queryFilter

	if src.SortParam != nil {
		tmp := *src.SortParam
		p.SortParam = &tmp
	}

	if src.Order != nil {
		tmp := *src.Order
		p.Order = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	if src.NodeIds != nil {
		p.NodeIds = make([]string, 0, len(src.NodeIds))
		for _, elem := range src.NodeIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.NodeIds = append(p.NodeIds, _elem)
		}
	}

	if src.ShardId != nil {
		var tmp string
		if *src.ShardId != "" {
			tmp = kutils.StringDeepCopy(*src.ShardId)
		}
		p.ShardId = &tmp
	}

	return nil
}

func (p *DescribeTrxAndLocksResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxAndLockList bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxAndLockList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTrxAndLockList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxAndLocksResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxAndLocksResp[fieldId]))
}

func (p *DescribeTrxAndLocksResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TrxAndLock, 0, size)
	values := make([]TrxAndLock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.TrxAndLockList = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeTrxAndLocksResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxAndLocksResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxAndLocksResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxAndLocksResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.TrxAndLockList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeTrxAndLocksResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeTrxAndLocksResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.TrxAndLockList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeTrxAndLocksResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxAndLocksResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxAndLocksResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TrxAndLockList != nil {
		p.TrxAndLockList = make([]*TrxAndLock, 0, len(src.TrxAndLockList))
		for _, elem := range src.TrxAndLockList {
			var _elem *TrxAndLock
			if elem != nil {
				_elem = &TrxAndLock{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.TrxAndLockList = append(p.TrxAndLockList, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *LockStatistics) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInnoDBLockOverview bool = false
	var issetMetaDBLockOverview bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInnoDBLockOverview = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetMetaDBLockOverview = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInnoDBLockOverview {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMetaDBLockOverview {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LockStatistics[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_LockStatistics[fieldId]))
}

func (p *LockStatistics) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewTrxAndLockOverview()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.InnoDBLockOverview = _field
	return offset, nil
}

func (p *LockStatistics) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewTrxAndLockOverview()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.MetaDBLockOverview = _field
	return offset, nil
}

func (p *LockStatistics) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LockStatistics) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LockStatistics) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LockStatistics) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.InnoDBLockOverview.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *LockStatistics) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.MetaDBLockOverview.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *LockStatistics) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.InnoDBLockOverview.BLength()
	return l
}

func (p *LockStatistics) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.MetaDBLockOverview.BLength()
	return l
}

func (p *LockStatistics) DeepCopy(s interface{}) error {
	src, ok := s.(*LockStatistics)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _innoDBLockOverview *TrxAndLockOverview
	if src.InnoDBLockOverview != nil {
		_innoDBLockOverview = &TrxAndLockOverview{}
		if err := _innoDBLockOverview.DeepCopy(src.InnoDBLockOverview); err != nil {
			return err
		}
	}
	p.InnoDBLockOverview = _innoDBLockOverview

	var _metaDBLockOverview *TrxAndLockOverview
	if src.MetaDBLockOverview != nil {
		_metaDBLockOverview = &TrxAndLockOverview{}
		if err := _metaDBLockOverview.DeepCopy(src.MetaDBLockOverview); err != nil {
			return err
		}
	}
	p.MetaDBLockOverview = _metaDBLockOverview

	return nil
}

func (p *WaitLockDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRequestedLockId bool = false
	var issetRequestedTrxId bool = false
	var issetSqlText bool = false
	var issetLockInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRequestedLockId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRequestedTrxId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRequestedLockId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRequestedTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlText {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockInfo {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLockDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_WaitLockDetail[fieldId]))
}

func (p *WaitLockDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RequestedLockId = _field
	return offset, nil
}

func (p *WaitLockDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RequestedTrxId = _field
	return offset, nil
}

func (p *WaitLockDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlText = _field
	return offset, nil
}

func (p *WaitLockDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0
	_field := NewLock()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.LockInfo = _field
	return offset, nil
}

func (p *WaitLockDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WaitLockDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WaitLockDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WaitLockDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RequestedLockId)
	return offset
}

func (p *WaitLockDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RequestedTrxId)
	return offset
}

func (p *WaitLockDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlText)
	return offset
}

func (p *WaitLockDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 4)
	offset += p.LockInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *WaitLockDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RequestedLockId)
	return l
}

func (p *WaitLockDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RequestedTrxId)
	return l
}

func (p *WaitLockDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlText)
	return l
}

func (p *WaitLockDetail) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.LockInfo.BLength()
	return l
}

func (p *WaitLockDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*WaitLockDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RequestedLockId != "" {
		p.RequestedLockId = kutils.StringDeepCopy(src.RequestedLockId)
	}

	if src.RequestedTrxId != "" {
		p.RequestedTrxId = kutils.StringDeepCopy(src.RequestedTrxId)
	}

	if src.SqlText != "" {
		p.SqlText = kutils.StringDeepCopy(src.SqlText)
	}

	var _lockInfo *Lock
	if src.LockInfo != nil {
		_lockInfo = &Lock{}
		if err := _lockInfo.DeepCopy(src.LockInfo); err != nil {
			return err
		}
	}
	p.LockInfo = _lockInfo

	return nil
}

func (p *TrxAndLockOverview) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotalHoldLock bool = false
	var issetTotalWaitLock bool = false
	var issetTimeGreaterThanCount bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotalHoldLock = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotalWaitLock = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTimeGreaterThanCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotalHoldLock {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotalWaitLock {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTimeGreaterThanCount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxAndLockOverview[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_TrxAndLockOverview[fieldId]))
}

func (p *TrxAndLockOverview) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalHoldLock = _field
	return offset, nil
}

func (p *TrxAndLockOverview) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalWaitLock = _field
	return offset, nil
}

func (p *TrxAndLockOverview) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TimeGreaterThanCount = _field
	return offset, nil
}

func (p *TrxAndLockOverview) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TrxAndLockOverview) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TrxAndLockOverview) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TrxAndLockOverview) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TotalHoldLock)
	return offset
}

func (p *TrxAndLockOverview) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TotalWaitLock)
	return offset
}

func (p *TrxAndLockOverview) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TimeGreaterThanCount)
	return offset
}

func (p *TrxAndLockOverview) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLockOverview) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLockOverview) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLockOverview) DeepCopy(s interface{}) error {
	src, ok := s.(*TrxAndLockOverview)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.TotalHoldLock = src.TotalHoldLock

	p.TotalWaitLock = src.TotalWaitLock

	p.TimeGreaterThanCount = src.TimeGreaterThanCount

	return nil
}

func (p *TrxAndLock) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProcessId bool = false
	var issetTrxId bool = false
	var issetTrxstatus bool = false
	var issetLockstatus bool = false
	var issetTrxIsoLevel bool = false
	var issetTrxStartTime bool = false
	var issetTrxTablesLocked bool = false
	var issetTrxRowsLocked bool = false
	var issetTrxRowsModified bool = false
	var issetLockList bool = false
	var issetTrxExecTime bool = false
	var issetSnapshotTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxstatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockstatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxIsoLevel = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxStartTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxTablesLocked = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxRowsLocked = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxRowsModified = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxExecTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetProcessId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTrxstatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockstatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTrxIsoLevel {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTrxStartTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTrxTablesLocked {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetTrxRowsLocked {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetTrxRowsModified {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetLockList {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetTrxExecTime {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetSnapshotTime {
		fieldId = 18
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxAndLock[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_TrxAndLock[fieldId]))
}

func (p *TrxAndLock) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProcessId = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxId = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Trxstatus = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field Lockstatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Lockstatus(v)
	}
	p.Lockstatus = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxIsoLevel = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxStartTime = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxWaitStartTime = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SqlBlocked = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxTablesLocked = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxRowsLocked = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxRowsModified = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Lock, 0, size)
	values := make([]Lock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.LockList = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeType = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxExecTime = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField16(buf []byte) (int, error) {
	offset := 0
	_field := NewWaitLockDetail()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.WaitLockDetail = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BlockTrxId = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SnapshotTime = _field
	return offset, nil
}

func (p *TrxAndLock) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TrxChangeTime = _field
	return offset, nil
}

func (p *TrxAndLock) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TrxAndLock) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TrxAndLock) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TrxAndLock) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ProcessId)
	return offset
}

func (p *TrxAndLock) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxId)
	return offset
}

func (p *TrxAndLock) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Trxstatus)
	return offset
}

func (p *TrxAndLock) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Lockstatus))
	return offset
}

func (p *TrxAndLock) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxIsoLevel)
	return offset
}

func (p *TrxAndLock) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxStartTime)
	return offset
}

func (p *TrxAndLock) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxWaitStartTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TrxWaitStartTime)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlBlocked() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SqlBlocked)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TrxTablesLocked)
	return offset
}

func (p *TrxAndLock) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TrxRowsLocked)
	return offset
}

func (p *TrxAndLock) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TrxRowsModified)
	return offset
}

func (p *TrxAndLock) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 12)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.LockList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *TrxAndLock) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeId)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeType)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
	offset += thrift.Binary.WriteI32(buf[offset:], p.TrxExecTime)
	return offset
}

func (p *TrxAndLock) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetWaitLockDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 16)
		offset += p.WaitLockDetail.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBlockTrxId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BlockTrxId)
	}
	return offset
}

func (p *TrxAndLock) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 18)
	offset += thrift.Binary.WriteI64(buf[offset:], p.SnapshotTime)
	return offset
}

func (p *TrxAndLock) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTrxChangeTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 19)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TrxChangeTime)
	}
	return offset
}

func (p *TrxAndLock) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ProcessId)
	return l
}

func (p *TrxAndLock) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxId)
	return l
}

func (p *TrxAndLock) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Trxstatus)
	return l
}

func (p *TrxAndLock) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLock) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxIsoLevel)
	return l
}

func (p *TrxAndLock) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxStartTime)
	return l
}

func (p *TrxAndLock) field7Length() int {
	l := 0
	if p.IsSetTrxWaitStartTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TrxWaitStartTime)
	}
	return l
}

func (p *TrxAndLock) field8Length() int {
	l := 0
	if p.IsSetSqlBlocked() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SqlBlocked)
	}
	return l
}

func (p *TrxAndLock) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLock) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLock) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLock) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.LockList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *TrxAndLock) field13Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeId)
	}
	return l
}

func (p *TrxAndLock) field14Length() int {
	l := 0
	if p.IsSetNodeType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeType)
	}
	return l
}

func (p *TrxAndLock) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TrxAndLock) field16Length() int {
	l := 0
	if p.IsSetWaitLockDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += p.WaitLockDetail.BLength()
	}
	return l
}

func (p *TrxAndLock) field17Length() int {
	l := 0
	if p.IsSetBlockTrxId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BlockTrxId)
	}
	return l
}

func (p *TrxAndLock) field18Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *TrxAndLock) field19Length() int {
	l := 0
	if p.IsSetTrxChangeTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TrxChangeTime)
	}
	return l
}

func (p *TrxAndLock) DeepCopy(s interface{}) error {
	src, ok := s.(*TrxAndLock)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ProcessId != "" {
		p.ProcessId = kutils.StringDeepCopy(src.ProcessId)
	}

	if src.TrxId != "" {
		p.TrxId = kutils.StringDeepCopy(src.TrxId)
	}

	if src.Trxstatus != "" {
		p.Trxstatus = kutils.StringDeepCopy(src.Trxstatus)
	}

	p.Lockstatus = src.Lockstatus

	if src.TrxIsoLevel != "" {
		p.TrxIsoLevel = kutils.StringDeepCopy(src.TrxIsoLevel)
	}

	if src.TrxStartTime != "" {
		p.TrxStartTime = kutils.StringDeepCopy(src.TrxStartTime)
	}

	if src.TrxWaitStartTime != nil {
		var tmp string
		if *src.TrxWaitStartTime != "" {
			tmp = kutils.StringDeepCopy(*src.TrxWaitStartTime)
		}
		p.TrxWaitStartTime = &tmp
	}

	if src.SqlBlocked != nil {
		var tmp string
		if *src.SqlBlocked != "" {
			tmp = kutils.StringDeepCopy(*src.SqlBlocked)
		}
		p.SqlBlocked = &tmp
	}

	p.TrxTablesLocked = src.TrxTablesLocked

	p.TrxRowsLocked = src.TrxRowsLocked

	p.TrxRowsModified = src.TrxRowsModified

	if src.LockList != nil {
		p.LockList = make([]*Lock, 0, len(src.LockList))
		for _, elem := range src.LockList {
			var _elem *Lock
			if elem != nil {
				_elem = &Lock{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.LockList = append(p.LockList, _elem)
		}
	}

	if src.NodeId != nil {
		var tmp string
		if *src.NodeId != "" {
			tmp = kutils.StringDeepCopy(*src.NodeId)
		}
		p.NodeId = &tmp
	}

	if src.NodeType != nil {
		var tmp string
		if *src.NodeType != "" {
			tmp = kutils.StringDeepCopy(*src.NodeType)
		}
		p.NodeType = &tmp
	}

	p.TrxExecTime = src.TrxExecTime

	var _waitLockDetail *WaitLockDetail
	if src.WaitLockDetail != nil {
		_waitLockDetail = &WaitLockDetail{}
		if err := _waitLockDetail.DeepCopy(src.WaitLockDetail); err != nil {
			return err
		}
	}
	p.WaitLockDetail = _waitLockDetail

	if src.BlockTrxId != nil {
		var tmp string
		if *src.BlockTrxId != "" {
			tmp = kutils.StringDeepCopy(*src.BlockTrxId)
		}
		p.BlockTrxId = &tmp
	}

	p.SnapshotTime = src.SnapshotTime

	if src.TrxChangeTime != nil {
		var tmp string
		if *src.TrxChangeTime != "" {
			tmp = kutils.StringDeepCopy(*src.TrxChangeTime)
		}
		p.TrxChangeTime = &tmp
	}

	return nil
}

func (p *Lock) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLockProperty bool = false
	var issetLockId bool = false
	var issetLockAssociateIndex bool = false
	var issetLockAssociateTable bool = false
	var issetLockType bool = false
	var issetLockModel bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockProperty = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockAssociateIndex = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockAssociateTable = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockModel = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetLockProperty {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLockId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateIndex {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLockType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLockModel {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Lock[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_Lock[fieldId]))
}

func (p *Lock) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockProperty = _field
	return offset, nil
}

func (p *Lock) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockId = _field
	return offset, nil
}

func (p *Lock) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockAssociateIndex = _field
	return offset, nil
}

func (p *Lock) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockAssociateTable = _field
	return offset, nil
}

func (p *Lock) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockType = _field
	return offset, nil
}

func (p *Lock) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockModel = _field
	return offset, nil
}

func (p *Lock) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Lock) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Lock) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Lock) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockProperty)
	return offset
}

func (p *Lock) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockId)
	return offset
}

func (p *Lock) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockAssociateIndex)
	return offset
}

func (p *Lock) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockAssociateTable)
	return offset
}

func (p *Lock) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockType)
	return offset
}

func (p *Lock) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockModel)
	return offset
}

func (p *Lock) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockProperty)
	return l
}

func (p *Lock) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockId)
	return l
}

func (p *Lock) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockAssociateIndex)
	return l
}

func (p *Lock) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockAssociateTable)
	return l
}

func (p *Lock) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockType)
	return l
}

func (p *Lock) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockModel)
	return l
}

func (p *Lock) DeepCopy(s interface{}) error {
	src, ok := s.(*Lock)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.LockProperty != "" {
		p.LockProperty = kutils.StringDeepCopy(src.LockProperty)
	}

	if src.LockId != "" {
		p.LockId = kutils.StringDeepCopy(src.LockId)
	}

	if src.LockAssociateIndex != "" {
		p.LockAssociateIndex = kutils.StringDeepCopy(src.LockAssociateIndex)
	}

	if src.LockAssociateTable != "" {
		p.LockAssociateTable = kutils.StringDeepCopy(src.LockAssociateTable)
	}

	if src.LockType != "" {
		p.LockType = kutils.StringDeepCopy(src.LockType)
	}

	if src.LockModel != "" {
		p.LockModel = kutils.StringDeepCopy(src.LockModel)
	}

	return nil
}

func (p *WaitLock) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProcessId bool = false
	var issetTrxId bool = false
	var issetSqlBlocked bool = false
	var issetLockAssociateTable bool = false
	var issetLockType bool = false
	var issetLockModel bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlBlocked = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockAssociateTable = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLockModel = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetProcessId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTrxId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlBlocked {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLockAssociateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLockType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLockModel {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WaitLock[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_WaitLock[fieldId]))
}

func (p *WaitLock) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProcessId = _field
	return offset, nil
}

func (p *WaitLock) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxId = _field
	return offset, nil
}

func (p *WaitLock) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlBlocked = _field
	return offset, nil
}

func (p *WaitLock) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockAssociateTable = _field
	return offset, nil
}

func (p *WaitLock) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockType = _field
	return offset, nil
}

func (p *WaitLock) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LockModel = _field
	return offset, nil
}

func (p *WaitLock) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WaitLock) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WaitLock) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WaitLock) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ProcessId)
	return offset
}

func (p *WaitLock) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxId)
	return offset
}

func (p *WaitLock) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlBlocked)
	return offset
}

func (p *WaitLock) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockAssociateTable)
	return offset
}

func (p *WaitLock) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockType)
	return offset
}

func (p *WaitLock) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LockModel)
	return offset
}

func (p *WaitLock) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ProcessId)
	return l
}

func (p *WaitLock) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxId)
	return l
}

func (p *WaitLock) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlBlocked)
	return l
}

func (p *WaitLock) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockAssociateTable)
	return l
}

func (p *WaitLock) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockType)
	return l
}

func (p *WaitLock) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LockModel)
	return l
}

func (p *WaitLock) DeepCopy(s interface{}) error {
	src, ok := s.(*WaitLock)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ProcessId != "" {
		p.ProcessId = kutils.StringDeepCopy(src.ProcessId)
	}

	if src.TrxId != "" {
		p.TrxId = kutils.StringDeepCopy(src.TrxId)
	}

	if src.SqlBlocked != "" {
		p.SqlBlocked = kutils.StringDeepCopy(src.SqlBlocked)
	}

	if src.LockAssociateTable != "" {
		p.LockAssociateTable = kutils.StringDeepCopy(src.LockAssociateTable)
	}

	if src.LockType != "" {
		p.LockType = kutils.StringDeepCopy(src.LockType)
	}

	if src.LockModel != "" {
		p.LockModel = kutils.StringDeepCopy(src.LockModel)
	}

	return nil
}

func (p *DescribeDeadlockReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDSType bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeDeadlockReq[fieldId]))
}

func (p *DescribeDeadlockReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeDeadlockReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DSType(v)
	}
	p.DSType = _field
	return offset, nil
}

func (p *DescribeDeadlockReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeDeadlockReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DescribeDeadlockReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeadlockReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeadlockReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeadlockReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeDeadlockReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DSType))
	return offset
}

func (p *DescribeDeadlockReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeDeadlockReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeId {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeDeadlockReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeDeadlockReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeDeadlockReq) field3Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeDeadlockReq) field4Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeId {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeDeadlockReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeadlockReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.DSType = src.DSType

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	if src.NodeId != nil {
		p.NodeId = make([]string, 0, len(src.NodeId))
		for _, elem := range src.NodeId {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.NodeId = append(p.NodeId, _elem)
		}
	}

	return nil
}

func (p *DescribeDeadlockResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDeadlockInfoList bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDeadlockInfoList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDeadlockInfoList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeDeadlockResp[fieldId]))
}

func (p *DescribeDeadlockResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DeadlockInfo, 0, size)
	values := make([]DeadlockInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DeadlockInfoList = _field
	return offset, nil
}

func (p *DescribeDeadlockResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeadlockResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeadlockResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeadlockResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DeadlockInfoList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeDeadlockResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DeadlockInfoList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeDeadlockResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeadlockResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DeadlockInfoList != nil {
		p.DeadlockInfoList = make([]*DeadlockInfo, 0, len(src.DeadlockInfoList))
		for _, elem := range src.DeadlockInfoList {
			var _elem *DeadlockInfo
			if elem != nil {
				_elem = &DeadlockInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DeadlockInfoList = append(p.DeadlockInfoList, _elem)
		}
	}

	return nil
}

func (p *DeadlockInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDeadlockCollectionTime bool = false
	var issetDeadlockTime bool = false
	var issetDeadlockList bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDeadlockCollectionTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDeadlockTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDeadlockList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDeadlockCollectionTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDeadlockTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDeadlockList {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeadlockInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeadlockInfo[fieldId]))
}

func (p *DeadlockInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DeadlockCollectionTime = _field
	return offset, nil
}

func (p *DeadlockInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DeadlockTime = _field
	return offset, nil
}

func (p *DeadlockInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Deadlock, 0, size)
	values := make([]Deadlock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DeadlockList = _field
	return offset, nil
}

func (p *DeadlockInfo) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DeadlockInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeadlockInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeadlockInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeadlockInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DeadlockCollectionTime)
	return offset
}

func (p *DeadlockInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DeadlockTime)
	return offset
}

func (p *DeadlockInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DeadlockList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DeadlockInfo) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeId)
	}
	return offset
}

func (p *DeadlockInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DeadlockCollectionTime)
	return l
}

func (p *DeadlockInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DeadlockTime)
	return l
}

func (p *DeadlockInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DeadlockList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DeadlockInfo) field4Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeId)
	}
	return l
}

func (p *DeadlockInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*DeadlockInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DeadlockCollectionTime != "" {
		p.DeadlockCollectionTime = kutils.StringDeepCopy(src.DeadlockCollectionTime)
	}

	if src.DeadlockTime != "" {
		p.DeadlockTime = kutils.StringDeepCopy(src.DeadlockTime)
	}

	if src.DeadlockList != nil {
		p.DeadlockList = make([]*Deadlock, 0, len(src.DeadlockList))
		for _, elem := range src.DeadlockList {
			var _elem *Deadlock
			if elem != nil {
				_elem = &Deadlock{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DeadlockList = append(p.DeadlockList, _elem)
		}
	}

	if src.NodeId != nil {
		var tmp string
		if *src.NodeId != "" {
			tmp = kutils.StringDeepCopy(*src.NodeId)
		}
		p.NodeId = &tmp
	}

	return nil
}

func (p *Deadlock) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxInfo bool = false
	var issetProcessId bool = false
	var issetReqType bool = false
	var issetRelateTable bool = false
	var issetWaitLock bool = false
	var issetWaitIndex bool = false
	var issetWaitLockMode bool = false
	var issetHoldLock bool = false
	var issetHoldLockIndex bool = false
	var issetHoldLockMode bool = false
	var issetSql bool = false
	var issetTrxTreat bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProcessId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReqType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRelateTable = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetWaitLock = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetWaitIndex = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetWaitLockMode = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetHoldLock = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetHoldLockIndex = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetHoldLockMode = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSql = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxTreat = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTrxInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProcessId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReqType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRelateTable {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetWaitLock {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetWaitIndex {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetWaitLockMode {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetHoldLock {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetHoldLockIndex {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetHoldLockMode {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetSql {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetTrxTreat {
		fieldId = 12
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Deadlock[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_Deadlock[fieldId]))
}

func (p *Deadlock) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxInfo = _field
	return offset, nil
}

func (p *Deadlock) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProcessId = _field
	return offset, nil
}

func (p *Deadlock) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReqType = _field
	return offset, nil
}

func (p *Deadlock) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RelateTable = _field
	return offset, nil
}

func (p *Deadlock) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WaitLock = _field
	return offset, nil
}

func (p *Deadlock) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WaitIndex = _field
	return offset, nil
}

func (p *Deadlock) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WaitLockMode = _field
	return offset, nil
}

func (p *Deadlock) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HoldLock = _field
	return offset, nil
}

func (p *Deadlock) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HoldLockIndex = _field
	return offset, nil
}

func (p *Deadlock) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HoldLockMode = _field
	return offset, nil
}

func (p *Deadlock) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Sql = _field
	return offset, nil
}

func (p *Deadlock) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TrxTreat = _field
	return offset, nil
}

func (p *Deadlock) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Deadlock) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Deadlock) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Deadlock) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxInfo)
	return offset
}

func (p *Deadlock) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ProcessId)
	return offset
}

func (p *Deadlock) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ReqType)
	return offset
}

func (p *Deadlock) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RelateTable)
	return offset
}

func (p *Deadlock) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WaitLock)
	return offset
}

func (p *Deadlock) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WaitIndex)
	return offset
}

func (p *Deadlock) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WaitLockMode)
	return offset
}

func (p *Deadlock) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.HoldLock)
	return offset
}

func (p *Deadlock) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.HoldLockIndex)
	return offset
}

func (p *Deadlock) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.HoldLockMode)
	return offset
}

func (p *Deadlock) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Sql)
	return offset
}

func (p *Deadlock) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TrxTreat)
	return offset
}

func (p *Deadlock) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxInfo)
	return l
}

func (p *Deadlock) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ProcessId)
	return l
}

func (p *Deadlock) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ReqType)
	return l
}

func (p *Deadlock) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RelateTable)
	return l
}

func (p *Deadlock) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WaitLock)
	return l
}

func (p *Deadlock) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WaitIndex)
	return l
}

func (p *Deadlock) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WaitLockMode)
	return l
}

func (p *Deadlock) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.HoldLock)
	return l
}

func (p *Deadlock) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.HoldLockIndex)
	return l
}

func (p *Deadlock) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.HoldLockMode)
	return l
}

func (p *Deadlock) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Sql)
	return l
}

func (p *Deadlock) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TrxTreat)
	return l
}

func (p *Deadlock) DeepCopy(s interface{}) error {
	src, ok := s.(*Deadlock)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TrxInfo != "" {
		p.TrxInfo = kutils.StringDeepCopy(src.TrxInfo)
	}

	if src.ProcessId != "" {
		p.ProcessId = kutils.StringDeepCopy(src.ProcessId)
	}

	if src.ReqType != "" {
		p.ReqType = kutils.StringDeepCopy(src.ReqType)
	}

	if src.RelateTable != "" {
		p.RelateTable = kutils.StringDeepCopy(src.RelateTable)
	}

	if src.WaitLock != "" {
		p.WaitLock = kutils.StringDeepCopy(src.WaitLock)
	}

	if src.WaitIndex != "" {
		p.WaitIndex = kutils.StringDeepCopy(src.WaitIndex)
	}

	if src.WaitLockMode != "" {
		p.WaitLockMode = kutils.StringDeepCopy(src.WaitLockMode)
	}

	if src.HoldLock != "" {
		p.HoldLock = kutils.StringDeepCopy(src.HoldLock)
	}

	if src.HoldLockIndex != "" {
		p.HoldLockIndex = kutils.StringDeepCopy(src.HoldLockIndex)
	}

	if src.HoldLockMode != "" {
		p.HoldLockMode = kutils.StringDeepCopy(src.HoldLockMode)
	}

	if src.Sql != "" {
		p.Sql = kutils.StringDeepCopy(src.Sql)
	}

	if src.TrxTreat != "" {
		p.TrxTreat = kutils.StringDeepCopy(src.TrxTreat)
	}

	return nil
}

func (p *DescribeDeadlockDetectReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDSType bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockDetectReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeDeadlockDetectReq[fieldId]))
}

func (p *DescribeDeadlockDetectReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeDeadlockDetectReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DSType(v)
	}
	p.DSType = _field
	return offset, nil
}

func (p *DescribeDeadlockDetectReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeDeadlockDetectReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeadlockDetectReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeadlockDetectReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeadlockDetectReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeDeadlockDetectReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DSType))
	return offset
}

func (p *DescribeDeadlockDetectReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeDeadlockDetectReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeDeadlockDetectReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeDeadlockDetectReq) field3Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeDeadlockDetectReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeadlockDetectReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.DSType = src.DSType

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DescribeDeadlockDetectResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOnOff bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOnOff = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOnOff {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDeadlockDetectResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeDeadlockDetectResp[fieldId]))
}

func (p *DescribeDeadlockDetectResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OnOff = _field
	return offset, nil
}

func (p *DescribeDeadlockDetectResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeDeadlockDetectResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeDeadlockDetectResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeDeadlockDetectResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.OnOff)
	return offset
}

func (p *DescribeDeadlockDetectResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *DescribeDeadlockDetectResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeDeadlockDetectResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.OnOff = src.OnOff

	return nil
}

func (p *TrxSnapshot) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSnapshotTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSnapshotTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TrxSnapshot[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_TrxSnapshot[fieldId]))
}

func (p *TrxSnapshot) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SnapshotTime = _field
	return offset, nil
}

func (p *TrxSnapshot) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TrxSnapshot) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TrxSnapshot) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TrxSnapshot) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.SnapshotTime)
	return offset
}

func (p *TrxSnapshot) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *TrxSnapshot) DeepCopy(s interface{}) error {
	src, ok := s.(*TrxSnapshot)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.SnapshotTime = src.SnapshotTime

	return nil
}

func (p *LockWaitSnapshot) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSnapshotTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSnapshotTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LockWaitSnapshot[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_LockWaitSnapshot[fieldId]))
}

func (p *LockWaitSnapshot) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SnapshotTime = _field
	return offset, nil
}

func (p *LockWaitSnapshot) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LockWaitSnapshot) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LockWaitSnapshot) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LockWaitSnapshot) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.SnapshotTime)
	return offset
}

func (p *LockWaitSnapshot) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LockWaitSnapshot) DeepCopy(s interface{}) error {
	src, ok := s.(*LockWaitSnapshot)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.SnapshotTime = src.SnapshotTime

	return nil
}

func (p *DescribeTrxSnapshotsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxSnapshotsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxSnapshotsReq[fieldId]))
}

func (p *DescribeTrxSnapshotsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DSType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StartTime = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EndTime = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxSnapshotsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxSnapshotsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxSnapshotsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.StartTime)
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.EndTime)
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeId)
	}
	return offset
}

func (p *DescribeTrxSnapshotsReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeTrxSnapshotsReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxSnapshotsReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeTrxSnapshotsReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxSnapshotsReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxSnapshotsReq) field5Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxSnapshotsReq) field6Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxSnapshotsReq) field7Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeId)
	}
	return l
}

func (p *DescribeTrxSnapshotsReq) field8Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeTrxSnapshotsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxSnapshotsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InstanceType = src.InstanceType

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.StartTime = src.StartTime

	p.EndTime = src.EndTime

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.NodeId != nil {
		var tmp string
		if *src.NodeId != "" {
			tmp = kutils.StringDeepCopy(*src.NodeId)
		}
		p.NodeId = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DescribeTrxSnapshotsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxSnapshots bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxSnapshots = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTrxSnapshots {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxSnapshotsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxSnapshotsResp[fieldId]))
}

func (p *DescribeTrxSnapshotsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TrxSnapshot, 0, size)
	values := make([]TrxSnapshot, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.TrxSnapshots = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeTrxSnapshotsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxSnapshotsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxSnapshotsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxSnapshotsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.TrxSnapshots {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeTrxSnapshotsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeTrxSnapshotsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.TrxSnapshots {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeTrxSnapshotsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxSnapshotsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxSnapshotsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TrxSnapshots != nil {
		p.TrxSnapshots = make([]*TrxSnapshot, 0, len(src.TrxSnapshots))
		for _, elem := range src.TrxSnapshots {
			var _elem *TrxSnapshot
			if elem != nil {
				_elem = &TrxSnapshot{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.TrxSnapshots = append(p.TrxSnapshots, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeTrxDetailSnapshotReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetSnapshotTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSnapshotTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSnapshotTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxDetailSnapshotReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxDetailSnapshotReq[fieldId]))
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DSType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SnapshotTime = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StartTime = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EndTime = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewTrxQueryFilter()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.QueryFilter = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *SortParam
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *SortBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.NodeIds = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxDetailSnapshotReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.SnapshotTime)
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeId)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStartTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.StartTime)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.EndTime)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryFilter() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
		offset += p.QueryFilter.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderBy))
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortBy))
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 13)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeTrxDetailSnapshotReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field4Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field5Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field6Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeId)
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field7Length() int {
	l := 0
	if p.IsSetStartTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field8Length() int {
	l := 0
	if p.IsSetEndTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field9Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field10Length() int {
	l := 0
	if p.IsSetQueryFilter() {
		l += thrift.Binary.FieldBeginLength()
		l += p.QueryFilter.BLength()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field11Length() int {
	l := 0
	if p.IsSetOrderBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field12Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) field13Length() int {
	l := 0
	if p.IsSetNodeIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeTrxDetailSnapshotReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxDetailSnapshotReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InstanceType = src.InstanceType

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.SnapshotTime = src.SnapshotTime

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.NodeId != nil {
		var tmp string
		if *src.NodeId != "" {
			tmp = kutils.StringDeepCopy(*src.NodeId)
		}
		p.NodeId = &tmp
	}

	if src.StartTime != nil {
		tmp := *src.StartTime
		p.StartTime = &tmp
	}

	if src.EndTime != nil {
		tmp := *src.EndTime
		p.EndTime = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	var _queryFilter *TrxQueryFilter
	if src.QueryFilter != nil {
		_queryFilter = &TrxQueryFilter{}
		if err := _queryFilter.DeepCopy(src.QueryFilter); err != nil {
			return err
		}
	}
	p.QueryFilter = _queryFilter

	if src.OrderBy != nil {
		tmp := *src.OrderBy
		p.OrderBy = &tmp
	}

	if src.SortBy != nil {
		tmp := *src.SortBy
		p.SortBy = &tmp
	}

	if src.NodeIds != nil {
		p.NodeIds = make([]string, 0, len(src.NodeIds))
		for _, elem := range src.NodeIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.NodeIds = append(p.NodeIds, _elem)
		}
	}

	return nil
}

func (p *DescribeTrxDetailSnapshotResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTrxAndLocks bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTrxAndLocks = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTrxAndLocks {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTrxDetailSnapshotResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeTrxDetailSnapshotResp[fieldId]))
}

func (p *DescribeTrxDetailSnapshotResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*TrxAndLock, 0, size)
	values := make([]TrxAndLock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.TrxAndLocks = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeTrxDetailSnapshotResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeTrxDetailSnapshotResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeTrxDetailSnapshotResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeTrxDetailSnapshotResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.TrxAndLocks {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeTrxDetailSnapshotResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeTrxDetailSnapshotResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.TrxAndLocks {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeTrxDetailSnapshotResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeTrxDetailSnapshotResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeTrxDetailSnapshotResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TrxAndLocks != nil {
		p.TrxAndLocks = make([]*TrxAndLock, 0, len(src.TrxAndLocks))
		for _, elem := range src.TrxAndLocks {
			var _elem *TrxAndLock
			if elem != nil {
				_elem = &TrxAndLock{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.TrxAndLocks = append(p.TrxAndLocks, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeLockCurrentWaitsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsReq[fieldId]))
}

func (p *DescribeLockCurrentWaitsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.NodeIds = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0
	_field := NewWaitLockQueryFilter()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.QueryFilter = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *WaitLockSortParam
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := WaitLockSortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *SortBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeLockCurrentWaitsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeLockCurrentWaitsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryFilter() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 4)
		offset += p.QueryFilter.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderBy))
	}
	return offset
}

func (p *DescribeLockCurrentWaitsReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortBy))
	}
	return offset
}

func (p *DescribeLockCurrentWaitsReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeLockCurrentWaitsReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockCurrentWaitsReq) field3Length() int {
	l := 0
	if p.IsSetNodeIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeLockCurrentWaitsReq) field4Length() int {
	l := 0
	if p.IsSetQueryFilter() {
		l += thrift.Binary.FieldBeginLength()
		l += p.QueryFilter.BLength()
	}
	return l
}

func (p *DescribeLockCurrentWaitsReq) field5Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeLockCurrentWaitsReq) field6Length() int {
	l := 0
	if p.IsSetOrderBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockCurrentWaitsReq) field7Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockCurrentWaitsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeLockCurrentWaitsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.InstanceType = src.InstanceType

	if src.NodeIds != nil {
		p.NodeIds = make([]string, 0, len(src.NodeIds))
		for _, elem := range src.NodeIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.NodeIds = append(p.NodeIds, _elem)
		}
	}

	var _queryFilter *WaitLockQueryFilter
	if src.QueryFilter != nil {
		_queryFilter = &WaitLockQueryFilter{}
		if err := _queryFilter.DeepCopy(src.QueryFilter); err != nil {
			return err
		}
	}
	p.QueryFilter = _queryFilter

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	if src.OrderBy != nil {
		tmp := *src.OrderBy
		p.OrderBy = &tmp
	}

	if src.SortBy != nil {
		tmp := *src.SortBy
		p.SortBy = &tmp
	}

	return nil
}

func (p *DescribeLockCurrentWaitsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDetailList bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDetailList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsResp[fieldId]))
}

func (p *DescribeLockCurrentWaitsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DescribeLockCurrentWaitsDetail, 0, size)
	values := make([]DescribeLockCurrentWaitsDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DetailList = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeLockCurrentWaitsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeLockCurrentWaitsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeLockCurrentWaitsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DetailList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeLockCurrentWaitsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeLockCurrentWaitsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DetailList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeLockCurrentWaitsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockCurrentWaitsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeLockCurrentWaitsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DetailList != nil {
		p.DetailList = make([]*DescribeLockCurrentWaitsDetail, 0, len(src.DetailList))
		for _, elem := range src.DetailList {
			var _elem *DescribeLockCurrentWaitsDetail
			if elem != nil {
				_elem = &DescribeLockCurrentWaitsDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DetailList = append(p.DetailList, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockWaitsDetailSnapshotReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeLockWaitsDetailSnapshotReq[fieldId]))
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StartTime = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EndTime = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField8(buf []byte) (int, error) {
	offset := 0
	_field := NewWaitLockQueryFilter()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.QueryFilter = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *WaitLockSortParam
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := WaitLockSortParam(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *SortBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.NodeIds = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeLockWaitsDetailSnapshotReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.StartTime)
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.EndTime)
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.NodeId)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetQueryFilter() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 8)
		offset += p.QueryFilter.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderBy))
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortBy))
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNodeIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 12)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.NodeIds {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field5Length() int {
	l := 0
	if p.IsSetNodeId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.NodeId)
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field6Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field7Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field8Length() int {
	l := 0
	if p.IsSetQueryFilter() {
		l += thrift.Binary.FieldBeginLength()
		l += p.QueryFilter.BLength()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field9Length() int {
	l := 0
	if p.IsSetOrderBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field10Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field11Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) field12Length() int {
	l := 0
	if p.IsSetNodeIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.NodeIds {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeLockWaitsDetailSnapshotReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	p.InstanceType = src.InstanceType

	p.StartTime = src.StartTime

	p.EndTime = src.EndTime

	if src.NodeId != nil {
		var tmp string
		if *src.NodeId != "" {
			tmp = kutils.StringDeepCopy(*src.NodeId)
		}
		p.NodeId = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	var _queryFilter *WaitLockQueryFilter
	if src.QueryFilter != nil {
		_queryFilter = &WaitLockQueryFilter{}
		if err := _queryFilter.DeepCopy(src.QueryFilter); err != nil {
			return err
		}
	}
	p.QueryFilter = _queryFilter

	if src.OrderBy != nil {
		tmp := *src.OrderBy
		p.OrderBy = &tmp
	}

	if src.SortBy != nil {
		tmp := *src.SortBy
		p.SortBy = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	if src.NodeIds != nil {
		p.NodeIds = make([]string, 0, len(src.NodeIds))
		for _, elem := range src.NodeIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.NodeIds = append(p.NodeIds, _elem)
		}
	}

	return nil
}

func (p *DescribeLockWaitsDetailSnapshotResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDetailList bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDetailList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockWaitsDetailSnapshotResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeLockWaitsDetailSnapshotResp[fieldId]))
}

func (p *DescribeLockWaitsDetailSnapshotResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*DescribeLockCurrentWaitsDetail, 0, size)
	values := make([]DescribeLockCurrentWaitsDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DetailList = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeLockWaitsDetailSnapshotResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeLockWaitsDetailSnapshotResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DetailList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeLockWaitsDetailSnapshotResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DetailList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeLockWaitsDetailSnapshotResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeLockWaitsDetailSnapshotResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeLockWaitsDetailSnapshotResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DetailList != nil {
		p.DetailList = make([]*DescribeLockCurrentWaitsDetail, 0, len(src.DetailList))
		for _, elem := range src.DetailList {
			var _elem *DescribeLockCurrentWaitsDetail
			if elem != nil {
				_elem = &DescribeLockCurrentWaitsDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DetailList = append(p.DetailList, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeLockCurrentWaitsDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeId bool = false
	var issetInstanceId bool = false
	var issetCollectTime bool = false
	var issetDbName bool = false
	var issetRTrxMysqlThreadId bool = false
	var issetRTrxId bool = false
	var issetRTrxState bool = false
	var issetRWaitingQuery bool = false
	var issetRTrxStarted bool = false
	var issetRTrxWaitStarted bool = false
	var issetRBlockedWaitSecs bool = false
	var issetRTrxRowsModified bool = false
	var issetRTrxRowsLocked bool = false
	var issetRTrxOperationState bool = false
	var issetBTrxMysqlThreadId bool = false
	var issetBTrxId bool = false
	var issetBTrxState bool = false
	var issetBBlockingQuery bool = false
	var issetBTrxStarted bool = false
	var issetBTrxWaitStarted bool = false
	var issetBBlockingWaitSecs bool = false
	var issetBTrxRowsModified bool = false
	var issetBTrxRowsLocked bool = false
	var issetBTrxOperationState bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCollectTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxMysqlThreadId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxState = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRWaitingQuery = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxStarted = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxWaitStarted = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRBlockedWaitSecs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxRowsModified = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxRowsLocked = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRTrxOperationState = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxMysqlThreadId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxState = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBBlockingQuery = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxStarted = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxWaitStarted = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBBlockingWaitSecs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxRowsModified = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxRowsLocked = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField24(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBTrxOperationState = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetNodeId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCollectTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRTrxMysqlThreadId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetRTrxId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRTrxState {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetRWaitingQuery {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetRTrxStarted {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetRTrxWaitStarted {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetRBlockedWaitSecs {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetRTrxRowsModified {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetRTrxRowsLocked {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetRTrxOperationState {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetBTrxMysqlThreadId {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetBTrxId {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetBTrxState {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetBBlockingQuery {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetBTrxStarted {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetBTrxWaitStarted {
		fieldId = 20
		goto RequiredFieldNotSetError
	}

	if !issetBBlockingWaitSecs {
		fieldId = 21
		goto RequiredFieldNotSetError
	}

	if !issetBTrxRowsModified {
		fieldId = 22
		goto RequiredFieldNotSetError
	}

	if !issetBTrxRowsLocked {
		fieldId = 23
		goto RequiredFieldNotSetError
	}

	if !issetBTrxOperationState {
		fieldId = 24
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLockCurrentWaitsDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeLockCurrentWaitsDetail[fieldId]))
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NodeId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CollectTime = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DbName = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxMysqlThreadId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxState = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RWaitingQuery = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxStarted = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxWaitStarted = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RBlockedWaitSecs = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxRowsModified = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxRowsLocked = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RTrxOperationState = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxMysqlThreadId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxId = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxState = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BBlockingQuery = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxStarted = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxWaitStarted = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BBlockingWaitSecs = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField22(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxRowsModified = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField23(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxRowsLocked = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastReadField24(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BTrxOperationState = _field
	return offset, nil
}

func (p *DescribeLockCurrentWaitsDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeLockCurrentWaitsDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField24(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field24Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.NodeId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CollectTime)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DbName)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxMysqlThreadId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxState)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RWaitingQuery)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxStarted)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxWaitStarted)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RBlockedWaitSecs)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxRowsModified)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxRowsLocked)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RTrxOperationState)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxMysqlThreadId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 16)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxId)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxState)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 18)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BBlockingQuery)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 19)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxStarted)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxWaitStarted)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BBlockingWaitSecs)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 22)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxRowsModified)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 23)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxRowsLocked)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) fastWriteField24(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 24)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BTrxOperationState)
	return offset
}

func (p *DescribeLockCurrentWaitsDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.NodeId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CollectTime)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DbName)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxMysqlThreadId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxState)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RWaitingQuery)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxStarted)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxWaitStarted)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RBlockedWaitSecs)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxRowsModified)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxRowsLocked)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RTrxOperationState)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxMysqlThreadId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxId)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field17Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxState)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field18Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BBlockingQuery)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field19Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxStarted)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxWaitStarted)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BBlockingWaitSecs)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field22Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxRowsModified)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field23Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxRowsLocked)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) field24Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BTrxOperationState)
	return l
}

func (p *DescribeLockCurrentWaitsDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeLockCurrentWaitsDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.NodeId != "" {
		p.NodeId = kutils.StringDeepCopy(src.NodeId)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.CollectTime != "" {
		p.CollectTime = kutils.StringDeepCopy(src.CollectTime)
	}

	if src.DbName != "" {
		p.DbName = kutils.StringDeepCopy(src.DbName)
	}

	if src.RTrxMysqlThreadId != "" {
		p.RTrxMysqlThreadId = kutils.StringDeepCopy(src.RTrxMysqlThreadId)
	}

	if src.RTrxId != "" {
		p.RTrxId = kutils.StringDeepCopy(src.RTrxId)
	}

	if src.RTrxState != "" {
		p.RTrxState = kutils.StringDeepCopy(src.RTrxState)
	}

	if src.RWaitingQuery != "" {
		p.RWaitingQuery = kutils.StringDeepCopy(src.RWaitingQuery)
	}

	if src.RTrxStarted != "" {
		p.RTrxStarted = kutils.StringDeepCopy(src.RTrxStarted)
	}

	if src.RTrxWaitStarted != "" {
		p.RTrxWaitStarted = kutils.StringDeepCopy(src.RTrxWaitStarted)
	}

	if src.RBlockedWaitSecs != "" {
		p.RBlockedWaitSecs = kutils.StringDeepCopy(src.RBlockedWaitSecs)
	}

	if src.RTrxRowsModified != "" {
		p.RTrxRowsModified = kutils.StringDeepCopy(src.RTrxRowsModified)
	}

	if src.RTrxRowsLocked != "" {
		p.RTrxRowsLocked = kutils.StringDeepCopy(src.RTrxRowsLocked)
	}

	if src.RTrxOperationState != "" {
		p.RTrxOperationState = kutils.StringDeepCopy(src.RTrxOperationState)
	}

	if src.BTrxMysqlThreadId != "" {
		p.BTrxMysqlThreadId = kutils.StringDeepCopy(src.BTrxMysqlThreadId)
	}

	if src.BTrxId != "" {
		p.BTrxId = kutils.StringDeepCopy(src.BTrxId)
	}

	if src.BTrxState != "" {
		p.BTrxState = kutils.StringDeepCopy(src.BTrxState)
	}

	if src.BBlockingQuery != "" {
		p.BBlockingQuery = kutils.StringDeepCopy(src.BBlockingQuery)
	}

	if src.BTrxStarted != "" {
		p.BTrxStarted = kutils.StringDeepCopy(src.BTrxStarted)
	}

	if src.BTrxWaitStarted != "" {
		p.BTrxWaitStarted = kutils.StringDeepCopy(src.BTrxWaitStarted)
	}

	if src.BBlockingWaitSecs != "" {
		p.BBlockingWaitSecs = kutils.StringDeepCopy(src.BBlockingWaitSecs)
	}

	if src.BTrxRowsModified != "" {
		p.BTrxRowsModified = kutils.StringDeepCopy(src.BTrxRowsModified)
	}

	if src.BTrxRowsLocked != "" {
		p.BTrxRowsLocked = kutils.StringDeepCopy(src.BTrxRowsLocked)
	}

	if src.BTrxOperationState != "" {
		p.BTrxOperationState = kutils.StringDeepCopy(src.BTrxOperationState)
	}

	return nil
}

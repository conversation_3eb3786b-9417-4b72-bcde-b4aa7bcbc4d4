// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type MgrParam struct {
	Name         string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Value        string `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
	Desc         string `thrift:"Desc,3,required" frugal:"3,required,string" json:"Desc"`
	DefaultValue string `thrift:"DefaultValue,4,required" frugal:"4,required,string" json:"DefaultValue"`
}

func NewMgrParam() *MgrParam {
	return &MgrParam{}
}

func (p *MgrParam) InitDefault() {
}

func (p *MgrParam) GetName() (v string) {
	return p.Name
}

func (p *MgrParam) GetValue() (v string) {
	return p.Value
}

func (p *MgrParam) GetDesc() (v string) {
	return p.Desc
}

func (p *MgrParam) GetDefaultValue() (v string) {
	return p.DefaultValue
}
func (p *MgrParam) SetName(val string) {
	p.Name = val
}
func (p *MgrParam) SetValue(val string) {
	p.Value = val
}
func (p *MgrParam) SetDesc(val string) {
	p.Desc = val
}
func (p *MgrParam) SetDefaultValue(val string) {
	p.DefaultValue = val
}

var fieldIDToName_MgrParam = map[int16]string{
	1: "Name",
	2: "Value",
	3: "Desc",
	4: "DefaultValue",
}

func (p *MgrParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MgrParam")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetValue bool = false
	var issetDesc bool = false
	var issetDefaultValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDesc = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDefaultValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDesc {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDefaultValue {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MgrParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MgrParam[fieldId]))
}

func (p *MgrParam) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *MgrParam) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *MgrParam) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *MgrParam) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DefaultValue = _field
	return nil
}

func (p *MgrParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MgrParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("MgrParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MgrParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MgrParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MgrParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Desc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *MgrParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DefaultValue", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DefaultValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *MgrParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MgrParam(%+v)", *p)

}

func (p *MgrParam) DeepEqual(ano *MgrParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Desc) {
		return false
	}
	if !p.Field4DeepEqual(ano.DefaultValue) {
		return false
	}
	return true
}

func (p *MgrParam) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *MgrParam) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}
func (p *MgrParam) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Desc, src) != 0 {
		return false
	}
	return true
}
func (p *MgrParam) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DefaultValue, src) != 0 {
		return false
	}
	return true
}

type DescribeMgrParamReq struct {
}

func NewDescribeMgrParamReq() *DescribeMgrParamReq {
	return &DescribeMgrParamReq{}
}

func (p *DescribeMgrParamReq) InitDefault() {
}

var fieldIDToName_DescribeMgrParamReq = map[int16]string{}

func (p *DescribeMgrParamReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMgrParamReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeMgrParamReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMgrParamReq")

	if err = oprot.WriteStructBegin("DescribeMgrParamReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMgrParamReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMgrParamReq(%+v)", *p)

}

func (p *DescribeMgrParamReq) DeepEqual(ano *DescribeMgrParamReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeMgrParamResp struct {
	Params []*MgrParam `thrift:"Params,1,required" frugal:"1,required,list<MgrParam>" json:"Params"`
}

func NewDescribeMgrParamResp() *DescribeMgrParamResp {
	return &DescribeMgrParamResp{}
}

func (p *DescribeMgrParamResp) InitDefault() {
}

func (p *DescribeMgrParamResp) GetParams() (v []*MgrParam) {
	return p.Params
}
func (p *DescribeMgrParamResp) SetParams(val []*MgrParam) {
	p.Params = val
}

var fieldIDToName_DescribeMgrParamResp = map[int16]string{
	1: "Params",
}

func (p *DescribeMgrParamResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMgrParamResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetParams bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetParams = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetParams {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMgrParamResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMgrParamResp[fieldId]))
}

func (p *DescribeMgrParamResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MgrParam, 0, size)
	values := make([]MgrParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Params = _field
	return nil
}

func (p *DescribeMgrParamResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMgrParamResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMgrParamResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMgrParamResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Params", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Params)); err != nil {
		return err
	}
	for _, v := range p.Params {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMgrParamResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMgrParamResp(%+v)", *p)

}

func (p *DescribeMgrParamResp) DeepEqual(ano *DescribeMgrParamResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Params) {
		return false
	}
	return true
}

func (p *DescribeMgrParamResp) Field1DeepEqual(src []*MgrParam) bool {

	if len(p.Params) != len(src) {
		return false
	}
	for i, v := range p.Params {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type UpdateMgrParamItem struct {
	Name  string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Value string `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
}

func NewUpdateMgrParamItem() *UpdateMgrParamItem {
	return &UpdateMgrParamItem{}
}

func (p *UpdateMgrParamItem) InitDefault() {
}

func (p *UpdateMgrParamItem) GetName() (v string) {
	return p.Name
}

func (p *UpdateMgrParamItem) GetValue() (v string) {
	return p.Value
}
func (p *UpdateMgrParamItem) SetName(val string) {
	p.Name = val
}
func (p *UpdateMgrParamItem) SetValue(val string) {
	p.Value = val
}

var fieldIDToName_UpdateMgrParamItem = map[int16]string{
	1: "Name",
	2: "Value",
}

func (p *UpdateMgrParamItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateMgrParamItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateMgrParamItem[fieldId]))
}

func (p *UpdateMgrParamItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *UpdateMgrParamItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *UpdateMgrParamItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateMgrParamItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateMgrParamItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateMgrParamItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateMgrParamItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMgrParamItem(%+v)", *p)

}

func (p *UpdateMgrParamItem) DeepEqual(ano *UpdateMgrParamItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *UpdateMgrParamItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateMgrParamItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}

type UpdateMgrParamReq struct {
	Items []*UpdateMgrParamItem `thrift:"Items,1,required" frugal:"1,required,list<UpdateMgrParamItem>" json:"Items"`
}

func NewUpdateMgrParamReq() *UpdateMgrParamReq {
	return &UpdateMgrParamReq{}
}

func (p *UpdateMgrParamReq) InitDefault() {
}

func (p *UpdateMgrParamReq) GetItems() (v []*UpdateMgrParamItem) {
	return p.Items
}
func (p *UpdateMgrParamReq) SetItems(val []*UpdateMgrParamItem) {
	p.Items = val
}

var fieldIDToName_UpdateMgrParamReq = map[int16]string{
	1: "Items",
}

func (p *UpdateMgrParamReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItems bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateMgrParamReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateMgrParamReq[fieldId]))
}

func (p *UpdateMgrParamReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UpdateMgrParamItem, 0, size)
	values := make([]UpdateMgrParamItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}

func (p *UpdateMgrParamReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateMgrParamReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateMgrParamReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Items", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Items)); err != nil {
		return err
	}
	for _, v := range p.Items {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateMgrParamReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMgrParamReq(%+v)", *p)

}

func (p *UpdateMgrParamReq) DeepEqual(ano *UpdateMgrParamReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Items) {
		return false
	}
	return true
}

func (p *UpdateMgrParamReq) Field1DeepEqual(src []*UpdateMgrParamItem) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type UpdateMgrParamResp struct {
}

func NewUpdateMgrParamResp() *UpdateMgrParamResp {
	return &UpdateMgrParamResp{}
}

func (p *UpdateMgrParamResp) InitDefault() {
}

var fieldIDToName_UpdateMgrParamResp = map[int16]string{}

func (p *UpdateMgrParamResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateMgrParamResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateMgrParamResp")

	if err = oprot.WriteStructBegin("UpdateMgrParamResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateMgrParamResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMgrParamResp(%+v)", *p)

}

func (p *UpdateMgrParamResp) DeepEqual(ano *UpdateMgrParamResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeployReq struct {
	Force *bool `thrift:"Force,1,optional" frugal:"1,optional,bool" json:"Force,omitempty"`
}

func NewDeployReq() *DeployReq {
	return &DeployReq{}
}

func (p *DeployReq) InitDefault() {
}

var DeployReq_Force_DEFAULT bool

func (p *DeployReq) GetForce() (v bool) {
	if !p.IsSetForce() {
		return DeployReq_Force_DEFAULT
	}
	return *p.Force
}
func (p *DeployReq) SetForce(val *bool) {
	p.Force = val
}

var fieldIDToName_DeployReq = map[int16]string{
	1: "Force",
}

func (p *DeployReq) IsSetForce() bool {
	return p.Force != nil
}

func (p *DeployReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeployReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeployReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeployReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Force = _field
	return nil
}

func (p *DeployReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeployReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeployReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeployReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetForce() {
		if err = oprot.WriteFieldBegin("Force", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Force); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeployReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeployReq(%+v)", *p)

}

func (p *DeployReq) DeepEqual(ano *DeployReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Force) {
		return false
	}
	return true
}

func (p *DeployReq) Field1DeepEqual(src *bool) bool {

	if p.Force == src {
		return true
	} else if p.Force == nil || src == nil {
		return false
	}
	if *p.Force != *src {
		return false
	}
	return true
}

type DeployResp struct {
}

func NewDeployResp() *DeployResp {
	return &DeployResp{}
}

func (p *DeployResp) InitDefault() {
}

var fieldIDToName_DeployResp = map[int16]string{}

func (p *DeployResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeployResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeployResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeployResp")

	if err = oprot.WriteStructBegin("DeployResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeployResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeployResp(%+v)", *p)

}

func (p *DeployResp) DeepEqual(ano *DeployResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DmTaskSearchParam struct {
	TaskType     *string `thrift:"TaskType,1,optional" frugal:"1,optional,string" json:"TaskType,omitempty"`
	TenantId     *string `thrift:"TenantId,2,optional" frugal:"2,optional,string" json:"TenantId,omitempty"`
	TaskId       *string `thrift:"TaskId,3,optional" frugal:"3,optional,string" json:"TaskId,omitempty"`
	InstanceType *DSType `thrift:"InstanceType,4,optional" frugal:"4,optional,DSType" json:"InstanceType,omitempty"`
}

func NewDmTaskSearchParam() *DmTaskSearchParam {
	return &DmTaskSearchParam{}
}

func (p *DmTaskSearchParam) InitDefault() {
}

var DmTaskSearchParam_TaskType_DEFAULT string

func (p *DmTaskSearchParam) GetTaskType() (v string) {
	if !p.IsSetTaskType() {
		return DmTaskSearchParam_TaskType_DEFAULT
	}
	return *p.TaskType
}

var DmTaskSearchParam_TenantId_DEFAULT string

func (p *DmTaskSearchParam) GetTenantId() (v string) {
	if !p.IsSetTenantId() {
		return DmTaskSearchParam_TenantId_DEFAULT
	}
	return *p.TenantId
}

var DmTaskSearchParam_TaskId_DEFAULT string

func (p *DmTaskSearchParam) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return DmTaskSearchParam_TaskId_DEFAULT
	}
	return *p.TaskId
}

var DmTaskSearchParam_InstanceType_DEFAULT DSType

func (p *DmTaskSearchParam) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DmTaskSearchParam_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DmTaskSearchParam) SetTaskType(val *string) {
	p.TaskType = val
}
func (p *DmTaskSearchParam) SetTenantId(val *string) {
	p.TenantId = val
}
func (p *DmTaskSearchParam) SetTaskId(val *string) {
	p.TaskId = val
}
func (p *DmTaskSearchParam) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_DmTaskSearchParam = map[int16]string{
	1: "TaskType",
	2: "TenantId",
	3: "TaskId",
	4: "InstanceType",
}

func (p *DmTaskSearchParam) IsSetTaskType() bool {
	return p.TaskType != nil
}

func (p *DmTaskSearchParam) IsSetTenantId() bool {
	return p.TenantId != nil
}

func (p *DmTaskSearchParam) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *DmTaskSearchParam) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DmTaskSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DmTaskSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DmTaskSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DmTaskSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskType = _field
	return nil
}
func (p *DmTaskSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TenantId = _field
	return nil
}
func (p *DmTaskSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}
func (p *DmTaskSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DmTaskSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DmTaskSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("DmTaskSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DmTaskSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskType() {
		if err = oprot.WriteFieldBegin("TaskType", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DmTaskSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTenantId() {
		if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TenantId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DmTaskSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DmTaskSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DmTaskSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmTaskSearchParam(%+v)", *p)

}

func (p *DmTaskSearchParam) DeepEqual(ano *DmTaskSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field2DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DmTaskSearchParam) Field1DeepEqual(src *string) bool {

	if p.TaskType == src {
		return true
	} else if p.TaskType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskType, *src) != 0 {
		return false
	}
	return true
}
func (p *DmTaskSearchParam) Field2DeepEqual(src *string) bool {

	if p.TenantId == src {
		return true
	} else if p.TenantId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TenantId, *src) != 0 {
		return false
	}
	return true
}
func (p *DmTaskSearchParam) Field3DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}
func (p *DmTaskSearchParam) Field4DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type StopDataMigrationTaskReq struct {
	TaskId       string  `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	InstanceId   string  `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	InstanceType *DSType `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	TenantID     *string `thrift:"TenantID,4,optional" frugal:"4,optional,string" json:"TenantID,omitempty"`
}

func NewStopDataMigrationTaskReq() *StopDataMigrationTaskReq {
	return &StopDataMigrationTaskReq{}
}

func (p *StopDataMigrationTaskReq) InitDefault() {
}

func (p *StopDataMigrationTaskReq) GetTaskId() (v string) {
	return p.TaskId
}

func (p *StopDataMigrationTaskReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var StopDataMigrationTaskReq_InstanceType_DEFAULT DSType

func (p *StopDataMigrationTaskReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return StopDataMigrationTaskReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var StopDataMigrationTaskReq_TenantID_DEFAULT string

func (p *StopDataMigrationTaskReq) GetTenantID() (v string) {
	if !p.IsSetTenantID() {
		return StopDataMigrationTaskReq_TenantID_DEFAULT
	}
	return *p.TenantID
}
func (p *StopDataMigrationTaskReq) SetTaskId(val string) {
	p.TaskId = val
}
func (p *StopDataMigrationTaskReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *StopDataMigrationTaskReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *StopDataMigrationTaskReq) SetTenantID(val *string) {
	p.TenantID = val
}

var fieldIDToName_StopDataMigrationTaskReq = map[int16]string{
	1: "TaskId",
	2: "InstanceId",
	3: "InstanceType",
	4: "TenantID",
}

func (p *StopDataMigrationTaskReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *StopDataMigrationTaskReq) IsSetTenantID() bool {
	return p.TenantID != nil
}

func (p *StopDataMigrationTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopDataMigrationTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopDataMigrationTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_StopDataMigrationTaskReq[fieldId]))
}

func (p *StopDataMigrationTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *StopDataMigrationTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *StopDataMigrationTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *StopDataMigrationTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TenantID = _field
	return nil
}

func (p *StopDataMigrationTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopDataMigrationTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopDataMigrationTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopDataMigrationTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopDataMigrationTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StopDataMigrationTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *StopDataMigrationTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTenantID() {
		if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TenantID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *StopDataMigrationTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopDataMigrationTaskReq(%+v)", *p)

}

func (p *StopDataMigrationTaskReq) DeepEqual(ano *StopDataMigrationTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.TenantID) {
		return false
	}
	return true
}

func (p *StopDataMigrationTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *StopDataMigrationTaskReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *StopDataMigrationTaskReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *StopDataMigrationTaskReq) Field4DeepEqual(src *string) bool {

	if p.TenantID == src {
		return true
	} else if p.TenantID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TenantID, *src) != 0 {
		return false
	}
	return true
}

type DBTaskItem struct {
	TaskId          string            `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	TaskType        string            `thrift:"TaskType,2,required" frugal:"2,required,string" json:"TaskType"`
	DB              string            `thrift:"DB,3,required" frugal:"3,required,string" json:"DB"`
	Tables          string            `thrift:"Tables,4,required" frugal:"4,required,string" json:"Tables"`
	User            string            `thrift:"User,5,required" frugal:"5,required,string" json:"User"`
	CreateTime      string            `thrift:"CreateTime,6,required" frugal:"6,required,string" json:"CreateTime"`
	ExecTime        string            `thrift:"ExecTime,7,required" frugal:"7,required,string" json:"ExecTime"`
	TaskStatus      DBMigrationStatus `thrift:"TaskStatus,8,required" frugal:"8,required,DBMigrationStatus" json:"TaskStatus"`
	TaskProgress    int32             `thrift:"TaskProgress,9,required" frugal:"9,required,i32" json:"TaskProgress"`
	ExpiredTime     string            `thrift:"ExpiredTime,10,required" frugal:"10,required,string" json:"ExpiredTime"`
	InstanceId      string            `thrift:"InstanceId,11,required" frugal:"11,required,string" json:"InstanceId"`
	TenantId        string            `thrift:"TenantId,12,required" frugal:"12,required,string" json:"TenantId"`
	ProgressDetails []*ProgressDetail `thrift:"ProgressDetails,13,required" frugal:"13,required,list<ProgressDetail>" json:"ProgressDetails"`
	InstanceType    string            `thrift:"InstanceType,14,required" frugal:"14,required,string" json:"InstanceType"`
}

func NewDBTaskItem() *DBTaskItem {
	return &DBTaskItem{}
}

func (p *DBTaskItem) InitDefault() {
}

func (p *DBTaskItem) GetTaskId() (v string) {
	return p.TaskId
}

func (p *DBTaskItem) GetTaskType() (v string) {
	return p.TaskType
}

func (p *DBTaskItem) GetDB() (v string) {
	return p.DB
}

func (p *DBTaskItem) GetTables() (v string) {
	return p.Tables
}

func (p *DBTaskItem) GetUser() (v string) {
	return p.User
}

func (p *DBTaskItem) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *DBTaskItem) GetExecTime() (v string) {
	return p.ExecTime
}

func (p *DBTaskItem) GetTaskStatus() (v DBMigrationStatus) {
	return p.TaskStatus
}

func (p *DBTaskItem) GetTaskProgress() (v int32) {
	return p.TaskProgress
}

func (p *DBTaskItem) GetExpiredTime() (v string) {
	return p.ExpiredTime
}

func (p *DBTaskItem) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DBTaskItem) GetTenantId() (v string) {
	return p.TenantId
}

func (p *DBTaskItem) GetProgressDetails() (v []*ProgressDetail) {
	return p.ProgressDetails
}

func (p *DBTaskItem) GetInstanceType() (v string) {
	return p.InstanceType
}
func (p *DBTaskItem) SetTaskId(val string) {
	p.TaskId = val
}
func (p *DBTaskItem) SetTaskType(val string) {
	p.TaskType = val
}
func (p *DBTaskItem) SetDB(val string) {
	p.DB = val
}
func (p *DBTaskItem) SetTables(val string) {
	p.Tables = val
}
func (p *DBTaskItem) SetUser(val string) {
	p.User = val
}
func (p *DBTaskItem) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *DBTaskItem) SetExecTime(val string) {
	p.ExecTime = val
}
func (p *DBTaskItem) SetTaskStatus(val DBMigrationStatus) {
	p.TaskStatus = val
}
func (p *DBTaskItem) SetTaskProgress(val int32) {
	p.TaskProgress = val
}
func (p *DBTaskItem) SetExpiredTime(val string) {
	p.ExpiredTime = val
}
func (p *DBTaskItem) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DBTaskItem) SetTenantId(val string) {
	p.TenantId = val
}
func (p *DBTaskItem) SetProgressDetails(val []*ProgressDetail) {
	p.ProgressDetails = val
}
func (p *DBTaskItem) SetInstanceType(val string) {
	p.InstanceType = val
}

var fieldIDToName_DBTaskItem = map[int16]string{
	1:  "TaskId",
	2:  "TaskType",
	3:  "DB",
	4:  "Tables",
	5:  "User",
	6:  "CreateTime",
	7:  "ExecTime",
	8:  "TaskStatus",
	9:  "TaskProgress",
	10: "ExpiredTime",
	11: "InstanceId",
	12: "TenantId",
	13: "ProgressDetails",
	14: "InstanceType",
}

func (p *DBTaskItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DBTaskItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetTaskType bool = false
	var issetDB bool = false
	var issetTables bool = false
	var issetUser bool = false
	var issetCreateTime bool = false
	var issetExecTime bool = false
	var issetTaskStatus bool = false
	var issetTaskProgress bool = false
	var issetExpiredTime bool = false
	var issetInstanceId bool = false
	var issetTenantId bool = false
	var issetProgressDetails bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTables = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskProgress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpiredTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetProgressDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTaskType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTables {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetUser {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetExecTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetTaskStatus {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetTaskProgress {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetExpiredTime {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetTenantId {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetProgressDetails {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 14
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DBTaskItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DBTaskItem[fieldId]))
}

func (p *DBTaskItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *DBTaskItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskType = _field
	return nil
}
func (p *DBTaskItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DBTaskItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Tables = _field
	return nil
}
func (p *DBTaskItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.User = _field
	return nil
}
func (p *DBTaskItem) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *DBTaskItem) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecTime = _field
	return nil
}
func (p *DBTaskItem) ReadField8(iprot thrift.TProtocol) error {

	var _field DBMigrationStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBMigrationStatus(v)
	}
	p.TaskStatus = _field
	return nil
}
func (p *DBTaskItem) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskProgress = _field
	return nil
}
func (p *DBTaskItem) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpiredTime = _field
	return nil
}
func (p *DBTaskItem) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DBTaskItem) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *DBTaskItem) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ProgressDetail, 0, size)
	values := make([]ProgressDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProgressDetails = _field
	return nil
}
func (p *DBTaskItem) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}

func (p *DBTaskItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DBTaskItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("DBTaskItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DBTaskItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DBTaskItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskType", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DBTaskItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DBTaskItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tables", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Tables); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DBTaskItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("User", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.User); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DBTaskItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DBTaskItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DBTaskItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskStatus", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DBTaskItem) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskProgress", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TaskProgress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DBTaskItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpiredTime", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExpiredTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DBTaskItem) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DBTaskItem) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DBTaskItem) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProgressDetails", thrift.LIST, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProgressDetails)); err != nil {
		return err
	}
	for _, v := range p.ProgressDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DBTaskItem) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DBTaskItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DBTaskItem(%+v)", *p)

}

func (p *DBTaskItem) DeepEqual(ano *DBTaskItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field3DeepEqual(ano.DB) {
		return false
	}
	if !p.Field4DeepEqual(ano.Tables) {
		return false
	}
	if !p.Field5DeepEqual(ano.User) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.ExecTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.TaskStatus) {
		return false
	}
	if !p.Field9DeepEqual(ano.TaskProgress) {
		return false
	}
	if !p.Field10DeepEqual(ano.ExpiredTime) {
		return false
	}
	if !p.Field11DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field12DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field13DeepEqual(ano.ProgressDetails) {
		return false
	}
	if !p.Field14DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DBTaskItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TaskType, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Tables, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.User, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field7DeepEqual(src string) bool {

	if strings.Compare(p.ExecTime, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field8DeepEqual(src DBMigrationStatus) bool {

	if p.TaskStatus != src {
		return false
	}
	return true
}
func (p *DBTaskItem) Field9DeepEqual(src int32) bool {

	if p.TaskProgress != src {
		return false
	}
	return true
}
func (p *DBTaskItem) Field10DeepEqual(src string) bool {

	if strings.Compare(p.ExpiredTime, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field11DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field12DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *DBTaskItem) Field13DeepEqual(src []*ProgressDetail) bool {

	if len(p.ProgressDetails) != len(src) {
		return false
	}
	for i, v := range p.ProgressDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DBTaskItem) Field14DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}

type LogCollectorItem struct {
	ClusterName  string `thrift:"ClusterName,1,required" frugal:"1,required,string" json:"ClusterName"`
	Image        string `thrift:"Image,2,required" frugal:"2,required,string" json:"Image"`
	CreateTime   string `thrift:"CreateTime,3,required" frugal:"3,required,string" json:"CreateTime"`
	UpdateTime   string `thrift:"UpdateTime,4,required" frugal:"4,required,string" json:"UpdateTime"`
	InstanceType DSType `thrift:"InstanceType,5,required" frugal:"5,required,DSType" json:"InstanceType"`
}

func NewLogCollectorItem() *LogCollectorItem {
	return &LogCollectorItem{}
}

func (p *LogCollectorItem) InitDefault() {
}

func (p *LogCollectorItem) GetClusterName() (v string) {
	return p.ClusterName
}

func (p *LogCollectorItem) GetImage() (v string) {
	return p.Image
}

func (p *LogCollectorItem) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *LogCollectorItem) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *LogCollectorItem) GetInstanceType() (v DSType) {
	return p.InstanceType
}
func (p *LogCollectorItem) SetClusterName(val string) {
	p.ClusterName = val
}
func (p *LogCollectorItem) SetImage(val string) {
	p.Image = val
}
func (p *LogCollectorItem) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *LogCollectorItem) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *LogCollectorItem) SetInstanceType(val DSType) {
	p.InstanceType = val
}

var fieldIDToName_LogCollectorItem = map[int16]string{
	1: "ClusterName",
	2: "Image",
	3: "CreateTime",
	4: "UpdateTime",
	5: "InstanceType",
}

func (p *LogCollectorItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LogCollectorItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetClusterName bool = false
	var issetImage bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetClusterName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetImage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetClusterName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetImage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LogCollectorItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LogCollectorItem[fieldId]))
}

func (p *LogCollectorItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClusterName = _field
	return nil
}
func (p *LogCollectorItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Image = _field
	return nil
}
func (p *LogCollectorItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *LogCollectorItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *LogCollectorItem) ReadField5(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.InstanceType = _field
	return nil
}

func (p *LogCollectorItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LogCollectorItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("LogCollectorItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LogCollectorItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ClusterName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClusterName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LogCollectorItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Image", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Image); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LogCollectorItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LogCollectorItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *LogCollectorItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *LogCollectorItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogCollectorItem(%+v)", *p)

}

func (p *LogCollectorItem) DeepEqual(ano *LogCollectorItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ClusterName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Image) {
		return false
	}
	if !p.Field3DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *LogCollectorItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ClusterName, src) != 0 {
		return false
	}
	return true
}
func (p *LogCollectorItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Image, src) != 0 {
		return false
	}
	return true
}
func (p *LogCollectorItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *LogCollectorItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *LogCollectorItem) Field5DeepEqual(src DSType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}

type StopDataMigrationTaskResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewStopDataMigrationTaskResp() *StopDataMigrationTaskResp {
	return &StopDataMigrationTaskResp{}
}

func (p *StopDataMigrationTaskResp) InitDefault() {
}

func (p *StopDataMigrationTaskResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *StopDataMigrationTaskResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_StopDataMigrationTaskResp = map[int16]string{
	1: "Success",
}

func (p *StopDataMigrationTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopDataMigrationTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopDataMigrationTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_StopDataMigrationTaskResp[fieldId]))
}

func (p *StopDataMigrationTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *StopDataMigrationTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopDataMigrationTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopDataMigrationTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopDataMigrationTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopDataMigrationTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopDataMigrationTaskResp(%+v)", *p)

}

func (p *StopDataMigrationTaskResp) DeepEqual(ano *StopDataMigrationTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *StopDataMigrationTaskResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

type DescribeDmTasksReq struct {
	InstanceId  *string            `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	SearchParam *DmTaskSearchParam `thrift:"SearchParam,2,optional" frugal:"2,optional,DmTaskSearchParam" json:"SearchParam,omitempty"`
	PageNumber  *int32             `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize    *int32             `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	SortBy      *SortBy            `thrift:"SortBy,5,optional" frugal:"5,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy     *OrderByForDBTask  `thrift:"OrderBy,6,optional" frugal:"6,optional,OrderByForDBTask" json:"OrderBy,omitempty"`
}

func NewDescribeDmTasksReq() *DescribeDmTasksReq {
	return &DescribeDmTasksReq{}
}

func (p *DescribeDmTasksReq) InitDefault() {
}

var DescribeDmTasksReq_InstanceId_DEFAULT string

func (p *DescribeDmTasksReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeDmTasksReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeDmTasksReq_SearchParam_DEFAULT *DmTaskSearchParam

func (p *DescribeDmTasksReq) GetSearchParam() (v *DmTaskSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeDmTasksReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeDmTasksReq_PageNumber_DEFAULT int32

func (p *DescribeDmTasksReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDmTasksReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDmTasksReq_PageSize_DEFAULT int32

func (p *DescribeDmTasksReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDmTasksReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDmTasksReq_SortBy_DEFAULT SortBy

func (p *DescribeDmTasksReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeDmTasksReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeDmTasksReq_OrderBy_DEFAULT OrderByForDBTask

func (p *DescribeDmTasksReq) GetOrderBy() (v OrderByForDBTask) {
	if !p.IsSetOrderBy() {
		return DescribeDmTasksReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}
func (p *DescribeDmTasksReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeDmTasksReq) SetSearchParam(val *DmTaskSearchParam) {
	p.SearchParam = val
}
func (p *DescribeDmTasksReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDmTasksReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDmTasksReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeDmTasksReq) SetOrderBy(val *OrderByForDBTask) {
	p.OrderBy = val
}

var fieldIDToName_DescribeDmTasksReq = map[int16]string{
	1: "InstanceId",
	2: "SearchParam",
	3: "PageNumber",
	4: "PageSize",
	5: "SortBy",
	6: "OrderBy",
}

func (p *DescribeDmTasksReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeDmTasksReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeDmTasksReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDmTasksReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDmTasksReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeDmTasksReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeDmTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDmTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDmTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeDmTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDmTasksReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewDmTaskSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeDmTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDmTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDmTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeDmTasksReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *OrderByForDBTask
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForDBTask(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}

func (p *DescribeDmTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDmTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDmTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDmTasksReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDmTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDmTasksReq(%+v)", *p)

}

func (p *DescribeDmTasksReq) DeepEqual(ano *DescribeDmTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	return true
}

func (p *DescribeDmTasksReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDmTasksReq) Field2DeepEqual(src *DmTaskSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDmTasksReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDmTasksReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDmTasksReq) Field5DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeDmTasksReq) Field6DeepEqual(src *OrderByForDBTask) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}

type DescribeDmTasksResp struct {
	Tasks []*DBTaskItem `thrift:"Tasks,1,required" frugal:"1,required,list<DBTaskItem>" json:"Tasks"`
	Total int32         `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeDmTasksResp() *DescribeDmTasksResp {
	return &DescribeDmTasksResp{}
}

func (p *DescribeDmTasksResp) InitDefault() {
}

func (p *DescribeDmTasksResp) GetTasks() (v []*DBTaskItem) {
	return p.Tasks
}

func (p *DescribeDmTasksResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeDmTasksResp) SetTasks(val []*DBTaskItem) {
	p.Tasks = val
}
func (p *DescribeDmTasksResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeDmTasksResp = map[int16]string{
	1: "Tasks",
	2: "Total",
}

func (p *DescribeDmTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDmTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTasks bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTasks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTasks {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDmTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDmTasksResp[fieldId]))
}

func (p *DescribeDmTasksResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DBTaskItem, 0, size)
	values := make([]DBTaskItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tasks = _field
	return nil
}
func (p *DescribeDmTasksResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeDmTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDmTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDmTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDmTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tasks", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
		return err
	}
	for _, v := range p.Tasks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDmTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDmTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDmTasksResp(%+v)", *p)

}

func (p *DescribeDmTasksResp) DeepEqual(ano *DescribeDmTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Tasks) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeDmTasksResp) Field1DeepEqual(src []*DBTaskItem) bool {

	if len(p.Tasks) != len(src) {
		return false
	}
	for i, v := range p.Tasks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDmTasksResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type CreateManualCCLInspectionTaskReq struct {
}

func NewCreateManualCCLInspectionTaskReq() *CreateManualCCLInspectionTaskReq {
	return &CreateManualCCLInspectionTaskReq{}
}

func (p *CreateManualCCLInspectionTaskReq) InitDefault() {
}

var fieldIDToName_CreateManualCCLInspectionTaskReq = map[int16]string{}

func (p *CreateManualCCLInspectionTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualCCLInspectionTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateManualCCLInspectionTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualCCLInspectionTaskReq")

	if err = oprot.WriteStructBegin("CreateManualCCLInspectionTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateManualCCLInspectionTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateManualCCLInspectionTaskReq(%+v)", *p)

}

func (p *CreateManualCCLInspectionTaskReq) DeepEqual(ano *CreateManualCCLInspectionTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateManualCCLInspectionTaskResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewCreateManualCCLInspectionTaskResp() *CreateManualCCLInspectionTaskResp {
	return &CreateManualCCLInspectionTaskResp{}
}

func (p *CreateManualCCLInspectionTaskResp) InitDefault() {
}

func (p *CreateManualCCLInspectionTaskResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *CreateManualCCLInspectionTaskResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_CreateManualCCLInspectionTaskResp = map[int16]string{
	1: "Success",
}

func (p *CreateManualCCLInspectionTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualCCLInspectionTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateManualCCLInspectionTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateManualCCLInspectionTaskResp[fieldId]))
}

func (p *CreateManualCCLInspectionTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *CreateManualCCLInspectionTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualCCLInspectionTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateManualCCLInspectionTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateManualCCLInspectionTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateManualCCLInspectionTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateManualCCLInspectionTaskResp(%+v)", *p)

}

func (p *CreateManualCCLInspectionTaskResp) DeepEqual(ano *CreateManualCCLInspectionTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *CreateManualCCLInspectionTaskResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

type ModifyLogCollectorImageReq struct {
	ClusterName  string `thrift:"ClusterName,1,required" frugal:"1,required,string" json:"ClusterName"`
	Image        string `thrift:"Image,2,required" frugal:"2,required,string" json:"Image"`
	InstanceType DSType `thrift:"InstanceType,3,required" frugal:"3,required,DSType" json:"InstanceType"`
}

func NewModifyLogCollectorImageReq() *ModifyLogCollectorImageReq {
	return &ModifyLogCollectorImageReq{}
}

func (p *ModifyLogCollectorImageReq) InitDefault() {
}

func (p *ModifyLogCollectorImageReq) GetClusterName() (v string) {
	return p.ClusterName
}

func (p *ModifyLogCollectorImageReq) GetImage() (v string) {
	return p.Image
}

func (p *ModifyLogCollectorImageReq) GetInstanceType() (v DSType) {
	return p.InstanceType
}
func (p *ModifyLogCollectorImageReq) SetClusterName(val string) {
	p.ClusterName = val
}
func (p *ModifyLogCollectorImageReq) SetImage(val string) {
	p.Image = val
}
func (p *ModifyLogCollectorImageReq) SetInstanceType(val DSType) {
	p.InstanceType = val
}

var fieldIDToName_ModifyLogCollectorImageReq = map[int16]string{
	1: "ClusterName",
	2: "Image",
	3: "InstanceType",
}

func (p *ModifyLogCollectorImageReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyLogCollectorImageReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetClusterName bool = false
	var issetImage bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetClusterName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetImage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetClusterName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetImage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyLogCollectorImageReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyLogCollectorImageReq[fieldId]))
}

func (p *ModifyLogCollectorImageReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClusterName = _field
	return nil
}
func (p *ModifyLogCollectorImageReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Image = _field
	return nil
}
func (p *ModifyLogCollectorImageReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.InstanceType = _field
	return nil
}

func (p *ModifyLogCollectorImageReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyLogCollectorImageReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyLogCollectorImageReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyLogCollectorImageReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ClusterName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClusterName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyLogCollectorImageReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Image", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Image); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyLogCollectorImageReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyLogCollectorImageReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyLogCollectorImageReq(%+v)", *p)

}

func (p *ModifyLogCollectorImageReq) DeepEqual(ano *ModifyLogCollectorImageReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ClusterName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Image) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *ModifyLogCollectorImageReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ClusterName, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyLogCollectorImageReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Image, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyLogCollectorImageReq) Field3DeepEqual(src DSType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}

type ModifyLogCollectorImageResp struct {
}

func NewModifyLogCollectorImageResp() *ModifyLogCollectorImageResp {
	return &ModifyLogCollectorImageResp{}
}

func (p *ModifyLogCollectorImageResp) InitDefault() {
}

var fieldIDToName_ModifyLogCollectorImageResp = map[int16]string{}

func (p *ModifyLogCollectorImageResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyLogCollectorImageResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyLogCollectorImageResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyLogCollectorImageResp")

	if err = oprot.WriteStructBegin("ModifyLogCollectorImageResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyLogCollectorImageResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyLogCollectorImageResp(%+v)", *p)

}

func (p *ModifyLogCollectorImageResp) DeepEqual(ano *ModifyLogCollectorImageResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeLogCollectorClustersReq struct {
}

func NewDescribeLogCollectorClustersReq() *DescribeLogCollectorClustersReq {
	return &DescribeLogCollectorClustersReq{}
}

func (p *DescribeLogCollectorClustersReq) InitDefault() {
}

var fieldIDToName_DescribeLogCollectorClustersReq = map[int16]string{}

func (p *DescribeLogCollectorClustersReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogCollectorClustersReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeLogCollectorClustersReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogCollectorClustersReq")

	if err = oprot.WriteStructBegin("DescribeLogCollectorClustersReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogCollectorClustersReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogCollectorClustersReq(%+v)", *p)

}

func (p *DescribeLogCollectorClustersReq) DeepEqual(ano *DescribeLogCollectorClustersReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeLogCollectorClustersResp struct {
	Tasks []*LogCollectorItem `thrift:"Tasks,1,required" frugal:"1,required,list<LogCollectorItem>" json:"Tasks"`
	Total int32               `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeLogCollectorClustersResp() *DescribeLogCollectorClustersResp {
	return &DescribeLogCollectorClustersResp{}
}

func (p *DescribeLogCollectorClustersResp) InitDefault() {
}

func (p *DescribeLogCollectorClustersResp) GetTasks() (v []*LogCollectorItem) {
	return p.Tasks
}

func (p *DescribeLogCollectorClustersResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeLogCollectorClustersResp) SetTasks(val []*LogCollectorItem) {
	p.Tasks = val
}
func (p *DescribeLogCollectorClustersResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeLogCollectorClustersResp = map[int16]string{
	1: "Tasks",
	2: "Total",
}

func (p *DescribeLogCollectorClustersResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogCollectorClustersResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTasks bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTasks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTasks {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLogCollectorClustersResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLogCollectorClustersResp[fieldId]))
}

func (p *DescribeLogCollectorClustersResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LogCollectorItem, 0, size)
	values := make([]LogCollectorItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tasks = _field
	return nil
}
func (p *DescribeLogCollectorClustersResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeLogCollectorClustersResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogCollectorClustersResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLogCollectorClustersResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogCollectorClustersResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tasks", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
		return err
	}
	for _, v := range p.Tasks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLogCollectorClustersResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLogCollectorClustersResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogCollectorClustersResp(%+v)", *p)

}

func (p *DescribeLogCollectorClustersResp) DeepEqual(ano *DescribeLogCollectorClustersResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Tasks) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeLogCollectorClustersResp) Field1DeepEqual(src []*LogCollectorItem) bool {

	if len(p.Tasks) != len(src) {
		return false
	}
	for i, v := range p.Tasks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeLogCollectorClustersResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

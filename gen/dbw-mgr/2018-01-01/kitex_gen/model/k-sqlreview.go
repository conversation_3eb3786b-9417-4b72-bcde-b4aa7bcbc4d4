// Code generated by Kitex v1.18.1. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *SqlReview) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetDbName bool = false
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetReviewSource bool = false
	var issetReviewContent bool = false
	var issetReviewStatus bool = false
	var issetComment bool = false
	var issetCreateUserName bool = false
	var issetCreateUserID bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewSource = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewContent = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetComment = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateUserID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetReviewSource {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetReviewContent {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetReviewStatus {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetComment {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserName {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserID {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReview[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SqlReview[fieldId]))
}

func (p *SqlReview) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *SqlReview) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Name = _field
	return offset, nil
}

func (p *SqlReview) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DbName = _field
	return offset, nil
}

func (p *SqlReview) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *SqlReview) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceID = _field
	return offset, nil
}

func (p *SqlReview) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewSource = _field
	return offset, nil
}

func (p *SqlReview) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewContent = _field
	return offset, nil
}

func (p *SqlReview) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewStatus = _field
	return offset, nil
}

func (p *SqlReview) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Comment = _field
	return offset, nil
}

func (p *SqlReview) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateUserName = _field
	return offset, nil
}

func (p *SqlReview) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateUserID = _field
	return offset, nil
}

func (p *SqlReview) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *SqlReview) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *SqlReview) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReview) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReview) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReview) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ID)
	return offset
}

func (p *SqlReview) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Name)
	return offset
}

func (p *SqlReview) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DbName)
	return offset
}

func (p *SqlReview) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceType)
	return offset
}

func (p *SqlReview) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceID)
	return offset
}

func (p *SqlReview) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 6)
	offset += thrift.Binary.WriteByte(buf[offset:], p.ReviewSource)
	return offset
}

func (p *SqlReview) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ReviewContent)
	return offset
}

func (p *SqlReview) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 8)
	offset += thrift.Binary.WriteByte(buf[offset:], p.ReviewStatus)
	return offset
}

func (p *SqlReview) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Comment)
	return offset
}

func (p *SqlReview) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateUserName)
	return offset
}

func (p *SqlReview) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateUserID)
	return offset
}

func (p *SqlReview) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 12)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateTime)
	return offset
}

func (p *SqlReview) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 13)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UpdateTime)
	return offset
}

func (p *SqlReview) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ID)
	return l
}

func (p *SqlReview) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Name)
	return l
}

func (p *SqlReview) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DbName)
	return l
}

func (p *SqlReview) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceType)
	return l
}

func (p *SqlReview) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceID)
	return l
}

func (p *SqlReview) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReview) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ReviewContent)
	return l
}

func (p *SqlReview) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReview) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Comment)
	return l
}

func (p *SqlReview) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateUserName)
	return l
}

func (p *SqlReview) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateUserID)
	return l
}

func (p *SqlReview) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SqlReview) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SqlReview) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReview)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ID != "" {
		p.ID = kutils.StringDeepCopy(src.ID)
	}

	if src.Name != "" {
		p.Name = kutils.StringDeepCopy(src.Name)
	}

	if src.DbName != "" {
		p.DbName = kutils.StringDeepCopy(src.DbName)
	}

	if src.InstanceType != "" {
		p.InstanceType = kutils.StringDeepCopy(src.InstanceType)
	}

	if src.InstanceID != "" {
		p.InstanceID = kutils.StringDeepCopy(src.InstanceID)
	}

	p.ReviewSource = src.ReviewSource

	if src.ReviewContent != "" {
		p.ReviewContent = kutils.StringDeepCopy(src.ReviewContent)
	}

	p.ReviewStatus = src.ReviewStatus

	if src.Comment != "" {
		p.Comment = kutils.StringDeepCopy(src.Comment)
	}

	if src.CreateUserName != "" {
		p.CreateUserName = kutils.StringDeepCopy(src.CreateUserName)
	}

	if src.CreateUserID != "" {
		p.CreateUserID = kutils.StringDeepCopy(src.CreateUserID)
	}

	p.CreateTime = src.CreateTime

	p.UpdateTime = src.UpdateTime

	return nil
}

func (p *SqlReviewDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetReviewID bool = false
	var issetSqlStatement bool = false
	var issetStatus bool = false
	var issetHighRiskCount bool = false
	var issetMiddleRiskCount bool = false
	var issetLowRiskCount bool = false
	var issetIndexAdviceCount bool = false
	var issetReviewDetailContent bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlStatement = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetHighRiskCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetMiddleRiskCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLowRiskCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIndexAdviceCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewDetailContent = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetReviewID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlStatement {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetHighRiskCount {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetMiddleRiskCount {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLowRiskCount {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetIndexAdviceCount {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetReviewDetailContent {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SqlReviewDetail[fieldId]))
}

func (p *SqlReviewDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewID = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlStatement = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Status = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HighRiskCount = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MiddleRiskCount = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LowRiskCount = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IndexAdviceCount = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField9(buf []byte) (int, error) {
	offset := 0
	_field := NewReviewResult_()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ReviewDetailContent = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *SqlReviewDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReviewDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReviewDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReviewDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ID)
	return offset
}

func (p *SqlReviewDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ReviewID)
	return offset
}

func (p *SqlReviewDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlStatement)
	return offset
}

func (p *SqlReviewDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 4)
	offset += thrift.Binary.WriteByte(buf[offset:], p.Status)
	return offset
}

func (p *SqlReviewDetail) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 5)
	offset += thrift.Binary.WriteByte(buf[offset:], p.HighRiskCount)
	return offset
}

func (p *SqlReviewDetail) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 6)
	offset += thrift.Binary.WriteByte(buf[offset:], p.MiddleRiskCount)
	return offset
}

func (p *SqlReviewDetail) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 7)
	offset += thrift.Binary.WriteByte(buf[offset:], p.LowRiskCount)
	return offset
}

func (p *SqlReviewDetail) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 8)
	offset += thrift.Binary.WriteByte(buf[offset:], p.IndexAdviceCount)
	return offset
}

func (p *SqlReviewDetail) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 9)
	offset += p.ReviewDetailContent.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SqlReviewDetail) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 10)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateTime)
	return offset
}

func (p *SqlReviewDetail) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UpdateTime)
	return offset
}

func (p *SqlReviewDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ID)
	return l
}

func (p *SqlReviewDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ReviewID)
	return l
}

func (p *SqlReviewDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlStatement)
	return l
}

func (p *SqlReviewDetail) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReviewDetail) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReviewDetail) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReviewDetail) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReviewDetail) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *SqlReviewDetail) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ReviewDetailContent.BLength()
	return l
}

func (p *SqlReviewDetail) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SqlReviewDetail) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SqlReviewDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReviewDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ID != "" {
		p.ID = kutils.StringDeepCopy(src.ID)
	}

	if src.ReviewID != "" {
		p.ReviewID = kutils.StringDeepCopy(src.ReviewID)
	}

	if src.SqlStatement != "" {
		p.SqlStatement = kutils.StringDeepCopy(src.SqlStatement)
	}

	p.Status = src.Status

	p.HighRiskCount = src.HighRiskCount

	p.MiddleRiskCount = src.MiddleRiskCount

	p.LowRiskCount = src.LowRiskCount

	p.IndexAdviceCount = src.IndexAdviceCount

	var _reviewDetailContent *ReviewResult_
	if src.ReviewDetailContent != nil {
		_reviewDetailContent = &ReviewResult_{}
		if err := _reviewDetailContent.DeepCopy(src.ReviewDetailContent); err != nil {
			return err
		}
	}
	p.ReviewDetailContent = _reviewDetailContent

	p.CreateTime = src.CreateTime

	p.UpdateTime = src.UpdateTime

	return nil
}

func (p *DescribeSqlReviewListReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewListReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeSqlReviewListReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastReadField3(buf []byte) (int, error) {
	offset := 0
	_field := NewSqlReviewSearchParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SqlReviewSearchParam = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *SortBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *OrderByForSqlReview
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := OrderByForSqlReview(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *SqlReviewListType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SqlReviewListType(v)
		_field = &tmp
	}
	p.ListType = _field
	return offset, nil
}

func (p *DescribeSqlReviewListReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewListReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewListReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewListReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeSqlReviewListReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeSqlReviewListReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlReviewSearchParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 3)
		offset += p.SqlReviewSearchParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeSqlReviewListReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortBy))
	}
	return offset
}

func (p *DescribeSqlReviewListReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderBy))
	}
	return offset
}

func (p *DescribeSqlReviewListReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetListType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ListType))
	}
	return offset
}

func (p *DescribeSqlReviewListReq) field1Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewListReq) field2Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewListReq) field3Length() int {
	l := 0
	if p.IsSetSqlReviewSearchParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.SqlReviewSearchParam.BLength()
	}
	return l
}

func (p *DescribeSqlReviewListReq) field4Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewListReq) field5Length() int {
	l := 0
	if p.IsSetOrderBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewListReq) field6Length() int {
	l := 0
	if p.IsSetListType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewListReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewListReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	var _sqlReviewSearchParam *SqlReviewSearchParam
	if src.SqlReviewSearchParam != nil {
		_sqlReviewSearchParam = &SqlReviewSearchParam{}
		if err := _sqlReviewSearchParam.DeepCopy(src.SqlReviewSearchParam); err != nil {
			return err
		}
	}
	p.SqlReviewSearchParam = _sqlReviewSearchParam

	if src.SortBy != nil {
		tmp := *src.SortBy
		p.SortBy = &tmp
	}

	if src.OrderBy != nil {
		tmp := *src.OrderBy
		p.OrderBy = &tmp
	}

	if src.ListType != nil {
		tmp := *src.ListType
		p.ListType = &tmp
	}

	return nil
}

func (p *DescribeSqlReviewListResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewList bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlReviewList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlReviewList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewListResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlReviewListResp[fieldId]))
}

func (p *DescribeSqlReviewListResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SqlReview, 0, size)
	values := make([]SqlReview, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.SqlReviewList = _field
	return offset, nil
}

func (p *DescribeSqlReviewListResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeSqlReviewListResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewListResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewListResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewListResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SqlReviewList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeSqlReviewListResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Total)
	return offset
}

func (p *DescribeSqlReviewListResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SqlReviewList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeSqlReviewListResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DescribeSqlReviewListResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewListResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlReviewList != nil {
		p.SqlReviewList = make([]*SqlReview, 0, len(src.SqlReviewList))
		for _, elem := range src.SqlReviewList {
			var _elem *SqlReview
			if elem != nil {
				_elem = &SqlReview{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.SqlReviewList = append(p.SqlReviewList, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *SqlReviewSearchParam) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewSearchParam[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SqlReviewSearchParam) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ID = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ReviewStatus = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Name = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CreateUserId = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CreateUserName = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Comment = _field
	return offset, nil
}

func (p *SqlReviewSearchParam) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReviewSearchParam) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReviewSearchParam) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReviewSearchParam) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ID)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetReviewStatus() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 2)
		offset += thrift.Binary.WriteByte(buf[offset:], *p.ReviewStatus)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Name)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceType)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCreateUserId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CreateUserId)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCreateUserName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CreateUserName)
	}
	return offset
}

func (p *SqlReviewSearchParam) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComment() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Comment)
	}
	return offset
}

func (p *SqlReviewSearchParam) field1Length() int {
	l := 0
	if p.IsSetID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ID)
	}
	return l
}

func (p *SqlReviewSearchParam) field2Length() int {
	l := 0
	if p.IsSetReviewStatus() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ByteLength()
	}
	return l
}

func (p *SqlReviewSearchParam) field3Length() int {
	l := 0
	if p.IsSetName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Name)
	}
	return l
}

func (p *SqlReviewSearchParam) field4Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceType)
	}
	return l
}

func (p *SqlReviewSearchParam) field5Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *SqlReviewSearchParam) field6Length() int {
	l := 0
	if p.IsSetCreateUserId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CreateUserId)
	}
	return l
}

func (p *SqlReviewSearchParam) field7Length() int {
	l := 0
	if p.IsSetCreateUserName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CreateUserName)
	}
	return l
}

func (p *SqlReviewSearchParam) field8Length() int {
	l := 0
	if p.IsSetComment() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Comment)
	}
	return l
}

func (p *SqlReviewSearchParam) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReviewSearchParam)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ID != nil {
		var tmp string
		if *src.ID != "" {
			tmp = kutils.StringDeepCopy(*src.ID)
		}
		p.ID = &tmp
	}

	if src.ReviewStatus != nil {
		tmp := *src.ReviewStatus
		p.ReviewStatus = &tmp
	}

	if src.Name != nil {
		var tmp string
		if *src.Name != "" {
			tmp = kutils.StringDeepCopy(*src.Name)
		}
		p.Name = &tmp
	}

	if src.InstanceType != nil {
		var tmp string
		if *src.InstanceType != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceType)
		}
		p.InstanceType = &tmp
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.CreateUserId != nil {
		var tmp string
		if *src.CreateUserId != "" {
			tmp = kutils.StringDeepCopy(*src.CreateUserId)
		}
		p.CreateUserId = &tmp
	}

	if src.CreateUserName != nil {
		var tmp string
		if *src.CreateUserName != "" {
			tmp = kutils.StringDeepCopy(*src.CreateUserName)
		}
		p.CreateUserName = &tmp
	}

	if src.Comment != nil {
		var tmp string
		if *src.Comment != "" {
			tmp = kutils.StringDeepCopy(*src.Comment)
		}
		p.Comment = &tmp
	}

	return nil
}

func (p *AddSqlReviewReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetDbName bool = false
	var issetReviewSource bool = false
	var issetReviewContent bool = false
	var issetCreateUserName bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewSource = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetReviewContent = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetReviewSource {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetReviewContent {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserName {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddSqlReviewReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_AddSqlReviewReq[fieldId]))
}

func (p *AddSqlReviewReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Name = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field InstanceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceID = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DbName = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewSource = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReviewContent = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateUserName = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Comment = _field
	return offset, nil
}

func (p *AddSqlReviewReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AddSqlReviewReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AddSqlReviewReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AddSqlReviewReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Name)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceType))
	return offset
}

func (p *AddSqlReviewReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceID)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DbName)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 5)
	offset += thrift.Binary.WriteByte(buf[offset:], p.ReviewSource)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ReviewContent)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateUserName)
	return offset
}

func (p *AddSqlReviewReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComment() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Comment)
	}
	return offset
}

func (p *AddSqlReviewReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Name)
	return l
}

func (p *AddSqlReviewReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *AddSqlReviewReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceID)
	return l
}

func (p *AddSqlReviewReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DbName)
	return l
}

func (p *AddSqlReviewReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *AddSqlReviewReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ReviewContent)
	return l
}

func (p *AddSqlReviewReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateUserName)
	return l
}

func (p *AddSqlReviewReq) field8Length() int {
	l := 0
	if p.IsSetComment() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Comment)
	}
	return l
}

func (p *AddSqlReviewReq) DeepCopy(s interface{}) error {
	src, ok := s.(*AddSqlReviewReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Name != "" {
		p.Name = kutils.StringDeepCopy(src.Name)
	}

	p.InstanceType = src.InstanceType

	if src.InstanceID != "" {
		p.InstanceID = kutils.StringDeepCopy(src.InstanceID)
	}

	if src.DbName != "" {
		p.DbName = kutils.StringDeepCopy(src.DbName)
	}

	p.ReviewSource = src.ReviewSource

	if src.ReviewContent != "" {
		p.ReviewContent = kutils.StringDeepCopy(src.ReviewContent)
	}

	if src.CreateUserName != "" {
		p.CreateUserName = kutils.StringDeepCopy(src.CreateUserName)
	}

	if src.Comment != nil {
		var tmp string
		if *src.Comment != "" {
			tmp = kutils.StringDeepCopy(*src.Comment)
		}
		p.Comment = &tmp
	}

	return nil
}

func (p *AddSqlReviewResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddSqlReviewResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_AddSqlReviewResp[fieldId]))
}

func (p *AddSqlReviewResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Success = _field
	return offset, nil
}

func (p *AddSqlReviewResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AddSqlReviewResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AddSqlReviewResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AddSqlReviewResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Success)
	return offset
}

func (p *AddSqlReviewResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *AddSqlReviewResp) DeepCopy(s interface{}) error {
	src, ok := s.(*AddSqlReviewResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Success = src.Success

	return nil
}

func (p *SubmitApproveSqlReviewReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewID bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlReviewID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlReviewID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitApproveSqlReviewReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SubmitApproveSqlReviewReq[fieldId]))
}

func (p *SubmitApproveSqlReviewReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlReviewID = _field
	return offset, nil
}

func (p *SubmitApproveSqlReviewReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SubmitApproveSqlReviewReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SubmitApproveSqlReviewReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SubmitApproveSqlReviewReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlReviewID)
	return offset
}

func (p *SubmitApproveSqlReviewReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlReviewID)
	return l
}

func (p *SubmitApproveSqlReviewReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SubmitApproveSqlReviewReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlReviewID != "" {
		p.SqlReviewID = kutils.StringDeepCopy(src.SqlReviewID)
	}

	return nil
}

func (p *SubmitApproveSqlReviewResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitApproveSqlReviewResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SubmitApproveSqlReviewResp[fieldId]))
}

func (p *SubmitApproveSqlReviewResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Success = _field
	return offset, nil
}

func (p *SubmitApproveSqlReviewResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SubmitApproveSqlReviewResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SubmitApproveSqlReviewResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SubmitApproveSqlReviewResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Success)
	return offset
}

func (p *SubmitApproveSqlReviewResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *SubmitApproveSqlReviewResp) DeepCopy(s interface{}) error {
	src, ok := s.(*SubmitApproveSqlReviewResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Success = src.Success

	return nil
}

func (p *DescribeSqlReviewDetailListReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailListReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeSqlReviewDetailListReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailListReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailListReq) FastReadField3(buf []byte) (int, error) {
	offset := 0
	_field := NewSqlReviewDetailSearchParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SqlReviewDetailSearchParam = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailListReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewDetailListReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewDetailListReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewDetailListReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeSqlReviewDetailListReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeSqlReviewDetailListReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlReviewDetailSearchParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 3)
		offset += p.SqlReviewDetailSearchParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeSqlReviewDetailListReq) field1Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewDetailListReq) field2Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlReviewDetailListReq) field3Length() int {
	l := 0
	if p.IsSetSqlReviewDetailSearchParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.SqlReviewDetailSearchParam.BLength()
	}
	return l
}

func (p *DescribeSqlReviewDetailListReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewDetailListReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	var _sqlReviewDetailSearchParam *SqlReviewDetailSearchParam
	if src.SqlReviewDetailSearchParam != nil {
		_sqlReviewDetailSearchParam = &SqlReviewDetailSearchParam{}
		if err := _sqlReviewDetailSearchParam.DeepCopy(src.SqlReviewDetailSearchParam); err != nil {
			return err
		}
	}
	p.SqlReviewDetailSearchParam = _sqlReviewDetailSearchParam

	return nil
}

func (p *SqlReviewDetailSearchParam) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewDetailSearchParam[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SqlReviewDetailSearchParam) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ReviewID = _field
	return offset, nil
}

func (p *SqlReviewDetailSearchParam) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RuleLevel = _field
	return offset, nil
}

func (p *SqlReviewDetailSearchParam) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ActionName = _field
	return offset, nil
}

func (p *SqlReviewDetailSearchParam) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReviewDetailSearchParam) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReviewDetailSearchParam) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReviewDetailSearchParam) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetReviewID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ReviewID)
	}
	return offset
}

func (p *SqlReviewDetailSearchParam) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRuleLevel() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RuleLevel)
	}
	return offset
}

func (p *SqlReviewDetailSearchParam) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetActionName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ActionName)
	}
	return offset
}

func (p *SqlReviewDetailSearchParam) field1Length() int {
	l := 0
	if p.IsSetReviewID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ReviewID)
	}
	return l
}

func (p *SqlReviewDetailSearchParam) field2Length() int {
	l := 0
	if p.IsSetRuleLevel() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RuleLevel)
	}
	return l
}

func (p *SqlReviewDetailSearchParam) field3Length() int {
	l := 0
	if p.IsSetActionName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ActionName)
	}
	return l
}

func (p *SqlReviewDetailSearchParam) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReviewDetailSearchParam)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ReviewID != nil {
		var tmp string
		if *src.ReviewID != "" {
			tmp = kutils.StringDeepCopy(*src.ReviewID)
		}
		p.ReviewID = &tmp
	}

	if src.RuleLevel != nil {
		var tmp string
		if *src.RuleLevel != "" {
			tmp = kutils.StringDeepCopy(*src.RuleLevel)
		}
		p.RuleLevel = &tmp
	}

	if src.ActionName != nil {
		var tmp string
		if *src.ActionName != "" {
			tmp = kutils.StringDeepCopy(*src.ActionName)
		}
		p.ActionName = &tmp
	}

	return nil
}

func (p *DescribeSqlReviewDetailListResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewDetailList bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlReviewDetailList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlReviewDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailListResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlReviewDetailListResp[fieldId]))
}

func (p *DescribeSqlReviewDetailListResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SqlReviewDetail, 0, size)
	values := make([]SqlReviewDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.SqlReviewDetailList = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailListResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailListResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewDetailListResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewDetailListResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewDetailListResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SqlReviewDetailList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeSqlReviewDetailListResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Total)
	return offset
}

func (p *DescribeSqlReviewDetailListResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SqlReviewDetailList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeSqlReviewDetailListResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DescribeSqlReviewDetailListResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewDetailListResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlReviewDetailList != nil {
		p.SqlReviewDetailList = make([]*SqlReviewDetail, 0, len(src.SqlReviewDetailList))
		for _, elem := range src.SqlReviewDetailList {
			var _elem *SqlReviewDetail
			if elem != nil {
				_elem = &SqlReviewDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.SqlReviewDetailList = append(p.SqlReviewDetailList, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeSqlReviewDetailSummaryResultReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailSummaryResultReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeSqlReviewDetailSummaryResultReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ReviewID = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewDetailSummaryResultReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetReviewID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ReviewID)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultReq) field1Length() int {
	l := 0
	if p.IsSetReviewID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ReviewID)
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewDetailSummaryResultReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ReviewID != nil {
		var tmp string
		if *src.ReviewID != "" {
			tmp = kutils.StringDeepCopy(*src.ReviewID)
		}
		p.ReviewID = &tmp
	}

	return nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIndexAdviceCount bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIndexAdviceCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetIndexAdviceCount {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailSummaryResultResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlReviewDetailSummaryResultResp[fieldId]))
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.HighRiskCount = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.MiddleRiskCount = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.LowRiskCount = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int8
	if v, l, err := thrift.Binary.ReadByte(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IndexAdviceCount = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.HighRiskContent = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.MiddleRiskContent = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.LowRiskContent = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastReadField8(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.IndexAdviceContent = _field
	return offset, nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetHighRiskCount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 1)
		offset += thrift.Binary.WriteByte(buf[offset:], *p.HighRiskCount)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetMiddleRiskCount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 2)
		offset += thrift.Binary.WriteByte(buf[offset:], *p.MiddleRiskCount)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLowRiskCount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 3)
		offset += thrift.Binary.WriteByte(buf[offset:], *p.LowRiskCount)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BYTE, 4)
	offset += thrift.Binary.WriteByte(buf[offset:], p.IndexAdviceCount)
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetHighRiskContent() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.HighRiskContent {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetMiddleRiskContent() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 6)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.MiddleRiskContent {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLowRiskContent() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 7)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.LowRiskContent {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIndexAdviceContent() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 8)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.IndexAdviceContent {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field1Length() int {
	l := 0
	if p.IsSetHighRiskCount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ByteLength()
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field2Length() int {
	l := 0
	if p.IsSetMiddleRiskCount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ByteLength()
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field3Length() int {
	l := 0
	if p.IsSetLowRiskCount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ByteLength()
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ByteLength()
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field5Length() int {
	l := 0
	if p.IsSetHighRiskContent() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.HighRiskContent {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field6Length() int {
	l := 0
	if p.IsSetMiddleRiskContent() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.MiddleRiskContent {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field7Length() int {
	l := 0
	if p.IsSetLowRiskContent() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.LowRiskContent {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) field8Length() int {
	l := 0
	if p.IsSetIndexAdviceContent() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.IndexAdviceContent {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeSqlReviewDetailSummaryResultResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlReviewDetailSummaryResultResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.HighRiskCount != nil {
		tmp := *src.HighRiskCount
		p.HighRiskCount = &tmp
	}

	if src.MiddleRiskCount != nil {
		tmp := *src.MiddleRiskCount
		p.MiddleRiskCount = &tmp
	}

	if src.LowRiskCount != nil {
		tmp := *src.LowRiskCount
		p.LowRiskCount = &tmp
	}

	p.IndexAdviceCount = src.IndexAdviceCount

	if src.HighRiskContent != nil {
		p.HighRiskContent = make([]*CheckResult_, 0, len(src.HighRiskContent))
		for _, elem := range src.HighRiskContent {
			var _elem *CheckResult_
			if elem != nil {
				_elem = &CheckResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.HighRiskContent = append(p.HighRiskContent, _elem)
		}
	}

	if src.MiddleRiskContent != nil {
		p.MiddleRiskContent = make([]*CheckResult_, 0, len(src.MiddleRiskContent))
		for _, elem := range src.MiddleRiskContent {
			var _elem *CheckResult_
			if elem != nil {
				_elem = &CheckResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.MiddleRiskContent = append(p.MiddleRiskContent, _elem)
		}
	}

	if src.LowRiskContent != nil {
		p.LowRiskContent = make([]*CheckResult_, 0, len(src.LowRiskContent))
		for _, elem := range src.LowRiskContent {
			var _elem *CheckResult_
			if elem != nil {
				_elem = &CheckResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.LowRiskContent = append(p.LowRiskContent, _elem)
		}
	}

	if src.IndexAdviceContent != nil {
		p.IndexAdviceContent = make([]*IndexAdviceResult_, 0, len(src.IndexAdviceContent))
		for _, elem := range src.IndexAdviceContent {
			var _elem *IndexAdviceResult_
			if elem != nil {
				_elem = &IndexAdviceResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.IndexAdviceContent = append(p.IndexAdviceContent, _elem)
		}
	}

	return nil
}

func (p *SqlReviewBySingleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewBySingleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SqlReviewBySingleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceID = _field
	return offset, nil
}

func (p *SqlReviewBySingleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DbName = _field
	return offset, nil
}

func (p *SqlReviewBySingleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *SqlReviewBySingleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SqlStatement = _field
	return offset, nil
}

func (p *SqlReviewBySingleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReviewBySingleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReviewBySingleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReviewBySingleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceID)
	}
	return offset
}

func (p *SqlReviewBySingleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDbName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.DbName)
	}
	return offset
}

func (p *SqlReviewBySingleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceType)
	}
	return offset
}

func (p *SqlReviewBySingleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlStatement() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SqlStatement)
	}
	return offset
}

func (p *SqlReviewBySingleReq) field1Length() int {
	l := 0
	if p.IsSetInstanceID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceID)
	}
	return l
}

func (p *SqlReviewBySingleReq) field2Length() int {
	l := 0
	if p.IsSetDbName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.DbName)
	}
	return l
}

func (p *SqlReviewBySingleReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceType)
	}
	return l
}

func (p *SqlReviewBySingleReq) field4Length() int {
	l := 0
	if p.IsSetSqlStatement() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SqlStatement)
	}
	return l
}

func (p *SqlReviewBySingleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReviewBySingleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceID != nil {
		var tmp string
		if *src.InstanceID != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceID)
		}
		p.InstanceID = &tmp
	}

	if src.DbName != nil {
		var tmp string
		if *src.DbName != "" {
			tmp = kutils.StringDeepCopy(*src.DbName)
		}
		p.DbName = &tmp
	}

	if src.InstanceType != nil {
		var tmp string
		if *src.InstanceType != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceType)
		}
		p.InstanceType = &tmp
	}

	if src.SqlStatement != nil {
		var tmp string
		if *src.SqlStatement != "" {
			tmp = kutils.StringDeepCopy(*src.SqlStatement)
		}
		p.SqlStatement = &tmp
	}

	return nil
}

func (p *SqlReviewBySingleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewBySingleResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SqlReviewBySingleResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.IndexAdviceResult_ = _field
	return offset, nil
}

func (p *SqlReviewBySingleResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.CheckResultList = _field
	return offset, nil
}

func (p *SqlReviewBySingleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlReviewBySingleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlReviewBySingleResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlReviewBySingleResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIndexAdviceResult_() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.IndexAdviceResult_ {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *SqlReviewBySingleResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCheckResultList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.CheckResultList {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *SqlReviewBySingleResp) field1Length() int {
	l := 0
	if p.IsSetIndexAdviceResult_() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.IndexAdviceResult_ {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *SqlReviewBySingleResp) field2Length() int {
	l := 0
	if p.IsSetCheckResultList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.CheckResultList {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *SqlReviewBySingleResp) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlReviewBySingleResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.IndexAdviceResult_ != nil {
		p.IndexAdviceResult_ = make([]*IndexAdviceResult_, 0, len(src.IndexAdviceResult_))
		for _, elem := range src.IndexAdviceResult_ {
			var _elem *IndexAdviceResult_
			if elem != nil {
				_elem = &IndexAdviceResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.IndexAdviceResult_ = append(p.IndexAdviceResult_, _elem)
		}
	}

	if src.CheckResultList != nil {
		p.CheckResultList = make([]*CheckResult_, 0, len(src.CheckResultList))
		for _, elem := range src.CheckResultList {
			var _elem *CheckResult_
			if elem != nil {
				_elem = &CheckResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.CheckResultList = append(p.CheckResultList, _elem)
		}
	}

	return nil
}

func (p *ReviewResult_) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReviewResult_[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReviewResult_) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.IndexAdviceResult_ = _field
	return offset, nil
}

func (p *ReviewResult_) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.CheckResultList = _field
	return offset, nil
}

func (p *ReviewResult_) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReviewResult_) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReviewResult_) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReviewResult_) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIndexAdviceResult_() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.IndexAdviceResult_ {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ReviewResult_) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCheckResultList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.CheckResultList {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ReviewResult_) field1Length() int {
	l := 0
	if p.IsSetIndexAdviceResult_() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.IndexAdviceResult_ {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ReviewResult_) field2Length() int {
	l := 0
	if p.IsSetCheckResultList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.CheckResultList {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ReviewResult_) DeepCopy(s interface{}) error {
	src, ok := s.(*ReviewResult_)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.IndexAdviceResult_ != nil {
		p.IndexAdviceResult_ = make([]*IndexAdviceResult_, 0, len(src.IndexAdviceResult_))
		for _, elem := range src.IndexAdviceResult_ {
			var _elem *IndexAdviceResult_
			if elem != nil {
				_elem = &IndexAdviceResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.IndexAdviceResult_ = append(p.IndexAdviceResult_, _elem)
		}
	}

	if src.CheckResultList != nil {
		p.CheckResultList = make([]*CheckResult_, 0, len(src.CheckResultList))
		for _, elem := range src.CheckResultList {
			var _elem *CheckResult_
			if elem != nil {
				_elem = &CheckResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.CheckResultList = append(p.CheckResultList, _elem)
		}
	}

	return nil
}

func (p *IndexAdviceResult_) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTableName bool = false
	var issetDDLs bool = false
	var issetAdviceIndexColumns bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDDLs = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexColumns = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTableName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDDLs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexColumns {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndexAdviceResult_[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_IndexAdviceResult_[fieldId]))
}

func (p *IndexAdviceResult_) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TableName = _field
	return offset, nil
}

func (p *IndexAdviceResult_) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.DDLs = _field
	return offset, nil
}

func (p *IndexAdviceResult_) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.AdviceIndexColumns = _field
	return offset, nil
}

func (p *IndexAdviceResult_) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.SQLTemplates = _field
	return offset, nil
}

func (p *IndexAdviceResult_) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SQL = _field
	return offset, nil
}

func (p *IndexAdviceResult_) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *IndexAdviceResult_) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *IndexAdviceResult_) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *IndexAdviceResult_) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TableName)
	return offset
}

func (p *IndexAdviceResult_) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DDLs {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *IndexAdviceResult_) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.AdviceIndexColumns {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *IndexAdviceResult_) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSQLTemplates() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.SQLTemplates {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *IndexAdviceResult_) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSQL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SQL)
	}
	return offset
}

func (p *IndexAdviceResult_) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TableName)
	return l
}

func (p *IndexAdviceResult_) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DDLs {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *IndexAdviceResult_) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.AdviceIndexColumns {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *IndexAdviceResult_) field4Length() int {
	l := 0
	if p.IsSetSQLTemplates() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.SQLTemplates {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *IndexAdviceResult_) field5Length() int {
	l := 0
	if p.IsSetSQL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SQL)
	}
	return l
}

func (p *IndexAdviceResult_) DeepCopy(s interface{}) error {
	src, ok := s.(*IndexAdviceResult_)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TableName != "" {
		p.TableName = kutils.StringDeepCopy(src.TableName)
	}

	if src.DDLs != nil {
		p.DDLs = make([]string, 0, len(src.DDLs))
		for _, elem := range src.DDLs {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.DDLs = append(p.DDLs, _elem)
		}
	}

	if src.AdviceIndexColumns != nil {
		p.AdviceIndexColumns = make([]string, 0, len(src.AdviceIndexColumns))
		for _, elem := range src.AdviceIndexColumns {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.AdviceIndexColumns = append(p.AdviceIndexColumns, _elem)
		}
	}

	if src.SQLTemplates != nil {
		p.SQLTemplates = make([]string, 0, len(src.SQLTemplates))
		for _, elem := range src.SQLTemplates {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.SQLTemplates = append(p.SQLTemplates, _elem)
		}
	}

	if src.SQL != nil {
		var tmp string
		if *src.SQL != "" {
			tmp = kutils.StringDeepCopy(*src.SQL)
		}
		p.SQL = &tmp
	}

	return nil
}

func (p *CheckResult_) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckResult_[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CheckResult_) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ErrorMsg = _field
	return offset, nil
}

func (p *CheckResult_) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ActionName = _field
	return offset, nil
}

func (p *CheckResult_) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Level = _field
	return offset, nil
}

func (p *CheckResult_) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CheckResult_) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CheckResult_) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CheckResult_) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetErrorMsg() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ErrorMsg)
	}
	return offset
}

func (p *CheckResult_) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetActionName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ActionName)
	}
	return offset
}

func (p *CheckResult_) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLevel() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Level)
	}
	return offset
}

func (p *CheckResult_) field1Length() int {
	l := 0
	if p.IsSetErrorMsg() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ErrorMsg)
	}
	return l
}

func (p *CheckResult_) field2Length() int {
	l := 0
	if p.IsSetActionName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ActionName)
	}
	return l
}

func (p *CheckResult_) field3Length() int {
	l := 0
	if p.IsSetLevel() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Level)
	}
	return l
}

func (p *CheckResult_) DeepCopy(s interface{}) error {
	src, ok := s.(*CheckResult_)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ErrorMsg != nil {
		var tmp string
		if *src.ErrorMsg != "" {
			tmp = kutils.StringDeepCopy(*src.ErrorMsg)
		}
		p.ErrorMsg = &tmp
	}

	if src.ActionName != nil {
		var tmp string
		if *src.ActionName != "" {
			tmp = kutils.StringDeepCopy(*src.ActionName)
		}
		p.ActionName = &tmp
	}

	if src.Level != nil {
		var tmp string
		if *src.Level != "" {
			tmp = kutils.StringDeepCopy(*src.Level)
		}
		p.Level = &tmp
	}

	return nil
}

func (p *DescribeReviewDetailActionListReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeReviewDetailActionListReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeReviewDetailActionListReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ReviewID = _field
	return offset, nil
}

func (p *DescribeReviewDetailActionListReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeReviewDetailActionListReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeReviewDetailActionListReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeReviewDetailActionListReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetReviewID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ReviewID)
	}
	return offset
}

func (p *DescribeReviewDetailActionListReq) field1Length() int {
	l := 0
	if p.IsSetReviewID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ReviewID)
	}
	return l
}

func (p *DescribeReviewDetailActionListReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeReviewDetailActionListReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ReviewID != nil {
		var tmp string
		if *src.ReviewID != "" {
			tmp = kutils.StringDeepCopy(*src.ReviewID)
		}
		p.ReviewID = &tmp
	}

	return nil
}

func (p *DescribeReviewDetailActionListResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeReviewDetailActionListResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeReviewDetailActionListResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.ActionNameList = _field
	return offset, nil
}

func (p *DescribeReviewDetailActionListResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeReviewDetailActionListResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeReviewDetailActionListResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeReviewDetailActionListResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetActionNameList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.ActionNameList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DescribeReviewDetailActionListResp) field1Length() int {
	l := 0
	if p.IsSetActionNameList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.ActionNameList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DescribeReviewDetailActionListResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeReviewDetailActionListResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ActionNameList != nil {
		p.ActionNameList = make([]string, 0, len(src.ActionNameList))
		for _, elem := range src.ActionNameList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.ActionNameList = append(p.ActionNameList, _elem)
		}
	}

	return nil
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DataConnectInstanceReq) IsValid() error {
	return nil
}
func (p *DataConnectInstanceResp) IsValid() error {
	return nil
}
func (p *DataExecCommandsReq) IsValid() error {
	return nil
}
func (p *DataExecCommandsResp) IsValid() error {
	return nil
}
func (p *DataExecCommandSetAsyncReq) IsValid() error {
	if p.CommandInfo != nil {
		if err := p.CommandInfo.IsValid(); err != nil {
			return fmt.Errorf("field CommandInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CommandInfo) IsValid() error {
	return nil
}
func (p *DataExecCommandSetAsyncResp) IsValid() error {
	return nil
}
func (p *DataDescribeCommandSetReq) IsValid() error {
	return nil
}
func (p *DataDescribeCommandSetResp) IsValid() error {
	return nil
}
func (p *DataGetCommandSetResultReq) IsValid() error {
	return nil
}
func (p *DataGetCommandSetResultResp) IsValid() error {
	return nil
}
func (p *DataSessionKeepAliveReq) IsValid() error {
	return nil
}
func (p *DataSessionKeepAliveResp) IsValid() error {
	return nil
}
func (p *DataCloseSessionReq) IsValid() error {
	return nil
}
func (p *DataCloseSessionResp) IsValid() error {
	return nil
}
func (p *DataCancelExecReq) IsValid() error {
	return nil
}
func (p *DataCancelExecResp) IsValid() error {
	return nil
}
func (p *DescribeAuditLogConfigReq) IsValid() error {
	return nil
}
func (p *DescribeAuditLogConfigResp) IsValid() error {
	return nil
}
func (p *ModifyAuditLogConfigReq) IsValid() error {
	return nil
}
func (p *ModifyAuditLogConfigResp) IsValid() error {
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiReq) IsValid() error {
	return nil
}
func (p *SlowQueryAdviceTaskHistoryApiResp) IsValid() error {
	return nil
}
func (p *ListSlowQueryAdviceApiReq) IsValid() error {
	return nil
}
func (p *ListSlowQueryAdviceApiResp) IsValid() error {
	return nil
}
func (p *GenerateSQLFromNLReq) IsValid() error {
	return nil
}
func (p *GenerateSQLFromNLResp) IsValid() error {
	return nil
}

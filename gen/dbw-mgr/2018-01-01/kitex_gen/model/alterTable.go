// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type AlterTableReq struct {
	SessionId string     `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB        string     `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	TableName string     `thrift:"TableName,3,required" frugal:"3,required,string" json:"TableName"`
	TableMeta *TableMeta `thrift:"TableMeta,4,required" frugal:"4,required,TableMeta" json:"TableMeta"`
}

func NewAlterTableReq() *AlterTableReq {
	return &AlterTableReq{}
}

func (p *AlterTableReq) InitDefault() {
}

func (p *AlterTableReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *AlterTableReq) GetDB() (v string) {
	return p.DB
}

func (p *AlterTableReq) GetTableName() (v string) {
	return p.TableName
}

var AlterTableReq_TableMeta_DEFAULT *TableMeta

func (p *AlterTableReq) GetTableMeta() (v *TableMeta) {
	if !p.IsSetTableMeta() {
		return AlterTableReq_TableMeta_DEFAULT
	}
	return p.TableMeta
}
func (p *AlterTableReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *AlterTableReq) SetDB(val string) {
	p.DB = val
}
func (p *AlterTableReq) SetTableName(val string) {
	p.TableName = val
}
func (p *AlterTableReq) SetTableMeta(val *TableMeta) {
	p.TableMeta = val
}

var fieldIDToName_AlterTableReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "TableName",
	4: "TableMeta",
}

func (p *AlterTableReq) IsSetTableMeta() bool {
	return p.TableMeta != nil
}

func (p *AlterTableReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AlterTableReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false
	var issetTableName bool = false
	var issetTableMeta bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableMeta = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTableMeta {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AlterTableReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AlterTableReq[fieldId]))
}

func (p *AlterTableReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *AlterTableReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *AlterTableReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *AlterTableReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewTableMeta()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TableMeta = _field
	return nil
}

func (p *AlterTableReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AlterTableReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AlterTableReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AlterTableReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AlterTableReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AlterTableReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AlterTableReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableMeta", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TableMeta.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AlterTableReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AlterTableReq(%+v)", *p)

}

func (p *AlterTableReq) DeepEqual(ano *AlterTableReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field4DeepEqual(ano.TableMeta) {
		return false
	}
	return true
}

func (p *AlterTableReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *AlterTableReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *AlterTableReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *AlterTableReq) Field4DeepEqual(src *TableMeta) bool {

	if !p.TableMeta.DeepEqual(src) {
		return false
	}
	return true
}

type AlterTableResp struct {
	Statement string `thrift:"Statement,1,required" frugal:"1,required,string" json:"Statement"`
}

func NewAlterTableResp() *AlterTableResp {
	return &AlterTableResp{}
}

func (p *AlterTableResp) InitDefault() {
}

func (p *AlterTableResp) GetStatement() (v string) {
	return p.Statement
}
func (p *AlterTableResp) SetStatement(val string) {
	p.Statement = val
}

var fieldIDToName_AlterTableResp = map[int16]string{
	1: "Statement",
}

func (p *AlterTableResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AlterTableResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatement bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStatement {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AlterTableResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AlterTableResp[fieldId]))
}

func (p *AlterTableResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}

func (p *AlterTableResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AlterTableResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AlterTableResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AlterTableResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AlterTableResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AlterTableResp(%+v)", *p)

}

func (p *AlterTableResp) DeepEqual(ano *AlterTableResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Statement) {
		return false
	}
	return true
}

func (p *AlterTableResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}

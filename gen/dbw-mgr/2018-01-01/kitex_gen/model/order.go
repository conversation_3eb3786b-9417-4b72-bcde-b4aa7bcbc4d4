// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ProductType int64

const (
	ProductType_AuditLogCollectTrafficCharges ProductType = 1
	ProductType_SqlInsightStorageCharges      ProductType = 2
	ProductType_AuditLogRedisCharges          ProductType = 3
	ProductType_AuditLogMongoCharges          ProductType = 4
)

func (p ProductType) String() string {
	switch p {
	case ProductType_AuditLogCollectTrafficCharges:
		return "AuditLogCollectTrafficCharges"
	case ProductType_SqlInsightStorageCharges:
		return "SqlInsightStorageCharges"
	case ProductType_AuditLogRedisCharges:
		return "AuditLogRedisCharges"
	case ProductType_AuditLogMongoCharges:
		return "AuditLogMongoCharges"
	}
	return "<UNSET>"
}

func ProductTypeFromString(s string) (ProductType, error) {
	switch s {
	case "AuditLogCollectTrafficCharges":
		return ProductType_AuditLogCollectTrafficCharges, nil
	case "SqlInsightStorageCharges":
		return ProductType_SqlInsightStorageCharges, nil
	case "AuditLogRedisCharges":
		return ProductType_AuditLogRedisCharges, nil
	case "AuditLogMongoCharges":
		return ProductType_AuditLogMongoCharges, nil
	}
	return ProductType(0), fmt.Errorf("not a valid ProductType string")
}

func ProductTypePtr(v ProductType) *ProductType { return &v }

func (p ProductType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ProductType) UnmarshalText(text []byte) error {
	q, err := ProductTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ProductConfigCode int64

const (
	ProductConfigCode_log_collect       ProductConfigCode = 1
	ProductConfigCode_log_storage       ProductConfigCode = 2
	ProductConfigCode_vip_Instance      ProductConfigCode = 3
	ProductConfigCode_Redis_audit       ProductConfigCode = 4
	ProductConfigCode_MySQL_sql_insight ProductConfigCode = 5
	ProductConfigCode_vedb_sql_insight  ProductConfigCode = 6
	ProductConfigCode_MongoDB_audit     ProductConfigCode = 7
)

func (p ProductConfigCode) String() string {
	switch p {
	case ProductConfigCode_log_collect:
		return "log_collect"
	case ProductConfigCode_log_storage:
		return "log_storage"
	case ProductConfigCode_vip_Instance:
		return "vip_Instance"
	case ProductConfigCode_Redis_audit:
		return "Redis_audit"
	case ProductConfigCode_MySQL_sql_insight:
		return "MySQL_sql_insight"
	case ProductConfigCode_vedb_sql_insight:
		return "vedb_sql_insight"
	case ProductConfigCode_MongoDB_audit:
		return "MongoDB_audit"
	}
	return "<UNSET>"
}

func ProductConfigCodeFromString(s string) (ProductConfigCode, error) {
	switch s {
	case "log_collect":
		return ProductConfigCode_log_collect, nil
	case "log_storage":
		return ProductConfigCode_log_storage, nil
	case "vip_Instance":
		return ProductConfigCode_vip_Instance, nil
	case "Redis_audit":
		return ProductConfigCode_Redis_audit, nil
	case "MySQL_sql_insight":
		return ProductConfigCode_MySQL_sql_insight, nil
	case "vedb_sql_insight":
		return ProductConfigCode_vedb_sql_insight, nil
	case "MongoDB_audit":
		return ProductConfigCode_MongoDB_audit, nil
	}
	return ProductConfigCode(0), fmt.Errorf("not a valid ProductConfigCode string")
}

func ProductConfigCodePtr(v ProductConfigCode) *ProductConfigCode { return &v }

func (p ProductConfigCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ProductConfigCode) UnmarshalText(text []byte) error {
	q, err := ProductConfigCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ChargeItem int64

const (
	ChargeItem_log_storage                     ChargeItem = 1
	ChargeItem_log_collect                     ChargeItem = 2
	ChargeItem_vip_Instance                    ChargeItem = 3
	ChargeItem_bandwidth                       ChargeItem = 4
	ChargeItem_redis_log_storage_audit         ChargeItem = 5
	ChargeItem_redis_log_collect_audit         ChargeItem = 8
	ChargeItem_redis_index_storage_audit       ChargeItem = 9
	ChargeItem_redis_index_traffic_audit       ChargeItem = 10
	ChargeItem_mysql_log_storage_sql_insight   ChargeItem = 6
	ChargeItem_mysql_index_storage_sql_insight ChargeItem = 11
	ChargeItem_mysql_log_collect_sql_insight   ChargeItem = 12
	ChargeItem_mysql_aggregation_sql_insight   ChargeItem = 13
	ChargeItem_mysql_index_traffic_sql_insight ChargeItem = 14
	ChargeItem_vedb_log_storage_sql_insight    ChargeItem = 7
	ChargeItem_vedb_index_storage_sql_insight  ChargeItem = 15
	ChargeItem_vedb_log_collect_sql_insight    ChargeItem = 16
	ChargeItem_vedb_aggregation_sql_insight    ChargeItem = 17
	ChargeItem_vedb_index_traffic_sql_insight  ChargeItem = 18
	ChargeItem_mongo_log_storage_audit         ChargeItem = 19
	ChargeItem_mongo_index_storage_audit       ChargeItem = 20
	ChargeItem_mongo_log_collect_audit         ChargeItem = 21
	ChargeItem_mongo_aggregation_insight       ChargeItem = 22
	ChargeItem_mongo_index_traffic_audit       ChargeItem = 23
)

func (p ChargeItem) String() string {
	switch p {
	case ChargeItem_log_storage:
		return "log_storage"
	case ChargeItem_log_collect:
		return "log_collect"
	case ChargeItem_vip_Instance:
		return "vip_Instance"
	case ChargeItem_bandwidth:
		return "bandwidth"
	case ChargeItem_redis_log_storage_audit:
		return "redis_log_storage_audit"
	case ChargeItem_redis_log_collect_audit:
		return "redis_log_collect_audit"
	case ChargeItem_redis_index_storage_audit:
		return "redis_index_storage_audit"
	case ChargeItem_redis_index_traffic_audit:
		return "redis_index_traffic_audit"
	case ChargeItem_mysql_log_storage_sql_insight:
		return "mysql_log_storage_sql_insight"
	case ChargeItem_mysql_index_storage_sql_insight:
		return "mysql_index_storage_sql_insight"
	case ChargeItem_mysql_log_collect_sql_insight:
		return "mysql_log_collect_sql_insight"
	case ChargeItem_mysql_aggregation_sql_insight:
		return "mysql_aggregation_sql_insight"
	case ChargeItem_mysql_index_traffic_sql_insight:
		return "mysql_index_traffic_sql_insight"
	case ChargeItem_vedb_log_storage_sql_insight:
		return "vedb_log_storage_sql_insight"
	case ChargeItem_vedb_index_storage_sql_insight:
		return "vedb_index_storage_sql_insight"
	case ChargeItem_vedb_log_collect_sql_insight:
		return "vedb_log_collect_sql_insight"
	case ChargeItem_vedb_aggregation_sql_insight:
		return "vedb_aggregation_sql_insight"
	case ChargeItem_vedb_index_traffic_sql_insight:
		return "vedb_index_traffic_sql_insight"
	case ChargeItem_mongo_log_storage_audit:
		return "mongo_log_storage_audit"
	case ChargeItem_mongo_index_storage_audit:
		return "mongo_index_storage_audit"
	case ChargeItem_mongo_log_collect_audit:
		return "mongo_log_collect_audit"
	case ChargeItem_mongo_aggregation_insight:
		return "mongo_aggregation_insight"
	case ChargeItem_mongo_index_traffic_audit:
		return "mongo_index_traffic_audit"
	}
	return "<UNSET>"
}

func ChargeItemFromString(s string) (ChargeItem, error) {
	switch s {
	case "log_storage":
		return ChargeItem_log_storage, nil
	case "log_collect":
		return ChargeItem_log_collect, nil
	case "vip_Instance":
		return ChargeItem_vip_Instance, nil
	case "bandwidth":
		return ChargeItem_bandwidth, nil
	case "redis_log_storage_audit":
		return ChargeItem_redis_log_storage_audit, nil
	case "redis_log_collect_audit":
		return ChargeItem_redis_log_collect_audit, nil
	case "redis_index_storage_audit":
		return ChargeItem_redis_index_storage_audit, nil
	case "redis_index_traffic_audit":
		return ChargeItem_redis_index_traffic_audit, nil
	case "mysql_log_storage_sql_insight":
		return ChargeItem_mysql_log_storage_sql_insight, nil
	case "mysql_index_storage_sql_insight":
		return ChargeItem_mysql_index_storage_sql_insight, nil
	case "mysql_log_collect_sql_insight":
		return ChargeItem_mysql_log_collect_sql_insight, nil
	case "mysql_aggregation_sql_insight":
		return ChargeItem_mysql_aggregation_sql_insight, nil
	case "mysql_index_traffic_sql_insight":
		return ChargeItem_mysql_index_traffic_sql_insight, nil
	case "vedb_log_storage_sql_insight":
		return ChargeItem_vedb_log_storage_sql_insight, nil
	case "vedb_index_storage_sql_insight":
		return ChargeItem_vedb_index_storage_sql_insight, nil
	case "vedb_log_collect_sql_insight":
		return ChargeItem_vedb_log_collect_sql_insight, nil
	case "vedb_aggregation_sql_insight":
		return ChargeItem_vedb_aggregation_sql_insight, nil
	case "vedb_index_traffic_sql_insight":
		return ChargeItem_vedb_index_traffic_sql_insight, nil
	case "mongo_log_storage_audit":
		return ChargeItem_mongo_log_storage_audit, nil
	case "mongo_index_storage_audit":
		return ChargeItem_mongo_index_storage_audit, nil
	case "mongo_log_collect_audit":
		return ChargeItem_mongo_log_collect_audit, nil
	case "mongo_aggregation_insight":
		return ChargeItem_mongo_aggregation_insight, nil
	case "mongo_index_traffic_audit":
		return ChargeItem_mongo_index_traffic_audit, nil
	}
	return ChargeItem(0), fmt.Errorf("not a valid ChargeItem string")
}

func ChargeItemPtr(v ChargeItem) *ChargeItem { return &v }

func (p ChargeItem) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ChargeItem) UnmarshalText(text []byte) error {
	q, err := ChargeItemFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ChargeType int64

const (
	ChargeType_NotEnabled ChargeType = 0
	ChargeType_PostPaid   ChargeType = 1
	ChargeType_PrePaid    ChargeType = 2
)

func (p ChargeType) String() string {
	switch p {
	case ChargeType_NotEnabled:
		return "NotEnabled"
	case ChargeType_PostPaid:
		return "PostPaid"
	case ChargeType_PrePaid:
		return "PrePaid"
	}
	return "<UNSET>"
}

func ChargeTypeFromString(s string) (ChargeType, error) {
	switch s {
	case "NotEnabled":
		return ChargeType_NotEnabled, nil
	case "PostPaid":
		return ChargeType_PostPaid, nil
	case "PrePaid":
		return ChargeType_PrePaid, nil
	}
	return ChargeType(0), fmt.Errorf("not a valid ChargeType string")
}

func ChargeTypePtr(v ChargeType) *ChargeType { return &v }

func (p ChargeType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ChargeType) UnmarshalText(text []byte) error {
	q, err := ChargeTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type MeasureUnit int64

const (
	MeasureUnit_KB MeasureUnit = 1
	MeasureUnit_MB MeasureUnit = 2
	MeasureUnit_GB MeasureUnit = 3
	MeasureUnit_TB MeasureUnit = 4
)

func (p MeasureUnit) String() string {
	switch p {
	case MeasureUnit_KB:
		return "KB"
	case MeasureUnit_MB:
		return "MB"
	case MeasureUnit_GB:
		return "GB"
	case MeasureUnit_TB:
		return "TB"
	}
	return "<UNSET>"
}

func MeasureUnitFromString(s string) (MeasureUnit, error) {
	switch s {
	case "KB":
		return MeasureUnit_KB, nil
	case "MB":
		return MeasureUnit_MB, nil
	case "GB":
		return MeasureUnit_GB, nil
	case "TB":
		return MeasureUnit_TB, nil
	}
	return MeasureUnit(0), fmt.Errorf("not a valid MeasureUnit string")
}

func MeasureUnitPtr(v MeasureUnit) *MeasureUnit { return &v }

func (p MeasureUnit) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *MeasureUnit) UnmarshalText(text []byte) error {
	q, err := MeasureUnitFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeInstancePriceReq struct {
	ProductType  ProductType       `thrift:"ProductType,1,required" frugal:"1,required,ProductType" json:"ProductType"`
	RegionId     string            `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	Spec         *InstanceSpecInfo `thrift:"Spec,3,optional" frugal:"3,optional,InstanceSpecInfo" json:"Spec,omitempty"`
	ChargeType   *ChargeType       `thrift:"ChargeType,4,optional" frugal:"4,optional,ChargeType" json:"ChargeType,omitempty"`
	InstanceType DSType            `thrift:"InstanceType,5,required" frugal:"5,required,DSType" json:"InstanceType"`
}

func NewDescribeInstancePriceReq() *DescribeInstancePriceReq {
	return &DescribeInstancePriceReq{}
}

func (p *DescribeInstancePriceReq) InitDefault() {
}

func (p *DescribeInstancePriceReq) GetProductType() (v ProductType) {
	return p.ProductType
}

func (p *DescribeInstancePriceReq) GetRegionId() (v string) {
	return p.RegionId
}

var DescribeInstancePriceReq_Spec_DEFAULT *InstanceSpecInfo

func (p *DescribeInstancePriceReq) GetSpec() (v *InstanceSpecInfo) {
	if !p.IsSetSpec() {
		return DescribeInstancePriceReq_Spec_DEFAULT
	}
	return p.Spec
}

var DescribeInstancePriceReq_ChargeType_DEFAULT ChargeType

func (p *DescribeInstancePriceReq) GetChargeType() (v ChargeType) {
	if !p.IsSetChargeType() {
		return DescribeInstancePriceReq_ChargeType_DEFAULT
	}
	return *p.ChargeType
}

func (p *DescribeInstancePriceReq) GetInstanceType() (v DSType) {
	return p.InstanceType
}
func (p *DescribeInstancePriceReq) SetProductType(val ProductType) {
	p.ProductType = val
}
func (p *DescribeInstancePriceReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeInstancePriceReq) SetSpec(val *InstanceSpecInfo) {
	p.Spec = val
}
func (p *DescribeInstancePriceReq) SetChargeType(val *ChargeType) {
	p.ChargeType = val
}
func (p *DescribeInstancePriceReq) SetInstanceType(val DSType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeInstancePriceReq = map[int16]string{
	1: "ProductType",
	2: "RegionId",
	3: "Spec",
	4: "ChargeType",
	5: "InstanceType",
}

func (p *DescribeInstancePriceReq) IsSetSpec() bool {
	return p.Spec != nil
}

func (p *DescribeInstancePriceReq) IsSetChargeType() bool {
	return p.ChargeType != nil
}

func (p *DescribeInstancePriceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancePriceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProductType bool = false
	var issetRegionId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProductType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProductType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancePriceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstancePriceReq[fieldId]))
}

func (p *DescribeInstancePriceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field ProductType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductType(v)
	}
	p.ProductType = _field
	return nil
}
func (p *DescribeInstancePriceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeInstancePriceReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewInstanceSpecInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Spec = _field
	return nil
}
func (p *DescribeInstancePriceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *ChargeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ChargeType(v)
		_field = &tmp
	}
	p.ChargeType = _field
	return nil
}
func (p *DescribeInstancePriceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeInstancePriceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancePriceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstancePriceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstancePriceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProductType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstancePriceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstancePriceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpec() {
		if err = oprot.WriteFieldBegin("Spec", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Spec.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstancePriceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeType() {
		if err = oprot.WriteFieldBegin("ChargeType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ChargeType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstancePriceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstancePriceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstancePriceReq(%+v)", *p)

}

func (p *DescribeInstancePriceReq) DeepEqual(ano *DescribeInstancePriceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProductType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Spec) {
		return false
	}
	if !p.Field4DeepEqual(ano.ChargeType) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeInstancePriceReq) Field1DeepEqual(src ProductType) bool {

	if p.ProductType != src {
		return false
	}
	return true
}
func (p *DescribeInstancePriceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstancePriceReq) Field3DeepEqual(src *InstanceSpecInfo) bool {

	if !p.Spec.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeInstancePriceReq) Field4DeepEqual(src *ChargeType) bool {

	if p.ChargeType == src {
		return true
	} else if p.ChargeType == nil || src == nil {
		return false
	}
	if *p.ChargeType != *src {
		return false
	}
	return true
}
func (p *DescribeInstancePriceReq) Field5DeepEqual(src DSType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}

type InstanceSpecInfo struct {
}

func NewInstanceSpecInfo() *InstanceSpecInfo {
	return &InstanceSpecInfo{}
}

func (p *InstanceSpecInfo) InitDefault() {
}

var fieldIDToName_InstanceSpecInfo = map[int16]string{}

func (p *InstanceSpecInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceSpecInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InstanceSpecInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceSpecInfo")

	if err = oprot.WriteStructBegin("InstanceSpecInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceSpecInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceSpecInfo(%+v)", *p)

}

func (p *InstanceSpecInfo) DeepEqual(ano *InstanceSpecInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeInstancePriceResp struct {
	Price *InstanceSpecPrice `thrift:"Price,1,optional" frugal:"1,optional,InstanceSpecPrice" json:"Price,omitempty"`
}

func NewDescribeInstancePriceResp() *DescribeInstancePriceResp {
	return &DescribeInstancePriceResp{}
}

func (p *DescribeInstancePriceResp) InitDefault() {
}

var DescribeInstancePriceResp_Price_DEFAULT *InstanceSpecPrice

func (p *DescribeInstancePriceResp) GetPrice() (v *InstanceSpecPrice) {
	if !p.IsSetPrice() {
		return DescribeInstancePriceResp_Price_DEFAULT
	}
	return p.Price
}
func (p *DescribeInstancePriceResp) SetPrice(val *InstanceSpecPrice) {
	p.Price = val
}

var fieldIDToName_DescribeInstancePriceResp = map[int16]string{
	1: "Price",
}

func (p *DescribeInstancePriceResp) IsSetPrice() bool {
	return p.Price != nil
}

func (p *DescribeInstancePriceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancePriceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancePriceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeInstancePriceResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInstanceSpecPrice()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Price = _field
	return nil
}

func (p *DescribeInstancePriceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancePriceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstancePriceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstancePriceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrice() {
		if err = oprot.WriteFieldBegin("Price", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Price.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstancePriceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstancePriceResp(%+v)", *p)

}

func (p *DescribeInstancePriceResp) DeepEqual(ano *DescribeInstancePriceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Price) {
		return false
	}
	return true
}

func (p *DescribeInstancePriceResp) Field1DeepEqual(src *InstanceSpecPrice) bool {

	if !p.Price.DeepEqual(src) {
		return false
	}
	return true
}

type OrderNotifyAuditReq struct {
	MsgID           string `thrift:"MsgID,1,required" frugal:"1,required,string" json:"MsgID"`
	Success         bool   `thrift:"Success,2,required" frugal:"2,required,bool" json:"Success"`
	NotifyOrderType string `thrift:"NotifyOrderType,3,required" frugal:"3,required,string" json:"NotifyOrderType"`
}

func NewOrderNotifyAuditReq() *OrderNotifyAuditReq {
	return &OrderNotifyAuditReq{}
}

func (p *OrderNotifyAuditReq) InitDefault() {
}

func (p *OrderNotifyAuditReq) GetMsgID() (v string) {
	return p.MsgID
}

func (p *OrderNotifyAuditReq) GetSuccess() (v bool) {
	return p.Success
}

func (p *OrderNotifyAuditReq) GetNotifyOrderType() (v string) {
	return p.NotifyOrderType
}
func (p *OrderNotifyAuditReq) SetMsgID(val string) {
	p.MsgID = val
}
func (p *OrderNotifyAuditReq) SetSuccess(val bool) {
	p.Success = val
}
func (p *OrderNotifyAuditReq) SetNotifyOrderType(val string) {
	p.NotifyOrderType = val
}

var fieldIDToName_OrderNotifyAuditReq = map[int16]string{
	1: "MsgID",
	2: "Success",
	3: "NotifyOrderType",
}

func (p *OrderNotifyAuditReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OrderNotifyAuditReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMsgID bool = false
	var issetSuccess bool = false
	var issetNotifyOrderType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsgID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNotifyOrderType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMsgID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSuccess {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNotifyOrderType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderNotifyAuditReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_OrderNotifyAuditReq[fieldId]))
}

func (p *OrderNotifyAuditReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MsgID = _field
	return nil
}
func (p *OrderNotifyAuditReq) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *OrderNotifyAuditReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NotifyOrderType = _field
	return nil
}

func (p *OrderNotifyAuditReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OrderNotifyAuditReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderNotifyAuditReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderNotifyAuditReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MsgID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MsgID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OrderNotifyAuditReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OrderNotifyAuditReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NotifyOrderType", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NotifyOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *OrderNotifyAuditReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderNotifyAuditReq(%+v)", *p)

}

func (p *OrderNotifyAuditReq) DeepEqual(ano *OrderNotifyAuditReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MsgID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Success) {
		return false
	}
	if !p.Field3DeepEqual(ano.NotifyOrderType) {
		return false
	}
	return true
}

func (p *OrderNotifyAuditReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MsgID, src) != 0 {
		return false
	}
	return true
}
func (p *OrderNotifyAuditReq) Field2DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *OrderNotifyAuditReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.NotifyOrderType, src) != 0 {
		return false
	}
	return true
}

type OrderNotifyAuditResp struct {
}

func NewOrderNotifyAuditResp() *OrderNotifyAuditResp {
	return &OrderNotifyAuditResp{}
}

func (p *OrderNotifyAuditResp) InitDefault() {
}

var fieldIDToName_OrderNotifyAuditResp = map[int16]string{}

func (p *OrderNotifyAuditResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OrderNotifyAuditResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderNotifyAuditResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OrderNotifyAuditResp")

	if err = oprot.WriteStructBegin("OrderNotifyAuditResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderNotifyAuditResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderNotifyAuditResp(%+v)", *p)

}

func (p *OrderNotifyAuditResp) DeepEqual(ano *OrderNotifyAuditResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type InstanceSpecPrice struct {
	InstanceID       *string            `thrift:"InstanceID,1,optional" frugal:"1,optional,string" json:"InstanceID,omitempty"`
	OriginalPrice    *float64           `thrift:"OriginalPrice,2,optional" frugal:"2,optional,double" json:"OriginalPrice,omitempty"`
	DiscountPrice    *float64           `thrift:"DiscountPrice,3,optional" frugal:"3,optional,double" json:"DiscountPrice,omitempty"`
	PayablePrice     *float64           `thrift:"PayablePrice,4,optional" frugal:"4,optional,double" json:"PayablePrice,omitempty"`
	BillingMethod    *string            `thrift:"BillingMethod,5,optional" frugal:"5,optional,string" json:"BillingMethod,omitempty"`
	Currency         *string            `thrift:"Currency,6,optional" frugal:"6,optional,string" json:"Currency,omitempty"`
	ChargeItemPrices []*ChargeItemPrice `thrift:"ChargeItemPrices,7,optional" frugal:"7,optional,list<ChargeItemPrice>" json:"ChargeItemPrices,omitempty"`
}

func NewInstanceSpecPrice() *InstanceSpecPrice {
	return &InstanceSpecPrice{}
}

func (p *InstanceSpecPrice) InitDefault() {
}

var InstanceSpecPrice_InstanceID_DEFAULT string

func (p *InstanceSpecPrice) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return InstanceSpecPrice_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var InstanceSpecPrice_OriginalPrice_DEFAULT float64

func (p *InstanceSpecPrice) GetOriginalPrice() (v float64) {
	if !p.IsSetOriginalPrice() {
		return InstanceSpecPrice_OriginalPrice_DEFAULT
	}
	return *p.OriginalPrice
}

var InstanceSpecPrice_DiscountPrice_DEFAULT float64

func (p *InstanceSpecPrice) GetDiscountPrice() (v float64) {
	if !p.IsSetDiscountPrice() {
		return InstanceSpecPrice_DiscountPrice_DEFAULT
	}
	return *p.DiscountPrice
}

var InstanceSpecPrice_PayablePrice_DEFAULT float64

func (p *InstanceSpecPrice) GetPayablePrice() (v float64) {
	if !p.IsSetPayablePrice() {
		return InstanceSpecPrice_PayablePrice_DEFAULT
	}
	return *p.PayablePrice
}

var InstanceSpecPrice_BillingMethod_DEFAULT string

func (p *InstanceSpecPrice) GetBillingMethod() (v string) {
	if !p.IsSetBillingMethod() {
		return InstanceSpecPrice_BillingMethod_DEFAULT
	}
	return *p.BillingMethod
}

var InstanceSpecPrice_Currency_DEFAULT string

func (p *InstanceSpecPrice) GetCurrency() (v string) {
	if !p.IsSetCurrency() {
		return InstanceSpecPrice_Currency_DEFAULT
	}
	return *p.Currency
}

var InstanceSpecPrice_ChargeItemPrices_DEFAULT []*ChargeItemPrice

func (p *InstanceSpecPrice) GetChargeItemPrices() (v []*ChargeItemPrice) {
	if !p.IsSetChargeItemPrices() {
		return InstanceSpecPrice_ChargeItemPrices_DEFAULT
	}
	return p.ChargeItemPrices
}
func (p *InstanceSpecPrice) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *InstanceSpecPrice) SetOriginalPrice(val *float64) {
	p.OriginalPrice = val
}
func (p *InstanceSpecPrice) SetDiscountPrice(val *float64) {
	p.DiscountPrice = val
}
func (p *InstanceSpecPrice) SetPayablePrice(val *float64) {
	p.PayablePrice = val
}
func (p *InstanceSpecPrice) SetBillingMethod(val *string) {
	p.BillingMethod = val
}
func (p *InstanceSpecPrice) SetCurrency(val *string) {
	p.Currency = val
}
func (p *InstanceSpecPrice) SetChargeItemPrices(val []*ChargeItemPrice) {
	p.ChargeItemPrices = val
}

var fieldIDToName_InstanceSpecPrice = map[int16]string{
	1: "InstanceID",
	2: "OriginalPrice",
	3: "DiscountPrice",
	4: "PayablePrice",
	5: "BillingMethod",
	6: "Currency",
	7: "ChargeItemPrices",
}

func (p *InstanceSpecPrice) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *InstanceSpecPrice) IsSetOriginalPrice() bool {
	return p.OriginalPrice != nil
}

func (p *InstanceSpecPrice) IsSetDiscountPrice() bool {
	return p.DiscountPrice != nil
}

func (p *InstanceSpecPrice) IsSetPayablePrice() bool {
	return p.PayablePrice != nil
}

func (p *InstanceSpecPrice) IsSetBillingMethod() bool {
	return p.BillingMethod != nil
}

func (p *InstanceSpecPrice) IsSetCurrency() bool {
	return p.Currency != nil
}

func (p *InstanceSpecPrice) IsSetChargeItemPrices() bool {
	return p.ChargeItemPrices != nil
}

func (p *InstanceSpecPrice) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceSpecPrice")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceSpecPrice[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InstanceSpecPrice) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OriginalPrice = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DiscountPrice = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PayablePrice = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BillingMethod = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Currency = _field
	return nil
}
func (p *InstanceSpecPrice) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeItemPrice, 0, size)
	values := make([]ChargeItemPrice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeItemPrices = _field
	return nil
}

func (p *InstanceSpecPrice) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceSpecPrice")

	var fieldId int16
	if err = oprot.WriteStructBegin("InstanceSpecPrice"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOriginalPrice() {
		if err = oprot.WriteFieldBegin("OriginalPrice", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.OriginalPrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDiscountPrice() {
		if err = oprot.WriteFieldBegin("DiscountPrice", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.DiscountPrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayablePrice() {
		if err = oprot.WriteFieldBegin("PayablePrice", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.PayablePrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBillingMethod() {
		if err = oprot.WriteFieldBegin("BillingMethod", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BillingMethod); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCurrency() {
		if err = oprot.WriteFieldBegin("Currency", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Currency); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InstanceSpecPrice) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeItemPrices() {
		if err = oprot.WriteFieldBegin("ChargeItemPrices", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeItemPrices)); err != nil {
			return err
		}
		for _, v := range p.ChargeItemPrices {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InstanceSpecPrice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceSpecPrice(%+v)", *p)

}

func (p *InstanceSpecPrice) DeepEqual(ano *InstanceSpecPrice) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.OriginalPrice) {
		return false
	}
	if !p.Field3DeepEqual(ano.DiscountPrice) {
		return false
	}
	if !p.Field4DeepEqual(ano.PayablePrice) {
		return false
	}
	if !p.Field5DeepEqual(ano.BillingMethod) {
		return false
	}
	if !p.Field6DeepEqual(ano.Currency) {
		return false
	}
	if !p.Field7DeepEqual(ano.ChargeItemPrices) {
		return false
	}
	return true
}

func (p *InstanceSpecPrice) Field1DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field2DeepEqual(src *float64) bool {

	if p.OriginalPrice == src {
		return true
	} else if p.OriginalPrice == nil || src == nil {
		return false
	}
	if *p.OriginalPrice != *src {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field3DeepEqual(src *float64) bool {

	if p.DiscountPrice == src {
		return true
	} else if p.DiscountPrice == nil || src == nil {
		return false
	}
	if *p.DiscountPrice != *src {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field4DeepEqual(src *float64) bool {

	if p.PayablePrice == src {
		return true
	} else if p.PayablePrice == nil || src == nil {
		return false
	}
	if *p.PayablePrice != *src {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field5DeepEqual(src *string) bool {

	if p.BillingMethod == src {
		return true
	} else if p.BillingMethod == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BillingMethod, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field6DeepEqual(src *string) bool {

	if p.Currency == src {
		return true
	} else if p.Currency == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Currency, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceSpecPrice) Field7DeepEqual(src []*ChargeItemPrice) bool {

	if len(p.ChargeItemPrices) != len(src) {
		return false
	}
	for i, v := range p.ChargeItemPrices {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ChargeItemPrice struct {
	ChargeItemCode     *string  `thrift:"ChargeItemCode,1,optional" frugal:"1,optional,string" json:"ChargeItemCode,omitempty"`
	OriginalPrice      *float64 `thrift:"OriginalPrice,2,optional" frugal:"2,optional,double" json:"OriginalPrice,omitempty"`
	DiscountPrice      *float64 `thrift:"DiscountPrice,3,optional" frugal:"3,optional,double" json:"DiscountPrice,omitempty"`
	PayablePrice       *float64 `thrift:"PayablePrice,4,optional" frugal:"4,optional,double" json:"PayablePrice,omitempty"`
	UnitPrice          *float64 `thrift:"UnitPrice,5,optional" frugal:"5,optional,double" json:"UnitPrice,omitempty"`
	BillingMethod      *string  `thrift:"BillingMethod,6,optional" frugal:"6,optional,string" json:"BillingMethod,omitempty"`
	Quantity           *int32   `thrift:"Quantity,7,optional" frugal:"7,optional,i32" json:"Quantity,omitempty"`
	ChargeItem         *string  `thrift:"ChargeItem,8,optional" frugal:"8,optional,string" json:"ChargeItem,omitempty"`
	DescribeChargeItem *string  `thrift:"DescribeChargeItem,9,optional" frugal:"9,optional,string" json:"DescribeChargeItem,omitempty"`
	ChargeItemUnitCN   *string  `thrift:"ChargeItemUnitCN,10,optional" frugal:"10,optional,string" json:"ChargeItemUnitCN,omitempty"`
	ChargeItemUnitEN   *string  `thrift:"ChargeItemUnitEN,11,optional" frugal:"11,optional,string" json:"ChargeItemUnitEN,omitempty"`
}

func NewChargeItemPrice() *ChargeItemPrice {
	return &ChargeItemPrice{}
}

func (p *ChargeItemPrice) InitDefault() {
}

var ChargeItemPrice_ChargeItemCode_DEFAULT string

func (p *ChargeItemPrice) GetChargeItemCode() (v string) {
	if !p.IsSetChargeItemCode() {
		return ChargeItemPrice_ChargeItemCode_DEFAULT
	}
	return *p.ChargeItemCode
}

var ChargeItemPrice_OriginalPrice_DEFAULT float64

func (p *ChargeItemPrice) GetOriginalPrice() (v float64) {
	if !p.IsSetOriginalPrice() {
		return ChargeItemPrice_OriginalPrice_DEFAULT
	}
	return *p.OriginalPrice
}

var ChargeItemPrice_DiscountPrice_DEFAULT float64

func (p *ChargeItemPrice) GetDiscountPrice() (v float64) {
	if !p.IsSetDiscountPrice() {
		return ChargeItemPrice_DiscountPrice_DEFAULT
	}
	return *p.DiscountPrice
}

var ChargeItemPrice_PayablePrice_DEFAULT float64

func (p *ChargeItemPrice) GetPayablePrice() (v float64) {
	if !p.IsSetPayablePrice() {
		return ChargeItemPrice_PayablePrice_DEFAULT
	}
	return *p.PayablePrice
}

var ChargeItemPrice_UnitPrice_DEFAULT float64

func (p *ChargeItemPrice) GetUnitPrice() (v float64) {
	if !p.IsSetUnitPrice() {
		return ChargeItemPrice_UnitPrice_DEFAULT
	}
	return *p.UnitPrice
}

var ChargeItemPrice_BillingMethod_DEFAULT string

func (p *ChargeItemPrice) GetBillingMethod() (v string) {
	if !p.IsSetBillingMethod() {
		return ChargeItemPrice_BillingMethod_DEFAULT
	}
	return *p.BillingMethod
}

var ChargeItemPrice_Quantity_DEFAULT int32

func (p *ChargeItemPrice) GetQuantity() (v int32) {
	if !p.IsSetQuantity() {
		return ChargeItemPrice_Quantity_DEFAULT
	}
	return *p.Quantity
}

var ChargeItemPrice_ChargeItem_DEFAULT string

func (p *ChargeItemPrice) GetChargeItem() (v string) {
	if !p.IsSetChargeItem() {
		return ChargeItemPrice_ChargeItem_DEFAULT
	}
	return *p.ChargeItem
}

var ChargeItemPrice_DescribeChargeItem_DEFAULT string

func (p *ChargeItemPrice) GetDescribeChargeItem() (v string) {
	if !p.IsSetDescribeChargeItem() {
		return ChargeItemPrice_DescribeChargeItem_DEFAULT
	}
	return *p.DescribeChargeItem
}

var ChargeItemPrice_ChargeItemUnitCN_DEFAULT string

func (p *ChargeItemPrice) GetChargeItemUnitCN() (v string) {
	if !p.IsSetChargeItemUnitCN() {
		return ChargeItemPrice_ChargeItemUnitCN_DEFAULT
	}
	return *p.ChargeItemUnitCN
}

var ChargeItemPrice_ChargeItemUnitEN_DEFAULT string

func (p *ChargeItemPrice) GetChargeItemUnitEN() (v string) {
	if !p.IsSetChargeItemUnitEN() {
		return ChargeItemPrice_ChargeItemUnitEN_DEFAULT
	}
	return *p.ChargeItemUnitEN
}
func (p *ChargeItemPrice) SetChargeItemCode(val *string) {
	p.ChargeItemCode = val
}
func (p *ChargeItemPrice) SetOriginalPrice(val *float64) {
	p.OriginalPrice = val
}
func (p *ChargeItemPrice) SetDiscountPrice(val *float64) {
	p.DiscountPrice = val
}
func (p *ChargeItemPrice) SetPayablePrice(val *float64) {
	p.PayablePrice = val
}
func (p *ChargeItemPrice) SetUnitPrice(val *float64) {
	p.UnitPrice = val
}
func (p *ChargeItemPrice) SetBillingMethod(val *string) {
	p.BillingMethod = val
}
func (p *ChargeItemPrice) SetQuantity(val *int32) {
	p.Quantity = val
}
func (p *ChargeItemPrice) SetChargeItem(val *string) {
	p.ChargeItem = val
}
func (p *ChargeItemPrice) SetDescribeChargeItem(val *string) {
	p.DescribeChargeItem = val
}
func (p *ChargeItemPrice) SetChargeItemUnitCN(val *string) {
	p.ChargeItemUnitCN = val
}
func (p *ChargeItemPrice) SetChargeItemUnitEN(val *string) {
	p.ChargeItemUnitEN = val
}

var fieldIDToName_ChargeItemPrice = map[int16]string{
	1:  "ChargeItemCode",
	2:  "OriginalPrice",
	3:  "DiscountPrice",
	4:  "PayablePrice",
	5:  "UnitPrice",
	6:  "BillingMethod",
	7:  "Quantity",
	8:  "ChargeItem",
	9:  "DescribeChargeItem",
	10: "ChargeItemUnitCN",
	11: "ChargeItemUnitEN",
}

func (p *ChargeItemPrice) IsSetChargeItemCode() bool {
	return p.ChargeItemCode != nil
}

func (p *ChargeItemPrice) IsSetOriginalPrice() bool {
	return p.OriginalPrice != nil
}

func (p *ChargeItemPrice) IsSetDiscountPrice() bool {
	return p.DiscountPrice != nil
}

func (p *ChargeItemPrice) IsSetPayablePrice() bool {
	return p.PayablePrice != nil
}

func (p *ChargeItemPrice) IsSetUnitPrice() bool {
	return p.UnitPrice != nil
}

func (p *ChargeItemPrice) IsSetBillingMethod() bool {
	return p.BillingMethod != nil
}

func (p *ChargeItemPrice) IsSetQuantity() bool {
	return p.Quantity != nil
}

func (p *ChargeItemPrice) IsSetChargeItem() bool {
	return p.ChargeItem != nil
}

func (p *ChargeItemPrice) IsSetDescribeChargeItem() bool {
	return p.DescribeChargeItem != nil
}

func (p *ChargeItemPrice) IsSetChargeItemUnitCN() bool {
	return p.ChargeItemUnitCN != nil
}

func (p *ChargeItemPrice) IsSetChargeItemUnitEN() bool {
	return p.ChargeItemUnitEN != nil
}

func (p *ChargeItemPrice) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChargeItemPrice")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeItemPrice[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChargeItemPrice) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChargeItemCode = _field
	return nil
}
func (p *ChargeItemPrice) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OriginalPrice = _field
	return nil
}
func (p *ChargeItemPrice) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DiscountPrice = _field
	return nil
}
func (p *ChargeItemPrice) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PayablePrice = _field
	return nil
}
func (p *ChargeItemPrice) ReadField5(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UnitPrice = _field
	return nil
}
func (p *ChargeItemPrice) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BillingMethod = _field
	return nil
}
func (p *ChargeItemPrice) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Quantity = _field
	return nil
}
func (p *ChargeItemPrice) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChargeItem = _field
	return nil
}
func (p *ChargeItemPrice) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DescribeChargeItem = _field
	return nil
}
func (p *ChargeItemPrice) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChargeItemUnitCN = _field
	return nil
}
func (p *ChargeItemPrice) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChargeItemUnitEN = _field
	return nil
}

func (p *ChargeItemPrice) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChargeItemPrice")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeItemPrice"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeItemPrice) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeItemCode() {
		if err = oprot.WriteFieldBegin("ChargeItemCode", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChargeItemCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOriginalPrice() {
		if err = oprot.WriteFieldBegin("OriginalPrice", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.OriginalPrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDiscountPrice() {
		if err = oprot.WriteFieldBegin("DiscountPrice", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.DiscountPrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayablePrice() {
		if err = oprot.WriteFieldBegin("PayablePrice", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.PayablePrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetUnitPrice() {
		if err = oprot.WriteFieldBegin("UnitPrice", thrift.DOUBLE, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.UnitPrice); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetBillingMethod() {
		if err = oprot.WriteFieldBegin("BillingMethod", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BillingMethod); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetQuantity() {
		if err = oprot.WriteFieldBegin("Quantity", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Quantity); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeItem() {
		if err = oprot.WriteFieldBegin("ChargeItem", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChargeItem); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeChargeItem() {
		if err = oprot.WriteFieldBegin("DescribeChargeItem", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DescribeChargeItem); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeItemUnitCN() {
		if err = oprot.WriteFieldBegin("ChargeItemUnitCN", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChargeItemUnitCN); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ChargeItemPrice) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetChargeItemUnitEN() {
		if err = oprot.WriteFieldBegin("ChargeItemUnitEN", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChargeItemUnitEN); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ChargeItemPrice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeItemPrice(%+v)", *p)

}

func (p *ChargeItemPrice) DeepEqual(ano *ChargeItemPrice) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChargeItemCode) {
		return false
	}
	if !p.Field2DeepEqual(ano.OriginalPrice) {
		return false
	}
	if !p.Field3DeepEqual(ano.DiscountPrice) {
		return false
	}
	if !p.Field4DeepEqual(ano.PayablePrice) {
		return false
	}
	if !p.Field5DeepEqual(ano.UnitPrice) {
		return false
	}
	if !p.Field6DeepEqual(ano.BillingMethod) {
		return false
	}
	if !p.Field7DeepEqual(ano.Quantity) {
		return false
	}
	if !p.Field8DeepEqual(ano.ChargeItem) {
		return false
	}
	if !p.Field9DeepEqual(ano.DescribeChargeItem) {
		return false
	}
	if !p.Field10DeepEqual(ano.ChargeItemUnitCN) {
		return false
	}
	if !p.Field11DeepEqual(ano.ChargeItemUnitEN) {
		return false
	}
	return true
}

func (p *ChargeItemPrice) Field1DeepEqual(src *string) bool {

	if p.ChargeItemCode == src {
		return true
	} else if p.ChargeItemCode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChargeItemCode, *src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field2DeepEqual(src *float64) bool {

	if p.OriginalPrice == src {
		return true
	} else if p.OriginalPrice == nil || src == nil {
		return false
	}
	if *p.OriginalPrice != *src {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field3DeepEqual(src *float64) bool {

	if p.DiscountPrice == src {
		return true
	} else if p.DiscountPrice == nil || src == nil {
		return false
	}
	if *p.DiscountPrice != *src {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field4DeepEqual(src *float64) bool {

	if p.PayablePrice == src {
		return true
	} else if p.PayablePrice == nil || src == nil {
		return false
	}
	if *p.PayablePrice != *src {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field5DeepEqual(src *float64) bool {

	if p.UnitPrice == src {
		return true
	} else if p.UnitPrice == nil || src == nil {
		return false
	}
	if *p.UnitPrice != *src {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field6DeepEqual(src *string) bool {

	if p.BillingMethod == src {
		return true
	} else if p.BillingMethod == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BillingMethod, *src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field7DeepEqual(src *int32) bool {

	if p.Quantity == src {
		return true
	} else if p.Quantity == nil || src == nil {
		return false
	}
	if *p.Quantity != *src {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field8DeepEqual(src *string) bool {

	if p.ChargeItem == src {
		return true
	} else if p.ChargeItem == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChargeItem, *src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field9DeepEqual(src *string) bool {

	if p.DescribeChargeItem == src {
		return true
	} else if p.DescribeChargeItem == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DescribeChargeItem, *src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field10DeepEqual(src *string) bool {

	if p.ChargeItemUnitCN == src {
		return true
	} else if p.ChargeItemUnitCN == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChargeItemUnitCN, *src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemPrice) Field11DeepEqual(src *string) bool {

	if p.ChargeItemUnitEN == src {
		return true
	} else if p.ChargeItemUnitEN == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChargeItemUnitEN, *src) != 0 {
		return false
	}
	return true
}

type CreateAuditOrder struct {
	AuditServiceID string     `thrift:"AuditServiceID,1,required" frugal:"1,required,string" json:"AuditServiceID"`
	ChargeType     ChargeType `thrift:"ChargeType,2,required" frugal:"2,required,ChargeType" json:"ChargeType"`
}

func NewCreateAuditOrder() *CreateAuditOrder {
	return &CreateAuditOrder{}
}

func (p *CreateAuditOrder) InitDefault() {
}

func (p *CreateAuditOrder) GetAuditServiceID() (v string) {
	return p.AuditServiceID
}

func (p *CreateAuditOrder) GetChargeType() (v ChargeType) {
	return p.ChargeType
}
func (p *CreateAuditOrder) SetAuditServiceID(val string) {
	p.AuditServiceID = val
}
func (p *CreateAuditOrder) SetChargeType(val ChargeType) {
	p.ChargeType = val
}

var fieldIDToName_CreateAuditOrder = map[int16]string{
	1: "AuditServiceID",
	2: "ChargeType",
}

func (p *CreateAuditOrder) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAuditOrder")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAuditServiceID bool = false
	var issetChargeType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAuditServiceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChargeType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAuditServiceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChargeType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAuditOrder[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAuditOrder[fieldId]))
}

func (p *CreateAuditOrder) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AuditServiceID = _field
	return nil
}
func (p *CreateAuditOrder) ReadField2(iprot thrift.TProtocol) error {

	var _field ChargeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChargeType(v)
	}
	p.ChargeType = _field
	return nil
}

func (p *CreateAuditOrder) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAuditOrder")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAuditOrder"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAuditOrder) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AuditServiceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AuditServiceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAuditOrder) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChargeType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ChargeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateAuditOrder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAuditOrder(%+v)", *p)

}

func (p *CreateAuditOrder) DeepEqual(ano *CreateAuditOrder) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AuditServiceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChargeType) {
		return false
	}
	return true
}

func (p *CreateAuditOrder) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AuditServiceID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAuditOrder) Field2DeepEqual(src ChargeType) bool {

	if p.ChargeType != src {
		return false
	}
	return true
}

type ReportAllInstanceMeasureReq struct {
	AccountantTime int64       `thrift:"AccountantTime,1,required" frugal:"1,required,i64" json:"AccountantTime"`
	RegionID       string      `thrift:"RegionID,2,required" frugal:"2,required,string" json:"RegionID"`
	MeasureUnit    MeasureUnit `thrift:"MeasureUnit,3,required" frugal:"3,required,MeasureUnit" json:"MeasureUnit"`
}

func NewReportAllInstanceMeasureReq() *ReportAllInstanceMeasureReq {
	return &ReportAllInstanceMeasureReq{}
}

func (p *ReportAllInstanceMeasureReq) InitDefault() {
}

func (p *ReportAllInstanceMeasureReq) GetAccountantTime() (v int64) {
	return p.AccountantTime
}

func (p *ReportAllInstanceMeasureReq) GetRegionID() (v string) {
	return p.RegionID
}

func (p *ReportAllInstanceMeasureReq) GetMeasureUnit() (v MeasureUnit) {
	return p.MeasureUnit
}
func (p *ReportAllInstanceMeasureReq) SetAccountantTime(val int64) {
	p.AccountantTime = val
}
func (p *ReportAllInstanceMeasureReq) SetRegionID(val string) {
	p.RegionID = val
}
func (p *ReportAllInstanceMeasureReq) SetMeasureUnit(val MeasureUnit) {
	p.MeasureUnit = val
}

var fieldIDToName_ReportAllInstanceMeasureReq = map[int16]string{
	1: "AccountantTime",
	2: "RegionID",
	3: "MeasureUnit",
}

func (p *ReportAllInstanceMeasureReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportAllInstanceMeasureReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAccountantTime bool = false
	var issetRegionID bool = false
	var issetMeasureUnit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountantTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMeasureUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAccountantTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMeasureUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReportAllInstanceMeasureReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReportAllInstanceMeasureReq[fieldId]))
}

func (p *ReportAllInstanceMeasureReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountantTime = _field
	return nil
}
func (p *ReportAllInstanceMeasureReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionID = _field
	return nil
}
func (p *ReportAllInstanceMeasureReq) ReadField3(iprot thrift.TProtocol) error {

	var _field MeasureUnit
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = MeasureUnit(v)
	}
	p.MeasureUnit = _field
	return nil
}

func (p *ReportAllInstanceMeasureReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportAllInstanceMeasureReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReportAllInstanceMeasureReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReportAllInstanceMeasureReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountantTime", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.AccountantTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReportAllInstanceMeasureReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ReportAllInstanceMeasureReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MeasureUnit", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.MeasureUnit)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ReportAllInstanceMeasureReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportAllInstanceMeasureReq(%+v)", *p)

}

func (p *ReportAllInstanceMeasureReq) DeepEqual(ano *ReportAllInstanceMeasureReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AccountantTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionID) {
		return false
	}
	if !p.Field3DeepEqual(ano.MeasureUnit) {
		return false
	}
	return true
}

func (p *ReportAllInstanceMeasureReq) Field1DeepEqual(src int64) bool {

	if p.AccountantTime != src {
		return false
	}
	return true
}
func (p *ReportAllInstanceMeasureReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionID, src) != 0 {
		return false
	}
	return true
}
func (p *ReportAllInstanceMeasureReq) Field3DeepEqual(src MeasureUnit) bool {

	if p.MeasureUnit != src {
		return false
	}
	return true
}

type ReportAllInstanceMeasureResp struct {
	Complete     bool  `thrift:"complete,1,required" frugal:"1,required,bool" json:"complete"`
	CompleteTime int64 `thrift:"completeTime,2,required" frugal:"2,required,i64" json:"completeTime"`
}

func NewReportAllInstanceMeasureResp() *ReportAllInstanceMeasureResp {
	return &ReportAllInstanceMeasureResp{}
}

func (p *ReportAllInstanceMeasureResp) InitDefault() {
}

func (p *ReportAllInstanceMeasureResp) GetComplete() (v bool) {
	return p.Complete
}

func (p *ReportAllInstanceMeasureResp) GetCompleteTime() (v int64) {
	return p.CompleteTime
}
func (p *ReportAllInstanceMeasureResp) SetComplete(val bool) {
	p.Complete = val
}
func (p *ReportAllInstanceMeasureResp) SetCompleteTime(val int64) {
	p.CompleteTime = val
}

var fieldIDToName_ReportAllInstanceMeasureResp = map[int16]string{
	1: "complete",
	2: "completeTime",
}

func (p *ReportAllInstanceMeasureResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportAllInstanceMeasureResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetComplete bool = false
	var issetCompleteTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetComplete = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCompleteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetComplete {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCompleteTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReportAllInstanceMeasureResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReportAllInstanceMeasureResp[fieldId]))
}

func (p *ReportAllInstanceMeasureResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Complete = _field
	return nil
}
func (p *ReportAllInstanceMeasureResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CompleteTime = _field
	return nil
}

func (p *ReportAllInstanceMeasureResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportAllInstanceMeasureResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReportAllInstanceMeasureResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReportAllInstanceMeasureResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("complete", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Complete); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReportAllInstanceMeasureResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("completeTime", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CompleteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ReportAllInstanceMeasureResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportAllInstanceMeasureResp(%+v)", *p)

}

func (p *ReportAllInstanceMeasureResp) DeepEqual(ano *ReportAllInstanceMeasureResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Complete) {
		return false
	}
	if !p.Field2DeepEqual(ano.CompleteTime) {
		return false
	}
	return true
}

func (p *ReportAllInstanceMeasureResp) Field1DeepEqual(src bool) bool {

	if p.Complete != src {
		return false
	}
	return true
}
func (p *ReportAllInstanceMeasureResp) Field2DeepEqual(src int64) bool {

	if p.CompleteTime != src {
		return false
	}
	return true
}

type DescribeInstanceChargeItemUsageReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	StartTime  *int32 `thrift:"StartTime,3,optional" frugal:"3,optional,i32" json:"StartTime,omitempty"`
	EndTime    *int32 `thrift:"EndTime,4,optional" frugal:"4,optional,i32" json:"EndTime,omitempty"`
}

func NewDescribeInstanceChargeItemUsageReq() *DescribeInstanceChargeItemUsageReq {
	return &DescribeInstanceChargeItemUsageReq{}
}

func (p *DescribeInstanceChargeItemUsageReq) InitDefault() {
}

func (p *DescribeInstanceChargeItemUsageReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeInstanceChargeItemUsageReq_StartTime_DEFAULT int32

func (p *DescribeInstanceChargeItemUsageReq) GetStartTime() (v int32) {
	if !p.IsSetStartTime() {
		return DescribeInstanceChargeItemUsageReq_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DescribeInstanceChargeItemUsageReq_EndTime_DEFAULT int32

func (p *DescribeInstanceChargeItemUsageReq) GetEndTime() (v int32) {
	if !p.IsSetEndTime() {
		return DescribeInstanceChargeItemUsageReq_EndTime_DEFAULT
	}
	return *p.EndTime
}
func (p *DescribeInstanceChargeItemUsageReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeInstanceChargeItemUsageReq) SetStartTime(val *int32) {
	p.StartTime = val
}
func (p *DescribeInstanceChargeItemUsageReq) SetEndTime(val *int32) {
	p.EndTime = val
}

var fieldIDToName_DescribeInstanceChargeItemUsageReq = map[int16]string{
	1: "InstanceId",
	3: "StartTime",
	4: "EndTime",
}

func (p *DescribeInstanceChargeItemUsageReq) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DescribeInstanceChargeItemUsageReq) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DescribeInstanceChargeItemUsageReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceChargeItemUsageReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceChargeItemUsageReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceChargeItemUsageReq[fieldId]))
}

func (p *DescribeInstanceChargeItemUsageReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeInstanceChargeItemUsageReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeInstanceChargeItemUsageReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}

func (p *DescribeInstanceChargeItemUsageReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceChargeItemUsageReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceChargeItemUsageReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceChargeItemUsageReq(%+v)", *p)

}

func (p *DescribeInstanceChargeItemUsageReq) DeepEqual(ano *DescribeInstanceChargeItemUsageReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *DescribeInstanceChargeItemUsageReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceChargeItemUsageReq) Field3DeepEqual(src *int32) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if *p.StartTime != *src {
		return false
	}
	return true
}
func (p *DescribeInstanceChargeItemUsageReq) Field4DeepEqual(src *int32) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if *p.EndTime != *src {
		return false
	}
	return true
}

type DescribeInstanceChargeItemUsageResp struct {
	ChargeItemUsages []*ChargeItemUsage `thrift:"ChargeItemUsages,1,required" frugal:"1,required,list<ChargeItemUsage>" json:"ChargeItemUsages"`
}

func NewDescribeInstanceChargeItemUsageResp() *DescribeInstanceChargeItemUsageResp {
	return &DescribeInstanceChargeItemUsageResp{}
}

func (p *DescribeInstanceChargeItemUsageResp) InitDefault() {
}

func (p *DescribeInstanceChargeItemUsageResp) GetChargeItemUsages() (v []*ChargeItemUsage) {
	return p.ChargeItemUsages
}
func (p *DescribeInstanceChargeItemUsageResp) SetChargeItemUsages(val []*ChargeItemUsage) {
	p.ChargeItemUsages = val
}

var fieldIDToName_DescribeInstanceChargeItemUsageResp = map[int16]string{
	1: "ChargeItemUsages",
}

func (p *DescribeInstanceChargeItemUsageResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceChargeItemUsageResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChargeItemUsages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChargeItemUsages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChargeItemUsages {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceChargeItemUsageResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceChargeItemUsageResp[fieldId]))
}

func (p *DescribeInstanceChargeItemUsageResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeItemUsage, 0, size)
	values := make([]ChargeItemUsage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeItemUsages = _field
	return nil
}

func (p *DescribeInstanceChargeItemUsageResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceChargeItemUsageResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceChargeItemUsageResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChargeItemUsages", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeItemUsages)); err != nil {
		return err
	}
	for _, v := range p.ChargeItemUsages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceChargeItemUsageResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceChargeItemUsageResp(%+v)", *p)

}

func (p *DescribeInstanceChargeItemUsageResp) DeepEqual(ano *DescribeInstanceChargeItemUsageResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChargeItemUsages) {
		return false
	}
	return true
}

func (p *DescribeInstanceChargeItemUsageResp) Field1DeepEqual(src []*ChargeItemUsage) bool {

	if len(p.ChargeItemUsages) != len(src) {
		return false
	}
	for i, v := range p.ChargeItemUsages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ChargeItemUsage struct {
	InstanceId           string             `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ChargeItem           string             `thrift:"ChargeItem,2,required" frugal:"2,required,string" json:"ChargeItem"`
	UsageMeasurementUnit string             `thrift:"UsageMeasurementUnit,3,required" frugal:"3,required,string" json:"UsageMeasurementUnit"`
	Usages               []*AccountantUsage `thrift:"Usages,4,required" frugal:"4,required,list<AccountantUsage>" json:"Usages"`
}

func NewChargeItemUsage() *ChargeItemUsage {
	return &ChargeItemUsage{}
}

func (p *ChargeItemUsage) InitDefault() {
}

func (p *ChargeItemUsage) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ChargeItemUsage) GetChargeItem() (v string) {
	return p.ChargeItem
}

func (p *ChargeItemUsage) GetUsageMeasurementUnit() (v string) {
	return p.UsageMeasurementUnit
}

func (p *ChargeItemUsage) GetUsages() (v []*AccountantUsage) {
	return p.Usages
}
func (p *ChargeItemUsage) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ChargeItemUsage) SetChargeItem(val string) {
	p.ChargeItem = val
}
func (p *ChargeItemUsage) SetUsageMeasurementUnit(val string) {
	p.UsageMeasurementUnit = val
}
func (p *ChargeItemUsage) SetUsages(val []*AccountantUsage) {
	p.Usages = val
}

var fieldIDToName_ChargeItemUsage = map[int16]string{
	1: "InstanceId",
	2: "ChargeItem",
	3: "UsageMeasurementUnit",
	4: "Usages",
}

func (p *ChargeItemUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChargeItemUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetChargeItem bool = false
	var issetUsageMeasurementUnit bool = false
	var issetUsages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChargeItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUsageMeasurementUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUsages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChargeItem {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUsageMeasurementUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUsages {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeItemUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChargeItemUsage[fieldId]))
}

func (p *ChargeItemUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ChargeItemUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChargeItem = _field
	return nil
}
func (p *ChargeItemUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UsageMeasurementUnit = _field
	return nil
}
func (p *ChargeItemUsage) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AccountantUsage, 0, size)
	values := make([]AccountantUsage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Usages = _field
	return nil
}

func (p *ChargeItemUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChargeItemUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeItemUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeItemUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChargeItemUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChargeItem", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChargeItem); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChargeItemUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UsageMeasurementUnit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UsageMeasurementUnit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ChargeItemUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Usages", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Usages)); err != nil {
		return err
	}
	for _, v := range p.Usages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ChargeItemUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeItemUsage(%+v)", *p)

}

func (p *ChargeItemUsage) DeepEqual(ano *ChargeItemUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChargeItem) {
		return false
	}
	if !p.Field3DeepEqual(ano.UsageMeasurementUnit) {
		return false
	}
	if !p.Field4DeepEqual(ano.Usages) {
		return false
	}
	return true
}

func (p *ChargeItemUsage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemUsage) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ChargeItem, src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemUsage) Field3DeepEqual(src string) bool {

	if strings.Compare(p.UsageMeasurementUnit, src) != 0 {
		return false
	}
	return true
}
func (p *ChargeItemUsage) Field4DeepEqual(src []*AccountantUsage) bool {

	if len(p.Usages) != len(src) {
		return false
	}
	for i, v := range p.Usages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AccountantUsage struct {
	AccountantTime int64 `thrift:"AccountantTime,3,required" frugal:"3,required,i64" json:"AccountantTime"`
	Usage          int64 `thrift:"Usage,4,required" frugal:"4,required,i64" json:"Usage"`
}

func NewAccountantUsage() *AccountantUsage {
	return &AccountantUsage{}
}

func (p *AccountantUsage) InitDefault() {
}

func (p *AccountantUsage) GetAccountantTime() (v int64) {
	return p.AccountantTime
}

func (p *AccountantUsage) GetUsage() (v int64) {
	return p.Usage
}
func (p *AccountantUsage) SetAccountantTime(val int64) {
	p.AccountantTime = val
}
func (p *AccountantUsage) SetUsage(val int64) {
	p.Usage = val
}

var fieldIDToName_AccountantUsage = map[int16]string{
	3: "AccountantTime",
	4: "Usage",
}

func (p *AccountantUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AccountantUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAccountantTime bool = false
	var issetUsage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountantTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAccountantTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUsage {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AccountantUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AccountantUsage[fieldId]))
}

func (p *AccountantUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountantTime = _field
	return nil
}
func (p *AccountantUsage) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Usage = _field
	return nil
}

func (p *AccountantUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AccountantUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("AccountantUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AccountantUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountantTime", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.AccountantTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AccountantUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Usage", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Usage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AccountantUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountantUsage(%+v)", *p)

}

func (p *AccountantUsage) DeepEqual(ano *AccountantUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountantTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.Usage) {
		return false
	}
	return true
}

func (p *AccountantUsage) Field3DeepEqual(src int64) bool {

	if p.AccountantTime != src {
		return false
	}
	return true
}
func (p *AccountantUsage) Field4DeepEqual(src int64) bool {

	if p.Usage != src {
		return false
	}
	return true
}

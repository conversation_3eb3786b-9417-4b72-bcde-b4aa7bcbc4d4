// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

var (
	ErrorCodeMap = map[ErrorCode]*ErrorDetail{
		ErrorCode_OK: &ErrorDetail{
			HttpErrCode: 200,
			DescEn:      "OK",
			DescCN:      "成功",
		},
		ErrorCode_InternalError: &ErrorDetail{
			HttpErrCode: 500,
			DescEn:      "Internal error",
			DescCN:      "系统内部错误",
		},
		ErrorCode_ParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Param error",
			DescCN:      "参数错误",
		},
		ErrorCode_CreateSessionError: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Create session error",
			DescCN:      "创建会话失败",
		},
		ErrorCode_TooManyConnectionsError: &ErrorDetail{
			HttpErrCode: 429,
			DescEn:      "Too many connections error",
			DescCN:      "连接数过多",
		},
		ErrorCode_SessionNotExistError: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Session not exist",
			DescCN:      "会话超时/不存在",
		},
		ErrorCode_CreateSessionConnectionError: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Create connection error",
			DescCN:      "创建连接失败",
		},
		ErrorCode_CommandTooLarge: &ErrorDetail{
			HttpErrCode: 413,
			DescEn:      "Command too large",
			DescCN:      "命令过长",
		},
		ErrorCode_ExplainCommandError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Explain command error",
			DescCN:      "无法解析命令",
		},
		ErrorCode_ConnectionBusy: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Connection busy",
			DescCN:      "连接占用中",
		},
		ErrorCode_InvalidConnection: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Invalid connection",
			DescCN:      "连接断开",
		},
		ErrorCode_CommandNotFound: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Command not found",
			DescCN:      "命令不存在",
		},
		ErrorCode_DataSourceOpFail: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Data source operation failed",
			DescCN:      "读写数据源失败",
		},
		ErrorCode_CancelCommandFail: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Cancel command failed",
			DescCN:      "取消命令失败",
		},
		ErrorCode_ConnectionFailed: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Connect datasource failed",
			DescCN:      "连接数据源失败:%s",
		},
		ErrorCode_ConnectionNotExist: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Describe connection failed",
			DescCN:      "连接不存在",
		},
		ErrorCode_ListAccountFail: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "List Account failed",
			DescCN:      "查询账户失败",
		},
		ErrorCode_ModifyTaskNotAllowed: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Modify Task Not Allowed",
			DescCN:      "不允许修改任务",
		},
		ErrorCode_TaskNotPaid: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Task not paid",
			DescCN:      "任务未支付",
		},
		ErrorCode_ChargeTypeInvalid: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "ChargeType Invalid",
			DescCN:      "订单类型不正确",
		},
		ErrorCode_DataValidationResultNotFound: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "data validation result not found",
			DescCN:      "数据校验报告不存在",
		},
		ErrorCode_ChargeStatusInvalid: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "charge status invalid",
			DescCN:      "付费状态非法",
		},
		ErrorCode_SyncConfigChangeForbidden: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Sync config change forbidden",
			DescCN:      "该同步配置项不允许更改",
		},
		ErrorCode_OrderExist: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Instance order already exist",
			DescCN:      "实例的订单已存在",
		},
		ErrorCode_AccountNotReal: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Not real",
			DescCN:      "未实名认证",
		},
		ErrorCode_AccountBalanceLack: &ErrorDetail{
			HttpErrCode: 402,
			DescEn:      "Account balance lack",
			DescCN:      "账户余额不足",
		},
		ErrorCode_CreateOrderFailed: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Create order failed",
			DescCN:      "创建订单失败",
		},
		ErrorCode_ProductTypeInvalid: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Product type invalid",
			DescCN:      "创建订单失败",
		},
		ErrorCode_ResourceNotFoundInvalid: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Resource not found",
			DescCN:      "没有找到相关资源",
		},
		ErrorCode_RepeatCreateResourceError: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Resource already exist",
			DescCN:      "资源无法重复创建",
		},
		ErrorCode_RdsStatusNotRunning: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "RDS instance status not running",
			DescCN:      "RDS实例状态异常",
		},
		ErrorCode_MetaDBQueryError: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Query meta db error",
			DescCN:      "查询元数据库失败",
		},
		ErrorCode_BillServiceCallFail: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Query billing service error",
			DescCN:      "查询账单服务失败",
		},
		ErrorCode_InstanceAlreadyCreating: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Instance already creating",
			DescCN:      "实例已经在创建中",
		},
		ErrorCode_InstanceNotFound: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Instance not found",
			DescCN:      "实例不存在",
		},
		ErrorCode_NotSupportSsl: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "SSL are not supported at this time",
			DescCN:      "暂不支持开启了SSL的数据库实例",
		},
		ErrorCode_QueryLogTimeout: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Query log time out, please retry or reduce the time range of the query.",
			DescCN:      "查询日志超时，请重试或者缩小查询的时间范围",
		},
		ErrorCode_NotSupportMultiTasks: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not support running multiple import or export tasks simultaneously for the instance",
			DescCN:      "实例不支持同时运行多个导入或导出任务",
		},
		ErrorCode_ThrottleRuleRepeatedErr: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not support duplicate rules for SQL throttling rules",
			DescCN:      "SQL限流规则不允许重复",
		},
		ErrorCode_SqlRuleMaxNumsErr: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The number of rules exceeds the maximum",
			DescCN:      "SQL规则数量达到最大值",
		},
		ErrorCode_KeywordLengthErr: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Keyword length exceeds the maximum",
			DescCN:      "关键字长度超过最大值",
		},
		ErrorCode_SqlRuleNotRunning: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The CCL rule is not currently running",
			DescCN:      "限流规则当前处于运行中",
		},
		ErrorCode_ProxyModeDisabled: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Proxy is not enabled on the instance",
			DescCN:      "实例未开启Proxy",
		},
		ErrorCode_NotSupportAction: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Action is not supported in the current state",
			DescCN:      "当前状态不支持该操作",
		},
		ErrorCode_NotSupportSymbol: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Exist not supported characters",
			DescCN:      "存在不支持的字符",
		},
		ErrorCode_NotSupportInstanceType: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not Supported Instance Type",
			DescCN:      "不支持当前实例类型",
		},
		ErrorCode_NotSupportSqlLevelKeyWord: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not Supported keywords that restrict all query statements of a certain type",
			DescCN:      "不支持SQL类型级的限流关键词",
		},
		ErrorCode_CheckConnectionFailed: &ErrorDetail{
			HttpErrCode: 401,
			DescEn:      "Connection failed, Authentication failed, check username/password and database permission",
			DescCN:      "鉴权报错，检查账户，密码以及对应库权限",
		},
		ErrorCode_CheckRDSConnectionFailed: &ErrorDetail{
			HttpErrCode: 401,
			DescEn:      "Connection failed, check username/password",
			DescCN:      "连接失败,请确认用户名密码正确或者用户名Host与数据库工作台白名单组匹配, 详情参见文档`登录数据交互台时提示连接失败怎么办`",
		},
		ErrorCode_UserMgmtPermissionDeny: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The current user does not have permission to operate this function",
			DescCN:      "当前用户没有操作该功能的权限",
		},
		ErrorCode_TicketStatusNotCompleted: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Ticket status not completed",
			DescCN:      "该实例有正在流程中的工单",
		},
		ErrorCode_UserIsExisted: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Can't add existed user",
			DescCN:      "已加入用户无法再次添加",
		},
		ErrorCode_TicketIsNotExisted: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Can't find ticket ",
			DescCN:      "无法找到工单信息",
		},
		ErrorCode_TicketStatusNotSatisfy: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Ticket status is not satisfy ",
			DescCN:      "工单状态不满足执行条件",
		},
		ErrorCode_RecordNotFound: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Record not found",
			DescCN:      "查询记录不存在",
		},
		ErrorCode_AlreadyEnableInstanceManagement: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "The instance has been added to security control",
			DescCN:      "该实例已加入安全管控",
		},
		ErrorCode_ExecuteCommandFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Ticket execute error",
			DescCN:      "执行命令错误",
		},
		ErrorCode_PrivilegeMaxEffectTimeErr: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "The maximum effect time for permissions is 1 year",
			DescCN:      "权限最大有效时间为1年",
		},
		ErrorCode_UserNotJoinUserMgmt: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "The user has not joined user management，please contact the administrator to add or create a permission ticket application role",
			DescCN:      "该用户未加入用户管理, 请联系管理员添加或创建权限工单申请角色",
		},
		ErrorCode_SqlDurationMaxNumsErr: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "The duration of rules exceeds the maximum",
			DescCN:      "SQL规则持续时长超过最大值",
		},
		ErrorCode_InstanceStatusNotSatisfy: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Instance is not managed, check instance status",
			DescCN:      "实例状态不满足条件,请检查实例是否已纳管",
		},
		ErrorCode_PrivilegesNotFound: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Privileges not exists",
			DescCN:      "权限不存在",
		},
		ErrorCode_SessionIdParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The SessionID should not be NULL",
			DescCN:      "SessionID不应为空",
		},
		ErrorCode_InstanceIdParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The instanceID should not be NULL",
			DescCN:      "instanceID不应为空",
		},
		ErrorCode_InstanceTypeParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The instanceType should not be NULL",
			DescCN:      "instanceType不应为空",
		},
		ErrorCode_TaskIdParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The taskID should not be NULL",
			DescCN:      "taskID不应为空",
		},
		ErrorCode_ObjectNameParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The objectName should not be NULL",
			DescCN:      "对象名称不应为空",
		},
		ErrorCode_TimeRangeError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The ExecuteTime must be larger than CreateTime and only last 24h are valid",
			DescCN:      "执行时间必须大于创建时间，并且只有最近24小时有效",
		},
		ErrorCode_ExportContentParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "CSV format can't export any structure contents",
			DescCN:      "CSV格式无法导出任何结构内容",
		},
		ErrorCode_GetTempCredentialFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get temp credential failed",
			DescCN:      "获取临时凭据失败",
		},
		ErrorCode_GetAvailableDaysFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get available days failed",
			DescCN:      "获取可用时间失败",
		},
		ErrorCode_GetLatestDiskUsageFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get latest disk usage failed",
			DescCN:      "获取磁盘使用率失败",
		},
		ErrorCode_GetStorageSpaceFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get storage space failed",
			DescCN:      "获取实例详情失败",
		},
		ErrorCode_ModifyInstanceAllowListFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get storage space failed",
			DescCN:      "修改实例白名单失败",
		},
		ErrorCode_AddPrivilegeToDBFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to add privilege to the database",
			DescCN:      "向数据库添加权限失败",
		},
		ErrorCode_PaginationParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "PageSize and PageNumber should be larger than 0",
			DescCN:      "分页参数错误",
		},
		ErrorCode_QueryTimeRangeError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Query time range error",
			DescCN:      "查询时间范围错误",
		},
		ErrorCode_GetMonitorDataFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get monitor data failed",
			DescCN:      "获取云监控数据失败",
		},
		ErrorCode_IntervalParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The interval must be larger than 0",
			DescCN:      "间隔必须大于0",
		},
		ErrorCode_InvalidEndpoint: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Invalid endpoint",
			DescCN:      "无效的终端",
		},
		ErrorCode_InvalidDatabase: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Invalid database",
			DescCN:      "无效的数据库",
		},
		ErrorCode_InputParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The input parameter error, because: %s",
			DescCN:      "请求参数错误，详情: %s",
		},
		ErrorCode_CommandSetExecuteTimeout: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Execute command set timeout",
			DescCN:      "执行命令集超时",
		},
		ErrorCode_CommandSetEmpty: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Command set is empty",
			DescCN:      "命令集为空",
		},
		ErrorCode_CheckSessionFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to check SessionID",
			DescCN:      "校验SessionID失败",
		},
		ErrorCode_NotSupportUseTheFunction: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Tenant is not support this function",
			DescCN:      "此功能不支持租户使用",
		},
		ErrorCode_PermissionDeniedError: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "No permission to operate this function",
			DescCN:      "无权限操作此功能",
		},
		ErrorCode_InstanceTypeNotSupport: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "This function does not support this instance type",
			DescCN:      "此功能不支持这种实例类型",
		},
		ErrorCode_InspectionCronCreateError: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Create auto inspection cron error",
			DescCN:      "创建自动巡检cron任务失败",
		},
		ErrorCode_WaitForRetryError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Retry later",
			DescCN:      "请稍后重试",
		},
		ErrorCode_TenantPermissionDeny: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Tenant id is no Permission",
			DescCN:      "租户无权限",
		},
		ErrorCode_InspectionDalError: &ErrorDetail{
			HttpErrCode: 409,
			DescEn:      "Get inspection metadata error",
			DescCN:      "获取巡检元信息错误",
		},
		ErrorCode_SqlPrivilegeCheckFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "[%s] permission verification failed. Cancel the relevant query or apply for relevant permissions",
			DescCN:      "【%s】权限校验失败，请取消相关查询或者申请相关权限",
		},
		ErrorCode_SqlColumnParserFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Column parser failed. Modify the relevant SQL in the recommended format of table.column",
			DescCN:      "Column解析失败,请修改相关SQL,建议格式为table.column",
		},
		ErrorCode_SecurityRuleCheckFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "[%s] Security rule verification failed. Modify the relevant SQL or contact the administrator to modify the relevant rules",
			DescCN:      "【%s】安全规则校验失败，请修改相关SQL或联系管理员修改相关规则",
		},
		ErrorCode_ChatNotFoundError: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Chat not found",
			DescCN:      "会话不存在",
		},
		ErrorCode_SqlParseFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Check the correctness and compatibility of SQL syntax",
			DescCN:      "请检查SQL语法是否正确或兼容",
		},
		ErrorCode_CheckInstanceError: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Check instance error",
			DescCN:      "实例状态非Running或实例不存在",
		},
		ErrorCode_InspectionReportNotFound: &ErrorDetail{
			HttpErrCode: 404,
			DescEn:      "Inspection report is not found, it may be generating",
			DescCN:      "巡检报告不存在或报告正在生成中",
		},
		ErrorCode_JsonMarshalFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Json marshal failed",
			DescCN:      "json序列化失败",
		},
		ErrorCode_JsonUnmarshalFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Json unmarshal failed",
			DescCN:      "json反序列化失败",
		},
		ErrorCode_OverMaxQueries: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The result exceeds the daily execution limit. Contact the administrator to modify or update configuration by permission ticket",
			DescCN:      "结果超过每日执行次数上限，请联系管理员修改或创建权限工单去更新配置",
		},
		ErrorCode_OverMaxQueriesExpirationTime: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The daily execution frequency configuration has expired. Contact the administrator to modify or update configuration by permission ticket",
			DescCN:      "每日执行次数配置已过期，请联系管理员修改或创建权限工单去更新配置",
		},
		ErrorCode_OverMaxRows: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The result exceeds the daily query limit of rows. Contact the administrator to modify or update configuration by permission ticket",
			DescCN:      "结果超过每日查询行数上限，请联系管理员修改或创建权限工单去更新配置",
		},
		ErrorCode_OverMaxRowsExpirationTime: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The configuration for daily row count query has expired. Contact the administrator to modify or update configuration by permission ticket",
			DescCN:      "每日查询行数配置已过期，请联系管理员修改或创建权限工单去更新配置",
		},
		ErrorCode_NotAssociatedSecGroup: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Managed instance is not associated with the security group",
			DescCN:      "管控实例未绑定安全规则集",
		},
		ErrorCode_SqlPrivilegeParseCheckFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "SQL parsing permission failed. Modify the relevant SQL or contact the administrator to modify the relevant rules.",
			DescCN:      "SQL解析权限失败，请修改相关SQL或联系管理员修改相关规则",
		},
		ErrorCode_InstanceCreateTimeIllegal: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Instance created too early (before 2023-05-25).",
			DescCN:      "实例创建时间过早,不支持该功能",
		},
		ErrorCode_AutoScaleFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Instance auto scale failed. %s",
			DescCN:      "实例扩缩容失败. %s ",
		},
		ErrorCode_TimeIllegal: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The time parameter is illegal, the format is wrong or the time is out of bounds",
			DescCN:      "时间参数不合法，可能是格式错误或时间越界",
		},
		ErrorCode_TicketStartError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Ticket initiation failed, need to check",
			DescCN:      "工单发起失败,请检查",
		},
		ErrorCode_NotSupportInstanceVersion: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not supported by the current instance version, upgrade the instance",
			DescCN:      "实例版本不支持，请升级实例",
		},
		ErrorCode_AuditLogDisabled: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Audit log is disabled",
			DescCN:      "实例未开启审计日志",
		},
		ErrorCode_NotSupprotNoVpcInstance: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Instance login without VPC enabled is not supported.",
			DescCN:      "不支持未开启VPC的实例登录",
		},
		ErrorCode_NotSupprotPGChangeDB: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "PG does not support changedb operations. If you need to switch, use openConnection to create a new connection.",
			DescCN:      "PG不支持切换DB操作，如需切换，请调用openConnection创建新连接",
		},
		ErrorCode_InstanceNotInSecureMode: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The instance is not found or instance not in security control mode.",
			DescCN:      "实例不存在,请检查实例是否开启了安全管控",
		},
		ErrorCode_ExportSqlResultParamError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Export SQL result param error",
			DescCN:      "创建导出sql结果集参数报错，请检查参数",
		},
		ErrorCode_TaskFlowNotFound: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The task flow not found.",
			DescCN:      "任务不存在",
		},
		ErrorCode_OperationUnsupported: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "The task flow is executing, not support edit",
			DescCN:      "任务正在执行, 无法编辑",
		},
		ErrorCode_NotSupportedByOnlineDDL: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "There are DDLs that are not supported by Online DDL.",
			DescCN:      "sql中存在Online DDL不支持的DDL语句",
		},
		ErrorCode_AllowlistError: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Check the allow list is satisfied",
			DescCN:      "请检查实例已经绑定DBW白名单和IP组，参见文档`DBW 绑定的白名单 IP`",
		},
		ErrorCode_NotSupportSqlType: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "SQL type is not supported to filter",
			DescCN:      "当前SQL类型不支持筛选",
		},
		ErrorCode_CallVeDBAPIError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Call vedb API failed",
			DescCN:      "调用VeDB接口报错",
		},
		ErrorCode_CallRdsAPIError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Call MySQL API failed",
			DescCN:      "调用MySQL接口报错",
		},
		ErrorCode_CallPgAPIError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Call PG API failed",
			DescCN:      "调用PG接口报错",
		},
		ErrorCode_TooManyTasks: &ErrorDetail{
			HttpErrCode: 403,
			DescEn:      "Too many tasks for this instance",
			DescCN:      "该实例创建了过多任务",
		},
		ErrorCode_NotSupportThrottleMode: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Throttle Mode is not support",
			DescCN:      "不支持的限流模式",
		},
		ErrorCode_UnsupportSelectStatement: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Select statement is not support",
			DescCN:      "不支持select语句",
		},
		ErrorCode_SystemError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "system error: %s",
			DescCN:      "系统错误: %s",
		},
		ErrorCode_CreateSQLAdvisorTaskError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "create sql advisor task error",
			DescCN:      "创建索引优化任务失败",
		},
		ErrorCode_DescribeSQLAdvisorTaskError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "describe sql advisor task error",
			DescCN:      "查看索引优化任务结果失败",
		},
		ErrorCode_SQLReviewGetCurrentTableError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The current data table fails to be queried. Please check whether the data table in the SQL statement exists in the database",
			DescCN:      "查询当前数据表信息失败，请检查SQL语句中的数据表是否存在该库下",
		},
		ErrorCode_SQLReviewCompareTableError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The SQL audit text contains data tables that do not exist in the database",
			DescCN:      "本次SQL审核文本中包含该数据库内不存在的数据表",
		},
		ErrorCode_SQLReviewParserSqlError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The SQL syntax parsing failed. Please check the correctness of the SQL statement",
			DescCN:      "SQL语法解析失败，请检查SQL语句正确性",
		},
		ErrorCode_SQLReviewStustNotFinishError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The current SQL audit form status is not pre-audit complete. Please check the status",
			DescCN:      "当前SQL审核单状态不是预审完成，请检查状态",
		},
		ErrorCode_SqlRuleMaximumErr: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Exceeds the maximum value configured by the rule",
			DescCN:      "超过规则配置的最大值",
		},
		ErrorCode_TicketApprovalFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Ticket approval failed, reason: %s",
			DescCN:      "工单审批失败，原因：%s",
		},
		ErrorCode_CommandExecuteFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Command Execute failed, reason: %s",
			DescCN:      "执行失败，原因: %s",
		},
		ErrorCode_UnknownCreateSessionError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Create Session failed, unknown reason: %s",
			DescCN:      "创建连接失败，未知异常: %s",
		},
		ErrorCode_UpdateRuleStatusFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to update the security rule because of the following reasons: %s",
			DescCN:      "更新安全规则失败，错误原因: %s",
		},
		ErrorCode_DeleteRuleFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to delete the security rule. Please try again later",
			DescCN:      "删除安全规则失败，请稍后再试",
		},
		ErrorCode_GetInstanceAddressFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Get Instance Address Failed, reason: %s",
			DescCN:      "获取实例地址失败，失败原因：%s",
		},
		ErrorCode_ConnectionFailedByReason: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Connect datasource failed, %s",
			DescCN:      "连接数据源失败，%s",
		},
		ErrorCode_CurrentUserNotDbwUser: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The current user is not in the DBW user role. Please add a user and try again.",
			DescCN:      "当前用户不是DBW用户角色，请添加用户后重试",
		},
		ErrorCode_GetMetricDataFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to obtain metric data. The error message is: %s",
			DescCN:      "获取监控数据失败，错误信息为：%s",
		},
		ErrorCode_DescribeSlowLogsFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to obtain slow log data. The error message is: %s",
			DescCN:      "获取慢日志数据失败，错误信息为：%s",
		},
		ErrorCode_DescribeTablesFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Querying the data table failed. The error message is:: %s",
			DescCN:      "查询数据表失败，错误信息为：%s",
		},
		ErrorCode_SqlKillRuleRepeatedErr: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Not support duplicate rules for SQL kill rules",
			DescCN:      "SQL Kill规则不允许重复",
		},
		ErrorCode_MetaDataBaseError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "[DBW] meta data execute error",
			DescCN:      "[DBW] 元数据执行操作异常",
		},
		ErrorCode_UserDataBaseError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "There was an error reported in the user's business database. The error message is: %s",
			DescCN:      "用户业务数据库报错，错误信息为：%s",
		},
		ErrorCode_UserBelongToApprovalNode: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The user %s is approver of the approval node: %s. Modify the approver of this node before executing this operation",
			DescCN:      "用户 %s 是审批节点: %s的审批人，请修改该节点审批人后再执行该操作",
		},
		ErrorCode_UserIsRoleOfSecInstance: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The user %s belongs to the role of security control instance: %s. Modify the role of this instance before performing this operation",
			DescCN:      "用户 %s 属于安全管控实例: %s的角色，请修改该实例角色后再执行该操作",
		},
		ErrorCode_BillStatementNotGenerated: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The usage amounts for the charge items for the current billing period have not yet been generated.",
			DescCN:      "暂未生成当前账期的各个计费项的使用量",
		},
		ErrorCode_UserControlledByUserGroup: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The user %s has joined a user group and is managed by the user group，please disassociate before deleting the user",
			DescCN:      "用户 %s 已加入用户组，受用户组管理，请先解除关联再删除用户",
		},
		ErrorCode_CollectionFingerprintExceedLimit: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The number of collectioin fingerprints exceeds the limit.",
			DescCN:      "收藏的sql指纹数量超过限制",
		},
		ErrorCode_CollectionFingerprintExist: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The collection fingerprint already exists.",
			DescCN:      "收藏的sql指纹已存在",
		},
		ErrorCode_UserActionPermissionDeny: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "%s instance has enabled security control, and user does not have [%s] permission. Please apply for the [%s] permission",
			DescCN:      "%s实例已开启安全管控，当前用户无该实例【%s】权限，请联系管理员授权或申请【%s】权限",
		},
		ErrorCode_UserRolePermissionDeny: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "This user role is not authorized to perform this operation. Please contact the administrator to modify or create a permission ticket to apply for the role",
			DescCN:      "该用户角色无权进行此操作, 请联系管理员修改或创建权限工单申请角色",
		},
		ErrorCode_InvalidAutoscaleChargeType: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Autoscale only support PostPaid instance,not support PrePaid instance,please check instance charge type",
			DescCN:      "规格扩缩容仅支持按量付费类型实例,不支持包年包月类型实例,请检查实例付费类型",
		},
		ErrorCode_InitControlInstanceFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The initialization of the current user information has failed. Please contact the Oncall for handling",
			DescCN:      "当前用户信息初始化失败，请联系Oncall处理",
		},
		ErrorCode_CurrentIPBeingAddedWhiteList: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The whitelisting process for the current instance is incomplete. Please try again later.",
			DescCN:      "当前实例绑定白名单未完成，请稍后再试",
		},
		ErrorCode_InstanceNotHaveVpcID: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The connection failed. Please check if the instance has a private network.",
			DescCN:      "连接失败，请检查实例是否具有私有网络",
		},
		ErrorCode_CallThirdPartyTimeout: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The call to the third - party interface timed out. Please try again later.",
			DescCN:      "调用第三方接口超时，请您稍后再试",
		},
		ErrorCode_AllowListMaintaining: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The whitelisting process for the current instance is incomplete. Please try again later.",
			DescCN:      "当前实例绑定白名单未完成，请稍后再试",
		},
		ErrorCode_TorchLogServiceError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "tls(torch log service) client query error. Please try again later.",
			DescCN:      "日志服务查询失败，请稍后再试",
		},
		ErrorCode_CopilotCreateChatFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Create copilot chat failed. Please try again later.",
			DescCN:      "创建会话失败，请稍后再试",
		},
		ErrorCode_CreateTicketError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "create ticket error:%v.",
			DescCN:      "创建工单失败,%v",
		},
		ErrorCode_InvalidParameter: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Parameter error detected, preventing auto-scaling. Rule creation is unaffected.",
			DescCN:      "当前存在参数异常，无法正常触发自动扩容事件。此状态不影响创建规则功能。",
		},
		ErrorCode_InsufficientBalance: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Insufficient balance prevents auto-scaling. Rule creation is unaffected.",
			DescCN:      "当前账户余额已不足，无法正常触发自动扩容事件。此状态不影响创建规则功能。",
		},
		ErrorCode_OperationDeniedResourceSoldOut: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Host storage exhausted, preventing auto-scaling. Rule creation is unaffected.",
			DescCN:      "当前数据库实例节点宿主机存储资源已耗尽，无法正常触发自动扩容事件。此状态不影响创建规则功能。",
		},
		ErrorCode_PrecheckTicketError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "precheck ticket error:%v.",
			DescCN:      "预检查工单失败,%v",
		},
		ErrorCode_ErrSessionNotBelongToCurrentUser: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "You are temporarily unable to modify the session name because the session does not belong to you.",
			DescCN:      "您暂时无法修改该会话名称，因为该会话不属于您",
		},
		ErrorCode_TooManyRequestFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "too many requests.",
			DescCN:      "请求过多，请稍后重试",
		},
		ErrorCode_CallTLSAPIFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Call TLS api failed error.",
			DescCN:      "调用日志服务接口失败",
		},
		ErrorCode_ErrSelectOnlineDDLRuleFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "select OnlineDDL rule failed",
			DescCN:      "查询无锁结构变更规则失败",
		},
		ErrorCode_CancelMigrationTicketError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Cannot cancel migration ticket，because ticket is running",
			DescCN:      "无法删除导入导出工单,因为工单正在运行",
		},
		ErrorCode_InstanceNotInRunningStatus: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "instance status is not running:%s,try it again later",
			DescCN:      "当前实例状态为%s，请稍后重试",
		},
		ErrorCode_RateModelReplyFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "System exception. This evaluation has failed. Please try again later.",
			DescCN:      "系统异常，本次评价失败，请您稍后再试",
		},
		ErrorCode_UpdateConsoleConnEnvFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Failed to save the historical SQL records.",
			DescCN:      "保存历史SQL记录失败：%s",
		},
		ErrorCode_ConvertTypeError: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Call TLS api failed error.",
			DescCN:      "转换类型失败",
		},
		ErrorCode_PSMAuthFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "PSM auth failed, If you're using this for the first time, there may be a delay before the authorization takes effect. Please try again after 1-2 minutes.",
			DescCN:      "PSM认证失败，如果是初次使用，授权生效有延迟，1-2分钟之后再试",
		},
		ErrorCode_InstanceResetStatus: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "the current instance status is %s, and the instance status has been reset, please refresh and check",
			DescCN:      "当前实例实际状态为%s，已为实例重置状态，请刷新后查看",
		},
		ErrorCode_ApprovalFlowConfigNodeIllegal: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The approval flow configuration is illegal. if submitter not support approval self, The approval node needs to be configured with at least 2 approvers",
			DescCN:      "审批流配置非法，如果开启了审批人不能审批自己，审批节点至少需要配置2个审批人",
		},
		ErrorCode_ApprovalFlowConfigIllegal: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "When 'Do not allow submitter approval' is enabled, skipping approval and exemption from approval are not allowed",
			DescCN:      "在开启'不允许提交人审批'的情况下，不允许开启跳过审批和免审批",
		},
		ErrorCode_ReachMaxRetryTimes: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "The number of retries has reached the maximum limit %v times, please try again after one minute",
			DescCN:      "重试次数已达上限%v次, 请1分钟后重试",
		},
		ErrorCode_InitSecurityGroupFailed: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "Init Security Rule Group Failed, failed reason:%s",
			DescCN:      "初始化安全规则集失败，失败原因为：%s",
		},
		ErrorCode_NoHistoricalDataCollection: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "the current instance is missing historical collection data",
			DescCN:      "当前实例缺少历史采集数据",
		},
		ErrorCode_InstanceNotSupportSpace: &ErrorDetail{
			HttpErrCode: 400,
			DescEn:      "the current instance does not support spatial analysis related functions",
			DescCN:      "当前实例不支持空间分析相关功能",
		},
	}
)

type ErrorCode int64

const (
	ErrorCode_OK                               ErrorCode = 0
	ErrorCode_InternalError                    ErrorCode = -300000
	ErrorCode_ParamError                       ErrorCode = -300001
	ErrorCode_CreateSessionError               ErrorCode = -300002
	ErrorCode_TooManyConnectionsError          ErrorCode = -300003
	ErrorCode_SessionNotExistError             ErrorCode = -300004
	ErrorCode_CreateSessionConnectionError     ErrorCode = -300005
	ErrorCode_CommandTooLarge                  ErrorCode = -300006
	ErrorCode_ExplainCommandError              ErrorCode = -300007
	ErrorCode_ConnectionBusy                   ErrorCode = -300008
	ErrorCode_InvalidConnection                ErrorCode = -300009
	ErrorCode_CommandNotFound                  ErrorCode = -300010
	ErrorCode_DataSourceOpFail                 ErrorCode = -300011
	ErrorCode_CancelCommandFail                ErrorCode = -300012
	ErrorCode_ConnectionFailed                 ErrorCode = -300013
	ErrorCode_ConnectionNotExist               ErrorCode = -300014
	ErrorCode_PermissionDeniedError            ErrorCode = -300015
	ErrorCode_InstanceTypeNotSupport           ErrorCode = -300016
	ErrorCode_QueryLogTimeout                  ErrorCode = -300017
	ErrorCode_MetaDBQueryError                 ErrorCode = -300100
	ErrorCode_ListAccountFail                  ErrorCode = -300101
	ErrorCode_ModifyTaskNotAllowed             ErrorCode = -300102
	ErrorCode_TaskNotPaid                      ErrorCode = -300103
	ErrorCode_ChargeTypeInvalid                ErrorCode = -300104
	ErrorCode_DataValidationResultNotFound     ErrorCode = -300105
	ErrorCode_UpgradeJobError                  ErrorCode = -300106
	ErrorCode_ChargeStatusInvalid              ErrorCode = -300107
	ErrorCode_SyncConfigChangeForbidden        ErrorCode = -300108
	ErrorCode_OrderExist                       ErrorCode = -300109
	ErrorCode_AccountNotReal                   ErrorCode = -300110
	ErrorCode_AccountBalanceLack               ErrorCode = -300111
	ErrorCode_CreateOrderFailed                ErrorCode = -300112
	ErrorCode_ProductTypeInvalid               ErrorCode = -300113
	ErrorCode_ResourceNotFoundInvalid          ErrorCode = -300114
	ErrorCode_RepeatCreateResourceError        ErrorCode = -300115
	ErrorCode_RdsStatusNotRunning              ErrorCode = -300116
	ErrorCode_BillServiceCallFail              ErrorCode = -300117
	ErrorCode_InstanceAlreadyCreating          ErrorCode = -300118
	ErrorCode_WaitForRetryError                ErrorCode = -300119
	ErrorCode_InstanceNotFound                 ErrorCode = -300120
	ErrorCode_NotSupportSsl                    ErrorCode = -300121
	ErrorCode_NotSupportMultiTasks             ErrorCode = -300122
	ErrorCode_ThrottleRuleRepeatedErr          ErrorCode = -300123
	ErrorCode_SqlRuleMaxNumsErr                ErrorCode = -300124
	ErrorCode_SqlRuleNotRunning                ErrorCode = -300125
	ErrorCode_ProxyModeDisabled                ErrorCode = -300126
	ErrorCode_NotSupportAction                 ErrorCode = -300127
	ErrorCode_NotSupportSymbol                 ErrorCode = -300128
	ErrorCode_NotSupportInstanceType           ErrorCode = -300129
	ErrorCode_NotSupportSqlLevelKeyWord        ErrorCode = -300130
	ErrorCode_CheckConnectionFailed            ErrorCode = -300131
	ErrorCode_UserMgmtPermissionDeny           ErrorCode = -300132
	ErrorCode_TicketStatusNotCompleted         ErrorCode = -300133
	ErrorCode_UserIsExisted                    ErrorCode = -300134
	ErrorCode_TicketIsNotExisted               ErrorCode = -300135
	ErrorCode_TicketStatusNotSatisfy           ErrorCode = -300136
	ErrorCode_RecordNotFound                   ErrorCode = -300137
	ErrorCode_AlreadyEnableInstanceManagement  ErrorCode = -300138
	ErrorCode_ExecuteCommandFailed             ErrorCode = -300139
	ErrorCode_PrivilegeMaxEffectTimeErr        ErrorCode = -300140
	ErrorCode_UserNotJoinUserMgmt              ErrorCode = -300141
	ErrorCode_SqlDurationMaxNumsErr            ErrorCode = -300142
	ErrorCode_InstanceStatusNotSatisfy         ErrorCode = -300143
	ErrorCode_PrivilegesNotFound               ErrorCode = -300144
	ErrorCode_InstanceTypeParamError           ErrorCode = -300145
	ErrorCode_TaskIdParamError                 ErrorCode = -300146
	ErrorCode_ObjectNameParamError             ErrorCode = -300147
	ErrorCode_TimeRangeError                   ErrorCode = -300148
	ErrorCode_ExportContentParamError          ErrorCode = -300149
	ErrorCode_GetTempCredentialFailed          ErrorCode = -300150
	ErrorCode_GetAvailableDaysFailed           ErrorCode = -300151
	ErrorCode_GetLatestDiskUsageFailed         ErrorCode = -300152
	ErrorCode_GetStorageSpaceFailed            ErrorCode = -300153
	ErrorCode_ModifyInstanceAllowListFailed    ErrorCode = -300154
	ErrorCode_AddPrivilegeToDBFailed           ErrorCode = -300155
	ErrorCode_PaginationParamError             ErrorCode = -300156
	ErrorCode_QueryTimeRangeError              ErrorCode = -300157
	ErrorCode_GetMonitorDataFailed             ErrorCode = -300158
	ErrorCode_IntervalParamError               ErrorCode = -300159
	ErrorCode_InvalidEndpoint                  ErrorCode = -300160
	ErrorCode_SessionIdParamError              ErrorCode = -300161
	ErrorCode_InvalidDatabase                  ErrorCode = -300162
	ErrorCode_InputParamError                  ErrorCode = -300163
	ErrorCode_CommandSetExecuteTimeout         ErrorCode = -300164
	ErrorCode_CommandSetEmpty                  ErrorCode = -300165
	ErrorCode_CheckSessionFailed               ErrorCode = -300166
	ErrorCode_InstanceIdParamError             ErrorCode = -300167
	ErrorCode_TenantNotInFunctionWhiteList     ErrorCode = -300168
	ErrorCode_TenantPermissionDeny             ErrorCode = -300169
	ErrorCode_NotSupportUseTheFunction         ErrorCode = -300170
	ErrorCode_InspectionCronCreateError        ErrorCode = -300171
	ErrorCode_InspectionDalError               ErrorCode = -300172
	ErrorCode_ChatNotFoundError                ErrorCode = -300173
	ErrorCode_SqlParseFailed                   ErrorCode = -300174
	ErrorCode_CheckInstanceError               ErrorCode = -300175
	ErrorCode_InspectionReportNotFound         ErrorCode = -300176
	ErrorCode_SqlPrivilegeCheckFailed          ErrorCode = -300177
	ErrorCode_SqlColumnParserFailed            ErrorCode = -300178
	ErrorCode_SecurityRuleCheckFailed          ErrorCode = -300179
	ErrorCode_JsonMarshalFailed                ErrorCode = -300180
	ErrorCode_JsonUnmarshalFailed              ErrorCode = -300181
	ErrorCode_OverMaxRows                      ErrorCode = -300182
	ErrorCode_OverMaxQueries                   ErrorCode = -300183
	ErrorCode_NotAssociatedSecGroup            ErrorCode = -300184
	ErrorCode_SqlPrivilegeParseCheckFailed     ErrorCode = -300185
	ErrorCode_InstanceCreateTimeIllegal        ErrorCode = -300186
	ErrorCode_AutoScaleFailed                  ErrorCode = -300187
	ErrorCode_TimeIllegal                      ErrorCode = -300188
	ErrorCode_TicketStartError                 ErrorCode = -300189
	ErrorCode_NotSupportInstanceVersion        ErrorCode = -300190
	ErrorCode_KeywordLengthErr                 ErrorCode = -300191
	ErrorCode_AuditLogDisabled                 ErrorCode = -300192
	ErrorCode_NotSupprotNoVpcInstance          ErrorCode = -300193
	ErrorCode_NotSupprotPGChangeDB             ErrorCode = -300194
	ErrorCode_InstanceNotInSecureMode          ErrorCode = -300195
	ErrorCode_TaskFlowNotFound                 ErrorCode = -300196
	ErrorCode_OperationUnsupported             ErrorCode = -300197
	ErrorCode_ExportSqlResultParamError        ErrorCode = -300198
	ErrorCode_NotSupportedByOnlineDDL          ErrorCode = -300199
	ErrorCode_AllowlistError                   ErrorCode = -300200
	ErrorCode_NotSupportSqlType                ErrorCode = -300201
	ErrorCode_CallVeDBAPIError                 ErrorCode = -300202
	ErrorCode_CallRdsAPIError                  ErrorCode = -300203
	ErrorCode_CallPgAPIError                   ErrorCode = -300204
	ErrorCode_CheckRDSConnectionFailed         ErrorCode = -300205
	ErrorCode_TooManyTasks                     ErrorCode = -300206
	ErrorCode_UnsupportSelectStatement         ErrorCode = -300207
	ErrorCode_DatabaseAlreadyExist             ErrorCode = -300208
	ErrorCode_NotSupportThrottleMode           ErrorCode = -300209
	ErrorCode_CreateSQLAdvisorTaskError        ErrorCode = -300210
	ErrorCode_DescribeSQLAdvisorTaskError      ErrorCode = -300211
	ErrorCode_SystemError                      ErrorCode = -300215
	ErrorCode_SQLReviewGetCurrentTableError    ErrorCode = -300216
	ErrorCode_SQLReviewCompareTableError       ErrorCode = -300217
	ErrorCode_SQLReviewParserSqlError          ErrorCode = -300218
	ErrorCode_SQLReviewStustNotFinishError     ErrorCode = -300219
	ErrorCode_SqlRuleMaximumErr                ErrorCode = -300220
	ErrorCode_TicketApprovalFailed             ErrorCode = -300221
	ErrorCode_CommandExecuteFailed             ErrorCode = -300222
	ErrorCode_UnknownCreateSessionError        ErrorCode = -300223
	ErrorCode_UpdateRuleStatusFailed           ErrorCode = -300224
	ErrorCode_DeleteRuleFailed                 ErrorCode = -300225
	ErrorCode_GetInstanceAddressFailed         ErrorCode = -300226
	ErrorCode_ConnectionFailedByReason         ErrorCode = -300227
	ErrorCode_CurrentUserNotDbwUser            ErrorCode = -300228
	ErrorCode_GetMetricDataFailed              ErrorCode = -300229
	ErrorCode_DescribeSlowLogsFailed           ErrorCode = -300230
	ErrorCode_DescribeTablesFailed             ErrorCode = -300231
	ErrorCode_SqlKillRuleRepeatedErr           ErrorCode = -300232
	ErrorCode_MetaDataBaseError                ErrorCode = -300233
	ErrorCode_UserDataBaseError                ErrorCode = -300234
	ErrorCode_UserBelongToApprovalNode         ErrorCode = -300235
	ErrorCode_UserIsRoleOfSecInstance          ErrorCode = -300236
	ErrorCode_OverMaxQueriesExpirationTime     ErrorCode = -300237
	ErrorCode_OverMaxRowsExpirationTime        ErrorCode = -300238
	ErrorCode_BillStatementNotGenerated        ErrorCode = -300239
	ErrorCode_UserControlledByUserGroup        ErrorCode = -300240
	ErrorCode_UserActionPermissionDeny         ErrorCode = -300241
	ErrorCode_UserRolePermissionDeny           ErrorCode = -300242
	ErrorCode_InvalidAutoscaleChargeType       ErrorCode = -300243
	ErrorCode_InitControlInstanceFailed        ErrorCode = -300244
	ErrorCode_CurrentIPBeingAddedWhiteList     ErrorCode = -300245
	ErrorCode_InstanceNotHaveVpcID             ErrorCode = -300246
	ErrorCode_CallThirdPartyTimeout            ErrorCode = -300247
	ErrorCode_AllowListMaintaining             ErrorCode = -300248
	ErrorCode_CollectionFingerprintExceedLimit ErrorCode = -300249
	ErrorCode_CollectionFingerprintExist       ErrorCode = -300250
	ErrorCode_TorchLogServiceError             ErrorCode = -300251
	ErrorCode_CreateTicketError                ErrorCode = -300252
	ErrorCode_PrecheckTicketError              ErrorCode = -300253
	ErrorCode_CopilotCreateChatFailed          ErrorCode = -300254
	ErrorCode_InvalidParameter                 ErrorCode = -300255
	ErrorCode_InsufficientBalance              ErrorCode = -300256
	ErrorCode_OperationDeniedResourceSoldOut   ErrorCode = -300257
	ErrorCode_ErrSessionNotBelongToCurrentUser ErrorCode = -300258
	ErrorCode_CallTLSAPIFailed                 ErrorCode = -300259
	ErrorCode_TooManyRequestFailed             ErrorCode = -300260
	ErrorCode_ErrSelectOnlineDDLRuleFailed     ErrorCode = -300261
	ErrorCode_CancelMigrationTicketError       ErrorCode = -300262
	ErrorCode_InstanceNotInRunningStatus       ErrorCode = -300263
	ErrorCode_RateModelReplyFailed             ErrorCode = -300264
	ErrorCode_UpdateConsoleConnEnvFailed       ErrorCode = -300265
	ErrorCode_ConvertTypeError                 ErrorCode = -300266
	ErrorCode_InstanceResetStatus              ErrorCode = -300267
	ErrorCode_PSMAuthFailed                    ErrorCode = -300268
	ErrorCode_ReachMaxRetryTimes               ErrorCode = -300269
	ErrorCode_ApprovalFlowConfigNodeIllegal    ErrorCode = -300270
	ErrorCode_ApprovalFlowConfigIllegal        ErrorCode = -300271
	ErrorCode_InitSecurityGroupFailed          ErrorCode = -300272
	ErrorCode_NoHistoricalDataCollection       ErrorCode = -300273
	ErrorCode_InstanceNotSupportSpace          ErrorCode = -300274
)

func (p ErrorCode) String() string {
	switch p {
	case ErrorCode_OK:
		return "OK"
	case ErrorCode_InternalError:
		return "InternalError"
	case ErrorCode_ParamError:
		return "ParamError"
	case ErrorCode_CreateSessionError:
		return "CreateSessionError"
	case ErrorCode_TooManyConnectionsError:
		return "TooManyConnectionsError"
	case ErrorCode_SessionNotExistError:
		return "SessionNotExistError"
	case ErrorCode_CreateSessionConnectionError:
		return "CreateSessionConnectionError"
	case ErrorCode_CommandTooLarge:
		return "CommandTooLarge"
	case ErrorCode_ExplainCommandError:
		return "ExplainCommandError"
	case ErrorCode_ConnectionBusy:
		return "ConnectionBusy"
	case ErrorCode_InvalidConnection:
		return "InvalidConnection"
	case ErrorCode_CommandNotFound:
		return "CommandNotFound"
	case ErrorCode_DataSourceOpFail:
		return "DataSourceOpFail"
	case ErrorCode_CancelCommandFail:
		return "CancelCommandFail"
	case ErrorCode_ConnectionFailed:
		return "ConnectionFailed"
	case ErrorCode_ConnectionNotExist:
		return "ConnectionNotExist"
	case ErrorCode_PermissionDeniedError:
		return "PermissionDeniedError"
	case ErrorCode_InstanceTypeNotSupport:
		return "InstanceTypeNotSupport"
	case ErrorCode_QueryLogTimeout:
		return "QueryLogTimeout"
	case ErrorCode_MetaDBQueryError:
		return "MetaDBQueryError"
	case ErrorCode_ListAccountFail:
		return "ListAccountFail"
	case ErrorCode_ModifyTaskNotAllowed:
		return "ModifyTaskNotAllowed"
	case ErrorCode_TaskNotPaid:
		return "TaskNotPaid"
	case ErrorCode_ChargeTypeInvalid:
		return "ChargeTypeInvalid"
	case ErrorCode_DataValidationResultNotFound:
		return "DataValidationResultNotFound"
	case ErrorCode_UpgradeJobError:
		return "UpgradeJobError"
	case ErrorCode_ChargeStatusInvalid:
		return "ChargeStatusInvalid"
	case ErrorCode_SyncConfigChangeForbidden:
		return "SyncConfigChangeForbidden"
	case ErrorCode_OrderExist:
		return "OrderExist"
	case ErrorCode_AccountNotReal:
		return "AccountNotReal"
	case ErrorCode_AccountBalanceLack:
		return "AccountBalanceLack"
	case ErrorCode_CreateOrderFailed:
		return "CreateOrderFailed"
	case ErrorCode_ProductTypeInvalid:
		return "ProductTypeInvalid"
	case ErrorCode_ResourceNotFoundInvalid:
		return "ResourceNotFoundInvalid"
	case ErrorCode_RepeatCreateResourceError:
		return "RepeatCreateResourceError"
	case ErrorCode_RdsStatusNotRunning:
		return "RdsStatusNotRunning"
	case ErrorCode_BillServiceCallFail:
		return "BillServiceCallFail"
	case ErrorCode_InstanceAlreadyCreating:
		return "InstanceAlreadyCreating"
	case ErrorCode_WaitForRetryError:
		return "WaitForRetryError"
	case ErrorCode_InstanceNotFound:
		return "InstanceNotFound"
	case ErrorCode_NotSupportSsl:
		return "NotSupportSsl"
	case ErrorCode_NotSupportMultiTasks:
		return "NotSupportMultiTasks"
	case ErrorCode_ThrottleRuleRepeatedErr:
		return "ThrottleRuleRepeatedErr"
	case ErrorCode_SqlRuleMaxNumsErr:
		return "SqlRuleMaxNumsErr"
	case ErrorCode_SqlRuleNotRunning:
		return "SqlRuleNotRunning"
	case ErrorCode_ProxyModeDisabled:
		return "ProxyModeDisabled"
	case ErrorCode_NotSupportAction:
		return "NotSupportAction"
	case ErrorCode_NotSupportSymbol:
		return "NotSupportSymbol"
	case ErrorCode_NotSupportInstanceType:
		return "NotSupportInstanceType"
	case ErrorCode_NotSupportSqlLevelKeyWord:
		return "NotSupportSqlLevelKeyWord"
	case ErrorCode_CheckConnectionFailed:
		return "CheckConnectionFailed"
	case ErrorCode_UserMgmtPermissionDeny:
		return "UserMgmtPermissionDeny"
	case ErrorCode_TicketStatusNotCompleted:
		return "TicketStatusNotCompleted"
	case ErrorCode_UserIsExisted:
		return "UserIsExisted"
	case ErrorCode_TicketIsNotExisted:
		return "TicketIsNotExisted"
	case ErrorCode_TicketStatusNotSatisfy:
		return "TicketStatusNotSatisfy"
	case ErrorCode_RecordNotFound:
		return "RecordNotFound"
	case ErrorCode_AlreadyEnableInstanceManagement:
		return "AlreadyEnableInstanceManagement"
	case ErrorCode_ExecuteCommandFailed:
		return "ExecuteCommandFailed"
	case ErrorCode_PrivilegeMaxEffectTimeErr:
		return "PrivilegeMaxEffectTimeErr"
	case ErrorCode_UserNotJoinUserMgmt:
		return "UserNotJoinUserMgmt"
	case ErrorCode_SqlDurationMaxNumsErr:
		return "SqlDurationMaxNumsErr"
	case ErrorCode_InstanceStatusNotSatisfy:
		return "InstanceStatusNotSatisfy"
	case ErrorCode_PrivilegesNotFound:
		return "PrivilegesNotFound"
	case ErrorCode_InstanceTypeParamError:
		return "InstanceTypeParamError"
	case ErrorCode_TaskIdParamError:
		return "TaskIdParamError"
	case ErrorCode_ObjectNameParamError:
		return "ObjectNameParamError"
	case ErrorCode_TimeRangeError:
		return "TimeRangeError"
	case ErrorCode_ExportContentParamError:
		return "ExportContentParamError"
	case ErrorCode_GetTempCredentialFailed:
		return "GetTempCredentialFailed"
	case ErrorCode_GetAvailableDaysFailed:
		return "GetAvailableDaysFailed"
	case ErrorCode_GetLatestDiskUsageFailed:
		return "GetLatestDiskUsageFailed"
	case ErrorCode_GetStorageSpaceFailed:
		return "GetStorageSpaceFailed"
	case ErrorCode_ModifyInstanceAllowListFailed:
		return "ModifyInstanceAllowListFailed"
	case ErrorCode_AddPrivilegeToDBFailed:
		return "AddPrivilegeToDBFailed"
	case ErrorCode_PaginationParamError:
		return "PaginationParamError"
	case ErrorCode_QueryTimeRangeError:
		return "QueryTimeRangeError"
	case ErrorCode_GetMonitorDataFailed:
		return "GetMonitorDataFailed"
	case ErrorCode_IntervalParamError:
		return "IntervalParamError"
	case ErrorCode_InvalidEndpoint:
		return "InvalidEndpoint"
	case ErrorCode_SessionIdParamError:
		return "SessionIdParamError"
	case ErrorCode_InvalidDatabase:
		return "InvalidDatabase"
	case ErrorCode_InputParamError:
		return "InputParamError"
	case ErrorCode_CommandSetExecuteTimeout:
		return "CommandSetExecuteTimeout"
	case ErrorCode_CommandSetEmpty:
		return "CommandSetEmpty"
	case ErrorCode_CheckSessionFailed:
		return "CheckSessionFailed"
	case ErrorCode_InstanceIdParamError:
		return "InstanceIdParamError"
	case ErrorCode_TenantNotInFunctionWhiteList:
		return "TenantNotInFunctionWhiteList"
	case ErrorCode_TenantPermissionDeny:
		return "TenantPermissionDeny"
	case ErrorCode_NotSupportUseTheFunction:
		return "NotSupportUseTheFunction"
	case ErrorCode_InspectionCronCreateError:
		return "InspectionCronCreateError"
	case ErrorCode_InspectionDalError:
		return "InspectionDalError"
	case ErrorCode_ChatNotFoundError:
		return "ChatNotFoundError"
	case ErrorCode_SqlParseFailed:
		return "SqlParseFailed"
	case ErrorCode_CheckInstanceError:
		return "CheckInstanceError"
	case ErrorCode_InspectionReportNotFound:
		return "InspectionReportNotFound"
	case ErrorCode_SqlPrivilegeCheckFailed:
		return "SqlPrivilegeCheckFailed"
	case ErrorCode_SqlColumnParserFailed:
		return "SqlColumnParserFailed"
	case ErrorCode_SecurityRuleCheckFailed:
		return "SecurityRuleCheckFailed"
	case ErrorCode_JsonMarshalFailed:
		return "JsonMarshalFailed"
	case ErrorCode_JsonUnmarshalFailed:
		return "JsonUnmarshalFailed"
	case ErrorCode_OverMaxRows:
		return "OverMaxRows"
	case ErrorCode_OverMaxQueries:
		return "OverMaxQueries"
	case ErrorCode_NotAssociatedSecGroup:
		return "NotAssociatedSecGroup"
	case ErrorCode_SqlPrivilegeParseCheckFailed:
		return "SqlPrivilegeParseCheckFailed"
	case ErrorCode_InstanceCreateTimeIllegal:
		return "InstanceCreateTimeIllegal"
	case ErrorCode_AutoScaleFailed:
		return "AutoScaleFailed"
	case ErrorCode_TimeIllegal:
		return "TimeIllegal"
	case ErrorCode_TicketStartError:
		return "TicketStartError"
	case ErrorCode_NotSupportInstanceVersion:
		return "NotSupportInstanceVersion"
	case ErrorCode_KeywordLengthErr:
		return "KeywordLengthErr"
	case ErrorCode_AuditLogDisabled:
		return "AuditLogDisabled"
	case ErrorCode_NotSupprotNoVpcInstance:
		return "NotSupprotNoVpcInstance"
	case ErrorCode_NotSupprotPGChangeDB:
		return "NotSupprotPGChangeDB"
	case ErrorCode_InstanceNotInSecureMode:
		return "InstanceNotInSecureMode"
	case ErrorCode_TaskFlowNotFound:
		return "TaskFlowNotFound"
	case ErrorCode_OperationUnsupported:
		return "OperationUnsupported"
	case ErrorCode_ExportSqlResultParamError:
		return "ExportSqlResultParamError"
	case ErrorCode_NotSupportedByOnlineDDL:
		return "NotSupportedByOnlineDDL"
	case ErrorCode_AllowlistError:
		return "AllowlistError"
	case ErrorCode_NotSupportSqlType:
		return "NotSupportSqlType"
	case ErrorCode_CallVeDBAPIError:
		return "CallVeDBAPIError"
	case ErrorCode_CallRdsAPIError:
		return "CallRdsAPIError"
	case ErrorCode_CallPgAPIError:
		return "CallPgAPIError"
	case ErrorCode_CheckRDSConnectionFailed:
		return "CheckRDSConnectionFailed"
	case ErrorCode_TooManyTasks:
		return "TooManyTasks"
	case ErrorCode_UnsupportSelectStatement:
		return "UnsupportSelectStatement"
	case ErrorCode_DatabaseAlreadyExist:
		return "DatabaseAlreadyExist"
	case ErrorCode_NotSupportThrottleMode:
		return "NotSupportThrottleMode"
	case ErrorCode_CreateSQLAdvisorTaskError:
		return "CreateSQLAdvisorTaskError"
	case ErrorCode_DescribeSQLAdvisorTaskError:
		return "DescribeSQLAdvisorTaskError"
	case ErrorCode_SystemError:
		return "SystemError"
	case ErrorCode_SQLReviewGetCurrentTableError:
		return "SQLReviewGetCurrentTableError"
	case ErrorCode_SQLReviewCompareTableError:
		return "SQLReviewCompareTableError"
	case ErrorCode_SQLReviewParserSqlError:
		return "SQLReviewParserSqlError"
	case ErrorCode_SQLReviewStustNotFinishError:
		return "SQLReviewStustNotFinishError"
	case ErrorCode_SqlRuleMaximumErr:
		return "SqlRuleMaximumErr"
	case ErrorCode_TicketApprovalFailed:
		return "TicketApprovalFailed"
	case ErrorCode_CommandExecuteFailed:
		return "CommandExecuteFailed"
	case ErrorCode_UnknownCreateSessionError:
		return "UnknownCreateSessionError"
	case ErrorCode_UpdateRuleStatusFailed:
		return "UpdateRuleStatusFailed"
	case ErrorCode_DeleteRuleFailed:
		return "DeleteRuleFailed"
	case ErrorCode_GetInstanceAddressFailed:
		return "GetInstanceAddressFailed"
	case ErrorCode_ConnectionFailedByReason:
		return "ConnectionFailedByReason"
	case ErrorCode_CurrentUserNotDbwUser:
		return "CurrentUserNotDbwUser"
	case ErrorCode_GetMetricDataFailed:
		return "GetMetricDataFailed"
	case ErrorCode_DescribeSlowLogsFailed:
		return "DescribeSlowLogsFailed"
	case ErrorCode_DescribeTablesFailed:
		return "DescribeTablesFailed"
	case ErrorCode_SqlKillRuleRepeatedErr:
		return "SqlKillRuleRepeatedErr"
	case ErrorCode_MetaDataBaseError:
		return "MetaDataBaseError"
	case ErrorCode_UserDataBaseError:
		return "UserDataBaseError"
	case ErrorCode_UserBelongToApprovalNode:
		return "UserBelongToApprovalNode"
	case ErrorCode_UserIsRoleOfSecInstance:
		return "UserIsRoleOfSecInstance"
	case ErrorCode_OverMaxQueriesExpirationTime:
		return "OverMaxQueriesExpirationTime"
	case ErrorCode_OverMaxRowsExpirationTime:
		return "OverMaxRowsExpirationTime"
	case ErrorCode_BillStatementNotGenerated:
		return "BillStatementNotGenerated"
	case ErrorCode_UserControlledByUserGroup:
		return "UserControlledByUserGroup"
	case ErrorCode_UserActionPermissionDeny:
		return "UserActionPermissionDeny"
	case ErrorCode_UserRolePermissionDeny:
		return "UserRolePermissionDeny"
	case ErrorCode_InvalidAutoscaleChargeType:
		return "InvalidAutoscaleChargeType"
	case ErrorCode_InitControlInstanceFailed:
		return "InitControlInstanceFailed"
	case ErrorCode_CurrentIPBeingAddedWhiteList:
		return "CurrentIPBeingAddedWhiteList"
	case ErrorCode_InstanceNotHaveVpcID:
		return "InstanceNotHaveVpcID"
	case ErrorCode_CallThirdPartyTimeout:
		return "CallThirdPartyTimeout"
	case ErrorCode_AllowListMaintaining:
		return "AllowListMaintaining"
	case ErrorCode_CollectionFingerprintExceedLimit:
		return "CollectionFingerprintExceedLimit"
	case ErrorCode_CollectionFingerprintExist:
		return "CollectionFingerprintExist"
	case ErrorCode_TorchLogServiceError:
		return "TorchLogServiceError"
	case ErrorCode_CreateTicketError:
		return "CreateTicketError"
	case ErrorCode_PrecheckTicketError:
		return "PrecheckTicketError"
	case ErrorCode_CopilotCreateChatFailed:
		return "CopilotCreateChatFailed"
	case ErrorCode_InvalidParameter:
		return "InvalidParameter"
	case ErrorCode_InsufficientBalance:
		return "InsufficientBalance"
	case ErrorCode_OperationDeniedResourceSoldOut:
		return "OperationDeniedResourceSoldOut"
	case ErrorCode_ErrSessionNotBelongToCurrentUser:
		return "ErrSessionNotBelongToCurrentUser"
	case ErrorCode_CallTLSAPIFailed:
		return "CallTLSAPIFailed"
	case ErrorCode_TooManyRequestFailed:
		return "TooManyRequestFailed"
	case ErrorCode_ErrSelectOnlineDDLRuleFailed:
		return "ErrSelectOnlineDDLRuleFailed"
	case ErrorCode_CancelMigrationTicketError:
		return "CancelMigrationTicketError"
	case ErrorCode_InstanceNotInRunningStatus:
		return "InstanceNotInRunningStatus"
	case ErrorCode_RateModelReplyFailed:
		return "RateModelReplyFailed"
	case ErrorCode_UpdateConsoleConnEnvFailed:
		return "UpdateConsoleConnEnvFailed"
	case ErrorCode_ConvertTypeError:
		return "ConvertTypeError"
	case ErrorCode_InstanceResetStatus:
		return "InstanceResetStatus"
	case ErrorCode_PSMAuthFailed:
		return "PSMAuthFailed"
	case ErrorCode_ReachMaxRetryTimes:
		return "ReachMaxRetryTimes"
	case ErrorCode_ApprovalFlowConfigNodeIllegal:
		return "ApprovalFlowConfigNodeIllegal"
	case ErrorCode_ApprovalFlowConfigIllegal:
		return "ApprovalFlowConfigIllegal"
	case ErrorCode_InitSecurityGroupFailed:
		return "InitSecurityGroupFailed"
	case ErrorCode_NoHistoricalDataCollection:
		return "NoHistoricalDataCollection"
	case ErrorCode_InstanceNotSupportSpace:
		return "InstanceNotSupportSpace"
	}
	return "<UNSET>"
}

func ErrorCodeFromString(s string) (ErrorCode, error) {
	switch s {
	case "OK":
		return ErrorCode_OK, nil
	case "InternalError":
		return ErrorCode_InternalError, nil
	case "ParamError":
		return ErrorCode_ParamError, nil
	case "CreateSessionError":
		return ErrorCode_CreateSessionError, nil
	case "TooManyConnectionsError":
		return ErrorCode_TooManyConnectionsError, nil
	case "SessionNotExistError":
		return ErrorCode_SessionNotExistError, nil
	case "CreateSessionConnectionError":
		return ErrorCode_CreateSessionConnectionError, nil
	case "CommandTooLarge":
		return ErrorCode_CommandTooLarge, nil
	case "ExplainCommandError":
		return ErrorCode_ExplainCommandError, nil
	case "ConnectionBusy":
		return ErrorCode_ConnectionBusy, nil
	case "InvalidConnection":
		return ErrorCode_InvalidConnection, nil
	case "CommandNotFound":
		return ErrorCode_CommandNotFound, nil
	case "DataSourceOpFail":
		return ErrorCode_DataSourceOpFail, nil
	case "CancelCommandFail":
		return ErrorCode_CancelCommandFail, nil
	case "ConnectionFailed":
		return ErrorCode_ConnectionFailed, nil
	case "ConnectionNotExist":
		return ErrorCode_ConnectionNotExist, nil
	case "PermissionDeniedError":
		return ErrorCode_PermissionDeniedError, nil
	case "InstanceTypeNotSupport":
		return ErrorCode_InstanceTypeNotSupport, nil
	case "QueryLogTimeout":
		return ErrorCode_QueryLogTimeout, nil
	case "MetaDBQueryError":
		return ErrorCode_MetaDBQueryError, nil
	case "ListAccountFail":
		return ErrorCode_ListAccountFail, nil
	case "ModifyTaskNotAllowed":
		return ErrorCode_ModifyTaskNotAllowed, nil
	case "TaskNotPaid":
		return ErrorCode_TaskNotPaid, nil
	case "ChargeTypeInvalid":
		return ErrorCode_ChargeTypeInvalid, nil
	case "DataValidationResultNotFound":
		return ErrorCode_DataValidationResultNotFound, nil
	case "UpgradeJobError":
		return ErrorCode_UpgradeJobError, nil
	case "ChargeStatusInvalid":
		return ErrorCode_ChargeStatusInvalid, nil
	case "SyncConfigChangeForbidden":
		return ErrorCode_SyncConfigChangeForbidden, nil
	case "OrderExist":
		return ErrorCode_OrderExist, nil
	case "AccountNotReal":
		return ErrorCode_AccountNotReal, nil
	case "AccountBalanceLack":
		return ErrorCode_AccountBalanceLack, nil
	case "CreateOrderFailed":
		return ErrorCode_CreateOrderFailed, nil
	case "ProductTypeInvalid":
		return ErrorCode_ProductTypeInvalid, nil
	case "ResourceNotFoundInvalid":
		return ErrorCode_ResourceNotFoundInvalid, nil
	case "RepeatCreateResourceError":
		return ErrorCode_RepeatCreateResourceError, nil
	case "RdsStatusNotRunning":
		return ErrorCode_RdsStatusNotRunning, nil
	case "BillServiceCallFail":
		return ErrorCode_BillServiceCallFail, nil
	case "InstanceAlreadyCreating":
		return ErrorCode_InstanceAlreadyCreating, nil
	case "WaitForRetryError":
		return ErrorCode_WaitForRetryError, nil
	case "InstanceNotFound":
		return ErrorCode_InstanceNotFound, nil
	case "NotSupportSsl":
		return ErrorCode_NotSupportSsl, nil
	case "NotSupportMultiTasks":
		return ErrorCode_NotSupportMultiTasks, nil
	case "ThrottleRuleRepeatedErr":
		return ErrorCode_ThrottleRuleRepeatedErr, nil
	case "SqlRuleMaxNumsErr":
		return ErrorCode_SqlRuleMaxNumsErr, nil
	case "SqlRuleNotRunning":
		return ErrorCode_SqlRuleNotRunning, nil
	case "ProxyModeDisabled":
		return ErrorCode_ProxyModeDisabled, nil
	case "NotSupportAction":
		return ErrorCode_NotSupportAction, nil
	case "NotSupportSymbol":
		return ErrorCode_NotSupportSymbol, nil
	case "NotSupportInstanceType":
		return ErrorCode_NotSupportInstanceType, nil
	case "NotSupportSqlLevelKeyWord":
		return ErrorCode_NotSupportSqlLevelKeyWord, nil
	case "CheckConnectionFailed":
		return ErrorCode_CheckConnectionFailed, nil
	case "UserMgmtPermissionDeny":
		return ErrorCode_UserMgmtPermissionDeny, nil
	case "TicketStatusNotCompleted":
		return ErrorCode_TicketStatusNotCompleted, nil
	case "UserIsExisted":
		return ErrorCode_UserIsExisted, nil
	case "TicketIsNotExisted":
		return ErrorCode_TicketIsNotExisted, nil
	case "TicketStatusNotSatisfy":
		return ErrorCode_TicketStatusNotSatisfy, nil
	case "RecordNotFound":
		return ErrorCode_RecordNotFound, nil
	case "AlreadyEnableInstanceManagement":
		return ErrorCode_AlreadyEnableInstanceManagement, nil
	case "ExecuteCommandFailed":
		return ErrorCode_ExecuteCommandFailed, nil
	case "PrivilegeMaxEffectTimeErr":
		return ErrorCode_PrivilegeMaxEffectTimeErr, nil
	case "UserNotJoinUserMgmt":
		return ErrorCode_UserNotJoinUserMgmt, nil
	case "SqlDurationMaxNumsErr":
		return ErrorCode_SqlDurationMaxNumsErr, nil
	case "InstanceStatusNotSatisfy":
		return ErrorCode_InstanceStatusNotSatisfy, nil
	case "PrivilegesNotFound":
		return ErrorCode_PrivilegesNotFound, nil
	case "InstanceTypeParamError":
		return ErrorCode_InstanceTypeParamError, nil
	case "TaskIdParamError":
		return ErrorCode_TaskIdParamError, nil
	case "ObjectNameParamError":
		return ErrorCode_ObjectNameParamError, nil
	case "TimeRangeError":
		return ErrorCode_TimeRangeError, nil
	case "ExportContentParamError":
		return ErrorCode_ExportContentParamError, nil
	case "GetTempCredentialFailed":
		return ErrorCode_GetTempCredentialFailed, nil
	case "GetAvailableDaysFailed":
		return ErrorCode_GetAvailableDaysFailed, nil
	case "GetLatestDiskUsageFailed":
		return ErrorCode_GetLatestDiskUsageFailed, nil
	case "GetStorageSpaceFailed":
		return ErrorCode_GetStorageSpaceFailed, nil
	case "ModifyInstanceAllowListFailed":
		return ErrorCode_ModifyInstanceAllowListFailed, nil
	case "AddPrivilegeToDBFailed":
		return ErrorCode_AddPrivilegeToDBFailed, nil
	case "PaginationParamError":
		return ErrorCode_PaginationParamError, nil
	case "QueryTimeRangeError":
		return ErrorCode_QueryTimeRangeError, nil
	case "GetMonitorDataFailed":
		return ErrorCode_GetMonitorDataFailed, nil
	case "IntervalParamError":
		return ErrorCode_IntervalParamError, nil
	case "InvalidEndpoint":
		return ErrorCode_InvalidEndpoint, nil
	case "SessionIdParamError":
		return ErrorCode_SessionIdParamError, nil
	case "InvalidDatabase":
		return ErrorCode_InvalidDatabase, nil
	case "InputParamError":
		return ErrorCode_InputParamError, nil
	case "CommandSetExecuteTimeout":
		return ErrorCode_CommandSetExecuteTimeout, nil
	case "CommandSetEmpty":
		return ErrorCode_CommandSetEmpty, nil
	case "CheckSessionFailed":
		return ErrorCode_CheckSessionFailed, nil
	case "InstanceIdParamError":
		return ErrorCode_InstanceIdParamError, nil
	case "TenantNotInFunctionWhiteList":
		return ErrorCode_TenantNotInFunctionWhiteList, nil
	case "TenantPermissionDeny":
		return ErrorCode_TenantPermissionDeny, nil
	case "NotSupportUseTheFunction":
		return ErrorCode_NotSupportUseTheFunction, nil
	case "InspectionCronCreateError":
		return ErrorCode_InspectionCronCreateError, nil
	case "InspectionDalError":
		return ErrorCode_InspectionDalError, nil
	case "ChatNotFoundError":
		return ErrorCode_ChatNotFoundError, nil
	case "SqlParseFailed":
		return ErrorCode_SqlParseFailed, nil
	case "CheckInstanceError":
		return ErrorCode_CheckInstanceError, nil
	case "InspectionReportNotFound":
		return ErrorCode_InspectionReportNotFound, nil
	case "SqlPrivilegeCheckFailed":
		return ErrorCode_SqlPrivilegeCheckFailed, nil
	case "SqlColumnParserFailed":
		return ErrorCode_SqlColumnParserFailed, nil
	case "SecurityRuleCheckFailed":
		return ErrorCode_SecurityRuleCheckFailed, nil
	case "JsonMarshalFailed":
		return ErrorCode_JsonMarshalFailed, nil
	case "JsonUnmarshalFailed":
		return ErrorCode_JsonUnmarshalFailed, nil
	case "OverMaxRows":
		return ErrorCode_OverMaxRows, nil
	case "OverMaxQueries":
		return ErrorCode_OverMaxQueries, nil
	case "NotAssociatedSecGroup":
		return ErrorCode_NotAssociatedSecGroup, nil
	case "SqlPrivilegeParseCheckFailed":
		return ErrorCode_SqlPrivilegeParseCheckFailed, nil
	case "InstanceCreateTimeIllegal":
		return ErrorCode_InstanceCreateTimeIllegal, nil
	case "AutoScaleFailed":
		return ErrorCode_AutoScaleFailed, nil
	case "TimeIllegal":
		return ErrorCode_TimeIllegal, nil
	case "TicketStartError":
		return ErrorCode_TicketStartError, nil
	case "NotSupportInstanceVersion":
		return ErrorCode_NotSupportInstanceVersion, nil
	case "KeywordLengthErr":
		return ErrorCode_KeywordLengthErr, nil
	case "AuditLogDisabled":
		return ErrorCode_AuditLogDisabled, nil
	case "NotSupprotNoVpcInstance":
		return ErrorCode_NotSupprotNoVpcInstance, nil
	case "NotSupprotPGChangeDB":
		return ErrorCode_NotSupprotPGChangeDB, nil
	case "InstanceNotInSecureMode":
		return ErrorCode_InstanceNotInSecureMode, nil
	case "TaskFlowNotFound":
		return ErrorCode_TaskFlowNotFound, nil
	case "OperationUnsupported":
		return ErrorCode_OperationUnsupported, nil
	case "ExportSqlResultParamError":
		return ErrorCode_ExportSqlResultParamError, nil
	case "NotSupportedByOnlineDDL":
		return ErrorCode_NotSupportedByOnlineDDL, nil
	case "AllowlistError":
		return ErrorCode_AllowlistError, nil
	case "NotSupportSqlType":
		return ErrorCode_NotSupportSqlType, nil
	case "CallVeDBAPIError":
		return ErrorCode_CallVeDBAPIError, nil
	case "CallRdsAPIError":
		return ErrorCode_CallRdsAPIError, nil
	case "CallPgAPIError":
		return ErrorCode_CallPgAPIError, nil
	case "CheckRDSConnectionFailed":
		return ErrorCode_CheckRDSConnectionFailed, nil
	case "TooManyTasks":
		return ErrorCode_TooManyTasks, nil
	case "UnsupportSelectStatement":
		return ErrorCode_UnsupportSelectStatement, nil
	case "DatabaseAlreadyExist":
		return ErrorCode_DatabaseAlreadyExist, nil
	case "NotSupportThrottleMode":
		return ErrorCode_NotSupportThrottleMode, nil
	case "CreateSQLAdvisorTaskError":
		return ErrorCode_CreateSQLAdvisorTaskError, nil
	case "DescribeSQLAdvisorTaskError":
		return ErrorCode_DescribeSQLAdvisorTaskError, nil
	case "SystemError":
		return ErrorCode_SystemError, nil
	case "SQLReviewGetCurrentTableError":
		return ErrorCode_SQLReviewGetCurrentTableError, nil
	case "SQLReviewCompareTableError":
		return ErrorCode_SQLReviewCompareTableError, nil
	case "SQLReviewParserSqlError":
		return ErrorCode_SQLReviewParserSqlError, nil
	case "SQLReviewStustNotFinishError":
		return ErrorCode_SQLReviewStustNotFinishError, nil
	case "SqlRuleMaximumErr":
		return ErrorCode_SqlRuleMaximumErr, nil
	case "TicketApprovalFailed":
		return ErrorCode_TicketApprovalFailed, nil
	case "CommandExecuteFailed":
		return ErrorCode_CommandExecuteFailed, nil
	case "UnknownCreateSessionError":
		return ErrorCode_UnknownCreateSessionError, nil
	case "UpdateRuleStatusFailed":
		return ErrorCode_UpdateRuleStatusFailed, nil
	case "DeleteRuleFailed":
		return ErrorCode_DeleteRuleFailed, nil
	case "GetInstanceAddressFailed":
		return ErrorCode_GetInstanceAddressFailed, nil
	case "ConnectionFailedByReason":
		return ErrorCode_ConnectionFailedByReason, nil
	case "CurrentUserNotDbwUser":
		return ErrorCode_CurrentUserNotDbwUser, nil
	case "GetMetricDataFailed":
		return ErrorCode_GetMetricDataFailed, nil
	case "DescribeSlowLogsFailed":
		return ErrorCode_DescribeSlowLogsFailed, nil
	case "DescribeTablesFailed":
		return ErrorCode_DescribeTablesFailed, nil
	case "SqlKillRuleRepeatedErr":
		return ErrorCode_SqlKillRuleRepeatedErr, nil
	case "MetaDataBaseError":
		return ErrorCode_MetaDataBaseError, nil
	case "UserDataBaseError":
		return ErrorCode_UserDataBaseError, nil
	case "UserBelongToApprovalNode":
		return ErrorCode_UserBelongToApprovalNode, nil
	case "UserIsRoleOfSecInstance":
		return ErrorCode_UserIsRoleOfSecInstance, nil
	case "OverMaxQueriesExpirationTime":
		return ErrorCode_OverMaxQueriesExpirationTime, nil
	case "OverMaxRowsExpirationTime":
		return ErrorCode_OverMaxRowsExpirationTime, nil
	case "BillStatementNotGenerated":
		return ErrorCode_BillStatementNotGenerated, nil
	case "UserControlledByUserGroup":
		return ErrorCode_UserControlledByUserGroup, nil
	case "UserActionPermissionDeny":
		return ErrorCode_UserActionPermissionDeny, nil
	case "UserRolePermissionDeny":
		return ErrorCode_UserRolePermissionDeny, nil
	case "InvalidAutoscaleChargeType":
		return ErrorCode_InvalidAutoscaleChargeType, nil
	case "InitControlInstanceFailed":
		return ErrorCode_InitControlInstanceFailed, nil
	case "CurrentIPBeingAddedWhiteList":
		return ErrorCode_CurrentIPBeingAddedWhiteList, nil
	case "InstanceNotHaveVpcID":
		return ErrorCode_InstanceNotHaveVpcID, nil
	case "CallThirdPartyTimeout":
		return ErrorCode_CallThirdPartyTimeout, nil
	case "AllowListMaintaining":
		return ErrorCode_AllowListMaintaining, nil
	case "CollectionFingerprintExceedLimit":
		return ErrorCode_CollectionFingerprintExceedLimit, nil
	case "CollectionFingerprintExist":
		return ErrorCode_CollectionFingerprintExist, nil
	case "TorchLogServiceError":
		return ErrorCode_TorchLogServiceError, nil
	case "CreateTicketError":
		return ErrorCode_CreateTicketError, nil
	case "PrecheckTicketError":
		return ErrorCode_PrecheckTicketError, nil
	case "CopilotCreateChatFailed":
		return ErrorCode_CopilotCreateChatFailed, nil
	case "InvalidParameter":
		return ErrorCode_InvalidParameter, nil
	case "InsufficientBalance":
		return ErrorCode_InsufficientBalance, nil
	case "OperationDeniedResourceSoldOut":
		return ErrorCode_OperationDeniedResourceSoldOut, nil
	case "ErrSessionNotBelongToCurrentUser":
		return ErrorCode_ErrSessionNotBelongToCurrentUser, nil
	case "CallTLSAPIFailed":
		return ErrorCode_CallTLSAPIFailed, nil
	case "TooManyRequestFailed":
		return ErrorCode_TooManyRequestFailed, nil
	case "ErrSelectOnlineDDLRuleFailed":
		return ErrorCode_ErrSelectOnlineDDLRuleFailed, nil
	case "CancelMigrationTicketError":
		return ErrorCode_CancelMigrationTicketError, nil
	case "InstanceNotInRunningStatus":
		return ErrorCode_InstanceNotInRunningStatus, nil
	case "RateModelReplyFailed":
		return ErrorCode_RateModelReplyFailed, nil
	case "UpdateConsoleConnEnvFailed":
		return ErrorCode_UpdateConsoleConnEnvFailed, nil
	case "ConvertTypeError":
		return ErrorCode_ConvertTypeError, nil
	case "InstanceResetStatus":
		return ErrorCode_InstanceResetStatus, nil
	case "PSMAuthFailed":
		return ErrorCode_PSMAuthFailed, nil
	case "ReachMaxRetryTimes":
		return ErrorCode_ReachMaxRetryTimes, nil
	case "ApprovalFlowConfigNodeIllegal":
		return ErrorCode_ApprovalFlowConfigNodeIllegal, nil
	case "ApprovalFlowConfigIllegal":
		return ErrorCode_ApprovalFlowConfigIllegal, nil
	case "InitSecurityGroupFailed":
		return ErrorCode_InitSecurityGroupFailed, nil
	case "NoHistoricalDataCollection":
		return ErrorCode_NoHistoricalDataCollection, nil
	case "InstanceNotSupportSpace":
		return ErrorCode_InstanceNotSupportSpace, nil
	}
	return ErrorCode(0), fmt.Errorf("not a valid ErrorCode string")
}

func ErrorCodePtr(v ErrorCode) *ErrorCode { return &v }

func (p ErrorCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrorCode) UnmarshalText(text []byte) error {
	q, err := ErrorCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrorDetail struct {
	HttpErrCode int64  `thrift:"HttpErrCode,1,required" frugal:"1,required,i64" json:"HttpErrCode"`
	DescCN      string `thrift:"DescCN,2,required" frugal:"2,required,string" json:"DescCN"`
	DescEn      string `thrift:"DescEn,3,required" frugal:"3,required,string" json:"DescEn"`
}

func NewErrorDetail() *ErrorDetail {
	return &ErrorDetail{}
}

func (p *ErrorDetail) InitDefault() {
}

func (p *ErrorDetail) GetHttpErrCode() (v int64) {
	return p.HttpErrCode
}

func (p *ErrorDetail) GetDescCN() (v string) {
	return p.DescCN
}

func (p *ErrorDetail) GetDescEn() (v string) {
	return p.DescEn
}
func (p *ErrorDetail) SetHttpErrCode(val int64) {
	p.HttpErrCode = val
}
func (p *ErrorDetail) SetDescCN(val string) {
	p.DescCN = val
}
func (p *ErrorDetail) SetDescEn(val string) {
	p.DescEn = val
}

var fieldIDToName_ErrorDetail = map[int16]string{
	1: "HttpErrCode",
	2: "DescCN",
	3: "DescEn",
}

func (p *ErrorDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHttpErrCode bool = false
	var issetDescCN bool = false
	var issetDescEn bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHttpErrCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescCN = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescEn = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHttpErrCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDescCN {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescEn {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrorDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ErrorDetail[fieldId]))
}

func (p *ErrorDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HttpErrCode = _field
	return nil
}
func (p *ErrorDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescCN = _field
	return nil
}
func (p *ErrorDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescEn = _field
	return nil
}

func (p *ErrorDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrorDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrorDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HttpErrCode", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.HttpErrCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ErrorDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DescCN", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescCN); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrorDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DescEn", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescEn); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrorDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrorDetail(%+v)", *p)

}

func (p *ErrorDetail) DeepEqual(ano *ErrorDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HttpErrCode) {
		return false
	}
	if !p.Field2DeepEqual(ano.DescCN) {
		return false
	}
	if !p.Field3DeepEqual(ano.DescEn) {
		return false
	}
	return true
}

func (p *ErrorDetail) Field1DeepEqual(src int64) bool {

	if p.HttpErrCode != src {
		return false
	}
	return true
}
func (p *ErrorDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DescCN, src) != 0 {
		return false
	}
	return true
}
func (p *ErrorDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DescEn, src) != 0 {
		return false
	}
	return true
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ExecutePromptReq) IsValid() error {
	return nil
}
func (p *ExecutePromptResp) IsValid() error {
	return nil
}
func (p *ExecuteProxyAgentReq) IsValid() error {
	return nil
}
func (p *ExecuteProxyAgentResp) IsValid() error {
	return nil
}
func (p *ExecuteSlaveAgentReq) IsValid() error {
	return nil
}
func (p *ExecuteSlaveAgentResp) IsValid() error {
	return nil
}
func (p *DescribeChatMessagesReq) IsValid() error {
	return nil
}
func (p *DescribeChatMessagesResp) IsValid() error {
	return nil
}
func (p *DescribeCopilotTaskListReq) IsValid() error {
	return nil
}
func (p *DescribeCopilotTaskListResp) IsValid() error {
	return nil
}
func (p *TriggerButtonReq) IsValid() error {
	return nil
}
func (p *TriggerButtonResp) IsValid() error {
	return nil
}
func (p *ChatMessage) IsValid() error {
	if p.ExtraInfo != nil {
		if err := p.ExtraInfo.IsValid(); err != nil {
			return fmt.Errorf("field ExtraInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CopilotExtraInfo) IsValid() error {
	return nil
}
func (p *ExtraButtonInfo) IsValid() error {
	return nil
}
func (p *CopilotDataPoint) IsValid() error {
	return nil
}
func (p *CopilotMetricDataResults) IsValid() error {
	return nil
}
func (p *CopilotButtonGenerationReq) IsValid() error {
	return nil
}
func (p *CopilotButtonGenerationResp) IsValid() error {
	if p.ExtraInfo != nil {
		if err := p.ExtraInfo.IsValid(); err != nil {
			return fmt.Errorf("field ExtraInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateCopilotChatReq) IsValid() error {
	return nil
}
func (p *CreateCopilotChatResp) IsValid() error {
	return nil
}
func (p *UpdateCopilotChatNameReq) IsValid() error {
	return nil
}
func (p *UpdateCopilotChatNameResp) IsValid() error {
	return nil
}
func (p *DeleteCopilotChatReq) IsValid() error {
	return nil
}
func (p *DeleteCopilotChatResp) IsValid() error {
	return nil
}
func (p *ChatInfo) IsValid() error {
	return nil
}
func (p *DescribeCopilotChatListReq) IsValid() error {
	return nil
}
func (p *DescribeCopilotChatListResp) IsValid() error {
	return nil
}
func (p *CopilotStreamResponse) IsValid() error {
	return nil
}
func (p *FunctionCall) IsValid() error {
	return nil
}
func (p *ExecuteAssistantPromptReq) IsValid() error {
	return nil
}
func (p *ExecuteAssistantPromptResp) IsValid() error {
	return nil
}
func (p *CopilotAssistantResponse) IsValid() error {
	return nil
}
func (p *RelatedDocInfo) IsValid() error {
	return nil
}
func (p *RateModelReplyReq) IsValid() error {
	return nil
}
func (p *RateModelReplyResp) IsValid() error {
	return nil
}
func (p *CreateOrderContentFromMessagesReq) IsValid() error {
	return nil
}
func (p *CreateOrderContentFromMessagesResp) IsValid() error {
	return nil
}

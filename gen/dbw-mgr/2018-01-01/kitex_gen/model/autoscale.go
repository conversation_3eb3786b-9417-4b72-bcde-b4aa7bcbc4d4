// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type AutoScaleAction int64

const (
	AutoScaleAction_Expand AutoScaleAction = 0
	AutoScaleAction_Reduce AutoScaleAction = 1
)

func (p AutoScaleAction) String() string {
	switch p {
	case AutoScaleAction_Expand:
		return "Expand"
	case AutoScaleAction_Reduce:
		return "Reduce"
	}
	return "<UNSET>"
}

func AutoScaleActionFromString(s string) (AutoScaleAction, error) {
	switch s {
	case "Expand":
		return AutoScaleAction_Expand, nil
	case "Reduce":
		return AutoScaleAction_Reduce, nil
	}
	return AutoScaleAction(0), fmt.Errorf("not a valid AutoScaleAction string")
}

func AutoScaleActionPtr(v AutoScaleAction) *AutoScaleAction { return &v }

func (p AutoScaleAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AutoScaleAction) UnmarshalText(text []byte) error {
	q, err := AutoScaleActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AutoScaleMetricName int64

const (
	AutoScaleMetricName_BandWidth AutoScaleMetricName = 0
	AutoScaleMetricName_Spec      AutoScaleMetricName = 1
)

func (p AutoScaleMetricName) String() string {
	switch p {
	case AutoScaleMetricName_BandWidth:
		return "BandWidth"
	case AutoScaleMetricName_Spec:
		return "Spec"
	}
	return "<UNSET>"
}

func AutoScaleMetricNameFromString(s string) (AutoScaleMetricName, error) {
	switch s {
	case "BandWidth":
		return AutoScaleMetricName_BandWidth, nil
	case "Spec":
		return AutoScaleMetricName_Spec, nil
	}
	return AutoScaleMetricName(0), fmt.Errorf("not a valid AutoScaleMetricName string")
}

func AutoScaleMetricNamePtr(v AutoScaleMetricName) *AutoScaleMetricName { return &v }

func (p AutoScaleMetricName) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AutoScaleMetricName) UnmarshalText(text []byte) error {
	q, err := AutoScaleMetricNameFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type EventStatus int64

const (
	EventStatus_EventUndo     EventStatus = 0
	EventStatus_EventScaling  EventStatus = 1
	EventStatus_EventFinished EventStatus = 2
	EventStatus_EventError    EventStatus = 3
)

func (p EventStatus) String() string {
	switch p {
	case EventStatus_EventUndo:
		return "EventUndo"
	case EventStatus_EventScaling:
		return "EventScaling"
	case EventStatus_EventFinished:
		return "EventFinished"
	case EventStatus_EventError:
		return "EventError"
	}
	return "<UNSET>"
}

func EventStatusFromString(s string) (EventStatus, error) {
	switch s {
	case "EventUndo":
		return EventStatus_EventUndo, nil
	case "EventScaling":
		return EventStatus_EventScaling, nil
	case "EventFinished":
		return EventStatus_EventFinished, nil
	case "EventError":
		return EventStatus_EventError, nil
	}
	return EventStatus(0), fmt.Errorf("not a valid EventStatus string")
}

func EventStatusPtr(v EventStatus) *EventStatus { return &v }

func (p EventStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *EventStatus) UnmarshalText(text []byte) error {
	q, err := EventStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AutoScaleTriggerEventType int64

const (
	AutoScaleTriggerEventType_Manual AutoScaleTriggerEventType = 0
	AutoScaleTriggerEventType_Auto   AutoScaleTriggerEventType = 1
)

func (p AutoScaleTriggerEventType) String() string {
	switch p {
	case AutoScaleTriggerEventType_Manual:
		return "Manual"
	case AutoScaleTriggerEventType_Auto:
		return "Auto"
	}
	return "<UNSET>"
}

func AutoScaleTriggerEventTypeFromString(s string) (AutoScaleTriggerEventType, error) {
	switch s {
	case "Manual":
		return AutoScaleTriggerEventType_Manual, nil
	case "Auto":
		return AutoScaleTriggerEventType_Auto, nil
	}
	return AutoScaleTriggerEventType(0), fmt.Errorf("not a valid AutoScaleTriggerEventType string")
}

func AutoScaleTriggerEventTypePtr(v AutoScaleTriggerEventType) *AutoScaleTriggerEventType { return &v }

func (p AutoScaleTriggerEventType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AutoScaleTriggerEventType) UnmarshalText(text []byte) error {
	q, err := AutoScaleTriggerEventTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TaskStatus int64

const (
	TaskStatus_Running              TaskStatus = 0
	TaskStatus_Success              TaskStatus = 1
	TaskStatus_Failed               TaskStatus = 2
	TaskStatus_Timeout              TaskStatus = 3
	TaskStatus_Running_BeforeSwitch TaskStatus = 4
	TaskStatus_Running_AfterSwitch  TaskStatus = 5
	TaskStatus_Running_Switching    TaskStatus = 6
	TaskStatus_WaitSwitch           TaskStatus = 7
	TaskStatus_Rollbacking          TaskStatus = 8
	TaskStatus_RollbackFailed       TaskStatus = 9
	TaskStatus_Canceled             TaskStatus = 10
	TaskStatus_Paused               TaskStatus = 11
	TaskStatus_Stopped              TaskStatus = 12
	TaskStatus_Stopping             TaskStatus = 13
	TaskStatus_WaitStart            TaskStatus = 14
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_Running:
		return "Running"
	case TaskStatus_Success:
		return "Success"
	case TaskStatus_Failed:
		return "Failed"
	case TaskStatus_Timeout:
		return "Timeout"
	case TaskStatus_Running_BeforeSwitch:
		return "Running_BeforeSwitch"
	case TaskStatus_Running_AfterSwitch:
		return "Running_AfterSwitch"
	case TaskStatus_Running_Switching:
		return "Running_Switching"
	case TaskStatus_WaitSwitch:
		return "WaitSwitch"
	case TaskStatus_Rollbacking:
		return "Rollbacking"
	case TaskStatus_RollbackFailed:
		return "RollbackFailed"
	case TaskStatus_Canceled:
		return "Canceled"
	case TaskStatus_Paused:
		return "Paused"
	case TaskStatus_Stopped:
		return "Stopped"
	case TaskStatus_Stopping:
		return "Stopping"
	case TaskStatus_WaitStart:
		return "WaitStart"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "Running":
		return TaskStatus_Running, nil
	case "Success":
		return TaskStatus_Success, nil
	case "Failed":
		return TaskStatus_Failed, nil
	case "Timeout":
		return TaskStatus_Timeout, nil
	case "Running_BeforeSwitch":
		return TaskStatus_Running_BeforeSwitch, nil
	case "Running_AfterSwitch":
		return TaskStatus_Running_AfterSwitch, nil
	case "Running_Switching":
		return TaskStatus_Running_Switching, nil
	case "WaitSwitch":
		return TaskStatus_WaitSwitch, nil
	case "Rollbacking":
		return TaskStatus_Rollbacking, nil
	case "RollbackFailed":
		return TaskStatus_RollbackFailed, nil
	case "Canceled":
		return TaskStatus_Canceled, nil
	case "Paused":
		return TaskStatus_Paused, nil
	case "Stopped":
		return TaskStatus_Stopped, nil
	case "Stopping":
		return TaskStatus_Stopping, nil
	case "WaitStart":
		return TaskStatus_WaitStart, nil
	}
	return TaskStatus(0), fmt.Errorf("not a valid TaskStatus string")
}

func TaskStatusPtr(v TaskStatus) *TaskStatus { return &v }

func (p TaskStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TaskStatus) UnmarshalText(text []byte) error {
	q, err := TaskStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ScaleNodeType int64

const (
	ScaleNodeType_Primary   ScaleNodeType = 0
	ScaleNodeType_Secondary ScaleNodeType = 1
	ScaleNodeType_ReadOnly  ScaleNodeType = 2
)

func (p ScaleNodeType) String() string {
	switch p {
	case ScaleNodeType_Primary:
		return "Primary"
	case ScaleNodeType_Secondary:
		return "Secondary"
	case ScaleNodeType_ReadOnly:
		return "ReadOnly"
	}
	return "<UNSET>"
}

func ScaleNodeTypeFromString(s string) (ScaleNodeType, error) {
	switch s {
	case "Primary":
		return ScaleNodeType_Primary, nil
	case "Secondary":
		return ScaleNodeType_Secondary, nil
	case "ReadOnly":
		return ScaleNodeType_ReadOnly, nil
	}
	return ScaleNodeType(0), fmt.Errorf("not a valid ScaleNodeType string")
}

func ScaleNodeTypePtr(v ScaleNodeType) *ScaleNodeType { return &v }

func (p ScaleNodeType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ScaleNodeType) UnmarshalText(text []byte) error {
	q, err := ScaleNodeTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type LocalSpecScalingRuleStatus int64

const (
	LocalSpecScalingRuleStatus_NoTriggered     LocalSpecScalingRuleStatus = 0
	LocalSpecScalingRuleStatus_TriggerExpanded LocalSpecScalingRuleStatus = 1
	LocalSpecScalingRuleStatus_TriggerReduced  LocalSpecScalingRuleStatus = 2
	LocalSpecScalingRuleStatus_Closing         LocalSpecScalingRuleStatus = 3
	LocalSpecScalingRuleStatus_Triggering      LocalSpecScalingRuleStatus = 4
)

func (p LocalSpecScalingRuleStatus) String() string {
	switch p {
	case LocalSpecScalingRuleStatus_NoTriggered:
		return "NoTriggered"
	case LocalSpecScalingRuleStatus_TriggerExpanded:
		return "TriggerExpanded"
	case LocalSpecScalingRuleStatus_TriggerReduced:
		return "TriggerReduced"
	case LocalSpecScalingRuleStatus_Closing:
		return "Closing"
	case LocalSpecScalingRuleStatus_Triggering:
		return "Triggering"
	}
	return "<UNSET>"
}

func LocalSpecScalingRuleStatusFromString(s string) (LocalSpecScalingRuleStatus, error) {
	switch s {
	case "NoTriggered":
		return LocalSpecScalingRuleStatus_NoTriggered, nil
	case "TriggerExpanded":
		return LocalSpecScalingRuleStatus_TriggerExpanded, nil
	case "TriggerReduced":
		return LocalSpecScalingRuleStatus_TriggerReduced, nil
	case "Closing":
		return LocalSpecScalingRuleStatus_Closing, nil
	case "Triggering":
		return LocalSpecScalingRuleStatus_Triggering, nil
	}
	return LocalSpecScalingRuleStatus(0), fmt.Errorf("not a valid LocalSpecScalingRuleStatus string")
}

func LocalSpecScalingRuleStatusPtr(v LocalSpecScalingRuleStatus) *LocalSpecScalingRuleStatus {
	return &v
}

func (p LocalSpecScalingRuleStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *LocalSpecScalingRuleStatus) UnmarshalText(text []byte) error {
	q, err := LocalSpecScalingRuleStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ScalingRuleType int64

const (
	ScalingRuleType_AllNodes            ScalingRuleType = 0
	ScalingRuleType_ReadOnly            ScalingRuleType = 1
	ScalingRuleType_PrimaryAndSecondary ScalingRuleType = 2
	ScalingRuleType_AllReadOnlyNodes    ScalingRuleType = 3
)

func (p ScalingRuleType) String() string {
	switch p {
	case ScalingRuleType_AllNodes:
		return "AllNodes"
	case ScalingRuleType_ReadOnly:
		return "ReadOnly"
	case ScalingRuleType_PrimaryAndSecondary:
		return "PrimaryAndSecondary"
	case ScalingRuleType_AllReadOnlyNodes:
		return "AllReadOnlyNodes"
	}
	return "<UNSET>"
}

func ScalingRuleTypeFromString(s string) (ScalingRuleType, error) {
	switch s {
	case "AllNodes":
		return ScalingRuleType_AllNodes, nil
	case "ReadOnly":
		return ScalingRuleType_ReadOnly, nil
	case "PrimaryAndSecondary":
		return ScalingRuleType_PrimaryAndSecondary, nil
	case "AllReadOnlyNodes":
		return ScalingRuleType_AllReadOnlyNodes, nil
	}
	return ScalingRuleType(0), fmt.Errorf("not a valid ScalingRuleType string")
}

func ScalingRuleTypePtr(v ScalingRuleType) *ScalingRuleType { return &v }

func (p ScalingRuleType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ScalingRuleType) UnmarshalText(text []byte) error {
	q, err := ScalingRuleTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TriggerEventType int64

const (
	TriggerEventType_Unknown         TriggerEventType = 0
	TriggerEventType_Manual          TriggerEventType = 1
	TriggerEventType_AutoRule        TriggerEventType = 2
	TriggerEventType_AutoRuleReset   TriggerEventType = 3
	TriggerEventType_InstanceChanged TriggerEventType = 4
)

func (p TriggerEventType) String() string {
	switch p {
	case TriggerEventType_Unknown:
		return "Unknown"
	case TriggerEventType_Manual:
		return "Manual"
	case TriggerEventType_AutoRule:
		return "AutoRule"
	case TriggerEventType_AutoRuleReset:
		return "AutoRuleReset"
	case TriggerEventType_InstanceChanged:
		return "InstanceChanged"
	}
	return "<UNSET>"
}

func TriggerEventTypeFromString(s string) (TriggerEventType, error) {
	switch s {
	case "Unknown":
		return TriggerEventType_Unknown, nil
	case "Manual":
		return TriggerEventType_Manual, nil
	case "AutoRule":
		return TriggerEventType_AutoRule, nil
	case "AutoRuleReset":
		return TriggerEventType_AutoRuleReset, nil
	case "InstanceChanged":
		return TriggerEventType_InstanceChanged, nil
	}
	return TriggerEventType(0), fmt.Errorf("not a valid TriggerEventType string")
}

func TriggerEventTypePtr(v TriggerEventType) *TriggerEventType { return &v }

func (p TriggerEventType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TriggerEventType) UnmarshalText(text []byte) error {
	q, err := TriggerEventTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AutoScaleReq struct {
	InstanceType      InstanceType        `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId        string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	InstanceName      string              `thrift:"InstanceName,3,required" frugal:"3,required,string" json:"InstanceName"`
	RegionId          string              `thrift:"RegionId,4,required" frugal:"4,required,string" json:"RegionId"`
	ObservationWindow int32               `thrift:"ObservationWindow,5,required" frugal:"5,required,i32" json:"ObservationWindow"`
	Configs           []*RuleConfig       `thrift:"Configs,6,required" frugal:"6,required,list<RuleConfig>" json:"Configs"`
	Metric            AutoScaleMetricName `thrift:"Metric,7,required" frugal:"7,required,AutoScaleMetricName" json:"Metric"`
}

func NewAutoScaleReq() *AutoScaleReq {
	return &AutoScaleReq{}
}

func (p *AutoScaleReq) InitDefault() {
}

func (p *AutoScaleReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *AutoScaleReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AutoScaleReq) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *AutoScaleReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *AutoScaleReq) GetObservationWindow() (v int32) {
	return p.ObservationWindow
}

func (p *AutoScaleReq) GetConfigs() (v []*RuleConfig) {
	return p.Configs
}

func (p *AutoScaleReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}
func (p *AutoScaleReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *AutoScaleReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AutoScaleReq) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *AutoScaleReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *AutoScaleReq) SetObservationWindow(val int32) {
	p.ObservationWindow = val
}
func (p *AutoScaleReq) SetConfigs(val []*RuleConfig) {
	p.Configs = val
}
func (p *AutoScaleReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}

var fieldIDToName_AutoScaleReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "InstanceName",
	4: "RegionId",
	5: "ObservationWindow",
	6: "Configs",
	7: "Metric",
}

func (p *AutoScaleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetRegionId bool = false
	var issetObservationWindow bool = false
	var issetConfigs bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetObservationWindow = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetConfigs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetObservationWindow {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetConfigs {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoScaleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AutoScaleReq[fieldId]))
}

func (p *AutoScaleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *AutoScaleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AutoScaleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *AutoScaleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *AutoScaleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ObservationWindow = _field
	return nil
}
func (p *AutoScaleReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RuleConfig, 0, size)
	values := make([]RuleConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Configs = _field
	return nil
}
func (p *AutoScaleReq) ReadField7(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}

func (p *AutoScaleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoScaleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoScaleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoScaleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoScaleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoScaleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AutoScaleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ObservationWindow", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ObservationWindow); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AutoScaleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Configs", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Configs)); err != nil {
		return err
	}
	for _, v := range p.Configs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AutoScaleReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AutoScaleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoScaleReq(%+v)", *p)

}

func (p *AutoScaleReq) DeepEqual(ano *AutoScaleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field5DeepEqual(ano.ObservationWindow) {
		return false
	}
	if !p.Field6DeepEqual(ano.Configs) {
		return false
	}
	if !p.Field7DeepEqual(ano.Metric) {
		return false
	}
	return true
}

func (p *AutoScaleReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *AutoScaleReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleReq) Field5DeepEqual(src int32) bool {

	if p.ObservationWindow != src {
		return false
	}
	return true
}
func (p *AutoScaleReq) Field6DeepEqual(src []*RuleConfig) bool {

	if len(p.Configs) != len(src) {
		return false
	}
	for i, v := range p.Configs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *AutoScaleReq) Field7DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}

type AutoScaleResp struct {
}

func NewAutoScaleResp() *AutoScaleResp {
	return &AutoScaleResp{}
}

func (p *AutoScaleResp) InitDefault() {
}

var fieldIDToName_AutoScaleResp = map[int16]string{}

func (p *AutoScaleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AutoScaleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleResp")

	if err = oprot.WriteStructBegin("AutoScaleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoScaleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoScaleResp(%+v)", *p)

}

func (p *AutoScaleResp) DeepEqual(ano *AutoScaleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type RuleConfig struct {
	Action       AutoScaleAction `thrift:"Action,1,required" frugal:"1,required,AutoScaleAction" json:"Action"`
	IsOpen       bool            `thrift:"IsOpen,2,required" frugal:"2,required,bool" json:"IsOpen"`
	Threshold    float64         `thrift:"Threshold,3,required" frugal:"3,required,double" json:"Threshold"`
	ScalingLimit *string         `thrift:"ScalingLimit,4,optional" frugal:"4,optional,string" json:"ScalingLimit,omitempty"`
}

func NewRuleConfig() *RuleConfig {
	return &RuleConfig{}
}

func (p *RuleConfig) InitDefault() {
}

func (p *RuleConfig) GetAction() (v AutoScaleAction) {
	return p.Action
}

func (p *RuleConfig) GetIsOpen() (v bool) {
	return p.IsOpen
}

func (p *RuleConfig) GetThreshold() (v float64) {
	return p.Threshold
}

var RuleConfig_ScalingLimit_DEFAULT string

func (p *RuleConfig) GetScalingLimit() (v string) {
	if !p.IsSetScalingLimit() {
		return RuleConfig_ScalingLimit_DEFAULT
	}
	return *p.ScalingLimit
}
func (p *RuleConfig) SetAction(val AutoScaleAction) {
	p.Action = val
}
func (p *RuleConfig) SetIsOpen(val bool) {
	p.IsOpen = val
}
func (p *RuleConfig) SetThreshold(val float64) {
	p.Threshold = val
}
func (p *RuleConfig) SetScalingLimit(val *string) {
	p.ScalingLimit = val
}

var fieldIDToName_RuleConfig = map[int16]string{
	1: "Action",
	2: "IsOpen",
	3: "Threshold",
	4: "ScalingLimit",
}

func (p *RuleConfig) IsSetScalingLimit() bool {
	return p.ScalingLimit != nil
}

func (p *RuleConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RuleConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAction bool = false
	var issetIsOpen bool = false
	var issetThreshold bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsOpen = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAction {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIsOpen {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetThreshold {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RuleConfig[fieldId]))
}

func (p *RuleConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field AutoScaleAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleAction(v)
	}
	p.Action = _field
	return nil
}
func (p *RuleConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsOpen = _field
	return nil
}
func (p *RuleConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Threshold = _field
	return nil
}
func (p *RuleConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ScalingLimit = _field
	return nil
}

func (p *RuleConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RuleConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("RuleConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RuleConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Action", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RuleConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsOpen", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsOpen); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RuleConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Threshold", thrift.DOUBLE, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Threshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RuleConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetScalingLimit() {
		if err = oprot.WriteFieldBegin("ScalingLimit", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ScalingLimit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RuleConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleConfig(%+v)", *p)

}

func (p *RuleConfig) DeepEqual(ano *RuleConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Action) {
		return false
	}
	if !p.Field2DeepEqual(ano.IsOpen) {
		return false
	}
	if !p.Field3DeepEqual(ano.Threshold) {
		return false
	}
	if !p.Field4DeepEqual(ano.ScalingLimit) {
		return false
	}
	return true
}

func (p *RuleConfig) Field1DeepEqual(src AutoScaleAction) bool {

	if p.Action != src {
		return false
	}
	return true
}
func (p *RuleConfig) Field2DeepEqual(src bool) bool {

	if p.IsOpen != src {
		return false
	}
	return true
}
func (p *RuleConfig) Field3DeepEqual(src float64) bool {

	if p.Threshold != src {
		return false
	}
	return true
}
func (p *RuleConfig) Field4DeepEqual(src *string) bool {

	if p.ScalingLimit == src {
		return true
	} else if p.ScalingLimit == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ScalingLimit, *src) != 0 {
		return false
	}
	return true
}

type DescribeAutoScaleEventsReq struct {
	InstanceType         InstanceType          `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId           string                `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	RegionId             string                `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	Metric               AutoScaleMetricName   `thrift:"Metric,4,required" frugal:"4,required,AutoScaleMetricName" json:"Metric"`
	AutoScaleSearchParam *AutoScaleSearchParam `thrift:"autoScaleSearchParam,5,optional" frugal:"5,optional,AutoScaleSearchParam" json:"autoScaleSearchParam,omitempty"`
	PageNumber           *int32                `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	PageSize             *int32                `thrift:"PageSize,7,optional" frugal:"7,optional,i32" json:"PageSize,omitempty"`
	SortBy               *SortBy               `thrift:"SortBy,8,optional" frugal:"8,optional,SortBy" json:"SortBy,omitempty"`
}

func NewDescribeAutoScaleEventsReq() *DescribeAutoScaleEventsReq {
	return &DescribeAutoScaleEventsReq{}
}

func (p *DescribeAutoScaleEventsReq) InitDefault() {
}

func (p *DescribeAutoScaleEventsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAutoScaleEventsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAutoScaleEventsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAutoScaleEventsReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}

var DescribeAutoScaleEventsReq_AutoScaleSearchParam_DEFAULT *AutoScaleSearchParam

func (p *DescribeAutoScaleEventsReq) GetAutoScaleSearchParam() (v *AutoScaleSearchParam) {
	if !p.IsSetAutoScaleSearchParam() {
		return DescribeAutoScaleEventsReq_AutoScaleSearchParam_DEFAULT
	}
	return p.AutoScaleSearchParam
}

var DescribeAutoScaleEventsReq_PageNumber_DEFAULT int32

func (p *DescribeAutoScaleEventsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeAutoScaleEventsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeAutoScaleEventsReq_PageSize_DEFAULT int32

func (p *DescribeAutoScaleEventsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeAutoScaleEventsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeAutoScaleEventsReq_SortBy_DEFAULT SortBy

func (p *DescribeAutoScaleEventsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeAutoScaleEventsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}
func (p *DescribeAutoScaleEventsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAutoScaleEventsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAutoScaleEventsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAutoScaleEventsReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}
func (p *DescribeAutoScaleEventsReq) SetAutoScaleSearchParam(val *AutoScaleSearchParam) {
	p.AutoScaleSearchParam = val
}
func (p *DescribeAutoScaleEventsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeAutoScaleEventsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeAutoScaleEventsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}

var fieldIDToName_DescribeAutoScaleEventsReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "RegionId",
	4: "Metric",
	5: "autoScaleSearchParam",
	6: "PageNumber",
	7: "PageSize",
	8: "SortBy",
}

func (p *DescribeAutoScaleEventsReq) IsSetAutoScaleSearchParam() bool {
	return p.AutoScaleSearchParam != nil
}

func (p *DescribeAutoScaleEventsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeAutoScaleEventsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeAutoScaleEventsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeAutoScaleEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetRegionId bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleEventsReq[fieldId]))
}

func (p *DescribeAutoScaleEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField5(iprot thrift.TProtocol) error {
	_field := NewAutoScaleSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AutoScaleSearchParam = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeAutoScaleEventsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}

func (p *DescribeAutoScaleEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleSearchParam() {
		if err = oprot.WriteFieldBegin("autoScaleSearchParam", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AutoScaleSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleEventsReq(%+v)", *p)

}

func (p *DescribeAutoScaleEventsReq) DeepEqual(ano *DescribeAutoScaleEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Metric) {
		return false
	}
	if !p.Field5DeepEqual(ano.AutoScaleSearchParam) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field8DeepEqual(ano.SortBy) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleEventsReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field4DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field5DeepEqual(src *AutoScaleSearchParam) bool {

	if !p.AutoScaleSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field7DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsReq) Field8DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}

type AutoScaleSearchParam struct {
	EventId   *string          `thrift:"EventId,1,optional" frugal:"1,optional,string" json:"EventId,omitempty"`
	Action    *AutoScaleAction `thrift:"Action,2,optional" frugal:"2,optional,AutoScaleAction" json:"Action,omitempty"`
	StartTime *int64           `thrift:"StartTime,3,optional" frugal:"3,optional,i64" json:"StartTime,omitempty"`
	EndTime   *int64           `thrift:"EndTime,4,optional" frugal:"4,optional,i64" json:"EndTime,omitempty"`
}

func NewAutoScaleSearchParam() *AutoScaleSearchParam {
	return &AutoScaleSearchParam{}
}

func (p *AutoScaleSearchParam) InitDefault() {
}

var AutoScaleSearchParam_EventId_DEFAULT string

func (p *AutoScaleSearchParam) GetEventId() (v string) {
	if !p.IsSetEventId() {
		return AutoScaleSearchParam_EventId_DEFAULT
	}
	return *p.EventId
}

var AutoScaleSearchParam_Action_DEFAULT AutoScaleAction

func (p *AutoScaleSearchParam) GetAction() (v AutoScaleAction) {
	if !p.IsSetAction() {
		return AutoScaleSearchParam_Action_DEFAULT
	}
	return *p.Action
}

var AutoScaleSearchParam_StartTime_DEFAULT int64

func (p *AutoScaleSearchParam) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return AutoScaleSearchParam_StartTime_DEFAULT
	}
	return *p.StartTime
}

var AutoScaleSearchParam_EndTime_DEFAULT int64

func (p *AutoScaleSearchParam) GetEndTime() (v int64) {
	if !p.IsSetEndTime() {
		return AutoScaleSearchParam_EndTime_DEFAULT
	}
	return *p.EndTime
}
func (p *AutoScaleSearchParam) SetEventId(val *string) {
	p.EventId = val
}
func (p *AutoScaleSearchParam) SetAction(val *AutoScaleAction) {
	p.Action = val
}
func (p *AutoScaleSearchParam) SetStartTime(val *int64) {
	p.StartTime = val
}
func (p *AutoScaleSearchParam) SetEndTime(val *int64) {
	p.EndTime = val
}

var fieldIDToName_AutoScaleSearchParam = map[int16]string{
	1: "EventId",
	2: "Action",
	3: "StartTime",
	4: "EndTime",
}

func (p *AutoScaleSearchParam) IsSetEventId() bool {
	return p.EventId != nil
}

func (p *AutoScaleSearchParam) IsSetAction() bool {
	return p.Action != nil
}

func (p *AutoScaleSearchParam) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *AutoScaleSearchParam) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *AutoScaleSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoScaleSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AutoScaleSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EventId = _field
	return nil
}
func (p *AutoScaleSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *AutoScaleAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AutoScaleAction(v)
		_field = &tmp
	}
	p.Action = _field
	return nil
}
func (p *AutoScaleSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *AutoScaleSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}

func (p *AutoScaleSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoScaleSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoScaleSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventId() {
		if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EventId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoScaleSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAction() {
		if err = oprot.WriteFieldBegin("Action", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Action)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoScaleSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoScaleSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AutoScaleSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoScaleSearchParam(%+v)", *p)

}

func (p *AutoScaleSearchParam) DeepEqual(ano *AutoScaleSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Action) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *AutoScaleSearchParam) Field1DeepEqual(src *string) bool {

	if p.EventId == src {
		return true
	} else if p.EventId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EventId, *src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleSearchParam) Field2DeepEqual(src *AutoScaleAction) bool {

	if p.Action == src {
		return true
	} else if p.Action == nil || src == nil {
		return false
	}
	if *p.Action != *src {
		return false
	}
	return true
}
func (p *AutoScaleSearchParam) Field3DeepEqual(src *int64) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if *p.StartTime != *src {
		return false
	}
	return true
}
func (p *AutoScaleSearchParam) Field4DeepEqual(src *int64) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if *p.EndTime != *src {
		return false
	}
	return true
}

type DescribeAutoScaleEventsResp struct {
	Total           int32                 `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	AutoScaleEvents []*AutoScaleEventItem `thrift:"AutoScaleEvents,2,required" frugal:"2,required,list<AutoScaleEventItem>" json:"AutoScaleEvents"`
}

func NewDescribeAutoScaleEventsResp() *DescribeAutoScaleEventsResp {
	return &DescribeAutoScaleEventsResp{}
}

func (p *DescribeAutoScaleEventsResp) InitDefault() {
}

func (p *DescribeAutoScaleEventsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeAutoScaleEventsResp) GetAutoScaleEvents() (v []*AutoScaleEventItem) {
	return p.AutoScaleEvents
}
func (p *DescribeAutoScaleEventsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeAutoScaleEventsResp) SetAutoScaleEvents(val []*AutoScaleEventItem) {
	p.AutoScaleEvents = val
}

var fieldIDToName_DescribeAutoScaleEventsResp = map[int16]string{
	1: "Total",
	2: "AutoScaleEvents",
}

func (p *DescribeAutoScaleEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetAutoScaleEvents bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAutoScaleEvents = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAutoScaleEvents {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleEventsResp[fieldId]))
}

func (p *DescribeAutoScaleEventsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeAutoScaleEventsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AutoScaleEventItem, 0, size)
	values := make([]AutoScaleEventItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AutoScaleEvents = _field
	return nil
}

func (p *DescribeAutoScaleEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoScaleEvents", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AutoScaleEvents)); err != nil {
		return err
	}
	for _, v := range p.AutoScaleEvents {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleEventsResp(%+v)", *p)

}

func (p *DescribeAutoScaleEventsResp) DeepEqual(ano *DescribeAutoScaleEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.AutoScaleEvents) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleEventsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleEventsResp) Field2DeepEqual(src []*AutoScaleEventItem) bool {

	if len(p.AutoScaleEvents) != len(src) {
		return false
	}
	for i, v := range p.AutoScaleEvents {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AutoScaleEventItem struct {
	EventId             string              `thrift:"EventId,1,required" frugal:"1,required,string" json:"EventId"`
	BeforeAutoScale     int32               `thrift:"BeforeAutoScale,2,required" frugal:"2,required,i32" json:"BeforeAutoScale"`
	AfterAutoScale      int32               `thrift:"AfterAutoScale,3,required" frugal:"3,required,i32" json:"AfterAutoScale"`
	Action              AutoScaleAction     `thrift:"Action,4,required" frugal:"4,required,AutoScaleAction" json:"Action"`
	StartTime           string              `thrift:"StartTime,5,required" frugal:"5,required,string" json:"StartTime"`
	EventStatus         EventStatus         `thrift:"EventStatus,6,required" frugal:"6,required,EventStatus" json:"EventStatus"`
	TriggerCondition    []*TriggerCondition `thrift:"TriggerCondition,7,required" frugal:"7,required,list<TriggerCondition>" json:"TriggerCondition"`
	AutoScaleMetricName AutoScaleMetricName `thrift:"AutoScaleMetricName,8,required" frugal:"8,required,AutoScaleMetricName" json:"AutoScaleMetricName"`
	TriggerValue        string              `thrift:"TriggerValue,9,required" frugal:"9,required,string" json:"TriggerValue"`
	Before              string              `thrift:"Before,10,required" frugal:"10,required,string" json:"Before"`
	After               string              `thrift:"After,11,required" frugal:"11,required,string" json:"After"`
	RuleId              string              `thrift:"RuleId,12,required" frugal:"12,required,string" json:"RuleId"`
	Memo                string              `thrift:"Memo,13,required" frugal:"13,required,string" json:"Memo"`
}

func NewAutoScaleEventItem() *AutoScaleEventItem {
	return &AutoScaleEventItem{}
}

func (p *AutoScaleEventItem) InitDefault() {
}

func (p *AutoScaleEventItem) GetEventId() (v string) {
	return p.EventId
}

func (p *AutoScaleEventItem) GetBeforeAutoScale() (v int32) {
	return p.BeforeAutoScale
}

func (p *AutoScaleEventItem) GetAfterAutoScale() (v int32) {
	return p.AfterAutoScale
}

func (p *AutoScaleEventItem) GetAction() (v AutoScaleAction) {
	return p.Action
}

func (p *AutoScaleEventItem) GetStartTime() (v string) {
	return p.StartTime
}

func (p *AutoScaleEventItem) GetEventStatus() (v EventStatus) {
	return p.EventStatus
}

func (p *AutoScaleEventItem) GetTriggerCondition() (v []*TriggerCondition) {
	return p.TriggerCondition
}

func (p *AutoScaleEventItem) GetAutoScaleMetricName() (v AutoScaleMetricName) {
	return p.AutoScaleMetricName
}

func (p *AutoScaleEventItem) GetTriggerValue() (v string) {
	return p.TriggerValue
}

func (p *AutoScaleEventItem) GetBefore() (v string) {
	return p.Before
}

func (p *AutoScaleEventItem) GetAfter() (v string) {
	return p.After
}

func (p *AutoScaleEventItem) GetRuleId() (v string) {
	return p.RuleId
}

func (p *AutoScaleEventItem) GetMemo() (v string) {
	return p.Memo
}
func (p *AutoScaleEventItem) SetEventId(val string) {
	p.EventId = val
}
func (p *AutoScaleEventItem) SetBeforeAutoScale(val int32) {
	p.BeforeAutoScale = val
}
func (p *AutoScaleEventItem) SetAfterAutoScale(val int32) {
	p.AfterAutoScale = val
}
func (p *AutoScaleEventItem) SetAction(val AutoScaleAction) {
	p.Action = val
}
func (p *AutoScaleEventItem) SetStartTime(val string) {
	p.StartTime = val
}
func (p *AutoScaleEventItem) SetEventStatus(val EventStatus) {
	p.EventStatus = val
}
func (p *AutoScaleEventItem) SetTriggerCondition(val []*TriggerCondition) {
	p.TriggerCondition = val
}
func (p *AutoScaleEventItem) SetAutoScaleMetricName(val AutoScaleMetricName) {
	p.AutoScaleMetricName = val
}
func (p *AutoScaleEventItem) SetTriggerValue(val string) {
	p.TriggerValue = val
}
func (p *AutoScaleEventItem) SetBefore(val string) {
	p.Before = val
}
func (p *AutoScaleEventItem) SetAfter(val string) {
	p.After = val
}
func (p *AutoScaleEventItem) SetRuleId(val string) {
	p.RuleId = val
}
func (p *AutoScaleEventItem) SetMemo(val string) {
	p.Memo = val
}

var fieldIDToName_AutoScaleEventItem = map[int16]string{
	1:  "EventId",
	2:  "BeforeAutoScale",
	3:  "AfterAutoScale",
	4:  "Action",
	5:  "StartTime",
	6:  "EventStatus",
	7:  "TriggerCondition",
	8:  "AutoScaleMetricName",
	9:  "TriggerValue",
	10: "Before",
	11: "After",
	12: "RuleId",
	13: "Memo",
}

func (p *AutoScaleEventItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleEventItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEventId bool = false
	var issetBeforeAutoScale bool = false
	var issetAfterAutoScale bool = false
	var issetAction bool = false
	var issetStartTime bool = false
	var issetEventStatus bool = false
	var issetTriggerCondition bool = false
	var issetAutoScaleMetricName bool = false
	var issetTriggerValue bool = false
	var issetBefore bool = false
	var issetAfter bool = false
	var issetRuleId bool = false
	var issetMemo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBeforeAutoScale = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAfterAutoScale = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerCondition = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetAutoScaleMetricName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetBefore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetAfter = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEventId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBeforeAutoScale {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAfterAutoScale {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEventStatus {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTriggerCondition {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetAutoScaleMetricName {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetTriggerValue {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetBefore {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetAfter {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetRuleId {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetMemo {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoScaleEventItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AutoScaleEventItem[fieldId]))
}

func (p *AutoScaleEventItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventId = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BeforeAutoScale = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AfterAutoScale = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField4(iprot thrift.TProtocol) error {

	var _field AutoScaleAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleAction(v)
	}
	p.Action = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField6(iprot thrift.TProtocol) error {

	var _field EventStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EventStatus(v)
	}
	p.EventStatus = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TriggerCondition, 0, size)
	values := make([]TriggerCondition, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TriggerCondition = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField8(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.AutoScaleMetricName = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TriggerValue = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Before = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.After = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleId = _field
	return nil
}
func (p *AutoScaleEventItem) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Memo = _field
	return nil
}

func (p *AutoScaleEventItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScaleEventItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoScaleEventItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BeforeAutoScale", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BeforeAutoScale); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AfterAutoScale", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AfterAutoScale); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Action", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventStatus", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EventStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerCondition", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TriggerCondition)); err != nil {
		return err
	}
	for _, v := range p.TriggerCondition {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoScaleMetricName", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AutoScaleMetricName)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerValue", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TriggerValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Before", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Before); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("After", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.After); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RuleId", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *AutoScaleEventItem) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Memo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *AutoScaleEventItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoScaleEventItem(%+v)", *p)

}

func (p *AutoScaleEventItem) DeepEqual(ano *AutoScaleEventItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BeforeAutoScale) {
		return false
	}
	if !p.Field3DeepEqual(ano.AfterAutoScale) {
		return false
	}
	if !p.Field4DeepEqual(ano.Action) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.EventStatus) {
		return false
	}
	if !p.Field7DeepEqual(ano.TriggerCondition) {
		return false
	}
	if !p.Field8DeepEqual(ano.AutoScaleMetricName) {
		return false
	}
	if !p.Field9DeepEqual(ano.TriggerValue) {
		return false
	}
	if !p.Field10DeepEqual(ano.Before) {
		return false
	}
	if !p.Field11DeepEqual(ano.After) {
		return false
	}
	if !p.Field12DeepEqual(ano.RuleId) {
		return false
	}
	if !p.Field13DeepEqual(ano.Memo) {
		return false
	}
	return true
}

func (p *AutoScaleEventItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EventId, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field2DeepEqual(src int32) bool {

	if p.BeforeAutoScale != src {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field3DeepEqual(src int32) bool {

	if p.AfterAutoScale != src {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field4DeepEqual(src AutoScaleAction) bool {

	if p.Action != src {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field6DeepEqual(src EventStatus) bool {

	if p.EventStatus != src {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field7DeepEqual(src []*TriggerCondition) bool {

	if len(p.TriggerCondition) != len(src) {
		return false
	}
	for i, v := range p.TriggerCondition {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *AutoScaleEventItem) Field8DeepEqual(src AutoScaleMetricName) bool {

	if p.AutoScaleMetricName != src {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field9DeepEqual(src string) bool {

	if strings.Compare(p.TriggerValue, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field10DeepEqual(src string) bool {

	if strings.Compare(p.Before, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field11DeepEqual(src string) bool {

	if strings.Compare(p.After, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field12DeepEqual(src string) bool {

	if strings.Compare(p.RuleId, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScaleEventItem) Field13DeepEqual(src string) bool {

	if strings.Compare(p.Memo, src) != 0 {
		return false
	}
	return true
}

type TriggerCondition struct {
	MetricName         string `thrift:"MetricName,1,required" frugal:"1,required,string" json:"MetricName"`
	ComparisonOperator string `thrift:"ComparisonOperator,2,required" frugal:"2,required,string" json:"ComparisonOperator"`
	Threshold          string `thrift:"Threshold,3,required" frugal:"3,required,string" json:"Threshold"`
}

func NewTriggerCondition() *TriggerCondition {
	return &TriggerCondition{}
}

func (p *TriggerCondition) InitDefault() {
}

func (p *TriggerCondition) GetMetricName() (v string) {
	return p.MetricName
}

func (p *TriggerCondition) GetComparisonOperator() (v string) {
	return p.ComparisonOperator
}

func (p *TriggerCondition) GetThreshold() (v string) {
	return p.Threshold
}
func (p *TriggerCondition) SetMetricName(val string) {
	p.MetricName = val
}
func (p *TriggerCondition) SetComparisonOperator(val string) {
	p.ComparisonOperator = val
}
func (p *TriggerCondition) SetThreshold(val string) {
	p.Threshold = val
}

var fieldIDToName_TriggerCondition = map[int16]string{
	1: "MetricName",
	2: "ComparisonOperator",
	3: "Threshold",
}

func (p *TriggerCondition) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerCondition")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMetricName bool = false
	var issetComparisonOperator bool = false
	var issetThreshold bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetricName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetComparisonOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMetricName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComparisonOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetThreshold {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TriggerCondition[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TriggerCondition[fieldId]))
}

func (p *TriggerCondition) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MetricName = _field
	return nil
}
func (p *TriggerCondition) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComparisonOperator = _field
	return nil
}
func (p *TriggerCondition) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Threshold = _field
	return nil
}

func (p *TriggerCondition) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerCondition")

	var fieldId int16
	if err = oprot.WriteStructBegin("TriggerCondition"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerCondition) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MetricName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MetricName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TriggerCondition) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComparisonOperator", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComparisonOperator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TriggerCondition) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Threshold", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Threshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TriggerCondition) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerCondition(%+v)", *p)

}

func (p *TriggerCondition) DeepEqual(ano *TriggerCondition) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MetricName) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComparisonOperator) {
		return false
	}
	if !p.Field3DeepEqual(ano.Threshold) {
		return false
	}
	return true
}

func (p *TriggerCondition) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MetricName, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerCondition) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComparisonOperator, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerCondition) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Threshold, src) != 0 {
		return false
	}
	return true
}

type DescribeAutoScaleRulesReq struct {
	InstanceType InstanceType        `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	RegionId     string              `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	Metric       AutoScaleMetricName `thrift:"Metric,4,required" frugal:"4,required,AutoScaleMetricName" json:"Metric"`
}

func NewDescribeAutoScaleRulesReq() *DescribeAutoScaleRulesReq {
	return &DescribeAutoScaleRulesReq{}
}

func (p *DescribeAutoScaleRulesReq) InitDefault() {
}

func (p *DescribeAutoScaleRulesReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAutoScaleRulesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeAutoScaleRulesReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeAutoScaleRulesReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}
func (p *DescribeAutoScaleRulesReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAutoScaleRulesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAutoScaleRulesReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeAutoScaleRulesReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}

var fieldIDToName_DescribeAutoScaleRulesReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "RegionId",
	4: "Metric",
}

func (p *DescribeAutoScaleRulesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleRulesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetRegionId bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleRulesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleRulesReq[fieldId]))
}

func (p *DescribeAutoScaleRulesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAutoScaleRulesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAutoScaleRulesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeAutoScaleRulesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}

func (p *DescribeAutoScaleRulesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleRulesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleRulesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleRulesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleRulesReq(%+v)", *p)

}

func (p *DescribeAutoScaleRulesReq) DeepEqual(ano *DescribeAutoScaleRulesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Metric) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleRulesReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleRulesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoScaleRulesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoScaleRulesReq) Field4DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}

type DescribeAutoScaleRulesResp struct {
	InstanceId        string        `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ObservationWindow *int32        `thrift:"ObservationWindow,2,optional" frugal:"2,optional,i32" json:"ObservationWindow,omitempty"`
	Configs           []*RuleConfig `thrift:"Configs,3,optional" frugal:"3,optional,list<RuleConfig>" json:"Configs,omitempty"`
}

func NewDescribeAutoScaleRulesResp() *DescribeAutoScaleRulesResp {
	return &DescribeAutoScaleRulesResp{}
}

func (p *DescribeAutoScaleRulesResp) InitDefault() {
}

func (p *DescribeAutoScaleRulesResp) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeAutoScaleRulesResp_ObservationWindow_DEFAULT int32

func (p *DescribeAutoScaleRulesResp) GetObservationWindow() (v int32) {
	if !p.IsSetObservationWindow() {
		return DescribeAutoScaleRulesResp_ObservationWindow_DEFAULT
	}
	return *p.ObservationWindow
}

var DescribeAutoScaleRulesResp_Configs_DEFAULT []*RuleConfig

func (p *DescribeAutoScaleRulesResp) GetConfigs() (v []*RuleConfig) {
	if !p.IsSetConfigs() {
		return DescribeAutoScaleRulesResp_Configs_DEFAULT
	}
	return p.Configs
}
func (p *DescribeAutoScaleRulesResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeAutoScaleRulesResp) SetObservationWindow(val *int32) {
	p.ObservationWindow = val
}
func (p *DescribeAutoScaleRulesResp) SetConfigs(val []*RuleConfig) {
	p.Configs = val
}

var fieldIDToName_DescribeAutoScaleRulesResp = map[int16]string{
	1: "InstanceId",
	2: "ObservationWindow",
	3: "Configs",
}

func (p *DescribeAutoScaleRulesResp) IsSetObservationWindow() bool {
	return p.ObservationWindow != nil
}

func (p *DescribeAutoScaleRulesResp) IsSetConfigs() bool {
	return p.Configs != nil
}

func (p *DescribeAutoScaleRulesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleRulesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleRulesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleRulesResp[fieldId]))
}

func (p *DescribeAutoScaleRulesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAutoScaleRulesResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ObservationWindow = _field
	return nil
}
func (p *DescribeAutoScaleRulesResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RuleConfig, 0, size)
	values := make([]RuleConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Configs = _field
	return nil
}

func (p *DescribeAutoScaleRulesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleRulesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleRulesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleRulesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetObservationWindow() {
		if err = oprot.WriteFieldBegin("ObservationWindow", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ObservationWindow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConfigs() {
		if err = oprot.WriteFieldBegin("Configs", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Configs)); err != nil {
			return err
		}
		for _, v := range p.Configs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAutoScaleRulesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleRulesResp(%+v)", *p)

}

func (p *DescribeAutoScaleRulesResp) DeepEqual(ano *DescribeAutoScaleRulesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ObservationWindow) {
		return false
	}
	if !p.Field3DeepEqual(ano.Configs) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleRulesResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoScaleRulesResp) Field2DeepEqual(src *int32) bool {

	if p.ObservationWindow == src {
		return true
	} else if p.ObservationWindow == nil || src == nil {
		return false
	}
	if *p.ObservationWindow != *src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleRulesResp) Field3DeepEqual(src []*RuleConfig) bool {

	if len(p.Configs) != len(src) {
		return false
	}
	for i, v := range p.Configs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDiskDBAutoScalingConfigReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	RegionId     string       `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
}

func NewDescribeDiskDBAutoScalingConfigReq() *DescribeDiskDBAutoScalingConfigReq {
	return &DescribeDiskDBAutoScalingConfigReq{}
}

func (p *DescribeDiskDBAutoScalingConfigReq) InitDefault() {
}

func (p *DescribeDiskDBAutoScalingConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDiskDBAutoScalingConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDiskDBAutoScalingConfigReq) GetRegionId() (v string) {
	return p.RegionId
}
func (p *DescribeDiskDBAutoScalingConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDiskDBAutoScalingConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDiskDBAutoScalingConfigReq) SetRegionId(val string) {
	p.RegionId = val
}

var fieldIDToName_DescribeDiskDBAutoScalingConfigReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "RegionId",
}

func (p *DescribeDiskDBAutoScalingConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDiskDBAutoScalingConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDiskDBAutoScalingConfigReq[fieldId]))
}

func (p *DescribeDiskDBAutoScalingConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeDiskDBAutoScalingConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDiskDBAutoScalingConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDiskDBAutoScalingConfigReq(%+v)", *p)

}

func (p *DescribeDiskDBAutoScalingConfigReq) DeepEqual(ano *DescribeDiskDBAutoScalingConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeDiskDBAutoScalingConfigReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfigReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfigReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}

type DescribeDiskDBAutoScalingConfigResp struct {
	DiskDBAutoScalingConfig *DescribeDiskDBAutoScalingConfig `thrift:"DiskDBAutoScalingConfig,1,required" frugal:"1,required,DescribeDiskDBAutoScalingConfig" json:"DiskDBAutoScalingConfig"`
}

func NewDescribeDiskDBAutoScalingConfigResp() *DescribeDiskDBAutoScalingConfigResp {
	return &DescribeDiskDBAutoScalingConfigResp{}
}

func (p *DescribeDiskDBAutoScalingConfigResp) InitDefault() {
}

var DescribeDiskDBAutoScalingConfigResp_DiskDBAutoScalingConfig_DEFAULT *DescribeDiskDBAutoScalingConfig

func (p *DescribeDiskDBAutoScalingConfigResp) GetDiskDBAutoScalingConfig() (v *DescribeDiskDBAutoScalingConfig) {
	if !p.IsSetDiskDBAutoScalingConfig() {
		return DescribeDiskDBAutoScalingConfigResp_DiskDBAutoScalingConfig_DEFAULT
	}
	return p.DiskDBAutoScalingConfig
}
func (p *DescribeDiskDBAutoScalingConfigResp) SetDiskDBAutoScalingConfig(val *DescribeDiskDBAutoScalingConfig) {
	p.DiskDBAutoScalingConfig = val
}

var fieldIDToName_DescribeDiskDBAutoScalingConfigResp = map[int16]string{
	1: "DiskDBAutoScalingConfig",
}

func (p *DescribeDiskDBAutoScalingConfigResp) IsSetDiskDBAutoScalingConfig() bool {
	return p.DiskDBAutoScalingConfig != nil
}

func (p *DescribeDiskDBAutoScalingConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDiskDBAutoScalingConfig bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiskDBAutoScalingConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDiskDBAutoScalingConfig {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDiskDBAutoScalingConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDiskDBAutoScalingConfigResp[fieldId]))
}

func (p *DescribeDiskDBAutoScalingConfigResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDescribeDiskDBAutoScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DiskDBAutoScalingConfig = _field
	return nil
}

func (p *DescribeDiskDBAutoScalingConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDiskDBAutoScalingConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiskDBAutoScalingConfig", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.DiskDBAutoScalingConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDiskDBAutoScalingConfigResp(%+v)", *p)

}

func (p *DescribeDiskDBAutoScalingConfigResp) DeepEqual(ano *DescribeDiskDBAutoScalingConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DiskDBAutoScalingConfig) {
		return false
	}
	return true
}

func (p *DescribeDiskDBAutoScalingConfigResp) Field1DeepEqual(src *DescribeDiskDBAutoScalingConfig) bool {

	if !p.DiskDBAutoScalingConfig.DeepEqual(src) {
		return false
	}
	return true
}

type DescribeDiskDBAutoScalingConfig struct {
	EnableStorageAutoScale     bool  `thrift:"EnableStorageAutoScale,1,required" frugal:"1,required,bool" json:"EnableStorageAutoScale"`
	StorageUpperBound          int32 `thrift:"StorageUpperBound,2,required" frugal:"2,required,i32" json:"StorageUpperBound"`
	StorageThreshold           int32 `thrift:"StorageThreshold,3,required" frugal:"3,required,i32" json:"StorageThreshold"`
	StorageMaxCapacity         int32 `thrift:"StorageMaxCapacity,4,required" frugal:"4,required,i32" json:"StorageMaxCapacity"`
	StorageMinCapacity         int32 `thrift:"StorageMinCapacity,5,required" frugal:"5,required,i32" json:"StorageMinCapacity"`
	StorageMaxTriggerThreshold int32 `thrift:"StorageMaxTriggerThreshold,6,required" frugal:"6,required,i32" json:"StorageMaxTriggerThreshold"`
	StorageMinTriggerThreshold int32 `thrift:"StorageMinTriggerThreshold,7,required" frugal:"7,required,i32" json:"StorageMinTriggerThreshold"`
}

func NewDescribeDiskDBAutoScalingConfig() *DescribeDiskDBAutoScalingConfig {
	return &DescribeDiskDBAutoScalingConfig{}
}

func (p *DescribeDiskDBAutoScalingConfig) InitDefault() {
}

func (p *DescribeDiskDBAutoScalingConfig) GetEnableStorageAutoScale() (v bool) {
	return p.EnableStorageAutoScale
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageUpperBound() (v int32) {
	return p.StorageUpperBound
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageThreshold() (v int32) {
	return p.StorageThreshold
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageMaxCapacity() (v int32) {
	return p.StorageMaxCapacity
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageMinCapacity() (v int32) {
	return p.StorageMinCapacity
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageMaxTriggerThreshold() (v int32) {
	return p.StorageMaxTriggerThreshold
}

func (p *DescribeDiskDBAutoScalingConfig) GetStorageMinTriggerThreshold() (v int32) {
	return p.StorageMinTriggerThreshold
}
func (p *DescribeDiskDBAutoScalingConfig) SetEnableStorageAutoScale(val bool) {
	p.EnableStorageAutoScale = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageUpperBound(val int32) {
	p.StorageUpperBound = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageThreshold(val int32) {
	p.StorageThreshold = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageMaxCapacity(val int32) {
	p.StorageMaxCapacity = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageMinCapacity(val int32) {
	p.StorageMinCapacity = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageMaxTriggerThreshold(val int32) {
	p.StorageMaxTriggerThreshold = val
}
func (p *DescribeDiskDBAutoScalingConfig) SetStorageMinTriggerThreshold(val int32) {
	p.StorageMinTriggerThreshold = val
}

var fieldIDToName_DescribeDiskDBAutoScalingConfig = map[int16]string{
	1: "EnableStorageAutoScale",
	2: "StorageUpperBound",
	3: "StorageThreshold",
	4: "StorageMaxCapacity",
	5: "StorageMinCapacity",
	6: "StorageMaxTriggerThreshold",
	7: "StorageMinTriggerThreshold",
}

func (p *DescribeDiskDBAutoScalingConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEnableStorageAutoScale bool = false
	var issetStorageUpperBound bool = false
	var issetStorageThreshold bool = false
	var issetStorageMaxCapacity bool = false
	var issetStorageMinCapacity bool = false
	var issetStorageMaxTriggerThreshold bool = false
	var issetStorageMinTriggerThreshold bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableStorageAutoScale = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageUpperBound = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageMaxCapacity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageMinCapacity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageMaxTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageMinTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEnableStorageAutoScale {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStorageUpperBound {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStorageThreshold {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStorageMaxCapacity {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStorageMinCapacity {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStorageMaxTriggerThreshold {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStorageMinTriggerThreshold {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDiskDBAutoScalingConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDiskDBAutoScalingConfig[fieldId]))
}

func (p *DescribeDiskDBAutoScalingConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EnableStorageAutoScale = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageUpperBound = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageThreshold = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageMaxCapacity = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageMinCapacity = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageMaxTriggerThreshold = _field
	return nil
}
func (p *DescribeDiskDBAutoScalingConfig) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageMinTriggerThreshold = _field
	return nil
}

func (p *DescribeDiskDBAutoScalingConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskDBAutoScalingConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDiskDBAutoScalingConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableStorageAutoScale", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.EnableStorageAutoScale); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageUpperBound", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageUpperBound); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageThreshold", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageMaxCapacity", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageMaxCapacity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageMinCapacity", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageMinCapacity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageMaxTriggerThreshold", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageMaxTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageMinTriggerThreshold", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageMinTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDiskDBAutoScalingConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDiskDBAutoScalingConfig(%+v)", *p)

}

func (p *DescribeDiskDBAutoScalingConfig) DeepEqual(ano *DescribeDiskDBAutoScalingConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EnableStorageAutoScale) {
		return false
	}
	if !p.Field2DeepEqual(ano.StorageUpperBound) {
		return false
	}
	if !p.Field3DeepEqual(ano.StorageThreshold) {
		return false
	}
	if !p.Field4DeepEqual(ano.StorageMaxCapacity) {
		return false
	}
	if !p.Field5DeepEqual(ano.StorageMinCapacity) {
		return false
	}
	if !p.Field6DeepEqual(ano.StorageMaxTriggerThreshold) {
		return false
	}
	if !p.Field7DeepEqual(ano.StorageMinTriggerThreshold) {
		return false
	}
	return true
}

func (p *DescribeDiskDBAutoScalingConfig) Field1DeepEqual(src bool) bool {

	if p.EnableStorageAutoScale != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field2DeepEqual(src int32) bool {

	if p.StorageUpperBound != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field3DeepEqual(src int32) bool {

	if p.StorageThreshold != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field4DeepEqual(src int32) bool {

	if p.StorageMaxCapacity != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field5DeepEqual(src int32) bool {

	if p.StorageMinCapacity != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field6DeepEqual(src int32) bool {

	if p.StorageMaxTriggerThreshold != src {
		return false
	}
	return true
}
func (p *DescribeDiskDBAutoScalingConfig) Field7DeepEqual(src int32) bool {

	if p.StorageMinTriggerThreshold != src {
		return false
	}
	return true
}

type ModifyDiskDBAutoScalingConfigReq struct {
	InstanceType            InstanceType                     `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId                string                           `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId              string                           `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	DiskDBAutoScalingConfig *DescribeDiskDBAutoScalingConfig `thrift:"DiskDBAutoScalingConfig,4,optional" frugal:"4,optional,DescribeDiskDBAutoScalingConfig" json:"DiskDBAutoScalingConfig,omitempty"`
}

func NewModifyDiskDBAutoScalingConfigReq() *ModifyDiskDBAutoScalingConfigReq {
	return &ModifyDiskDBAutoScalingConfigReq{}
}

func (p *ModifyDiskDBAutoScalingConfigReq) InitDefault() {
}

func (p *ModifyDiskDBAutoScalingConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ModifyDiskDBAutoScalingConfigReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *ModifyDiskDBAutoScalingConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyDiskDBAutoScalingConfigReq_DiskDBAutoScalingConfig_DEFAULT *DescribeDiskDBAutoScalingConfig

func (p *ModifyDiskDBAutoScalingConfigReq) GetDiskDBAutoScalingConfig() (v *DescribeDiskDBAutoScalingConfig) {
	if !p.IsSetDiskDBAutoScalingConfig() {
		return ModifyDiskDBAutoScalingConfigReq_DiskDBAutoScalingConfig_DEFAULT
	}
	return p.DiskDBAutoScalingConfig
}
func (p *ModifyDiskDBAutoScalingConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyDiskDBAutoScalingConfigReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ModifyDiskDBAutoScalingConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDiskDBAutoScalingConfigReq) SetDiskDBAutoScalingConfig(val *DescribeDiskDBAutoScalingConfig) {
	p.DiskDBAutoScalingConfig = val
}

var fieldIDToName_ModifyDiskDBAutoScalingConfigReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceId",
	4: "DiskDBAutoScalingConfig",
}

func (p *ModifyDiskDBAutoScalingConfigReq) IsSetDiskDBAutoScalingConfig() bool {
	return p.DiskDBAutoScalingConfig != nil
}

func (p *ModifyDiskDBAutoScalingConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDiskDBAutoScalingConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDiskDBAutoScalingConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDiskDBAutoScalingConfigReq[fieldId]))
}

func (p *ModifyDiskDBAutoScalingConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyDiskDBAutoScalingConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ModifyDiskDBAutoScalingConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDiskDBAutoScalingConfigReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewDescribeDiskDBAutoScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DiskDBAutoScalingConfig = _field
	return nil
}

func (p *ModifyDiskDBAutoScalingConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDiskDBAutoScalingConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDiskDBAutoScalingConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDiskDBAutoScalingConfig() {
		if err = oprot.WriteFieldBegin("DiskDBAutoScalingConfig", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DiskDBAutoScalingConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDiskDBAutoScalingConfigReq(%+v)", *p)

}

func (p *ModifyDiskDBAutoScalingConfigReq) DeepEqual(ano *ModifyDiskDBAutoScalingConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DiskDBAutoScalingConfig) {
		return false
	}
	return true
}

func (p *ModifyDiskDBAutoScalingConfigReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyDiskDBAutoScalingConfigReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDiskDBAutoScalingConfigReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDiskDBAutoScalingConfigReq) Field4DeepEqual(src *DescribeDiskDBAutoScalingConfig) bool {

	if !p.DiskDBAutoScalingConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyDiskDBAutoScalingConfigResp struct {
}

func NewModifyDiskDBAutoScalingConfigResp() *ModifyDiskDBAutoScalingConfigResp {
	return &ModifyDiskDBAutoScalingConfigResp{}
}

func (p *ModifyDiskDBAutoScalingConfigResp) InitDefault() {
}

var fieldIDToName_ModifyDiskDBAutoScalingConfigResp = map[int16]string{}

func (p *ModifyDiskDBAutoScalingConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDiskDBAutoScalingConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDiskDBAutoScalingConfigResp")

	if err = oprot.WriteStructBegin("ModifyDiskDBAutoScalingConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDiskDBAutoScalingConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDiskDBAutoScalingConfigResp(%+v)", *p)

}

func (p *ModifyDiskDBAutoScalingConfigResp) DeepEqual(ano *ModifyDiskDBAutoScalingConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ModifyDBAutoStorageScalingReq struct {
	InstanceType            InstanceType                     `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId                string                           `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceId              string                           `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	DiskDBAutoScalingConfig *DescribeDiskDBAutoScalingConfig `thrift:"DiskDBAutoScalingConfig,4,optional" frugal:"4,optional,DescribeDiskDBAutoScalingConfig" json:"DiskDBAutoScalingConfig,omitempty"`
}

func NewModifyDBAutoStorageScalingReq() *ModifyDBAutoStorageScalingReq {
	return &ModifyDBAutoStorageScalingReq{}
}

func (p *ModifyDBAutoStorageScalingReq) InitDefault() {
}

func (p *ModifyDBAutoStorageScalingReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ModifyDBAutoStorageScalingReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *ModifyDBAutoStorageScalingReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyDBAutoStorageScalingReq_DiskDBAutoScalingConfig_DEFAULT *DescribeDiskDBAutoScalingConfig

func (p *ModifyDBAutoStorageScalingReq) GetDiskDBAutoScalingConfig() (v *DescribeDiskDBAutoScalingConfig) {
	if !p.IsSetDiskDBAutoScalingConfig() {
		return ModifyDBAutoStorageScalingReq_DiskDBAutoScalingConfig_DEFAULT
	}
	return p.DiskDBAutoScalingConfig
}
func (p *ModifyDBAutoStorageScalingReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyDBAutoStorageScalingReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ModifyDBAutoStorageScalingReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBAutoStorageScalingReq) SetDiskDBAutoScalingConfig(val *DescribeDiskDBAutoScalingConfig) {
	p.DiskDBAutoScalingConfig = val
}

var fieldIDToName_ModifyDBAutoStorageScalingReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceId",
	4: "DiskDBAutoScalingConfig",
}

func (p *ModifyDBAutoStorageScalingReq) IsSetDiskDBAutoScalingConfig() bool {
	return p.DiskDBAutoScalingConfig != nil
}

func (p *ModifyDBAutoStorageScalingReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoStorageScalingReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBAutoStorageScalingReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBAutoStorageScalingReq[fieldId]))
}

func (p *ModifyDBAutoStorageScalingReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyDBAutoStorageScalingReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ModifyDBAutoStorageScalingReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBAutoStorageScalingReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewDescribeDiskDBAutoScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DiskDBAutoScalingConfig = _field
	return nil
}

func (p *ModifyDBAutoStorageScalingReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoStorageScalingReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBAutoStorageScalingReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDiskDBAutoScalingConfig() {
		if err = oprot.WriteFieldBegin("DiskDBAutoScalingConfig", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DiskDBAutoScalingConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBAutoStorageScalingReq(%+v)", *p)

}

func (p *ModifyDBAutoStorageScalingReq) DeepEqual(ano *ModifyDBAutoStorageScalingReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.DiskDBAutoScalingConfig) {
		return false
	}
	return true
}

func (p *ModifyDBAutoStorageScalingReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyDBAutoStorageScalingReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBAutoStorageScalingReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBAutoStorageScalingReq) Field4DeepEqual(src *DescribeDiskDBAutoScalingConfig) bool {

	if !p.DiskDBAutoScalingConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyDBAutoStorageScalingResp struct {
}

func NewModifyDBAutoStorageScalingResp() *ModifyDBAutoStorageScalingResp {
	return &ModifyDBAutoStorageScalingResp{}
}

func (p *ModifyDBAutoStorageScalingResp) InitDefault() {
}

var fieldIDToName_ModifyDBAutoStorageScalingResp = map[int16]string{}

func (p *ModifyDBAutoStorageScalingResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoStorageScalingResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoStorageScalingResp")

	if err = oprot.WriteStructBegin("ModifyDBAutoStorageScalingResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBAutoStorageScalingResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBAutoStorageScalingResp(%+v)", *p)

}

func (p *ModifyDBAutoStorageScalingResp) DeepEqual(ano *ModifyDBAutoStorageScalingResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeDiskAutoScaleEventsReq struct {
	InstanceType         InstanceType          `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId           string                `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	RegionId             string                `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	Metric               AutoScaleMetricName   `thrift:"Metric,4,required" frugal:"4,required,AutoScaleMetricName" json:"Metric"`
	AutoScaleSearchParam *AutoScaleSearchParam `thrift:"autoScaleSearchParam,5,optional" frugal:"5,optional,AutoScaleSearchParam" json:"autoScaleSearchParam,omitempty"`
	PageNumber           *int32                `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	PageSize             *int32                `thrift:"PageSize,7,optional" frugal:"7,optional,i32" json:"PageSize,omitempty"`
	SortBy               *SortBy               `thrift:"SortBy,8,optional" frugal:"8,optional,SortBy" json:"SortBy,omitempty"`
	CreationStartTime    *string               `thrift:"CreationStartTime,9,optional" frugal:"9,optional,string" json:"CreationStartTime,omitempty"`
	CreationEndTime      *string               `thrift:"CreationEndTime,10,optional" frugal:"10,optional,string" json:"CreationEndTime,omitempty"`
}

func NewDescribeDiskAutoScaleEventsReq() *DescribeDiskAutoScaleEventsReq {
	return &DescribeDiskAutoScaleEventsReq{}
}

func (p *DescribeDiskAutoScaleEventsReq) InitDefault() {
}

func (p *DescribeDiskAutoScaleEventsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDiskAutoScaleEventsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDiskAutoScaleEventsReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeDiskAutoScaleEventsReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}

var DescribeDiskAutoScaleEventsReq_AutoScaleSearchParam_DEFAULT *AutoScaleSearchParam

func (p *DescribeDiskAutoScaleEventsReq) GetAutoScaleSearchParam() (v *AutoScaleSearchParam) {
	if !p.IsSetAutoScaleSearchParam() {
		return DescribeDiskAutoScaleEventsReq_AutoScaleSearchParam_DEFAULT
	}
	return p.AutoScaleSearchParam
}

var DescribeDiskAutoScaleEventsReq_PageNumber_DEFAULT int32

func (p *DescribeDiskAutoScaleEventsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDiskAutoScaleEventsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDiskAutoScaleEventsReq_PageSize_DEFAULT int32

func (p *DescribeDiskAutoScaleEventsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDiskAutoScaleEventsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDiskAutoScaleEventsReq_SortBy_DEFAULT SortBy

func (p *DescribeDiskAutoScaleEventsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeDiskAutoScaleEventsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeDiskAutoScaleEventsReq_CreationStartTime_DEFAULT string

func (p *DescribeDiskAutoScaleEventsReq) GetCreationStartTime() (v string) {
	if !p.IsSetCreationStartTime() {
		return DescribeDiskAutoScaleEventsReq_CreationStartTime_DEFAULT
	}
	return *p.CreationStartTime
}

var DescribeDiskAutoScaleEventsReq_CreationEndTime_DEFAULT string

func (p *DescribeDiskAutoScaleEventsReq) GetCreationEndTime() (v string) {
	if !p.IsSetCreationEndTime() {
		return DescribeDiskAutoScaleEventsReq_CreationEndTime_DEFAULT
	}
	return *p.CreationEndTime
}
func (p *DescribeDiskAutoScaleEventsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetAutoScaleSearchParam(val *AutoScaleSearchParam) {
	p.AutoScaleSearchParam = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetCreationStartTime(val *string) {
	p.CreationStartTime = val
}
func (p *DescribeDiskAutoScaleEventsReq) SetCreationEndTime(val *string) {
	p.CreationEndTime = val
}

var fieldIDToName_DescribeDiskAutoScaleEventsReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "RegionId",
	4:  "Metric",
	5:  "autoScaleSearchParam",
	6:  "PageNumber",
	7:  "PageSize",
	8:  "SortBy",
	9:  "CreationStartTime",
	10: "CreationEndTime",
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetAutoScaleSearchParam() bool {
	return p.AutoScaleSearchParam != nil
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetCreationStartTime() bool {
	return p.CreationStartTime != nil
}

func (p *DescribeDiskAutoScaleEventsReq) IsSetCreationEndTime() bool {
	return p.CreationEndTime != nil
}

func (p *DescribeDiskAutoScaleEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskAutoScaleEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetRegionId bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDiskAutoScaleEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDiskAutoScaleEventsReq[fieldId]))
}

func (p *DescribeDiskAutoScaleEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField5(iprot thrift.TProtocol) error {
	_field := NewAutoScaleSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AutoScaleSearchParam = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreationStartTime = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreationEndTime = _field
	return nil
}

func (p *DescribeDiskAutoScaleEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskAutoScaleEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDiskAutoScaleEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleSearchParam() {
		if err = oprot.WriteFieldBegin("autoScaleSearchParam", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AutoScaleSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreationStartTime() {
		if err = oprot.WriteFieldBegin("CreationStartTime", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreationStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreationEndTime() {
		if err = oprot.WriteFieldBegin("CreationEndTime", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreationEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDiskAutoScaleEventsReq(%+v)", *p)

}

func (p *DescribeDiskAutoScaleEventsReq) DeepEqual(ano *DescribeDiskAutoScaleEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Metric) {
		return false
	}
	if !p.Field5DeepEqual(ano.AutoScaleSearchParam) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field8DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field9DeepEqual(ano.CreationStartTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreationEndTime) {
		return false
	}
	return true
}

func (p *DescribeDiskAutoScaleEventsReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field4DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field5DeepEqual(src *AutoScaleSearchParam) bool {

	if !p.AutoScaleSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field7DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field8DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field9DeepEqual(src *string) bool {

	if p.CreationStartTime == src {
		return true
	} else if p.CreationStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreationStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsReq) Field10DeepEqual(src *string) bool {

	if p.CreationEndTime == src {
		return true
	} else if p.CreationEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreationEndTime, *src) != 0 {
		return false
	}
	return true
}

type DescribeDiskAutoScaleEventsResp struct {
	DiskAutoScaleEvents []*DiskAutoScaleEventItem `thrift:"DiskAutoScaleEvents,1,required" frugal:"1,required,list<DiskAutoScaleEventItem>" json:"DiskAutoScaleEvents"`
	Total               int32                     `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeDiskAutoScaleEventsResp() *DescribeDiskAutoScaleEventsResp {
	return &DescribeDiskAutoScaleEventsResp{}
}

func (p *DescribeDiskAutoScaleEventsResp) InitDefault() {
}

func (p *DescribeDiskAutoScaleEventsResp) GetDiskAutoScaleEvents() (v []*DiskAutoScaleEventItem) {
	return p.DiskAutoScaleEvents
}

func (p *DescribeDiskAutoScaleEventsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeDiskAutoScaleEventsResp) SetDiskAutoScaleEvents(val []*DiskAutoScaleEventItem) {
	p.DiskAutoScaleEvents = val
}
func (p *DescribeDiskAutoScaleEventsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeDiskAutoScaleEventsResp = map[int16]string{
	1: "DiskAutoScaleEvents",
	2: "Total",
}

func (p *DescribeDiskAutoScaleEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskAutoScaleEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDiskAutoScaleEvents bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiskAutoScaleEvents = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDiskAutoScaleEvents {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDiskAutoScaleEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDiskAutoScaleEventsResp[fieldId]))
}

func (p *DescribeDiskAutoScaleEventsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DiskAutoScaleEventItem, 0, size)
	values := make([]DiskAutoScaleEventItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DiskAutoScaleEvents = _field
	return nil
}
func (p *DescribeDiskAutoScaleEventsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeDiskAutoScaleEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDiskAutoScaleEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDiskAutoScaleEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiskAutoScaleEvents", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DiskAutoScaleEvents)); err != nil {
		return err
	}
	for _, v := range p.DiskAutoScaleEvents {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDiskAutoScaleEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDiskAutoScaleEventsResp(%+v)", *p)

}

func (p *DescribeDiskAutoScaleEventsResp) DeepEqual(ano *DescribeDiskAutoScaleEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DiskAutoScaleEvents) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeDiskAutoScaleEventsResp) Field1DeepEqual(src []*DiskAutoScaleEventItem) bool {

	if len(p.DiskAutoScaleEvents) != len(src) {
		return false
	}
	for i, v := range p.DiskAutoScaleEvents {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDiskAutoScaleEventsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DiskAutoScaleEventItem struct {
	EventId                    string                     `thrift:"EventId,1,required" frugal:"1,required,string" json:"EventId"`
	BeforeAutoScale            string                     `thrift:"BeforeAutoScale,2,required" frugal:"2,required,string" json:"BeforeAutoScale"`
	AfterAutoScale             string                     `thrift:"AfterAutoScale,3,required" frugal:"3,required,string" json:"AfterAutoScale"`
	Action                     string                     `thrift:"Action,4,required" frugal:"4,required,string" json:"Action"`
	AutoScaleType              *string                    `thrift:"AutoScaleType,5,optional" frugal:"5,optional,string" json:"AutoScaleType,omitempty"`
	MetricTriggerValue         *string                    `thrift:"MetricTriggerValue,6,optional" frugal:"6,optional,string" json:"MetricTriggerValue,omitempty"`
	AutoScaleTriggerMetricUnit *string                    `thrift:"AutoScaleTriggerMetricUnit,7,optional" frugal:"7,optional,string" json:"AutoScaleTriggerMetricUnit,omitempty"`
	AutoScaleMetric            *string                    `thrift:"AutoScaleMetric,8,optional" frugal:"8,optional,string" json:"AutoScaleMetric,omitempty"`
	TriggerConditionThreshold  *string                    `thrift:"TriggerConditionThreshold,9,optional" frugal:"9,optional,string" json:"TriggerConditionThreshold,omitempty"`
	TriggerConditionComparison *string                    `thrift:"TriggerConditionComparison,10,optional" frugal:"10,optional,string" json:"TriggerConditionComparison,omitempty"`
	MetricValue                *float64                   `thrift:"MetricValue,11,optional" frugal:"11,optional,double" json:"MetricValue,omitempty"`
	TriggerDateTime            *string                    `thrift:"TriggerDateTime,12,optional" frugal:"12,optional,string" json:"TriggerDateTime,omitempty"`
	TriggerState               *TaskStatus                `thrift:"TriggerState,13,optional" frugal:"13,optional,TaskStatus" json:"TriggerState,omitempty"`
	NodeID                     *string                    `thrift:"NodeID,14,optional" frugal:"14,optional,string" json:"NodeID,omitempty"`
	EventReason                *string                    `thrift:"EventReason,15,optional" frugal:"15,optional,string" json:"EventReason,omitempty"`
	IsPreCheck                 *bool                      `thrift:"IsPreCheck,16,optional" frugal:"16,optional,bool" json:"IsPreCheck,omitempty"`
	AutoScaleAction            *AutoScaleAction           `thrift:"AutoScaleAction,17,optional" frugal:"17,optional,AutoScaleAction" json:"AutoScaleAction,omitempty"`
	AutoScaleTriggerEventType  *AutoScaleTriggerEventType `thrift:"AutoScaleTriggerEventType,18,optional" frugal:"18,optional,AutoScaleTriggerEventType" json:"AutoScaleTriggerEventType,omitempty"`
}

func NewDiskAutoScaleEventItem() *DiskAutoScaleEventItem {
	return &DiskAutoScaleEventItem{}
}

func (p *DiskAutoScaleEventItem) InitDefault() {
}

func (p *DiskAutoScaleEventItem) GetEventId() (v string) {
	return p.EventId
}

func (p *DiskAutoScaleEventItem) GetBeforeAutoScale() (v string) {
	return p.BeforeAutoScale
}

func (p *DiskAutoScaleEventItem) GetAfterAutoScale() (v string) {
	return p.AfterAutoScale
}

func (p *DiskAutoScaleEventItem) GetAction() (v string) {
	return p.Action
}

var DiskAutoScaleEventItem_AutoScaleType_DEFAULT string

func (p *DiskAutoScaleEventItem) GetAutoScaleType() (v string) {
	if !p.IsSetAutoScaleType() {
		return DiskAutoScaleEventItem_AutoScaleType_DEFAULT
	}
	return *p.AutoScaleType
}

var DiskAutoScaleEventItem_MetricTriggerValue_DEFAULT string

func (p *DiskAutoScaleEventItem) GetMetricTriggerValue() (v string) {
	if !p.IsSetMetricTriggerValue() {
		return DiskAutoScaleEventItem_MetricTriggerValue_DEFAULT
	}
	return *p.MetricTriggerValue
}

var DiskAutoScaleEventItem_AutoScaleTriggerMetricUnit_DEFAULT string

func (p *DiskAutoScaleEventItem) GetAutoScaleTriggerMetricUnit() (v string) {
	if !p.IsSetAutoScaleTriggerMetricUnit() {
		return DiskAutoScaleEventItem_AutoScaleTriggerMetricUnit_DEFAULT
	}
	return *p.AutoScaleTriggerMetricUnit
}

var DiskAutoScaleEventItem_AutoScaleMetric_DEFAULT string

func (p *DiskAutoScaleEventItem) GetAutoScaleMetric() (v string) {
	if !p.IsSetAutoScaleMetric() {
		return DiskAutoScaleEventItem_AutoScaleMetric_DEFAULT
	}
	return *p.AutoScaleMetric
}

var DiskAutoScaleEventItem_TriggerConditionThreshold_DEFAULT string

func (p *DiskAutoScaleEventItem) GetTriggerConditionThreshold() (v string) {
	if !p.IsSetTriggerConditionThreshold() {
		return DiskAutoScaleEventItem_TriggerConditionThreshold_DEFAULT
	}
	return *p.TriggerConditionThreshold
}

var DiskAutoScaleEventItem_TriggerConditionComparison_DEFAULT string

func (p *DiskAutoScaleEventItem) GetTriggerConditionComparison() (v string) {
	if !p.IsSetTriggerConditionComparison() {
		return DiskAutoScaleEventItem_TriggerConditionComparison_DEFAULT
	}
	return *p.TriggerConditionComparison
}

var DiskAutoScaleEventItem_MetricValue_DEFAULT float64

func (p *DiskAutoScaleEventItem) GetMetricValue() (v float64) {
	if !p.IsSetMetricValue() {
		return DiskAutoScaleEventItem_MetricValue_DEFAULT
	}
	return *p.MetricValue
}

var DiskAutoScaleEventItem_TriggerDateTime_DEFAULT string

func (p *DiskAutoScaleEventItem) GetTriggerDateTime() (v string) {
	if !p.IsSetTriggerDateTime() {
		return DiskAutoScaleEventItem_TriggerDateTime_DEFAULT
	}
	return *p.TriggerDateTime
}

var DiskAutoScaleEventItem_TriggerState_DEFAULT TaskStatus

func (p *DiskAutoScaleEventItem) GetTriggerState() (v TaskStatus) {
	if !p.IsSetTriggerState() {
		return DiskAutoScaleEventItem_TriggerState_DEFAULT
	}
	return *p.TriggerState
}

var DiskAutoScaleEventItem_NodeID_DEFAULT string

func (p *DiskAutoScaleEventItem) GetNodeID() (v string) {
	if !p.IsSetNodeID() {
		return DiskAutoScaleEventItem_NodeID_DEFAULT
	}
	return *p.NodeID
}

var DiskAutoScaleEventItem_EventReason_DEFAULT string

func (p *DiskAutoScaleEventItem) GetEventReason() (v string) {
	if !p.IsSetEventReason() {
		return DiskAutoScaleEventItem_EventReason_DEFAULT
	}
	return *p.EventReason
}

var DiskAutoScaleEventItem_IsPreCheck_DEFAULT bool

func (p *DiskAutoScaleEventItem) GetIsPreCheck() (v bool) {
	if !p.IsSetIsPreCheck() {
		return DiskAutoScaleEventItem_IsPreCheck_DEFAULT
	}
	return *p.IsPreCheck
}

var DiskAutoScaleEventItem_AutoScaleAction_DEFAULT AutoScaleAction

func (p *DiskAutoScaleEventItem) GetAutoScaleAction() (v AutoScaleAction) {
	if !p.IsSetAutoScaleAction() {
		return DiskAutoScaleEventItem_AutoScaleAction_DEFAULT
	}
	return *p.AutoScaleAction
}

var DiskAutoScaleEventItem_AutoScaleTriggerEventType_DEFAULT AutoScaleTriggerEventType

func (p *DiskAutoScaleEventItem) GetAutoScaleTriggerEventType() (v AutoScaleTriggerEventType) {
	if !p.IsSetAutoScaleTriggerEventType() {
		return DiskAutoScaleEventItem_AutoScaleTriggerEventType_DEFAULT
	}
	return *p.AutoScaleTriggerEventType
}
func (p *DiskAutoScaleEventItem) SetEventId(val string) {
	p.EventId = val
}
func (p *DiskAutoScaleEventItem) SetBeforeAutoScale(val string) {
	p.BeforeAutoScale = val
}
func (p *DiskAutoScaleEventItem) SetAfterAutoScale(val string) {
	p.AfterAutoScale = val
}
func (p *DiskAutoScaleEventItem) SetAction(val string) {
	p.Action = val
}
func (p *DiskAutoScaleEventItem) SetAutoScaleType(val *string) {
	p.AutoScaleType = val
}
func (p *DiskAutoScaleEventItem) SetMetricTriggerValue(val *string) {
	p.MetricTriggerValue = val
}
func (p *DiskAutoScaleEventItem) SetAutoScaleTriggerMetricUnit(val *string) {
	p.AutoScaleTriggerMetricUnit = val
}
func (p *DiskAutoScaleEventItem) SetAutoScaleMetric(val *string) {
	p.AutoScaleMetric = val
}
func (p *DiskAutoScaleEventItem) SetTriggerConditionThreshold(val *string) {
	p.TriggerConditionThreshold = val
}
func (p *DiskAutoScaleEventItem) SetTriggerConditionComparison(val *string) {
	p.TriggerConditionComparison = val
}
func (p *DiskAutoScaleEventItem) SetMetricValue(val *float64) {
	p.MetricValue = val
}
func (p *DiskAutoScaleEventItem) SetTriggerDateTime(val *string) {
	p.TriggerDateTime = val
}
func (p *DiskAutoScaleEventItem) SetTriggerState(val *TaskStatus) {
	p.TriggerState = val
}
func (p *DiskAutoScaleEventItem) SetNodeID(val *string) {
	p.NodeID = val
}
func (p *DiskAutoScaleEventItem) SetEventReason(val *string) {
	p.EventReason = val
}
func (p *DiskAutoScaleEventItem) SetIsPreCheck(val *bool) {
	p.IsPreCheck = val
}
func (p *DiskAutoScaleEventItem) SetAutoScaleAction(val *AutoScaleAction) {
	p.AutoScaleAction = val
}
func (p *DiskAutoScaleEventItem) SetAutoScaleTriggerEventType(val *AutoScaleTriggerEventType) {
	p.AutoScaleTriggerEventType = val
}

var fieldIDToName_DiskAutoScaleEventItem = map[int16]string{
	1:  "EventId",
	2:  "BeforeAutoScale",
	3:  "AfterAutoScale",
	4:  "Action",
	5:  "AutoScaleType",
	6:  "MetricTriggerValue",
	7:  "AutoScaleTriggerMetricUnit",
	8:  "AutoScaleMetric",
	9:  "TriggerConditionThreshold",
	10: "TriggerConditionComparison",
	11: "MetricValue",
	12: "TriggerDateTime",
	13: "TriggerState",
	14: "NodeID",
	15: "EventReason",
	16: "IsPreCheck",
	17: "AutoScaleAction",
	18: "AutoScaleTriggerEventType",
}

func (p *DiskAutoScaleEventItem) IsSetAutoScaleType() bool {
	return p.AutoScaleType != nil
}

func (p *DiskAutoScaleEventItem) IsSetMetricTriggerValue() bool {
	return p.MetricTriggerValue != nil
}

func (p *DiskAutoScaleEventItem) IsSetAutoScaleTriggerMetricUnit() bool {
	return p.AutoScaleTriggerMetricUnit != nil
}

func (p *DiskAutoScaleEventItem) IsSetAutoScaleMetric() bool {
	return p.AutoScaleMetric != nil
}

func (p *DiskAutoScaleEventItem) IsSetTriggerConditionThreshold() bool {
	return p.TriggerConditionThreshold != nil
}

func (p *DiskAutoScaleEventItem) IsSetTriggerConditionComparison() bool {
	return p.TriggerConditionComparison != nil
}

func (p *DiskAutoScaleEventItem) IsSetMetricValue() bool {
	return p.MetricValue != nil
}

func (p *DiskAutoScaleEventItem) IsSetTriggerDateTime() bool {
	return p.TriggerDateTime != nil
}

func (p *DiskAutoScaleEventItem) IsSetTriggerState() bool {
	return p.TriggerState != nil
}

func (p *DiskAutoScaleEventItem) IsSetNodeID() bool {
	return p.NodeID != nil
}

func (p *DiskAutoScaleEventItem) IsSetEventReason() bool {
	return p.EventReason != nil
}

func (p *DiskAutoScaleEventItem) IsSetIsPreCheck() bool {
	return p.IsPreCheck != nil
}

func (p *DiskAutoScaleEventItem) IsSetAutoScaleAction() bool {
	return p.AutoScaleAction != nil
}

func (p *DiskAutoScaleEventItem) IsSetAutoScaleTriggerEventType() bool {
	return p.AutoScaleTriggerEventType != nil
}

func (p *DiskAutoScaleEventItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DiskAutoScaleEventItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEventId bool = false
	var issetBeforeAutoScale bool = false
	var issetAfterAutoScale bool = false
	var issetAction bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBeforeAutoScale = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAfterAutoScale = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEventId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBeforeAutoScale {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAfterAutoScale {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DiskAutoScaleEventItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DiskAutoScaleEventItem[fieldId]))
}

func (p *DiskAutoScaleEventItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventId = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BeforeAutoScale = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AfterAutoScale = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Action = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScaleType = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MetricTriggerValue = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScaleTriggerMetricUnit = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScaleMetric = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TriggerConditionThreshold = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TriggerConditionComparison = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField11(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MetricValue = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TriggerDateTime = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField13(iprot thrift.TProtocol) error {

	var _field *TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskStatus(v)
		_field = &tmp
	}
	p.TriggerState = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeID = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EventReason = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField16(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsPreCheck = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField17(iprot thrift.TProtocol) error {

	var _field *AutoScaleAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AutoScaleAction(v)
		_field = &tmp
	}
	p.AutoScaleAction = _field
	return nil
}
func (p *DiskAutoScaleEventItem) ReadField18(iprot thrift.TProtocol) error {

	var _field *AutoScaleTriggerEventType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AutoScaleTriggerEventType(v)
		_field = &tmp
	}
	p.AutoScaleTriggerEventType = _field
	return nil
}

func (p *DiskAutoScaleEventItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DiskAutoScaleEventItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("DiskAutoScaleEventItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BeforeAutoScale", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BeforeAutoScale); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AfterAutoScale", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AfterAutoScale); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Action", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Action); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleType() {
		if err = oprot.WriteFieldBegin("AutoScaleType", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AutoScaleType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetMetricTriggerValue() {
		if err = oprot.WriteFieldBegin("MetricTriggerValue", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MetricTriggerValue); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleTriggerMetricUnit() {
		if err = oprot.WriteFieldBegin("AutoScaleTriggerMetricUnit", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AutoScaleTriggerMetricUnit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleMetric() {
		if err = oprot.WriteFieldBegin("AutoScaleMetric", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AutoScaleMetric); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerConditionThreshold() {
		if err = oprot.WriteFieldBegin("TriggerConditionThreshold", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TriggerConditionThreshold); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerConditionComparison() {
		if err = oprot.WriteFieldBegin("TriggerConditionComparison", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TriggerConditionComparison); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetMetricValue() {
		if err = oprot.WriteFieldBegin("MetricValue", thrift.DOUBLE, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.MetricValue); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerDateTime() {
		if err = oprot.WriteFieldBegin("TriggerDateTime", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TriggerDateTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerState() {
		if err = oprot.WriteFieldBegin("TriggerState", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TriggerState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeID() {
		if err = oprot.WriteFieldBegin("NodeID", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventReason() {
		if err = oprot.WriteFieldBegin("EventReason", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EventReason); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsPreCheck() {
		if err = oprot.WriteFieldBegin("IsPreCheck", thrift.BOOL, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsPreCheck); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleAction() {
		if err = oprot.WriteFieldBegin("AutoScaleAction", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AutoScaleAction)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScaleTriggerEventType() {
		if err = oprot.WriteFieldBegin("AutoScaleTriggerEventType", thrift.I32, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AutoScaleTriggerEventType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *DiskAutoScaleEventItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DiskAutoScaleEventItem(%+v)", *p)

}

func (p *DiskAutoScaleEventItem) DeepEqual(ano *DiskAutoScaleEventItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BeforeAutoScale) {
		return false
	}
	if !p.Field3DeepEqual(ano.AfterAutoScale) {
		return false
	}
	if !p.Field4DeepEqual(ano.Action) {
		return false
	}
	if !p.Field5DeepEqual(ano.AutoScaleType) {
		return false
	}
	if !p.Field6DeepEqual(ano.MetricTriggerValue) {
		return false
	}
	if !p.Field7DeepEqual(ano.AutoScaleTriggerMetricUnit) {
		return false
	}
	if !p.Field8DeepEqual(ano.AutoScaleMetric) {
		return false
	}
	if !p.Field9DeepEqual(ano.TriggerConditionThreshold) {
		return false
	}
	if !p.Field10DeepEqual(ano.TriggerConditionComparison) {
		return false
	}
	if !p.Field11DeepEqual(ano.MetricValue) {
		return false
	}
	if !p.Field12DeepEqual(ano.TriggerDateTime) {
		return false
	}
	if !p.Field13DeepEqual(ano.TriggerState) {
		return false
	}
	if !p.Field14DeepEqual(ano.NodeID) {
		return false
	}
	if !p.Field15DeepEqual(ano.EventReason) {
		return false
	}
	if !p.Field16DeepEqual(ano.IsPreCheck) {
		return false
	}
	if !p.Field17DeepEqual(ano.AutoScaleAction) {
		return false
	}
	if !p.Field18DeepEqual(ano.AutoScaleTriggerEventType) {
		return false
	}
	return true
}

func (p *DiskAutoScaleEventItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EventId, src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BeforeAutoScale, src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AfterAutoScale, src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Action, src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field5DeepEqual(src *string) bool {

	if p.AutoScaleType == src {
		return true
	} else if p.AutoScaleType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AutoScaleType, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field6DeepEqual(src *string) bool {

	if p.MetricTriggerValue == src {
		return true
	} else if p.MetricTriggerValue == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MetricTriggerValue, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field7DeepEqual(src *string) bool {

	if p.AutoScaleTriggerMetricUnit == src {
		return true
	} else if p.AutoScaleTriggerMetricUnit == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AutoScaleTriggerMetricUnit, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field8DeepEqual(src *string) bool {

	if p.AutoScaleMetric == src {
		return true
	} else if p.AutoScaleMetric == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AutoScaleMetric, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field9DeepEqual(src *string) bool {

	if p.TriggerConditionThreshold == src {
		return true
	} else if p.TriggerConditionThreshold == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TriggerConditionThreshold, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field10DeepEqual(src *string) bool {

	if p.TriggerConditionComparison == src {
		return true
	} else if p.TriggerConditionComparison == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TriggerConditionComparison, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field11DeepEqual(src *float64) bool {

	if p.MetricValue == src {
		return true
	} else if p.MetricValue == nil || src == nil {
		return false
	}
	if *p.MetricValue != *src {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field12DeepEqual(src *string) bool {

	if p.TriggerDateTime == src {
		return true
	} else if p.TriggerDateTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TriggerDateTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field13DeepEqual(src *TaskStatus) bool {

	if p.TriggerState == src {
		return true
	} else if p.TriggerState == nil || src == nil {
		return false
	}
	if *p.TriggerState != *src {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field14DeepEqual(src *string) bool {

	if p.NodeID == src {
		return true
	} else if p.NodeID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeID, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field15DeepEqual(src *string) bool {

	if p.EventReason == src {
		return true
	} else if p.EventReason == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EventReason, *src) != 0 {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field16DeepEqual(src *bool) bool {

	if p.IsPreCheck == src {
		return true
	} else if p.IsPreCheck == nil || src == nil {
		return false
	}
	if *p.IsPreCheck != *src {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field17DeepEqual(src *AutoScaleAction) bool {

	if p.AutoScaleAction == src {
		return true
	} else if p.AutoScaleAction == nil || src == nil {
		return false
	}
	if *p.AutoScaleAction != *src {
		return false
	}
	return true
}
func (p *DiskAutoScaleEventItem) Field18DeepEqual(src *AutoScaleTriggerEventType) bool {

	if p.AutoScaleTriggerEventType == src {
		return true
	} else if p.AutoScaleTriggerEventType == nil || src == nil {
		return false
	}
	if *p.AutoScaleTriggerEventType != *src {
		return false
	}
	return true
}

type DescribeAutoScaleInstanceSpecReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
}

func NewDescribeAutoScaleInstanceSpecReq() *DescribeAutoScaleInstanceSpecReq {
	return &DescribeAutoScaleInstanceSpecReq{}
}

func (p *DescribeAutoScaleInstanceSpecReq) InitDefault() {
}

func (p *DescribeAutoScaleInstanceSpecReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeAutoScaleInstanceSpecReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DescribeAutoScaleInstanceSpecReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeAutoScaleInstanceSpecReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeAutoScaleInstanceSpecReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
}

func (p *DescribeAutoScaleInstanceSpecReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleInstanceSpecReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleInstanceSpecReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleInstanceSpecReq[fieldId]))
}

func (p *DescribeAutoScaleInstanceSpecReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeAutoScaleInstanceSpecReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeAutoScaleInstanceSpecReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleInstanceSpecReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleInstanceSpecReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleInstanceSpecReq(%+v)", *p)

}

func (p *DescribeAutoScaleInstanceSpecReq) DeepEqual(ano *DescribeAutoScaleInstanceSpecReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleInstanceSpecReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleInstanceSpecReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type DescribeAutoScaleInstanceSpecResp struct {
	Total           int32             `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	NodeObjectSpecs []*NodeObjectSpec `thrift:"NodeObjectSpecs,2,required" frugal:"2,required,list<NodeObjectSpec>" json:"NodeObjectSpecs"`
}

func NewDescribeAutoScaleInstanceSpecResp() *DescribeAutoScaleInstanceSpecResp {
	return &DescribeAutoScaleInstanceSpecResp{}
}

func (p *DescribeAutoScaleInstanceSpecResp) InitDefault() {
}

func (p *DescribeAutoScaleInstanceSpecResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeAutoScaleInstanceSpecResp) GetNodeObjectSpecs() (v []*NodeObjectSpec) {
	return p.NodeObjectSpecs
}
func (p *DescribeAutoScaleInstanceSpecResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeAutoScaleInstanceSpecResp) SetNodeObjectSpecs(val []*NodeObjectSpec) {
	p.NodeObjectSpecs = val
}

var fieldIDToName_DescribeAutoScaleInstanceSpecResp = map[int16]string{
	1: "Total",
	2: "NodeObjectSpecs",
}

func (p *DescribeAutoScaleInstanceSpecResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleInstanceSpecResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetNodeObjectSpecs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeObjectSpecs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeObjectSpecs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoScaleInstanceSpecResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoScaleInstanceSpecResp[fieldId]))
}

func (p *DescribeAutoScaleInstanceSpecResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeAutoScaleInstanceSpecResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*NodeObjectSpec, 0, size)
	values := make([]NodeObjectSpec, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeObjectSpecs = _field
	return nil
}

func (p *DescribeAutoScaleInstanceSpecResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoScaleInstanceSpecResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoScaleInstanceSpecResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeObjectSpecs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeObjectSpecs)); err != nil {
		return err
	}
	for _, v := range p.NodeObjectSpecs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoScaleInstanceSpecResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoScaleInstanceSpecResp(%+v)", *p)

}

func (p *DescribeAutoScaleInstanceSpecResp) DeepEqual(ano *DescribeAutoScaleInstanceSpecResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeObjectSpecs) {
		return false
	}
	return true
}

func (p *DescribeAutoScaleInstanceSpecResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeAutoScaleInstanceSpecResp) Field2DeepEqual(src []*NodeObjectSpec) bool {

	if len(p.NodeObjectSpecs) != len(src) {
		return false
	}
	for i, v := range p.NodeObjectSpecs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type NodeObjectSpec struct {
	NodeSpec          string `thrift:"NodeSpec,1,required" frugal:"1,required,string" json:"NodeSpec"`
	VCPU              int32  `thrift:"VCPU,2,required" frugal:"2,required,i32" json:"VCPU"`
	Memory            int32  `thrift:"Memory,3,required" frugal:"3,required,i32" json:"Memory"`
	Connection        int32  `thrift:"Connection,4,required" frugal:"4,required,i32" json:"Connection"`
	SpecFamily        string `thrift:"SpecFamily,5,required" frugal:"5,required,string" json:"SpecFamily"`
	PrePaidMinStorage int32  `thrift:"PrePaidMinStorage,6,required" frugal:"6,required,i32" json:"PrePaidMinStorage"`
	PrePaidMaxStorage int32  `thrift:"PrePaidMaxStorage,7,required" frugal:"7,required,i32" json:"PrePaidMaxStorage"`
	MaxIops           int32  `thrift:"MaxIops,8,required" frugal:"8,required,i32" json:"MaxIops"`
}

func NewNodeObjectSpec() *NodeObjectSpec {
	return &NodeObjectSpec{}
}

func (p *NodeObjectSpec) InitDefault() {
}

func (p *NodeObjectSpec) GetNodeSpec() (v string) {
	return p.NodeSpec
}

func (p *NodeObjectSpec) GetVCPU() (v int32) {
	return p.VCPU
}

func (p *NodeObjectSpec) GetMemory() (v int32) {
	return p.Memory
}

func (p *NodeObjectSpec) GetConnection() (v int32) {
	return p.Connection
}

func (p *NodeObjectSpec) GetSpecFamily() (v string) {
	return p.SpecFamily
}

func (p *NodeObjectSpec) GetPrePaidMinStorage() (v int32) {
	return p.PrePaidMinStorage
}

func (p *NodeObjectSpec) GetPrePaidMaxStorage() (v int32) {
	return p.PrePaidMaxStorage
}

func (p *NodeObjectSpec) GetMaxIops() (v int32) {
	return p.MaxIops
}
func (p *NodeObjectSpec) SetNodeSpec(val string) {
	p.NodeSpec = val
}
func (p *NodeObjectSpec) SetVCPU(val int32) {
	p.VCPU = val
}
func (p *NodeObjectSpec) SetMemory(val int32) {
	p.Memory = val
}
func (p *NodeObjectSpec) SetConnection(val int32) {
	p.Connection = val
}
func (p *NodeObjectSpec) SetSpecFamily(val string) {
	p.SpecFamily = val
}
func (p *NodeObjectSpec) SetPrePaidMinStorage(val int32) {
	p.PrePaidMinStorage = val
}
func (p *NodeObjectSpec) SetPrePaidMaxStorage(val int32) {
	p.PrePaidMaxStorage = val
}
func (p *NodeObjectSpec) SetMaxIops(val int32) {
	p.MaxIops = val
}

var fieldIDToName_NodeObjectSpec = map[int16]string{
	1: "NodeSpec",
	2: "VCPU",
	3: "Memory",
	4: "Connection",
	5: "SpecFamily",
	6: "PrePaidMinStorage",
	7: "PrePaidMaxStorage",
	8: "MaxIops",
}

func (p *NodeObjectSpec) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("NodeObjectSpec")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeSpec bool = false
	var issetVCPU bool = false
	var issetMemory bool = false
	var issetConnection bool = false
	var issetSpecFamily bool = false
	var issetPrePaidMinStorage bool = false
	var issetPrePaidMaxStorage bool = false
	var issetMaxIops bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeSpec = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVCPU = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemory = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnection = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSpecFamily = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrePaidMinStorage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrePaidMaxStorage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxIops = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeSpec {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVCPU {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMemory {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetConnection {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSpecFamily {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetPrePaidMinStorage {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetPrePaidMaxStorage {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetMaxIops {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_NodeObjectSpec[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_NodeObjectSpec[fieldId]))
}

func (p *NodeObjectSpec) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeSpec = _field
	return nil
}
func (p *NodeObjectSpec) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VCPU = _field
	return nil
}
func (p *NodeObjectSpec) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Memory = _field
	return nil
}
func (p *NodeObjectSpec) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Connection = _field
	return nil
}
func (p *NodeObjectSpec) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpecFamily = _field
	return nil
}
func (p *NodeObjectSpec) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PrePaidMinStorage = _field
	return nil
}
func (p *NodeObjectSpec) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PrePaidMaxStorage = _field
	return nil
}
func (p *NodeObjectSpec) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxIops = _field
	return nil
}

func (p *NodeObjectSpec) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("NodeObjectSpec")

	var fieldId int16
	if err = oprot.WriteStructBegin("NodeObjectSpec"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *NodeObjectSpec) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeSpec", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeSpec); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VCPU", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.VCPU); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Memory", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Memory); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Connection", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Connection); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SpecFamily", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SpecFamily); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrePaidMinStorage", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PrePaidMinStorage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrePaidMaxStorage", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PrePaidMaxStorage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *NodeObjectSpec) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxIops", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.MaxIops); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *NodeObjectSpec) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeObjectSpec(%+v)", *p)

}

func (p *NodeObjectSpec) DeepEqual(ano *NodeObjectSpec) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeSpec) {
		return false
	}
	if !p.Field2DeepEqual(ano.VCPU) {
		return false
	}
	if !p.Field3DeepEqual(ano.Memory) {
		return false
	}
	if !p.Field4DeepEqual(ano.Connection) {
		return false
	}
	if !p.Field5DeepEqual(ano.SpecFamily) {
		return false
	}
	if !p.Field6DeepEqual(ano.PrePaidMinStorage) {
		return false
	}
	if !p.Field7DeepEqual(ano.PrePaidMaxStorage) {
		return false
	}
	if !p.Field8DeepEqual(ano.MaxIops) {
		return false
	}
	return true
}

func (p *NodeObjectSpec) Field1DeepEqual(src string) bool {

	if strings.Compare(p.NodeSpec, src) != 0 {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field2DeepEqual(src int32) bool {

	if p.VCPU != src {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field3DeepEqual(src int32) bool {

	if p.Memory != src {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field4DeepEqual(src int32) bool {

	if p.Connection != src {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SpecFamily, src) != 0 {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field6DeepEqual(src int32) bool {

	if p.PrePaidMinStorage != src {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field7DeepEqual(src int32) bool {

	if p.PrePaidMaxStorage != src {
		return false
	}
	return true
}
func (p *NodeObjectSpec) Field8DeepEqual(src int32) bool {

	if p.MaxIops != src {
		return false
	}
	return true
}

type DescribeDBAutoScalingConfigReq struct {
	InstanceType InstanceType        `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	Metric       AutoScaleMetricName `thrift:"Metric,3,required" frugal:"3,required,AutoScaleMetricName" json:"Metric"`
}

func NewDescribeDBAutoScalingConfigReq() *DescribeDBAutoScalingConfigReq {
	return &DescribeDBAutoScalingConfigReq{}
}

func (p *DescribeDBAutoScalingConfigReq) InitDefault() {
}

func (p *DescribeDBAutoScalingConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDBAutoScalingConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBAutoScalingConfigReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}
func (p *DescribeDBAutoScalingConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDBAutoScalingConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBAutoScalingConfigReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}

var fieldIDToName_DescribeDBAutoScalingConfigReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Metric",
}

func (p *DescribeDBAutoScalingConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScalingConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAutoScalingConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAutoScalingConfigReq[fieldId]))
}

func (p *DescribeDBAutoScalingConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDBAutoScalingConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBAutoScalingConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}

func (p *DescribeDBAutoScalingConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScalingConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAutoScalingConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAutoScalingConfigReq(%+v)", *p)

}

func (p *DescribeDBAutoScalingConfigReq) DeepEqual(ano *DescribeDBAutoScalingConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Metric) {
		return false
	}
	return true
}

func (p *DescribeDBAutoScalingConfigReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScalingConfigReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAutoScalingConfigReq) Field3DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}

type DescribeDBAutoScalingConfigResp struct {
	AutoLocalSpecConfig   *AutoLocalSpecScalingConfig   `thrift:"AutoLocalSpecConfig,1,optional" frugal:"1,optional,AutoLocalSpecScalingConfig" json:"AutoLocalSpecConfig,omitempty"`
	ManualLocalSpecConfig *ManualLocalSpecScalingConfig `thrift:"ManualLocalSpecConfig,2,optional" frugal:"2,optional,ManualLocalSpecScalingConfig" json:"ManualLocalSpecConfig,omitempty"`
	LocalSpecLimit        *LocalSpecScalingLimit        `thrift:"LocalSpecLimit,3,required" frugal:"3,required,LocalSpecScalingLimit" json:"LocalSpecLimit"`
}

func NewDescribeDBAutoScalingConfigResp() *DescribeDBAutoScalingConfigResp {
	return &DescribeDBAutoScalingConfigResp{}
}

func (p *DescribeDBAutoScalingConfigResp) InitDefault() {
}

var DescribeDBAutoScalingConfigResp_AutoLocalSpecConfig_DEFAULT *AutoLocalSpecScalingConfig

func (p *DescribeDBAutoScalingConfigResp) GetAutoLocalSpecConfig() (v *AutoLocalSpecScalingConfig) {
	if !p.IsSetAutoLocalSpecConfig() {
		return DescribeDBAutoScalingConfigResp_AutoLocalSpecConfig_DEFAULT
	}
	return p.AutoLocalSpecConfig
}

var DescribeDBAutoScalingConfigResp_ManualLocalSpecConfig_DEFAULT *ManualLocalSpecScalingConfig

func (p *DescribeDBAutoScalingConfigResp) GetManualLocalSpecConfig() (v *ManualLocalSpecScalingConfig) {
	if !p.IsSetManualLocalSpecConfig() {
		return DescribeDBAutoScalingConfigResp_ManualLocalSpecConfig_DEFAULT
	}
	return p.ManualLocalSpecConfig
}

var DescribeDBAutoScalingConfigResp_LocalSpecLimit_DEFAULT *LocalSpecScalingLimit

func (p *DescribeDBAutoScalingConfigResp) GetLocalSpecLimit() (v *LocalSpecScalingLimit) {
	if !p.IsSetLocalSpecLimit() {
		return DescribeDBAutoScalingConfigResp_LocalSpecLimit_DEFAULT
	}
	return p.LocalSpecLimit
}
func (p *DescribeDBAutoScalingConfigResp) SetAutoLocalSpecConfig(val *AutoLocalSpecScalingConfig) {
	p.AutoLocalSpecConfig = val
}
func (p *DescribeDBAutoScalingConfigResp) SetManualLocalSpecConfig(val *ManualLocalSpecScalingConfig) {
	p.ManualLocalSpecConfig = val
}
func (p *DescribeDBAutoScalingConfigResp) SetLocalSpecLimit(val *LocalSpecScalingLimit) {
	p.LocalSpecLimit = val
}

var fieldIDToName_DescribeDBAutoScalingConfigResp = map[int16]string{
	1: "AutoLocalSpecConfig",
	2: "ManualLocalSpecConfig",
	3: "LocalSpecLimit",
}

func (p *DescribeDBAutoScalingConfigResp) IsSetAutoLocalSpecConfig() bool {
	return p.AutoLocalSpecConfig != nil
}

func (p *DescribeDBAutoScalingConfigResp) IsSetManualLocalSpecConfig() bool {
	return p.ManualLocalSpecConfig != nil
}

func (p *DescribeDBAutoScalingConfigResp) IsSetLocalSpecLimit() bool {
	return p.LocalSpecLimit != nil
}

func (p *DescribeDBAutoScalingConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScalingConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLocalSpecLimit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLocalSpecLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLocalSpecLimit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAutoScalingConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAutoScalingConfigResp[fieldId]))
}

func (p *DescribeDBAutoScalingConfigResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewAutoLocalSpecScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AutoLocalSpecConfig = _field
	return nil
}
func (p *DescribeDBAutoScalingConfigResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewManualLocalSpecScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ManualLocalSpecConfig = _field
	return nil
}
func (p *DescribeDBAutoScalingConfigResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewLocalSpecScalingLimit()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LocalSpecLimit = _field
	return nil
}

func (p *DescribeDBAutoScalingConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScalingConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAutoScalingConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoLocalSpecConfig() {
		if err = oprot.WriteFieldBegin("AutoLocalSpecConfig", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AutoLocalSpecConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetManualLocalSpecConfig() {
		if err = oprot.WriteFieldBegin("ManualLocalSpecConfig", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ManualLocalSpecConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LocalSpecLimit", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.LocalSpecLimit.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBAutoScalingConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAutoScalingConfigResp(%+v)", *p)

}

func (p *DescribeDBAutoScalingConfigResp) DeepEqual(ano *DescribeDBAutoScalingConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AutoLocalSpecConfig) {
		return false
	}
	if !p.Field2DeepEqual(ano.ManualLocalSpecConfig) {
		return false
	}
	if !p.Field3DeepEqual(ano.LocalSpecLimit) {
		return false
	}
	return true
}

func (p *DescribeDBAutoScalingConfigResp) Field1DeepEqual(src *AutoLocalSpecScalingConfig) bool {

	if !p.AutoLocalSpecConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBAutoScalingConfigResp) Field2DeepEqual(src *ManualLocalSpecScalingConfig) bool {

	if !p.ManualLocalSpecConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBAutoScalingConfigResp) Field3DeepEqual(src *LocalSpecScalingLimit) bool {

	if !p.LocalSpecLimit.DeepEqual(src) {
		return false
	}
	return true
}

type AutoLocalSpecScalingConfig struct {
	AutoEnable       bool                        `thrift:"AutoEnable,1,required" frugal:"1,required,bool" json:"AutoEnable"`
	AutoNodeRules    []*AutoLocalSpecScalingRule `thrift:"AutoNodeRules,2,required" frugal:"2,required,list<AutoLocalSpecScalingRule>" json:"AutoNodeRules"`
	TriggerNodeInfos []*LocalSpecNodeInfo        `thrift:"TriggerNodeInfos,3,optional" frugal:"3,optional,list<LocalSpecNodeInfo>" json:"TriggerNodeInfos,omitempty"`
}

func NewAutoLocalSpecScalingConfig() *AutoLocalSpecScalingConfig {
	return &AutoLocalSpecScalingConfig{}
}

func (p *AutoLocalSpecScalingConfig) InitDefault() {
}

func (p *AutoLocalSpecScalingConfig) GetAutoEnable() (v bool) {
	return p.AutoEnable
}

func (p *AutoLocalSpecScalingConfig) GetAutoNodeRules() (v []*AutoLocalSpecScalingRule) {
	return p.AutoNodeRules
}

var AutoLocalSpecScalingConfig_TriggerNodeInfos_DEFAULT []*LocalSpecNodeInfo

func (p *AutoLocalSpecScalingConfig) GetTriggerNodeInfos() (v []*LocalSpecNodeInfo) {
	if !p.IsSetTriggerNodeInfos() {
		return AutoLocalSpecScalingConfig_TriggerNodeInfos_DEFAULT
	}
	return p.TriggerNodeInfos
}
func (p *AutoLocalSpecScalingConfig) SetAutoEnable(val bool) {
	p.AutoEnable = val
}
func (p *AutoLocalSpecScalingConfig) SetAutoNodeRules(val []*AutoLocalSpecScalingRule) {
	p.AutoNodeRules = val
}
func (p *AutoLocalSpecScalingConfig) SetTriggerNodeInfos(val []*LocalSpecNodeInfo) {
	p.TriggerNodeInfos = val
}

var fieldIDToName_AutoLocalSpecScalingConfig = map[int16]string{
	1: "AutoEnable",
	2: "AutoNodeRules",
	3: "TriggerNodeInfos",
}

func (p *AutoLocalSpecScalingConfig) IsSetTriggerNodeInfos() bool {
	return p.TriggerNodeInfos != nil
}

func (p *AutoLocalSpecScalingConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoLocalSpecScalingConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAutoEnable bool = false
	var issetAutoNodeRules bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAutoEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAutoNodeRules = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAutoEnable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAutoNodeRules {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoLocalSpecScalingConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AutoLocalSpecScalingConfig[fieldId]))
}

func (p *AutoLocalSpecScalingConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AutoEnable = _field
	return nil
}
func (p *AutoLocalSpecScalingConfig) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AutoLocalSpecScalingRule, 0, size)
	values := make([]AutoLocalSpecScalingRule, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AutoNodeRules = _field
	return nil
}
func (p *AutoLocalSpecScalingConfig) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LocalSpecNodeInfo, 0, size)
	values := make([]LocalSpecNodeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TriggerNodeInfos = _field
	return nil
}

func (p *AutoLocalSpecScalingConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoLocalSpecScalingConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoLocalSpecScalingConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoLocalSpecScalingConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoEnable", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AutoEnable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoLocalSpecScalingConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoNodeRules", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AutoNodeRules)); err != nil {
		return err
	}
	for _, v := range p.AutoNodeRules {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoLocalSpecScalingConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerNodeInfos() {
		if err = oprot.WriteFieldBegin("TriggerNodeInfos", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TriggerNodeInfos)); err != nil {
			return err
		}
		for _, v := range p.TriggerNodeInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoLocalSpecScalingConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoLocalSpecScalingConfig(%+v)", *p)

}

func (p *AutoLocalSpecScalingConfig) DeepEqual(ano *AutoLocalSpecScalingConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AutoEnable) {
		return false
	}
	if !p.Field2DeepEqual(ano.AutoNodeRules) {
		return false
	}
	if !p.Field3DeepEqual(ano.TriggerNodeInfos) {
		return false
	}
	return true
}

func (p *AutoLocalSpecScalingConfig) Field1DeepEqual(src bool) bool {

	if p.AutoEnable != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingConfig) Field2DeepEqual(src []*AutoLocalSpecScalingRule) bool {

	if len(p.AutoNodeRules) != len(src) {
		return false
	}
	for i, v := range p.AutoNodeRules {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *AutoLocalSpecScalingConfig) Field3DeepEqual(src []*LocalSpecNodeInfo) bool {

	if len(p.TriggerNodeInfos) != len(src) {
		return false
	}
	for i, v := range p.TriggerNodeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ManualLocalSpecScalingConfig struct {
	ManualEnable      bool                        `thrift:"ManualEnable,1,required" frugal:"1,required,bool" json:"ManualEnable"`
	TriggerNodeInfos  []*LocalSpecNodeInfo        `thrift:"TriggerNodeInfos,2,required" frugal:"2,required,list<LocalSpecNodeInfo>" json:"TriggerNodeInfos"`
	DeadlineTime      *string                     `thrift:"DeadlineTime,3,optional" frugal:"3,optional,string" json:"DeadlineTime,omitempty"`
	RuleStatus        *LocalSpecScalingRuleStatus `thrift:"RuleStatus,4,optional" frugal:"4,optional,LocalSpecScalingRuleStatus" json:"RuleStatus,omitempty"`
	EffectiveTimeHour *int32                      `thrift:"EffectiveTimeHour,5,optional" frugal:"5,optional,i32" json:"EffectiveTimeHour,omitempty"`
}

func NewManualLocalSpecScalingConfig() *ManualLocalSpecScalingConfig {
	return &ManualLocalSpecScalingConfig{}
}

func (p *ManualLocalSpecScalingConfig) InitDefault() {
}

func (p *ManualLocalSpecScalingConfig) GetManualEnable() (v bool) {
	return p.ManualEnable
}

func (p *ManualLocalSpecScalingConfig) GetTriggerNodeInfos() (v []*LocalSpecNodeInfo) {
	return p.TriggerNodeInfos
}

var ManualLocalSpecScalingConfig_DeadlineTime_DEFAULT string

func (p *ManualLocalSpecScalingConfig) GetDeadlineTime() (v string) {
	if !p.IsSetDeadlineTime() {
		return ManualLocalSpecScalingConfig_DeadlineTime_DEFAULT
	}
	return *p.DeadlineTime
}

var ManualLocalSpecScalingConfig_RuleStatus_DEFAULT LocalSpecScalingRuleStatus

func (p *ManualLocalSpecScalingConfig) GetRuleStatus() (v LocalSpecScalingRuleStatus) {
	if !p.IsSetRuleStatus() {
		return ManualLocalSpecScalingConfig_RuleStatus_DEFAULT
	}
	return *p.RuleStatus
}

var ManualLocalSpecScalingConfig_EffectiveTimeHour_DEFAULT int32

func (p *ManualLocalSpecScalingConfig) GetEffectiveTimeHour() (v int32) {
	if !p.IsSetEffectiveTimeHour() {
		return ManualLocalSpecScalingConfig_EffectiveTimeHour_DEFAULT
	}
	return *p.EffectiveTimeHour
}
func (p *ManualLocalSpecScalingConfig) SetManualEnable(val bool) {
	p.ManualEnable = val
}
func (p *ManualLocalSpecScalingConfig) SetTriggerNodeInfos(val []*LocalSpecNodeInfo) {
	p.TriggerNodeInfos = val
}
func (p *ManualLocalSpecScalingConfig) SetDeadlineTime(val *string) {
	p.DeadlineTime = val
}
func (p *ManualLocalSpecScalingConfig) SetRuleStatus(val *LocalSpecScalingRuleStatus) {
	p.RuleStatus = val
}
func (p *ManualLocalSpecScalingConfig) SetEffectiveTimeHour(val *int32) {
	p.EffectiveTimeHour = val
}

var fieldIDToName_ManualLocalSpecScalingConfig = map[int16]string{
	1: "ManualEnable",
	2: "TriggerNodeInfos",
	3: "DeadlineTime",
	4: "RuleStatus",
	5: "EffectiveTimeHour",
}

func (p *ManualLocalSpecScalingConfig) IsSetDeadlineTime() bool {
	return p.DeadlineTime != nil
}

func (p *ManualLocalSpecScalingConfig) IsSetRuleStatus() bool {
	return p.RuleStatus != nil
}

func (p *ManualLocalSpecScalingConfig) IsSetEffectiveTimeHour() bool {
	return p.EffectiveTimeHour != nil
}

func (p *ManualLocalSpecScalingConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ManualLocalSpecScalingConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetManualEnable bool = false
	var issetTriggerNodeInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetManualEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerNodeInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetManualEnable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTriggerNodeInfos {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ManualLocalSpecScalingConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ManualLocalSpecScalingConfig[fieldId]))
}

func (p *ManualLocalSpecScalingConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ManualEnable = _field
	return nil
}
func (p *ManualLocalSpecScalingConfig) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LocalSpecNodeInfo, 0, size)
	values := make([]LocalSpecNodeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TriggerNodeInfos = _field
	return nil
}
func (p *ManualLocalSpecScalingConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DeadlineTime = _field
	return nil
}
func (p *ManualLocalSpecScalingConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *LocalSpecScalingRuleStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LocalSpecScalingRuleStatus(v)
		_field = &tmp
	}
	p.RuleStatus = _field
	return nil
}
func (p *ManualLocalSpecScalingConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EffectiveTimeHour = _field
	return nil
}

func (p *ManualLocalSpecScalingConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ManualLocalSpecScalingConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("ManualLocalSpecScalingConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ManualEnable", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ManualEnable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerNodeInfos", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TriggerNodeInfos)); err != nil {
		return err
	}
	for _, v := range p.TriggerNodeInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeadlineTime() {
		if err = oprot.WriteFieldBegin("DeadlineTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DeadlineTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleStatus() {
		if err = oprot.WriteFieldBegin("RuleStatus", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RuleStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEffectiveTimeHour() {
		if err = oprot.WriteFieldBegin("EffectiveTimeHour", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.EffectiveTimeHour); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ManualLocalSpecScalingConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ManualLocalSpecScalingConfig(%+v)", *p)

}

func (p *ManualLocalSpecScalingConfig) DeepEqual(ano *ManualLocalSpecScalingConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ManualEnable) {
		return false
	}
	if !p.Field2DeepEqual(ano.TriggerNodeInfos) {
		return false
	}
	if !p.Field3DeepEqual(ano.DeadlineTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.RuleStatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.EffectiveTimeHour) {
		return false
	}
	return true
}

func (p *ManualLocalSpecScalingConfig) Field1DeepEqual(src bool) bool {

	if p.ManualEnable != src {
		return false
	}
	return true
}
func (p *ManualLocalSpecScalingConfig) Field2DeepEqual(src []*LocalSpecNodeInfo) bool {

	if len(p.TriggerNodeInfos) != len(src) {
		return false
	}
	for i, v := range p.TriggerNodeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ManualLocalSpecScalingConfig) Field3DeepEqual(src *string) bool {

	if p.DeadlineTime == src {
		return true
	} else if p.DeadlineTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DeadlineTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ManualLocalSpecScalingConfig) Field4DeepEqual(src *LocalSpecScalingRuleStatus) bool {

	if p.RuleStatus == src {
		return true
	} else if p.RuleStatus == nil || src == nil {
		return false
	}
	if *p.RuleStatus != *src {
		return false
	}
	return true
}
func (p *ManualLocalSpecScalingConfig) Field5DeepEqual(src *int32) bool {

	if p.EffectiveTimeHour == src {
		return true
	} else if p.EffectiveTimeHour == nil || src == nil {
		return false
	}
	if *p.EffectiveTimeHour != *src {
		return false
	}
	return true
}

type LocalSpecScalingLimit struct {
	ExpandTimeDurations        []int32                  `thrift:"ExpandTimeDurations,1,required" frugal:"1,required,list<i32>" json:"ExpandTimeDurations"`
	ExpandMaxTriggerThreshold  int32                    `thrift:"ExpandMaxTriggerThreshold,2,required" frugal:"2,required,i32" json:"ExpandMaxTriggerThreshold"`
	ExpandMinTriggerThreshold  int32                    `thrift:"ExpandMinTriggerThreshold,3,required" frugal:"3,required,i32" json:"ExpandMinTriggerThreshold"`
	ReduceMinTriggerThreshold  int32                    `thrift:"ReduceMinTriggerThreshold,4,required" frugal:"4,required,i32" json:"ReduceMinTriggerThreshold"`
	ReduceTimeDurations        []int32                  `thrift:"ReduceTimeDurations,5,required" frugal:"5,required,list<i32>" json:"ReduceTimeDurations"`
	NodeInfos                  []*CurrentNodeInfoConfig `thrift:"NodeInfos,6,required" frugal:"6,required,list<CurrentNodeInfoConfig>" json:"NodeInfos"`
	OriginPrice                float64                  `thrift:"OriginPrice,7,required" frugal:"7,required,double" json:"OriginPrice"`
	DiscountPrice              float64                  `thrift:"DiscountPrice,8,required" frugal:"8,required,double" json:"DiscountPrice"`
	CpuMaxTimes                int32                    `thrift:"CpuMaxTimes,9,required" frugal:"9,required,i32" json:"CpuMaxTimes"`
	SupportScaling             bool                     `thrift:"SupportScaling,10,required" frugal:"10,required,bool" json:"SupportScaling"`
	CpuMaxCount                int32                    `thrift:"CpuMaxCount,11,required" frugal:"11,required,i32" json:"CpuMaxCount"`
	MaxManualEffectiveTimeHour int64                    `thrift:"MaxManualEffectiveTimeHour,12,required" frugal:"12,required,i64" json:"MaxManualEffectiveTimeHour"`
	MinManualEffectiveTimeHour int64                    `thrift:"MinManualEffectiveTimeHour,13,required" frugal:"13,required,i64" json:"MinManualEffectiveTimeHour"`
	HidePriceInfo              bool                     `thrift:"HidePriceInfo,14,required" frugal:"14,required,bool" json:"HidePriceInfo"`
}

func NewLocalSpecScalingLimit() *LocalSpecScalingLimit {
	return &LocalSpecScalingLimit{}
}

func (p *LocalSpecScalingLimit) InitDefault() {
}

func (p *LocalSpecScalingLimit) GetExpandTimeDurations() (v []int32) {
	return p.ExpandTimeDurations
}

func (p *LocalSpecScalingLimit) GetExpandMaxTriggerThreshold() (v int32) {
	return p.ExpandMaxTriggerThreshold
}

func (p *LocalSpecScalingLimit) GetExpandMinTriggerThreshold() (v int32) {
	return p.ExpandMinTriggerThreshold
}

func (p *LocalSpecScalingLimit) GetReduceMinTriggerThreshold() (v int32) {
	return p.ReduceMinTriggerThreshold
}

func (p *LocalSpecScalingLimit) GetReduceTimeDurations() (v []int32) {
	return p.ReduceTimeDurations
}

func (p *LocalSpecScalingLimit) GetNodeInfos() (v []*CurrentNodeInfoConfig) {
	return p.NodeInfos
}

func (p *LocalSpecScalingLimit) GetOriginPrice() (v float64) {
	return p.OriginPrice
}

func (p *LocalSpecScalingLimit) GetDiscountPrice() (v float64) {
	return p.DiscountPrice
}

func (p *LocalSpecScalingLimit) GetCpuMaxTimes() (v int32) {
	return p.CpuMaxTimes
}

func (p *LocalSpecScalingLimit) GetSupportScaling() (v bool) {
	return p.SupportScaling
}

func (p *LocalSpecScalingLimit) GetCpuMaxCount() (v int32) {
	return p.CpuMaxCount
}

func (p *LocalSpecScalingLimit) GetMaxManualEffectiveTimeHour() (v int64) {
	return p.MaxManualEffectiveTimeHour
}

func (p *LocalSpecScalingLimit) GetMinManualEffectiveTimeHour() (v int64) {
	return p.MinManualEffectiveTimeHour
}

func (p *LocalSpecScalingLimit) GetHidePriceInfo() (v bool) {
	return p.HidePriceInfo
}
func (p *LocalSpecScalingLimit) SetExpandTimeDurations(val []int32) {
	p.ExpandTimeDurations = val
}
func (p *LocalSpecScalingLimit) SetExpandMaxTriggerThreshold(val int32) {
	p.ExpandMaxTriggerThreshold = val
}
func (p *LocalSpecScalingLimit) SetExpandMinTriggerThreshold(val int32) {
	p.ExpandMinTriggerThreshold = val
}
func (p *LocalSpecScalingLimit) SetReduceMinTriggerThreshold(val int32) {
	p.ReduceMinTriggerThreshold = val
}
func (p *LocalSpecScalingLimit) SetReduceTimeDurations(val []int32) {
	p.ReduceTimeDurations = val
}
func (p *LocalSpecScalingLimit) SetNodeInfos(val []*CurrentNodeInfoConfig) {
	p.NodeInfos = val
}
func (p *LocalSpecScalingLimit) SetOriginPrice(val float64) {
	p.OriginPrice = val
}
func (p *LocalSpecScalingLimit) SetDiscountPrice(val float64) {
	p.DiscountPrice = val
}
func (p *LocalSpecScalingLimit) SetCpuMaxTimes(val int32) {
	p.CpuMaxTimes = val
}
func (p *LocalSpecScalingLimit) SetSupportScaling(val bool) {
	p.SupportScaling = val
}
func (p *LocalSpecScalingLimit) SetCpuMaxCount(val int32) {
	p.CpuMaxCount = val
}
func (p *LocalSpecScalingLimit) SetMaxManualEffectiveTimeHour(val int64) {
	p.MaxManualEffectiveTimeHour = val
}
func (p *LocalSpecScalingLimit) SetMinManualEffectiveTimeHour(val int64) {
	p.MinManualEffectiveTimeHour = val
}
func (p *LocalSpecScalingLimit) SetHidePriceInfo(val bool) {
	p.HidePriceInfo = val
}

var fieldIDToName_LocalSpecScalingLimit = map[int16]string{
	1:  "ExpandTimeDurations",
	2:  "ExpandMaxTriggerThreshold",
	3:  "ExpandMinTriggerThreshold",
	4:  "ReduceMinTriggerThreshold",
	5:  "ReduceTimeDurations",
	6:  "NodeInfos",
	7:  "OriginPrice",
	8:  "DiscountPrice",
	9:  "CpuMaxTimes",
	10: "SupportScaling",
	11: "CpuMaxCount",
	12: "MaxManualEffectiveTimeHour",
	13: "MinManualEffectiveTimeHour",
	14: "HidePriceInfo",
}

func (p *LocalSpecScalingLimit) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LocalSpecScalingLimit")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExpandTimeDurations bool = false
	var issetExpandMaxTriggerThreshold bool = false
	var issetExpandMinTriggerThreshold bool = false
	var issetReduceMinTriggerThreshold bool = false
	var issetReduceTimeDurations bool = false
	var issetNodeInfos bool = false
	var issetOriginPrice bool = false
	var issetDiscountPrice bool = false
	var issetCpuMaxTimes bool = false
	var issetSupportScaling bool = false
	var issetCpuMaxCount bool = false
	var issetMaxManualEffectiveTimeHour bool = false
	var issetMinManualEffectiveTimeHour bool = false
	var issetHidePriceInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpandTimeDurations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpandMaxTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpandMinTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetReduceMinTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetReduceTimeDurations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetOriginPrice = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiscountPrice = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuMaxTimes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetSupportScaling = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuMaxCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxManualEffectiveTimeHour = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetMinManualEffectiveTimeHour = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetHidePriceInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExpandTimeDurations {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetExpandMaxTriggerThreshold {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetExpandMinTriggerThreshold {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetReduceMinTriggerThreshold {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetReduceTimeDurations {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNodeInfos {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetOriginPrice {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDiscountPrice {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetCpuMaxTimes {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetSupportScaling {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCpuMaxCount {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetMaxManualEffectiveTimeHour {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetMinManualEffectiveTimeHour {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetHidePriceInfo {
		fieldId = 14
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LocalSpecScalingLimit[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LocalSpecScalingLimit[fieldId]))
}

func (p *LocalSpecScalingLimit) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int32, 0, size)
	for i := 0; i < size; i++ {

		var _elem int32
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExpandTimeDurations = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpandMaxTriggerThreshold = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpandMinTriggerThreshold = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReduceMinTriggerThreshold = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int32, 0, size)
	for i := 0; i < size; i++ {

		var _elem int32
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ReduceTimeDurations = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CurrentNodeInfoConfig, 0, size)
	values := make([]CurrentNodeInfoConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeInfos = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField7(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OriginPrice = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField8(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DiscountPrice = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpuMaxTimes = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SupportScaling = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField11(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpuMaxCount = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField12(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxManualEffectiveTimeHour = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField13(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MinManualEffectiveTimeHour = _field
	return nil
}
func (p *LocalSpecScalingLimit) ReadField14(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HidePriceInfo = _field
	return nil
}

func (p *LocalSpecScalingLimit) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LocalSpecScalingLimit")

	var fieldId int16
	if err = oprot.WriteStructBegin("LocalSpecScalingLimit"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpandTimeDurations", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.ExpandTimeDurations)); err != nil {
		return err
	}
	for _, v := range p.ExpandTimeDurations {
		if err := oprot.WriteI32(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpandMaxTriggerThreshold", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExpandMaxTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpandMinTriggerThreshold", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExpandMinTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReduceMinTriggerThreshold", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ReduceMinTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReduceTimeDurations", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.ReduceTimeDurations)); err != nil {
		return err
	}
	for _, v := range p.ReduceTimeDurations {
		if err := oprot.WriteI32(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeInfos", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeInfos)); err != nil {
		return err
	}
	for _, v := range p.NodeInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OriginPrice", thrift.DOUBLE, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.OriginPrice); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiscountPrice", thrift.DOUBLE, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.DiscountPrice); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuMaxTimes", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CpuMaxTimes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SupportScaling", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.SupportScaling); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuMaxCount", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CpuMaxCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxManualEffectiveTimeHour", thrift.I64, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxManualEffectiveTimeHour); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MinManualEffectiveTimeHour", thrift.I64, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MinManualEffectiveTimeHour); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HidePriceInfo", thrift.BOOL, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HidePriceInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *LocalSpecScalingLimit) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LocalSpecScalingLimit(%+v)", *p)

}

func (p *LocalSpecScalingLimit) DeepEqual(ano *LocalSpecScalingLimit) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExpandTimeDurations) {
		return false
	}
	if !p.Field2DeepEqual(ano.ExpandMaxTriggerThreshold) {
		return false
	}
	if !p.Field3DeepEqual(ano.ExpandMinTriggerThreshold) {
		return false
	}
	if !p.Field4DeepEqual(ano.ReduceMinTriggerThreshold) {
		return false
	}
	if !p.Field5DeepEqual(ano.ReduceTimeDurations) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeInfos) {
		return false
	}
	if !p.Field7DeepEqual(ano.OriginPrice) {
		return false
	}
	if !p.Field8DeepEqual(ano.DiscountPrice) {
		return false
	}
	if !p.Field9DeepEqual(ano.CpuMaxTimes) {
		return false
	}
	if !p.Field10DeepEqual(ano.SupportScaling) {
		return false
	}
	if !p.Field11DeepEqual(ano.CpuMaxCount) {
		return false
	}
	if !p.Field12DeepEqual(ano.MaxManualEffectiveTimeHour) {
		return false
	}
	if !p.Field13DeepEqual(ano.MinManualEffectiveTimeHour) {
		return false
	}
	if !p.Field14DeepEqual(ano.HidePriceInfo) {
		return false
	}
	return true
}

func (p *LocalSpecScalingLimit) Field1DeepEqual(src []int32) bool {

	if len(p.ExpandTimeDurations) != len(src) {
		return false
	}
	for i, v := range p.ExpandTimeDurations {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *LocalSpecScalingLimit) Field2DeepEqual(src int32) bool {

	if p.ExpandMaxTriggerThreshold != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field3DeepEqual(src int32) bool {

	if p.ExpandMinTriggerThreshold != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field4DeepEqual(src int32) bool {

	if p.ReduceMinTriggerThreshold != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field5DeepEqual(src []int32) bool {

	if len(p.ReduceTimeDurations) != len(src) {
		return false
	}
	for i, v := range p.ReduceTimeDurations {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *LocalSpecScalingLimit) Field6DeepEqual(src []*CurrentNodeInfoConfig) bool {

	if len(p.NodeInfos) != len(src) {
		return false
	}
	for i, v := range p.NodeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LocalSpecScalingLimit) Field7DeepEqual(src float64) bool {

	if p.OriginPrice != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field8DeepEqual(src float64) bool {

	if p.DiscountPrice != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field9DeepEqual(src int32) bool {

	if p.CpuMaxTimes != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field10DeepEqual(src bool) bool {

	if p.SupportScaling != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field11DeepEqual(src int32) bool {

	if p.CpuMaxCount != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field12DeepEqual(src int64) bool {

	if p.MaxManualEffectiveTimeHour != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field13DeepEqual(src int64) bool {

	if p.MinManualEffectiveTimeHour != src {
		return false
	}
	return true
}
func (p *LocalSpecScalingLimit) Field14DeepEqual(src bool) bool {

	if p.HidePriceInfo != src {
		return false
	}
	return true
}

type CurrentNodeInfoConfig struct {
	NodeId   string        `thrift:"NodeId,1,required" frugal:"1,required,string" json:"NodeId"`
	VCPU     int32         `thrift:"VCPU,2,required" frugal:"2,required,i32" json:"VCPU"`
	NodeType ScaleNodeType `thrift:"NodeType,3,required" frugal:"3,required,ScaleNodeType" json:"NodeType"`
}

func NewCurrentNodeInfoConfig() *CurrentNodeInfoConfig {
	return &CurrentNodeInfoConfig{}
}

func (p *CurrentNodeInfoConfig) InitDefault() {
}

func (p *CurrentNodeInfoConfig) GetNodeId() (v string) {
	return p.NodeId
}

func (p *CurrentNodeInfoConfig) GetVCPU() (v int32) {
	return p.VCPU
}

func (p *CurrentNodeInfoConfig) GetNodeType() (v ScaleNodeType) {
	return p.NodeType
}
func (p *CurrentNodeInfoConfig) SetNodeId(val string) {
	p.NodeId = val
}
func (p *CurrentNodeInfoConfig) SetVCPU(val int32) {
	p.VCPU = val
}
func (p *CurrentNodeInfoConfig) SetNodeType(val ScaleNodeType) {
	p.NodeType = val
}

var fieldIDToName_CurrentNodeInfoConfig = map[int16]string{
	1: "NodeId",
	2: "VCPU",
	3: "NodeType",
}

func (p *CurrentNodeInfoConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CurrentNodeInfoConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeId bool = false
	var issetVCPU bool = false
	var issetNodeType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVCPU = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVCPU {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CurrentNodeInfoConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CurrentNodeInfoConfig[fieldId]))
}

func (p *CurrentNodeInfoConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *CurrentNodeInfoConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VCPU = _field
	return nil
}
func (p *CurrentNodeInfoConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field ScaleNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ScaleNodeType(v)
	}
	p.NodeType = _field
	return nil
}

func (p *CurrentNodeInfoConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CurrentNodeInfoConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("CurrentNodeInfoConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CurrentNodeInfoConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CurrentNodeInfoConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VCPU", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.VCPU); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CurrentNodeInfoConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.NodeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CurrentNodeInfoConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CurrentNodeInfoConfig(%+v)", *p)

}

func (p *CurrentNodeInfoConfig) DeepEqual(ano *CurrentNodeInfoConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field2DeepEqual(ano.VCPU) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeType) {
		return false
	}
	return true
}

func (p *CurrentNodeInfoConfig) Field1DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *CurrentNodeInfoConfig) Field2DeepEqual(src int32) bool {

	if p.VCPU != src {
		return false
	}
	return true
}
func (p *CurrentNodeInfoConfig) Field3DeepEqual(src ScaleNodeType) bool {

	if p.NodeType != src {
		return false
	}
	return true
}

type AutoLocalSpecScalingRule struct {
	ExpandTimeDurations       int32                       `thrift:"ExpandTimeDurations,1,required" frugal:"1,required,i32" json:"ExpandTimeDurations"`
	ExpandTriggerThreshold    int32                       `thrift:"ExpandTriggerThreshold,2,required" frugal:"2,required,i32" json:"ExpandTriggerThreshold"`
	ReduceTimeDurations       int32                       `thrift:"ReduceTimeDurations,3,required" frugal:"3,required,i32" json:"ReduceTimeDurations"`
	ReduceMinTriggerThreshold int32                       `thrift:"ReduceMinTriggerThreshold,4,required" frugal:"4,required,i32" json:"ReduceMinTriggerThreshold"`
	RuleType                  ScalingRuleType             `thrift:"RuleType,5,required" frugal:"5,required,ScalingRuleType" json:"RuleType"`
	NodeIds                   []string                    `thrift:"NodeIds,6,required" frugal:"6,required,list<string>" json:"NodeIds"`
	Trigger                   *bool                       `thrift:"Trigger,7,optional" frugal:"7,optional,bool" json:"Trigger,omitempty"`
	RuleId                    *int64                      `thrift:"RuleId,8,optional" frugal:"8,optional,i64" json:"RuleId,omitempty"`
	RuleStatus                *LocalSpecScalingRuleStatus `thrift:"RuleStatus,9,optional" frugal:"9,optional,LocalSpecScalingRuleStatus" json:"RuleStatus,omitempty"`
}

func NewAutoLocalSpecScalingRule() *AutoLocalSpecScalingRule {
	return &AutoLocalSpecScalingRule{}
}

func (p *AutoLocalSpecScalingRule) InitDefault() {
}

func (p *AutoLocalSpecScalingRule) GetExpandTimeDurations() (v int32) {
	return p.ExpandTimeDurations
}

func (p *AutoLocalSpecScalingRule) GetExpandTriggerThreshold() (v int32) {
	return p.ExpandTriggerThreshold
}

func (p *AutoLocalSpecScalingRule) GetReduceTimeDurations() (v int32) {
	return p.ReduceTimeDurations
}

func (p *AutoLocalSpecScalingRule) GetReduceMinTriggerThreshold() (v int32) {
	return p.ReduceMinTriggerThreshold
}

func (p *AutoLocalSpecScalingRule) GetRuleType() (v ScalingRuleType) {
	return p.RuleType
}

func (p *AutoLocalSpecScalingRule) GetNodeIds() (v []string) {
	return p.NodeIds
}

var AutoLocalSpecScalingRule_Trigger_DEFAULT bool

func (p *AutoLocalSpecScalingRule) GetTrigger() (v bool) {
	if !p.IsSetTrigger() {
		return AutoLocalSpecScalingRule_Trigger_DEFAULT
	}
	return *p.Trigger
}

var AutoLocalSpecScalingRule_RuleId_DEFAULT int64

func (p *AutoLocalSpecScalingRule) GetRuleId() (v int64) {
	if !p.IsSetRuleId() {
		return AutoLocalSpecScalingRule_RuleId_DEFAULT
	}
	return *p.RuleId
}

var AutoLocalSpecScalingRule_RuleStatus_DEFAULT LocalSpecScalingRuleStatus

func (p *AutoLocalSpecScalingRule) GetRuleStatus() (v LocalSpecScalingRuleStatus) {
	if !p.IsSetRuleStatus() {
		return AutoLocalSpecScalingRule_RuleStatus_DEFAULT
	}
	return *p.RuleStatus
}
func (p *AutoLocalSpecScalingRule) SetExpandTimeDurations(val int32) {
	p.ExpandTimeDurations = val
}
func (p *AutoLocalSpecScalingRule) SetExpandTriggerThreshold(val int32) {
	p.ExpandTriggerThreshold = val
}
func (p *AutoLocalSpecScalingRule) SetReduceTimeDurations(val int32) {
	p.ReduceTimeDurations = val
}
func (p *AutoLocalSpecScalingRule) SetReduceMinTriggerThreshold(val int32) {
	p.ReduceMinTriggerThreshold = val
}
func (p *AutoLocalSpecScalingRule) SetRuleType(val ScalingRuleType) {
	p.RuleType = val
}
func (p *AutoLocalSpecScalingRule) SetNodeIds(val []string) {
	p.NodeIds = val
}
func (p *AutoLocalSpecScalingRule) SetTrigger(val *bool) {
	p.Trigger = val
}
func (p *AutoLocalSpecScalingRule) SetRuleId(val *int64) {
	p.RuleId = val
}
func (p *AutoLocalSpecScalingRule) SetRuleStatus(val *LocalSpecScalingRuleStatus) {
	p.RuleStatus = val
}

var fieldIDToName_AutoLocalSpecScalingRule = map[int16]string{
	1: "ExpandTimeDurations",
	2: "ExpandTriggerThreshold",
	3: "ReduceTimeDurations",
	4: "ReduceMinTriggerThreshold",
	5: "RuleType",
	6: "NodeIds",
	7: "Trigger",
	8: "RuleId",
	9: "RuleStatus",
}

func (p *AutoLocalSpecScalingRule) IsSetTrigger() bool {
	return p.Trigger != nil
}

func (p *AutoLocalSpecScalingRule) IsSetRuleId() bool {
	return p.RuleId != nil
}

func (p *AutoLocalSpecScalingRule) IsSetRuleStatus() bool {
	return p.RuleStatus != nil
}

func (p *AutoLocalSpecScalingRule) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoLocalSpecScalingRule")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExpandTimeDurations bool = false
	var issetExpandTriggerThreshold bool = false
	var issetReduceTimeDurations bool = false
	var issetReduceMinTriggerThreshold bool = false
	var issetRuleType bool = false
	var issetNodeIds bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpandTimeDurations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpandTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReduceTimeDurations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetReduceMinTriggerThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExpandTimeDurations {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetExpandTriggerThreshold {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReduceTimeDurations {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetReduceMinTriggerThreshold {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRuleType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetNodeIds {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoLocalSpecScalingRule[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AutoLocalSpecScalingRule[fieldId]))
}

func (p *AutoLocalSpecScalingRule) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpandTimeDurations = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpandTriggerThreshold = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReduceTimeDurations = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReduceMinTriggerThreshold = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField5(iprot thrift.TProtocol) error {

	var _field ScalingRuleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ScalingRuleType(v)
	}
	p.RuleType = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Trigger = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RuleId = _field
	return nil
}
func (p *AutoLocalSpecScalingRule) ReadField9(iprot thrift.TProtocol) error {

	var _field *LocalSpecScalingRuleStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LocalSpecScalingRuleStatus(v)
		_field = &tmp
	}
	p.RuleStatus = _field
	return nil
}

func (p *AutoLocalSpecScalingRule) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoLocalSpecScalingRule")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoLocalSpecScalingRule"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpandTimeDurations", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExpandTimeDurations); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpandTriggerThreshold", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExpandTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReduceTimeDurations", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ReduceTimeDurations); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReduceMinTriggerThreshold", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ReduceMinTriggerThreshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RuleType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RuleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
		return err
	}
	for _, v := range p.NodeIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrigger() {
		if err = oprot.WriteFieldBegin("Trigger", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Trigger); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleId() {
		if err = oprot.WriteFieldBegin("RuleId", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.RuleId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleStatus() {
		if err = oprot.WriteFieldBegin("RuleStatus", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RuleStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *AutoLocalSpecScalingRule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoLocalSpecScalingRule(%+v)", *p)

}

func (p *AutoLocalSpecScalingRule) DeepEqual(ano *AutoLocalSpecScalingRule) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExpandTimeDurations) {
		return false
	}
	if !p.Field2DeepEqual(ano.ExpandTriggerThreshold) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReduceTimeDurations) {
		return false
	}
	if !p.Field4DeepEqual(ano.ReduceMinTriggerThreshold) {
		return false
	}
	if !p.Field5DeepEqual(ano.RuleType) {
		return false
	}
	if !p.Field6DeepEqual(ano.NodeIds) {
		return false
	}
	if !p.Field7DeepEqual(ano.Trigger) {
		return false
	}
	if !p.Field8DeepEqual(ano.RuleId) {
		return false
	}
	if !p.Field9DeepEqual(ano.RuleStatus) {
		return false
	}
	return true
}

func (p *AutoLocalSpecScalingRule) Field1DeepEqual(src int32) bool {

	if p.ExpandTimeDurations != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field2DeepEqual(src int32) bool {

	if p.ExpandTriggerThreshold != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field3DeepEqual(src int32) bool {

	if p.ReduceTimeDurations != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field4DeepEqual(src int32) bool {

	if p.ReduceMinTriggerThreshold != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field5DeepEqual(src ScalingRuleType) bool {

	if p.RuleType != src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field6DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field7DeepEqual(src *bool) bool {

	if p.Trigger == src {
		return true
	} else if p.Trigger == nil || src == nil {
		return false
	}
	if *p.Trigger != *src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field8DeepEqual(src *int64) bool {

	if p.RuleId == src {
		return true
	} else if p.RuleId == nil || src == nil {
		return false
	}
	if *p.RuleId != *src {
		return false
	}
	return true
}
func (p *AutoLocalSpecScalingRule) Field9DeepEqual(src *LocalSpecScalingRuleStatus) bool {

	if p.RuleStatus == src {
		return true
	} else if p.RuleStatus == nil || src == nil {
		return false
	}
	if *p.RuleStatus != *src {
		return false
	}
	return true
}

type LocalSpecNodeInfo struct {
	NodeId   string `thrift:"NodeId,1,required" frugal:"1,required,string" json:"NodeId"`
	IncrVCPU int32  `thrift:"IncrVCPU,2,required" frugal:"2,required,i32" json:"IncrVCPU"`
}

func NewLocalSpecNodeInfo() *LocalSpecNodeInfo {
	return &LocalSpecNodeInfo{}
}

func (p *LocalSpecNodeInfo) InitDefault() {
}

func (p *LocalSpecNodeInfo) GetNodeId() (v string) {
	return p.NodeId
}

func (p *LocalSpecNodeInfo) GetIncrVCPU() (v int32) {
	return p.IncrVCPU
}
func (p *LocalSpecNodeInfo) SetNodeId(val string) {
	p.NodeId = val
}
func (p *LocalSpecNodeInfo) SetIncrVCPU(val int32) {
	p.IncrVCPU = val
}

var fieldIDToName_LocalSpecNodeInfo = map[int16]string{
	1: "NodeId",
	2: "IncrVCPU",
}

func (p *LocalSpecNodeInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LocalSpecNodeInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeId bool = false
	var issetIncrVCPU bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetIncrVCPU = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIncrVCPU {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LocalSpecNodeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LocalSpecNodeInfo[fieldId]))
}

func (p *LocalSpecNodeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *LocalSpecNodeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IncrVCPU = _field
	return nil
}

func (p *LocalSpecNodeInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("LocalSpecNodeInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("LocalSpecNodeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LocalSpecNodeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LocalSpecNodeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IncrVCPU", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.IncrVCPU); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LocalSpecNodeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LocalSpecNodeInfo(%+v)", *p)

}

func (p *LocalSpecNodeInfo) DeepEqual(ano *LocalSpecNodeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field2DeepEqual(ano.IncrVCPU) {
		return false
	}
	return true
}

func (p *LocalSpecNodeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *LocalSpecNodeInfo) Field2DeepEqual(src int32) bool {

	if p.IncrVCPU != src {
		return false
	}
	return true
}

type ModifyDBAutoScalingConfigReq struct {
	InstanceId      string                      `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType    InstanceType                `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	LocalSpecConfig *AutoLocalSpecScalingConfig `thrift:"LocalSpecConfig,3,optional" frugal:"3,optional,AutoLocalSpecScalingConfig" json:"LocalSpecConfig,omitempty"`
}

func NewModifyDBAutoScalingConfigReq() *ModifyDBAutoScalingConfigReq {
	return &ModifyDBAutoScalingConfigReq{}
}

func (p *ModifyDBAutoScalingConfigReq) InitDefault() {
}

func (p *ModifyDBAutoScalingConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBAutoScalingConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var ModifyDBAutoScalingConfigReq_LocalSpecConfig_DEFAULT *AutoLocalSpecScalingConfig

func (p *ModifyDBAutoScalingConfigReq) GetLocalSpecConfig() (v *AutoLocalSpecScalingConfig) {
	if !p.IsSetLocalSpecConfig() {
		return ModifyDBAutoScalingConfigReq_LocalSpecConfig_DEFAULT
	}
	return p.LocalSpecConfig
}
func (p *ModifyDBAutoScalingConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBAutoScalingConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyDBAutoScalingConfigReq) SetLocalSpecConfig(val *AutoLocalSpecScalingConfig) {
	p.LocalSpecConfig = val
}

var fieldIDToName_ModifyDBAutoScalingConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "LocalSpecConfig",
}

func (p *ModifyDBAutoScalingConfigReq) IsSetLocalSpecConfig() bool {
	return p.LocalSpecConfig != nil
}

func (p *ModifyDBAutoScalingConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoScalingConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBAutoScalingConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBAutoScalingConfigReq[fieldId]))
}

func (p *ModifyDBAutoScalingConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBAutoScalingConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyDBAutoScalingConfigReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewAutoLocalSpecScalingConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LocalSpecConfig = _field
	return nil
}

func (p *ModifyDBAutoScalingConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoScalingConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBAutoScalingConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocalSpecConfig() {
		if err = oprot.WriteFieldBegin("LocalSpecConfig", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.LocalSpecConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBAutoScalingConfigReq(%+v)", *p)

}

func (p *ModifyDBAutoScalingConfigReq) DeepEqual(ano *ModifyDBAutoScalingConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.LocalSpecConfig) {
		return false
	}
	return true
}

func (p *ModifyDBAutoScalingConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBAutoScalingConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyDBAutoScalingConfigReq) Field3DeepEqual(src *AutoLocalSpecScalingConfig) bool {

	if !p.LocalSpecConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyDBAutoScalingConfigResp struct {
}

func NewModifyDBAutoScalingConfigResp() *ModifyDBAutoScalingConfigResp {
	return &ModifyDBAutoScalingConfigResp{}
}

func (p *ModifyDBAutoScalingConfigResp) InitDefault() {
}

var fieldIDToName_ModifyDBAutoScalingConfigResp = map[int16]string{}

func (p *ModifyDBAutoScalingConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoScalingConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBAutoScalingConfigResp")

	if err = oprot.WriteStructBegin("ModifyDBAutoScalingConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBAutoScalingConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBAutoScalingConfigResp(%+v)", *p)

}

func (p *ModifyDBAutoScalingConfigResp) DeepEqual(ano *ModifyDBAutoScalingConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeDBAutoScaleEventsReq struct {
	InstanceType      InstanceType        `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId        string              `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	Metric            AutoScaleMetricName `thrift:"Metric,3,required" frugal:"3,required,AutoScaleMetricName" json:"Metric"`
	PageNumber        *int32              `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
	PageSize          *int32              `thrift:"PageSize,6,optional" frugal:"6,optional,i32" json:"PageSize,omitempty"`
	CreationStartTime *string             `thrift:"CreationStartTime,7,optional" frugal:"7,optional,string" json:"CreationStartTime,omitempty"`
	CreationEndTime   *string             `thrift:"CreationEndTime,8,optional" frugal:"8,optional,string" json:"CreationEndTime,omitempty"`
	EventId           *string             `thrift:"EventId,9,optional" frugal:"9,optional,string" json:"EventId,omitempty"`
	Action            *AutoScaleAction    `thrift:"Action,10,optional" frugal:"10,optional,AutoScaleAction" json:"Action,omitempty"`
	SortBy            *SortBy             `thrift:"SortBy,11,optional" frugal:"11,optional,SortBy" json:"SortBy,omitempty"`
}

func NewDescribeDBAutoScaleEventsReq() *DescribeDBAutoScaleEventsReq {
	return &DescribeDBAutoScaleEventsReq{}
}

func (p *DescribeDBAutoScaleEventsReq) InitDefault() {
}

func (p *DescribeDBAutoScaleEventsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDBAutoScaleEventsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeDBAutoScaleEventsReq) GetMetric() (v AutoScaleMetricName) {
	return p.Metric
}

var DescribeDBAutoScaleEventsReq_PageNumber_DEFAULT int32

func (p *DescribeDBAutoScaleEventsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBAutoScaleEventsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDBAutoScaleEventsReq_PageSize_DEFAULT int32

func (p *DescribeDBAutoScaleEventsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBAutoScaleEventsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDBAutoScaleEventsReq_CreationStartTime_DEFAULT string

func (p *DescribeDBAutoScaleEventsReq) GetCreationStartTime() (v string) {
	if !p.IsSetCreationStartTime() {
		return DescribeDBAutoScaleEventsReq_CreationStartTime_DEFAULT
	}
	return *p.CreationStartTime
}

var DescribeDBAutoScaleEventsReq_CreationEndTime_DEFAULT string

func (p *DescribeDBAutoScaleEventsReq) GetCreationEndTime() (v string) {
	if !p.IsSetCreationEndTime() {
		return DescribeDBAutoScaleEventsReq_CreationEndTime_DEFAULT
	}
	return *p.CreationEndTime
}

var DescribeDBAutoScaleEventsReq_EventId_DEFAULT string

func (p *DescribeDBAutoScaleEventsReq) GetEventId() (v string) {
	if !p.IsSetEventId() {
		return DescribeDBAutoScaleEventsReq_EventId_DEFAULT
	}
	return *p.EventId
}

var DescribeDBAutoScaleEventsReq_Action_DEFAULT AutoScaleAction

func (p *DescribeDBAutoScaleEventsReq) GetAction() (v AutoScaleAction) {
	if !p.IsSetAction() {
		return DescribeDBAutoScaleEventsReq_Action_DEFAULT
	}
	return *p.Action
}

var DescribeDBAutoScaleEventsReq_SortBy_DEFAULT SortBy

func (p *DescribeDBAutoScaleEventsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeDBAutoScaleEventsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}
func (p *DescribeDBAutoScaleEventsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDBAutoScaleEventsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDBAutoScaleEventsReq) SetMetric(val AutoScaleMetricName) {
	p.Metric = val
}
func (p *DescribeDBAutoScaleEventsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDBAutoScaleEventsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDBAutoScaleEventsReq) SetCreationStartTime(val *string) {
	p.CreationStartTime = val
}
func (p *DescribeDBAutoScaleEventsReq) SetCreationEndTime(val *string) {
	p.CreationEndTime = val
}
func (p *DescribeDBAutoScaleEventsReq) SetEventId(val *string) {
	p.EventId = val
}
func (p *DescribeDBAutoScaleEventsReq) SetAction(val *AutoScaleAction) {
	p.Action = val
}
func (p *DescribeDBAutoScaleEventsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}

var fieldIDToName_DescribeDBAutoScaleEventsReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "Metric",
	5:  "PageNumber",
	6:  "PageSize",
	7:  "CreationStartTime",
	8:  "CreationEndTime",
	9:  "EventId",
	10: "Action",
	11: "SortBy",
}

func (p *DescribeDBAutoScaleEventsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetCreationStartTime() bool {
	return p.CreationStartTime != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetCreationEndTime() bool {
	return p.CreationEndTime != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetEventId() bool {
	return p.EventId != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetAction() bool {
	return p.Action != nil
}

func (p *DescribeDBAutoScaleEventsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeDBAutoScaleEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScaleEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMetric {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAutoScaleEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAutoScaleEventsReq[fieldId]))
}

func (p *DescribeDBAutoScaleEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field AutoScaleMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AutoScaleMetricName(v)
	}
	p.Metric = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreationStartTime = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreationEndTime = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EventId = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *AutoScaleAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AutoScaleAction(v)
		_field = &tmp
	}
	p.Action = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}

func (p *DescribeDBAutoScaleEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScaleEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAutoScaleEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Metric", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Metric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreationStartTime() {
		if err = oprot.WriteFieldBegin("CreationStartTime", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreationStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreationEndTime() {
		if err = oprot.WriteFieldBegin("CreationEndTime", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreationEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventId() {
		if err = oprot.WriteFieldBegin("EventId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EventId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetAction() {
		if err = oprot.WriteFieldBegin("Action", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Action)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAutoScaleEventsReq(%+v)", *p)

}

func (p *DescribeDBAutoScaleEventsReq) DeepEqual(ano *DescribeDBAutoScaleEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Metric) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreationStartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.CreationEndTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.EventId) {
		return false
	}
	if !p.Field10DeepEqual(ano.Action) {
		return false
	}
	if !p.Field11DeepEqual(ano.SortBy) {
		return false
	}
	return true
}

func (p *DescribeDBAutoScaleEventsReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field3DeepEqual(src AutoScaleMetricName) bool {

	if p.Metric != src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field6DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field7DeepEqual(src *string) bool {

	if p.CreationStartTime == src {
		return true
	} else if p.CreationStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreationStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field8DeepEqual(src *string) bool {

	if p.CreationEndTime == src {
		return true
	} else if p.CreationEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreationEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field9DeepEqual(src *string) bool {

	if p.EventId == src {
		return true
	} else if p.EventId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EventId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field10DeepEqual(src *AutoScaleAction) bool {

	if p.Action == src {
		return true
	} else if p.Action == nil || src == nil {
		return false
	}
	if *p.Action != *src {
		return false
	}
	return true
}
func (p *DescribeDBAutoScaleEventsReq) Field11DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}

type DescribeDBAutoScaleEventsResp struct {
	DiskAutoScaleEvents []*AutoScalingTask `thrift:"DiskAutoScaleEvents,1,required" frugal:"1,required,list<AutoScalingTask>" json:"DiskAutoScaleEvents"`
	Total               int32              `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeDBAutoScaleEventsResp() *DescribeDBAutoScaleEventsResp {
	return &DescribeDBAutoScaleEventsResp{}
}

func (p *DescribeDBAutoScaleEventsResp) InitDefault() {
}

func (p *DescribeDBAutoScaleEventsResp) GetDiskAutoScaleEvents() (v []*AutoScalingTask) {
	return p.DiskAutoScaleEvents
}

func (p *DescribeDBAutoScaleEventsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeDBAutoScaleEventsResp) SetDiskAutoScaleEvents(val []*AutoScalingTask) {
	p.DiskAutoScaleEvents = val
}
func (p *DescribeDBAutoScaleEventsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeDBAutoScaleEventsResp = map[int16]string{
	1: "DiskAutoScaleEvents",
	2: "Total",
}

func (p *DescribeDBAutoScaleEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScaleEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDiskAutoScaleEvents bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiskAutoScaleEvents = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDiskAutoScaleEvents {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBAutoScaleEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBAutoScaleEventsResp[fieldId]))
}

func (p *DescribeDBAutoScaleEventsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AutoScalingTask, 0, size)
	values := make([]AutoScalingTask, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DiskAutoScaleEvents = _field
	return nil
}
func (p *DescribeDBAutoScaleEventsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeDBAutoScaleEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBAutoScaleEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBAutoScaleEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiskAutoScaleEvents", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DiskAutoScaleEvents)); err != nil {
		return err
	}
	for _, v := range p.DiskAutoScaleEvents {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBAutoScaleEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBAutoScaleEventsResp(%+v)", *p)

}

func (p *DescribeDBAutoScaleEventsResp) DeepEqual(ano *DescribeDBAutoScaleEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DiskAutoScaleEvents) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeDBAutoScaleEventsResp) Field1DeepEqual(src []*AutoScalingTask) bool {

	if len(p.DiskAutoScaleEvents) != len(src) {
		return false
	}
	for i, v := range p.DiskAutoScaleEvents {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBAutoScaleEventsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type AutoScalingTask struct {
	TaskAction             string            `thrift:"TaskAction,1,required" frugal:"1,required,string" json:"TaskAction"`
	TaskId                 string            `thrift:"TaskId,2,required" frugal:"2,required,string" json:"TaskId"`
	TaskState              TaskStatus        `thrift:"TaskState,3,required" frugal:"3,required,TaskStatus" json:"TaskState"`
	TriggerDateTime        string            `thrift:"TriggerDateTime,4,required" frugal:"4,required,string" json:"TriggerDateTime"`
	TriggerMetricValue     string            `thrift:"TriggerMetricValue,6,required" frugal:"6,required,string" json:"TriggerMetricValue"`
	TriggerConfigValue     string            `thrift:"TriggerConfigValue,7,required" frugal:"7,required,string" json:"TriggerConfigValue"`
	ScalingBeforeSpecValue string            `thrift:"ScalingBeforeSpecValue,8,required" frugal:"8,required,string" json:"ScalingBeforeSpecValue"`
	ScalingAfterSpecValue  string            `thrift:"ScalingAfterSpecValue,9,required" frugal:"9,required,string" json:"ScalingAfterSpecValue"`
	EventReason            string            `thrift:"EventReason,10,required" frugal:"10,required,string" json:"EventReason"`
	IsPreCheck             bool              `thrift:"IsPreCheck,11,required" frugal:"11,required,bool" json:"IsPreCheck"`
	TriggerEventType       *TriggerEventType `thrift:"TriggerEventType,12,optional" frugal:"12,optional,TriggerEventType" json:"TriggerEventType,omitempty"`
}

func NewAutoScalingTask() *AutoScalingTask {
	return &AutoScalingTask{}
}

func (p *AutoScalingTask) InitDefault() {
}

func (p *AutoScalingTask) GetTaskAction() (v string) {
	return p.TaskAction
}

func (p *AutoScalingTask) GetTaskId() (v string) {
	return p.TaskId
}

func (p *AutoScalingTask) GetTaskState() (v TaskStatus) {
	return p.TaskState
}

func (p *AutoScalingTask) GetTriggerDateTime() (v string) {
	return p.TriggerDateTime
}

func (p *AutoScalingTask) GetTriggerMetricValue() (v string) {
	return p.TriggerMetricValue
}

func (p *AutoScalingTask) GetTriggerConfigValue() (v string) {
	return p.TriggerConfigValue
}

func (p *AutoScalingTask) GetScalingBeforeSpecValue() (v string) {
	return p.ScalingBeforeSpecValue
}

func (p *AutoScalingTask) GetScalingAfterSpecValue() (v string) {
	return p.ScalingAfterSpecValue
}

func (p *AutoScalingTask) GetEventReason() (v string) {
	return p.EventReason
}

func (p *AutoScalingTask) GetIsPreCheck() (v bool) {
	return p.IsPreCheck
}

var AutoScalingTask_TriggerEventType_DEFAULT TriggerEventType

func (p *AutoScalingTask) GetTriggerEventType() (v TriggerEventType) {
	if !p.IsSetTriggerEventType() {
		return AutoScalingTask_TriggerEventType_DEFAULT
	}
	return *p.TriggerEventType
}
func (p *AutoScalingTask) SetTaskAction(val string) {
	p.TaskAction = val
}
func (p *AutoScalingTask) SetTaskId(val string) {
	p.TaskId = val
}
func (p *AutoScalingTask) SetTaskState(val TaskStatus) {
	p.TaskState = val
}
func (p *AutoScalingTask) SetTriggerDateTime(val string) {
	p.TriggerDateTime = val
}
func (p *AutoScalingTask) SetTriggerMetricValue(val string) {
	p.TriggerMetricValue = val
}
func (p *AutoScalingTask) SetTriggerConfigValue(val string) {
	p.TriggerConfigValue = val
}
func (p *AutoScalingTask) SetScalingBeforeSpecValue(val string) {
	p.ScalingBeforeSpecValue = val
}
func (p *AutoScalingTask) SetScalingAfterSpecValue(val string) {
	p.ScalingAfterSpecValue = val
}
func (p *AutoScalingTask) SetEventReason(val string) {
	p.EventReason = val
}
func (p *AutoScalingTask) SetIsPreCheck(val bool) {
	p.IsPreCheck = val
}
func (p *AutoScalingTask) SetTriggerEventType(val *TriggerEventType) {
	p.TriggerEventType = val
}

var fieldIDToName_AutoScalingTask = map[int16]string{
	1:  "TaskAction",
	2:  "TaskId",
	3:  "TaskState",
	4:  "TriggerDateTime",
	6:  "TriggerMetricValue",
	7:  "TriggerConfigValue",
	8:  "ScalingBeforeSpecValue",
	9:  "ScalingAfterSpecValue",
	10: "EventReason",
	11: "IsPreCheck",
	12: "TriggerEventType",
}

func (p *AutoScalingTask) IsSetTriggerEventType() bool {
	return p.TriggerEventType != nil
}

func (p *AutoScalingTask) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScalingTask")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskAction bool = false
	var issetTaskId bool = false
	var issetTaskState bool = false
	var issetTriggerDateTime bool = false
	var issetTriggerMetricValue bool = false
	var issetTriggerConfigValue bool = false
	var issetScalingBeforeSpecValue bool = false
	var issetScalingAfterSpecValue bool = false
	var issetEventReason bool = false
	var issetIsPreCheck bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerDateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerMetricValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerConfigValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetScalingBeforeSpecValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetScalingAfterSpecValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventReason = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsPreCheck = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskAction {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTaskState {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTriggerDateTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTriggerMetricValue {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTriggerConfigValue {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetScalingBeforeSpecValue {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetScalingAfterSpecValue {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetEventReason {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetIsPreCheck {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AutoScalingTask[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AutoScalingTask[fieldId]))
}

func (p *AutoScalingTask) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskAction = _field
	return nil
}
func (p *AutoScalingTask) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *AutoScalingTask) ReadField3(iprot thrift.TProtocol) error {

	var _field TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TaskStatus(v)
	}
	p.TaskState = _field
	return nil
}
func (p *AutoScalingTask) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TriggerDateTime = _field
	return nil
}
func (p *AutoScalingTask) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TriggerMetricValue = _field
	return nil
}
func (p *AutoScalingTask) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TriggerConfigValue = _field
	return nil
}
func (p *AutoScalingTask) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ScalingBeforeSpecValue = _field
	return nil
}
func (p *AutoScalingTask) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ScalingAfterSpecValue = _field
	return nil
}
func (p *AutoScalingTask) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventReason = _field
	return nil
}
func (p *AutoScalingTask) ReadField11(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsPreCheck = _field
	return nil
}
func (p *AutoScalingTask) ReadField12(iprot thrift.TProtocol) error {

	var _field *TriggerEventType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TriggerEventType(v)
		_field = &tmp
	}
	p.TriggerEventType = _field
	return nil
}

func (p *AutoScalingTask) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AutoScalingTask")

	var fieldId int16
	if err = oprot.WriteStructBegin("AutoScalingTask"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AutoScalingTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskAction", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AutoScalingTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AutoScalingTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskState", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskState)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AutoScalingTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerDateTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TriggerDateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AutoScalingTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerMetricValue", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TriggerMetricValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AutoScalingTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerConfigValue", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TriggerConfigValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AutoScalingTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScalingBeforeSpecValue", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ScalingBeforeSpecValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AutoScalingTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScalingAfterSpecValue", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ScalingAfterSpecValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *AutoScalingTask) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventReason", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventReason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *AutoScalingTask) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsPreCheck", thrift.BOOL, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsPreCheck); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *AutoScalingTask) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerEventType() {
		if err = oprot.WriteFieldBegin("TriggerEventType", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TriggerEventType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *AutoScalingTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AutoScalingTask(%+v)", *p)

}

func (p *AutoScalingTask) DeepEqual(ano *AutoScalingTask) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskAction) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskState) {
		return false
	}
	if !p.Field4DeepEqual(ano.TriggerDateTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.TriggerMetricValue) {
		return false
	}
	if !p.Field7DeepEqual(ano.TriggerConfigValue) {
		return false
	}
	if !p.Field8DeepEqual(ano.ScalingBeforeSpecValue) {
		return false
	}
	if !p.Field9DeepEqual(ano.ScalingAfterSpecValue) {
		return false
	}
	if !p.Field10DeepEqual(ano.EventReason) {
		return false
	}
	if !p.Field11DeepEqual(ano.IsPreCheck) {
		return false
	}
	if !p.Field12DeepEqual(ano.TriggerEventType) {
		return false
	}
	return true
}

func (p *AutoScalingTask) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskAction, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field3DeepEqual(src TaskStatus) bool {

	if p.TaskState != src {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TriggerDateTime, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field6DeepEqual(src string) bool {

	if strings.Compare(p.TriggerMetricValue, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field7DeepEqual(src string) bool {

	if strings.Compare(p.TriggerConfigValue, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field8DeepEqual(src string) bool {

	if strings.Compare(p.ScalingBeforeSpecValue, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field9DeepEqual(src string) bool {

	if strings.Compare(p.ScalingAfterSpecValue, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field10DeepEqual(src string) bool {

	if strings.Compare(p.EventReason, src) != 0 {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field11DeepEqual(src bool) bool {

	if p.IsPreCheck != src {
		return false
	}
	return true
}
func (p *AutoScalingTask) Field12DeepEqual(src *TriggerEventType) bool {

	if p.TriggerEventType == src {
		return true
	} else if p.TriggerEventType == nil || src == nil {
		return false
	}
	if *p.TriggerEventType != *src {
		return false
	}
	return true
}

type ModifyDBLocalSpecManuallyReq struct {
	InstanceId              string               `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType            InstanceType         `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	NodeInfos               []*LocalSpecNodeInfo `thrift:"NodeInfos,3,required" frugal:"3,required,list<LocalSpecNodeInfo>" json:"NodeInfos"`
	ManualEffectiveTimeHour *int32               `thrift:"ManualEffectiveTimeHour,4,optional" frugal:"4,optional,i32" json:"ManualEffectiveTimeHour,omitempty"`
}

func NewModifyDBLocalSpecManuallyReq() *ModifyDBLocalSpecManuallyReq {
	return &ModifyDBLocalSpecManuallyReq{}
}

func (p *ModifyDBLocalSpecManuallyReq) InitDefault() {
}

func (p *ModifyDBLocalSpecManuallyReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyDBLocalSpecManuallyReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ModifyDBLocalSpecManuallyReq) GetNodeInfos() (v []*LocalSpecNodeInfo) {
	return p.NodeInfos
}

var ModifyDBLocalSpecManuallyReq_ManualEffectiveTimeHour_DEFAULT int32

func (p *ModifyDBLocalSpecManuallyReq) GetManualEffectiveTimeHour() (v int32) {
	if !p.IsSetManualEffectiveTimeHour() {
		return ModifyDBLocalSpecManuallyReq_ManualEffectiveTimeHour_DEFAULT
	}
	return *p.ManualEffectiveTimeHour
}
func (p *ModifyDBLocalSpecManuallyReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyDBLocalSpecManuallyReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyDBLocalSpecManuallyReq) SetNodeInfos(val []*LocalSpecNodeInfo) {
	p.NodeInfos = val
}
func (p *ModifyDBLocalSpecManuallyReq) SetManualEffectiveTimeHour(val *int32) {
	p.ManualEffectiveTimeHour = val
}

var fieldIDToName_ModifyDBLocalSpecManuallyReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "NodeInfos",
	4: "ManualEffectiveTimeHour",
}

func (p *ModifyDBLocalSpecManuallyReq) IsSetManualEffectiveTimeHour() bool {
	return p.ManualEffectiveTimeHour != nil
}

func (p *ModifyDBLocalSpecManuallyReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBLocalSpecManuallyReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetNodeInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeInfos {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyDBLocalSpecManuallyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyDBLocalSpecManuallyReq[fieldId]))
}

func (p *ModifyDBLocalSpecManuallyReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyDBLocalSpecManuallyReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyDBLocalSpecManuallyReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LocalSpecNodeInfo, 0, size)
	values := make([]LocalSpecNodeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeInfos = _field
	return nil
}
func (p *ModifyDBLocalSpecManuallyReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ManualEffectiveTimeHour = _field
	return nil
}

func (p *ModifyDBLocalSpecManuallyReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBLocalSpecManuallyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyDBLocalSpecManuallyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeInfos", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.NodeInfos)); err != nil {
		return err
	}
	for _, v := range p.NodeInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetManualEffectiveTimeHour() {
		if err = oprot.WriteFieldBegin("ManualEffectiveTimeHour", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ManualEffectiveTimeHour); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBLocalSpecManuallyReq(%+v)", *p)

}

func (p *ModifyDBLocalSpecManuallyReq) DeepEqual(ano *ModifyDBLocalSpecManuallyReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeInfos) {
		return false
	}
	if !p.Field4DeepEqual(ano.ManualEffectiveTimeHour) {
		return false
	}
	return true
}

func (p *ModifyDBLocalSpecManuallyReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyDBLocalSpecManuallyReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyDBLocalSpecManuallyReq) Field3DeepEqual(src []*LocalSpecNodeInfo) bool {

	if len(p.NodeInfos) != len(src) {
		return false
	}
	for i, v := range p.NodeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyDBLocalSpecManuallyReq) Field4DeepEqual(src *int32) bool {

	if p.ManualEffectiveTimeHour == src {
		return true
	} else if p.ManualEffectiveTimeHour == nil || src == nil {
		return false
	}
	if *p.ManualEffectiveTimeHour != *src {
		return false
	}
	return true
}

type ModifyDBLocalSpecManuallyResp struct {
}

func NewModifyDBLocalSpecManuallyResp() *ModifyDBLocalSpecManuallyResp {
	return &ModifyDBLocalSpecManuallyResp{}
}

func (p *ModifyDBLocalSpecManuallyResp) InitDefault() {
}

var fieldIDToName_ModifyDBLocalSpecManuallyResp = map[int16]string{}

func (p *ModifyDBLocalSpecManuallyResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBLocalSpecManuallyResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyDBLocalSpecManuallyResp")

	if err = oprot.WriteStructBegin("ModifyDBLocalSpecManuallyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyDBLocalSpecManuallyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyDBLocalSpecManuallyResp(%+v)", *p)

}

func (p *ModifyDBLocalSpecManuallyResp) DeepEqual(ano *ModifyDBLocalSpecManuallyResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

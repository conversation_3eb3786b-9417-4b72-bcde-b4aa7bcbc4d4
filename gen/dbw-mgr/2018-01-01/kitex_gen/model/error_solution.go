// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

var (
	ErrorSolutionMap = map[ErrorSolution]*ErrorSolutionDetail{
		ErrorSolution_Contact_Oncall: &ErrorSolutionDetail{
			DescEn: "Please contact Oncall",
			DescCN: "请联系Oncall",
		},
	}
)

type ErrorSolution int64

const (
	ErrorSolution_None           ErrorSolution = 0
	ErrorSolution_Contact_Oncall ErrorSolution = 1
)

func (p ErrorSolution) String() string {
	switch p {
	case ErrorSolution_None:
		return "None"
	case ErrorSolution_Contact_Oncall:
		return "Contact_Oncall"
	}
	return "<UNSET>"
}

func ErrorSolutionFromString(s string) (ErrorSolution, error) {
	switch s {
	case "None":
		return ErrorSolution_None, nil
	case "Contact_Oncall":
		return ErrorSolution_Contact_Oncall, nil
	}
	return ErrorSolution(0), fmt.Errorf("not a valid ErrorSolution string")
}

func ErrorSolutionPtr(v ErrorSolution) *ErrorSolution { return &v }

func (p ErrorSolution) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrorSolution) UnmarshalText(text []byte) error {
	q, err := ErrorSolutionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrorSolutionDetail struct {
	DescCN string `thrift:"DescCN,2,required" frugal:"2,required,string" json:"DescCN"`
	DescEn string `thrift:"DescEn,3,required" frugal:"3,required,string" json:"DescEn"`
}

func NewErrorSolutionDetail() *ErrorSolutionDetail {
	return &ErrorSolutionDetail{}
}

func (p *ErrorSolutionDetail) InitDefault() {
}

func (p *ErrorSolutionDetail) GetDescCN() (v string) {
	return p.DescCN
}

func (p *ErrorSolutionDetail) GetDescEn() (v string) {
	return p.DescEn
}
func (p *ErrorSolutionDetail) SetDescCN(val string) {
	p.DescCN = val
}
func (p *ErrorSolutionDetail) SetDescEn(val string) {
	p.DescEn = val
}

var fieldIDToName_ErrorSolutionDetail = map[int16]string{
	2: "DescCN",
	3: "DescEn",
}

func (p *ErrorSolutionDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorSolutionDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDescCN bool = false
	var issetDescEn bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescCN = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescEn = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDescCN {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescEn {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrorSolutionDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ErrorSolutionDetail[fieldId]))
}

func (p *ErrorSolutionDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescCN = _field
	return nil
}
func (p *ErrorSolutionDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescEn = _field
	return nil
}

func (p *ErrorSolutionDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorSolutionDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrorSolutionDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrorSolutionDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DescCN", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescCN); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrorSolutionDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DescEn", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescEn); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrorSolutionDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrorSolutionDetail(%+v)", *p)

}

func (p *ErrorSolutionDetail) DeepEqual(ano *ErrorSolutionDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field2DeepEqual(ano.DescCN) {
		return false
	}
	if !p.Field3DeepEqual(ano.DescEn) {
		return false
	}
	return true
}

func (p *ErrorSolutionDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DescCN, src) != 0 {
		return false
	}
	return true
}
func (p *ErrorSolutionDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DescEn, src) != 0 {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ExplainInfo struct {
	ID           int32  `thrift:"ID,1,required" frugal:"1,required,i32" json:"ID"`
	SelectType   string `thrift:"SelectType,2,required" frugal:"2,required,string" json:"SelectType"`
	Table        string `thrift:"Table,3,required" frugal:"3,required,string" json:"Table"`
	Partitions   string `thrift:"Partitions,4,required" frugal:"4,required,string" json:"Partitions"`
	Type         string `thrift:"Type,5,required" frugal:"5,required,string" json:"Type"`
	PossibleKeys string `thrift:"PossibleKeys,6,required" frugal:"6,required,string" json:"PossibleKeys"`
	Key          string `thrift:"Key,7,required" frugal:"7,required,string" json:"Key"`
	KeyLen       string `thrift:"KeyLen,8,required" frugal:"8,required,string" json:"KeyLen"`
	Ref          string `thrift:"Ref,9,required" frugal:"9,required,string" json:"Ref"`
	Rows         string `thrift:"Rows,10,required" frugal:"10,required,string" json:"Rows"`
	Filtered     string `thrift:"Filtered,11,required" frugal:"11,required,string" json:"Filtered"`
	Extra        string `thrift:"Extra,12,required" frugal:"12,required,string" json:"Extra"`
}

func NewExplainInfo() *ExplainInfo {
	return &ExplainInfo{}
}

func (p *ExplainInfo) InitDefault() {
}

func (p *ExplainInfo) GetID() (v int32) {
	return p.ID
}

func (p *ExplainInfo) GetSelectType() (v string) {
	return p.SelectType
}

func (p *ExplainInfo) GetTable() (v string) {
	return p.Table
}

func (p *ExplainInfo) GetPartitions() (v string) {
	return p.Partitions
}

func (p *ExplainInfo) GetType() (v string) {
	return p.Type
}

func (p *ExplainInfo) GetPossibleKeys() (v string) {
	return p.PossibleKeys
}

func (p *ExplainInfo) GetKey() (v string) {
	return p.Key
}

func (p *ExplainInfo) GetKeyLen() (v string) {
	return p.KeyLen
}

func (p *ExplainInfo) GetRef() (v string) {
	return p.Ref
}

func (p *ExplainInfo) GetRows() (v string) {
	return p.Rows
}

func (p *ExplainInfo) GetFiltered() (v string) {
	return p.Filtered
}

func (p *ExplainInfo) GetExtra() (v string) {
	return p.Extra
}
func (p *ExplainInfo) SetID(val int32) {
	p.ID = val
}
func (p *ExplainInfo) SetSelectType(val string) {
	p.SelectType = val
}
func (p *ExplainInfo) SetTable(val string) {
	p.Table = val
}
func (p *ExplainInfo) SetPartitions(val string) {
	p.Partitions = val
}
func (p *ExplainInfo) SetType(val string) {
	p.Type = val
}
func (p *ExplainInfo) SetPossibleKeys(val string) {
	p.PossibleKeys = val
}
func (p *ExplainInfo) SetKey(val string) {
	p.Key = val
}
func (p *ExplainInfo) SetKeyLen(val string) {
	p.KeyLen = val
}
func (p *ExplainInfo) SetRef(val string) {
	p.Ref = val
}
func (p *ExplainInfo) SetRows(val string) {
	p.Rows = val
}
func (p *ExplainInfo) SetFiltered(val string) {
	p.Filtered = val
}
func (p *ExplainInfo) SetExtra(val string) {
	p.Extra = val
}

var fieldIDToName_ExplainInfo = map[int16]string{
	1:  "ID",
	2:  "SelectType",
	3:  "Table",
	4:  "Partitions",
	5:  "Type",
	6:  "PossibleKeys",
	7:  "Key",
	8:  "KeyLen",
	9:  "Ref",
	10: "Rows",
	11: "Filtered",
	12: "Extra",
}

func (p *ExplainInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExplainInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetSelectType bool = false
	var issetTable bool = false
	var issetPartitions bool = false
	var issetType bool = false
	var issetPossibleKeys bool = false
	var issetKey bool = false
	var issetKeyLen bool = false
	var issetRef bool = false
	var issetRows bool = false
	var issetFiltered bool = false
	var issetExtra bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSelectType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPartitions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetPossibleKeys = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyLen = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetRef = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetRows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetFiltered = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetExtra = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSelectType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTable {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPartitions {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetPossibleKeys {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetKey {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetKeyLen {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetRef {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetRows {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetFiltered {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetExtra {
		fieldId = 12
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExplainInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExplainInfo[fieldId]))
}

func (p *ExplainInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *ExplainInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SelectType = _field
	return nil
}
func (p *ExplainInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Table = _field
	return nil
}
func (p *ExplainInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Partitions = _field
	return nil
}
func (p *ExplainInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *ExplainInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PossibleKeys = _field
	return nil
}
func (p *ExplainInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Key = _field
	return nil
}
func (p *ExplainInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyLen = _field
	return nil
}
func (p *ExplainInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Ref = _field
	return nil
}
func (p *ExplainInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Rows = _field
	return nil
}
func (p *ExplainInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Filtered = _field
	return nil
}
func (p *ExplainInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Extra = _field
	return nil
}

func (p *ExplainInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExplainInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExplainInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExplainInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExplainInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SelectType", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SelectType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExplainInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Table", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Table); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExplainInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Partitions", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Partitions); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExplainInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExplainInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PossibleKeys", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PossibleKeys); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ExplainInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Key); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ExplainInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyLen", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyLen); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ExplainInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Ref", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Ref); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ExplainInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Rows", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Rows); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ExplainInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Filtered", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Filtered); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ExplainInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Extra", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Extra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ExplainInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExplainInfo(%+v)", *p)

}

func (p *ExplainInfo) DeepEqual(ano *ExplainInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.SelectType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Table) {
		return false
	}
	if !p.Field4DeepEqual(ano.Partitions) {
		return false
	}
	if !p.Field5DeepEqual(ano.Type) {
		return false
	}
	if !p.Field6DeepEqual(ano.PossibleKeys) {
		return false
	}
	if !p.Field7DeepEqual(ano.Key) {
		return false
	}
	if !p.Field8DeepEqual(ano.KeyLen) {
		return false
	}
	if !p.Field9DeepEqual(ano.Ref) {
		return false
	}
	if !p.Field10DeepEqual(ano.Rows) {
		return false
	}
	if !p.Field11DeepEqual(ano.Filtered) {
		return false
	}
	if !p.Field12DeepEqual(ano.Extra) {
		return false
	}
	return true
}

func (p *ExplainInfo) Field1DeepEqual(src int32) bool {

	if p.ID != src {
		return false
	}
	return true
}
func (p *ExplainInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SelectType, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Table, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Partitions, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.PossibleKeys, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.Key, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.KeyLen, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Ref, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field10DeepEqual(src string) bool {

	if strings.Compare(p.Rows, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field11DeepEqual(src string) bool {

	if strings.Compare(p.Filtered, src) != 0 {
		return false
	}
	return true
}
func (p *ExplainInfo) Field12DeepEqual(src string) bool {

	if strings.Compare(p.Extra, src) != 0 {
		return false
	}
	return true
}

type ExplainTranslationInfo struct {
	SelectTypeTranslations []string `thrift:"SelectTypeTranslations,1,required" frugal:"1,required,list<string>" json:"SelectTypeTranslations"`
	TypeTranslations       []string `thrift:"TypeTranslations,2,required" frugal:"2,required,list<string>" json:"TypeTranslations"`
	ExtraTranslations      []string `thrift:"ExtraTranslations,3,required" frugal:"3,required,list<string>" json:"ExtraTranslations"`
}

func NewExplainTranslationInfo() *ExplainTranslationInfo {
	return &ExplainTranslationInfo{}
}

func (p *ExplainTranslationInfo) InitDefault() {
}

func (p *ExplainTranslationInfo) GetSelectTypeTranslations() (v []string) {
	return p.SelectTypeTranslations
}

func (p *ExplainTranslationInfo) GetTypeTranslations() (v []string) {
	return p.TypeTranslations
}

func (p *ExplainTranslationInfo) GetExtraTranslations() (v []string) {
	return p.ExtraTranslations
}
func (p *ExplainTranslationInfo) SetSelectTypeTranslations(val []string) {
	p.SelectTypeTranslations = val
}
func (p *ExplainTranslationInfo) SetTypeTranslations(val []string) {
	p.TypeTranslations = val
}
func (p *ExplainTranslationInfo) SetExtraTranslations(val []string) {
	p.ExtraTranslations = val
}

var fieldIDToName_ExplainTranslationInfo = map[int16]string{
	1: "SelectTypeTranslations",
	2: "TypeTranslations",
	3: "ExtraTranslations",
}

func (p *ExplainTranslationInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExplainTranslationInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSelectTypeTranslations bool = false
	var issetTypeTranslations bool = false
	var issetExtraTranslations bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSelectTypeTranslations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTypeTranslations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetExtraTranslations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSelectTypeTranslations {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTypeTranslations {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetExtraTranslations {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExplainTranslationInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExplainTranslationInfo[fieldId]))
}

func (p *ExplainTranslationInfo) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SelectTypeTranslations = _field
	return nil
}
func (p *ExplainTranslationInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TypeTranslations = _field
	return nil
}
func (p *ExplainTranslationInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExtraTranslations = _field
	return nil
}

func (p *ExplainTranslationInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExplainTranslationInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExplainTranslationInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExplainTranslationInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SelectTypeTranslations", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SelectTypeTranslations)); err != nil {
		return err
	}
	for _, v := range p.SelectTypeTranslations {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExplainTranslationInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TypeTranslations", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.TypeTranslations)); err != nil {
		return err
	}
	for _, v := range p.TypeTranslations {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExplainTranslationInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExtraTranslations", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ExtraTranslations)); err != nil {
		return err
	}
	for _, v := range p.ExtraTranslations {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExplainTranslationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExplainTranslationInfo(%+v)", *p)

}

func (p *ExplainTranslationInfo) DeepEqual(ano *ExplainTranslationInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SelectTypeTranslations) {
		return false
	}
	if !p.Field2DeepEqual(ano.TypeTranslations) {
		return false
	}
	if !p.Field3DeepEqual(ano.ExtraTranslations) {
		return false
	}
	return true
}

func (p *ExplainTranslationInfo) Field1DeepEqual(src []string) bool {

	if len(p.SelectTypeTranslations) != len(src) {
		return false
	}
	for i, v := range p.SelectTypeTranslations {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ExplainTranslationInfo) Field2DeepEqual(src []string) bool {

	if len(p.TypeTranslations) != len(src) {
		return false
	}
	for i, v := range p.TypeTranslations {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ExplainTranslationInfo) Field3DeepEqual(src []string) bool {

	if len(p.ExtraTranslations) != len(src) {
		return false
	}
	for i, v := range p.ExtraTranslations {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type GetSqlAdviceReq struct {
	RegionId   string   `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId string   `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	DSType     DSType   `thrift:"DSType,3,required" frugal:"3,required,DSType" json:"DSType"`
	LinkType   LinkType `thrift:"LinkType,4,required" frugal:"4,required,LinkType" json:"LinkType"`
	DB         string   `thrift:"DB,5,required" frugal:"5,required,string" json:"DB"`
	Sql        string   `thrift:"Sql,6,required" frugal:"6,required,string" json:"Sql"`
}

func NewGetSqlAdviceReq() *GetSqlAdviceReq {
	return &GetSqlAdviceReq{}
}

func (p *GetSqlAdviceReq) InitDefault() {
}

func (p *GetSqlAdviceReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *GetSqlAdviceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *GetSqlAdviceReq) GetDSType() (v DSType) {
	return p.DSType
}

func (p *GetSqlAdviceReq) GetLinkType() (v LinkType) {
	return p.LinkType
}

func (p *GetSqlAdviceReq) GetDB() (v string) {
	return p.DB
}

func (p *GetSqlAdviceReq) GetSql() (v string) {
	return p.Sql
}
func (p *GetSqlAdviceReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *GetSqlAdviceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *GetSqlAdviceReq) SetDSType(val DSType) {
	p.DSType = val
}
func (p *GetSqlAdviceReq) SetLinkType(val LinkType) {
	p.LinkType = val
}
func (p *GetSqlAdviceReq) SetDB(val string) {
	p.DB = val
}
func (p *GetSqlAdviceReq) SetSql(val string) {
	p.Sql = val
}

var fieldIDToName_GetSqlAdviceReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "DSType",
	4: "LinkType",
	5: "DB",
	6: "Sql",
}

func (p *GetSqlAdviceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAdviceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetDSType bool = false
	var issetLinkType bool = false
	var issetDB bool = false
	var issetSql bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDSType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLinkType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetSql = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDSType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLinkType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSql {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlAdviceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSqlAdviceReq[fieldId]))
}

func (p *GetSqlAdviceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *GetSqlAdviceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *GetSqlAdviceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DSType(v)
	}
	p.DSType = _field
	return nil
}
func (p *GetSqlAdviceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LinkType(v)
	}
	p.LinkType = _field
	return nil
}
func (p *GetSqlAdviceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *GetSqlAdviceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Sql = _field
	return nil
}

func (p *GetSqlAdviceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAdviceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSqlAdviceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DSType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DSType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LinkType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *GetSqlAdviceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Sql", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Sql); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GetSqlAdviceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlAdviceReq(%+v)", *p)

}

func (p *GetSqlAdviceReq) DeepEqual(ano *GetSqlAdviceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DSType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.DB) {
		return false
	}
	if !p.Field6DeepEqual(ano.Sql) {
		return false
	}
	return true
}

func (p *GetSqlAdviceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *GetSqlAdviceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *GetSqlAdviceReq) Field3DeepEqual(src DSType) bool {

	if p.DSType != src {
		return false
	}
	return true
}
func (p *GetSqlAdviceReq) Field4DeepEqual(src LinkType) bool {

	if p.LinkType != src {
		return false
	}
	return true
}
func (p *GetSqlAdviceReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *GetSqlAdviceReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Sql, src) != 0 {
		return false
	}
	return true
}

type GetSqlAdviceResp struct {
	Suggestions        []string                `thrift:"Suggestions,1,required" frugal:"1,required,list<string>" json:"Suggestions"`
	Explains           []*ExplainInfo          `thrift:"Explains,2,required" frugal:"2,required,list<ExplainInfo>" json:"Explains"`
	ExplainTranslation *ExplainTranslationInfo `thrift:"ExplainTranslation,3,required" frugal:"3,required,ExplainTranslationInfo" json:"ExplainTranslation"`
}

func NewGetSqlAdviceResp() *GetSqlAdviceResp {
	return &GetSqlAdviceResp{}
}

func (p *GetSqlAdviceResp) InitDefault() {
}

func (p *GetSqlAdviceResp) GetSuggestions() (v []string) {
	return p.Suggestions
}

func (p *GetSqlAdviceResp) GetExplains() (v []*ExplainInfo) {
	return p.Explains
}

var GetSqlAdviceResp_ExplainTranslation_DEFAULT *ExplainTranslationInfo

func (p *GetSqlAdviceResp) GetExplainTranslation() (v *ExplainTranslationInfo) {
	if !p.IsSetExplainTranslation() {
		return GetSqlAdviceResp_ExplainTranslation_DEFAULT
	}
	return p.ExplainTranslation
}
func (p *GetSqlAdviceResp) SetSuggestions(val []string) {
	p.Suggestions = val
}
func (p *GetSqlAdviceResp) SetExplains(val []*ExplainInfo) {
	p.Explains = val
}
func (p *GetSqlAdviceResp) SetExplainTranslation(val *ExplainTranslationInfo) {
	p.ExplainTranslation = val
}

var fieldIDToName_GetSqlAdviceResp = map[int16]string{
	1: "Suggestions",
	2: "Explains",
	3: "ExplainTranslation",
}

func (p *GetSqlAdviceResp) IsSetExplainTranslation() bool {
	return p.ExplainTranslation != nil
}

func (p *GetSqlAdviceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAdviceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuggestions bool = false
	var issetExplains bool = false
	var issetExplainTranslation bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuggestions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetExplains = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetExplainTranslation = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuggestions {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetExplains {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetExplainTranslation {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlAdviceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSqlAdviceResp[fieldId]))
}

func (p *GetSqlAdviceResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Suggestions = _field
	return nil
}
func (p *GetSqlAdviceResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExplainInfo, 0, size)
	values := make([]ExplainInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Explains = _field
	return nil
}
func (p *GetSqlAdviceResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewExplainTranslationInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExplainTranslation = _field
	return nil
}

func (p *GetSqlAdviceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAdviceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSqlAdviceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlAdviceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Suggestions", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Suggestions)); err != nil {
		return err
	}
	for _, v := range p.Suggestions {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSqlAdviceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Explains", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Explains)); err != nil {
		return err
	}
	for _, v := range p.Explains {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetSqlAdviceResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExplainTranslation", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExplainTranslation.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetSqlAdviceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlAdviceResp(%+v)", *p)

}

func (p *GetSqlAdviceResp) DeepEqual(ano *GetSqlAdviceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Suggestions) {
		return false
	}
	if !p.Field2DeepEqual(ano.Explains) {
		return false
	}
	if !p.Field3DeepEqual(ano.ExplainTranslation) {
		return false
	}
	return true
}

func (p *GetSqlAdviceResp) Field1DeepEqual(src []string) bool {

	if len(p.Suggestions) != len(src) {
		return false
	}
	for i, v := range p.Suggestions {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *GetSqlAdviceResp) Field2DeepEqual(src []*ExplainInfo) bool {

	if len(p.Explains) != len(src) {
		return false
	}
	for i, v := range p.Explains {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetSqlAdviceResp) Field3DeepEqual(src *ExplainTranslationInfo) bool {

	if !p.ExplainTranslation.DeepEqual(src) {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeMongoDBsReq struct {
	SessionId  string  `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	PageNumber *int32  `thrift:"PageNumber,2,optional" frugal:"2,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32  `thrift:"PageSize,3,optional" frugal:"3,optional,i32" json:"PageSize,omitempty"`
	Query      *string `thrift:"Query,4,optional" frugal:"4,optional,string" json:"Query,omitempty"`
}

func NewDescribeMongoDBsReq() *DescribeMongoDBsReq {
	return &DescribeMongoDBsReq{}
}

func (p *DescribeMongoDBsReq) InitDefault() {
}

func (p *DescribeMongoDBsReq) GetSessionId() (v string) {
	return p.SessionId
}

var DescribeMongoDBsReq_PageNumber_DEFAULT int32

func (p *DescribeMongoDBsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeMongoDBsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeMongoDBsReq_PageSize_DEFAULT int32

func (p *DescribeMongoDBsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeMongoDBsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeMongoDBsReq_Query_DEFAULT string

func (p *DescribeMongoDBsReq) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return DescribeMongoDBsReq_Query_DEFAULT
	}
	return *p.Query
}
func (p *DescribeMongoDBsReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DescribeMongoDBsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeMongoDBsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeMongoDBsReq) SetQuery(val *string) {
	p.Query = val
}

var fieldIDToName_DescribeMongoDBsReq = map[int16]string{
	1: "SessionId",
	2: "PageNumber",
	3: "PageSize",
	4: "Query",
}

func (p *DescribeMongoDBsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeMongoDBsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeMongoDBsReq) IsSetQuery() bool {
	return p.Query != nil
}

func (p *DescribeMongoDBsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMongoDBsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMongoDBsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMongoDBsReq[fieldId]))
}

func (p *DescribeMongoDBsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DescribeMongoDBsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeMongoDBsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeMongoDBsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Query = _field
	return nil
}

func (p *DescribeMongoDBsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMongoDBsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMongoDBsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMongoDBsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMongoDBsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeMongoDBsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeMongoDBsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetQuery() {
		if err = oprot.WriteFieldBegin("Query", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Query); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeMongoDBsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMongoDBsReq(%+v)", *p)

}

func (p *DescribeMongoDBsReq) DeepEqual(ano *DescribeMongoDBsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.Query) {
		return false
	}
	return true
}

func (p *DescribeMongoDBsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeMongoDBsReq) Field2DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeMongoDBsReq) Field3DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeMongoDBsReq) Field4DeepEqual(src *string) bool {

	if p.Query == src {
		return true
	} else if p.Query == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Query, *src) != 0 {
		return false
	}
	return true
}

type DescribeMongoDBsResp struct {
	Total           int32          `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	MongoDBInfoList []*MongoDBInfo `thrift:"MongoDBInfoList,2,required" frugal:"2,required,list<MongoDBInfo>" json:"MongoDBInfoList"`
}

func NewDescribeMongoDBsResp() *DescribeMongoDBsResp {
	return &DescribeMongoDBsResp{}
}

func (p *DescribeMongoDBsResp) InitDefault() {
}

func (p *DescribeMongoDBsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeMongoDBsResp) GetMongoDBInfoList() (v []*MongoDBInfo) {
	return p.MongoDBInfoList
}
func (p *DescribeMongoDBsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeMongoDBsResp) SetMongoDBInfoList(val []*MongoDBInfo) {
	p.MongoDBInfoList = val
}

var fieldIDToName_DescribeMongoDBsResp = map[int16]string{
	1: "Total",
	2: "MongoDBInfoList",
}

func (p *DescribeMongoDBsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMongoDBsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetMongoDBInfoList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMongoDBInfoList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMongoDBInfoList {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeMongoDBsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeMongoDBsResp[fieldId]))
}

func (p *DescribeMongoDBsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeMongoDBsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MongoDBInfo, 0, size)
	values := make([]MongoDBInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MongoDBInfoList = _field
	return nil
}

func (p *DescribeMongoDBsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeMongoDBsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeMongoDBsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeMongoDBsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeMongoDBsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MongoDBInfoList", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MongoDBInfoList)); err != nil {
		return err
	}
	for _, v := range p.MongoDBInfoList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeMongoDBsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeMongoDBsResp(%+v)", *p)

}

func (p *DescribeMongoDBsResp) DeepEqual(ano *DescribeMongoDBsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.MongoDBInfoList) {
		return false
	}
	return true
}

func (p *DescribeMongoDBsResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeMongoDBsResp) Field2DeepEqual(src []*MongoDBInfo) bool {

	if len(p.MongoDBInfoList) != len(src) {
		return false
	}
	for i, v := range p.MongoDBInfoList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type MongoDBInfo struct {
	Name       string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	DbSize     string `thrift:"DbSize,2,required" frugal:"2,required,string" json:"DbSize"`
	IsSystemDB bool   `thrift:"IsSystemDB,3,required" frugal:"3,required,bool" json:"IsSystemDB"`
}

func NewMongoDBInfo() *MongoDBInfo {
	return &MongoDBInfo{}
}

func (p *MongoDBInfo) InitDefault() {
}

func (p *MongoDBInfo) GetName() (v string) {
	return p.Name
}

func (p *MongoDBInfo) GetDbSize() (v string) {
	return p.DbSize
}

func (p *MongoDBInfo) GetIsSystemDB() (v bool) {
	return p.IsSystemDB
}
func (p *MongoDBInfo) SetName(val string) {
	p.Name = val
}
func (p *MongoDBInfo) SetDbSize(val string) {
	p.DbSize = val
}
func (p *MongoDBInfo) SetIsSystemDB(val bool) {
	p.IsSystemDB = val
}

var fieldIDToName_MongoDBInfo = map[int16]string{
	1: "Name",
	2: "DbSize",
	3: "IsSystemDB",
}

func (p *MongoDBInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MongoDBInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetDbSize bool = false
	var issetIsSystemDB bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsSystemDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDbSize {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIsSystemDB {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MongoDBInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MongoDBInfo[fieldId]))
}

func (p *MongoDBInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *MongoDBInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbSize = _field
	return nil
}
func (p *MongoDBInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsSystemDB = _field
	return nil
}

func (p *MongoDBInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MongoDBInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("MongoDBInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MongoDBInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MongoDBInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbSize", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MongoDBInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsSystemDB", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsSystemDB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *MongoDBInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MongoDBInfo(%+v)", *p)

}

func (p *MongoDBInfo) DeepEqual(ano *MongoDBInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.DbSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsSystemDB) {
		return false
	}
	return true
}

func (p *MongoDBInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *MongoDBInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DbSize, src) != 0 {
		return false
	}
	return true
}
func (p *MongoDBInfo) Field3DeepEqual(src bool) bool {

	if p.IsSystemDB != src {
		return false
	}
	return true
}

type DescribeCollectionsReq struct {
	SessionId  string  `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB         string  `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	PageNumber *int32  `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32  `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	Query      *string `thrift:"Query,5,optional" frugal:"5,optional,string" json:"Query,omitempty"`
}

func NewDescribeCollectionsReq() *DescribeCollectionsReq {
	return &DescribeCollectionsReq{}
}

func (p *DescribeCollectionsReq) InitDefault() {
}

func (p *DescribeCollectionsReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DescribeCollectionsReq) GetDB() (v string) {
	return p.DB
}

var DescribeCollectionsReq_PageNumber_DEFAULT int32

func (p *DescribeCollectionsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeCollectionsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeCollectionsReq_PageSize_DEFAULT int32

func (p *DescribeCollectionsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeCollectionsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeCollectionsReq_Query_DEFAULT string

func (p *DescribeCollectionsReq) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return DescribeCollectionsReq_Query_DEFAULT
	}
	return *p.Query
}
func (p *DescribeCollectionsReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DescribeCollectionsReq) SetDB(val string) {
	p.DB = val
}
func (p *DescribeCollectionsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeCollectionsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeCollectionsReq) SetQuery(val *string) {
	p.Query = val
}

var fieldIDToName_DescribeCollectionsReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "PageNumber",
	4: "PageSize",
	5: "Query",
}

func (p *DescribeCollectionsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeCollectionsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeCollectionsReq) IsSetQuery() bool {
	return p.Query != nil
}

func (p *DescribeCollectionsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCollectionsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCollectionsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCollectionsReq[fieldId]))
}

func (p *DescribeCollectionsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DescribeCollectionsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DescribeCollectionsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeCollectionsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeCollectionsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Query = _field
	return nil
}

func (p *DescribeCollectionsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCollectionsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCollectionsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCollectionsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCollectionsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCollectionsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeCollectionsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeCollectionsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetQuery() {
		if err = oprot.WriteFieldBegin("Query", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Query); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeCollectionsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCollectionsReq(%+v)", *p)

}

func (p *DescribeCollectionsReq) DeepEqual(ano *DescribeCollectionsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.Query) {
		return false
	}
	return true
}

func (p *DescribeCollectionsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCollectionsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeCollectionsReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeCollectionsReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeCollectionsReq) Field5DeepEqual(src *string) bool {

	if p.Query == src {
		return true
	} else if p.Query == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Query, *src) != 0 {
		return false
	}
	return true
}

type DescribeCollectionsResp struct {
	Items []string `thrift:"Items,1,required" frugal:"1,required,list<string>" json:"Items"`
	Total int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeCollectionsResp() *DescribeCollectionsResp {
	return &DescribeCollectionsResp{}
}

func (p *DescribeCollectionsResp) InitDefault() {
}

func (p *DescribeCollectionsResp) GetItems() (v []string) {
	return p.Items
}

func (p *DescribeCollectionsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeCollectionsResp) SetItems(val []string) {
	p.Items = val
}
func (p *DescribeCollectionsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeCollectionsResp = map[int16]string{
	1: "Items",
	2: "Total",
}

func (p *DescribeCollectionsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCollectionsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItems bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeCollectionsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeCollectionsResp[fieldId]))
}

func (p *DescribeCollectionsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}
func (p *DescribeCollectionsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeCollectionsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeCollectionsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeCollectionsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeCollectionsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Items", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Items)); err != nil {
		return err
	}
	for _, v := range p.Items {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeCollectionsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeCollectionsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeCollectionsResp(%+v)", *p)

}

func (p *DescribeCollectionsResp) DeepEqual(ano *DescribeCollectionsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Items) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeCollectionsResp) Field1DeepEqual(src []string) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeCollectionsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeIndexsReq struct {
	SessionId  string  `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB         string  `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	Col        string  `thrift:"Col,3,required" frugal:"3,required,string" json:"Col"`
	PageNumber *int32  `thrift:"PageNumber,4,optional" frugal:"4,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32  `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
	Query      *string `thrift:"Query,6,optional" frugal:"6,optional,string" json:"Query,omitempty"`
	Idx        *string `thrift:"Idx,7,optional" frugal:"7,optional,string" json:"Idx,omitempty"`
}

func NewDescribeIndexsReq() *DescribeIndexsReq {
	return &DescribeIndexsReq{}
}

func (p *DescribeIndexsReq) InitDefault() {
}

func (p *DescribeIndexsReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DescribeIndexsReq) GetDB() (v string) {
	return p.DB
}

func (p *DescribeIndexsReq) GetCol() (v string) {
	return p.Col
}

var DescribeIndexsReq_PageNumber_DEFAULT int32

func (p *DescribeIndexsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeIndexsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeIndexsReq_PageSize_DEFAULT int32

func (p *DescribeIndexsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeIndexsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeIndexsReq_Query_DEFAULT string

func (p *DescribeIndexsReq) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return DescribeIndexsReq_Query_DEFAULT
	}
	return *p.Query
}

var DescribeIndexsReq_Idx_DEFAULT string

func (p *DescribeIndexsReq) GetIdx() (v string) {
	if !p.IsSetIdx() {
		return DescribeIndexsReq_Idx_DEFAULT
	}
	return *p.Idx
}
func (p *DescribeIndexsReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DescribeIndexsReq) SetDB(val string) {
	p.DB = val
}
func (p *DescribeIndexsReq) SetCol(val string) {
	p.Col = val
}
func (p *DescribeIndexsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeIndexsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeIndexsReq) SetQuery(val *string) {
	p.Query = val
}
func (p *DescribeIndexsReq) SetIdx(val *string) {
	p.Idx = val
}

var fieldIDToName_DescribeIndexsReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "Col",
	4: "PageNumber",
	5: "PageSize",
	6: "Query",
	7: "Idx",
}

func (p *DescribeIndexsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeIndexsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeIndexsReq) IsSetQuery() bool {
	return p.Query != nil
}

func (p *DescribeIndexsReq) IsSetIdx() bool {
	return p.Idx != nil
}

func (p *DescribeIndexsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false
	var issetCol bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCol = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCol {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeIndexsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeIndexsReq[fieldId]))
}

func (p *DescribeIndexsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Col = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Query = _field
	return nil
}
func (p *DescribeIndexsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Idx = _field
	return nil
}

func (p *DescribeIndexsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeIndexsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Col", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Col); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetQuery() {
		if err = oprot.WriteFieldBegin("Query", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Query); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeIndexsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdx() {
		if err = oprot.WriteFieldBegin("Idx", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Idx); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeIndexsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeIndexsReq(%+v)", *p)

}

func (p *DescribeIndexsReq) DeepEqual(ano *DescribeIndexsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.Col) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.Query) {
		return false
	}
	if !p.Field7DeepEqual(ano.Idx) {
		return false
	}
	return true
}

func (p *DescribeIndexsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Col, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field4DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field6DeepEqual(src *string) bool {

	if p.Query == src {
		return true
	} else if p.Query == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Query, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexsReq) Field7DeepEqual(src *string) bool {

	if p.Idx == src {
		return true
	} else if p.Idx == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Idx, *src) != 0 {
		return false
	}
	return true
}

type IndexKey struct {
	Name      string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	ValueType string `thrift:"ValueType,2,required" frugal:"2,required,string" json:"ValueType"`
	Value     string `thrift:"Value,3,required" frugal:"3,required,string" json:"Value"`
}

func NewIndexKey() *IndexKey {
	return &IndexKey{}
}

func (p *IndexKey) InitDefault() {
}

func (p *IndexKey) GetName() (v string) {
	return p.Name
}

func (p *IndexKey) GetValueType() (v string) {
	return p.ValueType
}

func (p *IndexKey) GetValue() (v string) {
	return p.Value
}
func (p *IndexKey) SetName(val string) {
	p.Name = val
}
func (p *IndexKey) SetValueType(val string) {
	p.ValueType = val
}
func (p *IndexKey) SetValue(val string) {
	p.Value = val
}

var fieldIDToName_IndexKey = map[int16]string{
	1: "Name",
	2: "ValueType",
	3: "Value",
}

func (p *IndexKey) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexKey")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetValueType bool = false
	var issetValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValueType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValueType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndexKey[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_IndexKey[fieldId]))
}

func (p *IndexKey) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *IndexKey) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValueType = _field
	return nil
}
func (p *IndexKey) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *IndexKey) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexKey")

	var fieldId int16
	if err = oprot.WriteStructBegin("IndexKey"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IndexKey) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *IndexKey) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueType", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ValueType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *IndexKey) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IndexKey) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexKey(%+v)", *p)

}

func (p *IndexKey) DeepEqual(ano *IndexKey) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.ValueType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *IndexKey) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *IndexKey) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ValueType, src) != 0 {
		return false
	}
	return true
}
func (p *IndexKey) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}

type MongoIndexInfo struct {
	Name               string      `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Ns                 string      `thrift:"Ns,2,required" frugal:"2,required,string" json:"Ns"`
	Background         bool        `thrift:"Background,3,required" frugal:"3,required,bool" json:"Background"`
	Unique             bool        `thrift:"Unique,4,required" frugal:"4,required,bool" json:"Unique"`
	Sparse             bool        `thrift:"Sparse,5,required" frugal:"5,required,bool" json:"Sparse"`
	ExpireAfterSeconds int32       `thrift:"ExpireAfterSeconds,6,required" frugal:"6,required,i32" json:"ExpireAfterSeconds"`
	Key                []*IndexKey `thrift:"Key,7,required" frugal:"7,required,list<IndexKey>" json:"Key"`
}

func NewMongoIndexInfo() *MongoIndexInfo {
	return &MongoIndexInfo{}
}

func (p *MongoIndexInfo) InitDefault() {
}

func (p *MongoIndexInfo) GetName() (v string) {
	return p.Name
}

func (p *MongoIndexInfo) GetNs() (v string) {
	return p.Ns
}

func (p *MongoIndexInfo) GetBackground() (v bool) {
	return p.Background
}

func (p *MongoIndexInfo) GetUnique() (v bool) {
	return p.Unique
}

func (p *MongoIndexInfo) GetSparse() (v bool) {
	return p.Sparse
}

func (p *MongoIndexInfo) GetExpireAfterSeconds() (v int32) {
	return p.ExpireAfterSeconds
}

func (p *MongoIndexInfo) GetKey() (v []*IndexKey) {
	return p.Key
}
func (p *MongoIndexInfo) SetName(val string) {
	p.Name = val
}
func (p *MongoIndexInfo) SetNs(val string) {
	p.Ns = val
}
func (p *MongoIndexInfo) SetBackground(val bool) {
	p.Background = val
}
func (p *MongoIndexInfo) SetUnique(val bool) {
	p.Unique = val
}
func (p *MongoIndexInfo) SetSparse(val bool) {
	p.Sparse = val
}
func (p *MongoIndexInfo) SetExpireAfterSeconds(val int32) {
	p.ExpireAfterSeconds = val
}
func (p *MongoIndexInfo) SetKey(val []*IndexKey) {
	p.Key = val
}

var fieldIDToName_MongoIndexInfo = map[int16]string{
	1: "Name",
	2: "Ns",
	3: "Background",
	4: "Unique",
	5: "Sparse",
	6: "ExpireAfterSeconds",
	7: "Key",
}

func (p *MongoIndexInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MongoIndexInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetNs bool = false
	var issetBackground bool = false
	var issetUnique bool = false
	var issetSparse bool = false
	var issetExpireAfterSeconds bool = false
	var issetKey bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBackground = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnique = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSparse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpireAfterSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBackground {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUnique {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSparse {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetExpireAfterSeconds {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetKey {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MongoIndexInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MongoIndexInfo[fieldId]))
}

func (p *MongoIndexInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *MongoIndexInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Ns = _field
	return nil
}
func (p *MongoIndexInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Background = _field
	return nil
}
func (p *MongoIndexInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unique = _field
	return nil
}
func (p *MongoIndexInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Sparse = _field
	return nil
}
func (p *MongoIndexInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpireAfterSeconds = _field
	return nil
}
func (p *MongoIndexInfo) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IndexKey, 0, size)
	values := make([]IndexKey, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Key = _field
	return nil
}

func (p *MongoIndexInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MongoIndexInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("MongoIndexInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MongoIndexInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Ns", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Ns); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Background", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Background); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unique", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Unique); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Sparse", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Sparse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpireAfterSeconds", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExpireAfterSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *MongoIndexInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Key)); err != nil {
		return err
	}
	for _, v := range p.Key {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *MongoIndexInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MongoIndexInfo(%+v)", *p)

}

func (p *MongoIndexInfo) DeepEqual(ano *MongoIndexInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Ns) {
		return false
	}
	if !p.Field3DeepEqual(ano.Background) {
		return false
	}
	if !p.Field4DeepEqual(ano.Unique) {
		return false
	}
	if !p.Field5DeepEqual(ano.Sparse) {
		return false
	}
	if !p.Field6DeepEqual(ano.ExpireAfterSeconds) {
		return false
	}
	if !p.Field7DeepEqual(ano.Key) {
		return false
	}
	return true
}

func (p *MongoIndexInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Ns, src) != 0 {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field3DeepEqual(src bool) bool {

	if p.Background != src {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field4DeepEqual(src bool) bool {

	if p.Unique != src {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field5DeepEqual(src bool) bool {

	if p.Sparse != src {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field6DeepEqual(src int32) bool {

	if p.ExpireAfterSeconds != src {
		return false
	}
	return true
}
func (p *MongoIndexInfo) Field7DeepEqual(src []*IndexKey) bool {

	if len(p.Key) != len(src) {
		return false
	}
	for i, v := range p.Key {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeIndexsResp struct {
	Items     []string        `thrift:"Items,1,required" frugal:"1,required,list<string>" json:"Items"`
	Total     int32           `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
	IndexInfo *MongoIndexInfo `thrift:"IndexInfo,3,required" frugal:"3,required,MongoIndexInfo" json:"IndexInfo"`
	Col       string          `thrift:"Col,4,required" frugal:"4,required,string" json:"Col"`
	DB        string          `thrift:"DB,5,required" frugal:"5,required,string" json:"DB"`
}

func NewDescribeIndexsResp() *DescribeIndexsResp {
	return &DescribeIndexsResp{}
}

func (p *DescribeIndexsResp) InitDefault() {
}

func (p *DescribeIndexsResp) GetItems() (v []string) {
	return p.Items
}

func (p *DescribeIndexsResp) GetTotal() (v int32) {
	return p.Total
}

var DescribeIndexsResp_IndexInfo_DEFAULT *MongoIndexInfo

func (p *DescribeIndexsResp) GetIndexInfo() (v *MongoIndexInfo) {
	if !p.IsSetIndexInfo() {
		return DescribeIndexsResp_IndexInfo_DEFAULT
	}
	return p.IndexInfo
}

func (p *DescribeIndexsResp) GetCol() (v string) {
	return p.Col
}

func (p *DescribeIndexsResp) GetDB() (v string) {
	return p.DB
}
func (p *DescribeIndexsResp) SetItems(val []string) {
	p.Items = val
}
func (p *DescribeIndexsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeIndexsResp) SetIndexInfo(val *MongoIndexInfo) {
	p.IndexInfo = val
}
func (p *DescribeIndexsResp) SetCol(val string) {
	p.Col = val
}
func (p *DescribeIndexsResp) SetDB(val string) {
	p.DB = val
}

var fieldIDToName_DescribeIndexsResp = map[int16]string{
	1: "Items",
	2: "Total",
	3: "IndexInfo",
	4: "Col",
	5: "DB",
}

func (p *DescribeIndexsResp) IsSetIndexInfo() bool {
	return p.IndexInfo != nil
}

func (p *DescribeIndexsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItems bool = false
	var issetTotal bool = false
	var issetIndexInfo bool = false
	var issetCol bool = false
	var issetDB bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCol = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIndexInfo {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCol {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeIndexsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeIndexsResp[fieldId]))
}

func (p *DescribeIndexsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}
func (p *DescribeIndexsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeIndexsResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewMongoIndexInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.IndexInfo = _field
	return nil
}
func (p *DescribeIndexsResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Col = _field
	return nil
}
func (p *DescribeIndexsResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}

func (p *DescribeIndexsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeIndexsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeIndexsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Items", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Items)); err != nil {
		return err
	}
	for _, v := range p.Items {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeIndexsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeIndexsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexInfo", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.IndexInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeIndexsResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Col", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Col); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeIndexsResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeIndexsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeIndexsResp(%+v)", *p)

}

func (p *DescribeIndexsResp) DeepEqual(ano *DescribeIndexsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Items) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	if !p.Field3DeepEqual(ano.IndexInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.Col) {
		return false
	}
	if !p.Field5DeepEqual(ano.DB) {
		return false
	}
	return true
}

func (p *DescribeIndexsResp) Field1DeepEqual(src []string) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeIndexsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeIndexsResp) Field3DeepEqual(src *MongoIndexInfo) bool {

	if !p.IndexInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeIndexsResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Col, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexsResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}

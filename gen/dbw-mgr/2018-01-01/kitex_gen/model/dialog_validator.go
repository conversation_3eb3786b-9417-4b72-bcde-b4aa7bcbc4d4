// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DescribeDialogStatisticsReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeDialogStatisticsResp) IsValid() error {
	if p.DialogOverview != nil {
		if err := p.DialogOverview.IsValid(); err != nil {
			return fmt.E<PERSON>rf("field DialogOverview not valid, %w", err)
		}
	}
	return nil
}
func (p *DialogOverview) IsValid() error {
	return nil
}
func (p *ConnOverview) IsValid() error {
	return nil
}
func (p *UserAggregatedInfo) IsValid() error {
	return nil
}
func (p *IPAggregatedInfo) IsValid() error {
	return nil
}
func (p *PSMAggregatedInfo) IsValid() error {
	return nil
}
func (p *DBAggregatedInfo) IsValid() error {
	return nil
}
func (p *NsAggregatedInfo) IsValid() error {
	return nil
}
func (p *SqlHotspotsDistribution) IsValid() error {
	return nil
}
func (p *DescribeDialogDetailsReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *QueryFilter) IsValid() error {
	return nil
}
func (p *AggregateDialogFilter) IsValid() error {
	return nil
}
func (p *HotspotsFilter) IsValid() error {
	return nil
}
func (p *DescribeDialogDetailsResp) IsValid() error {
	return nil
}
func (p *DialogDetail) IsValid() error {
	return nil
}
func (p *CurrentDialogInfo) IsValid() error {
	return nil
}
func (p *ConnDetail) IsValid() error {
	return nil
}
func (p *KillProcessReq) IsValid() error {
	return nil
}
func (p *ProcessInfo) IsValid() error {
	return nil
}
func (p *KillProcessResp) IsValid() error {
	return nil
}
func (p *FailInfo) IsValid() error {
	return nil
}
func (p *DescribeDialogSnapshotsReq) IsValid() error {
	return nil
}
func (p *DescribeDialogSnapshotsResp) IsValid() error {
	return nil
}
func (p *DialogSnapshot) IsValid() error {
	return nil
}
func (p *DescribeDialogDetailSnapshotReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeDialogDetailSnapshotResp) IsValid() error {
	return nil
}
func (p *DescribeEngineStatusSnapShotReq) IsValid() error {
	return nil
}
func (p *DescribeEngineStatusSnapShotResp) IsValid() error {
	return nil
}
func (p *DescribeDialogInfosReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeCurrentConnInfosReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeCurrentConnInfosResp) IsValid() error {
	if p.Statistics != nil {
		if err := p.Statistics.IsValid(); err != nil {
			return fmt.Errorf("field Statistics not valid, %w", err)
		}
	}
	if p.Details != nil {
		if err := p.Details.IsValid(); err != nil {
			return fmt.Errorf("field Details not valid, %w", err)
		}
	}
	return nil
}
func (p *DialogStatistics) IsValid() error {
	if p.DialogOverview != nil {
		if err := p.DialogOverview.IsValid(); err != nil {
			return fmt.Errorf("field DialogOverview not valid, %w", err)
		}
	}
	return nil
}
func (p *ConnStatistics) IsValid() error {
	if p.ConnOverview != nil {
		if err := p.ConnOverview.IsValid(); err != nil {
			return fmt.Errorf("field ConnOverview not valid, %w", err)
		}
	}
	return nil
}
func (p *DialogDetails) IsValid() error {
	return nil
}
func (p *ConnDetails) IsValid() error {
	return nil
}
func (p *DescribeDialogInfosResp) IsValid() error {
	if p.Details != nil {
		if err := p.Details.IsValid(); err != nil {
			return fmt.Errorf("field Details not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeCurrentDialogsReq) IsValid() error {
	if p.QueryFilter != nil {
		if err := p.QueryFilter.IsValid(); err != nil {
			return fmt.Errorf("field QueryFilter not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeCurrentDialogsResp) IsValid() error {
	return nil
}
func (p *DescribeDBProxyStatusReq) IsValid() error {
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	return nil
}
func (p *DescribeDBProxyStatusResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceFeaturesReq) IsValid() error {
	return nil
}
func (p *DescribeInstanceFeaturesResp) IsValid() error {
	return nil
}
func (p *AggregateDialog) IsValid() error {
	if p.KeepTimeStats != nil {
		if err := p.KeepTimeStats.IsValid(); err != nil {
			return fmt.Errorf("field KeepTimeStats not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeAggregateDialogsReq) IsValid() error {
	if p.SearchParam != nil {
		if err := p.SearchParam.IsValid(); err != nil {
			return fmt.Errorf("field SearchParam not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeAggregateDialogsResp) IsValid() error {
	return nil
}
func (p *SqlHotspots) IsValid() error {
	return nil
}
func (p *DescribeDialogHotspotsReq) IsValid() error {
	if p.SearchParam != nil {
		if err := p.SearchParam.IsValid(); err != nil {
			return fmt.Errorf("field SearchParam not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeDialogHotspotsResp) IsValid() error {
	if p.SqlHotspots != nil {
		if err := p.SqlHotspots.IsValid(); err != nil {
			return fmt.Errorf("field SqlHotspots not valid, %w", err)
		}
	}
	return nil
}

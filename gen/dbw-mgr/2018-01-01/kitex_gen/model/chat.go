// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type StreamState int64

const (
	StreamState_Continue StreamState = 1
	StreamState_Finish   StreamState = 2
)

func (p StreamState) String() string {
	switch p {
	case StreamState_Continue:
		return "Continue"
	case StreamState_Finish:
		return "Finish"
	}
	return "<UNSET>"
}

func StreamStateFromString(s string) (StreamState, error) {
	switch s {
	case "Continue":
		return StreamState_Continue, nil
	case "Finish":
		return StreamState_Finish, nil
	}
	return StreamState(0), fmt.Errorf("not a valid StreamState string")
}

func StreamStatePtr(v StreamState) *StreamState { return &v }

func (p StreamState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *StreamState) UnmarshalText(text []byte) error {
	q, err := StreamStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type QueryAction int64

const (
	QueryAction_Generate QueryAction = 1
	QueryAction_Explain  QueryAction = 2
)

func (p QueryAction) String() string {
	switch p {
	case QueryAction_Generate:
		return "Generate"
	case QueryAction_Explain:
		return "Explain"
	}
	return "<UNSET>"
}

func QueryActionFromString(s string) (QueryAction, error) {
	switch s {
	case "Generate":
		return QueryAction_Generate, nil
	case "Explain":
		return QueryAction_Explain, nil
	}
	return QueryAction(0), fmt.Errorf("not a valid QueryAction string")
}

func QueryActionPtr(v QueryAction) *QueryAction { return &v }

func (p QueryAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *QueryAction) UnmarshalText(text []byte) error {
	q, err := QueryActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CreateChatReq struct {
}

func NewCreateChatReq() *CreateChatReq {
	return &CreateChatReq{}
}

func (p *CreateChatReq) InitDefault() {
}

var fieldIDToName_CreateChatReq = map[int16]string{}

func (p *CreateChatReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateChatReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateChatReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateChatReq")

	if err = oprot.WriteStructBegin("CreateChatReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateChatReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateChatReq(%+v)", *p)

}

func (p *CreateChatReq) DeepEqual(ano *CreateChatReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateChatResp struct {
	ChatId      string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Name        string `thrift:"Name,2,required" frugal:"2,required,string" json:"Name"`
	Description string `thrift:"Description,3,required" frugal:"3,required,string" json:"Description"`
	CreateTime  int32  `thrift:"CreateTime,4,required" frugal:"4,required,i32" json:"CreateTime"`
}

func NewCreateChatResp() *CreateChatResp {
	return &CreateChatResp{}
}

func (p *CreateChatResp) InitDefault() {
}

func (p *CreateChatResp) GetChatId() (v string) {
	return p.ChatId
}

func (p *CreateChatResp) GetName() (v string) {
	return p.Name
}

func (p *CreateChatResp) GetDescription() (v string) {
	return p.Description
}

func (p *CreateChatResp) GetCreateTime() (v int32) {
	return p.CreateTime
}
func (p *CreateChatResp) SetChatId(val string) {
	p.ChatId = val
}
func (p *CreateChatResp) SetName(val string) {
	p.Name = val
}
func (p *CreateChatResp) SetDescription(val string) {
	p.Description = val
}
func (p *CreateChatResp) SetCreateTime(val int32) {
	p.CreateTime = val
}

var fieldIDToName_CreateChatResp = map[int16]string{
	1: "ChatId",
	2: "Name",
	3: "Description",
	4: "CreateTime",
}

func (p *CreateChatResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateChatResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetName bool = false
	var issetDescription bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateChatResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateChatResp[fieldId]))
}

func (p *CreateChatResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *CreateChatResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CreateChatResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *CreateChatResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}

func (p *CreateChatResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateChatResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateChatResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateChatResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateChatResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateChatResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateChatResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateChatResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateChatResp(%+v)", *p)

}

func (p *CreateChatResp) DeepEqual(ano *CreateChatResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.Description) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *CreateChatResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateChatResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *CreateChatResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *CreateChatResp) Field4DeepEqual(src int32) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}

type ChatReq struct {
	ChatId  string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Message string `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
}

func NewChatReq() *ChatReq {
	return &ChatReq{}
}

func (p *ChatReq) InitDefault() {
}

func (p *ChatReq) GetChatId() (v string) {
	return p.ChatId
}

func (p *ChatReq) GetMessage() (v string) {
	return p.Message
}
func (p *ChatReq) SetChatId(val string) {
	p.ChatId = val
}
func (p *ChatReq) SetMessage(val string) {
	p.Message = val
}

var fieldIDToName_ChatReq = map[int16]string{
	1: "ChatId",
	2: "Message",
}

func (p *ChatReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChatReq[fieldId]))
}

func (p *ChatReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *ChatReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *ChatReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChatReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChatReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChatReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatReq(%+v)", *p)

}

func (p *ChatReq) DeepEqual(ano *ChatReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	return true
}

func (p *ChatReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *ChatReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}

type ChatResp struct {
	ChatId string   `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Query  *Message `thrift:"Query,2,required" frugal:"2,required,Message" json:"Query"`
	Reply  *Message `thrift:"Reply,3,required" frugal:"3,required,Message" json:"Reply"`
}

func NewChatResp() *ChatResp {
	return &ChatResp{}
}

func (p *ChatResp) InitDefault() {
}

func (p *ChatResp) GetChatId() (v string) {
	return p.ChatId
}

var ChatResp_Query_DEFAULT *Message

func (p *ChatResp) GetQuery() (v *Message) {
	if !p.IsSetQuery() {
		return ChatResp_Query_DEFAULT
	}
	return p.Query
}

var ChatResp_Reply_DEFAULT *Message

func (p *ChatResp) GetReply() (v *Message) {
	if !p.IsSetReply() {
		return ChatResp_Reply_DEFAULT
	}
	return p.Reply
}
func (p *ChatResp) SetChatId(val string) {
	p.ChatId = val
}
func (p *ChatResp) SetQuery(val *Message) {
	p.Query = val
}
func (p *ChatResp) SetReply(val *Message) {
	p.Reply = val
}

var fieldIDToName_ChatResp = map[int16]string{
	1: "ChatId",
	2: "Query",
	3: "Reply",
}

func (p *ChatResp) IsSetQuery() bool {
	return p.Query != nil
}

func (p *ChatResp) IsSetReply() bool {
	return p.Reply != nil
}

func (p *ChatResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetQuery bool = false
	var issetReply bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReply = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReply {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChatResp[fieldId]))
}

func (p *ChatResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *ChatResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Query = _field
	return nil
}
func (p *ChatResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Reply = _field
	return nil
}

func (p *ChatResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ChatResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChatResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ChatResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Query.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChatResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Reply", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Reply.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ChatResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatResp(%+v)", *p)

}

func (p *ChatResp) DeepEqual(ano *ChatResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Query) {
		return false
	}
	if !p.Field3DeepEqual(ano.Reply) {
		return false
	}
	return true
}

func (p *ChatResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *ChatResp) Field2DeepEqual(src *Message) bool {

	if !p.Query.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ChatResp) Field3DeepEqual(src *Message) bool {

	if !p.Reply.DeepEqual(src) {
		return false
	}
	return true
}

type Message struct {
	MessageId  string              `thrift:"MessageId,1,required" frugal:"1,required,string" json:"MessageId"`
	Text       string              `thrift:"Text,2,required" frugal:"2,required,string" json:"Text"`
	ReplyId    string              `thrift:"ReplyId,3,required" frugal:"3,required,string" json:"ReplyId"`
	CreateTime int32               `thrift:"CreateTime,4,required" frugal:"4,required,i32" json:"CreateTime"`
	Reply      *RateModelReplyEnum `thrift:"reply,5,optional" frugal:"5,optional,RateModelReplyEnum" json:"reply,omitempty"`
}

func NewMessage() *Message {
	return &Message{}
}

func (p *Message) InitDefault() {
}

func (p *Message) GetMessageId() (v string) {
	return p.MessageId
}

func (p *Message) GetText() (v string) {
	return p.Text
}

func (p *Message) GetReplyId() (v string) {
	return p.ReplyId
}

func (p *Message) GetCreateTime() (v int32) {
	return p.CreateTime
}

var Message_Reply_DEFAULT RateModelReplyEnum

func (p *Message) GetReply() (v RateModelReplyEnum) {
	if !p.IsSetReply() {
		return Message_Reply_DEFAULT
	}
	return *p.Reply
}
func (p *Message) SetMessageId(val string) {
	p.MessageId = val
}
func (p *Message) SetText(val string) {
	p.Text = val
}
func (p *Message) SetReplyId(val string) {
	p.ReplyId = val
}
func (p *Message) SetCreateTime(val int32) {
	p.CreateTime = val
}
func (p *Message) SetReply(val *RateModelReplyEnum) {
	p.Reply = val
}

var fieldIDToName_Message = map[int16]string{
	1: "MessageId",
	2: "Text",
	3: "ReplyId",
	4: "CreateTime",
	5: "reply",
}

func (p *Message) IsSetReply() bool {
	return p.Reply != nil
}

func (p *Message) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Message")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageId bool = false
	var issetText bool = false
	var issetReplyId bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReplyId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetText {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReplyId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Message[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Message[fieldId]))
}

func (p *Message) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}
func (p *Message) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Text = _field
	return nil
}
func (p *Message) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReplyId = _field
	return nil
}
func (p *Message) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *Message) ReadField5(iprot thrift.TProtocol) error {

	var _field *RateModelReplyEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RateModelReplyEnum(v)
		_field = &tmp
	}
	p.Reply = _field
	return nil
}

func (p *Message) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Message")

	var fieldId int16
	if err = oprot.WriteStructBegin("Message"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Message) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Message) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Text", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Text); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Message) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReplyId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReplyId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Message) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Message) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetReply() {
		if err = oprot.WriteFieldBegin("reply", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Reply)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Message) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Message(%+v)", *p)

}

func (p *Message) DeepEqual(ano *Message) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Text) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReplyId) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.Reply) {
		return false
	}
	return true
}

func (p *Message) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Text, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ReplyId, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field4DeepEqual(src int32) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}
func (p *Message) Field5DeepEqual(src *RateModelReplyEnum) bool {

	if p.Reply == src {
		return true
	} else if p.Reply == nil || src == nil {
		return false
	}
	if *p.Reply != *src {
		return false
	}
	return true
}

type ListChatHistoryReq struct {
	ChatId string  `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Cursor *string `thrift:"Cursor,2,optional" frugal:"2,optional,string" json:"Cursor,omitempty"`
	Limit  *int8   `thrift:"limit,3,optional" frugal:"3,optional,i8" json:"limit,omitempty"`
}

func NewListChatHistoryReq() *ListChatHistoryReq {
	return &ListChatHistoryReq{}
}

func (p *ListChatHistoryReq) InitDefault() {
}

func (p *ListChatHistoryReq) GetChatId() (v string) {
	return p.ChatId
}

var ListChatHistoryReq_Cursor_DEFAULT string

func (p *ListChatHistoryReq) GetCursor() (v string) {
	if !p.IsSetCursor() {
		return ListChatHistoryReq_Cursor_DEFAULT
	}
	return *p.Cursor
}

var ListChatHistoryReq_Limit_DEFAULT int8

func (p *ListChatHistoryReq) GetLimit() (v int8) {
	if !p.IsSetLimit() {
		return ListChatHistoryReq_Limit_DEFAULT
	}
	return *p.Limit
}
func (p *ListChatHistoryReq) SetChatId(val string) {
	p.ChatId = val
}
func (p *ListChatHistoryReq) SetCursor(val *string) {
	p.Cursor = val
}
func (p *ListChatHistoryReq) SetLimit(val *int8) {
	p.Limit = val
}

var fieldIDToName_ListChatHistoryReq = map[int16]string{
	1: "ChatId",
	2: "Cursor",
	3: "limit",
}

func (p *ListChatHistoryReq) IsSetCursor() bool {
	return p.Cursor != nil
}

func (p *ListChatHistoryReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListChatHistoryReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatHistoryReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListChatHistoryReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListChatHistoryReq[fieldId]))
}

func (p *ListChatHistoryReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *ListChatHistoryReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Cursor = _field
	return nil
}
func (p *ListChatHistoryReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}

func (p *ListChatHistoryReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatHistoryReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListChatHistoryReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListChatHistoryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListChatHistoryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCursor() {
		if err = oprot.WriteFieldBegin("Cursor", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Cursor); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListChatHistoryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("limit", thrift.BYTE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListChatHistoryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListChatHistoryReq(%+v)", *p)

}

func (p *ListChatHistoryReq) DeepEqual(ano *ListChatHistoryReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Cursor) {
		return false
	}
	if !p.Field3DeepEqual(ano.Limit) {
		return false
	}
	return true
}

func (p *ListChatHistoryReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *ListChatHistoryReq) Field2DeepEqual(src *string) bool {

	if p.Cursor == src {
		return true
	} else if p.Cursor == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Cursor, *src) != 0 {
		return false
	}
	return true
}
func (p *ListChatHistoryReq) Field3DeepEqual(src *int8) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}

type ListChatHistoryResp struct {
	HasPrevious bool       `thrift:"HasPrevious,1,required" frugal:"1,required,bool" json:"HasPrevious"`
	Messages    []*Message `thrift:"Messages,2,required" frugal:"2,required,list<Message>" json:"Messages"`
}

func NewListChatHistoryResp() *ListChatHistoryResp {
	return &ListChatHistoryResp{}
}

func (p *ListChatHistoryResp) InitDefault() {
}

func (p *ListChatHistoryResp) GetHasPrevious() (v bool) {
	return p.HasPrevious
}

func (p *ListChatHistoryResp) GetMessages() (v []*Message) {
	return p.Messages
}
func (p *ListChatHistoryResp) SetHasPrevious(val bool) {
	p.HasPrevious = val
}
func (p *ListChatHistoryResp) SetMessages(val []*Message) {
	p.Messages = val
}

var fieldIDToName_ListChatHistoryResp = map[int16]string{
	1: "HasPrevious",
	2: "Messages",
}

func (p *ListChatHistoryResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatHistoryResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHasPrevious bool = false
	var issetMessages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHasPrevious = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHasPrevious {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessages {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListChatHistoryResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListChatHistoryResp[fieldId]))
}

func (p *ListChatHistoryResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasPrevious = _field
	return nil
}
func (p *ListChatHistoryResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Message, 0, size)
	values := make([]Message, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}

func (p *ListChatHistoryResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatHistoryResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListChatHistoryResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListChatHistoryResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HasPrevious", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasPrevious); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListChatHistoryResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListChatHistoryResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListChatHistoryResp(%+v)", *p)

}

func (p *ListChatHistoryResp) DeepEqual(ano *ListChatHistoryResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HasPrevious) {
		return false
	}
	if !p.Field2DeepEqual(ano.Messages) {
		return false
	}
	return true
}

func (p *ListChatHistoryResp) Field1DeepEqual(src bool) bool {

	if p.HasPrevious != src {
		return false
	}
	return true
}
func (p *ListChatHistoryResp) Field2DeepEqual(src []*Message) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type Chat struct {
	ChatId      string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Name        string `thrift:"Name,2,required" frugal:"2,required,string" json:"Name"`
	Description string `thrift:"Description,3,required" frugal:"3,required,string" json:"Description"`
	CreateTime  int32  `thrift:"CreateTime,4,required" frugal:"4,required,i32" json:"CreateTime"`
}

func NewChat() *Chat {
	return &Chat{}
}

func (p *Chat) InitDefault() {
}

func (p *Chat) GetChatId() (v string) {
	return p.ChatId
}

func (p *Chat) GetName() (v string) {
	return p.Name
}

func (p *Chat) GetDescription() (v string) {
	return p.Description
}

func (p *Chat) GetCreateTime() (v int32) {
	return p.CreateTime
}
func (p *Chat) SetChatId(val string) {
	p.ChatId = val
}
func (p *Chat) SetName(val string) {
	p.Name = val
}
func (p *Chat) SetDescription(val string) {
	p.Description = val
}
func (p *Chat) SetCreateTime(val int32) {
	p.CreateTime = val
}

var fieldIDToName_Chat = map[int16]string{
	1: "ChatId",
	2: "Name",
	3: "Description",
	4: "CreateTime",
}

func (p *Chat) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Chat")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetName bool = false
	var issetDescription bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Chat[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Chat[fieldId]))
}

func (p *Chat) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *Chat) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Chat) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *Chat) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}

func (p *Chat) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Chat")

	var fieldId int16
	if err = oprot.WriteStructBegin("Chat"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Chat) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Chat) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Chat) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Chat) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Chat) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Chat(%+v)", *p)

}

func (p *Chat) DeepEqual(ano *Chat) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.Description) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *Chat) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *Chat) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *Chat) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *Chat) Field4DeepEqual(src int32) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}

type ListChatsReq struct {
	Cursor *string `thrift:"Cursor,1,optional" frugal:"1,optional,string" json:"Cursor,omitempty"`
	Limit  *int8   `thrift:"limit,2,optional" frugal:"2,optional,i8" json:"limit,omitempty"`
}

func NewListChatsReq() *ListChatsReq {
	return &ListChatsReq{}
}

func (p *ListChatsReq) InitDefault() {
}

var ListChatsReq_Cursor_DEFAULT string

func (p *ListChatsReq) GetCursor() (v string) {
	if !p.IsSetCursor() {
		return ListChatsReq_Cursor_DEFAULT
	}
	return *p.Cursor
}

var ListChatsReq_Limit_DEFAULT int8

func (p *ListChatsReq) GetLimit() (v int8) {
	if !p.IsSetLimit() {
		return ListChatsReq_Limit_DEFAULT
	}
	return *p.Limit
}
func (p *ListChatsReq) SetCursor(val *string) {
	p.Cursor = val
}
func (p *ListChatsReq) SetLimit(val *int8) {
	p.Limit = val
}

var fieldIDToName_ListChatsReq = map[int16]string{
	1: "Cursor",
	2: "limit",
}

func (p *ListChatsReq) IsSetCursor() bool {
	return p.Cursor != nil
}

func (p *ListChatsReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListChatsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListChatsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListChatsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Cursor = _field
	return nil
}
func (p *ListChatsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}

func (p *ListChatsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListChatsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListChatsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCursor() {
		if err = oprot.WriteFieldBegin("Cursor", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Cursor); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListChatsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("limit", thrift.BYTE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListChatsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListChatsReq(%+v)", *p)

}

func (p *ListChatsReq) DeepEqual(ano *ListChatsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Cursor) {
		return false
	}
	if !p.Field2DeepEqual(ano.Limit) {
		return false
	}
	return true
}

func (p *ListChatsReq) Field1DeepEqual(src *string) bool {

	if p.Cursor == src {
		return true
	} else if p.Cursor == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Cursor, *src) != 0 {
		return false
	}
	return true
}
func (p *ListChatsReq) Field2DeepEqual(src *int8) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}

type ListChatsResp struct {
	HasPrevious bool    `thrift:"HasPrevious,1,required" frugal:"1,required,bool" json:"HasPrevious"`
	Chats       []*Chat `thrift:"Chats,2,required" frugal:"2,required,list<Chat>" json:"Chats"`
}

func NewListChatsResp() *ListChatsResp {
	return &ListChatsResp{}
}

func (p *ListChatsResp) InitDefault() {
}

func (p *ListChatsResp) GetHasPrevious() (v bool) {
	return p.HasPrevious
}

func (p *ListChatsResp) GetChats() (v []*Chat) {
	return p.Chats
}
func (p *ListChatsResp) SetHasPrevious(val bool) {
	p.HasPrevious = val
}
func (p *ListChatsResp) SetChats(val []*Chat) {
	p.Chats = val
}

var fieldIDToName_ListChatsResp = map[int16]string{
	1: "HasPrevious",
	2: "Chats",
}

func (p *ListChatsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHasPrevious bool = false
	var issetChats bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHasPrevious = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChats = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHasPrevious {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChats {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListChatsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListChatsResp[fieldId]))
}

func (p *ListChatsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasPrevious = _field
	return nil
}
func (p *ListChatsResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Chat, 0, size)
	values := make([]Chat, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Chats = _field
	return nil
}

func (p *ListChatsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ListChatsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ListChatsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListChatsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HasPrevious", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasPrevious); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ListChatsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Chats", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Chats)); err != nil {
		return err
	}
	for _, v := range p.Chats {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListChatsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListChatsResp(%+v)", *p)

}

func (p *ListChatsResp) DeepEqual(ano *ListChatsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HasPrevious) {
		return false
	}
	if !p.Field2DeepEqual(ano.Chats) {
		return false
	}
	return true
}

func (p *ListChatsResp) Field1DeepEqual(src bool) bool {

	if p.HasPrevious != src {
		return false
	}
	return true
}
func (p *ListChatsResp) Field2DeepEqual(src []*Chat) bool {

	if len(p.Chats) != len(src) {
		return false
	}
	for i, v := range p.Chats {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ReportReq struct {
	MessageId string             `thrift:"MessageId,1,required" frugal:"1,required,string" json:"MessageId"`
	Reply     RateModelReplyEnum `thrift:"reply,2,required" frugal:"2,required,RateModelReplyEnum" json:"reply"`
}

func NewReportReq() *ReportReq {
	return &ReportReq{}
}

func (p *ReportReq) InitDefault() {
}

func (p *ReportReq) GetMessageId() (v string) {
	return p.MessageId
}

func (p *ReportReq) GetReply() (v RateModelReplyEnum) {
	return p.Reply
}
func (p *ReportReq) SetMessageId(val string) {
	p.MessageId = val
}
func (p *ReportReq) SetReply(val RateModelReplyEnum) {
	p.Reply = val
}

var fieldIDToName_ReportReq = map[int16]string{
	1: "MessageId",
	2: "reply",
}

func (p *ReportReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageId bool = false
	var issetReply bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetReply = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetReply {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReportReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReportReq[fieldId]))
}

func (p *ReportReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}
func (p *ReportReq) ReadField2(iprot thrift.TProtocol) error {

	var _field RateModelReplyEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RateModelReplyEnum(v)
	}
	p.Reply = _field
	return nil
}

func (p *ReportReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReportReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReportReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReportReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reply", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Reply)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ReportReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportReq(%+v)", *p)

}

func (p *ReportReq) DeepEqual(ano *ReportReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Reply) {
		return false
	}
	return true
}

func (p *ReportReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}
func (p *ReportReq) Field2DeepEqual(src RateModelReplyEnum) bool {

	if p.Reply != src {
		return false
	}
	return true
}

type ReportResp struct {
}

func NewReportResp() *ReportResp {
	return &ReportResp{}
}

func (p *ReportResp) InitDefault() {
}

var fieldIDToName_ReportResp = map[int16]string{}

func (p *ReportResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ReportResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReportResp")

	if err = oprot.WriteStructBegin("ReportResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReportResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportResp(%+v)", *p)

}

func (p *ReportResp) DeepEqual(ano *ReportResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteChatReq struct {
	ChatId string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
}

func NewDeleteChatReq() *DeleteChatReq {
	return &DeleteChatReq{}
}

func (p *DeleteChatReq) InitDefault() {
}

func (p *DeleteChatReq) GetChatId() (v string) {
	return p.ChatId
}
func (p *DeleteChatReq) SetChatId(val string) {
	p.ChatId = val
}

var fieldIDToName_DeleteChatReq = map[int16]string{
	1: "ChatId",
}

func (p *DeleteChatReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteChatReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteChatReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteChatReq[fieldId]))
}

func (p *DeleteChatReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}

func (p *DeleteChatReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteChatReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteChatReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteChatReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteChatReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteChatReq(%+v)", *p)

}

func (p *DeleteChatReq) DeepEqual(ano *DeleteChatReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	return true
}

func (p *DeleteChatReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}

type DeleteChatResp struct {
}

func NewDeleteChatResp() *DeleteChatResp {
	return &DeleteChatResp{}
}

func (p *DeleteChatResp) InitDefault() {
}

var fieldIDToName_DeleteChatResp = map[int16]string{}

func (p *DeleteChatResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteChatResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteChatResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteChatResp")

	if err = oprot.WriteStructBegin("DeleteChatResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteChatResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteChatResp(%+v)", *p)

}

func (p *DeleteChatResp) DeepEqual(ano *DeleteChatResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type SqlAssistantReq struct {
	SessionId       string      `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	Database        string      `thrift:"Database,2,required" frugal:"2,required,string" json:"Database"`
	Query           string      `thrift:"Query,3,required" frugal:"3,required,string" json:"Query"`
	Action          QueryAction `thrift:"Action,4,required" frugal:"4,required,QueryAction" json:"Action"`
	Tables          []string    `thrift:"Tables,5,optional" frugal:"5,optional,list<string>" json:"Tables,omitempty"`
	IsStream        *bool       `thrift:"IsStream,6,optional" frugal:"6,optional,bool" json:"IsStream,omitempty"`
	WithExplanation *bool       `thrift:"WithExplanation,7,optional" frugal:"7,optional,bool" json:"WithExplanation,omitempty"`
}

func NewSqlAssistantReq() *SqlAssistantReq {
	return &SqlAssistantReq{}
}

func (p *SqlAssistantReq) InitDefault() {
}

func (p *SqlAssistantReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *SqlAssistantReq) GetDatabase() (v string) {
	return p.Database
}

func (p *SqlAssistantReq) GetQuery() (v string) {
	return p.Query
}

func (p *SqlAssistantReq) GetAction() (v QueryAction) {
	return p.Action
}

var SqlAssistantReq_Tables_DEFAULT []string

func (p *SqlAssistantReq) GetTables() (v []string) {
	if !p.IsSetTables() {
		return SqlAssistantReq_Tables_DEFAULT
	}
	return p.Tables
}

var SqlAssistantReq_IsStream_DEFAULT bool

func (p *SqlAssistantReq) GetIsStream() (v bool) {
	if !p.IsSetIsStream() {
		return SqlAssistantReq_IsStream_DEFAULT
	}
	return *p.IsStream
}

var SqlAssistantReq_WithExplanation_DEFAULT bool

func (p *SqlAssistantReq) GetWithExplanation() (v bool) {
	if !p.IsSetWithExplanation() {
		return SqlAssistantReq_WithExplanation_DEFAULT
	}
	return *p.WithExplanation
}
func (p *SqlAssistantReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *SqlAssistantReq) SetDatabase(val string) {
	p.Database = val
}
func (p *SqlAssistantReq) SetQuery(val string) {
	p.Query = val
}
func (p *SqlAssistantReq) SetAction(val QueryAction) {
	p.Action = val
}
func (p *SqlAssistantReq) SetTables(val []string) {
	p.Tables = val
}
func (p *SqlAssistantReq) SetIsStream(val *bool) {
	p.IsStream = val
}
func (p *SqlAssistantReq) SetWithExplanation(val *bool) {
	p.WithExplanation = val
}

var fieldIDToName_SqlAssistantReq = map[int16]string{
	1: "SessionId",
	2: "Database",
	3: "Query",
	4: "Action",
	5: "Tables",
	6: "IsStream",
	7: "WithExplanation",
}

func (p *SqlAssistantReq) IsSetTables() bool {
	return p.Tables != nil
}

func (p *SqlAssistantReq) IsSetIsStream() bool {
	return p.IsStream != nil
}

func (p *SqlAssistantReq) IsSetWithExplanation() bool {
	return p.WithExplanation != nil
}

func (p *SqlAssistantReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDatabase bool = false
	var issetQuery bool = false
	var issetAction bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabase = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabase {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlAssistantReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlAssistantReq[fieldId]))
}

func (p *SqlAssistantReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *SqlAssistantReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Database = _field
	return nil
}
func (p *SqlAssistantReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *SqlAssistantReq) ReadField4(iprot thrift.TProtocol) error {

	var _field QueryAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = QueryAction(v)
	}
	p.Action = _field
	return nil
}
func (p *SqlAssistantReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tables = _field
	return nil
}
func (p *SqlAssistantReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsStream = _field
	return nil
}
func (p *SqlAssistantReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WithExplanation = _field
	return nil
}

func (p *SqlAssistantReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlAssistantReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlAssistantReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Database", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Database); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Action", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTables() {
		if err = oprot.WriteFieldBegin("Tables", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Tables)); err != nil {
			return err
		}
		for _, v := range p.Tables {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsStream() {
		if err = oprot.WriteFieldBegin("IsStream", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsStream); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlAssistantReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetWithExplanation() {
		if err = oprot.WriteFieldBegin("WithExplanation", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.WithExplanation); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlAssistantReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlAssistantReq(%+v)", *p)

}

func (p *SqlAssistantReq) DeepEqual(ano *SqlAssistantReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Database) {
		return false
	}
	if !p.Field3DeepEqual(ano.Query) {
		return false
	}
	if !p.Field4DeepEqual(ano.Action) {
		return false
	}
	if !p.Field5DeepEqual(ano.Tables) {
		return false
	}
	if !p.Field6DeepEqual(ano.IsStream) {
		return false
	}
	if !p.Field7DeepEqual(ano.WithExplanation) {
		return false
	}
	return true
}

func (p *SqlAssistantReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Database, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantReq) Field4DeepEqual(src QueryAction) bool {

	if p.Action != src {
		return false
	}
	return true
}
func (p *SqlAssistantReq) Field5DeepEqual(src []string) bool {

	if len(p.Tables) != len(src) {
		return false
	}
	for i, v := range p.Tables {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SqlAssistantReq) Field6DeepEqual(src *bool) bool {

	if p.IsStream == src {
		return true
	} else if p.IsStream == nil || src == nil {
		return false
	}
	if *p.IsStream != *src {
		return false
	}
	return true
}
func (p *SqlAssistantReq) Field7DeepEqual(src *bool) bool {

	if p.WithExplanation == src {
		return true
	} else if p.WithExplanation == nil || src == nil {
		return false
	}
	if *p.WithExplanation != *src {
		return false
	}
	return true
}

type SqlAssistantResp struct {
	ChatId string   `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Query  *Message `thrift:"Query,2,required" frugal:"2,required,Message" json:"Query"`
	Reply  *Message `thrift:"Reply,3,required" frugal:"3,required,Message" json:"Reply"`
}

func NewSqlAssistantResp() *SqlAssistantResp {
	return &SqlAssistantResp{}
}

func (p *SqlAssistantResp) InitDefault() {
}

func (p *SqlAssistantResp) GetChatId() (v string) {
	return p.ChatId
}

var SqlAssistantResp_Query_DEFAULT *Message

func (p *SqlAssistantResp) GetQuery() (v *Message) {
	if !p.IsSetQuery() {
		return SqlAssistantResp_Query_DEFAULT
	}
	return p.Query
}

var SqlAssistantResp_Reply_DEFAULT *Message

func (p *SqlAssistantResp) GetReply() (v *Message) {
	if !p.IsSetReply() {
		return SqlAssistantResp_Reply_DEFAULT
	}
	return p.Reply
}
func (p *SqlAssistantResp) SetChatId(val string) {
	p.ChatId = val
}
func (p *SqlAssistantResp) SetQuery(val *Message) {
	p.Query = val
}
func (p *SqlAssistantResp) SetReply(val *Message) {
	p.Reply = val
}

var fieldIDToName_SqlAssistantResp = map[int16]string{
	1: "ChatId",
	2: "Query",
	3: "Reply",
}

func (p *SqlAssistantResp) IsSetQuery() bool {
	return p.Query != nil
}

func (p *SqlAssistantResp) IsSetReply() bool {
	return p.Reply != nil
}

func (p *SqlAssistantResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetQuery bool = false
	var issetReply bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetReply = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetReply {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlAssistantResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlAssistantResp[fieldId]))
}

func (p *SqlAssistantResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *SqlAssistantResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Query = _field
	return nil
}
func (p *SqlAssistantResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Reply = _field
	return nil
}

func (p *SqlAssistantResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlAssistantResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlAssistantResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlAssistantResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Query.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlAssistantResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Reply", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Reply.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlAssistantResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlAssistantResp(%+v)", *p)

}

func (p *SqlAssistantResp) DeepEqual(ano *SqlAssistantResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Query) {
		return false
	}
	if !p.Field3DeepEqual(ano.Reply) {
		return false
	}
	return true
}

func (p *SqlAssistantResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantResp) Field2DeepEqual(src *Message) bool {

	if !p.Query.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SqlAssistantResp) Field3DeepEqual(src *Message) bool {

	if !p.Reply.DeepEqual(src) {
		return false
	}
	return true
}

type SqlAssistantStreamResp struct {
	ChatId    string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	MessageId string `thrift:"MessageId,2,required" frugal:"2,required,string" json:"MessageId"`
	Content   string `thrift:"Content,3,required" frugal:"3,required,string" json:"Content"`
	Index     int32  `thrift:"Index,4,required" frugal:"4,required,i32" json:"Index"`
}

func NewSqlAssistantStreamResp() *SqlAssistantStreamResp {
	return &SqlAssistantStreamResp{}
}

func (p *SqlAssistantStreamResp) InitDefault() {
}

func (p *SqlAssistantStreamResp) GetChatId() (v string) {
	return p.ChatId
}

func (p *SqlAssistantStreamResp) GetMessageId() (v string) {
	return p.MessageId
}

func (p *SqlAssistantStreamResp) GetContent() (v string) {
	return p.Content
}

func (p *SqlAssistantStreamResp) GetIndex() (v int32) {
	return p.Index
}
func (p *SqlAssistantStreamResp) SetChatId(val string) {
	p.ChatId = val
}
func (p *SqlAssistantStreamResp) SetMessageId(val string) {
	p.MessageId = val
}
func (p *SqlAssistantStreamResp) SetContent(val string) {
	p.Content = val
}
func (p *SqlAssistantStreamResp) SetIndex(val int32) {
	p.Index = val
}

var fieldIDToName_SqlAssistantStreamResp = map[int16]string{
	1: "ChatId",
	2: "MessageId",
	3: "Content",
	4: "Index",
}

func (p *SqlAssistantStreamResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantStreamResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetMessageId bool = false
	var issetContent bool = false
	var issetIndex bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessageId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIndex {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlAssistantStreamResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlAssistantStreamResp[fieldId]))
}

func (p *SqlAssistantStreamResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *SqlAssistantStreamResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageId = _field
	return nil
}
func (p *SqlAssistantStreamResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *SqlAssistantStreamResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Index = _field
	return nil
}

func (p *SqlAssistantStreamResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlAssistantStreamResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlAssistantStreamResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlAssistantStreamResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlAssistantStreamResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlAssistantStreamResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlAssistantStreamResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Index", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Index); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlAssistantStreamResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlAssistantStreamResp(%+v)", *p)

}

func (p *SqlAssistantStreamResp) DeepEqual(ano *SqlAssistantStreamResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.Index) {
		return false
	}
	return true
}

func (p *SqlAssistantStreamResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantStreamResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.MessageId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantStreamResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *SqlAssistantStreamResp) Field4DeepEqual(src int32) bool {

	if p.Index != src {
		return false
	}
	return true
}

type GetSqlAssistantProtocolReq struct {
}

func NewGetSqlAssistantProtocolReq() *GetSqlAssistantProtocolReq {
	return &GetSqlAssistantProtocolReq{}
}

func (p *GetSqlAssistantProtocolReq) InitDefault() {
}

var fieldIDToName_GetSqlAssistantProtocolReq = map[int16]string{}

func (p *GetSqlAssistantProtocolReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAssistantProtocolReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSqlAssistantProtocolReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAssistantProtocolReq")

	if err = oprot.WriteStructBegin("GetSqlAssistantProtocolReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlAssistantProtocolReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlAssistantProtocolReq(%+v)", *p)

}

func (p *GetSqlAssistantProtocolReq) DeepEqual(ano *GetSqlAssistantProtocolReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type GetSqlAssistantProtocolResp struct {
	Agreed bool `thrift:"Agreed,1,required" frugal:"1,required,bool" json:"Agreed"`
}

func NewGetSqlAssistantProtocolResp() *GetSqlAssistantProtocolResp {
	return &GetSqlAssistantProtocolResp{}
}

func (p *GetSqlAssistantProtocolResp) InitDefault() {
}

func (p *GetSqlAssistantProtocolResp) GetAgreed() (v bool) {
	return p.Agreed
}
func (p *GetSqlAssistantProtocolResp) SetAgreed(val bool) {
	p.Agreed = val
}

var fieldIDToName_GetSqlAssistantProtocolResp = map[int16]string{
	1: "Agreed",
}

func (p *GetSqlAssistantProtocolResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAssistantProtocolResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAgreed bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgreed = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAgreed {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlAssistantProtocolResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSqlAssistantProtocolResp[fieldId]))
}

func (p *GetSqlAssistantProtocolResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Agreed = _field
	return nil
}

func (p *GetSqlAssistantProtocolResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlAssistantProtocolResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSqlAssistantProtocolResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlAssistantProtocolResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Agreed", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Agreed); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSqlAssistantProtocolResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlAssistantProtocolResp(%+v)", *p)

}

func (p *GetSqlAssistantProtocolResp) DeepEqual(ano *GetSqlAssistantProtocolResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Agreed) {
		return false
	}
	return true
}

func (p *GetSqlAssistantProtocolResp) Field1DeepEqual(src bool) bool {

	if p.Agreed != src {
		return false
	}
	return true
}

type AgreeSqlAssistantProtocolReq struct {
}

func NewAgreeSqlAssistantProtocolReq() *AgreeSqlAssistantProtocolReq {
	return &AgreeSqlAssistantProtocolReq{}
}

func (p *AgreeSqlAssistantProtocolReq) InitDefault() {
}

var fieldIDToName_AgreeSqlAssistantProtocolReq = map[int16]string{}

func (p *AgreeSqlAssistantProtocolReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSqlAssistantProtocolReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgreeSqlAssistantProtocolReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSqlAssistantProtocolReq")

	if err = oprot.WriteStructBegin("AgreeSqlAssistantProtocolReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreeSqlAssistantProtocolReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreeSqlAssistantProtocolReq(%+v)", *p)

}

func (p *AgreeSqlAssistantProtocolReq) DeepEqual(ano *AgreeSqlAssistantProtocolReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type AgreeSqlAssistantProtocolResp struct {
}

func NewAgreeSqlAssistantProtocolResp() *AgreeSqlAssistantProtocolResp {
	return &AgreeSqlAssistantProtocolResp{}
}

func (p *AgreeSqlAssistantProtocolResp) InitDefault() {
}

var fieldIDToName_AgreeSqlAssistantProtocolResp = map[int16]string{}

func (p *AgreeSqlAssistantProtocolResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSqlAssistantProtocolResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgreeSqlAssistantProtocolResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSqlAssistantProtocolResp")

	if err = oprot.WriteStructBegin("AgreeSqlAssistantProtocolResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreeSqlAssistantProtocolResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreeSqlAssistantProtocolResp(%+v)", *p)

}

func (p *AgreeSqlAssistantProtocolResp) DeepEqual(ano *AgreeSqlAssistantProtocolResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type SqlCorrectReq struct {
	SessionId     string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	Database      string `thrift:"Database,2,required" frugal:"2,required,string" json:"Database"`
	Statement     string `thrift:"Statement,3,required" frugal:"3,required,string" json:"Statement"`
	OriginalError string `thrift:"OriginalError,4,required" frugal:"4,required,string" json:"OriginalError"`
	IsStream      *bool  `thrift:"IsStream,5,optional" frugal:"5,optional,bool" json:"IsStream,omitempty"`
}

func NewSqlCorrectReq() *SqlCorrectReq {
	return &SqlCorrectReq{}
}

func (p *SqlCorrectReq) InitDefault() {
}

func (p *SqlCorrectReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *SqlCorrectReq) GetDatabase() (v string) {
	return p.Database
}

func (p *SqlCorrectReq) GetStatement() (v string) {
	return p.Statement
}

func (p *SqlCorrectReq) GetOriginalError() (v string) {
	return p.OriginalError
}

var SqlCorrectReq_IsStream_DEFAULT bool

func (p *SqlCorrectReq) GetIsStream() (v bool) {
	if !p.IsSetIsStream() {
		return SqlCorrectReq_IsStream_DEFAULT
	}
	return *p.IsStream
}
func (p *SqlCorrectReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *SqlCorrectReq) SetDatabase(val string) {
	p.Database = val
}
func (p *SqlCorrectReq) SetStatement(val string) {
	p.Statement = val
}
func (p *SqlCorrectReq) SetOriginalError(val string) {
	p.OriginalError = val
}
func (p *SqlCorrectReq) SetIsStream(val *bool) {
	p.IsStream = val
}

var fieldIDToName_SqlCorrectReq = map[int16]string{
	1: "SessionId",
	2: "Database",
	3: "Statement",
	4: "OriginalError",
	5: "IsStream",
}

func (p *SqlCorrectReq) IsSetIsStream() bool {
	return p.IsStream != nil
}

func (p *SqlCorrectReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDatabase bool = false
	var issetStatement bool = false
	var issetOriginalError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabase = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetOriginalError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabase {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStatement {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetOriginalError {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlCorrectReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlCorrectReq[fieldId]))
}

func (p *SqlCorrectReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *SqlCorrectReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Database = _field
	return nil
}
func (p *SqlCorrectReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}
func (p *SqlCorrectReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OriginalError = _field
	return nil
}
func (p *SqlCorrectReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsStream = _field
	return nil
}

func (p *SqlCorrectReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlCorrectReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlCorrectReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlCorrectReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Database", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Database); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlCorrectReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlCorrectReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OriginalError", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OriginalError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlCorrectReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsStream() {
		if err = oprot.WriteFieldBegin("IsStream", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsStream); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlCorrectReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlCorrectReq(%+v)", *p)

}

func (p *SqlCorrectReq) DeepEqual(ano *SqlCorrectReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Database) {
		return false
	}
	if !p.Field3DeepEqual(ano.Statement) {
		return false
	}
	if !p.Field4DeepEqual(ano.OriginalError) {
		return false
	}
	if !p.Field5DeepEqual(ano.IsStream) {
		return false
	}
	return true
}

func (p *SqlCorrectReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlCorrectReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Database, src) != 0 {
		return false
	}
	return true
}
func (p *SqlCorrectReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}
func (p *SqlCorrectReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.OriginalError, src) != 0 {
		return false
	}
	return true
}
func (p *SqlCorrectReq) Field5DeepEqual(src *bool) bool {

	if p.IsStream == src {
		return true
	} else if p.IsStream == nil || src == nil {
		return false
	}
	if *p.IsStream != *src {
		return false
	}
	return true
}

type SqlCorrectResp struct {
	Answer string `thrift:"Answer,1,required" frugal:"1,required,string" json:"Answer"`
}

func NewSqlCorrectResp() *SqlCorrectResp {
	return &SqlCorrectResp{}
}

func (p *SqlCorrectResp) InitDefault() {
}

func (p *SqlCorrectResp) GetAnswer() (v string) {
	return p.Answer
}
func (p *SqlCorrectResp) SetAnswer(val string) {
	p.Answer = val
}

var fieldIDToName_SqlCorrectResp = map[int16]string{
	1: "Answer",
}

func (p *SqlCorrectResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAnswer bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAnswer = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAnswer {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlCorrectResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlCorrectResp[fieldId]))
}

func (p *SqlCorrectResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Answer = _field
	return nil
}

func (p *SqlCorrectResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlCorrectResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlCorrectResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Answer", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Answer); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlCorrectResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlCorrectResp(%+v)", *p)

}

func (p *SqlCorrectResp) DeepEqual(ano *SqlCorrectResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Answer) {
		return false
	}
	return true
}

func (p *SqlCorrectResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Answer, src) != 0 {
		return false
	}
	return true
}

type SqlCorrectStreamResp struct {
	ChatId  string `thrift:"ChatId,1,required" frugal:"1,required,string" json:"ChatId"`
	Content string `thrift:"Content,2,required" frugal:"2,required,string" json:"Content"`
}

func NewSqlCorrectStreamResp() *SqlCorrectStreamResp {
	return &SqlCorrectStreamResp{}
}

func (p *SqlCorrectStreamResp) InitDefault() {
}

func (p *SqlCorrectStreamResp) GetChatId() (v string) {
	return p.ChatId
}

func (p *SqlCorrectStreamResp) GetContent() (v string) {
	return p.Content
}
func (p *SqlCorrectStreamResp) SetChatId(val string) {
	p.ChatId = val
}
func (p *SqlCorrectStreamResp) SetContent(val string) {
	p.Content = val
}

var fieldIDToName_SqlCorrectStreamResp = map[int16]string{
	1: "ChatId",
	2: "Content",
}

func (p *SqlCorrectStreamResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectStreamResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChatId bool = false
	var issetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChatId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChatId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlCorrectStreamResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlCorrectStreamResp[fieldId]))
}

func (p *SqlCorrectStreamResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatId = _field
	return nil
}
func (p *SqlCorrectStreamResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}

func (p *SqlCorrectStreamResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlCorrectStreamResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlCorrectStreamResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlCorrectStreamResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChatId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlCorrectStreamResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlCorrectStreamResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlCorrectStreamResp(%+v)", *p)

}

func (p *SqlCorrectStreamResp) DeepEqual(ano *SqlCorrectStreamResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChatId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	return true
}

func (p *SqlCorrectStreamResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ChatId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlCorrectStreamResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}

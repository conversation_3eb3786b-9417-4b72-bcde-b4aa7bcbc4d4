// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ConsoleOperationType int64

const (
	ConsoleOperationType_EXECUTE_SQL ConsoleOperationType = 1
	ConsoleOperationType_DATA_EXPORT ConsoleOperationType = 2
	ConsoleOperationType_DATA_IMPORT ConsoleOperationType = 3
)

func (p ConsoleOperationType) String() string {
	switch p {
	case ConsoleOperationType_EXECUTE_SQL:
		return "EXECUTE_SQL"
	case ConsoleOperationType_DATA_EXPORT:
		return "DATA_EXPORT"
	case ConsoleOperationType_DATA_IMPORT:
		return "DATA_IMPORT"
	}
	return "<UNSET>"
}

func ConsoleOperationTypeFromString(s string) (ConsoleOperationType, error) {
	switch s {
	case "EXECUTE_SQL":
		return ConsoleOperationType_EXECUTE_SQL, nil
	case "DATA_EXPORT":
		return ConsoleOperationType_DATA_EXPORT, nil
	case "DATA_IMPORT":
		return ConsoleOperationType_DATA_IMPORT, nil
	}
	return ConsoleOperationType(0), fmt.Errorf("not a valid ConsoleOperationType string")
}

func ConsoleOperationTypePtr(v ConsoleOperationType) *ConsoleOperationType { return &v }

func (p ConsoleOperationType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ConsoleOperationType) UnmarshalText(text []byte) error {
	q, err := ConsoleOperationTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ConsoleOperationStatus int64

const (
	ConsoleOperationStatus_ECECUTION ConsoleOperationStatus = 0
	ConsoleOperationStatus_SUCCESS   ConsoleOperationStatus = 1
	ConsoleOperationStatus_FAILED    ConsoleOperationStatus = 2
)

func (p ConsoleOperationStatus) String() string {
	switch p {
	case ConsoleOperationStatus_ECECUTION:
		return "ECECUTION"
	case ConsoleOperationStatus_SUCCESS:
		return "SUCCESS"
	case ConsoleOperationStatus_FAILED:
		return "FAILED"
	}
	return "<UNSET>"
}

func ConsoleOperationStatusFromString(s string) (ConsoleOperationStatus, error) {
	switch s {
	case "ECECUTION":
		return ConsoleOperationStatus_ECECUTION, nil
	case "SUCCESS":
		return ConsoleOperationStatus_SUCCESS, nil
	case "FAILED":
		return ConsoleOperationStatus_FAILED, nil
	}
	return ConsoleOperationStatus(0), fmt.Errorf("not a valid ConsoleOperationStatus string")
}

func ConsoleOperationStatusPtr(v ConsoleOperationStatus) *ConsoleOperationStatus { return &v }

func (p ConsoleOperationStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ConsoleOperationStatus) UnmarshalText(text []byte) error {
	q, err := ConsoleOperationStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DasOperationCategory int64

const (
	DasOperationCategory_CCL  DasOperationCategory = 0
	DasOperationCategory_Kill DasOperationCategory = 1
)

func (p DasOperationCategory) String() string {
	switch p {
	case DasOperationCategory_CCL:
		return "CCL"
	case DasOperationCategory_Kill:
		return "Kill"
	}
	return "<UNSET>"
}

func DasOperationCategoryFromString(s string) (DasOperationCategory, error) {
	switch s {
	case "CCL":
		return DasOperationCategory_CCL, nil
	case "Kill":
		return DasOperationCategory_Kill, nil
	}
	return DasOperationCategory(0), fmt.Errorf("not a valid DasOperationCategory string")
}

func DasOperationCategoryPtr(v DasOperationCategory) *DasOperationCategory { return &v }

func (p DasOperationCategory) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DasOperationCategory) UnmarshalText(text []byte) error {
	q, err := DasOperationCategoryFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DasAction int64

const (
	DasAction_CreateThrottleRule  DasAction = 0
	DasAction_DeleteThrottleRule  DasAction = 1
	DasAction_StopThrottleRule    DasAction = 2
	DasAction_ModifyThrottleRule  DasAction = 3
	DasAction_CreateAutoKillRule  DasAction = 4
	DasAction_DeleteAutoKillRule  DasAction = 5
	DasAction_StopAutoKillRule    DasAction = 6
	DasAction_ManualKill          DasAction = 7
	DasAction_RestartThrottleRule DasAction = 8
)

func (p DasAction) String() string {
	switch p {
	case DasAction_CreateThrottleRule:
		return "CreateThrottleRule"
	case DasAction_DeleteThrottleRule:
		return "DeleteThrottleRule"
	case DasAction_StopThrottleRule:
		return "StopThrottleRule"
	case DasAction_ModifyThrottleRule:
		return "ModifyThrottleRule"
	case DasAction_CreateAutoKillRule:
		return "CreateAutoKillRule"
	case DasAction_DeleteAutoKillRule:
		return "DeleteAutoKillRule"
	case DasAction_StopAutoKillRule:
		return "StopAutoKillRule"
	case DasAction_ManualKill:
		return "ManualKill"
	case DasAction_RestartThrottleRule:
		return "RestartThrottleRule"
	}
	return "<UNSET>"
}

func DasActionFromString(s string) (DasAction, error) {
	switch s {
	case "CreateThrottleRule":
		return DasAction_CreateThrottleRule, nil
	case "DeleteThrottleRule":
		return DasAction_DeleteThrottleRule, nil
	case "StopThrottleRule":
		return DasAction_StopThrottleRule, nil
	case "ModifyThrottleRule":
		return DasAction_ModifyThrottleRule, nil
	case "CreateAutoKillRule":
		return DasAction_CreateAutoKillRule, nil
	case "DeleteAutoKillRule":
		return DasAction_DeleteAutoKillRule, nil
	case "StopAutoKillRule":
		return DasAction_StopAutoKillRule, nil
	case "ManualKill":
		return DasAction_ManualKill, nil
	case "RestartThrottleRule":
		return DasAction_RestartThrottleRule, nil
	}
	return DasAction(0), fmt.Errorf("not a valid DasAction string")
}

func DasActionPtr(v DasAction) *DasAction { return &v }

func (p DasAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DasAction) UnmarshalText(text []byte) error {
	q, err := DasActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TriggerType int64

const (
	TriggerType_Auto   TriggerType = 0
	TriggerType_Manual TriggerType = 1
)

func (p TriggerType) String() string {
	switch p {
	case TriggerType_Auto:
		return "Auto"
	case TriggerType_Manual:
		return "Manual"
	}
	return "<UNSET>"
}

func TriggerTypeFromString(s string) (TriggerType, error) {
	switch s {
	case "Auto":
		return TriggerType_Auto, nil
	case "Manual":
		return TriggerType_Manual, nil
	}
	return TriggerType(0), fmt.Errorf("not a valid TriggerType string")
}

func TriggerTypePtr(v TriggerType) *TriggerType { return &v }

func (p TriggerType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TriggerType) UnmarshalText(text []byte) error {
	q, err := TriggerTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type KillType int64

const (
	KillType_AutoKill   KillType = 0
	KillType_ManualKill KillType = 1
)

func (p KillType) String() string {
	switch p {
	case KillType_AutoKill:
		return "AutoKill"
	case KillType_ManualKill:
		return "ManualKill"
	}
	return "<UNSET>"
}

func KillTypeFromString(s string) (KillType, error) {
	switch s {
	case "AutoKill":
		return KillType_AutoKill, nil
	case "ManualKill":
		return KillType_ManualKill, nil
	}
	return KillType(0), fmt.Errorf("not a valid KillType string")
}

func KillTypePtr(v KillType) *KillType { return &v }

func (p KillType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *KillType) UnmarshalText(text []byte) error {
	q, err := KillTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OpsTaskState int64

const (
	OpsTaskState_Running       OpsTaskState = 0
	OpsTaskState_RunningFailed OpsTaskState = 1
	OpsTaskState_Done          OpsTaskState = 2
)

func (p OpsTaskState) String() string {
	switch p {
	case OpsTaskState_Running:
		return "Running"
	case OpsTaskState_RunningFailed:
		return "RunningFailed"
	case OpsTaskState_Done:
		return "Done"
	}
	return "<UNSET>"
}

func OpsTaskStateFromString(s string) (OpsTaskState, error) {
	switch s {
	case "Running":
		return OpsTaskState_Running, nil
	case "RunningFailed":
		return OpsTaskState_RunningFailed, nil
	case "Done":
		return OpsTaskState_Done, nil
	}
	return OpsTaskState(0), fmt.Errorf("not a valid OpsTaskState string")
}

func OpsTaskStatePtr(v OpsTaskState) *OpsTaskState { return &v }

func (p OpsTaskState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OpsTaskState) UnmarshalText(text []byte) error {
	q, err := OpsTaskStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForConsoleRecord int64

const (
	OrderByForConsoleRecord_CreateTime     OrderByForConsoleRecord = 0
	OrderByForConsoleRecord_ExecuteCost    OrderByForConsoleRecord = 1
	OrderByForConsoleRecord_InfluenceLines OrderByForConsoleRecord = 2
)

func (p OrderByForConsoleRecord) String() string {
	switch p {
	case OrderByForConsoleRecord_CreateTime:
		return "CreateTime"
	case OrderByForConsoleRecord_ExecuteCost:
		return "ExecuteCost"
	case OrderByForConsoleRecord_InfluenceLines:
		return "InfluenceLines"
	}
	return "<UNSET>"
}

func OrderByForConsoleRecordFromString(s string) (OrderByForConsoleRecord, error) {
	switch s {
	case "CreateTime":
		return OrderByForConsoleRecord_CreateTime, nil
	case "ExecuteCost":
		return OrderByForConsoleRecord_ExecuteCost, nil
	case "InfluenceLines":
		return OrderByForConsoleRecord_InfluenceLines, nil
	}
	return OrderByForConsoleRecord(0), fmt.Errorf("not a valid OrderByForConsoleRecord string")
}

func OrderByForConsoleRecordPtr(v OrderByForConsoleRecord) *OrderByForConsoleRecord { return &v }

func (p OrderByForConsoleRecord) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForConsoleRecord) UnmarshalText(text []byte) error {
	q, err := OrderByForConsoleRecordFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForDasRecord int64

const (
	OrderByForDasRecord_CreateTime OrderByForDasRecord = 0
)

func (p OrderByForDasRecord) String() string {
	switch p {
	case OrderByForDasRecord_CreateTime:
		return "CreateTime"
	}
	return "<UNSET>"
}

func OrderByForDasRecordFromString(s string) (OrderByForDasRecord, error) {
	switch s {
	case "CreateTime":
		return OrderByForDasRecord_CreateTime, nil
	}
	return OrderByForDasRecord(0), fmt.Errorf("not a valid OrderByForDasRecord string")
}

func OrderByForDasRecordPtr(v OrderByForDasRecord) *OrderByForDasRecord { return &v }

func (p OrderByForDasRecord) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForDasRecord) UnmarshalText(text []byte) error {
	q, err := OrderByForDasRecordFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlTypeForMySQL int64

const (
	SqlTypeForMySQL_SELECT   SqlTypeForMySQL = 1
	SqlTypeForMySQL_INSERT   SqlTypeForMySQL = 2
	SqlTypeForMySQL_UPDATE   SqlTypeForMySQL = 3
	SqlTypeForMySQL_DELETE   SqlTypeForMySQL = 4
	SqlTypeForMySQL_ALTER    SqlTypeForMySQL = 5
	SqlTypeForMySQL_CREATE   SqlTypeForMySQL = 6
	SqlTypeForMySQL_DROP     SqlTypeForMySQL = 7
	SqlTypeForMySQL_RENAME   SqlTypeForMySQL = 8
	SqlTypeForMySQL_TRUNCATE SqlTypeForMySQL = 9
	SqlTypeForMySQL_EXPLAIN  SqlTypeForMySQL = 10
	SqlTypeForMySQL_KILL     SqlTypeForMySQL = 11
	SqlTypeForMySQL_SHOW     SqlTypeForMySQL = 12
)

func (p SqlTypeForMySQL) String() string {
	switch p {
	case SqlTypeForMySQL_SELECT:
		return "SELECT"
	case SqlTypeForMySQL_INSERT:
		return "INSERT"
	case SqlTypeForMySQL_UPDATE:
		return "UPDATE"
	case SqlTypeForMySQL_DELETE:
		return "DELETE"
	case SqlTypeForMySQL_ALTER:
		return "ALTER"
	case SqlTypeForMySQL_CREATE:
		return "CREATE"
	case SqlTypeForMySQL_DROP:
		return "DROP"
	case SqlTypeForMySQL_RENAME:
		return "RENAME"
	case SqlTypeForMySQL_TRUNCATE:
		return "TRUNCATE"
	case SqlTypeForMySQL_EXPLAIN:
		return "EXPLAIN"
	case SqlTypeForMySQL_KILL:
		return "KILL"
	case SqlTypeForMySQL_SHOW:
		return "SHOW"
	}
	return "<UNSET>"
}

func SqlTypeForMySQLFromString(s string) (SqlTypeForMySQL, error) {
	switch s {
	case "SELECT":
		return SqlTypeForMySQL_SELECT, nil
	case "INSERT":
		return SqlTypeForMySQL_INSERT, nil
	case "UPDATE":
		return SqlTypeForMySQL_UPDATE, nil
	case "DELETE":
		return SqlTypeForMySQL_DELETE, nil
	case "ALTER":
		return SqlTypeForMySQL_ALTER, nil
	case "CREATE":
		return SqlTypeForMySQL_CREATE, nil
	case "DROP":
		return SqlTypeForMySQL_DROP, nil
	case "RENAME":
		return SqlTypeForMySQL_RENAME, nil
	case "TRUNCATE":
		return SqlTypeForMySQL_TRUNCATE, nil
	case "EXPLAIN":
		return SqlTypeForMySQL_EXPLAIN, nil
	case "KILL":
		return SqlTypeForMySQL_KILL, nil
	case "SHOW":
		return SqlTypeForMySQL_SHOW, nil
	}
	return SqlTypeForMySQL(0), fmt.Errorf("not a valid SqlTypeForMySQL string")
}

func SqlTypeForMySQLPtr(v SqlTypeForMySQL) *SqlTypeForMySQL { return &v }

func (p SqlTypeForMySQL) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlTypeForMySQL) UnmarshalText(text []byte) error {
	q, err := SqlTypeForMySQLFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlTypeForPostgreSQL int64

const (
	SqlTypeForPostgreSQL_SELECT  SqlTypeForPostgreSQL = 1
	SqlTypeForPostgreSQL_INSERT  SqlTypeForPostgreSQL = 2
	SqlTypeForPostgreSQL_UPDATE  SqlTypeForPostgreSQL = 3
	SqlTypeForPostgreSQL_DELETE  SqlTypeForPostgreSQL = 4
	SqlTypeForPostgreSQL_ALTER   SqlTypeForPostgreSQL = 5
	SqlTypeForPostgreSQL_CREATE  SqlTypeForPostgreSQL = 6
	SqlTypeForPostgreSQL_DROP    SqlTypeForPostgreSQL = 7
	SqlTypeForPostgreSQL_RENAME  SqlTypeForPostgreSQL = 8
	SqlTypeForPostgreSQL_EXPLAIN SqlTypeForPostgreSQL = 10
)

func (p SqlTypeForPostgreSQL) String() string {
	switch p {
	case SqlTypeForPostgreSQL_SELECT:
		return "SELECT"
	case SqlTypeForPostgreSQL_INSERT:
		return "INSERT"
	case SqlTypeForPostgreSQL_UPDATE:
		return "UPDATE"
	case SqlTypeForPostgreSQL_DELETE:
		return "DELETE"
	case SqlTypeForPostgreSQL_ALTER:
		return "ALTER"
	case SqlTypeForPostgreSQL_CREATE:
		return "CREATE"
	case SqlTypeForPostgreSQL_DROP:
		return "DROP"
	case SqlTypeForPostgreSQL_RENAME:
		return "RENAME"
	case SqlTypeForPostgreSQL_EXPLAIN:
		return "EXPLAIN"
	}
	return "<UNSET>"
}

func SqlTypeForPostgreSQLFromString(s string) (SqlTypeForPostgreSQL, error) {
	switch s {
	case "SELECT":
		return SqlTypeForPostgreSQL_SELECT, nil
	case "INSERT":
		return SqlTypeForPostgreSQL_INSERT, nil
	case "UPDATE":
		return SqlTypeForPostgreSQL_UPDATE, nil
	case "DELETE":
		return SqlTypeForPostgreSQL_DELETE, nil
	case "ALTER":
		return SqlTypeForPostgreSQL_ALTER, nil
	case "CREATE":
		return SqlTypeForPostgreSQL_CREATE, nil
	case "DROP":
		return SqlTypeForPostgreSQL_DROP, nil
	case "RENAME":
		return SqlTypeForPostgreSQL_RENAME, nil
	case "EXPLAIN":
		return SqlTypeForPostgreSQL_EXPLAIN, nil
	}
	return SqlTypeForPostgreSQL(0), fmt.Errorf("not a valid SqlTypeForPostgreSQL string")
}

func SqlTypeForPostgreSQLPtr(v SqlTypeForPostgreSQL) *SqlTypeForPostgreSQL { return &v }

func (p SqlTypeForPostgreSQL) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlTypeForPostgreSQL) UnmarshalText(text []byte) error {
	q, err := SqlTypeForPostgreSQLFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ConsoleRecord struct {
	ID              string `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	UserID          string `thrift:"UserID,2,required" frugal:"2,required,string" json:"UserID"`
	UserName        string `thrift:"UserName,3,required" frugal:"3,required,string" json:"UserName"`
	InstanceID      string `thrift:"InstanceID,4,required" frugal:"4,required,string" json:"InstanceID"`
	InstanceType    string `thrift:"InstanceType,5,required" frugal:"5,required,string" json:"InstanceType"`
	DbName          string `thrift:"DbName,6,required" frugal:"6,required,string" json:"DbName"`
	SqlStatement    string `thrift:"SqlStatement,7,required" frugal:"7,required,string" json:"SqlStatement"`
	SqlType         string `thrift:"SqlType,8,required" frugal:"8,required,string" json:"SqlType"`
	OperationType   int8   `thrift:"OperationType,9,required" frugal:"9,required,i8" json:"OperationType"`
	OperationStatus int8   `thrift:"OperationStatus,10,required" frugal:"10,required,i8" json:"OperationStatus"`
	InfluenceLines  int64  `thrift:"InfluenceLines,11,required" frugal:"11,required,i64" json:"InfluenceLines"`
	ExecuteCost     int64  `thrift:"ExecuteCost,12,required" frugal:"12,required,i64" json:"ExecuteCost"`
	CreateTime      int64  `thrift:"CreateTime,13,required" frugal:"13,required,i64" json:"CreateTime"`
}

func NewConsoleRecord() *ConsoleRecord {
	return &ConsoleRecord{}
}

func (p *ConsoleRecord) InitDefault() {
}

func (p *ConsoleRecord) GetID() (v string) {
	return p.ID
}

func (p *ConsoleRecord) GetUserID() (v string) {
	return p.UserID
}

func (p *ConsoleRecord) GetUserName() (v string) {
	return p.UserName
}

func (p *ConsoleRecord) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *ConsoleRecord) GetInstanceType() (v string) {
	return p.InstanceType
}

func (p *ConsoleRecord) GetDbName() (v string) {
	return p.DbName
}

func (p *ConsoleRecord) GetSqlStatement() (v string) {
	return p.SqlStatement
}

func (p *ConsoleRecord) GetSqlType() (v string) {
	return p.SqlType
}

func (p *ConsoleRecord) GetOperationType() (v int8) {
	return p.OperationType
}

func (p *ConsoleRecord) GetOperationStatus() (v int8) {
	return p.OperationStatus
}

func (p *ConsoleRecord) GetInfluenceLines() (v int64) {
	return p.InfluenceLines
}

func (p *ConsoleRecord) GetExecuteCost() (v int64) {
	return p.ExecuteCost
}

func (p *ConsoleRecord) GetCreateTime() (v int64) {
	return p.CreateTime
}
func (p *ConsoleRecord) SetID(val string) {
	p.ID = val
}
func (p *ConsoleRecord) SetUserID(val string) {
	p.UserID = val
}
func (p *ConsoleRecord) SetUserName(val string) {
	p.UserName = val
}
func (p *ConsoleRecord) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *ConsoleRecord) SetInstanceType(val string) {
	p.InstanceType = val
}
func (p *ConsoleRecord) SetDbName(val string) {
	p.DbName = val
}
func (p *ConsoleRecord) SetSqlStatement(val string) {
	p.SqlStatement = val
}
func (p *ConsoleRecord) SetSqlType(val string) {
	p.SqlType = val
}
func (p *ConsoleRecord) SetOperationType(val int8) {
	p.OperationType = val
}
func (p *ConsoleRecord) SetOperationStatus(val int8) {
	p.OperationStatus = val
}
func (p *ConsoleRecord) SetInfluenceLines(val int64) {
	p.InfluenceLines = val
}
func (p *ConsoleRecord) SetExecuteCost(val int64) {
	p.ExecuteCost = val
}
func (p *ConsoleRecord) SetCreateTime(val int64) {
	p.CreateTime = val
}

var fieldIDToName_ConsoleRecord = map[int16]string{
	1:  "ID",
	2:  "UserID",
	3:  "UserName",
	4:  "InstanceID",
	5:  "InstanceType",
	6:  "DbName",
	7:  "SqlStatement",
	8:  "SqlType",
	9:  "OperationType",
	10: "OperationStatus",
	11: "InfluenceLines",
	12: "ExecuteCost",
	13: "CreateTime",
}

func (p *ConsoleRecord) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConsoleRecord")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetUserID bool = false
	var issetUserName bool = false
	var issetInstanceID bool = false
	var issetInstanceType bool = false
	var issetDbName bool = false
	var issetSqlStatement bool = false
	var issetSqlType bool = false
	var issetOperationType bool = false
	var issetOperationStatus bool = false
	var issetInfluenceLines bool = false
	var issetExecuteCost bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperationType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperationStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetInfluenceLines = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteCost = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUserName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetSqlStatement {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetSqlType {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetOperationType {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetOperationStatus {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetInfluenceLines {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetExecuteCost {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConsoleRecord[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ConsoleRecord[fieldId]))
}

func (p *ConsoleRecord) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *ConsoleRecord) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *ConsoleRecord) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *ConsoleRecord) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *ConsoleRecord) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}
func (p *ConsoleRecord) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *ConsoleRecord) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlStatement = _field
	return nil
}
func (p *ConsoleRecord) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlType = _field
	return nil
}
func (p *ConsoleRecord) ReadField9(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperationType = _field
	return nil
}
func (p *ConsoleRecord) ReadField10(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperationStatus = _field
	return nil
}
func (p *ConsoleRecord) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InfluenceLines = _field
	return nil
}
func (p *ConsoleRecord) ReadField12(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteCost = _field
	return nil
}
func (p *ConsoleRecord) ReadField13(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}

func (p *ConsoleRecord) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConsoleRecord")

	var fieldId int16
	if err = oprot.WriteStructBegin("ConsoleRecord"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConsoleRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConsoleRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ConsoleRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ConsoleRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ConsoleRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ConsoleRecord) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ConsoleRecord) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlStatement", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlStatement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ConsoleRecord) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlType", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ConsoleRecord) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OperationType", thrift.BYTE, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.OperationType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ConsoleRecord) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OperationStatus", thrift.BYTE, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.OperationStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ConsoleRecord) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InfluenceLines", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.InfluenceLines); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ConsoleRecord) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteCost", thrift.I64, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ExecuteCost); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ConsoleRecord) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I64, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ConsoleRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsoleRecord(%+v)", *p)

}

func (p *ConsoleRecord) DeepEqual(ano *ConsoleRecord) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field7DeepEqual(ano.SqlStatement) {
		return false
	}
	if !p.Field8DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field9DeepEqual(ano.OperationType) {
		return false
	}
	if !p.Field10DeepEqual(ano.OperationStatus) {
		return false
	}
	if !p.Field11DeepEqual(ano.InfluenceLines) {
		return false
	}
	if !p.Field12DeepEqual(ano.ExecuteCost) {
		return false
	}
	if !p.Field13DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *ConsoleRecord) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field2DeepEqual(src string) bool {

	if strings.Compare(p.UserID, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field3DeepEqual(src string) bool {

	if strings.Compare(p.UserName, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field5DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field6DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field7DeepEqual(src string) bool {

	if strings.Compare(p.SqlStatement, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field8DeepEqual(src string) bool {

	if strings.Compare(p.SqlType, src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field9DeepEqual(src int8) bool {

	if p.OperationType != src {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field10DeepEqual(src int8) bool {

	if p.OperationStatus != src {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field11DeepEqual(src int64) bool {

	if p.InfluenceLines != src {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field12DeepEqual(src int64) bool {

	if p.ExecuteCost != src {
		return false
	}
	return true
}
func (p *ConsoleRecord) Field13DeepEqual(src int64) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}

type DescribeConsoleRecordListReq struct {
	PageNumber               *int32                    `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize                 *int32                    `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	ConsoleRecordSearchParam *ConsoleRecordSearchParam `thrift:"ConsoleRecordSearchParam,3,optional" frugal:"3,optional,ConsoleRecordSearchParam" json:"ConsoleRecordSearchParam,omitempty"`
	SortBy                   *SortBy                   `thrift:"SortBy,4,optional" frugal:"4,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy                  *OrderByForConsoleRecord  `thrift:"OrderBy,5,optional" frugal:"5,optional,OrderByForConsoleRecord" json:"OrderBy,omitempty"`
}

func NewDescribeConsoleRecordListReq() *DescribeConsoleRecordListReq {
	return &DescribeConsoleRecordListReq{}
}

func (p *DescribeConsoleRecordListReq) InitDefault() {
}

var DescribeConsoleRecordListReq_PageNumber_DEFAULT int32

func (p *DescribeConsoleRecordListReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeConsoleRecordListReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeConsoleRecordListReq_PageSize_DEFAULT int32

func (p *DescribeConsoleRecordListReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeConsoleRecordListReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeConsoleRecordListReq_ConsoleRecordSearchParam_DEFAULT *ConsoleRecordSearchParam

func (p *DescribeConsoleRecordListReq) GetConsoleRecordSearchParam() (v *ConsoleRecordSearchParam) {
	if !p.IsSetConsoleRecordSearchParam() {
		return DescribeConsoleRecordListReq_ConsoleRecordSearchParam_DEFAULT
	}
	return p.ConsoleRecordSearchParam
}

var DescribeConsoleRecordListReq_SortBy_DEFAULT SortBy

func (p *DescribeConsoleRecordListReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeConsoleRecordListReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeConsoleRecordListReq_OrderBy_DEFAULT OrderByForConsoleRecord

func (p *DescribeConsoleRecordListReq) GetOrderBy() (v OrderByForConsoleRecord) {
	if !p.IsSetOrderBy() {
		return DescribeConsoleRecordListReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}
func (p *DescribeConsoleRecordListReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeConsoleRecordListReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeConsoleRecordListReq) SetConsoleRecordSearchParam(val *ConsoleRecordSearchParam) {
	p.ConsoleRecordSearchParam = val
}
func (p *DescribeConsoleRecordListReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeConsoleRecordListReq) SetOrderBy(val *OrderByForConsoleRecord) {
	p.OrderBy = val
}

var fieldIDToName_DescribeConsoleRecordListReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "ConsoleRecordSearchParam",
	4: "SortBy",
	5: "OrderBy",
}

func (p *DescribeConsoleRecordListReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeConsoleRecordListReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeConsoleRecordListReq) IsSetConsoleRecordSearchParam() bool {
	return p.ConsoleRecordSearchParam != nil
}

func (p *DescribeConsoleRecordListReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeConsoleRecordListReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeConsoleRecordListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleRecordListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeConsoleRecordListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeConsoleRecordListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeConsoleRecordListReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewConsoleRecordSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ConsoleRecordSearchParam = _field
	return nil
}
func (p *DescribeConsoleRecordListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeConsoleRecordListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *OrderByForConsoleRecord
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForConsoleRecord(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}

func (p *DescribeConsoleRecordListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleRecordListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeConsoleRecordListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConsoleRecordSearchParam() {
		if err = oprot.WriteFieldBegin("ConsoleRecordSearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ConsoleRecordSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeConsoleRecordListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeConsoleRecordListReq(%+v)", *p)

}

func (p *DescribeConsoleRecordListReq) DeepEqual(ano *DescribeConsoleRecordListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConsoleRecordSearchParam) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	return true
}

func (p *DescribeConsoleRecordListReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeConsoleRecordListReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeConsoleRecordListReq) Field3DeepEqual(src *ConsoleRecordSearchParam) bool {

	if !p.ConsoleRecordSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeConsoleRecordListReq) Field4DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeConsoleRecordListReq) Field5DeepEqual(src *OrderByForConsoleRecord) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}

type ConsoleRecordSearchParam struct {
	SqlStatement      *string  `thrift:"SqlStatement,1,optional" frugal:"1,optional,string" json:"SqlStatement,omitempty"`
	StartTime         *string  `thrift:"StartTime,2,optional" frugal:"2,optional,string" json:"StartTime,omitempty"`
	EndTime           *string  `thrift:"EndTime,3,optional" frugal:"3,optional,string" json:"EndTime,omitempty"`
	InstanceID        *string  `thrift:"InstanceID,4,optional" frugal:"4,optional,string" json:"InstanceID,omitempty"`
	InfluenceLinesMax *string  `thrift:"InfluenceLinesMax,5,optional" frugal:"5,optional,string" json:"InfluenceLinesMax,omitempty"`
	InfluenceLinesMin *string  `thrift:"InfluenceLinesMin,6,optional" frugal:"6,optional,string" json:"InfluenceLinesMin,omitempty"`
	ExecuteCostMax    *string  `thrift:"ExecuteCostMax,7,optional" frugal:"7,optional,string" json:"ExecuteCostMax,omitempty"`
	ExecuteCostMin    *string  `thrift:"ExecuteCostMin,8,optional" frugal:"8,optional,string" json:"ExecuteCostMin,omitempty"`
	OperationType     *string  `thrift:"OperationType,9,optional" frugal:"9,optional,string" json:"OperationType,omitempty"`
	SqlTypeList       []string `thrift:"SqlTypeList,10,optional" frugal:"10,optional,list<string>" json:"SqlTypeList,omitempty"`
	OperationStatus   *string  `thrift:"OperationStatus,11,optional" frugal:"11,optional,string" json:"OperationStatus,omitempty"`
	InstanceType      *string  `thrift:"InstanceType,12,optional" frugal:"12,optional,string" json:"InstanceType,omitempty"`
	CreateUserId      *string  `thrift:"CreateUserId,13,optional" frugal:"13,optional,string" json:"CreateUserId,omitempty"`
	CreateUserName    *string  `thrift:"CreateUserName,14,optional" frugal:"14,optional,string" json:"CreateUserName,omitempty"`
	DbName            *string  `thrift:"DbName,15,optional" frugal:"15,optional,string" json:"DbName,omitempty"`
}

func NewConsoleRecordSearchParam() *ConsoleRecordSearchParam {
	return &ConsoleRecordSearchParam{}
}

func (p *ConsoleRecordSearchParam) InitDefault() {
}

var ConsoleRecordSearchParam_SqlStatement_DEFAULT string

func (p *ConsoleRecordSearchParam) GetSqlStatement() (v string) {
	if !p.IsSetSqlStatement() {
		return ConsoleRecordSearchParam_SqlStatement_DEFAULT
	}
	return *p.SqlStatement
}

var ConsoleRecordSearchParam_StartTime_DEFAULT string

func (p *ConsoleRecordSearchParam) GetStartTime() (v string) {
	if !p.IsSetStartTime() {
		return ConsoleRecordSearchParam_StartTime_DEFAULT
	}
	return *p.StartTime
}

var ConsoleRecordSearchParam_EndTime_DEFAULT string

func (p *ConsoleRecordSearchParam) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return ConsoleRecordSearchParam_EndTime_DEFAULT
	}
	return *p.EndTime
}

var ConsoleRecordSearchParam_InstanceID_DEFAULT string

func (p *ConsoleRecordSearchParam) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return ConsoleRecordSearchParam_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var ConsoleRecordSearchParam_InfluenceLinesMax_DEFAULT string

func (p *ConsoleRecordSearchParam) GetInfluenceLinesMax() (v string) {
	if !p.IsSetInfluenceLinesMax() {
		return ConsoleRecordSearchParam_InfluenceLinesMax_DEFAULT
	}
	return *p.InfluenceLinesMax
}

var ConsoleRecordSearchParam_InfluenceLinesMin_DEFAULT string

func (p *ConsoleRecordSearchParam) GetInfluenceLinesMin() (v string) {
	if !p.IsSetInfluenceLinesMin() {
		return ConsoleRecordSearchParam_InfluenceLinesMin_DEFAULT
	}
	return *p.InfluenceLinesMin
}

var ConsoleRecordSearchParam_ExecuteCostMax_DEFAULT string

func (p *ConsoleRecordSearchParam) GetExecuteCostMax() (v string) {
	if !p.IsSetExecuteCostMax() {
		return ConsoleRecordSearchParam_ExecuteCostMax_DEFAULT
	}
	return *p.ExecuteCostMax
}

var ConsoleRecordSearchParam_ExecuteCostMin_DEFAULT string

func (p *ConsoleRecordSearchParam) GetExecuteCostMin() (v string) {
	if !p.IsSetExecuteCostMin() {
		return ConsoleRecordSearchParam_ExecuteCostMin_DEFAULT
	}
	return *p.ExecuteCostMin
}

var ConsoleRecordSearchParam_OperationType_DEFAULT string

func (p *ConsoleRecordSearchParam) GetOperationType() (v string) {
	if !p.IsSetOperationType() {
		return ConsoleRecordSearchParam_OperationType_DEFAULT
	}
	return *p.OperationType
}

var ConsoleRecordSearchParam_SqlTypeList_DEFAULT []string

func (p *ConsoleRecordSearchParam) GetSqlTypeList() (v []string) {
	if !p.IsSetSqlTypeList() {
		return ConsoleRecordSearchParam_SqlTypeList_DEFAULT
	}
	return p.SqlTypeList
}

var ConsoleRecordSearchParam_OperationStatus_DEFAULT string

func (p *ConsoleRecordSearchParam) GetOperationStatus() (v string) {
	if !p.IsSetOperationStatus() {
		return ConsoleRecordSearchParam_OperationStatus_DEFAULT
	}
	return *p.OperationStatus
}

var ConsoleRecordSearchParam_InstanceType_DEFAULT string

func (p *ConsoleRecordSearchParam) GetInstanceType() (v string) {
	if !p.IsSetInstanceType() {
		return ConsoleRecordSearchParam_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ConsoleRecordSearchParam_CreateUserId_DEFAULT string

func (p *ConsoleRecordSearchParam) GetCreateUserId() (v string) {
	if !p.IsSetCreateUserId() {
		return ConsoleRecordSearchParam_CreateUserId_DEFAULT
	}
	return *p.CreateUserId
}

var ConsoleRecordSearchParam_CreateUserName_DEFAULT string

func (p *ConsoleRecordSearchParam) GetCreateUserName() (v string) {
	if !p.IsSetCreateUserName() {
		return ConsoleRecordSearchParam_CreateUserName_DEFAULT
	}
	return *p.CreateUserName
}

var ConsoleRecordSearchParam_DbName_DEFAULT string

func (p *ConsoleRecordSearchParam) GetDbName() (v string) {
	if !p.IsSetDbName() {
		return ConsoleRecordSearchParam_DbName_DEFAULT
	}
	return *p.DbName
}
func (p *ConsoleRecordSearchParam) SetSqlStatement(val *string) {
	p.SqlStatement = val
}
func (p *ConsoleRecordSearchParam) SetStartTime(val *string) {
	p.StartTime = val
}
func (p *ConsoleRecordSearchParam) SetEndTime(val *string) {
	p.EndTime = val
}
func (p *ConsoleRecordSearchParam) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *ConsoleRecordSearchParam) SetInfluenceLinesMax(val *string) {
	p.InfluenceLinesMax = val
}
func (p *ConsoleRecordSearchParam) SetInfluenceLinesMin(val *string) {
	p.InfluenceLinesMin = val
}
func (p *ConsoleRecordSearchParam) SetExecuteCostMax(val *string) {
	p.ExecuteCostMax = val
}
func (p *ConsoleRecordSearchParam) SetExecuteCostMin(val *string) {
	p.ExecuteCostMin = val
}
func (p *ConsoleRecordSearchParam) SetOperationType(val *string) {
	p.OperationType = val
}
func (p *ConsoleRecordSearchParam) SetSqlTypeList(val []string) {
	p.SqlTypeList = val
}
func (p *ConsoleRecordSearchParam) SetOperationStatus(val *string) {
	p.OperationStatus = val
}
func (p *ConsoleRecordSearchParam) SetInstanceType(val *string) {
	p.InstanceType = val
}
func (p *ConsoleRecordSearchParam) SetCreateUserId(val *string) {
	p.CreateUserId = val
}
func (p *ConsoleRecordSearchParam) SetCreateUserName(val *string) {
	p.CreateUserName = val
}
func (p *ConsoleRecordSearchParam) SetDbName(val *string) {
	p.DbName = val
}

var fieldIDToName_ConsoleRecordSearchParam = map[int16]string{
	1:  "SqlStatement",
	2:  "StartTime",
	3:  "EndTime",
	4:  "InstanceID",
	5:  "InfluenceLinesMax",
	6:  "InfluenceLinesMin",
	7:  "ExecuteCostMax",
	8:  "ExecuteCostMin",
	9:  "OperationType",
	10: "SqlTypeList",
	11: "OperationStatus",
	12: "InstanceType",
	13: "CreateUserId",
	14: "CreateUserName",
	15: "DbName",
}

func (p *ConsoleRecordSearchParam) IsSetSqlStatement() bool {
	return p.SqlStatement != nil
}

func (p *ConsoleRecordSearchParam) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *ConsoleRecordSearchParam) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *ConsoleRecordSearchParam) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *ConsoleRecordSearchParam) IsSetInfluenceLinesMax() bool {
	return p.InfluenceLinesMax != nil
}

func (p *ConsoleRecordSearchParam) IsSetInfluenceLinesMin() bool {
	return p.InfluenceLinesMin != nil
}

func (p *ConsoleRecordSearchParam) IsSetExecuteCostMax() bool {
	return p.ExecuteCostMax != nil
}

func (p *ConsoleRecordSearchParam) IsSetExecuteCostMin() bool {
	return p.ExecuteCostMin != nil
}

func (p *ConsoleRecordSearchParam) IsSetOperationType() bool {
	return p.OperationType != nil
}

func (p *ConsoleRecordSearchParam) IsSetSqlTypeList() bool {
	return p.SqlTypeList != nil
}

func (p *ConsoleRecordSearchParam) IsSetOperationStatus() bool {
	return p.OperationStatus != nil
}

func (p *ConsoleRecordSearchParam) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ConsoleRecordSearchParam) IsSetCreateUserId() bool {
	return p.CreateUserId != nil
}

func (p *ConsoleRecordSearchParam) IsSetCreateUserName() bool {
	return p.CreateUserName != nil
}

func (p *ConsoleRecordSearchParam) IsSetDbName() bool {
	return p.DbName != nil
}

func (p *ConsoleRecordSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConsoleRecordSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConsoleRecordSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlStatement = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InfluenceLinesMax = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InfluenceLinesMin = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteCostMax = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteCostMin = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OperationType = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlTypeList = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OperationStatus = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceType = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserId = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserName = _field
	return nil
}
func (p *ConsoleRecordSearchParam) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DbName = _field
	return nil
}

func (p *ConsoleRecordSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConsoleRecordSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("ConsoleRecordSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlStatement() {
		if err = oprot.WriteFieldBegin("SqlStatement", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlStatement); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInfluenceLinesMax() {
		if err = oprot.WriteFieldBegin("InfluenceLinesMax", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InfluenceLinesMax); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInfluenceLinesMin() {
		if err = oprot.WriteFieldBegin("InfluenceLinesMin", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InfluenceLinesMin); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteCostMax() {
		if err = oprot.WriteFieldBegin("ExecuteCostMax", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExecuteCostMax); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteCostMin() {
		if err = oprot.WriteFieldBegin("ExecuteCostMin", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExecuteCostMin); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetOperationType() {
		if err = oprot.WriteFieldBegin("OperationType", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OperationType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTypeList() {
		if err = oprot.WriteFieldBegin("SqlTypeList", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SqlTypeList)); err != nil {
			return err
		}
		for _, v := range p.SqlTypeList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetOperationStatus() {
		if err = oprot.WriteFieldBegin("OperationStatus", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OperationStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserId() {
		if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserName() {
		if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetDbName() {
		if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DbName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *ConsoleRecordSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsoleRecordSearchParam(%+v)", *p)

}

func (p *ConsoleRecordSearchParam) DeepEqual(ano *ConsoleRecordSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlStatement) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field5DeepEqual(ano.InfluenceLinesMax) {
		return false
	}
	if !p.Field6DeepEqual(ano.InfluenceLinesMin) {
		return false
	}
	if !p.Field7DeepEqual(ano.ExecuteCostMax) {
		return false
	}
	if !p.Field8DeepEqual(ano.ExecuteCostMin) {
		return false
	}
	if !p.Field9DeepEqual(ano.OperationType) {
		return false
	}
	if !p.Field10DeepEqual(ano.SqlTypeList) {
		return false
	}
	if !p.Field11DeepEqual(ano.OperationStatus) {
		return false
	}
	if !p.Field12DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field13DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field14DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field15DeepEqual(ano.DbName) {
		return false
	}
	return true
}

func (p *ConsoleRecordSearchParam) Field1DeepEqual(src *string) bool {

	if p.SqlStatement == src {
		return true
	} else if p.SqlStatement == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlStatement, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field2DeepEqual(src *string) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field3DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field4DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field5DeepEqual(src *string) bool {

	if p.InfluenceLinesMax == src {
		return true
	} else if p.InfluenceLinesMax == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InfluenceLinesMax, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field6DeepEqual(src *string) bool {

	if p.InfluenceLinesMin == src {
		return true
	} else if p.InfluenceLinesMin == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InfluenceLinesMin, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field7DeepEqual(src *string) bool {

	if p.ExecuteCostMax == src {
		return true
	} else if p.ExecuteCostMax == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ExecuteCostMax, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field8DeepEqual(src *string) bool {

	if p.ExecuteCostMin == src {
		return true
	} else if p.ExecuteCostMin == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ExecuteCostMin, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field9DeepEqual(src *string) bool {

	if p.OperationType == src {
		return true
	} else if p.OperationType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OperationType, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field10DeepEqual(src []string) bool {

	if len(p.SqlTypeList) != len(src) {
		return false
	}
	for i, v := range p.SqlTypeList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field11DeepEqual(src *string) bool {

	if p.OperationStatus == src {
		return true
	} else if p.OperationStatus == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OperationStatus, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field12DeepEqual(src *string) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceType, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field13DeepEqual(src *string) bool {

	if p.CreateUserId == src {
		return true
	} else if p.CreateUserId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserId, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field14DeepEqual(src *string) bool {

	if p.CreateUserName == src {
		return true
	} else if p.CreateUserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserName, *src) != 0 {
		return false
	}
	return true
}
func (p *ConsoleRecordSearchParam) Field15DeepEqual(src *string) bool {

	if p.DbName == src {
		return true
	} else if p.DbName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DbName, *src) != 0 {
		return false
	}
	return true
}

type DescribeConsoleRecordListResp struct {
	ConsoleRecordList []*ConsoleRecord `thrift:"ConsoleRecordList,1,required" frugal:"1,required,list<ConsoleRecord>" json:"ConsoleRecordList"`
	Total             int64            `thrift:"Total,2,required" frugal:"2,required,i64" json:"Total"`
}

func NewDescribeConsoleRecordListResp() *DescribeConsoleRecordListResp {
	return &DescribeConsoleRecordListResp{}
}

func (p *DescribeConsoleRecordListResp) InitDefault() {
}

func (p *DescribeConsoleRecordListResp) GetConsoleRecordList() (v []*ConsoleRecord) {
	return p.ConsoleRecordList
}

func (p *DescribeConsoleRecordListResp) GetTotal() (v int64) {
	return p.Total
}
func (p *DescribeConsoleRecordListResp) SetConsoleRecordList(val []*ConsoleRecord) {
	p.ConsoleRecordList = val
}
func (p *DescribeConsoleRecordListResp) SetTotal(val int64) {
	p.Total = val
}

var fieldIDToName_DescribeConsoleRecordListResp = map[int16]string{
	1: "ConsoleRecordList",
	2: "Total",
}

func (p *DescribeConsoleRecordListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleRecordListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConsoleRecordList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConsoleRecordList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConsoleRecordList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeConsoleRecordListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeConsoleRecordListResp[fieldId]))
}

func (p *DescribeConsoleRecordListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ConsoleRecord, 0, size)
	values := make([]ConsoleRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConsoleRecordList = _field
	return nil
}
func (p *DescribeConsoleRecordListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeConsoleRecordListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleRecordListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeConsoleRecordListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeConsoleRecordListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConsoleRecordList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConsoleRecordList)); err != nil {
		return err
	}
	for _, v := range p.ConsoleRecordList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeConsoleRecordListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeConsoleRecordListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeConsoleRecordListResp(%+v)", *p)

}

func (p *DescribeConsoleRecordListResp) DeepEqual(ano *DescribeConsoleRecordListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ConsoleRecordList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeConsoleRecordListResp) Field1DeepEqual(src []*ConsoleRecord) bool {

	if len(p.ConsoleRecordList) != len(src) {
		return false
	}
	for i, v := range p.ConsoleRecordList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeConsoleRecordListResp) Field2DeepEqual(src int64) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeTicketRecordListReq struct {
	PageNumber              *int32                   `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize                *int32                   `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	TicketRecordSearchParam *TicketRecordSearchParam `thrift:"TicketRecordSearchParam,3,optional" frugal:"3,optional,TicketRecordSearchParam" json:"TicketRecordSearchParam,omitempty"`
	SortBy                  *SortBy                  `thrift:"SortBy,4,optional" frugal:"4,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy                 *OrderByForTicket        `thrift:"OrderBy,5,optional" frugal:"5,optional,OrderByForTicket" json:"OrderBy,omitempty"`
	CreateFrom              *string                  `thrift:"CreateFrom,6,optional" frugal:"6,optional,string" json:"CreateFrom,omitempty"`
}

func NewDescribeTicketRecordListReq() *DescribeTicketRecordListReq {
	return &DescribeTicketRecordListReq{}
}

func (p *DescribeTicketRecordListReq) InitDefault() {
}

var DescribeTicketRecordListReq_PageNumber_DEFAULT int32

func (p *DescribeTicketRecordListReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTicketRecordListReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTicketRecordListReq_PageSize_DEFAULT int32

func (p *DescribeTicketRecordListReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTicketRecordListReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTicketRecordListReq_TicketRecordSearchParam_DEFAULT *TicketRecordSearchParam

func (p *DescribeTicketRecordListReq) GetTicketRecordSearchParam() (v *TicketRecordSearchParam) {
	if !p.IsSetTicketRecordSearchParam() {
		return DescribeTicketRecordListReq_TicketRecordSearchParam_DEFAULT
	}
	return p.TicketRecordSearchParam
}

var DescribeTicketRecordListReq_SortBy_DEFAULT SortBy

func (p *DescribeTicketRecordListReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeTicketRecordListReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeTicketRecordListReq_OrderBy_DEFAULT OrderByForTicket

func (p *DescribeTicketRecordListReq) GetOrderBy() (v OrderByForTicket) {
	if !p.IsSetOrderBy() {
		return DescribeTicketRecordListReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeTicketRecordListReq_CreateFrom_DEFAULT string

func (p *DescribeTicketRecordListReq) GetCreateFrom() (v string) {
	if !p.IsSetCreateFrom() {
		return DescribeTicketRecordListReq_CreateFrom_DEFAULT
	}
	return *p.CreateFrom
}
func (p *DescribeTicketRecordListReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTicketRecordListReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTicketRecordListReq) SetTicketRecordSearchParam(val *TicketRecordSearchParam) {
	p.TicketRecordSearchParam = val
}
func (p *DescribeTicketRecordListReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeTicketRecordListReq) SetOrderBy(val *OrderByForTicket) {
	p.OrderBy = val
}
func (p *DescribeTicketRecordListReq) SetCreateFrom(val *string) {
	p.CreateFrom = val
}

var fieldIDToName_DescribeTicketRecordListReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "TicketRecordSearchParam",
	4: "SortBy",
	5: "OrderBy",
	6: "CreateFrom",
}

func (p *DescribeTicketRecordListReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTicketRecordListReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTicketRecordListReq) IsSetTicketRecordSearchParam() bool {
	return p.TicketRecordSearchParam != nil
}

func (p *DescribeTicketRecordListReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeTicketRecordListReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeTicketRecordListReq) IsSetCreateFrom() bool {
	return p.CreateFrom != nil
}

func (p *DescribeTicketRecordListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketRecordListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketRecordListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTicketRecordListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTicketRecordListReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewTicketRecordSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TicketRecordSearchParam = _field
	return nil
}
func (p *DescribeTicketRecordListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeTicketRecordListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *OrderByForTicket
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForTicket(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeTicketRecordListReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateFrom = _field
	return nil
}

func (p *DescribeTicketRecordListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketRecordListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketRecordListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketRecordSearchParam() {
		if err = oprot.WriteFieldBegin("TicketRecordSearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TicketRecordSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateFrom() {
		if err = oprot.WriteFieldBegin("CreateFrom", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateFrom); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTicketRecordListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketRecordListReq(%+v)", *p)

}

func (p *DescribeTicketRecordListReq) DeepEqual(ano *DescribeTicketRecordListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketRecordSearchParam) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateFrom) {
		return false
	}
	return true
}

func (p *DescribeTicketRecordListReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTicketRecordListReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTicketRecordListReq) Field3DeepEqual(src *TicketRecordSearchParam) bool {

	if !p.TicketRecordSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketRecordListReq) Field4DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeTicketRecordListReq) Field5DeepEqual(src *OrderByForTicket) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeTicketRecordListReq) Field6DeepEqual(src *string) bool {

	if p.CreateFrom == src {
		return true
	} else if p.CreateFrom == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateFrom, *src) != 0 {
		return false
	}
	return true
}

type TicketRecordSearchParam struct {
	UptdateStartTime *string       `thrift:"UptdateStartTime,1,optional" frugal:"1,optional,string" json:"UptdateStartTime,omitempty"`
	UptdateEndTime   *string       `thrift:"UptdateEndTime,2,optional" frugal:"2,optional,string" json:"UptdateEndTime,omitempty"`
	InstanceID       *string       `thrift:"InstanceID,3,optional" frugal:"3,optional,string" json:"InstanceID,omitempty"`
	CreateUserId     *string       `thrift:"CreateUserId,4,optional" frugal:"4,optional,string" json:"CreateUserId,omitempty"`
	CreateUserName   *string       `thrift:"CreateUserName,5,optional" frugal:"5,optional,string" json:"CreateUserName,omitempty"`
	DbName           *string       `thrift:"DbName,6,optional" frugal:"6,optional,string" json:"DbName,omitempty"`
	TicketID         *string       `thrift:"TicketID,7,optional" frugal:"7,optional,string" json:"TicketID,omitempty"`
	TicketStatus     *TicketStatus `thrift:"TicketStatus,8,optional" frugal:"8,optional,TicketStatus" json:"TicketStatus,omitempty"`
	TicketType       *TicketType   `thrift:"TicketType,9,optional" frugal:"9,optional,TicketType" json:"TicketType,omitempty"`
}

func NewTicketRecordSearchParam() *TicketRecordSearchParam {
	return &TicketRecordSearchParam{}
}

func (p *TicketRecordSearchParam) InitDefault() {
}

var TicketRecordSearchParam_UptdateStartTime_DEFAULT string

func (p *TicketRecordSearchParam) GetUptdateStartTime() (v string) {
	if !p.IsSetUptdateStartTime() {
		return TicketRecordSearchParam_UptdateStartTime_DEFAULT
	}
	return *p.UptdateStartTime
}

var TicketRecordSearchParam_UptdateEndTime_DEFAULT string

func (p *TicketRecordSearchParam) GetUptdateEndTime() (v string) {
	if !p.IsSetUptdateEndTime() {
		return TicketRecordSearchParam_UptdateEndTime_DEFAULT
	}
	return *p.UptdateEndTime
}

var TicketRecordSearchParam_InstanceID_DEFAULT string

func (p *TicketRecordSearchParam) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return TicketRecordSearchParam_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var TicketRecordSearchParam_CreateUserId_DEFAULT string

func (p *TicketRecordSearchParam) GetCreateUserId() (v string) {
	if !p.IsSetCreateUserId() {
		return TicketRecordSearchParam_CreateUserId_DEFAULT
	}
	return *p.CreateUserId
}

var TicketRecordSearchParam_CreateUserName_DEFAULT string

func (p *TicketRecordSearchParam) GetCreateUserName() (v string) {
	if !p.IsSetCreateUserName() {
		return TicketRecordSearchParam_CreateUserName_DEFAULT
	}
	return *p.CreateUserName
}

var TicketRecordSearchParam_DbName_DEFAULT string

func (p *TicketRecordSearchParam) GetDbName() (v string) {
	if !p.IsSetDbName() {
		return TicketRecordSearchParam_DbName_DEFAULT
	}
	return *p.DbName
}

var TicketRecordSearchParam_TicketID_DEFAULT string

func (p *TicketRecordSearchParam) GetTicketID() (v string) {
	if !p.IsSetTicketID() {
		return TicketRecordSearchParam_TicketID_DEFAULT
	}
	return *p.TicketID
}

var TicketRecordSearchParam_TicketStatus_DEFAULT TicketStatus

func (p *TicketRecordSearchParam) GetTicketStatus() (v TicketStatus) {
	if !p.IsSetTicketStatus() {
		return TicketRecordSearchParam_TicketStatus_DEFAULT
	}
	return *p.TicketStatus
}

var TicketRecordSearchParam_TicketType_DEFAULT TicketType

func (p *TicketRecordSearchParam) GetTicketType() (v TicketType) {
	if !p.IsSetTicketType() {
		return TicketRecordSearchParam_TicketType_DEFAULT
	}
	return *p.TicketType
}
func (p *TicketRecordSearchParam) SetUptdateStartTime(val *string) {
	p.UptdateStartTime = val
}
func (p *TicketRecordSearchParam) SetUptdateEndTime(val *string) {
	p.UptdateEndTime = val
}
func (p *TicketRecordSearchParam) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *TicketRecordSearchParam) SetCreateUserId(val *string) {
	p.CreateUserId = val
}
func (p *TicketRecordSearchParam) SetCreateUserName(val *string) {
	p.CreateUserName = val
}
func (p *TicketRecordSearchParam) SetDbName(val *string) {
	p.DbName = val
}
func (p *TicketRecordSearchParam) SetTicketID(val *string) {
	p.TicketID = val
}
func (p *TicketRecordSearchParam) SetTicketStatus(val *TicketStatus) {
	p.TicketStatus = val
}
func (p *TicketRecordSearchParam) SetTicketType(val *TicketType) {
	p.TicketType = val
}

var fieldIDToName_TicketRecordSearchParam = map[int16]string{
	1: "UptdateStartTime",
	2: "UptdateEndTime",
	3: "InstanceID",
	4: "CreateUserId",
	5: "CreateUserName",
	6: "DbName",
	7: "TicketID",
	8: "TicketStatus",
	9: "TicketType",
}

func (p *TicketRecordSearchParam) IsSetUptdateStartTime() bool {
	return p.UptdateStartTime != nil
}

func (p *TicketRecordSearchParam) IsSetUptdateEndTime() bool {
	return p.UptdateEndTime != nil
}

func (p *TicketRecordSearchParam) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *TicketRecordSearchParam) IsSetCreateUserId() bool {
	return p.CreateUserId != nil
}

func (p *TicketRecordSearchParam) IsSetCreateUserName() bool {
	return p.CreateUserName != nil
}

func (p *TicketRecordSearchParam) IsSetDbName() bool {
	return p.DbName != nil
}

func (p *TicketRecordSearchParam) IsSetTicketID() bool {
	return p.TicketID != nil
}

func (p *TicketRecordSearchParam) IsSetTicketStatus() bool {
	return p.TicketStatus != nil
}

func (p *TicketRecordSearchParam) IsSetTicketType() bool {
	return p.TicketType != nil
}

func (p *TicketRecordSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketRecordSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TicketRecordSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TicketRecordSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UptdateStartTime = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UptdateEndTime = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserId = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserName = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DbName = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TicketID = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *TicketStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketStatus(v)
		_field = &tmp
	}
	p.TicketStatus = _field
	return nil
}
func (p *TicketRecordSearchParam) ReadField9(iprot thrift.TProtocol) error {

	var _field *TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketType(v)
		_field = &tmp
	}
	p.TicketType = _field
	return nil
}

func (p *TicketRecordSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketRecordSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("TicketRecordSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUptdateStartTime() {
		if err = oprot.WriteFieldBegin("UptdateStartTime", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UptdateStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUptdateEndTime() {
		if err = oprot.WriteFieldBegin("UptdateEndTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UptdateEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserId() {
		if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserName() {
		if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDbName() {
		if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DbName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketID() {
		if err = oprot.WriteFieldBegin("TicketID", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TicketID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketStatus() {
		if err = oprot.WriteFieldBegin("TicketStatus", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TicketRecordSearchParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketType() {
		if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *TicketRecordSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TicketRecordSearchParam(%+v)", *p)

}

func (p *TicketRecordSearchParam) DeepEqual(ano *TicketRecordSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UptdateStartTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.UptdateEndTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field6DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field7DeepEqual(ano.TicketID) {
		return false
	}
	if !p.Field8DeepEqual(ano.TicketStatus) {
		return false
	}
	if !p.Field9DeepEqual(ano.TicketType) {
		return false
	}
	return true
}

func (p *TicketRecordSearchParam) Field1DeepEqual(src *string) bool {

	if p.UptdateStartTime == src {
		return true
	} else if p.UptdateStartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UptdateStartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field2DeepEqual(src *string) bool {

	if p.UptdateEndTime == src {
		return true
	} else if p.UptdateEndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UptdateEndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field3DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field4DeepEqual(src *string) bool {

	if p.CreateUserId == src {
		return true
	} else if p.CreateUserId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserId, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field5DeepEqual(src *string) bool {

	if p.CreateUserName == src {
		return true
	} else if p.CreateUserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserName, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field6DeepEqual(src *string) bool {

	if p.DbName == src {
		return true
	} else if p.DbName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DbName, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field7DeepEqual(src *string) bool {

	if p.TicketID == src {
		return true
	} else if p.TicketID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TicketID, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field8DeepEqual(src *TicketStatus) bool {

	if p.TicketStatus == src {
		return true
	} else if p.TicketStatus == nil || src == nil {
		return false
	}
	if *p.TicketStatus != *src {
		return false
	}
	return true
}
func (p *TicketRecordSearchParam) Field9DeepEqual(src *TicketType) bool {

	if p.TicketType == src {
		return true
	} else if p.TicketType == nil || src == nil {
		return false
	}
	if *p.TicketType != *src {
		return false
	}
	return true
}

type DescribeTicketRecordListResp struct {
	TicketRecordList []*TicketRecord `thrift:"TicketRecordList,1,required" frugal:"1,required,list<TicketRecord>" json:"TicketRecordList"`
	Total            int64           `thrift:"Total,2,required" frugal:"2,required,i64" json:"Total"`
}

func NewDescribeTicketRecordListResp() *DescribeTicketRecordListResp {
	return &DescribeTicketRecordListResp{}
}

func (p *DescribeTicketRecordListResp) InitDefault() {
}

func (p *DescribeTicketRecordListResp) GetTicketRecordList() (v []*TicketRecord) {
	return p.TicketRecordList
}

func (p *DescribeTicketRecordListResp) GetTotal() (v int64) {
	return p.Total
}
func (p *DescribeTicketRecordListResp) SetTicketRecordList(val []*TicketRecord) {
	p.TicketRecordList = val
}
func (p *DescribeTicketRecordListResp) SetTotal(val int64) {
	p.Total = val
}

var fieldIDToName_DescribeTicketRecordListResp = map[int16]string{
	1: "TicketRecordList",
	2: "Total",
}

func (p *DescribeTicketRecordListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketRecordListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketRecordList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketRecordList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketRecordList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketRecordListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketRecordListResp[fieldId]))
}

func (p *DescribeTicketRecordListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TicketRecord, 0, size)
	values := make([]TicketRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TicketRecordList = _field
	return nil
}
func (p *DescribeTicketRecordListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTicketRecordListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketRecordListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketRecordListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketRecordListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketRecordList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TicketRecordList)); err != nil {
		return err
	}
	for _, v := range p.TicketRecordList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketRecordListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketRecordListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketRecordListResp(%+v)", *p)

}

func (p *DescribeTicketRecordListResp) DeepEqual(ano *DescribeTicketRecordListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketRecordList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTicketRecordListResp) Field1DeepEqual(src []*TicketRecord) bool {

	if len(p.TicketRecordList) != len(src) {
		return false
	}
	for i, v := range p.TicketRecordList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTicketRecordListResp) Field2DeepEqual(src int64) bool {

	if p.Total != src {
		return false
	}
	return true
}

type TicketRecord struct {
	TicketId       string       `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	TicketType     *TicketType  `thrift:"TicketType,2,optional" frugal:"2,optional,TicketType" json:"TicketType,omitempty"`
	TicketStatus   TicketStatus `thrift:"TicketStatus,3,required" frugal:"3,required,TicketStatus" json:"TicketStatus"`
	CreateUserName string       `thrift:"CreateUserName,4,required" frugal:"4,required,string" json:"CreateUserName"`
	CreateUserId   string       `thrift:"CreateUserId,5,required" frugal:"5,required,string" json:"CreateUserId"`
	DbName         *string      `thrift:"DbName,6,optional" frugal:"6,optional,string" json:"DbName,omitempty"`
	InstanceID     *string      `thrift:"InstanceID,7,optional" frugal:"7,optional,string" json:"InstanceID,omitempty"`
	UpdateTime     int64        `thrift:"UpdateTime,8,required" frugal:"8,required,i64" json:"UpdateTime"`
}

func NewTicketRecord() *TicketRecord {
	return &TicketRecord{}
}

func (p *TicketRecord) InitDefault() {
}

func (p *TicketRecord) GetTicketId() (v string) {
	return p.TicketId
}

var TicketRecord_TicketType_DEFAULT TicketType

func (p *TicketRecord) GetTicketType() (v TicketType) {
	if !p.IsSetTicketType() {
		return TicketRecord_TicketType_DEFAULT
	}
	return *p.TicketType
}

func (p *TicketRecord) GetTicketStatus() (v TicketStatus) {
	return p.TicketStatus
}

func (p *TicketRecord) GetCreateUserName() (v string) {
	return p.CreateUserName
}

func (p *TicketRecord) GetCreateUserId() (v string) {
	return p.CreateUserId
}

var TicketRecord_DbName_DEFAULT string

func (p *TicketRecord) GetDbName() (v string) {
	if !p.IsSetDbName() {
		return TicketRecord_DbName_DEFAULT
	}
	return *p.DbName
}

var TicketRecord_InstanceID_DEFAULT string

func (p *TicketRecord) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return TicketRecord_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

func (p *TicketRecord) GetUpdateTime() (v int64) {
	return p.UpdateTime
}
func (p *TicketRecord) SetTicketId(val string) {
	p.TicketId = val
}
func (p *TicketRecord) SetTicketType(val *TicketType) {
	p.TicketType = val
}
func (p *TicketRecord) SetTicketStatus(val TicketStatus) {
	p.TicketStatus = val
}
func (p *TicketRecord) SetCreateUserName(val string) {
	p.CreateUserName = val
}
func (p *TicketRecord) SetCreateUserId(val string) {
	p.CreateUserId = val
}
func (p *TicketRecord) SetDbName(val *string) {
	p.DbName = val
}
func (p *TicketRecord) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *TicketRecord) SetUpdateTime(val int64) {
	p.UpdateTime = val
}

var fieldIDToName_TicketRecord = map[int16]string{
	1: "TicketId",
	2: "TicketType",
	3: "TicketStatus",
	4: "CreateUserName",
	5: "CreateUserId",
	6: "DbName",
	7: "InstanceID",
	8: "UpdateTime",
}

func (p *TicketRecord) IsSetTicketType() bool {
	return p.TicketType != nil
}

func (p *TicketRecord) IsSetDbName() bool {
	return p.DbName != nil
}

func (p *TicketRecord) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *TicketRecord) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketRecord")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetTicketStatus bool = false
	var issetCreateUserName bool = false
	var issetCreateUserId bool = false
	var issetUpdateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTicketStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TicketRecord[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TicketRecord[fieldId]))
}

func (p *TicketRecord) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *TicketRecord) ReadField2(iprot thrift.TProtocol) error {

	var _field *TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketType(v)
		_field = &tmp
	}
	p.TicketType = _field
	return nil
}
func (p *TicketRecord) ReadField3(iprot thrift.TProtocol) error {

	var _field TicketStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketStatus(v)
	}
	p.TicketStatus = _field
	return nil
}
func (p *TicketRecord) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserName = _field
	return nil
}
func (p *TicketRecord) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserId = _field
	return nil
}
func (p *TicketRecord) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DbName = _field
	return nil
}
func (p *TicketRecord) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *TicketRecord) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *TicketRecord) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketRecord")

	var fieldId int16
	if err = oprot.WriteStructBegin("TicketRecord"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TicketRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TicketRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketType() {
		if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TicketRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketStatus", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TicketRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TicketRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TicketRecord) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDbName() {
		if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DbName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TicketRecord) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TicketRecord) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TicketRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TicketRecord(%+v)", *p)

}

func (p *TicketRecord) DeepEqual(ano *TicketRecord) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TicketType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field6DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field8DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *TicketRecord) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecord) Field2DeepEqual(src *TicketType) bool {

	if p.TicketType == src {
		return true
	} else if p.TicketType == nil || src == nil {
		return false
	}
	if *p.TicketType != *src {
		return false
	}
	return true
}
func (p *TicketRecord) Field3DeepEqual(src TicketStatus) bool {

	if p.TicketStatus != src {
		return false
	}
	return true
}
func (p *TicketRecord) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserName, src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecord) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserId, src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecord) Field6DeepEqual(src *string) bool {

	if p.DbName == src {
		return true
	} else if p.DbName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DbName, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecord) Field7DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketRecord) Field8DeepEqual(src int64) bool {

	if p.UpdateTime != src {
		return false
	}
	return true
}

type KillTask struct {
	KillTriggerType  KillType       `thrift:"KillTriggerType,1,required" frugal:"1,required,KillType" json:"KillTriggerType"`
	ContinusKillRule *SQLKillRule   `thrift:"ContinusKillRule,2,optional" frugal:"2,optional,SQLKillRule" json:"ContinusKillRule,omitempty"`
	ProcessInfo      []*ProcessInfo `thrift:"ProcessInfo,3,optional" frugal:"3,optional,list<ProcessInfo>" json:"ProcessInfo,omitempty"`
}

func NewKillTask() *KillTask {
	return &KillTask{}
}

func (p *KillTask) InitDefault() {
}

func (p *KillTask) GetKillTriggerType() (v KillType) {
	return p.KillTriggerType
}

var KillTask_ContinusKillRule_DEFAULT *SQLKillRule

func (p *KillTask) GetContinusKillRule() (v *SQLKillRule) {
	if !p.IsSetContinusKillRule() {
		return KillTask_ContinusKillRule_DEFAULT
	}
	return p.ContinusKillRule
}

var KillTask_ProcessInfo_DEFAULT []*ProcessInfo

func (p *KillTask) GetProcessInfo() (v []*ProcessInfo) {
	if !p.IsSetProcessInfo() {
		return KillTask_ProcessInfo_DEFAULT
	}
	return p.ProcessInfo
}
func (p *KillTask) SetKillTriggerType(val KillType) {
	p.KillTriggerType = val
}
func (p *KillTask) SetContinusKillRule(val *SQLKillRule) {
	p.ContinusKillRule = val
}
func (p *KillTask) SetProcessInfo(val []*ProcessInfo) {
	p.ProcessInfo = val
}

var fieldIDToName_KillTask = map[int16]string{
	1: "KillTriggerType",
	2: "ContinusKillRule",
	3: "ProcessInfo",
}

func (p *KillTask) IsSetContinusKillRule() bool {
	return p.ContinusKillRule != nil
}

func (p *KillTask) IsSetProcessInfo() bool {
	return p.ProcessInfo != nil
}

func (p *KillTask) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("KillTask")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKillTriggerType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKillTriggerType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKillTriggerType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_KillTask[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_KillTask[fieldId]))
}

func (p *KillTask) ReadField1(iprot thrift.TProtocol) error {

	var _field KillType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = KillType(v)
	}
	p.KillTriggerType = _field
	return nil
}
func (p *KillTask) ReadField2(iprot thrift.TProtocol) error {
	_field := NewSQLKillRule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ContinusKillRule = _field
	return nil
}
func (p *KillTask) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ProcessInfo, 0, size)
	values := make([]ProcessInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProcessInfo = _field
	return nil
}

func (p *KillTask) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("KillTask")

	var fieldId int16
	if err = oprot.WriteStructBegin("KillTask"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *KillTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KillTriggerType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.KillTriggerType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *KillTask) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContinusKillRule() {
		if err = oprot.WriteFieldBegin("ContinusKillRule", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ContinusKillRule.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *KillTask) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetProcessInfo() {
		if err = oprot.WriteFieldBegin("ProcessInfo", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProcessInfo)); err != nil {
			return err
		}
		for _, v := range p.ProcessInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *KillTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KillTask(%+v)", *p)

}

func (p *KillTask) DeepEqual(ano *KillTask) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.KillTriggerType) {
		return false
	}
	if !p.Field2DeepEqual(ano.ContinusKillRule) {
		return false
	}
	if !p.Field3DeepEqual(ano.ProcessInfo) {
		return false
	}
	return true
}

func (p *KillTask) Field1DeepEqual(src KillType) bool {

	if p.KillTriggerType != src {
		return false
	}
	return true
}
func (p *KillTask) Field2DeepEqual(src *SQLKillRule) bool {

	if !p.ContinusKillRule.DeepEqual(src) {
		return false
	}
	return true
}
func (p *KillTask) Field3DeepEqual(src []*ProcessInfo) bool {

	if len(p.ProcessInfo) != len(src) {
		return false
	}
	for i, v := range p.ProcessInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DasTaskDetail struct {
	CCLTask  *RuleDetail `thrift:"CCLTask,1,optional" frugal:"1,optional,RuleDetail" json:"CCLTask,omitempty"`
	KillTask *KillTask   `thrift:"KillTask,2,optional" frugal:"2,optional,KillTask" json:"KillTask,omitempty"`
}

func NewDasTaskDetail() *DasTaskDetail {
	return &DasTaskDetail{}
}

func (p *DasTaskDetail) InitDefault() {
}

var DasTaskDetail_CCLTask_DEFAULT *RuleDetail

func (p *DasTaskDetail) GetCCLTask() (v *RuleDetail) {
	if !p.IsSetCCLTask() {
		return DasTaskDetail_CCLTask_DEFAULT
	}
	return p.CCLTask
}

var DasTaskDetail_KillTask_DEFAULT *KillTask

func (p *DasTaskDetail) GetKillTask() (v *KillTask) {
	if !p.IsSetKillTask() {
		return DasTaskDetail_KillTask_DEFAULT
	}
	return p.KillTask
}
func (p *DasTaskDetail) SetCCLTask(val *RuleDetail) {
	p.CCLTask = val
}
func (p *DasTaskDetail) SetKillTask(val *KillTask) {
	p.KillTask = val
}

var fieldIDToName_DasTaskDetail = map[int16]string{
	1: "CCLTask",
	2: "KillTask",
}

func (p *DasTaskDetail) IsSetCCLTask() bool {
	return p.CCLTask != nil
}

func (p *DasTaskDetail) IsSetKillTask() bool {
	return p.KillTask != nil
}

func (p *DasTaskDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasTaskDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DasTaskDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DasTaskDetail) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRuleDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CCLTask = _field
	return nil
}
func (p *DasTaskDetail) ReadField2(iprot thrift.TProtocol) error {
	_field := NewKillTask()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.KillTask = _field
	return nil
}

func (p *DasTaskDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasTaskDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("DasTaskDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DasTaskDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCCLTask() {
		if err = oprot.WriteFieldBegin("CCLTask", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CCLTask.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DasTaskDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetKillTask() {
		if err = oprot.WriteFieldBegin("KillTask", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.KillTask.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DasTaskDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DasTaskDetail(%+v)", *p)

}

func (p *DasTaskDetail) DeepEqual(ano *DasTaskDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CCLTask) {
		return false
	}
	if !p.Field2DeepEqual(ano.KillTask) {
		return false
	}
	return true
}

func (p *DasTaskDetail) Field1DeepEqual(src *RuleDetail) bool {

	if !p.CCLTask.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DasTaskDetail) Field2DeepEqual(src *KillTask) bool {

	if !p.KillTask.DeepEqual(src) {
		return false
	}
	return true
}

type DasTask struct {
	ID                   string         `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	UserID               string         `thrift:"UserID,2,required" frugal:"2,required,string" json:"UserID"`
	UserName             string         `thrift:"UserName,3,required" frugal:"3,required,string" json:"UserName"`
	InstanceID           string         `thrift:"InstanceID,4,required" frugal:"4,required,string" json:"InstanceID"`
	InstanceType         string         `thrift:"InstanceType,5,required" frugal:"5,required,string" json:"InstanceType"`
	OpsTaskState         string         `thrift:"OpsTaskState,6,required" frugal:"6,required,string" json:"OpsTaskState"`
	TriggerType          string         `thrift:"TriggerType,7,required" frugal:"7,required,string" json:"TriggerType"`
	TaskAction           string         `thrift:"TaskAction,8,required" frugal:"8,required,string" json:"TaskAction"`
	DasOperationCategory string         `thrift:"DasOperationCategory,9,required" frugal:"9,required,string" json:"DasOperationCategory"`
	TaskDetail           *DasTaskDetail `thrift:"TaskDetail,10,required" frugal:"10,required,DasTaskDetail" json:"TaskDetail"`
	CreateTime           int64          `thrift:"CreateTime,11,required" frugal:"11,required,i64" json:"CreateTime"`
	TaskId               *string        `thrift:"TaskId,12,optional" frugal:"12,optional,string" json:"TaskId,omitempty"`
}

func NewDasTask() *DasTask {
	return &DasTask{}
}

func (p *DasTask) InitDefault() {
}

func (p *DasTask) GetID() (v string) {
	return p.ID
}

func (p *DasTask) GetUserID() (v string) {
	return p.UserID
}

func (p *DasTask) GetUserName() (v string) {
	return p.UserName
}

func (p *DasTask) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DasTask) GetInstanceType() (v string) {
	return p.InstanceType
}

func (p *DasTask) GetOpsTaskState() (v string) {
	return p.OpsTaskState
}

func (p *DasTask) GetTriggerType() (v string) {
	return p.TriggerType
}

func (p *DasTask) GetTaskAction() (v string) {
	return p.TaskAction
}

func (p *DasTask) GetDasOperationCategory() (v string) {
	return p.DasOperationCategory
}

var DasTask_TaskDetail_DEFAULT *DasTaskDetail

func (p *DasTask) GetTaskDetail() (v *DasTaskDetail) {
	if !p.IsSetTaskDetail() {
		return DasTask_TaskDetail_DEFAULT
	}
	return p.TaskDetail
}

func (p *DasTask) GetCreateTime() (v int64) {
	return p.CreateTime
}

var DasTask_TaskId_DEFAULT string

func (p *DasTask) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return DasTask_TaskId_DEFAULT
	}
	return *p.TaskId
}
func (p *DasTask) SetID(val string) {
	p.ID = val
}
func (p *DasTask) SetUserID(val string) {
	p.UserID = val
}
func (p *DasTask) SetUserName(val string) {
	p.UserName = val
}
func (p *DasTask) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DasTask) SetInstanceType(val string) {
	p.InstanceType = val
}
func (p *DasTask) SetOpsTaskState(val string) {
	p.OpsTaskState = val
}
func (p *DasTask) SetTriggerType(val string) {
	p.TriggerType = val
}
func (p *DasTask) SetTaskAction(val string) {
	p.TaskAction = val
}
func (p *DasTask) SetDasOperationCategory(val string) {
	p.DasOperationCategory = val
}
func (p *DasTask) SetTaskDetail(val *DasTaskDetail) {
	p.TaskDetail = val
}
func (p *DasTask) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *DasTask) SetTaskId(val *string) {
	p.TaskId = val
}

var fieldIDToName_DasTask = map[int16]string{
	1:  "ID",
	2:  "UserID",
	3:  "UserName",
	4:  "InstanceID",
	5:  "InstanceType",
	6:  "OpsTaskState",
	7:  "TriggerType",
	8:  "TaskAction",
	9:  "DasOperationCategory",
	10: "TaskDetail",
	11: "CreateTime",
	12: "TaskId",
}

func (p *DasTask) IsSetTaskDetail() bool {
	return p.TaskDetail != nil
}

func (p *DasTask) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *DasTask) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasTask")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetUserID bool = false
	var issetUserName bool = false
	var issetInstanceID bool = false
	var issetInstanceType bool = false
	var issetOpsTaskState bool = false
	var issetTriggerType bool = false
	var issetTaskAction bool = false
	var issetDasOperationCategory bool = false
	var issetTaskDetail bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetOpsTaskState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTriggerType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetDasOperationCategory = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUserName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetOpsTaskState {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTriggerType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetTaskAction {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetDasOperationCategory {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetTaskDetail {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DasTask[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DasTask[fieldId]))
}

func (p *DasTask) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *DasTask) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *DasTask) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *DasTask) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DasTask) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}
func (p *DasTask) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OpsTaskState = _field
	return nil
}
func (p *DasTask) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TriggerType = _field
	return nil
}
func (p *DasTask) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskAction = _field
	return nil
}
func (p *DasTask) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DasOperationCategory = _field
	return nil
}
func (p *DasTask) ReadField10(iprot thrift.TProtocol) error {
	_field := NewDasTaskDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskDetail = _field
	return nil
}
func (p *DasTask) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *DasTask) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}

func (p *DasTask) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasTask")

	var fieldId int16
	if err = oprot.WriteStructBegin("DasTask"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DasTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DasTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DasTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DasTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DasTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DasTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OpsTaskState", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OpsTaskState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DasTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TriggerType", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TriggerType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DasTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskAction", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DasTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DasOperationCategory", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DasOperationCategory); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DasTask) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskDetail", thrift.STRUCT, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TaskDetail.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DasTask) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DasTask) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DasTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DasTask(%+v)", *p)

}

func (p *DasTask) DeepEqual(ano *DasTask) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.OpsTaskState) {
		return false
	}
	if !p.Field7DeepEqual(ano.TriggerType) {
		return false
	}
	if !p.Field8DeepEqual(ano.TaskAction) {
		return false
	}
	if !p.Field9DeepEqual(ano.DasOperationCategory) {
		return false
	}
	if !p.Field10DeepEqual(ano.TaskDetail) {
		return false
	}
	if !p.Field11DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field12DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *DasTask) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field2DeepEqual(src string) bool {

	if strings.Compare(p.UserID, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field3DeepEqual(src string) bool {

	if strings.Compare(p.UserName, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field5DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field6DeepEqual(src string) bool {

	if strings.Compare(p.OpsTaskState, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field7DeepEqual(src string) bool {

	if strings.Compare(p.TriggerType, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field8DeepEqual(src string) bool {

	if strings.Compare(p.TaskAction, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field9DeepEqual(src string) bool {

	if strings.Compare(p.DasOperationCategory, src) != 0 {
		return false
	}
	return true
}
func (p *DasTask) Field10DeepEqual(src *DasTaskDetail) bool {

	if !p.TaskDetail.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DasTask) Field11DeepEqual(src int64) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}
func (p *DasTask) Field12DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}

type DescribeDasOperationTaskListReq struct {
	PageNumber           *int32                `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize             *int32                `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	DasRecordSearchParam *DasRecordSearchParam `thrift:"DasRecordSearchParam,3,optional" frugal:"3,optional,DasRecordSearchParam" json:"DasRecordSearchParam,omitempty"`
	SortBy               *SortBy               `thrift:"SortBy,4,optional" frugal:"4,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy              *OrderByForDasRecord  `thrift:"OrderBy,5,optional" frugal:"5,optional,OrderByForDasRecord" json:"OrderBy,omitempty"`
}

func NewDescribeDasOperationTaskListReq() *DescribeDasOperationTaskListReq {
	return &DescribeDasOperationTaskListReq{}
}

func (p *DescribeDasOperationTaskListReq) InitDefault() {
}

var DescribeDasOperationTaskListReq_PageNumber_DEFAULT int32

func (p *DescribeDasOperationTaskListReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDasOperationTaskListReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDasOperationTaskListReq_PageSize_DEFAULT int32

func (p *DescribeDasOperationTaskListReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDasOperationTaskListReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDasOperationTaskListReq_DasRecordSearchParam_DEFAULT *DasRecordSearchParam

func (p *DescribeDasOperationTaskListReq) GetDasRecordSearchParam() (v *DasRecordSearchParam) {
	if !p.IsSetDasRecordSearchParam() {
		return DescribeDasOperationTaskListReq_DasRecordSearchParam_DEFAULT
	}
	return p.DasRecordSearchParam
}

var DescribeDasOperationTaskListReq_SortBy_DEFAULT SortBy

func (p *DescribeDasOperationTaskListReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeDasOperationTaskListReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeDasOperationTaskListReq_OrderBy_DEFAULT OrderByForDasRecord

func (p *DescribeDasOperationTaskListReq) GetOrderBy() (v OrderByForDasRecord) {
	if !p.IsSetOrderBy() {
		return DescribeDasOperationTaskListReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}
func (p *DescribeDasOperationTaskListReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDasOperationTaskListReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDasOperationTaskListReq) SetDasRecordSearchParam(val *DasRecordSearchParam) {
	p.DasRecordSearchParam = val
}
func (p *DescribeDasOperationTaskListReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeDasOperationTaskListReq) SetOrderBy(val *OrderByForDasRecord) {
	p.OrderBy = val
}

var fieldIDToName_DescribeDasOperationTaskListReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "DasRecordSearchParam",
	4: "SortBy",
	5: "OrderBy",
}

func (p *DescribeDasOperationTaskListReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDasOperationTaskListReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDasOperationTaskListReq) IsSetDasRecordSearchParam() bool {
	return p.DasRecordSearchParam != nil
}

func (p *DescribeDasOperationTaskListReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeDasOperationTaskListReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeDasOperationTaskListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDasOperationTaskListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDasOperationTaskListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDasOperationTaskListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDasOperationTaskListReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewDasRecordSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DasRecordSearchParam = _field
	return nil
}
func (p *DescribeDasOperationTaskListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeDasOperationTaskListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *OrderByForDasRecord
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForDasRecord(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}

func (p *DescribeDasOperationTaskListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDasOperationTaskListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDasOperationTaskListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDasRecordSearchParam() {
		if err = oprot.WriteFieldBegin("DasRecordSearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DasRecordSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDasOperationTaskListReq(%+v)", *p)

}

func (p *DescribeDasOperationTaskListReq) DeepEqual(ano *DescribeDasOperationTaskListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.DasRecordSearchParam) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	return true
}

func (p *DescribeDasOperationTaskListReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDasOperationTaskListReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDasOperationTaskListReq) Field3DeepEqual(src *DasRecordSearchParam) bool {

	if !p.DasRecordSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDasOperationTaskListReq) Field4DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeDasOperationTaskListReq) Field5DeepEqual(src *OrderByForDasRecord) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}

type DescribeDasOperationTaskListResp struct {
	DasTaskList []*DasTask `thrift:"DasTaskList,1,required" frugal:"1,required,list<DasTask>" json:"DasTaskList"`
	Total       int64      `thrift:"Total,2,required" frugal:"2,required,i64" json:"Total"`
}

func NewDescribeDasOperationTaskListResp() *DescribeDasOperationTaskListResp {
	return &DescribeDasOperationTaskListResp{}
}

func (p *DescribeDasOperationTaskListResp) InitDefault() {
}

func (p *DescribeDasOperationTaskListResp) GetDasTaskList() (v []*DasTask) {
	return p.DasTaskList
}

func (p *DescribeDasOperationTaskListResp) GetTotal() (v int64) {
	return p.Total
}
func (p *DescribeDasOperationTaskListResp) SetDasTaskList(val []*DasTask) {
	p.DasTaskList = val
}
func (p *DescribeDasOperationTaskListResp) SetTotal(val int64) {
	p.Total = val
}

var fieldIDToName_DescribeDasOperationTaskListResp = map[int16]string{
	1: "DasTaskList",
	2: "Total",
}

func (p *DescribeDasOperationTaskListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDasOperationTaskListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDasTaskList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDasTaskList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDasTaskList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDasOperationTaskListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDasOperationTaskListResp[fieldId]))
}

func (p *DescribeDasOperationTaskListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DasTask, 0, size)
	values := make([]DasTask, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DasTaskList = _field
	return nil
}
func (p *DescribeDasOperationTaskListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeDasOperationTaskListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDasOperationTaskListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDasOperationTaskListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDasOperationTaskListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DasTaskList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DasTaskList)); err != nil {
		return err
	}
	for _, v := range p.DasTaskList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDasOperationTaskListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDasOperationTaskListResp(%+v)", *p)

}

func (p *DescribeDasOperationTaskListResp) DeepEqual(ano *DescribeDasOperationTaskListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DasTaskList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeDasOperationTaskListResp) Field1DeepEqual(src []*DasTask) bool {

	if len(p.DasTaskList) != len(src) {
		return false
	}
	for i, v := range p.DasTaskList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDasOperationTaskListResp) Field2DeepEqual(src int64) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DasRecordSearchParam struct {
	StartTime            *int64                `thrift:"StartTime,1,optional" frugal:"1,optional,i64" json:"StartTime,omitempty"`
	EndTime              *int64                `thrift:"EndTime,2,optional" frugal:"2,optional,i64" json:"EndTime,omitempty"`
	InstanceID           *string               `thrift:"InstanceID,3,optional" frugal:"3,optional,string" json:"InstanceID,omitempty"`
	InstanceType         *DSType               `thrift:"InstanceType,4,optional" frugal:"4,optional,DSType" json:"InstanceType,omitempty"`
	CreateUserId         *string               `thrift:"CreateUserId,5,optional" frugal:"5,optional,string" json:"CreateUserId,omitempty"`
	CreateUserName       *string               `thrift:"CreateUserName,6,optional" frugal:"6,optional,string" json:"CreateUserName,omitempty"`
	TaskId               *string               `thrift:"TaskId,7,optional" frugal:"7,optional,string" json:"TaskId,omitempty"`
	TaskAction           *DasAction            `thrift:"TaskAction,8,optional" frugal:"8,optional,DasAction" json:"TaskAction,omitempty"`
	TriggerType          *TriggerType          `thrift:"TriggerType,9,optional" frugal:"9,optional,TriggerType" json:"TriggerType,omitempty"`
	OpsTaskState         *OpsTaskState         `thrift:"OpsTaskState,10,optional" frugal:"10,optional,OpsTaskState" json:"OpsTaskState,omitempty"`
	DasOperationCategory *DasOperationCategory `thrift:"DasOperationCategory,11,optional" frugal:"11,optional,DasOperationCategory" json:"DasOperationCategory,omitempty"`
}

func NewDasRecordSearchParam() *DasRecordSearchParam {
	return &DasRecordSearchParam{}
}

func (p *DasRecordSearchParam) InitDefault() {
}

var DasRecordSearchParam_StartTime_DEFAULT int64

func (p *DasRecordSearchParam) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return DasRecordSearchParam_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DasRecordSearchParam_EndTime_DEFAULT int64

func (p *DasRecordSearchParam) GetEndTime() (v int64) {
	if !p.IsSetEndTime() {
		return DasRecordSearchParam_EndTime_DEFAULT
	}
	return *p.EndTime
}

var DasRecordSearchParam_InstanceID_DEFAULT string

func (p *DasRecordSearchParam) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return DasRecordSearchParam_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var DasRecordSearchParam_InstanceType_DEFAULT DSType

func (p *DasRecordSearchParam) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DasRecordSearchParam_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DasRecordSearchParam_CreateUserId_DEFAULT string

func (p *DasRecordSearchParam) GetCreateUserId() (v string) {
	if !p.IsSetCreateUserId() {
		return DasRecordSearchParam_CreateUserId_DEFAULT
	}
	return *p.CreateUserId
}

var DasRecordSearchParam_CreateUserName_DEFAULT string

func (p *DasRecordSearchParam) GetCreateUserName() (v string) {
	if !p.IsSetCreateUserName() {
		return DasRecordSearchParam_CreateUserName_DEFAULT
	}
	return *p.CreateUserName
}

var DasRecordSearchParam_TaskId_DEFAULT string

func (p *DasRecordSearchParam) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return DasRecordSearchParam_TaskId_DEFAULT
	}
	return *p.TaskId
}

var DasRecordSearchParam_TaskAction_DEFAULT DasAction

func (p *DasRecordSearchParam) GetTaskAction() (v DasAction) {
	if !p.IsSetTaskAction() {
		return DasRecordSearchParam_TaskAction_DEFAULT
	}
	return *p.TaskAction
}

var DasRecordSearchParam_TriggerType_DEFAULT TriggerType

func (p *DasRecordSearchParam) GetTriggerType() (v TriggerType) {
	if !p.IsSetTriggerType() {
		return DasRecordSearchParam_TriggerType_DEFAULT
	}
	return *p.TriggerType
}

var DasRecordSearchParam_OpsTaskState_DEFAULT OpsTaskState

func (p *DasRecordSearchParam) GetOpsTaskState() (v OpsTaskState) {
	if !p.IsSetOpsTaskState() {
		return DasRecordSearchParam_OpsTaskState_DEFAULT
	}
	return *p.OpsTaskState
}

var DasRecordSearchParam_DasOperationCategory_DEFAULT DasOperationCategory

func (p *DasRecordSearchParam) GetDasOperationCategory() (v DasOperationCategory) {
	if !p.IsSetDasOperationCategory() {
		return DasRecordSearchParam_DasOperationCategory_DEFAULT
	}
	return *p.DasOperationCategory
}
func (p *DasRecordSearchParam) SetStartTime(val *int64) {
	p.StartTime = val
}
func (p *DasRecordSearchParam) SetEndTime(val *int64) {
	p.EndTime = val
}
func (p *DasRecordSearchParam) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *DasRecordSearchParam) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DasRecordSearchParam) SetCreateUserId(val *string) {
	p.CreateUserId = val
}
func (p *DasRecordSearchParam) SetCreateUserName(val *string) {
	p.CreateUserName = val
}
func (p *DasRecordSearchParam) SetTaskId(val *string) {
	p.TaskId = val
}
func (p *DasRecordSearchParam) SetTaskAction(val *DasAction) {
	p.TaskAction = val
}
func (p *DasRecordSearchParam) SetTriggerType(val *TriggerType) {
	p.TriggerType = val
}
func (p *DasRecordSearchParam) SetOpsTaskState(val *OpsTaskState) {
	p.OpsTaskState = val
}
func (p *DasRecordSearchParam) SetDasOperationCategory(val *DasOperationCategory) {
	p.DasOperationCategory = val
}

var fieldIDToName_DasRecordSearchParam = map[int16]string{
	1:  "StartTime",
	2:  "EndTime",
	3:  "InstanceID",
	4:  "InstanceType",
	5:  "CreateUserId",
	6:  "CreateUserName",
	7:  "TaskId",
	8:  "TaskAction",
	9:  "TriggerType",
	10: "OpsTaskState",
	11: "DasOperationCategory",
}

func (p *DasRecordSearchParam) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DasRecordSearchParam) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DasRecordSearchParam) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *DasRecordSearchParam) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DasRecordSearchParam) IsSetCreateUserId() bool {
	return p.CreateUserId != nil
}

func (p *DasRecordSearchParam) IsSetCreateUserName() bool {
	return p.CreateUserName != nil
}

func (p *DasRecordSearchParam) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *DasRecordSearchParam) IsSetTaskAction() bool {
	return p.TaskAction != nil
}

func (p *DasRecordSearchParam) IsSetTriggerType() bool {
	return p.TriggerType != nil
}

func (p *DasRecordSearchParam) IsSetOpsTaskState() bool {
	return p.OpsTaskState != nil
}

func (p *DasRecordSearchParam) IsSetDasOperationCategory() bool {
	return p.DasOperationCategory != nil
}

func (p *DasRecordSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasRecordSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DasRecordSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DasRecordSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserId = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserName = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *DasAction
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DasAction(v)
		_field = &tmp
	}
	p.TaskAction = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField9(iprot thrift.TProtocol) error {

	var _field *TriggerType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TriggerType(v)
		_field = &tmp
	}
	p.TriggerType = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField10(iprot thrift.TProtocol) error {

	var _field *OpsTaskState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OpsTaskState(v)
		_field = &tmp
	}
	p.OpsTaskState = _field
	return nil
}
func (p *DasRecordSearchParam) ReadField11(iprot thrift.TProtocol) error {

	var _field *DasOperationCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DasOperationCategory(v)
		_field = &tmp
	}
	p.DasOperationCategory = _field
	return nil
}

func (p *DasRecordSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DasRecordSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("DasRecordSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserId() {
		if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserName() {
		if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskAction() {
		if err = oprot.WriteFieldBegin("TaskAction", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TaskAction)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerType() {
		if err = oprot.WriteFieldBegin("TriggerType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TriggerType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetOpsTaskState() {
		if err = oprot.WriteFieldBegin("OpsTaskState", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OpsTaskState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DasRecordSearchParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetDasOperationCategory() {
		if err = oprot.WriteFieldBegin("DasOperationCategory", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DasOperationCategory)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DasRecordSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DasRecordSearchParam(%+v)", *p)

}

func (p *DasRecordSearchParam) DeepEqual(ano *DasRecordSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field7DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field8DeepEqual(ano.TaskAction) {
		return false
	}
	if !p.Field9DeepEqual(ano.TriggerType) {
		return false
	}
	if !p.Field10DeepEqual(ano.OpsTaskState) {
		return false
	}
	if !p.Field11DeepEqual(ano.DasOperationCategory) {
		return false
	}
	return true
}

func (p *DasRecordSearchParam) Field1DeepEqual(src *int64) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if *p.StartTime != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field2DeepEqual(src *int64) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if *p.EndTime != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field3DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field4DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field5DeepEqual(src *string) bool {

	if p.CreateUserId == src {
		return true
	} else if p.CreateUserId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserId, *src) != 0 {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field6DeepEqual(src *string) bool {

	if p.CreateUserName == src {
		return true
	} else if p.CreateUserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserName, *src) != 0 {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field7DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field8DeepEqual(src *DasAction) bool {

	if p.TaskAction == src {
		return true
	} else if p.TaskAction == nil || src == nil {
		return false
	}
	if *p.TaskAction != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field9DeepEqual(src *TriggerType) bool {

	if p.TriggerType == src {
		return true
	} else if p.TriggerType == nil || src == nil {
		return false
	}
	if *p.TriggerType != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field10DeepEqual(src *OpsTaskState) bool {

	if p.OpsTaskState == src {
		return true
	} else if p.OpsTaskState == nil || src == nil {
		return false
	}
	if *p.OpsTaskState != *src {
		return false
	}
	return true
}
func (p *DasRecordSearchParam) Field11DeepEqual(src *DasOperationCategory) bool {

	if p.DasOperationCategory == src {
		return true
	} else if p.DasOperationCategory == nil || src == nil {
		return false
	}
	if *p.DasOperationCategory != *src {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SQLAdvisorOrderBy int64

const (
	SQLAdvisorOrderBy_Asc  SQLAdvisorOrderBy = 0
	SQLAdvisorOrderBy_Desc SQLAdvisorOrderBy = 1
)

func (p SQLAdvisorOrderBy) String() string {
	switch p {
	case SQLAdvisorOrderBy_Asc:
		return "Asc"
	case SQLAdvisorOrderBy_Desc:
		return "Desc"
	}
	return "<UNSET>"
}

func SQLAdvisorOrderByFromString(s string) (SQLAdvisorOrderBy, error) {
	switch s {
	case "Asc":
		return SQLAdvisorOrderBy_Asc, nil
	case "Desc":
		return SQLAdvisorOrderBy_Desc, nil
	}
	return SQLAdvisorOrderBy(0), fmt.Errorf("not a valid SQLAdvisorOrderBy string")
}

func SQLAdvisorOrderByPtr(v SQLAdvisorOrderBy) *SQLAdvisorOrderBy { return &v }

func (p SQLAdvisorOrderBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SQLAdvisorOrderBy) UnmarshalText(text []byte) error {
	q, err := SQLAdvisorOrderByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SQLAdvisorDbType int64

const (
	SQLAdvisorDbType_VolcMySQL SQLAdvisorDbType = 0
	SQLAdvisorDbType_OpenMySQL SQLAdvisorDbType = 1
)

func (p SQLAdvisorDbType) String() string {
	switch p {
	case SQLAdvisorDbType_VolcMySQL:
		return "VolcMySQL"
	case SQLAdvisorDbType_OpenMySQL:
		return "OpenMySQL"
	}
	return "<UNSET>"
}

func SQLAdvisorDbTypeFromString(s string) (SQLAdvisorDbType, error) {
	switch s {
	case "VolcMySQL":
		return SQLAdvisorDbType_VolcMySQL, nil
	case "OpenMySQL":
		return SQLAdvisorDbType_OpenMySQL, nil
	}
	return SQLAdvisorDbType(0), fmt.Errorf("not a valid SQLAdvisorDbType string")
}

func SQLAdvisorDbTypePtr(v SQLAdvisorDbType) *SQLAdvisorDbType { return &v }

func (p SQLAdvisorDbType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SQLAdvisorDbType) UnmarshalText(text []byte) error {
	q, err := SQLAdvisorDbTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SQLAdvisorTaskStatus int64

const (
	SQLAdvisorTaskStatus_SUBMITTED SQLAdvisorTaskStatus = 0
	SQLAdvisorTaskStatus_FETCHED   SQLAdvisorTaskStatus = 1
	SQLAdvisorTaskStatus_SUCCESS   SQLAdvisorTaskStatus = 2
	SQLAdvisorTaskStatus_EXCEPTION SQLAdvisorTaskStatus = 3
)

func (p SQLAdvisorTaskStatus) String() string {
	switch p {
	case SQLAdvisorTaskStatus_SUBMITTED:
		return "SUBMITTED"
	case SQLAdvisorTaskStatus_FETCHED:
		return "FETCHED"
	case SQLAdvisorTaskStatus_SUCCESS:
		return "SUCCESS"
	case SQLAdvisorTaskStatus_EXCEPTION:
		return "EXCEPTION"
	}
	return "<UNSET>"
}

func SQLAdvisorTaskStatusFromString(s string) (SQLAdvisorTaskStatus, error) {
	switch s {
	case "SUBMITTED":
		return SQLAdvisorTaskStatus_SUBMITTED, nil
	case "FETCHED":
		return SQLAdvisorTaskStatus_FETCHED, nil
	case "SUCCESS":
		return SQLAdvisorTaskStatus_SUCCESS, nil
	case "EXCEPTION":
		return SQLAdvisorTaskStatus_EXCEPTION, nil
	}
	return SQLAdvisorTaskStatus(0), fmt.Errorf("not a valid SQLAdvisorTaskStatus string")
}

func SQLAdvisorTaskStatusPtr(v SQLAdvisorTaskStatus) *SQLAdvisorTaskStatus { return &v }

func (p SQLAdvisorTaskStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SQLAdvisorTaskStatus) UnmarshalText(text []byte) error {
	q, err := SQLAdvisorTaskStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeSQLAdvisorTableMetaReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceID   string       `thrift:"InstanceID,2,required" frugal:"2,required,string" json:"InstanceID"`
	TenantID     string       `thrift:"TenantID,3,required" frugal:"3,required,string" json:"TenantID"`
	Region       string       `thrift:"Region,4,required" frugal:"4,required,string" json:"Region"`
	DbName       string       `thrift:"DbName,5,required" frugal:"5,required,string" json:"DbName"`
	TableList    []string     `thrift:"TableList,6,required" frugal:"6,required,list<string>" json:"TableList"`
}

func NewDescribeSQLAdvisorTableMetaReq() *DescribeSQLAdvisorTableMetaReq {
	return &DescribeSQLAdvisorTableMetaReq{}
}

func (p *DescribeSQLAdvisorTableMetaReq) InitDefault() {
}

func (p *DescribeSQLAdvisorTableMetaReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeSQLAdvisorTableMetaReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSQLAdvisorTableMetaReq) GetTenantID() (v string) {
	return p.TenantID
}

func (p *DescribeSQLAdvisorTableMetaReq) GetRegion() (v string) {
	return p.Region
}

func (p *DescribeSQLAdvisorTableMetaReq) GetDbName() (v string) {
	return p.DbName
}

func (p *DescribeSQLAdvisorTableMetaReq) GetTableList() (v []string) {
	return p.TableList
}
func (p *DescribeSQLAdvisorTableMetaReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeSQLAdvisorTableMetaReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLAdvisorTableMetaReq) SetTenantID(val string) {
	p.TenantID = val
}
func (p *DescribeSQLAdvisorTableMetaReq) SetRegion(val string) {
	p.Region = val
}
func (p *DescribeSQLAdvisorTableMetaReq) SetDbName(val string) {
	p.DbName = val
}
func (p *DescribeSQLAdvisorTableMetaReq) SetTableList(val []string) {
	p.TableList = val
}

var fieldIDToName_DescribeSQLAdvisorTableMetaReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceID",
	3: "TenantID",
	4: "Region",
	5: "DbName",
	6: "TableList",
}

func (p *DescribeSQLAdvisorTableMetaReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTableMetaReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetTenantID bool = false
	var issetRegion bool = false
	var issetDbName bool = false
	var issetTableList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTenantID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTableList {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLAdvisorTableMetaReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLAdvisorTableMetaReq[fieldId]))
}

func (p *DescribeSQLAdvisorTableMetaReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantID = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TableList = _field
	return nil
}

func (p *DescribeSQLAdvisorTableMetaReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTableMetaReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLAdvisorTableMetaReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableList", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.TableList)); err != nil {
		return err
	}
	for _, v := range p.TableList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLAdvisorTableMetaReq(%+v)", *p)

}

func (p *DescribeSQLAdvisorTableMetaReq) DeepEqual(ano *DescribeSQLAdvisorTableMetaReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.TenantID) {
		return false
	}
	if !p.Field4DeepEqual(ano.Region) {
		return false
	}
	if !p.Field5DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field6DeepEqual(ano.TableList) {
		return false
	}
	return true
}

func (p *DescribeSQLAdvisorTableMetaReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TenantID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaReq) Field6DeepEqual(src []string) bool {

	if len(p.TableList) != len(src) {
		return false
	}
	for i, v := range p.TableList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeSQLAdvisorTableMetaResp struct {
	Success bool                       `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
	Data    []*SQLAdvisorTableMetaData `thrift:"Data,2,required" frugal:"2,required,list<SQLAdvisorTableMetaData>" json:"Data"`
}

func NewDescribeSQLAdvisorTableMetaResp() *DescribeSQLAdvisorTableMetaResp {
	return &DescribeSQLAdvisorTableMetaResp{}
}

func (p *DescribeSQLAdvisorTableMetaResp) InitDefault() {
}

func (p *DescribeSQLAdvisorTableMetaResp) GetSuccess() (v bool) {
	return p.Success
}

func (p *DescribeSQLAdvisorTableMetaResp) GetData() (v []*SQLAdvisorTableMetaData) {
	return p.Data
}
func (p *DescribeSQLAdvisorTableMetaResp) SetSuccess(val bool) {
	p.Success = val
}
func (p *DescribeSQLAdvisorTableMetaResp) SetData(val []*SQLAdvisorTableMetaData) {
	p.Data = val
}

var fieldIDToName_DescribeSQLAdvisorTableMetaResp = map[int16]string{
	1: "Success",
	2: "Data",
}

func (p *DescribeSQLAdvisorTableMetaResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTableMetaResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	var issetData bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetData {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLAdvisorTableMetaResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLAdvisorTableMetaResp[fieldId]))
}

func (p *DescribeSQLAdvisorTableMetaResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *DescribeSQLAdvisorTableMetaResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLAdvisorTableMetaData, 0, size)
	values := make([]SQLAdvisorTableMetaData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Data = _field
	return nil
}

func (p *DescribeSQLAdvisorTableMetaResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTableMetaResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLAdvisorTableMetaResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Data", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
		return err
	}
	for _, v := range p.Data {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTableMetaResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLAdvisorTableMetaResp(%+v)", *p)

}

func (p *DescribeSQLAdvisorTableMetaResp) DeepEqual(ano *DescribeSQLAdvisorTableMetaResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	if !p.Field2DeepEqual(ano.Data) {
		return false
	}
	return true
}

func (p *DescribeSQLAdvisorTableMetaResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTableMetaResp) Field2DeepEqual(src []*SQLAdvisorTableMetaData) bool {

	if len(p.Data) != len(src) {
		return false
	}
	for i, v := range p.Data {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SQLAdvisorTableMetaData struct {
	Name                 string                          `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	TableInfo            *SQLAdvisorTableInfo            `thrift:"TableInfo,2,required" frugal:"2,required,SQLAdvisorTableInfo" json:"TableInfo"`
	ColumnInfo           []*SQLAdvisorColumnInfo         `thrift:"ColumnInfo,3,required" frugal:"3,required,list<SQLAdvisorColumnInfo>" json:"ColumnInfo"`
	StatisticsInfo       []*SQLAdvisorStatisticsInfo     `thrift:"StatisticsInfo,4,required" frugal:"4,required,list<SQLAdvisorStatisticsInfo>" json:"StatisticsInfo"`
	InnodbTableStatsInfo *SQLAdvisorInnodbTableStatsInfo `thrift:"InnodbTableStatsInfo,5,required" frugal:"5,required,SQLAdvisorInnodbTableStatsInfo" json:"InnodbTableStatsInfo"`
	CreateTableInfo      *SQLAdvisorCreateTableInfo      `thrift:"CreateTableInfo,6,required" frugal:"6,required,SQLAdvisorCreateTableInfo" json:"CreateTableInfo"`
}

func NewSQLAdvisorTableMetaData() *SQLAdvisorTableMetaData {
	return &SQLAdvisorTableMetaData{}
}

func (p *SQLAdvisorTableMetaData) InitDefault() {
}

func (p *SQLAdvisorTableMetaData) GetName() (v string) {
	return p.Name
}

var SQLAdvisorTableMetaData_TableInfo_DEFAULT *SQLAdvisorTableInfo

func (p *SQLAdvisorTableMetaData) GetTableInfo() (v *SQLAdvisorTableInfo) {
	if !p.IsSetTableInfo() {
		return SQLAdvisorTableMetaData_TableInfo_DEFAULT
	}
	return p.TableInfo
}

func (p *SQLAdvisorTableMetaData) GetColumnInfo() (v []*SQLAdvisorColumnInfo) {
	return p.ColumnInfo
}

func (p *SQLAdvisorTableMetaData) GetStatisticsInfo() (v []*SQLAdvisorStatisticsInfo) {
	return p.StatisticsInfo
}

var SQLAdvisorTableMetaData_InnodbTableStatsInfo_DEFAULT *SQLAdvisorInnodbTableStatsInfo

func (p *SQLAdvisorTableMetaData) GetInnodbTableStatsInfo() (v *SQLAdvisorInnodbTableStatsInfo) {
	if !p.IsSetInnodbTableStatsInfo() {
		return SQLAdvisorTableMetaData_InnodbTableStatsInfo_DEFAULT
	}
	return p.InnodbTableStatsInfo
}

var SQLAdvisorTableMetaData_CreateTableInfo_DEFAULT *SQLAdvisorCreateTableInfo

func (p *SQLAdvisorTableMetaData) GetCreateTableInfo() (v *SQLAdvisorCreateTableInfo) {
	if !p.IsSetCreateTableInfo() {
		return SQLAdvisorTableMetaData_CreateTableInfo_DEFAULT
	}
	return p.CreateTableInfo
}
func (p *SQLAdvisorTableMetaData) SetName(val string) {
	p.Name = val
}
func (p *SQLAdvisorTableMetaData) SetTableInfo(val *SQLAdvisorTableInfo) {
	p.TableInfo = val
}
func (p *SQLAdvisorTableMetaData) SetColumnInfo(val []*SQLAdvisorColumnInfo) {
	p.ColumnInfo = val
}
func (p *SQLAdvisorTableMetaData) SetStatisticsInfo(val []*SQLAdvisorStatisticsInfo) {
	p.StatisticsInfo = val
}
func (p *SQLAdvisorTableMetaData) SetInnodbTableStatsInfo(val *SQLAdvisorInnodbTableStatsInfo) {
	p.InnodbTableStatsInfo = val
}
func (p *SQLAdvisorTableMetaData) SetCreateTableInfo(val *SQLAdvisorCreateTableInfo) {
	p.CreateTableInfo = val
}

var fieldIDToName_SQLAdvisorTableMetaData = map[int16]string{
	1: "Name",
	2: "TableInfo",
	3: "ColumnInfo",
	4: "StatisticsInfo",
	5: "InnodbTableStatsInfo",
	6: "CreateTableInfo",
}

func (p *SQLAdvisorTableMetaData) IsSetTableInfo() bool {
	return p.TableInfo != nil
}

func (p *SQLAdvisorTableMetaData) IsSetInnodbTableStatsInfo() bool {
	return p.InnodbTableStatsInfo != nil
}

func (p *SQLAdvisorTableMetaData) IsSetCreateTableInfo() bool {
	return p.CreateTableInfo != nil
}

func (p *SQLAdvisorTableMetaData) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTableMetaData")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetTableInfo bool = false
	var issetColumnInfo bool = false
	var issetStatisticsInfo bool = false
	var issetInnodbTableStatsInfo bool = false
	var issetCreateTableInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatisticsInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInnodbTableStatsInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTableInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetColumnInfo {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStatisticsInfo {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInnodbTableStatsInfo {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCreateTableInfo {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorTableMetaData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorTableMetaData[fieldId]))
}

func (p *SQLAdvisorTableMetaData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *SQLAdvisorTableMetaData) ReadField2(iprot thrift.TProtocol) error {
	_field := NewSQLAdvisorTableInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TableInfo = _field
	return nil
}
func (p *SQLAdvisorTableMetaData) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLAdvisorColumnInfo, 0, size)
	values := make([]SQLAdvisorColumnInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ColumnInfo = _field
	return nil
}
func (p *SQLAdvisorTableMetaData) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLAdvisorStatisticsInfo, 0, size)
	values := make([]SQLAdvisorStatisticsInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.StatisticsInfo = _field
	return nil
}
func (p *SQLAdvisorTableMetaData) ReadField5(iprot thrift.TProtocol) error {
	_field := NewSQLAdvisorInnodbTableStatsInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InnodbTableStatsInfo = _field
	return nil
}
func (p *SQLAdvisorTableMetaData) ReadField6(iprot thrift.TProtocol) error {
	_field := NewSQLAdvisorCreateTableInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CreateTableInfo = _field
	return nil
}

func (p *SQLAdvisorTableMetaData) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTableMetaData")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorTableMetaData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableInfo", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TableInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnInfo", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ColumnInfo)); err != nil {
		return err
	}
	for _, v := range p.ColumnInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatisticsInfo", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.StatisticsInfo)); err != nil {
		return err
	}
	for _, v := range p.StatisticsInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InnodbTableStatsInfo", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InnodbTableStatsInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTableInfo", thrift.STRUCT, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CreateTableInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLAdvisorTableMetaData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorTableMetaData(%+v)", *p)

}

func (p *SQLAdvisorTableMetaData) DeepEqual(ano *SQLAdvisorTableMetaData) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.ColumnInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.StatisticsInfo) {
		return false
	}
	if !p.Field5DeepEqual(ano.InnodbTableStatsInfo) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTableInfo) {
		return false
	}
	return true
}

func (p *SQLAdvisorTableMetaData) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableMetaData) Field2DeepEqual(src *SQLAdvisorTableInfo) bool {

	if !p.TableInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SQLAdvisorTableMetaData) Field3DeepEqual(src []*SQLAdvisorColumnInfo) bool {

	if len(p.ColumnInfo) != len(src) {
		return false
	}
	for i, v := range p.ColumnInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SQLAdvisorTableMetaData) Field4DeepEqual(src []*SQLAdvisorStatisticsInfo) bool {

	if len(p.StatisticsInfo) != len(src) {
		return false
	}
	for i, v := range p.StatisticsInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SQLAdvisorTableMetaData) Field5DeepEqual(src *SQLAdvisorInnodbTableStatsInfo) bool {

	if !p.InnodbTableStatsInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SQLAdvisorTableMetaData) Field6DeepEqual(src *SQLAdvisorCreateTableInfo) bool {

	if !p.CreateTableInfo.DeepEqual(src) {
		return false
	}
	return true
}

type SQLAdvisorTableInfo struct {
	TableCatalog   string `thrift:"TableCatalog,1,required" frugal:"1,required,string" json:"TableCatalog"`
	TableSchema    string `thrift:"TableSchema,2,required" frugal:"2,required,string" json:"TableSchema"`
	TableName      string `thrift:"TableName,3,required" frugal:"3,required,string" json:"TableName"`
	TableType      string `thrift:"TableType,4,required" frugal:"4,required,string" json:"TableType"`
	Engine         string `thrift:"Engine,5,required" frugal:"5,required,string" json:"Engine"`
	Version        string `thrift:"Version,6,required" frugal:"6,required,string" json:"Version"`
	RowFormat      string `thrift:"RowFormat,7,required" frugal:"7,required,string" json:"RowFormat"`
	TableRows      string `thrift:"TableRows,8,required" frugal:"8,required,string" json:"TableRows"`
	AvgRowLength   string `thrift:"AvgRowLength,9,required" frugal:"9,required,string" json:"AvgRowLength"`
	DataLength     string `thrift:"DataLength,10,required" frugal:"10,required,string" json:"DataLength"`
	MaxDataLength  string `thrift:"MaxDataLength,11,required" frugal:"11,required,string" json:"MaxDataLength"`
	IndexLength    string `thrift:"IndexLength,12,required" frugal:"12,required,string" json:"IndexLength"`
	DataFree       string `thrift:"DataFree,13,required" frugal:"13,required,string" json:"DataFree"`
	AutoIncrement  string `thrift:"AutoIncrement,14,required" frugal:"14,required,string" json:"AutoIncrement"`
	CreateTime     string `thrift:"CreateTime,15,required" frugal:"15,required,string" json:"CreateTime"`
	UpdateTime     string `thrift:"UpdateTime,16,required" frugal:"16,required,string" json:"UpdateTime"`
	CheckTime      string `thrift:"CheckTime,17,required" frugal:"17,required,string" json:"CheckTime"`
	TableCollation string `thrift:"TableCollation,18,required" frugal:"18,required,string" json:"TableCollation"`
	CheckSum       string `thrift:"CheckSum,19,required" frugal:"19,required,string" json:"CheckSum"`
	CreateOptions  string `thrift:"CreateOptions,20,required" frugal:"20,required,string" json:"CreateOptions"`
	TableComment   string `thrift:"TableComment,21,required" frugal:"21,required,string" json:"TableComment"`
}

func NewSQLAdvisorTableInfo() *SQLAdvisorTableInfo {
	return &SQLAdvisorTableInfo{}
}

func (p *SQLAdvisorTableInfo) InitDefault() {
}

func (p *SQLAdvisorTableInfo) GetTableCatalog() (v string) {
	return p.TableCatalog
}

func (p *SQLAdvisorTableInfo) GetTableSchema() (v string) {
	return p.TableSchema
}

func (p *SQLAdvisorTableInfo) GetTableName() (v string) {
	return p.TableName
}

func (p *SQLAdvisorTableInfo) GetTableType() (v string) {
	return p.TableType
}

func (p *SQLAdvisorTableInfo) GetEngine() (v string) {
	return p.Engine
}

func (p *SQLAdvisorTableInfo) GetVersion() (v string) {
	return p.Version
}

func (p *SQLAdvisorTableInfo) GetRowFormat() (v string) {
	return p.RowFormat
}

func (p *SQLAdvisorTableInfo) GetTableRows() (v string) {
	return p.TableRows
}

func (p *SQLAdvisorTableInfo) GetAvgRowLength() (v string) {
	return p.AvgRowLength
}

func (p *SQLAdvisorTableInfo) GetDataLength() (v string) {
	return p.DataLength
}

func (p *SQLAdvisorTableInfo) GetMaxDataLength() (v string) {
	return p.MaxDataLength
}

func (p *SQLAdvisorTableInfo) GetIndexLength() (v string) {
	return p.IndexLength
}

func (p *SQLAdvisorTableInfo) GetDataFree() (v string) {
	return p.DataFree
}

func (p *SQLAdvisorTableInfo) GetAutoIncrement() (v string) {
	return p.AutoIncrement
}

func (p *SQLAdvisorTableInfo) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *SQLAdvisorTableInfo) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *SQLAdvisorTableInfo) GetCheckTime() (v string) {
	return p.CheckTime
}

func (p *SQLAdvisorTableInfo) GetTableCollation() (v string) {
	return p.TableCollation
}

func (p *SQLAdvisorTableInfo) GetCheckSum() (v string) {
	return p.CheckSum
}

func (p *SQLAdvisorTableInfo) GetCreateOptions() (v string) {
	return p.CreateOptions
}

func (p *SQLAdvisorTableInfo) GetTableComment() (v string) {
	return p.TableComment
}
func (p *SQLAdvisorTableInfo) SetTableCatalog(val string) {
	p.TableCatalog = val
}
func (p *SQLAdvisorTableInfo) SetTableSchema(val string) {
	p.TableSchema = val
}
func (p *SQLAdvisorTableInfo) SetTableName(val string) {
	p.TableName = val
}
func (p *SQLAdvisorTableInfo) SetTableType(val string) {
	p.TableType = val
}
func (p *SQLAdvisorTableInfo) SetEngine(val string) {
	p.Engine = val
}
func (p *SQLAdvisorTableInfo) SetVersion(val string) {
	p.Version = val
}
func (p *SQLAdvisorTableInfo) SetRowFormat(val string) {
	p.RowFormat = val
}
func (p *SQLAdvisorTableInfo) SetTableRows(val string) {
	p.TableRows = val
}
func (p *SQLAdvisorTableInfo) SetAvgRowLength(val string) {
	p.AvgRowLength = val
}
func (p *SQLAdvisorTableInfo) SetDataLength(val string) {
	p.DataLength = val
}
func (p *SQLAdvisorTableInfo) SetMaxDataLength(val string) {
	p.MaxDataLength = val
}
func (p *SQLAdvisorTableInfo) SetIndexLength(val string) {
	p.IndexLength = val
}
func (p *SQLAdvisorTableInfo) SetDataFree(val string) {
	p.DataFree = val
}
func (p *SQLAdvisorTableInfo) SetAutoIncrement(val string) {
	p.AutoIncrement = val
}
func (p *SQLAdvisorTableInfo) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *SQLAdvisorTableInfo) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *SQLAdvisorTableInfo) SetCheckTime(val string) {
	p.CheckTime = val
}
func (p *SQLAdvisorTableInfo) SetTableCollation(val string) {
	p.TableCollation = val
}
func (p *SQLAdvisorTableInfo) SetCheckSum(val string) {
	p.CheckSum = val
}
func (p *SQLAdvisorTableInfo) SetCreateOptions(val string) {
	p.CreateOptions = val
}
func (p *SQLAdvisorTableInfo) SetTableComment(val string) {
	p.TableComment = val
}

var fieldIDToName_SQLAdvisorTableInfo = map[int16]string{
	1:  "TableCatalog",
	2:  "TableSchema",
	3:  "TableName",
	4:  "TableType",
	5:  "Engine",
	6:  "Version",
	7:  "RowFormat",
	8:  "TableRows",
	9:  "AvgRowLength",
	10: "DataLength",
	11: "MaxDataLength",
	12: "IndexLength",
	13: "DataFree",
	14: "AutoIncrement",
	15: "CreateTime",
	16: "UpdateTime",
	17: "CheckTime",
	18: "TableCollation",
	19: "CheckSum",
	20: "CreateOptions",
	21: "TableComment",
}

func (p *SQLAdvisorTableInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTableInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTableCatalog bool = false
	var issetTableSchema bool = false
	var issetTableName bool = false
	var issetTableType bool = false
	var issetEngine bool = false
	var issetVersion bool = false
	var issetRowFormat bool = false
	var issetTableRows bool = false
	var issetAvgRowLength bool = false
	var issetDataLength bool = false
	var issetMaxDataLength bool = false
	var issetIndexLength bool = false
	var issetDataFree bool = false
	var issetAutoIncrement bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetCheckTime bool = false
	var issetTableCollation bool = false
	var issetCheckSum bool = false
	var issetCreateOptions bool = false
	var issetTableComment bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableCatalog = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEngine = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetRowFormat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableRows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvgRowLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxDataLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFree = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetAutoIncrement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetCheckTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableCollation = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
				issetCheckSum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateOptions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTableCatalog {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableSchema {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTableType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEngine {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRowFormat {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetTableRows {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetAvgRowLength {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetDataLength {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetMaxDataLength {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetIndexLength {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetDataFree {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetAutoIncrement {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetCheckTime {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetTableCollation {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetCheckSum {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetCreateOptions {
		fieldId = 20
		goto RequiredFieldNotSetError
	}

	if !issetTableComment {
		fieldId = 21
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorTableInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorTableInfo[fieldId]))
}

func (p *SQLAdvisorTableInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableCatalog = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableSchema = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableType = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Engine = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RowFormat = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableRows = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvgRowLength = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataLength = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxDataLength = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexLength = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataFree = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AutoIncrement = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CheckTime = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableCollation = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CheckSum = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateOptions = _field
	return nil
}
func (p *SQLAdvisorTableInfo) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableComment = _field
	return nil
}

func (p *SQLAdvisorTableInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTableInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorTableInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableCatalog", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableCatalog); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableSchema", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Engine", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Engine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RowFormat", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RowFormat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableRows", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableRows); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvgRowLength", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvgRowLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataLength", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxDataLength", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MaxDataLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexLength", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IndexLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFree", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataFree); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoIncrement", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AutoIncrement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CheckTime", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CheckTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableCollation", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableCollation); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CheckSum", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CheckSum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateOptions", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateOptions); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableComment", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableComment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *SQLAdvisorTableInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorTableInfo(%+v)", *p)

}

func (p *SQLAdvisorTableInfo) DeepEqual(ano *SQLAdvisorTableInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TableCatalog) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableSchema) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field4DeepEqual(ano.TableType) {
		return false
	}
	if !p.Field5DeepEqual(ano.Engine) {
		return false
	}
	if !p.Field6DeepEqual(ano.Version) {
		return false
	}
	if !p.Field7DeepEqual(ano.RowFormat) {
		return false
	}
	if !p.Field8DeepEqual(ano.TableRows) {
		return false
	}
	if !p.Field9DeepEqual(ano.AvgRowLength) {
		return false
	}
	if !p.Field10DeepEqual(ano.DataLength) {
		return false
	}
	if !p.Field11DeepEqual(ano.MaxDataLength) {
		return false
	}
	if !p.Field12DeepEqual(ano.IndexLength) {
		return false
	}
	if !p.Field13DeepEqual(ano.DataFree) {
		return false
	}
	if !p.Field14DeepEqual(ano.AutoIncrement) {
		return false
	}
	if !p.Field15DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field16DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field17DeepEqual(ano.CheckTime) {
		return false
	}
	if !p.Field18DeepEqual(ano.TableCollation) {
		return false
	}
	if !p.Field19DeepEqual(ano.CheckSum) {
		return false
	}
	if !p.Field20DeepEqual(ano.CreateOptions) {
		return false
	}
	if !p.Field21DeepEqual(ano.TableComment) {
		return false
	}
	return true
}

func (p *SQLAdvisorTableInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TableCatalog, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableSchema, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TableType, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Engine, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.RowFormat, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.TableRows, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field9DeepEqual(src string) bool {

	if strings.Compare(p.AvgRowLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field10DeepEqual(src string) bool {

	if strings.Compare(p.DataLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field11DeepEqual(src string) bool {

	if strings.Compare(p.MaxDataLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field12DeepEqual(src string) bool {

	if strings.Compare(p.IndexLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field13DeepEqual(src string) bool {

	if strings.Compare(p.DataFree, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field14DeepEqual(src string) bool {

	if strings.Compare(p.AutoIncrement, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field15DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field16DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field17DeepEqual(src string) bool {

	if strings.Compare(p.CheckTime, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field18DeepEqual(src string) bool {

	if strings.Compare(p.TableCollation, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field19DeepEqual(src string) bool {

	if strings.Compare(p.CheckSum, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field20DeepEqual(src string) bool {

	if strings.Compare(p.CreateOptions, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTableInfo) Field21DeepEqual(src string) bool {

	if strings.Compare(p.TableComment, src) != 0 {
		return false
	}
	return true
}

type SQLAdvisorColumnInfo struct {
	TableCatalog           string `thrift:"TableCatalog,1,required" frugal:"1,required,string" json:"TableCatalog"`
	TableSchema            string `thrift:"TableSchema,2,required" frugal:"2,required,string" json:"TableSchema"`
	TableName              string `thrift:"TableName,3,required" frugal:"3,required,string" json:"TableName"`
	ColumnName             string `thrift:"ColumnName,4,required" frugal:"4,required,string" json:"ColumnName"`
	OrdinalPosition        string `thrift:"OrdinalPosition,5,required" frugal:"5,required,string" json:"OrdinalPosition"`
	ColumnDefault          string `thrift:"ColumnDefault,6,required" frugal:"6,required,string" json:"ColumnDefault"`
	IsNullable             string `thrift:"IsNullable,7,required" frugal:"7,required,string" json:"IsNullable"`
	DataType               string `thrift:"DataType,8,required" frugal:"8,required,string" json:"DataType"`
	CharacterMaximumLength string `thrift:"CharacterMaximumLength,9,required" frugal:"9,required,string" json:"CharacterMaximumLength"`
	CharacterOctetLength   string `thrift:"CharacterOctetLength,10,required" frugal:"10,required,string" json:"CharacterOctetLength"`
	NumericPrecision       string `thrift:"NumericPrecision,11,required" frugal:"11,required,string" json:"NumericPrecision"`
	CharacterSetName       string `thrift:"CharacterSetName,12,required" frugal:"12,required,string" json:"CharacterSetName"`
	CollationName          string `thrift:"CollationName,13,required" frugal:"13,required,string" json:"CollationName"`
	ColumnType             string `thrift:"ColumnType,14,required" frugal:"14,required,string" json:"ColumnType"`
	ColumnKey              string `thrift:"ColumnKey,15,required" frugal:"15,required,string" json:"ColumnKey"`
	Extra                  string `thrift:"Extra,16,required" frugal:"16,required,string" json:"Extra"`
	Privileges             string `thrift:"Privileges,17,required" frugal:"17,required,string" json:"Privileges"`
	ColumnComment          string `thrift:"ColumnComment,18,required" frugal:"18,required,string" json:"ColumnComment"`
	GenerationExpression   string `thrift:"GenerationExpression,19,required" frugal:"19,required,string" json:"GenerationExpression"`
	SrsID                  string `thrift:"SrsID,20,required" frugal:"20,required,string" json:"SrsID"`
}

func NewSQLAdvisorColumnInfo() *SQLAdvisorColumnInfo {
	return &SQLAdvisorColumnInfo{}
}

func (p *SQLAdvisorColumnInfo) InitDefault() {
}

func (p *SQLAdvisorColumnInfo) GetTableCatalog() (v string) {
	return p.TableCatalog
}

func (p *SQLAdvisorColumnInfo) GetTableSchema() (v string) {
	return p.TableSchema
}

func (p *SQLAdvisorColumnInfo) GetTableName() (v string) {
	return p.TableName
}

func (p *SQLAdvisorColumnInfo) GetColumnName() (v string) {
	return p.ColumnName
}

func (p *SQLAdvisorColumnInfo) GetOrdinalPosition() (v string) {
	return p.OrdinalPosition
}

func (p *SQLAdvisorColumnInfo) GetColumnDefault() (v string) {
	return p.ColumnDefault
}

func (p *SQLAdvisorColumnInfo) GetIsNullable() (v string) {
	return p.IsNullable
}

func (p *SQLAdvisorColumnInfo) GetDataType() (v string) {
	return p.DataType
}

func (p *SQLAdvisorColumnInfo) GetCharacterMaximumLength() (v string) {
	return p.CharacterMaximumLength
}

func (p *SQLAdvisorColumnInfo) GetCharacterOctetLength() (v string) {
	return p.CharacterOctetLength
}

func (p *SQLAdvisorColumnInfo) GetNumericPrecision() (v string) {
	return p.NumericPrecision
}

func (p *SQLAdvisorColumnInfo) GetCharacterSetName() (v string) {
	return p.CharacterSetName
}

func (p *SQLAdvisorColumnInfo) GetCollationName() (v string) {
	return p.CollationName
}

func (p *SQLAdvisorColumnInfo) GetColumnType() (v string) {
	return p.ColumnType
}

func (p *SQLAdvisorColumnInfo) GetColumnKey() (v string) {
	return p.ColumnKey
}

func (p *SQLAdvisorColumnInfo) GetExtra() (v string) {
	return p.Extra
}

func (p *SQLAdvisorColumnInfo) GetPrivileges() (v string) {
	return p.Privileges
}

func (p *SQLAdvisorColumnInfo) GetColumnComment() (v string) {
	return p.ColumnComment
}

func (p *SQLAdvisorColumnInfo) GetGenerationExpression() (v string) {
	return p.GenerationExpression
}

func (p *SQLAdvisorColumnInfo) GetSrsID() (v string) {
	return p.SrsID
}
func (p *SQLAdvisorColumnInfo) SetTableCatalog(val string) {
	p.TableCatalog = val
}
func (p *SQLAdvisorColumnInfo) SetTableSchema(val string) {
	p.TableSchema = val
}
func (p *SQLAdvisorColumnInfo) SetTableName(val string) {
	p.TableName = val
}
func (p *SQLAdvisorColumnInfo) SetColumnName(val string) {
	p.ColumnName = val
}
func (p *SQLAdvisorColumnInfo) SetOrdinalPosition(val string) {
	p.OrdinalPosition = val
}
func (p *SQLAdvisorColumnInfo) SetColumnDefault(val string) {
	p.ColumnDefault = val
}
func (p *SQLAdvisorColumnInfo) SetIsNullable(val string) {
	p.IsNullable = val
}
func (p *SQLAdvisorColumnInfo) SetDataType(val string) {
	p.DataType = val
}
func (p *SQLAdvisorColumnInfo) SetCharacterMaximumLength(val string) {
	p.CharacterMaximumLength = val
}
func (p *SQLAdvisorColumnInfo) SetCharacterOctetLength(val string) {
	p.CharacterOctetLength = val
}
func (p *SQLAdvisorColumnInfo) SetNumericPrecision(val string) {
	p.NumericPrecision = val
}
func (p *SQLAdvisorColumnInfo) SetCharacterSetName(val string) {
	p.CharacterSetName = val
}
func (p *SQLAdvisorColumnInfo) SetCollationName(val string) {
	p.CollationName = val
}
func (p *SQLAdvisorColumnInfo) SetColumnType(val string) {
	p.ColumnType = val
}
func (p *SQLAdvisorColumnInfo) SetColumnKey(val string) {
	p.ColumnKey = val
}
func (p *SQLAdvisorColumnInfo) SetExtra(val string) {
	p.Extra = val
}
func (p *SQLAdvisorColumnInfo) SetPrivileges(val string) {
	p.Privileges = val
}
func (p *SQLAdvisorColumnInfo) SetColumnComment(val string) {
	p.ColumnComment = val
}
func (p *SQLAdvisorColumnInfo) SetGenerationExpression(val string) {
	p.GenerationExpression = val
}
func (p *SQLAdvisorColumnInfo) SetSrsID(val string) {
	p.SrsID = val
}

var fieldIDToName_SQLAdvisorColumnInfo = map[int16]string{
	1:  "TableCatalog",
	2:  "TableSchema",
	3:  "TableName",
	4:  "ColumnName",
	5:  "OrdinalPosition",
	6:  "ColumnDefault",
	7:  "IsNullable",
	8:  "DataType",
	9:  "CharacterMaximumLength",
	10: "CharacterOctetLength",
	11: "NumericPrecision",
	12: "CharacterSetName",
	13: "CollationName",
	14: "ColumnType",
	15: "ColumnKey",
	16: "Extra",
	17: "Privileges",
	18: "ColumnComment",
	19: "GenerationExpression",
	20: "SrsID",
}

func (p *SQLAdvisorColumnInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorColumnInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTableCatalog bool = false
	var issetTableSchema bool = false
	var issetTableName bool = false
	var issetColumnName bool = false
	var issetOrdinalPosition bool = false
	var issetColumnDefault bool = false
	var issetIsNullable bool = false
	var issetDataType bool = false
	var issetCharacterMaximumLength bool = false
	var issetCharacterOctetLength bool = false
	var issetNumericPrecision bool = false
	var issetCharacterSetName bool = false
	var issetCollationName bool = false
	var issetColumnType bool = false
	var issetColumnKey bool = false
	var issetExtra bool = false
	var issetPrivileges bool = false
	var issetColumnComment bool = false
	var issetGenerationExpression bool = false
	var issetSrsID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableCatalog = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrdinalPosition = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnDefault = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsNullable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCharacterMaximumLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCharacterOctetLength = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetNumericPrecision = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetCharacterSetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetCollationName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetExtra = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrivileges = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
				issetGenerationExpression = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
				issetSrsID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTableCatalog {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableSchema {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetColumnName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetOrdinalPosition {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetColumnDefault {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetIsNullable {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetCharacterMaximumLength {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCharacterOctetLength {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetNumericPrecision {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetCharacterSetName {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetCollationName {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetColumnType {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetColumnKey {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetExtra {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetPrivileges {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetColumnComment {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetGenerationExpression {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetSrsID {
		fieldId = 20
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorColumnInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorColumnInfo[fieldId]))
}

func (p *SQLAdvisorColumnInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableCatalog = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableSchema = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnName = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrdinalPosition = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnDefault = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsNullable = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataType = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CharacterMaximumLength = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CharacterOctetLength = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NumericPrecision = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CharacterSetName = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CollationName = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnType = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnKey = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Extra = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Privileges = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnComment = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GenerationExpression = _field
	return nil
}
func (p *SQLAdvisorColumnInfo) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SrsID = _field
	return nil
}

func (p *SQLAdvisorColumnInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorColumnInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorColumnInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableCatalog", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableCatalog); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableSchema", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrdinalPosition", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrdinalPosition); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnDefault", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnDefault); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsNullable", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IsNullable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DataType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CharacterMaximumLength", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CharacterMaximumLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CharacterOctetLength", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CharacterOctetLength); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NumericPrecision", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NumericPrecision); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CharacterSetName", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CharacterSetName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CollationName", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CollationName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnType", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnKey", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Extra", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Extra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Privileges", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Privileges); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnComment", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnComment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GenerationExpression", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GenerationExpression); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SrsID", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SrsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *SQLAdvisorColumnInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorColumnInfo(%+v)", *p)

}

func (p *SQLAdvisorColumnInfo) DeepEqual(ano *SQLAdvisorColumnInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TableCatalog) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableSchema) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field4DeepEqual(ano.ColumnName) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrdinalPosition) {
		return false
	}
	if !p.Field6DeepEqual(ano.ColumnDefault) {
		return false
	}
	if !p.Field7DeepEqual(ano.IsNullable) {
		return false
	}
	if !p.Field8DeepEqual(ano.DataType) {
		return false
	}
	if !p.Field9DeepEqual(ano.CharacterMaximumLength) {
		return false
	}
	if !p.Field10DeepEqual(ano.CharacterOctetLength) {
		return false
	}
	if !p.Field11DeepEqual(ano.NumericPrecision) {
		return false
	}
	if !p.Field12DeepEqual(ano.CharacterSetName) {
		return false
	}
	if !p.Field13DeepEqual(ano.CollationName) {
		return false
	}
	if !p.Field14DeepEqual(ano.ColumnType) {
		return false
	}
	if !p.Field15DeepEqual(ano.ColumnKey) {
		return false
	}
	if !p.Field16DeepEqual(ano.Extra) {
		return false
	}
	if !p.Field17DeepEqual(ano.Privileges) {
		return false
	}
	if !p.Field18DeepEqual(ano.ColumnComment) {
		return false
	}
	if !p.Field19DeepEqual(ano.GenerationExpression) {
		return false
	}
	if !p.Field20DeepEqual(ano.SrsID) {
		return false
	}
	return true
}

func (p *SQLAdvisorColumnInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TableCatalog, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableSchema, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ColumnName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.OrdinalPosition, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.ColumnDefault, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.IsNullable, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.DataType, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field9DeepEqual(src string) bool {

	if strings.Compare(p.CharacterMaximumLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field10DeepEqual(src string) bool {

	if strings.Compare(p.CharacterOctetLength, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field11DeepEqual(src string) bool {

	if strings.Compare(p.NumericPrecision, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field12DeepEqual(src string) bool {

	if strings.Compare(p.CharacterSetName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field13DeepEqual(src string) bool {

	if strings.Compare(p.CollationName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field14DeepEqual(src string) bool {

	if strings.Compare(p.ColumnType, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field15DeepEqual(src string) bool {

	if strings.Compare(p.ColumnKey, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field16DeepEqual(src string) bool {

	if strings.Compare(p.Extra, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field17DeepEqual(src string) bool {

	if strings.Compare(p.Privileges, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field18DeepEqual(src string) bool {

	if strings.Compare(p.ColumnComment, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field19DeepEqual(src string) bool {

	if strings.Compare(p.GenerationExpression, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorColumnInfo) Field20DeepEqual(src string) bool {

	if strings.Compare(p.SrsID, src) != 0 {
		return false
	}
	return true
}

type SQLAdvisorStatisticsInfo struct {
	TableCatalog string `thrift:"TableCatalog,1,required" frugal:"1,required,string" json:"TableCatalog"`
	TableSchema  string `thrift:"TableSchema,2,required" frugal:"2,required,string" json:"TableSchema"`
	TableName    string `thrift:"TableName,3,required" frugal:"3,required,string" json:"TableName"`
	NonUnique    string `thrift:"NonUnique,4,required" frugal:"4,required,string" json:"NonUnique"`
	IndexSchema  string `thrift:"IndexSchema,5,required" frugal:"5,required,string" json:"IndexSchema"`
	IndexName    string `thrift:"IndexName,6,required" frugal:"6,required,string" json:"IndexName"`
	SeqInIndex   string `thrift:"SeqInIndex,7,required" frugal:"7,required,string" json:"SeqInIndex"`
	ColumnName   string `thrift:"ColumnName,8,required" frugal:"8,required,string" json:"ColumnName"`
	Collation    string `thrift:"Collation,9,required" frugal:"9,required,string" json:"Collation"`
	Cardinality  string `thrift:"Cardinality,10,required" frugal:"10,required,string" json:"Cardinality"`
	SubPart      string `thrift:"SubPart,11,required" frugal:"11,required,string" json:"SubPart"`
	Packed       string `thrift:"Packed,12,required" frugal:"12,required,string" json:"Packed"`
	Nullable     string `thrift:"Nullable,13,required" frugal:"13,required,string" json:"Nullable"`
	IndexType    string `thrift:"IndexType,14,required" frugal:"14,required,string" json:"IndexType"`
	Comment      string `thrift:"Comment,15,required" frugal:"15,required,string" json:"Comment"`
	IndexComment string `thrift:"IndexComment,16,required" frugal:"16,required,string" json:"IndexComment"`
	IsVisible    string `thrift:"IsVisible,17,required" frugal:"17,required,string" json:"IsVisible"`
	Expression   string `thrift:"Expression,18,required" frugal:"18,required,string" json:"Expression"`
}

func NewSQLAdvisorStatisticsInfo() *SQLAdvisorStatisticsInfo {
	return &SQLAdvisorStatisticsInfo{}
}

func (p *SQLAdvisorStatisticsInfo) InitDefault() {
}

func (p *SQLAdvisorStatisticsInfo) GetTableCatalog() (v string) {
	return p.TableCatalog
}

func (p *SQLAdvisorStatisticsInfo) GetTableSchema() (v string) {
	return p.TableSchema
}

func (p *SQLAdvisorStatisticsInfo) GetTableName() (v string) {
	return p.TableName
}

func (p *SQLAdvisorStatisticsInfo) GetNonUnique() (v string) {
	return p.NonUnique
}

func (p *SQLAdvisorStatisticsInfo) GetIndexSchema() (v string) {
	return p.IndexSchema
}

func (p *SQLAdvisorStatisticsInfo) GetIndexName() (v string) {
	return p.IndexName
}

func (p *SQLAdvisorStatisticsInfo) GetSeqInIndex() (v string) {
	return p.SeqInIndex
}

func (p *SQLAdvisorStatisticsInfo) GetColumnName() (v string) {
	return p.ColumnName
}

func (p *SQLAdvisorStatisticsInfo) GetCollation() (v string) {
	return p.Collation
}

func (p *SQLAdvisorStatisticsInfo) GetCardinality() (v string) {
	return p.Cardinality
}

func (p *SQLAdvisorStatisticsInfo) GetSubPart() (v string) {
	return p.SubPart
}

func (p *SQLAdvisorStatisticsInfo) GetPacked() (v string) {
	return p.Packed
}

func (p *SQLAdvisorStatisticsInfo) GetNullable() (v string) {
	return p.Nullable
}

func (p *SQLAdvisorStatisticsInfo) GetIndexType() (v string) {
	return p.IndexType
}

func (p *SQLAdvisorStatisticsInfo) GetComment() (v string) {
	return p.Comment
}

func (p *SQLAdvisorStatisticsInfo) GetIndexComment() (v string) {
	return p.IndexComment
}

func (p *SQLAdvisorStatisticsInfo) GetIsVisible() (v string) {
	return p.IsVisible
}

func (p *SQLAdvisorStatisticsInfo) GetExpression() (v string) {
	return p.Expression
}
func (p *SQLAdvisorStatisticsInfo) SetTableCatalog(val string) {
	p.TableCatalog = val
}
func (p *SQLAdvisorStatisticsInfo) SetTableSchema(val string) {
	p.TableSchema = val
}
func (p *SQLAdvisorStatisticsInfo) SetTableName(val string) {
	p.TableName = val
}
func (p *SQLAdvisorStatisticsInfo) SetNonUnique(val string) {
	p.NonUnique = val
}
func (p *SQLAdvisorStatisticsInfo) SetIndexSchema(val string) {
	p.IndexSchema = val
}
func (p *SQLAdvisorStatisticsInfo) SetIndexName(val string) {
	p.IndexName = val
}
func (p *SQLAdvisorStatisticsInfo) SetSeqInIndex(val string) {
	p.SeqInIndex = val
}
func (p *SQLAdvisorStatisticsInfo) SetColumnName(val string) {
	p.ColumnName = val
}
func (p *SQLAdvisorStatisticsInfo) SetCollation(val string) {
	p.Collation = val
}
func (p *SQLAdvisorStatisticsInfo) SetCardinality(val string) {
	p.Cardinality = val
}
func (p *SQLAdvisorStatisticsInfo) SetSubPart(val string) {
	p.SubPart = val
}
func (p *SQLAdvisorStatisticsInfo) SetPacked(val string) {
	p.Packed = val
}
func (p *SQLAdvisorStatisticsInfo) SetNullable(val string) {
	p.Nullable = val
}
func (p *SQLAdvisorStatisticsInfo) SetIndexType(val string) {
	p.IndexType = val
}
func (p *SQLAdvisorStatisticsInfo) SetComment(val string) {
	p.Comment = val
}
func (p *SQLAdvisorStatisticsInfo) SetIndexComment(val string) {
	p.IndexComment = val
}
func (p *SQLAdvisorStatisticsInfo) SetIsVisible(val string) {
	p.IsVisible = val
}
func (p *SQLAdvisorStatisticsInfo) SetExpression(val string) {
	p.Expression = val
}

var fieldIDToName_SQLAdvisorStatisticsInfo = map[int16]string{
	1:  "TableCatalog",
	2:  "TableSchema",
	3:  "TableName",
	4:  "NonUnique",
	5:  "IndexSchema",
	6:  "IndexName",
	7:  "SeqInIndex",
	8:  "ColumnName",
	9:  "Collation",
	10: "Cardinality",
	11: "SubPart",
	12: "Packed",
	13: "Nullable",
	14: "IndexType",
	15: "Comment",
	16: "IndexComment",
	17: "IsVisible",
	18: "Expression",
}

func (p *SQLAdvisorStatisticsInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorStatisticsInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTableCatalog bool = false
	var issetTableSchema bool = false
	var issetTableName bool = false
	var issetNonUnique bool = false
	var issetIndexSchema bool = false
	var issetIndexName bool = false
	var issetSeqInIndex bool = false
	var issetColumnName bool = false
	var issetCollation bool = false
	var issetCardinality bool = false
	var issetSubPart bool = false
	var issetPacked bool = false
	var issetNullable bool = false
	var issetIndexType bool = false
	var issetComment bool = false
	var issetIndexComment bool = false
	var issetIsVisible bool = false
	var issetExpression bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableCatalog = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetNonUnique = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexSchema = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetSeqInIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCollation = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCardinality = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetSubPart = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetPacked = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetNullable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsVisible = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpression = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTableCatalog {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableSchema {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNonUnique {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetIndexSchema {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetIndexName {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetSeqInIndex {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetColumnName {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetCollation {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCardinality {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetSubPart {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetPacked {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetNullable {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetIndexType {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetComment {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetIndexComment {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetIsVisible {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetExpression {
		fieldId = 18
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorStatisticsInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorStatisticsInfo[fieldId]))
}

func (p *SQLAdvisorStatisticsInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableCatalog = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableSchema = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NonUnique = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexSchema = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexName = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SeqInIndex = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnName = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Collation = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Cardinality = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubPart = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Packed = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Nullable = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexType = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Comment = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexComment = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsVisible = _field
	return nil
}
func (p *SQLAdvisorStatisticsInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Expression = _field
	return nil
}

func (p *SQLAdvisorStatisticsInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorStatisticsInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorStatisticsInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableCatalog", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableCatalog); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableSchema", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NonUnique", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NonUnique); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexSchema", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IndexSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IndexName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SeqInIndex", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SeqInIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnName", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Collation", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Collation); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Cardinality", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Cardinality); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SubPart", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubPart); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Packed", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Packed); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Nullable", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Nullable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexType", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IndexType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Comment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexComment", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IndexComment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsVisible", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IsVisible); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Expression", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Expression); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *SQLAdvisorStatisticsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorStatisticsInfo(%+v)", *p)

}

func (p *SQLAdvisorStatisticsInfo) DeepEqual(ano *SQLAdvisorStatisticsInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TableCatalog) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableSchema) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field4DeepEqual(ano.NonUnique) {
		return false
	}
	if !p.Field5DeepEqual(ano.IndexSchema) {
		return false
	}
	if !p.Field6DeepEqual(ano.IndexName) {
		return false
	}
	if !p.Field7DeepEqual(ano.SeqInIndex) {
		return false
	}
	if !p.Field8DeepEqual(ano.ColumnName) {
		return false
	}
	if !p.Field9DeepEqual(ano.Collation) {
		return false
	}
	if !p.Field10DeepEqual(ano.Cardinality) {
		return false
	}
	if !p.Field11DeepEqual(ano.SubPart) {
		return false
	}
	if !p.Field12DeepEqual(ano.Packed) {
		return false
	}
	if !p.Field13DeepEqual(ano.Nullable) {
		return false
	}
	if !p.Field14DeepEqual(ano.IndexType) {
		return false
	}
	if !p.Field15DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field16DeepEqual(ano.IndexComment) {
		return false
	}
	if !p.Field17DeepEqual(ano.IsVisible) {
		return false
	}
	if !p.Field18DeepEqual(ano.Expression) {
		return false
	}
	return true
}

func (p *SQLAdvisorStatisticsInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TableCatalog, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableSchema, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.NonUnique, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.IndexSchema, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.IndexName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.SeqInIndex, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.ColumnName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Collation, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field10DeepEqual(src string) bool {

	if strings.Compare(p.Cardinality, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field11DeepEqual(src string) bool {

	if strings.Compare(p.SubPart, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field12DeepEqual(src string) bool {

	if strings.Compare(p.Packed, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field13DeepEqual(src string) bool {

	if strings.Compare(p.Nullable, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field14DeepEqual(src string) bool {

	if strings.Compare(p.IndexType, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field15DeepEqual(src string) bool {

	if strings.Compare(p.Comment, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field16DeepEqual(src string) bool {

	if strings.Compare(p.IndexComment, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field17DeepEqual(src string) bool {

	if strings.Compare(p.IsVisible, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorStatisticsInfo) Field18DeepEqual(src string) bool {

	if strings.Compare(p.Expression, src) != 0 {
		return false
	}
	return true
}

type SQLAdvisorInnodbTableStatsInfo struct {
	DatabaseName         string `thrift:"DatabaseName,1,required" frugal:"1,required,string" json:"DatabaseName"`
	TableName            string `thrift:"TableName,2,required" frugal:"2,required,string" json:"TableName"`
	LastUpdate           string `thrift:"LastUpdate,3,required" frugal:"3,required,string" json:"LastUpdate"`
	NRows                string `thrift:"NRows,4,required" frugal:"4,required,string" json:"NRows"`
	ClusteredIndexSize   string `thrift:"ClusteredIndexSize,5,required" frugal:"5,required,string" json:"ClusteredIndexSize"`
	SumOfOtherIndexSizes string `thrift:"SumOfOtherIndexSizes,6,required" frugal:"6,required,string" json:"SumOfOtherIndexSizes"`
}

func NewSQLAdvisorInnodbTableStatsInfo() *SQLAdvisorInnodbTableStatsInfo {
	return &SQLAdvisorInnodbTableStatsInfo{}
}

func (p *SQLAdvisorInnodbTableStatsInfo) InitDefault() {
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetDatabaseName() (v string) {
	return p.DatabaseName
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetTableName() (v string) {
	return p.TableName
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetLastUpdate() (v string) {
	return p.LastUpdate
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetNRows() (v string) {
	return p.NRows
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetClusteredIndexSize() (v string) {
	return p.ClusteredIndexSize
}

func (p *SQLAdvisorInnodbTableStatsInfo) GetSumOfOtherIndexSizes() (v string) {
	return p.SumOfOtherIndexSizes
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetDatabaseName(val string) {
	p.DatabaseName = val
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetTableName(val string) {
	p.TableName = val
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetLastUpdate(val string) {
	p.LastUpdate = val
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetNRows(val string) {
	p.NRows = val
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetClusteredIndexSize(val string) {
	p.ClusteredIndexSize = val
}
func (p *SQLAdvisorInnodbTableStatsInfo) SetSumOfOtherIndexSizes(val string) {
	p.SumOfOtherIndexSizes = val
}

var fieldIDToName_SQLAdvisorInnodbTableStatsInfo = map[int16]string{
	1: "DatabaseName",
	2: "TableName",
	3: "LastUpdate",
	4: "NRows",
	5: "ClusteredIndexSize",
	6: "SumOfOtherIndexSizes",
}

func (p *SQLAdvisorInnodbTableStatsInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorInnodbTableStatsInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatabaseName bool = false
	var issetTableName bool = false
	var issetLastUpdate bool = false
	var issetNRows bool = false
	var issetClusteredIndexSize bool = false
	var issetSumOfOtherIndexSizes bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabaseName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastUpdate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetNRows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetClusteredIndexSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetSumOfOtherIndexSizes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatabaseName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLastUpdate {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNRows {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetClusteredIndexSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSumOfOtherIndexSizes {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorInnodbTableStatsInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorInnodbTableStatsInfo[fieldId]))
}

func (p *SQLAdvisorInnodbTableStatsInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatabaseName = _field
	return nil
}
func (p *SQLAdvisorInnodbTableStatsInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *SQLAdvisorInnodbTableStatsInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastUpdate = _field
	return nil
}
func (p *SQLAdvisorInnodbTableStatsInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NRows = _field
	return nil
}
func (p *SQLAdvisorInnodbTableStatsInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClusteredIndexSize = _field
	return nil
}
func (p *SQLAdvisorInnodbTableStatsInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SumOfOtherIndexSizes = _field
	return nil
}

func (p *SQLAdvisorInnodbTableStatsInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorInnodbTableStatsInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorInnodbTableStatsInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatabaseName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastUpdate", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastUpdate); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NRows", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NRows); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ClusteredIndexSize", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClusteredIndexSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SumOfOtherIndexSizes", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SumOfOtherIndexSizes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLAdvisorInnodbTableStatsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorInnodbTableStatsInfo(%+v)", *p)

}

func (p *SQLAdvisorInnodbTableStatsInfo) DeepEqual(ano *SQLAdvisorInnodbTableStatsInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field3DeepEqual(ano.LastUpdate) {
		return false
	}
	if !p.Field4DeepEqual(ano.NRows) {
		return false
	}
	if !p.Field5DeepEqual(ano.ClusteredIndexSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.SumOfOtherIndexSizes) {
		return false
	}
	return true
}

func (p *SQLAdvisorInnodbTableStatsInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DatabaseName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorInnodbTableStatsInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorInnodbTableStatsInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.LastUpdate, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorInnodbTableStatsInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.NRows, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorInnodbTableStatsInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ClusteredIndexSize, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorInnodbTableStatsInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.SumOfOtherIndexSizes, src) != 0 {
		return false
	}
	return true
}

type SQLAdvisorCreateTableInfo struct {
	Table       string `thrift:"Table,1,required" frugal:"1,required,string" json:"Table"`
	CreateTable string `thrift:"CreateTable,2,required" frugal:"2,required,string" json:"CreateTable"`
}

func NewSQLAdvisorCreateTableInfo() *SQLAdvisorCreateTableInfo {
	return &SQLAdvisorCreateTableInfo{}
}

func (p *SQLAdvisorCreateTableInfo) InitDefault() {
}

func (p *SQLAdvisorCreateTableInfo) GetTable() (v string) {
	return p.Table
}

func (p *SQLAdvisorCreateTableInfo) GetCreateTable() (v string) {
	return p.CreateTable
}
func (p *SQLAdvisorCreateTableInfo) SetTable(val string) {
	p.Table = val
}
func (p *SQLAdvisorCreateTableInfo) SetCreateTable(val string) {
	p.CreateTable = val
}

var fieldIDToName_SQLAdvisorCreateTableInfo = map[int16]string{
	1: "Table",
	2: "CreateTable",
}

func (p *SQLAdvisorCreateTableInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorCreateTableInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTable bool = false
	var issetCreateTable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCreateTable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorCreateTableInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorCreateTableInfo[fieldId]))
}

func (p *SQLAdvisorCreateTableInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Table = _field
	return nil
}
func (p *SQLAdvisorCreateTableInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTable = _field
	return nil
}

func (p *SQLAdvisorCreateTableInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorCreateTableInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorCreateTableInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorCreateTableInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Table", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Table); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorCreateTableInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTable", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorCreateTableInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorCreateTableInfo(%+v)", *p)

}

func (p *SQLAdvisorCreateTableInfo) DeepEqual(ano *SQLAdvisorCreateTableInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Table) {
		return false
	}
	if !p.Field2DeepEqual(ano.CreateTable) {
		return false
	}
	return true
}

func (p *SQLAdvisorCreateTableInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Table, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorCreateTableInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CreateTable, src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceVariablesReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceID   string       `thrift:"InstanceID,2,required" frugal:"2,required,string" json:"InstanceID"`
	TenantID     string       `thrift:"TenantID,3,required" frugal:"3,required,string" json:"TenantID"`
	Region       string       `thrift:"Region,4,required" frugal:"4,required,string" json:"Region"`
	Variables    []string     `thrift:"Variables,5,required" frugal:"5,required,list<string>" json:"Variables"`
}

func NewDescribeInstanceVariablesReq() *DescribeInstanceVariablesReq {
	return &DescribeInstanceVariablesReq{}
}

func (p *DescribeInstanceVariablesReq) InitDefault() {
}

func (p *DescribeInstanceVariablesReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeInstanceVariablesReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeInstanceVariablesReq) GetTenantID() (v string) {
	return p.TenantID
}

func (p *DescribeInstanceVariablesReq) GetRegion() (v string) {
	return p.Region
}

func (p *DescribeInstanceVariablesReq) GetVariables() (v []string) {
	return p.Variables
}
func (p *DescribeInstanceVariablesReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeInstanceVariablesReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeInstanceVariablesReq) SetTenantID(val string) {
	p.TenantID = val
}
func (p *DescribeInstanceVariablesReq) SetRegion(val string) {
	p.Region = val
}
func (p *DescribeInstanceVariablesReq) SetVariables(val []string) {
	p.Variables = val
}

var fieldIDToName_DescribeInstanceVariablesReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceID",
	3: "TenantID",
	4: "Region",
	5: "Variables",
}

func (p *DescribeInstanceVariablesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceVariablesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetTenantID bool = false
	var issetRegion bool = false
	var issetVariables bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetVariables = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTenantID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetVariables {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceVariablesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceVariablesReq[fieldId]))
}

func (p *DescribeInstanceVariablesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeInstanceVariablesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeInstanceVariablesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantID = _field
	return nil
}
func (p *DescribeInstanceVariablesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *DescribeInstanceVariablesReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Variables = _field
	return nil
}

func (p *DescribeInstanceVariablesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceVariablesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceVariablesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Variables", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Variables)); err != nil {
		return err
	}
	for _, v := range p.Variables {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstanceVariablesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceVariablesReq(%+v)", *p)

}

func (p *DescribeInstanceVariablesReq) DeepEqual(ano *DescribeInstanceVariablesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.TenantID) {
		return false
	}
	if !p.Field4DeepEqual(ano.Region) {
		return false
	}
	if !p.Field5DeepEqual(ano.Variables) {
		return false
	}
	return true
}

func (p *DescribeInstanceVariablesReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeInstanceVariablesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceVariablesReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TenantID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceVariablesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceVariablesReq) Field5DeepEqual(src []string) bool {

	if len(p.Variables) != len(src) {
		return false
	}
	for i, v := range p.Variables {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeInstanceVariablesResp struct {
	Variables map[string]string `thrift:"Variables,1,required" frugal:"1,required,map<string:string>" json:"Variables"`
}

func NewDescribeInstanceVariablesResp() *DescribeInstanceVariablesResp {
	return &DescribeInstanceVariablesResp{}
}

func (p *DescribeInstanceVariablesResp) InitDefault() {
}

func (p *DescribeInstanceVariablesResp) GetVariables() (v map[string]string) {
	return p.Variables
}
func (p *DescribeInstanceVariablesResp) SetVariables(val map[string]string) {
	p.Variables = val
}

var fieldIDToName_DescribeInstanceVariablesResp = map[int16]string{
	1: "Variables",
}

func (p *DescribeInstanceVariablesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceVariablesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetVariables bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetVariables = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetVariables {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceVariablesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceVariablesResp[fieldId]))
}

func (p *DescribeInstanceVariablesResp) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Variables = _field
	return nil
}

func (p *DescribeInstanceVariablesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceVariablesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceVariablesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceVariablesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Variables", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Variables)); err != nil {
		return err
	}
	for k, v := range p.Variables {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceVariablesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceVariablesResp(%+v)", *p)

}

func (p *DescribeInstanceVariablesResp) DeepEqual(ano *DescribeInstanceVariablesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Variables) {
		return false
	}
	return true
}

func (p *DescribeInstanceVariablesResp) Field1DeepEqual(src map[string]string) bool {

	if len(p.Variables) != len(src) {
		return false
	}
	for k, v := range p.Variables {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type SQLAdvisorVariableInfo struct {
	Name  string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Value string `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
}

func NewSQLAdvisorVariableInfo() *SQLAdvisorVariableInfo {
	return &SQLAdvisorVariableInfo{}
}

func (p *SQLAdvisorVariableInfo) InitDefault() {
}

func (p *SQLAdvisorVariableInfo) GetName() (v string) {
	return p.Name
}

func (p *SQLAdvisorVariableInfo) GetValue() (v string) {
	return p.Value
}
func (p *SQLAdvisorVariableInfo) SetName(val string) {
	p.Name = val
}
func (p *SQLAdvisorVariableInfo) SetValue(val string) {
	p.Value = val
}

var fieldIDToName_SQLAdvisorVariableInfo = map[int16]string{
	1: "Name",
	2: "Value",
}

func (p *SQLAdvisorVariableInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorVariableInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorVariableInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorVariableInfo[fieldId]))
}

func (p *SQLAdvisorVariableInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *SQLAdvisorVariableInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *SQLAdvisorVariableInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorVariableInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorVariableInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorVariableInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorVariableInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorVariableInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorVariableInfo(%+v)", *p)

}

func (p *SQLAdvisorVariableInfo) DeepEqual(ano *SQLAdvisorVariableInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *SQLAdvisorVariableInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorVariableInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}

type DescribePrimaryKeyRangeReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceID   string       `thrift:"InstanceID,2,required" frugal:"2,required,string" json:"InstanceID"`
	TenantID     string       `thrift:"TenantID,3,required" frugal:"3,required,string" json:"TenantID"`
	Region       string       `thrift:"Region,4,required" frugal:"4,required,string" json:"Region"`
	DbName       string       `thrift:"DbName,5,required" frugal:"5,required,string" json:"DbName"`
	TableName    string       `thrift:"TableName,6,required" frugal:"6,required,string" json:"TableName"`
	Columns      []string     `thrift:"Columns,7,required" frugal:"7,required,list<string>" json:"Columns"`
}

func NewDescribePrimaryKeyRangeReq() *DescribePrimaryKeyRangeReq {
	return &DescribePrimaryKeyRangeReq{}
}

func (p *DescribePrimaryKeyRangeReq) InitDefault() {
}

func (p *DescribePrimaryKeyRangeReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribePrimaryKeyRangeReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribePrimaryKeyRangeReq) GetTenantID() (v string) {
	return p.TenantID
}

func (p *DescribePrimaryKeyRangeReq) GetRegion() (v string) {
	return p.Region
}

func (p *DescribePrimaryKeyRangeReq) GetDbName() (v string) {
	return p.DbName
}

func (p *DescribePrimaryKeyRangeReq) GetTableName() (v string) {
	return p.TableName
}

func (p *DescribePrimaryKeyRangeReq) GetColumns() (v []string) {
	return p.Columns
}
func (p *DescribePrimaryKeyRangeReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribePrimaryKeyRangeReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribePrimaryKeyRangeReq) SetTenantID(val string) {
	p.TenantID = val
}
func (p *DescribePrimaryKeyRangeReq) SetRegion(val string) {
	p.Region = val
}
func (p *DescribePrimaryKeyRangeReq) SetDbName(val string) {
	p.DbName = val
}
func (p *DescribePrimaryKeyRangeReq) SetTableName(val string) {
	p.TableName = val
}
func (p *DescribePrimaryKeyRangeReq) SetColumns(val []string) {
	p.Columns = val
}

var fieldIDToName_DescribePrimaryKeyRangeReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceID",
	3: "TenantID",
	4: "Region",
	5: "DbName",
	6: "TableName",
	7: "Columns",
}

func (p *DescribePrimaryKeyRangeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePrimaryKeyRangeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetTenantID bool = false
	var issetRegion bool = false
	var issetDbName bool = false
	var issetTableName bool = false
	var issetColumns bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumns = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTenantID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetColumns {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribePrimaryKeyRangeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribePrimaryKeyRangeReq[fieldId]))
}

func (p *DescribePrimaryKeyRangeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantID = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *DescribePrimaryKeyRangeReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Columns = _field
	return nil
}

func (p *DescribePrimaryKeyRangeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePrimaryKeyRangeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribePrimaryKeyRangeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Columns", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Columns)); err != nil {
		return err
	}
	for _, v := range p.Columns {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribePrimaryKeyRangeReq(%+v)", *p)

}

func (p *DescribePrimaryKeyRangeReq) DeepEqual(ano *DescribePrimaryKeyRangeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.TenantID) {
		return false
	}
	if !p.Field4DeepEqual(ano.Region) {
		return false
	}
	if !p.Field5DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field6DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field7DeepEqual(ano.Columns) {
		return false
	}
	return true
}

func (p *DescribePrimaryKeyRangeReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TenantID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeReq) Field7DeepEqual(src []string) bool {

	if len(p.Columns) != len(src) {
		return false
	}
	for i, v := range p.Columns {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribePrimaryKeyRangeResp struct {
	Success        bool            `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
	PrimaryKeyInfo *PrimaryKeyInfo `thrift:"PrimaryKeyInfo,2,required" frugal:"2,required,PrimaryKeyInfo" json:"PrimaryKeyInfo"`
}

func NewDescribePrimaryKeyRangeResp() *DescribePrimaryKeyRangeResp {
	return &DescribePrimaryKeyRangeResp{}
}

func (p *DescribePrimaryKeyRangeResp) InitDefault() {
}

func (p *DescribePrimaryKeyRangeResp) GetSuccess() (v bool) {
	return p.Success
}

var DescribePrimaryKeyRangeResp_PrimaryKeyInfo_DEFAULT *PrimaryKeyInfo

func (p *DescribePrimaryKeyRangeResp) GetPrimaryKeyInfo() (v *PrimaryKeyInfo) {
	if !p.IsSetPrimaryKeyInfo() {
		return DescribePrimaryKeyRangeResp_PrimaryKeyInfo_DEFAULT
	}
	return p.PrimaryKeyInfo
}
func (p *DescribePrimaryKeyRangeResp) SetSuccess(val bool) {
	p.Success = val
}
func (p *DescribePrimaryKeyRangeResp) SetPrimaryKeyInfo(val *PrimaryKeyInfo) {
	p.PrimaryKeyInfo = val
}

var fieldIDToName_DescribePrimaryKeyRangeResp = map[int16]string{
	1: "Success",
	2: "PrimaryKeyInfo",
}

func (p *DescribePrimaryKeyRangeResp) IsSetPrimaryKeyInfo() bool {
	return p.PrimaryKeyInfo != nil
}

func (p *DescribePrimaryKeyRangeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePrimaryKeyRangeResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	var issetPrimaryKeyInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrimaryKeyInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPrimaryKeyInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribePrimaryKeyRangeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribePrimaryKeyRangeResp[fieldId]))
}

func (p *DescribePrimaryKeyRangeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *DescribePrimaryKeyRangeResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewPrimaryKeyInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PrimaryKeyInfo = _field
	return nil
}

func (p *DescribePrimaryKeyRangeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePrimaryKeyRangeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribePrimaryKeyRangeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrimaryKeyInfo", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PrimaryKeyInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribePrimaryKeyRangeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribePrimaryKeyRangeResp(%+v)", *p)

}

func (p *DescribePrimaryKeyRangeResp) DeepEqual(ano *DescribePrimaryKeyRangeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	if !p.Field2DeepEqual(ano.PrimaryKeyInfo) {
		return false
	}
	return true
}

func (p *DescribePrimaryKeyRangeResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *DescribePrimaryKeyRangeResp) Field2DeepEqual(src *PrimaryKeyInfo) bool {

	if !p.PrimaryKeyInfo.DeepEqual(src) {
		return false
	}
	return true
}

type PrimaryKeyInfo struct {
	MinNum []*PrimaryKeyValue `thrift:"MinNum,1,required" frugal:"1,required,list<PrimaryKeyValue>" json:"MinNum"`
	MaxNum []*PrimaryKeyValue `thrift:"MaxNum,2,required" frugal:"2,required,list<PrimaryKeyValue>" json:"MaxNum"`
}

func NewPrimaryKeyInfo() *PrimaryKeyInfo {
	return &PrimaryKeyInfo{}
}

func (p *PrimaryKeyInfo) InitDefault() {
}

func (p *PrimaryKeyInfo) GetMinNum() (v []*PrimaryKeyValue) {
	return p.MinNum
}

func (p *PrimaryKeyInfo) GetMaxNum() (v []*PrimaryKeyValue) {
	return p.MaxNum
}
func (p *PrimaryKeyInfo) SetMinNum(val []*PrimaryKeyValue) {
	p.MinNum = val
}
func (p *PrimaryKeyInfo) SetMaxNum(val []*PrimaryKeyValue) {
	p.MaxNum = val
}

var fieldIDToName_PrimaryKeyInfo = map[int16]string{
	1: "MinNum",
	2: "MaxNum",
}

func (p *PrimaryKeyInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PrimaryKeyInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMinNum bool = false
	var issetMaxNum bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMinNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMinNum {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMaxNum {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PrimaryKeyInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PrimaryKeyInfo[fieldId]))
}

func (p *PrimaryKeyInfo) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PrimaryKeyValue, 0, size)
	values := make([]PrimaryKeyValue, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MinNum = _field
	return nil
}
func (p *PrimaryKeyInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PrimaryKeyValue, 0, size)
	values := make([]PrimaryKeyValue, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MaxNum = _field
	return nil
}

func (p *PrimaryKeyInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PrimaryKeyInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("PrimaryKeyInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PrimaryKeyInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MinNum", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MinNum)); err != nil {
		return err
	}
	for _, v := range p.MinNum {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PrimaryKeyInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxNum", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MaxNum)); err != nil {
		return err
	}
	for _, v := range p.MaxNum {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PrimaryKeyInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PrimaryKeyInfo(%+v)", *p)

}

func (p *PrimaryKeyInfo) DeepEqual(ano *PrimaryKeyInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MinNum) {
		return false
	}
	if !p.Field2DeepEqual(ano.MaxNum) {
		return false
	}
	return true
}

func (p *PrimaryKeyInfo) Field1DeepEqual(src []*PrimaryKeyValue) bool {

	if len(p.MinNum) != len(src) {
		return false
	}
	for i, v := range p.MinNum {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *PrimaryKeyInfo) Field2DeepEqual(src []*PrimaryKeyValue) bool {

	if len(p.MaxNum) != len(src) {
		return false
	}
	for i, v := range p.MaxNum {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type PrimaryKeyValue struct {
	ColumnName string `thrift:"ColumnName,1,required" frugal:"1,required,string" json:"ColumnName"`
	Value      string `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
	Type       string `thrift:"Type,3,required" frugal:"3,required,string" json:"Type"`
}

func NewPrimaryKeyValue() *PrimaryKeyValue {
	return &PrimaryKeyValue{}
}

func (p *PrimaryKeyValue) InitDefault() {
}

func (p *PrimaryKeyValue) GetColumnName() (v string) {
	return p.ColumnName
}

func (p *PrimaryKeyValue) GetValue() (v string) {
	return p.Value
}

func (p *PrimaryKeyValue) GetType() (v string) {
	return p.Type
}
func (p *PrimaryKeyValue) SetColumnName(val string) {
	p.ColumnName = val
}
func (p *PrimaryKeyValue) SetValue(val string) {
	p.Value = val
}
func (p *PrimaryKeyValue) SetType(val string) {
	p.Type = val
}

var fieldIDToName_PrimaryKeyValue = map[int16]string{
	1: "ColumnName",
	2: "Value",
	3: "Type",
}

func (p *PrimaryKeyValue) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PrimaryKeyValue")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetColumnName bool = false
	var issetValue bool = false
	var issetType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumnName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetColumnName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PrimaryKeyValue[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PrimaryKeyValue[fieldId]))
}

func (p *PrimaryKeyValue) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ColumnName = _field
	return nil
}
func (p *PrimaryKeyValue) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *PrimaryKeyValue) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}

func (p *PrimaryKeyValue) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PrimaryKeyValue")

	var fieldId int16
	if err = oprot.WriteStructBegin("PrimaryKeyValue"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PrimaryKeyValue) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ColumnName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ColumnName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PrimaryKeyValue) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PrimaryKeyValue) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PrimaryKeyValue) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PrimaryKeyValue(%+v)", *p)

}

func (p *PrimaryKeyValue) DeepEqual(ano *PrimaryKeyValue) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ColumnName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Type) {
		return false
	}
	return true
}

func (p *PrimaryKeyValue) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ColumnName, src) != 0 {
		return false
	}
	return true
}
func (p *PrimaryKeyValue) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}
func (p *PrimaryKeyValue) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}

type DescribeSampleDataReq struct {
	InstanceType InstanceType       `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceID   string             `thrift:"InstanceID,2,required" frugal:"2,required,string" json:"InstanceID"`
	Region       string             `thrift:"Region,3,required" frugal:"3,required,string" json:"Region"`
	DbName       string             `thrift:"DbName,4,required" frugal:"4,required,string" json:"DbName"`
	TableName    string             `thrift:"TableName,5,required" frugal:"5,required,string" json:"TableName"`
	PrimaryKey   []string           `thrift:"PrimaryKey,6,required" frugal:"6,required,list<string>" json:"PrimaryKey"`
	Columns      []string           `thrift:"Columns,7,required" frugal:"7,required,list<string>" json:"Columns"`
	MinNum       []*PrimaryKeyValue `thrift:"MinNum,8,required" frugal:"8,required,list<PrimaryKeyValue>" json:"MinNum"`
	MaxNum       []*PrimaryKeyValue `thrift:"MaxNum,9,required" frugal:"9,required,list<PrimaryKeyValue>" json:"MaxNum"`
	OrderBy      SQLAdvisorOrderBy  `thrift:"OrderBy,10,required" frugal:"10,required,SQLAdvisorOrderBy" json:"OrderBy"`
	Limit        int32              `thrift:"Limit,11,required" frugal:"11,required,i32" json:"Limit"`
	TenantID     string             `thrift:"TenantID,12,required" frugal:"12,required,string" json:"TenantID"`
}

func NewDescribeSampleDataReq() *DescribeSampleDataReq {
	return &DescribeSampleDataReq{}
}

func (p *DescribeSampleDataReq) InitDefault() {
}

func (p *DescribeSampleDataReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeSampleDataReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *DescribeSampleDataReq) GetRegion() (v string) {
	return p.Region
}

func (p *DescribeSampleDataReq) GetDbName() (v string) {
	return p.DbName
}

func (p *DescribeSampleDataReq) GetTableName() (v string) {
	return p.TableName
}

func (p *DescribeSampleDataReq) GetPrimaryKey() (v []string) {
	return p.PrimaryKey
}

func (p *DescribeSampleDataReq) GetColumns() (v []string) {
	return p.Columns
}

func (p *DescribeSampleDataReq) GetMinNum() (v []*PrimaryKeyValue) {
	return p.MinNum
}

func (p *DescribeSampleDataReq) GetMaxNum() (v []*PrimaryKeyValue) {
	return p.MaxNum
}

func (p *DescribeSampleDataReq) GetOrderBy() (v SQLAdvisorOrderBy) {
	return p.OrderBy
}

func (p *DescribeSampleDataReq) GetLimit() (v int32) {
	return p.Limit
}

func (p *DescribeSampleDataReq) GetTenantID() (v string) {
	return p.TenantID
}
func (p *DescribeSampleDataReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeSampleDataReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSampleDataReq) SetRegion(val string) {
	p.Region = val
}
func (p *DescribeSampleDataReq) SetDbName(val string) {
	p.DbName = val
}
func (p *DescribeSampleDataReq) SetTableName(val string) {
	p.TableName = val
}
func (p *DescribeSampleDataReq) SetPrimaryKey(val []string) {
	p.PrimaryKey = val
}
func (p *DescribeSampleDataReq) SetColumns(val []string) {
	p.Columns = val
}
func (p *DescribeSampleDataReq) SetMinNum(val []*PrimaryKeyValue) {
	p.MinNum = val
}
func (p *DescribeSampleDataReq) SetMaxNum(val []*PrimaryKeyValue) {
	p.MaxNum = val
}
func (p *DescribeSampleDataReq) SetOrderBy(val SQLAdvisorOrderBy) {
	p.OrderBy = val
}
func (p *DescribeSampleDataReq) SetLimit(val int32) {
	p.Limit = val
}
func (p *DescribeSampleDataReq) SetTenantID(val string) {
	p.TenantID = val
}

var fieldIDToName_DescribeSampleDataReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceID",
	3:  "Region",
	4:  "DbName",
	5:  "TableName",
	6:  "PrimaryKey",
	7:  "Columns",
	8:  "MinNum",
	9:  "MaxNum",
	10: "OrderBy",
	11: "Limit",
	12: "TenantID",
}

func (p *DescribeSampleDataReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSampleDataReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetRegion bool = false
	var issetDbName bool = false
	var issetTableName bool = false
	var issetPrimaryKey bool = false
	var issetColumns bool = false
	var issetMinNum bool = false
	var issetMaxNum bool = false
	var issetOrderBy bool = false
	var issetLimit bool = false
	var issetTenantID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrimaryKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumns = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetMinNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetPrimaryKey {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetColumns {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetMinNum {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetMaxNum {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetTenantID {
		fieldId = 12
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSampleDataReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSampleDataReq[fieldId]))
}

func (p *DescribeSampleDataReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PrimaryKey = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Columns = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PrimaryKeyValue, 0, size)
	values := make([]PrimaryKeyValue, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MinNum = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PrimaryKeyValue, 0, size)
	values := make([]PrimaryKeyValue, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MaxNum = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField10(iprot thrift.TProtocol) error {

	var _field SQLAdvisorOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SQLAdvisorOrderBy(v)
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *DescribeSampleDataReq) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantID = _field
	return nil
}

func (p *DescribeSampleDataReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSampleDataReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSampleDataReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrimaryKey", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.PrimaryKey)); err != nil {
		return err
	}
	for _, v := range p.PrimaryKey {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Columns", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Columns)); err != nil {
		return err
	}
	for _, v := range p.Columns {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MinNum", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MinNum)); err != nil {
		return err
	}
	for _, v := range p.MinNum {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxNum", thrift.LIST, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MaxNum)); err != nil {
		return err
	}
	for _, v := range p.MaxNum {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeSampleDataReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantID", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeSampleDataReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSampleDataReq(%+v)", *p)

}

func (p *DescribeSampleDataReq) DeepEqual(ano *DescribeSampleDataReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.Region) {
		return false
	}
	if !p.Field4DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field5DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field6DeepEqual(ano.PrimaryKey) {
		return false
	}
	if !p.Field7DeepEqual(ano.Columns) {
		return false
	}
	if !p.Field8DeepEqual(ano.MinNum) {
		return false
	}
	if !p.Field9DeepEqual(ano.MaxNum) {
		return false
	}
	if !p.Field10DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field11DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field12DeepEqual(ano.TenantID) {
		return false
	}
	return true
}

func (p *DescribeSampleDataReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field6DeepEqual(src []string) bool {

	if len(p.PrimaryKey) != len(src) {
		return false
	}
	for i, v := range p.PrimaryKey {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeSampleDataReq) Field7DeepEqual(src []string) bool {

	if len(p.Columns) != len(src) {
		return false
	}
	for i, v := range p.Columns {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeSampleDataReq) Field8DeepEqual(src []*PrimaryKeyValue) bool {

	if len(p.MinNum) != len(src) {
		return false
	}
	for i, v := range p.MinNum {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSampleDataReq) Field9DeepEqual(src []*PrimaryKeyValue) bool {

	if len(p.MaxNum) != len(src) {
		return false
	}
	for i, v := range p.MaxNum {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSampleDataReq) Field10DeepEqual(src SQLAdvisorOrderBy) bool {

	if p.OrderBy != src {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field11DeepEqual(src int32) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *DescribeSampleDataReq) Field12DeepEqual(src string) bool {

	if strings.Compare(p.TenantID, src) != 0 {
		return false
	}
	return true
}

type DescribeSampleDataResp struct {
	Success bool                `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
	Total   int32               `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
	Records []map[string]string `thrift:"Records,3,required" frugal:"3,required,list<map<string:string>>" json:"Records"`
}

func NewDescribeSampleDataResp() *DescribeSampleDataResp {
	return &DescribeSampleDataResp{}
}

func (p *DescribeSampleDataResp) InitDefault() {
}

func (p *DescribeSampleDataResp) GetSuccess() (v bool) {
	return p.Success
}

func (p *DescribeSampleDataResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeSampleDataResp) GetRecords() (v []map[string]string) {
	return p.Records
}
func (p *DescribeSampleDataResp) SetSuccess(val bool) {
	p.Success = val
}
func (p *DescribeSampleDataResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeSampleDataResp) SetRecords(val []map[string]string) {
	p.Records = val
}

var fieldIDToName_DescribeSampleDataResp = map[int16]string{
	1: "Success",
	2: "Total",
	3: "Records",
}

func (p *DescribeSampleDataResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSampleDataResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	var issetTotal bool = false
	var issetRecords bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRecords = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRecords {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSampleDataResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSampleDataResp[fieldId]))
}

func (p *DescribeSampleDataResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *DescribeSampleDataResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeSampleDataResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]map[string]string, 0, size)
	for i := 0; i < size; i++ {
		_, _, size, err := iprot.ReadMapBegin()
		if err != nil {
			return err
		}
		_elem := make(map[string]string, size)
		for i := 0; i < size; i++ {
			var _key string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_key = v
			}

			var _val string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_val = v
			}

			_elem[_key] = _val
		}
		if err := iprot.ReadMapEnd(); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Records = _field
	return nil
}

func (p *DescribeSampleDataResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSampleDataResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSampleDataResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSampleDataResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSampleDataResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSampleDataResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Records", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.MAP, len(p.Records)); err != nil {
		return err
	}
	for _, v := range p.Records {
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(v)); err != nil {
			return err
		}
		for k, v := range v {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSampleDataResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSampleDataResp(%+v)", *p)

}

func (p *DescribeSampleDataResp) DeepEqual(ano *DescribeSampleDataResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	if !p.Field3DeepEqual(ano.Records) {
		return false
	}
	return true
}

func (p *DescribeSampleDataResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *DescribeSampleDataResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeSampleDataResp) Field3DeepEqual(src []map[string]string) bool {

	if len(p.Records) != len(src) {
		return false
	}
	for i, v := range p.Records {
		_src := src[i]
		if len(v) != len(_src) {
			return false
		}
		for k, v := range v {
			_src1 := _src[k]
			if strings.Compare(v, _src1) != 0 {
				return false
			}
		}
	}
	return true
}

type CreateSQLAdvisorTaskReq struct {
	InstanceID   string       `thrift:"InstanceID,1,required" frugal:"1,required,string" json:"InstanceID"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	DbName       string       `thrift:"DbName,3,required" frugal:"3,required,string" json:"DbName"`
	SQLList      []string     `thrift:"SQLList,4,required" frugal:"4,required,list<string>" json:"SQLList"`
	RegionId     *string      `thrift:"RegionId,5,optional" frugal:"5,optional,string" json:"RegionId,omitempty"`
}

func NewCreateSQLAdvisorTaskReq() *CreateSQLAdvisorTaskReq {
	return &CreateSQLAdvisorTaskReq{}
}

func (p *CreateSQLAdvisorTaskReq) InitDefault() {
}

func (p *CreateSQLAdvisorTaskReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *CreateSQLAdvisorTaskReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateSQLAdvisorTaskReq) GetDbName() (v string) {
	return p.DbName
}

func (p *CreateSQLAdvisorTaskReq) GetSQLList() (v []string) {
	return p.SQLList
}

var CreateSQLAdvisorTaskReq_RegionId_DEFAULT string

func (p *CreateSQLAdvisorTaskReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return CreateSQLAdvisorTaskReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *CreateSQLAdvisorTaskReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *CreateSQLAdvisorTaskReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateSQLAdvisorTaskReq) SetDbName(val string) {
	p.DbName = val
}
func (p *CreateSQLAdvisorTaskReq) SetSQLList(val []string) {
	p.SQLList = val
}
func (p *CreateSQLAdvisorTaskReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_CreateSQLAdvisorTaskReq = map[int16]string{
	1: "InstanceID",
	2: "InstanceType",
	3: "DbName",
	4: "SQLList",
	5: "RegionId",
}

func (p *CreateSQLAdvisorTaskReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *CreateSQLAdvisorTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSQLAdvisorTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceID bool = false
	var issetInstanceType bool = false
	var issetDbName bool = false
	var issetSQLList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetSQLList {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSQLAdvisorTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSQLAdvisorTaskReq[fieldId]))
}

func (p *CreateSQLAdvisorTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *CreateSQLAdvisorTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateSQLAdvisorTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *CreateSQLAdvisorTaskReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLList = _field
	return nil
}
func (p *CreateSQLAdvisorTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *CreateSQLAdvisorTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSQLAdvisorTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSQLAdvisorTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLList", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SQLList)); err != nil {
		return err
	}
	for _, v := range p.SQLList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSQLAdvisorTaskReq(%+v)", *p)

}

func (p *CreateSQLAdvisorTaskReq) DeepEqual(ano *CreateSQLAdvisorTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field4DeepEqual(ano.SQLList) {
		return false
	}
	if !p.Field5DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *CreateSQLAdvisorTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSQLAdvisorTaskReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateSQLAdvisorTaskReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSQLAdvisorTaskReq) Field4DeepEqual(src []string) bool {

	if len(p.SQLList) != len(src) {
		return false
	}
	for i, v := range p.SQLList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CreateSQLAdvisorTaskReq) Field5DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type SQLItem struct {
	Query string `thrift:"Query,1,required" frugal:"1,required,string" json:"Query"`
}

func NewSQLItem() *SQLItem {
	return &SQLItem{}
}

func (p *SQLItem) InitDefault() {
}

func (p *SQLItem) GetQuery() (v string) {
	return p.Query
}
func (p *SQLItem) SetQuery(val string) {
	p.Query = val
}

var fieldIDToName_SQLItem = map[int16]string{
	1: "Query",
}

func (p *SQLItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetQuery bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetQuery {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLItem[fieldId]))
}

func (p *SQLItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}

func (p *SQLItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLItem(%+v)", *p)

}

func (p *SQLItem) DeepEqual(ano *SQLItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Query) {
		return false
	}
	return true
}

func (p *SQLItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}

type CreateSQLAdvisorTaskResp struct {
	Success bool   `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
	Message string `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
	TaskID  string `thrift:"TaskID,3,required" frugal:"3,required,string" json:"TaskID"`
}

func NewCreateSQLAdvisorTaskResp() *CreateSQLAdvisorTaskResp {
	return &CreateSQLAdvisorTaskResp{}
}

func (p *CreateSQLAdvisorTaskResp) InitDefault() {
}

func (p *CreateSQLAdvisorTaskResp) GetSuccess() (v bool) {
	return p.Success
}

func (p *CreateSQLAdvisorTaskResp) GetMessage() (v string) {
	return p.Message
}

func (p *CreateSQLAdvisorTaskResp) GetTaskID() (v string) {
	return p.TaskID
}
func (p *CreateSQLAdvisorTaskResp) SetSuccess(val bool) {
	p.Success = val
}
func (p *CreateSQLAdvisorTaskResp) SetMessage(val string) {
	p.Message = val
}
func (p *CreateSQLAdvisorTaskResp) SetTaskID(val string) {
	p.TaskID = val
}

var fieldIDToName_CreateSQLAdvisorTaskResp = map[int16]string{
	1: "Success",
	2: "Message",
	3: "TaskID",
}

func (p *CreateSQLAdvisorTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSQLAdvisorTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	var issetMessage bool = false
	var issetTaskID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTaskID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSQLAdvisorTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSQLAdvisorTaskResp[fieldId]))
}

func (p *CreateSQLAdvisorTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *CreateSQLAdvisorTaskResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *CreateSQLAdvisorTaskResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}

func (p *CreateSQLAdvisorTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSQLAdvisorTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSQLAdvisorTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSQLAdvisorTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSQLAdvisorTaskResp(%+v)", *p)

}

func (p *CreateSQLAdvisorTaskResp) DeepEqual(ano *CreateSQLAdvisorTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskID) {
		return false
	}
	return true
}

func (p *CreateSQLAdvisorTaskResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *CreateSQLAdvisorTaskResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSQLAdvisorTaskResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TaskID, src) != 0 {
		return false
	}
	return true
}

type DescribeSQLAdvisorTaskReq struct {
	TaskID     string  `thrift:"TaskID,1,required" frugal:"1,required,string" json:"TaskID"`
	InstanceID string  `thrift:"InstanceID,2,required" frugal:"2,required,string" json:"InstanceID"`
	RegionId   *string `thrift:"RegionId,3,optional" frugal:"3,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeSQLAdvisorTaskReq() *DescribeSQLAdvisorTaskReq {
	return &DescribeSQLAdvisorTaskReq{}
}

func (p *DescribeSQLAdvisorTaskReq) InitDefault() {
}

func (p *DescribeSQLAdvisorTaskReq) GetTaskID() (v string) {
	return p.TaskID
}

func (p *DescribeSQLAdvisorTaskReq) GetInstanceID() (v string) {
	return p.InstanceID
}

var DescribeSQLAdvisorTaskReq_RegionId_DEFAULT string

func (p *DescribeSQLAdvisorTaskReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeSQLAdvisorTaskReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeSQLAdvisorTaskReq) SetTaskID(val string) {
	p.TaskID = val
}
func (p *DescribeSQLAdvisorTaskReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *DescribeSQLAdvisorTaskReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeSQLAdvisorTaskReq = map[int16]string{
	1: "TaskID",
	2: "InstanceID",
	3: "RegionId",
}

func (p *DescribeSQLAdvisorTaskReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeSQLAdvisorTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskID bool = false
	var issetInstanceID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLAdvisorTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLAdvisorTaskReq[fieldId]))
}

func (p *DescribeSQLAdvisorTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *DescribeSQLAdvisorTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *DescribeSQLAdvisorTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeSQLAdvisorTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLAdvisorTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLAdvisorTaskReq(%+v)", *p)

}

func (p *DescribeSQLAdvisorTaskReq) DeepEqual(ano *DescribeSQLAdvisorTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeSQLAdvisorTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTaskReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTaskReq) Field3DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeSQLAdvisorTaskResp struct {
	Success bool                 `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
	Message string               `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
	State   SQLAdvisorTaskStatus `thrift:"State,3,required" frugal:"3,required,SQLAdvisorTaskStatus" json:"State"`
	Data    *SQLAdvisorTaskInfo  `thrift:"Data,4,required" frugal:"4,required,SQLAdvisorTaskInfo" json:"Data"`
}

func NewDescribeSQLAdvisorTaskResp() *DescribeSQLAdvisorTaskResp {
	return &DescribeSQLAdvisorTaskResp{}
}

func (p *DescribeSQLAdvisorTaskResp) InitDefault() {
}

func (p *DescribeSQLAdvisorTaskResp) GetSuccess() (v bool) {
	return p.Success
}

func (p *DescribeSQLAdvisorTaskResp) GetMessage() (v string) {
	return p.Message
}

func (p *DescribeSQLAdvisorTaskResp) GetState() (v SQLAdvisorTaskStatus) {
	return p.State
}

var DescribeSQLAdvisorTaskResp_Data_DEFAULT *SQLAdvisorTaskInfo

func (p *DescribeSQLAdvisorTaskResp) GetData() (v *SQLAdvisorTaskInfo) {
	if !p.IsSetData() {
		return DescribeSQLAdvisorTaskResp_Data_DEFAULT
	}
	return p.Data
}
func (p *DescribeSQLAdvisorTaskResp) SetSuccess(val bool) {
	p.Success = val
}
func (p *DescribeSQLAdvisorTaskResp) SetMessage(val string) {
	p.Message = val
}
func (p *DescribeSQLAdvisorTaskResp) SetState(val SQLAdvisorTaskStatus) {
	p.State = val
}
func (p *DescribeSQLAdvisorTaskResp) SetData(val *SQLAdvisorTaskInfo) {
	p.Data = val
}

var fieldIDToName_DescribeSQLAdvisorTaskResp = map[int16]string{
	1: "Success",
	2: "Message",
	3: "State",
	4: "Data",
}

func (p *DescribeSQLAdvisorTaskResp) IsSetData() bool {
	return p.Data != nil
}

func (p *DescribeSQLAdvisorTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false
	var issetMessage bool = false
	var issetState bool = false
	var issetData bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetState {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetData {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSQLAdvisorTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSQLAdvisorTaskResp[fieldId]))
}

func (p *DescribeSQLAdvisorTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *DescribeSQLAdvisorTaskResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *DescribeSQLAdvisorTaskResp) ReadField3(iprot thrift.TProtocol) error {

	var _field SQLAdvisorTaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SQLAdvisorTaskStatus(v)
	}
	p.State = _field
	return nil
}
func (p *DescribeSQLAdvisorTaskResp) ReadField4(iprot thrift.TProtocol) error {
	_field := NewSQLAdvisorTaskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}

func (p *DescribeSQLAdvisorTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSQLAdvisorTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSQLAdvisorTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("State", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.State)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Data", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSQLAdvisorTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSQLAdvisorTaskResp(%+v)", *p)

}

func (p *DescribeSQLAdvisorTaskResp) DeepEqual(ano *DescribeSQLAdvisorTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.State) {
		return false
	}
	if !p.Field4DeepEqual(ano.Data) {
		return false
	}
	return true
}

func (p *DescribeSQLAdvisorTaskResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTaskResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTaskResp) Field3DeepEqual(src SQLAdvisorTaskStatus) bool {

	if p.State != src {
		return false
	}
	return true
}
func (p *DescribeSQLAdvisorTaskResp) Field4DeepEqual(src *SQLAdvisorTaskInfo) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}

type SQLAdvisorTaskInfo struct {
	TaskId          string            `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	IndexAdvices    []*IndexAdvice    `thrift:"IndexAdvices,2,required" frugal:"2,required,list<IndexAdvice>" json:"IndexAdvices"`
	OptimizeAdvice  []*OptimizeAdvice `thrift:"OptimizeAdvice,3,required" frugal:"3,required,list<OptimizeAdvice>" json:"OptimizeAdvice"`
	OptimizeStatus  string            `thrift:"OptimizeStatus,4,required" frugal:"4,required,string" json:"OptimizeStatus"`
	AdviceTimestamp string            `thrift:"AdviceTimestamp,5,required" frugal:"5,required,string" json:"AdviceTimestamp"`
}

func NewSQLAdvisorTaskInfo() *SQLAdvisorTaskInfo {
	return &SQLAdvisorTaskInfo{}
}

func (p *SQLAdvisorTaskInfo) InitDefault() {
}

func (p *SQLAdvisorTaskInfo) GetTaskId() (v string) {
	return p.TaskId
}

func (p *SQLAdvisorTaskInfo) GetIndexAdvices() (v []*IndexAdvice) {
	return p.IndexAdvices
}

func (p *SQLAdvisorTaskInfo) GetOptimizeAdvice() (v []*OptimizeAdvice) {
	return p.OptimizeAdvice
}

func (p *SQLAdvisorTaskInfo) GetOptimizeStatus() (v string) {
	return p.OptimizeStatus
}

func (p *SQLAdvisorTaskInfo) GetAdviceTimestamp() (v string) {
	return p.AdviceTimestamp
}
func (p *SQLAdvisorTaskInfo) SetTaskId(val string) {
	p.TaskId = val
}
func (p *SQLAdvisorTaskInfo) SetIndexAdvices(val []*IndexAdvice) {
	p.IndexAdvices = val
}
func (p *SQLAdvisorTaskInfo) SetOptimizeAdvice(val []*OptimizeAdvice) {
	p.OptimizeAdvice = val
}
func (p *SQLAdvisorTaskInfo) SetOptimizeStatus(val string) {
	p.OptimizeStatus = val
}
func (p *SQLAdvisorTaskInfo) SetAdviceTimestamp(val string) {
	p.AdviceTimestamp = val
}

var fieldIDToName_SQLAdvisorTaskInfo = map[int16]string{
	1: "TaskId",
	2: "IndexAdvices",
	3: "OptimizeAdvice",
	4: "OptimizeStatus",
	5: "AdviceTimestamp",
}

func (p *SQLAdvisorTaskInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTaskInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetIndexAdvices bool = false
	var issetOptimizeAdvice bool = false
	var issetOptimizeStatus bool = false
	var issetAdviceTimestamp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexAdvices = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetOptimizeAdvice = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetOptimizeStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceTimestamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIndexAdvices {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOptimizeAdvice {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetOptimizeStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetAdviceTimestamp {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorTaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorTaskInfo[fieldId]))
}

func (p *SQLAdvisorTaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *SQLAdvisorTaskInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IndexAdvice, 0, size)
	values := make([]IndexAdvice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IndexAdvices = _field
	return nil
}
func (p *SQLAdvisorTaskInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*OptimizeAdvice, 0, size)
	values := make([]OptimizeAdvice, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OptimizeAdvice = _field
	return nil
}
func (p *SQLAdvisorTaskInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OptimizeStatus = _field
	return nil
}
func (p *SQLAdvisorTaskInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceTimestamp = _field
	return nil
}

func (p *SQLAdvisorTaskInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorTaskInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorTaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexAdvices", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.IndexAdvices)); err != nil {
		return err
	}
	for _, v := range p.IndexAdvices {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OptimizeAdvice", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OptimizeAdvice)); err != nil {
		return err
	}
	for _, v := range p.OptimizeAdvice {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OptimizeStatus", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OptimizeStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceTimestamp", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AdviceTimestamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLAdvisorTaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorTaskInfo(%+v)", *p)

}

func (p *SQLAdvisorTaskInfo) DeepEqual(ano *SQLAdvisorTaskInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.IndexAdvices) {
		return false
	}
	if !p.Field3DeepEqual(ano.OptimizeAdvice) {
		return false
	}
	if !p.Field4DeepEqual(ano.OptimizeStatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.AdviceTimestamp) {
		return false
	}
	return true
}

func (p *SQLAdvisorTaskInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTaskInfo) Field2DeepEqual(src []*IndexAdvice) bool {

	if len(p.IndexAdvices) != len(src) {
		return false
	}
	for i, v := range p.IndexAdvices {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SQLAdvisorTaskInfo) Field3DeepEqual(src []*OptimizeAdvice) bool {

	if len(p.OptimizeAdvice) != len(src) {
		return false
	}
	for i, v := range p.OptimizeAdvice {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SQLAdvisorTaskInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.OptimizeStatus, src) != 0 {
		return false
	}
	return true
}
func (p *SQLAdvisorTaskInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.AdviceTimestamp, src) != 0 {
		return false
	}
	return true
}

type IndexAdvice struct {
	DbName              string               `thrift:"DbName,1,required" frugal:"1,required,string" json:"DbName"`
	TableName           string               `thrift:"TableName,2,required" frugal:"2,required,string" json:"TableName"`
	Columns             []*SQLAdvisorColumns `thrift:"Columns,3,required" frugal:"3,required,list<SQLAdvisorColumns>" json:"Columns"`
	DDL                 string               `thrift:"DDL,4,required" frugal:"4,required,string" json:"DDL"`
	RelatedFingerPrints []*FigerPrint        `thrift:"RelatedFingerPrints,5,required" frugal:"5,required,list<FigerPrint>" json:"RelatedFingerPrints"`
}

func NewIndexAdvice() *IndexAdvice {
	return &IndexAdvice{}
}

func (p *IndexAdvice) InitDefault() {
}

func (p *IndexAdvice) GetDbName() (v string) {
	return p.DbName
}

func (p *IndexAdvice) GetTableName() (v string) {
	return p.TableName
}

func (p *IndexAdvice) GetColumns() (v []*SQLAdvisorColumns) {
	return p.Columns
}

func (p *IndexAdvice) GetDDL() (v string) {
	return p.DDL
}

func (p *IndexAdvice) GetRelatedFingerPrints() (v []*FigerPrint) {
	return p.RelatedFingerPrints
}
func (p *IndexAdvice) SetDbName(val string) {
	p.DbName = val
}
func (p *IndexAdvice) SetTableName(val string) {
	p.TableName = val
}
func (p *IndexAdvice) SetColumns(val []*SQLAdvisorColumns) {
	p.Columns = val
}
func (p *IndexAdvice) SetDDL(val string) {
	p.DDL = val
}
func (p *IndexAdvice) SetRelatedFingerPrints(val []*FigerPrint) {
	p.RelatedFingerPrints = val
}

var fieldIDToName_IndexAdvice = map[int16]string{
	1: "DbName",
	2: "TableName",
	3: "Columns",
	4: "DDL",
	5: "RelatedFingerPrints",
}

func (p *IndexAdvice) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexAdvice")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDbName bool = false
	var issetTableName bool = false
	var issetColumns bool = false
	var issetDDL bool = false
	var issetRelatedFingerPrints bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetColumns = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDDL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRelatedFingerPrints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDbName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetColumns {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDDL {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRelatedFingerPrints {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndexAdvice[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_IndexAdvice[fieldId]))
}

func (p *IndexAdvice) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *IndexAdvice) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *IndexAdvice) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLAdvisorColumns, 0, size)
	values := make([]SQLAdvisorColumns, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Columns = _field
	return nil
}
func (p *IndexAdvice) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DDL = _field
	return nil
}
func (p *IndexAdvice) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FigerPrint, 0, size)
	values := make([]FigerPrint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RelatedFingerPrints = _field
	return nil
}

func (p *IndexAdvice) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexAdvice")

	var fieldId int16
	if err = oprot.WriteStructBegin("IndexAdvice"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IndexAdvice) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *IndexAdvice) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *IndexAdvice) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Columns", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Columns)); err != nil {
		return err
	}
	for _, v := range p.Columns {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IndexAdvice) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DDL", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DDL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *IndexAdvice) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RelatedFingerPrints", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelatedFingerPrints)); err != nil {
		return err
	}
	for _, v := range p.RelatedFingerPrints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *IndexAdvice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdvice(%+v)", *p)

}

func (p *IndexAdvice) DeepEqual(ano *IndexAdvice) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Columns) {
		return false
	}
	if !p.Field4DeepEqual(ano.DDL) {
		return false
	}
	if !p.Field5DeepEqual(ano.RelatedFingerPrints) {
		return false
	}
	return true
}

func (p *IndexAdvice) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *IndexAdvice) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *IndexAdvice) Field3DeepEqual(src []*SQLAdvisorColumns) bool {

	if len(p.Columns) != len(src) {
		return false
	}
	for i, v := range p.Columns {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *IndexAdvice) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DDL, src) != 0 {
		return false
	}
	return true
}
func (p *IndexAdvice) Field5DeepEqual(src []*FigerPrint) bool {

	if len(p.RelatedFingerPrints) != len(src) {
		return false
	}
	for i, v := range p.RelatedFingerPrints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SQLAdvisorColumns struct {
	Name string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
}

func NewSQLAdvisorColumns() *SQLAdvisorColumns {
	return &SQLAdvisorColumns{}
}

func (p *SQLAdvisorColumns) InitDefault() {
}

func (p *SQLAdvisorColumns) GetName() (v string) {
	return p.Name
}
func (p *SQLAdvisorColumns) SetName(val string) {
	p.Name = val
}

var fieldIDToName_SQLAdvisorColumns = map[int16]string{
	1: "Name",
}

func (p *SQLAdvisorColumns) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorColumns")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLAdvisorColumns[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLAdvisorColumns[fieldId]))
}

func (p *SQLAdvisorColumns) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}

func (p *SQLAdvisorColumns) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLAdvisorColumns")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLAdvisorColumns"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLAdvisorColumns) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLAdvisorColumns) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLAdvisorColumns(%+v)", *p)

}

func (p *SQLAdvisorColumns) DeepEqual(ano *SQLAdvisorColumns) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	return true
}

func (p *SQLAdvisorColumns) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}

type FigerPrint struct {
	FingerPrintMd5 string `thrift:"FingerPrintMd5,1,required" frugal:"1,required,string" json:"FingerPrintMd5"`
	Benefit        string `thrift:"Benefit,2,required" frugal:"2,required,string" json:"Benefit"`
}

func NewFigerPrint() *FigerPrint {
	return &FigerPrint{}
}

func (p *FigerPrint) InitDefault() {
}

func (p *FigerPrint) GetFingerPrintMd5() (v string) {
	return p.FingerPrintMd5
}

func (p *FigerPrint) GetBenefit() (v string) {
	return p.Benefit
}
func (p *FigerPrint) SetFingerPrintMd5(val string) {
	p.FingerPrintMd5 = val
}
func (p *FigerPrint) SetBenefit(val string) {
	p.Benefit = val
}

var fieldIDToName_FigerPrint = map[int16]string{
	1: "FingerPrintMd5",
	2: "Benefit",
}

func (p *FigerPrint) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FigerPrint")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFingerPrintMd5 bool = false
	var issetBenefit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFingerPrintMd5 = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBenefit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFingerPrintMd5 {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBenefit {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FigerPrint[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_FigerPrint[fieldId]))
}

func (p *FigerPrint) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FingerPrintMd5 = _field
	return nil
}
func (p *FigerPrint) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Benefit = _field
	return nil
}

func (p *FigerPrint) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("FigerPrint")

	var fieldId int16
	if err = oprot.WriteStructBegin("FigerPrint"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FigerPrint) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FingerPrintMd5", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FingerPrintMd5); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FigerPrint) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Benefit", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Benefit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FigerPrint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FigerPrint(%+v)", *p)

}

func (p *FigerPrint) DeepEqual(ano *FigerPrint) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FingerPrintMd5) {
		return false
	}
	if !p.Field2DeepEqual(ano.Benefit) {
		return false
	}
	return true
}

func (p *FigerPrint) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FingerPrintMd5, src) != 0 {
		return false
	}
	return true
}
func (p *FigerPrint) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Benefit, src) != 0 {
		return false
	}
	return true
}

type OptimizeAdvice struct {
	AdviceType          string        `thrift:"AdviceType,1,required" frugal:"1,required,string" json:"AdviceType"`
	AdviceContent       string        `thrift:"AdviceContent,2,required" frugal:"2,required,string" json:"AdviceContent"`
	AdviceCode          string        `thrift:"AdviceCode,3,required" frugal:"3,required,string" json:"AdviceCode"`
	RelatedFingerPrints []*FigerPrint `thrift:"RelatedFingerPrints,4,required" frugal:"4,required,list<FigerPrint>" json:"RelatedFingerPrints"`
	ExtraInfo           *ExtraInfo    `thrift:"ExtraInfo,5,required" frugal:"5,required,ExtraInfo" json:"ExtraInfo"`
	Template            string        `thrift:"Template,6,required" frugal:"6,required,string" json:"Template"`
	TemplateEn          string        `thrift:"TemplateEn,7,required" frugal:"7,required,string" json:"TemplateEn"`
}

func NewOptimizeAdvice() *OptimizeAdvice {
	return &OptimizeAdvice{}
}

func (p *OptimizeAdvice) InitDefault() {
}

func (p *OptimizeAdvice) GetAdviceType() (v string) {
	return p.AdviceType
}

func (p *OptimizeAdvice) GetAdviceContent() (v string) {
	return p.AdviceContent
}

func (p *OptimizeAdvice) GetAdviceCode() (v string) {
	return p.AdviceCode
}

func (p *OptimizeAdvice) GetRelatedFingerPrints() (v []*FigerPrint) {
	return p.RelatedFingerPrints
}

var OptimizeAdvice_ExtraInfo_DEFAULT *ExtraInfo

func (p *OptimizeAdvice) GetExtraInfo() (v *ExtraInfo) {
	if !p.IsSetExtraInfo() {
		return OptimizeAdvice_ExtraInfo_DEFAULT
	}
	return p.ExtraInfo
}

func (p *OptimizeAdvice) GetTemplate() (v string) {
	return p.Template
}

func (p *OptimizeAdvice) GetTemplateEn() (v string) {
	return p.TemplateEn
}
func (p *OptimizeAdvice) SetAdviceType(val string) {
	p.AdviceType = val
}
func (p *OptimizeAdvice) SetAdviceContent(val string) {
	p.AdviceContent = val
}
func (p *OptimizeAdvice) SetAdviceCode(val string) {
	p.AdviceCode = val
}
func (p *OptimizeAdvice) SetRelatedFingerPrints(val []*FigerPrint) {
	p.RelatedFingerPrints = val
}
func (p *OptimizeAdvice) SetExtraInfo(val *ExtraInfo) {
	p.ExtraInfo = val
}
func (p *OptimizeAdvice) SetTemplate(val string) {
	p.Template = val
}
func (p *OptimizeAdvice) SetTemplateEn(val string) {
	p.TemplateEn = val
}

var fieldIDToName_OptimizeAdvice = map[int16]string{
	1: "AdviceType",
	2: "AdviceContent",
	3: "AdviceCode",
	4: "RelatedFingerPrints",
	5: "ExtraInfo",
	6: "Template",
	7: "TemplateEn",
}

func (p *OptimizeAdvice) IsSetExtraInfo() bool {
	return p.ExtraInfo != nil
}

func (p *OptimizeAdvice) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OptimizeAdvice")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAdviceType bool = false
	var issetAdviceContent bool = false
	var issetAdviceCode bool = false
	var issetRelatedFingerPrints bool = false
	var issetExtraInfo bool = false
	var issetTemplate bool = false
	var issetTemplateEn bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRelatedFingerPrints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetExtraInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTemplateEn = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAdviceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAdviceContent {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceCode {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRelatedFingerPrints {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetExtraInfo {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTemplate {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTemplateEn {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OptimizeAdvice[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_OptimizeAdvice[fieldId]))
}

func (p *OptimizeAdvice) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceType = _field
	return nil
}
func (p *OptimizeAdvice) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceContent = _field
	return nil
}
func (p *OptimizeAdvice) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AdviceCode = _field
	return nil
}
func (p *OptimizeAdvice) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FigerPrint, 0, size)
	values := make([]FigerPrint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RelatedFingerPrints = _field
	return nil
}
func (p *OptimizeAdvice) ReadField5(iprot thrift.TProtocol) error {
	_field := NewExtraInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExtraInfo = _field
	return nil
}
func (p *OptimizeAdvice) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Template = _field
	return nil
}
func (p *OptimizeAdvice) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateEn = _field
	return nil
}

func (p *OptimizeAdvice) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OptimizeAdvice")

	var fieldId int16
	if err = oprot.WriteStructBegin("OptimizeAdvice"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OptimizeAdvice) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceType", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AdviceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceContent", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AdviceContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceCode", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AdviceCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RelatedFingerPrints", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelatedFingerPrints)); err != nil {
		return err
	}
	for _, v := range p.RelatedFingerPrints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExtraInfo", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExtraInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Template", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Template); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *OptimizeAdvice) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TemplateEn", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateEn); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *OptimizeAdvice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OptimizeAdvice(%+v)", *p)

}

func (p *OptimizeAdvice) DeepEqual(ano *OptimizeAdvice) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AdviceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.AdviceContent) {
		return false
	}
	if !p.Field3DeepEqual(ano.AdviceCode) {
		return false
	}
	if !p.Field4DeepEqual(ano.RelatedFingerPrints) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExtraInfo) {
		return false
	}
	if !p.Field6DeepEqual(ano.Template) {
		return false
	}
	if !p.Field7DeepEqual(ano.TemplateEn) {
		return false
	}
	return true
}

func (p *OptimizeAdvice) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AdviceType, src) != 0 {
		return false
	}
	return true
}
func (p *OptimizeAdvice) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AdviceContent, src) != 0 {
		return false
	}
	return true
}
func (p *OptimizeAdvice) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AdviceCode, src) != 0 {
		return false
	}
	return true
}
func (p *OptimizeAdvice) Field4DeepEqual(src []*FigerPrint) bool {

	if len(p.RelatedFingerPrints) != len(src) {
		return false
	}
	for i, v := range p.RelatedFingerPrints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *OptimizeAdvice) Field5DeepEqual(src *ExtraInfo) bool {

	if !p.ExtraInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *OptimizeAdvice) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Template, src) != 0 {
		return false
	}
	return true
}
func (p *OptimizeAdvice) Field7DeepEqual(src string) bool {

	if strings.Compare(p.TemplateEn, src) != 0 {
		return false
	}
	return true
}

type ExtraInfo struct {
	Condition       string `thrift:"Condition,1,required" frugal:"1,required,string" json:"Condition"`
	AffectedColumns string `thrift:"AffectedColumns,2,required" frugal:"2,required,string" json:"AffectedColumns"`
	Collations      string `thrift:"Collations,3,required" frugal:"3,required,string" json:"Collations"`
	Clause          string `thrift:"Clause,4,required" frugal:"4,required,string" json:"Clause"`
	Table           string `thrift:"Table,5,required" frugal:"5,required,string" json:"Table"`
	CurrentCharset  string `thrift:"CurrentCharset,6,required" frugal:"6,required,string" json:"CurrentCharset"`
	NormalKeyNum    string `thrift:"NormalKeyNum,7,required" frugal:"7,required,string" json:"NormalKeyNum"`
	DuplicateIndex  string `thrift:"DuplicateIndex,8,required" frugal:"8,required,string" json:"DuplicateIndex"`
}

func NewExtraInfo() *ExtraInfo {
	return &ExtraInfo{}
}

func (p *ExtraInfo) InitDefault() {
}

func (p *ExtraInfo) GetCondition() (v string) {
	return p.Condition
}

func (p *ExtraInfo) GetAffectedColumns() (v string) {
	return p.AffectedColumns
}

func (p *ExtraInfo) GetCollations() (v string) {
	return p.Collations
}

func (p *ExtraInfo) GetClause() (v string) {
	return p.Clause
}

func (p *ExtraInfo) GetTable() (v string) {
	return p.Table
}

func (p *ExtraInfo) GetCurrentCharset() (v string) {
	return p.CurrentCharset
}

func (p *ExtraInfo) GetNormalKeyNum() (v string) {
	return p.NormalKeyNum
}

func (p *ExtraInfo) GetDuplicateIndex() (v string) {
	return p.DuplicateIndex
}
func (p *ExtraInfo) SetCondition(val string) {
	p.Condition = val
}
func (p *ExtraInfo) SetAffectedColumns(val string) {
	p.AffectedColumns = val
}
func (p *ExtraInfo) SetCollations(val string) {
	p.Collations = val
}
func (p *ExtraInfo) SetClause(val string) {
	p.Clause = val
}
func (p *ExtraInfo) SetTable(val string) {
	p.Table = val
}
func (p *ExtraInfo) SetCurrentCharset(val string) {
	p.CurrentCharset = val
}
func (p *ExtraInfo) SetNormalKeyNum(val string) {
	p.NormalKeyNum = val
}
func (p *ExtraInfo) SetDuplicateIndex(val string) {
	p.DuplicateIndex = val
}

var fieldIDToName_ExtraInfo = map[int16]string{
	1: "Condition",
	2: "AffectedColumns",
	3: "Collations",
	4: "Clause",
	5: "Table",
	6: "CurrentCharset",
	7: "NormalKeyNum",
	8: "DuplicateIndex",
}

func (p *ExtraInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExtraInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCondition bool = false
	var issetAffectedColumns bool = false
	var issetCollations bool = false
	var issetClause bool = false
	var issetTable bool = false
	var issetCurrentCharset bool = false
	var issetNormalKeyNum bool = false
	var issetDuplicateIndex bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCondition = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAffectedColumns = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCollations = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetClause = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentCharset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetNormalKeyNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetDuplicateIndex = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCondition {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAffectedColumns {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCollations {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetClause {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTable {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCurrentCharset {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetNormalKeyNum {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDuplicateIndex {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExtraInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExtraInfo[fieldId]))
}

func (p *ExtraInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Condition = _field
	return nil
}
func (p *ExtraInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AffectedColumns = _field
	return nil
}
func (p *ExtraInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Collations = _field
	return nil
}
func (p *ExtraInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Clause = _field
	return nil
}
func (p *ExtraInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Table = _field
	return nil
}
func (p *ExtraInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentCharset = _field
	return nil
}
func (p *ExtraInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NormalKeyNum = _field
	return nil
}
func (p *ExtraInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DuplicateIndex = _field
	return nil
}

func (p *ExtraInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExtraInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExtraInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExtraInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Condition", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Condition); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExtraInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AffectedColumns", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AffectedColumns); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExtraInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Collations", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Collations); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExtraInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Clause", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Clause); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExtraInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Table", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Table); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExtraInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentCharset", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CurrentCharset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ExtraInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NormalKeyNum", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NormalKeyNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ExtraInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DuplicateIndex", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DuplicateIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ExtraInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraInfo(%+v)", *p)

}

func (p *ExtraInfo) DeepEqual(ano *ExtraInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Condition) {
		return false
	}
	if !p.Field2DeepEqual(ano.AffectedColumns) {
		return false
	}
	if !p.Field3DeepEqual(ano.Collations) {
		return false
	}
	if !p.Field4DeepEqual(ano.Clause) {
		return false
	}
	if !p.Field5DeepEqual(ano.Table) {
		return false
	}
	if !p.Field6DeepEqual(ano.CurrentCharset) {
		return false
	}
	if !p.Field7DeepEqual(ano.NormalKeyNum) {
		return false
	}
	if !p.Field8DeepEqual(ano.DuplicateIndex) {
		return false
	}
	return true
}

func (p *ExtraInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Condition, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AffectedColumns, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Collations, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Clause, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Table, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CurrentCharset, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.NormalKeyNum, src) != 0 {
		return false
	}
	return true
}
func (p *ExtraInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.DuplicateIndex, src) != 0 {
		return false
	}
	return true
}

type GetSQLAdvisorProtocolReq struct {
}

func NewGetSQLAdvisorProtocolReq() *GetSQLAdvisorProtocolReq {
	return &GetSQLAdvisorProtocolReq{}
}

func (p *GetSQLAdvisorProtocolReq) InitDefault() {
}

var fieldIDToName_GetSQLAdvisorProtocolReq = map[int16]string{}

func (p *GetSQLAdvisorProtocolReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSQLAdvisorProtocolReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSQLAdvisorProtocolReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSQLAdvisorProtocolReq")

	if err = oprot.WriteStructBegin("GetSQLAdvisorProtocolReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSQLAdvisorProtocolReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSQLAdvisorProtocolReq(%+v)", *p)

}

func (p *GetSQLAdvisorProtocolReq) DeepEqual(ano *GetSQLAdvisorProtocolReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type GetSQLAdvisorProtocolResp struct {
	Agreed bool `thrift:"Agreed,1,required" frugal:"1,required,bool" json:"Agreed"`
}

func NewGetSQLAdvisorProtocolResp() *GetSQLAdvisorProtocolResp {
	return &GetSQLAdvisorProtocolResp{}
}

func (p *GetSQLAdvisorProtocolResp) InitDefault() {
}

func (p *GetSQLAdvisorProtocolResp) GetAgreed() (v bool) {
	return p.Agreed
}
func (p *GetSQLAdvisorProtocolResp) SetAgreed(val bool) {
	p.Agreed = val
}

var fieldIDToName_GetSQLAdvisorProtocolResp = map[int16]string{
	1: "Agreed",
}

func (p *GetSQLAdvisorProtocolResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSQLAdvisorProtocolResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAgreed bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgreed = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAgreed {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSQLAdvisorProtocolResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSQLAdvisorProtocolResp[fieldId]))
}

func (p *GetSQLAdvisorProtocolResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Agreed = _field
	return nil
}

func (p *GetSQLAdvisorProtocolResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSQLAdvisorProtocolResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSQLAdvisorProtocolResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSQLAdvisorProtocolResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Agreed", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Agreed); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSQLAdvisorProtocolResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSQLAdvisorProtocolResp(%+v)", *p)

}

func (p *GetSQLAdvisorProtocolResp) DeepEqual(ano *GetSQLAdvisorProtocolResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Agreed) {
		return false
	}
	return true
}

func (p *GetSQLAdvisorProtocolResp) Field1DeepEqual(src bool) bool {

	if p.Agreed != src {
		return false
	}
	return true
}

type AgreeSQLAdvisorProtocolReq struct {
}

func NewAgreeSQLAdvisorProtocolReq() *AgreeSQLAdvisorProtocolReq {
	return &AgreeSQLAdvisorProtocolReq{}
}

func (p *AgreeSQLAdvisorProtocolReq) InitDefault() {
}

var fieldIDToName_AgreeSQLAdvisorProtocolReq = map[int16]string{}

func (p *AgreeSQLAdvisorProtocolReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSQLAdvisorProtocolReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgreeSQLAdvisorProtocolReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSQLAdvisorProtocolReq")

	if err = oprot.WriteStructBegin("AgreeSQLAdvisorProtocolReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreeSQLAdvisorProtocolReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreeSQLAdvisorProtocolReq(%+v)", *p)

}

func (p *AgreeSQLAdvisorProtocolReq) DeepEqual(ano *AgreeSQLAdvisorProtocolReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type AgreeSQLAdvisorProtocolResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewAgreeSQLAdvisorProtocolResp() *AgreeSQLAdvisorProtocolResp {
	return &AgreeSQLAdvisorProtocolResp{}
}

func (p *AgreeSQLAdvisorProtocolResp) InitDefault() {
}

func (p *AgreeSQLAdvisorProtocolResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *AgreeSQLAdvisorProtocolResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_AgreeSQLAdvisorProtocolResp = map[int16]string{
	1: "Success",
}

func (p *AgreeSQLAdvisorProtocolResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSQLAdvisorProtocolResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgreeSQLAdvisorProtocolResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AgreeSQLAdvisorProtocolResp[fieldId]))
}

func (p *AgreeSQLAdvisorProtocolResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *AgreeSQLAdvisorProtocolResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AgreeSQLAdvisorProtocolResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AgreeSQLAdvisorProtocolResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreeSQLAdvisorProtocolResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AgreeSQLAdvisorProtocolResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreeSQLAdvisorProtocolResp(%+v)", *p)

}

func (p *AgreeSQLAdvisorProtocolResp) DeepEqual(ano *AgreeSQLAdvisorProtocolResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AgreeSQLAdvisorProtocolResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

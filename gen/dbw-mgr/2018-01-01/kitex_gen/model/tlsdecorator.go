// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeTopicDBWReq struct {
	InstanceId string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DataType   StatisticDataType `thrift:"DataType,2,required" frugal:"2,required,StatisticDataType" json:"DataType"`
}

func NewDescribeTopicDBWReq() *DescribeTopicDBWReq {
	return &DescribeTopicDBWReq{}
}

func (p *DescribeTopicDBWReq) InitDefault() {
}

func (p *DescribeTopicDBWReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTopicDBWReq) GetDataType() (v StatisticDataType) {
	return p.DataType
}
func (p *DescribeTopicDBWReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTopicDBWReq) SetDataType(val StatisticDataType) {
	p.DataType = val
}

var fieldIDToName_DescribeTopicDBWReq = map[int16]string{
	1: "InstanceId",
	2: "DataType",
}

func (p *DescribeTopicDBWReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTopicDBWReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDataType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTopicDBWReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTopicDBWReq[fieldId]))
}

func (p *DescribeTopicDBWReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTopicDBWReq) ReadField2(iprot thrift.TProtocol) error {

	var _field StatisticDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StatisticDataType(v)
	}
	p.DataType = _field
	return nil
}

func (p *DescribeTopicDBWReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTopicDBWReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTopicDBWReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTopicDBWReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTopicDBWReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTopicDBWReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTopicDBWReq(%+v)", *p)

}

func (p *DescribeTopicDBWReq) DeepEqual(ano *DescribeTopicDBWReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataType) {
		return false
	}
	return true
}

func (p *DescribeTopicDBWReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTopicDBWReq) Field2DeepEqual(src StatisticDataType) bool {

	if p.DataType != src {
		return false
	}
	return true
}

type DescribeTopicDBWResp struct {
	TLSResponse string `thrift:"TLSResponse,1,required" frugal:"1,required,string" json:"TLSResponse"`
	TLSError    string `thrift:"TLSError,2,required" frugal:"2,required,string" json:"TLSError"`
}

func NewDescribeTopicDBWResp() *DescribeTopicDBWResp {
	return &DescribeTopicDBWResp{}
}

func (p *DescribeTopicDBWResp) InitDefault() {
}

func (p *DescribeTopicDBWResp) GetTLSResponse() (v string) {
	return p.TLSResponse
}

func (p *DescribeTopicDBWResp) GetTLSError() (v string) {
	return p.TLSError
}
func (p *DescribeTopicDBWResp) SetTLSResponse(val string) {
	p.TLSResponse = val
}
func (p *DescribeTopicDBWResp) SetTLSError(val string) {
	p.TLSError = val
}

var fieldIDToName_DescribeTopicDBWResp = map[int16]string{
	1: "TLSResponse",
	2: "TLSError",
}

func (p *DescribeTopicDBWResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTopicDBWResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTLSResponse bool = false
	var issetTLSError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSResponse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTLSResponse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTLSError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTopicDBWResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTopicDBWResp[fieldId]))
}

func (p *DescribeTopicDBWResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSResponse = _field
	return nil
}
func (p *DescribeTopicDBWResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSError = _field
	return nil
}

func (p *DescribeTopicDBWResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTopicDBWResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTopicDBWResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTopicDBWResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSResponse", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTopicDBWResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSError", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTopicDBWResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTopicDBWResp(%+v)", *p)

}

func (p *DescribeTopicDBWResp) DeepEqual(ano *DescribeTopicDBWResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TLSResponse) {
		return false
	}
	if !p.Field2DeepEqual(ano.TLSError) {
		return false
	}
	return true
}

func (p *DescribeTopicDBWResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TLSResponse, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTopicDBWResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TLSError, src) != 0 {
		return false
	}
	return true
}

type SearchLogDBWReq struct {
	InstanceId string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DataType   StatisticDataType `thrift:"DataType,2,required" frugal:"2,required,StatisticDataType" json:"DataType"`
	Query      string            `thrift:"Query,3,required" frugal:"3,required,string" json:"Query"`
	StartTime  int64             `thrift:"StartTime,4,required" frugal:"4,required,i64" json:"StartTime"`
	EndTime    int64             `thrift:"EndTime,5,required" frugal:"5,required,i64" json:"EndTime"`
	Limit      int64             `thrift:"Limit,6,required" frugal:"6,required,i64" json:"Limit"`
	HighLight  bool              `thrift:"HighLight,7,required" frugal:"7,required,bool" json:"HighLight"`
	Context    string            `thrift:"Context,8,required" frugal:"8,required,string" json:"Context"`
	Sort       string            `thrift:"Sort,9,required" frugal:"9,required,string" json:"Sort"`
}

func NewSearchLogDBWReq() *SearchLogDBWReq {
	return &SearchLogDBWReq{}
}

func (p *SearchLogDBWReq) InitDefault() {
}

func (p *SearchLogDBWReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SearchLogDBWReq) GetDataType() (v StatisticDataType) {
	return p.DataType
}

func (p *SearchLogDBWReq) GetQuery() (v string) {
	return p.Query
}

func (p *SearchLogDBWReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *SearchLogDBWReq) GetEndTime() (v int64) {
	return p.EndTime
}

func (p *SearchLogDBWReq) GetLimit() (v int64) {
	return p.Limit
}

func (p *SearchLogDBWReq) GetHighLight() (v bool) {
	return p.HighLight
}

func (p *SearchLogDBWReq) GetContext() (v string) {
	return p.Context
}

func (p *SearchLogDBWReq) GetSort() (v string) {
	return p.Sort
}
func (p *SearchLogDBWReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SearchLogDBWReq) SetDataType(val StatisticDataType) {
	p.DataType = val
}
func (p *SearchLogDBWReq) SetQuery(val string) {
	p.Query = val
}
func (p *SearchLogDBWReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *SearchLogDBWReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *SearchLogDBWReq) SetLimit(val int64) {
	p.Limit = val
}
func (p *SearchLogDBWReq) SetHighLight(val bool) {
	p.HighLight = val
}
func (p *SearchLogDBWReq) SetContext(val string) {
	p.Context = val
}
func (p *SearchLogDBWReq) SetSort(val string) {
	p.Sort = val
}

var fieldIDToName_SearchLogDBWReq = map[int16]string{
	1: "InstanceId",
	2: "DataType",
	3: "Query",
	4: "StartTime",
	5: "EndTime",
	6: "Limit",
	7: "HighLight",
	8: "Context",
	9: "Sort",
}

func (p *SearchLogDBWReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchLogDBWReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDataType bool = false
	var issetQuery bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetLimit bool = false
	var issetHighLight bool = false
	var issetContext bool = false
	var issetSort bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLimit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetHighLight = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetContext = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetSort = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLimit {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetHighLight {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetContext {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetSort {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchLogDBWReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SearchLogDBWReq[fieldId]))
}

func (p *SearchLogDBWReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField2(iprot thrift.TProtocol) error {

	var _field StatisticDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StatisticDataType(v)
	}
	p.DataType = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Limit = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField7(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HighLight = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Context = _field
	return nil
}
func (p *SearchLogDBWReq) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Sort = _field
	return nil
}

func (p *SearchLogDBWReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchLogDBWReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SearchLogDBWReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Limit", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Limit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HighLight", thrift.BOOL, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HighLight); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Context", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Context); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SearchLogDBWReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Sort", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Sort); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SearchLogDBWReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLogDBWReq(%+v)", *p)

}

func (p *SearchLogDBWReq) DeepEqual(ano *SearchLogDBWReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Query) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field7DeepEqual(ano.HighLight) {
		return false
	}
	if !p.Field8DeepEqual(ano.Context) {
		return false
	}
	if !p.Field9DeepEqual(ano.Sort) {
		return false
	}
	return true
}

func (p *SearchLogDBWReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field2DeepEqual(src StatisticDataType) bool {

	if p.DataType != src {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field4DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field5DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field6DeepEqual(src int64) bool {

	if p.Limit != src {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field7DeepEqual(src bool) bool {

	if p.HighLight != src {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field8DeepEqual(src string) bool {

	if strings.Compare(p.Context, src) != 0 {
		return false
	}
	return true
}
func (p *SearchLogDBWReq) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Sort, src) != 0 {
		return false
	}
	return true
}

type SearchLogDBWResp struct {
	TLSResponse string `thrift:"TLSResponse,1,required" frugal:"1,required,string" json:"TLSResponse"`
	TLSError    string `thrift:"TLSError,2,required" frugal:"2,required,string" json:"TLSError"`
}

func NewSearchLogDBWResp() *SearchLogDBWResp {
	return &SearchLogDBWResp{}
}

func (p *SearchLogDBWResp) InitDefault() {
}

func (p *SearchLogDBWResp) GetTLSResponse() (v string) {
	return p.TLSResponse
}

func (p *SearchLogDBWResp) GetTLSError() (v string) {
	return p.TLSError
}
func (p *SearchLogDBWResp) SetTLSResponse(val string) {
	p.TLSResponse = val
}
func (p *SearchLogDBWResp) SetTLSError(val string) {
	p.TLSError = val
}

var fieldIDToName_SearchLogDBWResp = map[int16]string{
	1: "TLSResponse",
	2: "TLSError",
}

func (p *SearchLogDBWResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchLogDBWResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTLSResponse bool = false
	var issetTLSError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSResponse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTLSResponse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTLSError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchLogDBWResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SearchLogDBWResp[fieldId]))
}

func (p *SearchLogDBWResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSResponse = _field
	return nil
}
func (p *SearchLogDBWResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSError = _field
	return nil
}

func (p *SearchLogDBWResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchLogDBWResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SearchLogDBWResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchLogDBWResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSResponse", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SearchLogDBWResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSError", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SearchLogDBWResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLogDBWResp(%+v)", *p)

}

func (p *SearchLogDBWResp) DeepEqual(ano *SearchLogDBWResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TLSResponse) {
		return false
	}
	if !p.Field2DeepEqual(ano.TLSError) {
		return false
	}
	return true
}

func (p *SearchLogDBWResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TLSResponse, src) != 0 {
		return false
	}
	return true
}
func (p *SearchLogDBWResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TLSError, src) != 0 {
		return false
	}
	return true
}

type DescribeHistogramV1DBWReq struct {
	InstanceId string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DataType   StatisticDataType `thrift:"DataType,2,required" frugal:"2,required,StatisticDataType" json:"DataType"`
	Query      string            `thrift:"Query,3,required" frugal:"3,required,string" json:"Query"`
	StartTime  int64             `thrift:"StartTime,4,required" frugal:"4,required,i64" json:"StartTime"`
	EndTime    int64             `thrift:"EndTime,5,required" frugal:"5,required,i64" json:"EndTime"`
	Interval   int64             `thrift:"Interval,6,required" frugal:"6,required,i64" json:"Interval"`
}

func NewDescribeHistogramV1DBWReq() *DescribeHistogramV1DBWReq {
	return &DescribeHistogramV1DBWReq{}
}

func (p *DescribeHistogramV1DBWReq) InitDefault() {
}

func (p *DescribeHistogramV1DBWReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeHistogramV1DBWReq) GetDataType() (v StatisticDataType) {
	return p.DataType
}

func (p *DescribeHistogramV1DBWReq) GetQuery() (v string) {
	return p.Query
}

func (p *DescribeHistogramV1DBWReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *DescribeHistogramV1DBWReq) GetEndTime() (v int64) {
	return p.EndTime
}

func (p *DescribeHistogramV1DBWReq) GetInterval() (v int64) {
	return p.Interval
}
func (p *DescribeHistogramV1DBWReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeHistogramV1DBWReq) SetDataType(val StatisticDataType) {
	p.DataType = val
}
func (p *DescribeHistogramV1DBWReq) SetQuery(val string) {
	p.Query = val
}
func (p *DescribeHistogramV1DBWReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *DescribeHistogramV1DBWReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *DescribeHistogramV1DBWReq) SetInterval(val int64) {
	p.Interval = val
}

var fieldIDToName_DescribeHistogramV1DBWReq = map[int16]string{
	1: "InstanceId",
	2: "DataType",
	3: "Query",
	4: "StartTime",
	5: "EndTime",
	6: "Interval",
}

func (p *DescribeHistogramV1DBWReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHistogramV1DBWReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDataType bool = false
	var issetQuery bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetInterval bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetQuery = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetInterval = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetQuery {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInterval {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeHistogramV1DBWReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeHistogramV1DBWReq[fieldId]))
}

func (p *DescribeHistogramV1DBWReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeHistogramV1DBWReq) ReadField2(iprot thrift.TProtocol) error {

	var _field StatisticDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StatisticDataType(v)
	}
	p.DataType = _field
	return nil
}
func (p *DescribeHistogramV1DBWReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *DescribeHistogramV1DBWReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeHistogramV1DBWReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeHistogramV1DBWReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Interval = _field
	return nil
}

func (p *DescribeHistogramV1DBWReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHistogramV1DBWReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeHistogramV1DBWReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Interval", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Interval); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeHistogramV1DBWReq(%+v)", *p)

}

func (p *DescribeHistogramV1DBWReq) DeepEqual(ano *DescribeHistogramV1DBWReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Query) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.Interval) {
		return false
	}
	return true
}

func (p *DescribeHistogramV1DBWReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWReq) Field2DeepEqual(src StatisticDataType) bool {

	if p.DataType != src {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWReq) Field4DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWReq) Field5DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWReq) Field6DeepEqual(src int64) bool {

	if p.Interval != src {
		return false
	}
	return true
}

type DescribeHistogramV1DBWResp struct {
	TLSResponse string `thrift:"TLSResponse,1,required" frugal:"1,required,string" json:"TLSResponse"`
	TLSError    string `thrift:"TLSError,2,required" frugal:"2,required,string" json:"TLSError"`
}

func NewDescribeHistogramV1DBWResp() *DescribeHistogramV1DBWResp {
	return &DescribeHistogramV1DBWResp{}
}

func (p *DescribeHistogramV1DBWResp) InitDefault() {
}

func (p *DescribeHistogramV1DBWResp) GetTLSResponse() (v string) {
	return p.TLSResponse
}

func (p *DescribeHistogramV1DBWResp) GetTLSError() (v string) {
	return p.TLSError
}
func (p *DescribeHistogramV1DBWResp) SetTLSResponse(val string) {
	p.TLSResponse = val
}
func (p *DescribeHistogramV1DBWResp) SetTLSError(val string) {
	p.TLSError = val
}

var fieldIDToName_DescribeHistogramV1DBWResp = map[int16]string{
	1: "TLSResponse",
	2: "TLSError",
}

func (p *DescribeHistogramV1DBWResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHistogramV1DBWResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTLSResponse bool = false
	var issetTLSError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSResponse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTLSResponse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTLSError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeHistogramV1DBWResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeHistogramV1DBWResp[fieldId]))
}

func (p *DescribeHistogramV1DBWResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSResponse = _field
	return nil
}
func (p *DescribeHistogramV1DBWResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSError = _field
	return nil
}

func (p *DescribeHistogramV1DBWResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHistogramV1DBWResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeHistogramV1DBWResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeHistogramV1DBWResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSResponse", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSError", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeHistogramV1DBWResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeHistogramV1DBWResp(%+v)", *p)

}

func (p *DescribeHistogramV1DBWResp) DeepEqual(ano *DescribeHistogramV1DBWResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TLSResponse) {
		return false
	}
	if !p.Field2DeepEqual(ano.TLSError) {
		return false
	}
	return true
}

func (p *DescribeHistogramV1DBWResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TLSResponse, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHistogramV1DBWResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TLSError, src) != 0 {
		return false
	}
	return true
}

type DescribeIndexDBWReq struct {
	InstanceId string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DataType   StatisticDataType `thrift:"DataType,2,required" frugal:"2,required,StatisticDataType" json:"DataType"`
}

func NewDescribeIndexDBWReq() *DescribeIndexDBWReq {
	return &DescribeIndexDBWReq{}
}

func (p *DescribeIndexDBWReq) InitDefault() {
}

func (p *DescribeIndexDBWReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeIndexDBWReq) GetDataType() (v StatisticDataType) {
	return p.DataType
}
func (p *DescribeIndexDBWReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeIndexDBWReq) SetDataType(val StatisticDataType) {
	p.DataType = val
}

var fieldIDToName_DescribeIndexDBWReq = map[int16]string{
	1: "InstanceId",
	2: "DataType",
}

func (p *DescribeIndexDBWReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexDBWReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDataType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeIndexDBWReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeIndexDBWReq[fieldId]))
}

func (p *DescribeIndexDBWReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeIndexDBWReq) ReadField2(iprot thrift.TProtocol) error {

	var _field StatisticDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StatisticDataType(v)
	}
	p.DataType = _field
	return nil
}

func (p *DescribeIndexDBWReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexDBWReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeIndexDBWReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeIndexDBWReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeIndexDBWReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeIndexDBWReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeIndexDBWReq(%+v)", *p)

}

func (p *DescribeIndexDBWReq) DeepEqual(ano *DescribeIndexDBWReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataType) {
		return false
	}
	return true
}

func (p *DescribeIndexDBWReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexDBWReq) Field2DeepEqual(src StatisticDataType) bool {

	if p.DataType != src {
		return false
	}
	return true
}

type DescribeIndexDBWResp struct {
	TLSResponse string `thrift:"TLSResponse,1,required" frugal:"1,required,string" json:"TLSResponse"`
	TLSError    string `thrift:"TLSError,2,required" frugal:"2,required,string" json:"TLSError"`
}

func NewDescribeIndexDBWResp() *DescribeIndexDBWResp {
	return &DescribeIndexDBWResp{}
}

func (p *DescribeIndexDBWResp) InitDefault() {
}

func (p *DescribeIndexDBWResp) GetTLSResponse() (v string) {
	return p.TLSResponse
}

func (p *DescribeIndexDBWResp) GetTLSError() (v string) {
	return p.TLSError
}
func (p *DescribeIndexDBWResp) SetTLSResponse(val string) {
	p.TLSResponse = val
}
func (p *DescribeIndexDBWResp) SetTLSError(val string) {
	p.TLSError = val
}

var fieldIDToName_DescribeIndexDBWResp = map[int16]string{
	1: "TLSResponse",
	2: "TLSError",
}

func (p *DescribeIndexDBWResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexDBWResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTLSResponse bool = false
	var issetTLSError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSResponse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTLSResponse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTLSError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeIndexDBWResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeIndexDBWResp[fieldId]))
}

func (p *DescribeIndexDBWResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSResponse = _field
	return nil
}
func (p *DescribeIndexDBWResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSError = _field
	return nil
}

func (p *DescribeIndexDBWResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeIndexDBWResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeIndexDBWResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeIndexDBWResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSResponse", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeIndexDBWResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSError", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeIndexDBWResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeIndexDBWResp(%+v)", *p)

}

func (p *DescribeIndexDBWResp) DeepEqual(ano *DescribeIndexDBWResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TLSResponse) {
		return false
	}
	if !p.Field2DeepEqual(ano.TLSError) {
		return false
	}
	return true
}

func (p *DescribeIndexDBWResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TLSResponse, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeIndexDBWResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TLSError, src) != 0 {
		return false
	}
	return true
}

type DescribeLogContextDBWReq struct {
	InstanceId    string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	DataType      StatisticDataType `thrift:"DataType,2,required" frugal:"2,required,StatisticDataType" json:"DataType"`
	ContextFlow   string            `thrift:"ContextFlow,4,required" frugal:"4,required,string" json:"ContextFlow"`
	PackageOffset int64             `thrift:"PackageOffset,5,required" frugal:"5,required,i64" json:"PackageOffset"`
	Source        string            `thrift:"Source,6,required" frugal:"6,required,string" json:"Source"`
	PrevLogs      *int64            `thrift:"PrevLogs,7,optional" frugal:"7,optional,i64" json:"PrevLogs,omitempty"`
	NextLogs      *int64            `thrift:"NextLogs,8,optional" frugal:"8,optional,i64" json:"NextLogs,omitempty"`
}

func NewDescribeLogContextDBWReq() *DescribeLogContextDBWReq {
	return &DescribeLogContextDBWReq{}
}

func (p *DescribeLogContextDBWReq) InitDefault() {
}

func (p *DescribeLogContextDBWReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeLogContextDBWReq) GetDataType() (v StatisticDataType) {
	return p.DataType
}

func (p *DescribeLogContextDBWReq) GetContextFlow() (v string) {
	return p.ContextFlow
}

func (p *DescribeLogContextDBWReq) GetPackageOffset() (v int64) {
	return p.PackageOffset
}

func (p *DescribeLogContextDBWReq) GetSource() (v string) {
	return p.Source
}

var DescribeLogContextDBWReq_PrevLogs_DEFAULT int64

func (p *DescribeLogContextDBWReq) GetPrevLogs() (v int64) {
	if !p.IsSetPrevLogs() {
		return DescribeLogContextDBWReq_PrevLogs_DEFAULT
	}
	return *p.PrevLogs
}

var DescribeLogContextDBWReq_NextLogs_DEFAULT int64

func (p *DescribeLogContextDBWReq) GetNextLogs() (v int64) {
	if !p.IsSetNextLogs() {
		return DescribeLogContextDBWReq_NextLogs_DEFAULT
	}
	return *p.NextLogs
}
func (p *DescribeLogContextDBWReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeLogContextDBWReq) SetDataType(val StatisticDataType) {
	p.DataType = val
}
func (p *DescribeLogContextDBWReq) SetContextFlow(val string) {
	p.ContextFlow = val
}
func (p *DescribeLogContextDBWReq) SetPackageOffset(val int64) {
	p.PackageOffset = val
}
func (p *DescribeLogContextDBWReq) SetSource(val string) {
	p.Source = val
}
func (p *DescribeLogContextDBWReq) SetPrevLogs(val *int64) {
	p.PrevLogs = val
}
func (p *DescribeLogContextDBWReq) SetNextLogs(val *int64) {
	p.NextLogs = val
}

var fieldIDToName_DescribeLogContextDBWReq = map[int16]string{
	1: "InstanceId",
	2: "DataType",
	4: "ContextFlow",
	5: "PackageOffset",
	6: "Source",
	7: "PrevLogs",
	8: "NextLogs",
}

func (p *DescribeLogContextDBWReq) IsSetPrevLogs() bool {
	return p.PrevLogs != nil
}

func (p *DescribeLogContextDBWReq) IsSetNextLogs() bool {
	return p.NextLogs != nil
}

func (p *DescribeLogContextDBWReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogContextDBWReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDataType bool = false
	var issetContextFlow bool = false
	var issetPackageOffset bool = false
	var issetSource bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetContextFlow = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPackageOffset = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetContextFlow {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPackageOffset {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSource {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLogContextDBWReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLogContextDBWReq[fieldId]))
}

func (p *DescribeLogContextDBWReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField2(iprot thrift.TProtocol) error {

	var _field StatisticDataType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StatisticDataType(v)
	}
	p.DataType = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContextFlow = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PackageOffset = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Source = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PrevLogs = _field
	return nil
}
func (p *DescribeLogContextDBWReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NextLogs = _field
	return nil
}

func (p *DescribeLogContextDBWReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogContextDBWReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLogContextDBWReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ContextFlow", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContextFlow); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PackageOffset", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PackageOffset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Source", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Source); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrevLogs() {
		if err = oprot.WriteFieldBegin("PrevLogs", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PrevLogs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetNextLogs() {
		if err = oprot.WriteFieldBegin("NextLogs", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.NextLogs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeLogContextDBWReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogContextDBWReq(%+v)", *p)

}

func (p *DescribeLogContextDBWReq) DeepEqual(ano *DescribeLogContextDBWReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataType) {
		return false
	}
	if !p.Field4DeepEqual(ano.ContextFlow) {
		return false
	}
	if !p.Field5DeepEqual(ano.PackageOffset) {
		return false
	}
	if !p.Field6DeepEqual(ano.Source) {
		return false
	}
	if !p.Field7DeepEqual(ano.PrevLogs) {
		return false
	}
	if !p.Field8DeepEqual(ano.NextLogs) {
		return false
	}
	return true
}

func (p *DescribeLogContextDBWReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field2DeepEqual(src StatisticDataType) bool {

	if p.DataType != src {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ContextFlow, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field5DeepEqual(src int64) bool {

	if p.PackageOffset != src {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Source, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field7DeepEqual(src *int64) bool {

	if p.PrevLogs == src {
		return true
	} else if p.PrevLogs == nil || src == nil {
		return false
	}
	if *p.PrevLogs != *src {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWReq) Field8DeepEqual(src *int64) bool {

	if p.NextLogs == src {
		return true
	} else if p.NextLogs == nil || src == nil {
		return false
	}
	if *p.NextLogs != *src {
		return false
	}
	return true
}

type DescribeLogContextDBWResp struct {
	TLSResponse string `thrift:"TLSResponse,1,required" frugal:"1,required,string" json:"TLSResponse"`
	TLSError    string `thrift:"TLSError,2,required" frugal:"2,required,string" json:"TLSError"`
}

func NewDescribeLogContextDBWResp() *DescribeLogContextDBWResp {
	return &DescribeLogContextDBWResp{}
}

func (p *DescribeLogContextDBWResp) InitDefault() {
}

func (p *DescribeLogContextDBWResp) GetTLSResponse() (v string) {
	return p.TLSResponse
}

func (p *DescribeLogContextDBWResp) GetTLSError() (v string) {
	return p.TLSError
}
func (p *DescribeLogContextDBWResp) SetTLSResponse(val string) {
	p.TLSResponse = val
}
func (p *DescribeLogContextDBWResp) SetTLSError(val string) {
	p.TLSError = val
}

var fieldIDToName_DescribeLogContextDBWResp = map[int16]string{
	1: "TLSResponse",
	2: "TLSError",
}

func (p *DescribeLogContextDBWResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogContextDBWResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTLSResponse bool = false
	var issetTLSError bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSResponse = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTLSError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTLSResponse {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTLSError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeLogContextDBWResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeLogContextDBWResp[fieldId]))
}

func (p *DescribeLogContextDBWResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSResponse = _field
	return nil
}
func (p *DescribeLogContextDBWResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TLSError = _field
	return nil
}

func (p *DescribeLogContextDBWResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeLogContextDBWResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeLogContextDBWResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeLogContextDBWResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSResponse", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeLogContextDBWResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TLSError", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TLSError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeLogContextDBWResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeLogContextDBWResp(%+v)", *p)

}

func (p *DescribeLogContextDBWResp) DeepEqual(ano *DescribeLogContextDBWResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TLSResponse) {
		return false
	}
	if !p.Field2DeepEqual(ano.TLSError) {
		return false
	}
	return true
}

func (p *DescribeLogContextDBWResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TLSResponse, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeLogContextDBWResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TLSError, src) != 0 {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ErrLogLevel int64

const (
	ErrLogLevel_Note    ErrLogLevel = 0
	ErrLogLevel_Warning ErrLogLevel = 1
	ErrLogLevel_Error   ErrLogLevel = 2
)

func (p ErrLogLevel) String() string {
	switch p {
	case ErrLogLevel_Note:
		return "Note"
	case ErrLogLevel_Warning:
		return "Warning"
	case ErrLogLevel_Error:
		return "Error"
	}
	return "<UNSET>"
}

func ErrLogLevelFromString(s string) (ErrLogLevel, error) {
	switch s {
	case "Note":
		return ErrLogLevel_Note, nil
	case "Warning":
		return ErrLogLevel_Warning, nil
	case "Error":
		return ErrLogLevel_Error, nil
	}
	return ErrLogLevel(0), fmt.E<PERSON>rf("not a valid ErrLogLevel string")
}

func ErrLogLevelPtr(v ErrLogLevel) *ErrLogLevel { return &v }

func (p ErrLogLevel) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrLogLevel) UnmarshalText(text []byte) error {
	q, err := ErrLogLevelFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type InnerRdsLogType int64

const (
	InnerRdsLogType_MySQLErrorLog InnerRdsLogType = 1
	InnerRdsLogType_ProxyErrorLog InnerRdsLogType = 2
)

func (p InnerRdsLogType) String() string {
	switch p {
	case InnerRdsLogType_MySQLErrorLog:
		return "MySQLErrorLog"
	case InnerRdsLogType_ProxyErrorLog:
		return "ProxyErrorLog"
	}
	return "<UNSET>"
}

func InnerRdsLogTypeFromString(s string) (InnerRdsLogType, error) {
	switch s {
	case "MySQLErrorLog":
		return InnerRdsLogType_MySQLErrorLog, nil
	case "ProxyErrorLog":
		return InnerRdsLogType_ProxyErrorLog, nil
	}
	return InnerRdsLogType(0), fmt.Errorf("not a valid InnerRdsLogType string")
}

func InnerRdsLogTypePtr(v InnerRdsLogType) *InnerRdsLogType { return &v }

func (p InnerRdsLogType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InnerRdsLogType) UnmarshalText(text []byte) error {
	q, err := InnerRdsLogTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type LogExportTaskStatus int64

const (
	LogExportTaskStatus_Creating LogExportTaskStatus = 1
	LogExportTaskStatus_Enable   LogExportTaskStatus = 2
	LogExportTaskStatus_Expire   LogExportTaskStatus = 3
	LogExportTaskStatus_Fail     LogExportTaskStatus = 4
)

func (p LogExportTaskStatus) String() string {
	switch p {
	case LogExportTaskStatus_Creating:
		return "Creating"
	case LogExportTaskStatus_Enable:
		return "Enable"
	case LogExportTaskStatus_Expire:
		return "Expire"
	case LogExportTaskStatus_Fail:
		return "Fail"
	}
	return "<UNSET>"
}

func LogExportTaskStatusFromString(s string) (LogExportTaskStatus, error) {
	switch s {
	case "Creating":
		return LogExportTaskStatus_Creating, nil
	case "Enable":
		return LogExportTaskStatus_Enable, nil
	case "Expire":
		return LogExportTaskStatus_Expire, nil
	case "Fail":
		return LogExportTaskStatus_Fail, nil
	}
	return LogExportTaskStatus(0), fmt.Errorf("not a valid LogExportTaskStatus string")
}

func LogExportTaskStatusPtr(v LogExportTaskStatus) *LogExportTaskStatus { return &v }

func (p LogExportTaskStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *LogExportTaskStatus) UnmarshalText(text []byte) error {
	q, err := LogExportTaskStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrLogSearchParam struct {
	LogLevel []ErrLogLevel `thrift:"LogLevel,1,optional" frugal:"1,optional,list<ErrLogLevel>" json:"LogLevel,omitempty"`
	NodeIds  []string      `thrift:"NodeIds,2,optional" frugal:"2,optional,list<string>" json:"NodeIds,omitempty"`
	Keyword  *string       `thrift:"keyword,3,optional" frugal:"3,optional,string" json:"keyword,omitempty"`
}

func NewErrLogSearchParam() *ErrLogSearchParam {
	return &ErrLogSearchParam{}
}

func (p *ErrLogSearchParam) InitDefault() {
}

var ErrLogSearchParam_LogLevel_DEFAULT []ErrLogLevel

func (p *ErrLogSearchParam) GetLogLevel() (v []ErrLogLevel) {
	if !p.IsSetLogLevel() {
		return ErrLogSearchParam_LogLevel_DEFAULT
	}
	return p.LogLevel
}

var ErrLogSearchParam_NodeIds_DEFAULT []string

func (p *ErrLogSearchParam) GetNodeIds() (v []string) {
	if !p.IsSetNodeIds() {
		return ErrLogSearchParam_NodeIds_DEFAULT
	}
	return p.NodeIds
}

var ErrLogSearchParam_Keyword_DEFAULT string

func (p *ErrLogSearchParam) GetKeyword() (v string) {
	if !p.IsSetKeyword() {
		return ErrLogSearchParam_Keyword_DEFAULT
	}
	return *p.Keyword
}
func (p *ErrLogSearchParam) SetLogLevel(val []ErrLogLevel) {
	p.LogLevel = val
}
func (p *ErrLogSearchParam) SetNodeIds(val []string) {
	p.NodeIds = val
}
func (p *ErrLogSearchParam) SetKeyword(val *string) {
	p.Keyword = val
}

var fieldIDToName_ErrLogSearchParam = map[int16]string{
	1: "LogLevel",
	2: "NodeIds",
	3: "keyword",
}

func (p *ErrLogSearchParam) IsSetLogLevel() bool {
	return p.LogLevel != nil
}

func (p *ErrLogSearchParam) IsSetNodeIds() bool {
	return p.NodeIds != nil
}

func (p *ErrLogSearchParam) IsSetKeyword() bool {
	return p.Keyword != nil
}

func (p *ErrLogSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrLogSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrLogSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ErrLogSearchParam) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]ErrLogLevel, 0, size)
	for i := 0; i < size; i++ {

		var _elem ErrLogLevel
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = ErrLogLevel(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.LogLevel = _field
	return nil
}
func (p *ErrLogSearchParam) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeIds = _field
	return nil
}
func (p *ErrLogSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Keyword = _field
	return nil
}

func (p *ErrLogSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrLogSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrLogSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrLogSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogLevel() {
		if err = oprot.WriteFieldBegin("LogLevel", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.LogLevel)); err != nil {
			return err
		}
		for _, v := range p.LogLevel {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ErrLogSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeIds() {
		if err = oprot.WriteFieldBegin("NodeIds", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeIds)); err != nil {
			return err
		}
		for _, v := range p.NodeIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrLogSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyword() {
		if err = oprot.WriteFieldBegin("keyword", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Keyword); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrLogSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrLogSearchParam(%+v)", *p)

}

func (p *ErrLogSearchParam) DeepEqual(ano *ErrLogSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.LogLevel) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeIds) {
		return false
	}
	if !p.Field3DeepEqual(ano.Keyword) {
		return false
	}
	return true
}

func (p *ErrLogSearchParam) Field1DeepEqual(src []ErrLogLevel) bool {

	if len(p.LogLevel) != len(src) {
		return false
	}
	for i, v := range p.LogLevel {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *ErrLogSearchParam) Field2DeepEqual(src []string) bool {

	if len(p.NodeIds) != len(src) {
		return false
	}
	for i, v := range p.NodeIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ErrLogSearchParam) Field3DeepEqual(src *string) bool {

	if p.Keyword == src {
		return true
	} else if p.Keyword == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Keyword, *src) != 0 {
		return false
	}
	return true
}

type ErrLog struct {
	Timestamp string      `thrift:"Timestamp,1,required" frugal:"1,required,string" json:"Timestamp"`
	LogLevel  ErrLogLevel `thrift:"LogLevel,2,required" frugal:"2,required,ErrLogLevel" json:"LogLevel"`
	Content   string      `thrift:"Content,3,required" frugal:"3,required,string" json:"Content"`
}

func NewErrLog() *ErrLog {
	return &ErrLog{}
}

func (p *ErrLog) InitDefault() {
}

func (p *ErrLog) GetTimestamp() (v string) {
	return p.Timestamp
}

func (p *ErrLog) GetLogLevel() (v ErrLogLevel) {
	return p.LogLevel
}

func (p *ErrLog) GetContent() (v string) {
	return p.Content
}
func (p *ErrLog) SetTimestamp(val string) {
	p.Timestamp = val
}
func (p *ErrLog) SetLogLevel(val ErrLogLevel) {
	p.LogLevel = val
}
func (p *ErrLog) SetContent(val string) {
	p.Content = val
}

var fieldIDToName_ErrLog = map[int16]string{
	1: "Timestamp",
	2: "LogLevel",
	3: "Content",
}

func (p *ErrLog) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrLog")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimestamp bool = false
	var issetLogLevel bool = false
	var issetContent bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimestamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogLevel = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimestamp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLogLevel {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetContent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrLog[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ErrLog[fieldId]))
}

func (p *ErrLog) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Timestamp = _field
	return nil
}
func (p *ErrLog) ReadField2(iprot thrift.TProtocol) error {

	var _field ErrLogLevel
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrLogLevel(v)
	}
	p.LogLevel = _field
	return nil
}
func (p *ErrLog) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}

func (p *ErrLog) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrLog")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrLog"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrLog) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Timestamp", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Timestamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ErrLog) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogLevel", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LogLevel)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrLog) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrLog) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrLog(%+v)", *p)

}

func (p *ErrLog) DeepEqual(ano *ErrLog) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Timestamp) {
		return false
	}
	if !p.Field2DeepEqual(ano.LogLevel) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	return true
}

func (p *ErrLog) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Timestamp, src) != 0 {
		return false
	}
	return true
}
func (p *ErrLog) Field2DeepEqual(src ErrLogLevel) bool {

	if p.LogLevel != src {
		return false
	}
	return true
}
func (p *ErrLog) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}

type DescribeErrLogsReq struct {
	InstanceId   string             `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType       `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	StartTime    int64              `thrift:"StartTime,3,required" frugal:"3,required,i64" json:"StartTime"`
	EndTime      int64              `thrift:"EndTime,4,required" frugal:"4,required,i64" json:"EndTime"`
	SearchParam  *ErrLogSearchParam `thrift:"SearchParam,5,optional" frugal:"5,optional,ErrLogSearchParam" json:"SearchParam,omitempty"`
	PageNumber   *int32             `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32             `thrift:"PageSize,7,optional" frugal:"7,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeErrLogsReq() *DescribeErrLogsReq {
	return &DescribeErrLogsReq{}
}

func (p *DescribeErrLogsReq) InitDefault() {
}

func (p *DescribeErrLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeErrLogsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeErrLogsReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *DescribeErrLogsReq) GetEndTime() (v int64) {
	return p.EndTime
}

var DescribeErrLogsReq_SearchParam_DEFAULT *ErrLogSearchParam

func (p *DescribeErrLogsReq) GetSearchParam() (v *ErrLogSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeErrLogsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeErrLogsReq_PageNumber_DEFAULT int32

func (p *DescribeErrLogsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeErrLogsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeErrLogsReq_PageSize_DEFAULT int32

func (p *DescribeErrLogsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeErrLogsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeErrLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeErrLogsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeErrLogsReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *DescribeErrLogsReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *DescribeErrLogsReq) SetSearchParam(val *ErrLogSearchParam) {
	p.SearchParam = val
}
func (p *DescribeErrLogsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeErrLogsReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeErrLogsReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "StartTime",
	4: "EndTime",
	5: "SearchParam",
	6: "PageNumber",
	7: "PageSize",
}

func (p *DescribeErrLogsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeErrLogsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeErrLogsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeErrLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrLogsReq[fieldId]))
}

func (p *DescribeErrLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField5(iprot thrift.TProtocol) error {
	_field := NewErrLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeErrLogsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeErrLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeErrLogsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeErrLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrLogsReq(%+v)", *p)

}

func (p *DescribeErrLogsReq) DeepEqual(ano *DescribeErrLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeErrLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field3DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field4DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field5DeepEqual(src *ErrLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeErrLogsReq) Field7DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeErrLogsResp struct {
	ErrLogs []*ErrLog `thrift:"ErrLogs,1,required" frugal:"1,required,list<ErrLog>" json:"ErrLogs"`
	Total   int32     `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeErrLogsResp() *DescribeErrLogsResp {
	return &DescribeErrLogsResp{}
}

func (p *DescribeErrLogsResp) InitDefault() {
}

func (p *DescribeErrLogsResp) GetErrLogs() (v []*ErrLog) {
	return p.ErrLogs
}

func (p *DescribeErrLogsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeErrLogsResp) SetErrLogs(val []*ErrLog) {
	p.ErrLogs = val
}
func (p *DescribeErrLogsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeErrLogsResp = map[int16]string{
	1: "ErrLogs",
	2: "Total",
}

func (p *DescribeErrLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetErrLogs bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetErrLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrLogsResp[fieldId]))
}

func (p *DescribeErrLogsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ErrLog, 0, size)
	values := make([]ErrLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ErrLogs = _field
	return nil
}
func (p *DescribeErrLogsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeErrLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ErrLogs)); err != nil {
		return err
	}
	for _, v := range p.ErrLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrLogsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrLogsResp(%+v)", *p)

}

func (p *DescribeErrLogsResp) DeepEqual(ano *DescribeErrLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ErrLogs) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeErrLogsResp) Field1DeepEqual(src []*ErrLog) bool {

	if len(p.ErrLogs) != len(src) {
		return false
	}
	for i, v := range p.ErrLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeErrLogsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type GetLogFilesReq struct {
	DbName   string          `thrift:"DbName,1,required" frugal:"1,required,string" json:"DbName"`
	NodeID   string          `thrift:"NodeID,2,required" frugal:"2,required,string" json:"NodeID"`
	RegionId string          `thrift:"RegionId,4,required" frugal:"4,required,string" json:"RegionId"`
	VRegion  string          `thrift:"VRegion,5,required" frugal:"5,required,string" json:"VRegion"`
	LogType  InnerRdsLogType `thrift:"LogType,6,required" frugal:"6,required,InnerRdsLogType" json:"LogType"`
}

func NewGetLogFilesReq() *GetLogFilesReq {
	return &GetLogFilesReq{}
}

func (p *GetLogFilesReq) InitDefault() {
}

func (p *GetLogFilesReq) GetDbName() (v string) {
	return p.DbName
}

func (p *GetLogFilesReq) GetNodeID() (v string) {
	return p.NodeID
}

func (p *GetLogFilesReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *GetLogFilesReq) GetVRegion() (v string) {
	return p.VRegion
}

func (p *GetLogFilesReq) GetLogType() (v InnerRdsLogType) {
	return p.LogType
}
func (p *GetLogFilesReq) SetDbName(val string) {
	p.DbName = val
}
func (p *GetLogFilesReq) SetNodeID(val string) {
	p.NodeID = val
}
func (p *GetLogFilesReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *GetLogFilesReq) SetVRegion(val string) {
	p.VRegion = val
}
func (p *GetLogFilesReq) SetLogType(val InnerRdsLogType) {
	p.LogType = val
}

var fieldIDToName_GetLogFilesReq = map[int16]string{
	1: "DbName",
	2: "NodeID",
	4: "RegionId",
	5: "VRegion",
	6: "LogType",
}

func (p *GetLogFilesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetLogFilesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDbName bool = false
	var issetNodeID bool = false
	var issetRegionId bool = false
	var issetVRegion bool = false
	var issetLogType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetVRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDbName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetVRegion {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLogType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetLogFilesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetLogFilesReq[fieldId]))
}

func (p *GetLogFilesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *GetLogFilesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeID = _field
	return nil
}
func (p *GetLogFilesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *GetLogFilesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VRegion = _field
	return nil
}
func (p *GetLogFilesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field InnerRdsLogType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InnerRdsLogType(v)
	}
	p.LogType = _field
	return nil
}

func (p *GetLogFilesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetLogFilesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetLogFilesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetLogFilesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetLogFilesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetLogFilesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GetLogFilesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VRegion", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VRegion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *GetLogFilesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LogType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GetLogFilesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLogFilesReq(%+v)", *p)

}

func (p *GetLogFilesReq) DeepEqual(ano *GetLogFilesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeID) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field5DeepEqual(ano.VRegion) {
		return false
	}
	if !p.Field6DeepEqual(ano.LogType) {
		return false
	}
	return true
}

func (p *GetLogFilesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *GetLogFilesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NodeID, src) != 0 {
		return false
	}
	return true
}
func (p *GetLogFilesReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *GetLogFilesReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.VRegion, src) != 0 {
		return false
	}
	return true
}
func (p *GetLogFilesReq) Field6DeepEqual(src InnerRdsLogType) bool {

	if p.LogType != src {
		return false
	}
	return true
}

type GetLogFilesResp struct {
	RdsLogFiles []*RdsLogFile `thrift:"RdsLogFiles,1,required" frugal:"1,required,list<RdsLogFile>" json:"RdsLogFiles"`
}

func NewGetLogFilesResp() *GetLogFilesResp {
	return &GetLogFilesResp{}
}

func (p *GetLogFilesResp) InitDefault() {
}

func (p *GetLogFilesResp) GetRdsLogFiles() (v []*RdsLogFile) {
	return p.RdsLogFiles
}
func (p *GetLogFilesResp) SetRdsLogFiles(val []*RdsLogFile) {
	p.RdsLogFiles = val
}

var fieldIDToName_GetLogFilesResp = map[int16]string{
	1: "RdsLogFiles",
}

func (p *GetLogFilesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetLogFilesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRdsLogFiles bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRdsLogFiles = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRdsLogFiles {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetLogFilesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetLogFilesResp[fieldId]))
}

func (p *GetLogFilesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RdsLogFile, 0, size)
	values := make([]RdsLogFile, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RdsLogFiles = _field
	return nil
}

func (p *GetLogFilesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetLogFilesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetLogFilesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetLogFilesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RdsLogFiles", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RdsLogFiles)); err != nil {
		return err
	}
	for _, v := range p.RdsLogFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetLogFilesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLogFilesResp(%+v)", *p)

}

func (p *GetLogFilesResp) DeepEqual(ano *GetLogFilesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RdsLogFiles) {
		return false
	}
	return true
}

func (p *GetLogFilesResp) Field1DeepEqual(src []*RdsLogFile) bool {

	if len(p.RdsLogFiles) != len(src) {
		return false
	}
	for i, v := range p.RdsLogFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RdsLogFile struct {
	FileName       string        `thrift:"FileName,1,required" frugal:"1,required,string" json:"FileName"`
	FileSize       int64         `thrift:"FileSize,2,required" frugal:"2,required,i64" json:"FileSize"`
	LastUpdateTime string        `thrift:"LastUpdateTime,3,required" frugal:"3,required,string" json:"LastUpdateTime"`
	ExportTasks    []*ExportTask `thrift:"ExportTasks,4,required" frugal:"4,required,list<ExportTask>" json:"ExportTasks"`
}

func NewRdsLogFile() *RdsLogFile {
	return &RdsLogFile{}
}

func (p *RdsLogFile) InitDefault() {
}

func (p *RdsLogFile) GetFileName() (v string) {
	return p.FileName
}

func (p *RdsLogFile) GetFileSize() (v int64) {
	return p.FileSize
}

func (p *RdsLogFile) GetLastUpdateTime() (v string) {
	return p.LastUpdateTime
}

func (p *RdsLogFile) GetExportTasks() (v []*ExportTask) {
	return p.ExportTasks
}
func (p *RdsLogFile) SetFileName(val string) {
	p.FileName = val
}
func (p *RdsLogFile) SetFileSize(val int64) {
	p.FileSize = val
}
func (p *RdsLogFile) SetLastUpdateTime(val string) {
	p.LastUpdateTime = val
}
func (p *RdsLogFile) SetExportTasks(val []*ExportTask) {
	p.ExportTasks = val
}

var fieldIDToName_RdsLogFile = map[int16]string{
	1: "FileName",
	2: "FileSize",
	3: "LastUpdateTime",
	4: "ExportTasks",
}

func (p *RdsLogFile) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RdsLogFile")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFileName bool = false
	var issetFileSize bool = false
	var issetLastUpdateTime bool = false
	var issetExportTasks bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetExportTasks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFileName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetFileSize {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLastUpdateTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetExportTasks {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RdsLogFile[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RdsLogFile[fieldId]))
}

func (p *RdsLogFile) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileName = _field
	return nil
}
func (p *RdsLogFile) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileSize = _field
	return nil
}
func (p *RdsLogFile) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastUpdateTime = _field
	return nil
}
func (p *RdsLogFile) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExportTask, 0, size)
	values := make([]ExportTask, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExportTasks = _field
	return nil
}

func (p *RdsLogFile) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RdsLogFile")

	var fieldId int16
	if err = oprot.WriteStructBegin("RdsLogFile"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RdsLogFile) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RdsLogFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileSize", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FileSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RdsLogFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastUpdateTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastUpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RdsLogFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExportTasks", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ExportTasks)); err != nil {
		return err
	}
	for _, v := range p.ExportTasks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RdsLogFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RdsLogFile(%+v)", *p)

}

func (p *RdsLogFile) DeepEqual(ano *RdsLogFile) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FileName) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.LastUpdateTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExportTasks) {
		return false
	}
	return true
}

func (p *RdsLogFile) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FileName, src) != 0 {
		return false
	}
	return true
}
func (p *RdsLogFile) Field2DeepEqual(src int64) bool {

	if p.FileSize != src {
		return false
	}
	return true
}
func (p *RdsLogFile) Field3DeepEqual(src string) bool {

	if strings.Compare(p.LastUpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *RdsLogFile) Field4DeepEqual(src []*ExportTask) bool {

	if len(p.ExportTasks) != len(src) {
		return false
	}
	for i, v := range p.ExportTasks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ExportTask struct {
	TaskId     string              `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	Url        string              `thrift:"Url,2,required" frugal:"2,required,string" json:"Url"`
	GenTime    string              `thrift:"GenTime,3,required" frugal:"3,required,string" json:"GenTime"`
	ExpireTime string              `thrift:"ExpireTime,4,required" frugal:"4,required,string" json:"ExpireTime"`
	TaskStatus LogExportTaskStatus `thrift:"TaskStatus,5,required" frugal:"5,required,LogExportTaskStatus" json:"TaskStatus"`
}

func NewExportTask() *ExportTask {
	return &ExportTask{}
}

func (p *ExportTask) InitDefault() {
}

func (p *ExportTask) GetTaskId() (v string) {
	return p.TaskId
}

func (p *ExportTask) GetUrl() (v string) {
	return p.Url
}

func (p *ExportTask) GetGenTime() (v string) {
	return p.GenTime
}

func (p *ExportTask) GetExpireTime() (v string) {
	return p.ExpireTime
}

func (p *ExportTask) GetTaskStatus() (v LogExportTaskStatus) {
	return p.TaskStatus
}
func (p *ExportTask) SetTaskId(val string) {
	p.TaskId = val
}
func (p *ExportTask) SetUrl(val string) {
	p.Url = val
}
func (p *ExportTask) SetGenTime(val string) {
	p.GenTime = val
}
func (p *ExportTask) SetExpireTime(val string) {
	p.ExpireTime = val
}
func (p *ExportTask) SetTaskStatus(val LogExportTaskStatus) {
	p.TaskStatus = val
}

var fieldIDToName_ExportTask = map[int16]string{
	1: "TaskId",
	2: "Url",
	3: "GenTime",
	4: "ExpireTime",
	5: "TaskStatus",
}

func (p *ExportTask) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExportTask")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetUrl bool = false
	var issetGenTime bool = false
	var issetExpireTime bool = false
	var issetTaskStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUrl = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetGenTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpireTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUrl {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetGenTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetExpireTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTaskStatus {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExportTask[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExportTask[fieldId]))
}

func (p *ExportTask) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *ExportTask) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Url = _field
	return nil
}
func (p *ExportTask) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GenTime = _field
	return nil
}
func (p *ExportTask) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpireTime = _field
	return nil
}
func (p *ExportTask) ReadField5(iprot thrift.TProtocol) error {

	var _field LogExportTaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LogExportTaskStatus(v)
	}
	p.TaskStatus = _field
	return nil
}

func (p *ExportTask) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExportTask")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExportTask"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExportTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExportTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Url", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Url); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExportTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GenTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GenTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExportTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExpireTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExpireTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExportTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskStatus", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExportTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportTask(%+v)", *p)

}

func (p *ExportTask) DeepEqual(ano *ExportTask) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Url) {
		return false
	}
	if !p.Field3DeepEqual(ano.GenTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExpireTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.TaskStatus) {
		return false
	}
	return true
}

func (p *ExportTask) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *ExportTask) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Url, src) != 0 {
		return false
	}
	return true
}
func (p *ExportTask) Field3DeepEqual(src string) bool {

	if strings.Compare(p.GenTime, src) != 0 {
		return false
	}
	return true
}
func (p *ExportTask) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ExpireTime, src) != 0 {
		return false
	}
	return true
}
func (p *ExportTask) Field5DeepEqual(src LogExportTaskStatus) bool {

	if p.TaskStatus != src {
		return false
	}
	return true
}

type DescribeErrLogsExportTasksReq struct {
	RegionId     string       `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,3,required" frugal:"3,required,InstanceType" json:"InstanceType"`
	PageNumber   int32        `thrift:"PageNumber,4,required" frugal:"4,required,i32" json:"PageNumber"`
	PageSize     int32        `thrift:"PageSize,5,required" frugal:"5,required,i32" json:"PageSize"`
}

func NewDescribeErrLogsExportTasksReq() *DescribeErrLogsExportTasksReq {
	return &DescribeErrLogsExportTasksReq{}
}

func (p *DescribeErrLogsExportTasksReq) InitDefault() {
}

func (p *DescribeErrLogsExportTasksReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *DescribeErrLogsExportTasksReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeErrLogsExportTasksReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeErrLogsExportTasksReq) GetPageNumber() (v int32) {
	return p.PageNumber
}

func (p *DescribeErrLogsExportTasksReq) GetPageSize() (v int32) {
	return p.PageSize
}
func (p *DescribeErrLogsExportTasksReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeErrLogsExportTasksReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeErrLogsExportTasksReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeErrLogsExportTasksReq) SetPageNumber(val int32) {
	p.PageNumber = val
}
func (p *DescribeErrLogsExportTasksReq) SetPageSize(val int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeErrLogsExportTasksReq = map[int16]string{
	1: "RegionId",
	2: "InstanceId",
	3: "InstanceType",
	4: "PageNumber",
	5: "PageSize",
}

func (p *DescribeErrLogsExportTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsExportTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetPageNumber bool = false
	var issetPageSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPageNumber {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrLogsExportTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrLogsExportTasksReq[fieldId]))
}

func (p *DescribeErrLogsExportTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeErrLogsExportTasksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeErrLogsExportTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeErrLogsExportTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeErrLogsExportTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeErrLogsExportTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsExportTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrLogsExportTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrLogsExportTasksReq(%+v)", *p)

}

func (p *DescribeErrLogsExportTasksReq) DeepEqual(ano *DescribeErrLogsExportTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeErrLogsExportTasksReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrLogsExportTasksReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeErrLogsExportTasksReq) Field3DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeErrLogsExportTasksReq) Field4DeepEqual(src int32) bool {

	if p.PageNumber != src {
		return false
	}
	return true
}
func (p *DescribeErrLogsExportTasksReq) Field5DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}

type DescribeErrLogsExportTasksResp struct {
	TaskInfos []*TaskInfo `thrift:"TaskInfos,1,required" frugal:"1,required,list<TaskInfo>" json:"TaskInfos"`
	Total     int32       `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeErrLogsExportTasksResp() *DescribeErrLogsExportTasksResp {
	return &DescribeErrLogsExportTasksResp{}
}

func (p *DescribeErrLogsExportTasksResp) InitDefault() {
}

func (p *DescribeErrLogsExportTasksResp) GetTaskInfos() (v []*TaskInfo) {
	return p.TaskInfos
}

func (p *DescribeErrLogsExportTasksResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeErrLogsExportTasksResp) SetTaskInfos(val []*TaskInfo) {
	p.TaskInfos = val
}
func (p *DescribeErrLogsExportTasksResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeErrLogsExportTasksResp = map[int16]string{
	1: "TaskInfos",
	2: "Total",
}

func (p *DescribeErrLogsExportTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsExportTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskInfos bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeErrLogsExportTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeErrLogsExportTasksResp[fieldId]))
}

func (p *DescribeErrLogsExportTasksResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TaskInfo, 0, size)
	values := make([]TaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskInfos = _field
	return nil
}
func (p *DescribeErrLogsExportTasksResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeErrLogsExportTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeErrLogsExportTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeErrLogsExportTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskInfos)); err != nil {
		return err
	}
	for _, v := range p.TaskInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeErrLogsExportTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeErrLogsExportTasksResp(%+v)", *p)

}

func (p *DescribeErrLogsExportTasksResp) DeepEqual(ano *DescribeErrLogsExportTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskInfos) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeErrLogsExportTasksResp) Field1DeepEqual(src []*TaskInfo) bool {

	if len(p.TaskInfos) != len(src) {
		return false
	}
	for i, v := range p.TaskInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeErrLogsExportTasksResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type CreateLogExportTaskReq struct {
	DbName         string          `thrift:"DbName,1,required" frugal:"1,required,string" json:"DbName"`
	NodeID         string          `thrift:"NodeID,2,required" frugal:"2,required,string" json:"NodeID"`
	RegionId       string          `thrift:"RegionId,4,required" frugal:"4,required,string" json:"RegionId"`
	VRegion        string          `thrift:"VRegion,5,required" frugal:"5,required,string" json:"VRegion"`
	LogType        InnerRdsLogType `thrift:"LogType,6,required" frugal:"6,required,InnerRdsLogType" json:"LogType"`
	FileName       string          `thrift:"FileName,7,required" frugal:"7,required,string" json:"FileName"`
	MaxGenFileSize *int64          `thrift:"MaxGenFileSize,8,optional" frugal:"8,optional,i64" json:"MaxGenFileSize,omitempty"`
}

func NewCreateLogExportTaskReq() *CreateLogExportTaskReq {
	return &CreateLogExportTaskReq{}
}

func (p *CreateLogExportTaskReq) InitDefault() {
}

func (p *CreateLogExportTaskReq) GetDbName() (v string) {
	return p.DbName
}

func (p *CreateLogExportTaskReq) GetNodeID() (v string) {
	return p.NodeID
}

func (p *CreateLogExportTaskReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *CreateLogExportTaskReq) GetVRegion() (v string) {
	return p.VRegion
}

func (p *CreateLogExportTaskReq) GetLogType() (v InnerRdsLogType) {
	return p.LogType
}

func (p *CreateLogExportTaskReq) GetFileName() (v string) {
	return p.FileName
}

var CreateLogExportTaskReq_MaxGenFileSize_DEFAULT int64

func (p *CreateLogExportTaskReq) GetMaxGenFileSize() (v int64) {
	if !p.IsSetMaxGenFileSize() {
		return CreateLogExportTaskReq_MaxGenFileSize_DEFAULT
	}
	return *p.MaxGenFileSize
}
func (p *CreateLogExportTaskReq) SetDbName(val string) {
	p.DbName = val
}
func (p *CreateLogExportTaskReq) SetNodeID(val string) {
	p.NodeID = val
}
func (p *CreateLogExportTaskReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *CreateLogExportTaskReq) SetVRegion(val string) {
	p.VRegion = val
}
func (p *CreateLogExportTaskReq) SetLogType(val InnerRdsLogType) {
	p.LogType = val
}
func (p *CreateLogExportTaskReq) SetFileName(val string) {
	p.FileName = val
}
func (p *CreateLogExportTaskReq) SetMaxGenFileSize(val *int64) {
	p.MaxGenFileSize = val
}

var fieldIDToName_CreateLogExportTaskReq = map[int16]string{
	1: "DbName",
	2: "NodeID",
	4: "RegionId",
	5: "VRegion",
	6: "LogType",
	7: "FileName",
	8: "MaxGenFileSize",
}

func (p *CreateLogExportTaskReq) IsSetMaxGenFileSize() bool {
	return p.MaxGenFileSize != nil
}

func (p *CreateLogExportTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateLogExportTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDbName bool = false
	var issetNodeID bool = false
	var issetRegionId bool = false
	var issetVRegion bool = false
	var issetLogType bool = false
	var issetFileName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetVRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDbName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetVRegion {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetLogType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetFileName {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateLogExportTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateLogExportTaskReq[fieldId]))
}

func (p *CreateLogExportTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeID = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VRegion = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field InnerRdsLogType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InnerRdsLogType(v)
	}
	p.LogType = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileName = _field
	return nil
}
func (p *CreateLogExportTaskReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxGenFileSize = _field
	return nil
}

func (p *CreateLogExportTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateLogExportTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateLogExportTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VRegion", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VRegion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LogType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileName", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxGenFileSize() {
		if err = oprot.WriteFieldBegin("MaxGenFileSize", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxGenFileSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateLogExportTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateLogExportTaskReq(%+v)", *p)

}

func (p *CreateLogExportTaskReq) DeepEqual(ano *CreateLogExportTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeID) {
		return false
	}
	if !p.Field4DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field5DeepEqual(ano.VRegion) {
		return false
	}
	if !p.Field6DeepEqual(ano.LogType) {
		return false
	}
	if !p.Field7DeepEqual(ano.FileName) {
		return false
	}
	if !p.Field8DeepEqual(ano.MaxGenFileSize) {
		return false
	}
	return true
}

func (p *CreateLogExportTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NodeID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.VRegion, src) != 0 {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field6DeepEqual(src InnerRdsLogType) bool {

	if p.LogType != src {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field7DeepEqual(src string) bool {

	if strings.Compare(p.FileName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateLogExportTaskReq) Field8DeepEqual(src *int64) bool {

	if p.MaxGenFileSize == src {
		return true
	} else if p.MaxGenFileSize == nil || src == nil {
		return false
	}
	if *p.MaxGenFileSize != *src {
		return false
	}
	return true
}

type CreateLogExportTaskResp struct {
	TaskId string `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
}

func NewCreateLogExportTaskResp() *CreateLogExportTaskResp {
	return &CreateLogExportTaskResp{}
}

func (p *CreateLogExportTaskResp) InitDefault() {
}

func (p *CreateLogExportTaskResp) GetTaskId() (v string) {
	return p.TaskId
}
func (p *CreateLogExportTaskResp) SetTaskId(val string) {
	p.TaskId = val
}

var fieldIDToName_CreateLogExportTaskResp = map[int16]string{
	1: "TaskId",
}

func (p *CreateLogExportTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateLogExportTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateLogExportTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateLogExportTaskResp[fieldId]))
}

func (p *CreateLogExportTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}

func (p *CreateLogExportTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateLogExportTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateLogExportTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateLogExportTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateLogExportTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateLogExportTaskResp(%+v)", *p)

}

func (p *CreateLogExportTaskResp) DeepEqual(ano *CreateLogExportTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *CreateLogExportTaskResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}

type CreateErrLogsExportTaskReq struct {
	TaskName     string             `thrift:"TaskName,1,required" frugal:"1,required,string" json:"TaskName"`
	RegionId     string             `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	VRegion      *string            `thrift:"VRegion,3,optional" frugal:"3,optional,string" json:"VRegion,omitempty"`
	InstanceId   string             `thrift:"InstanceId,4,required" frugal:"4,required,string" json:"InstanceId"`
	InstanceType InstanceType       `thrift:"InstanceType,5,required" frugal:"5,required,InstanceType" json:"InstanceType"`
	DataFormat   DataFormat         `thrift:"DataFormat,6,required" frugal:"6,required,DataFormat" json:"DataFormat"`
	StartTime    int64              `thrift:"StartTime,7,required" frugal:"7,required,i64" json:"StartTime"`
	EndTime      int64              `thrift:"EndTime,8,required" frugal:"8,required,i64" json:"EndTime"`
	Compression  Compression        `thrift:"Compression,9,required" frugal:"9,required,Compression" json:"Compression"`
	SearchParam  *ErrLogSearchParam `thrift:"SearchParam,10,optional" frugal:"10,optional,ErrLogSearchParam" json:"SearchParam,omitempty"`
}

func NewCreateErrLogsExportTaskReq() *CreateErrLogsExportTaskReq {
	return &CreateErrLogsExportTaskReq{}
}

func (p *CreateErrLogsExportTaskReq) InitDefault() {
}

func (p *CreateErrLogsExportTaskReq) GetTaskName() (v string) {
	return p.TaskName
}

func (p *CreateErrLogsExportTaskReq) GetRegionId() (v string) {
	return p.RegionId
}

var CreateErrLogsExportTaskReq_VRegion_DEFAULT string

func (p *CreateErrLogsExportTaskReq) GetVRegion() (v string) {
	if !p.IsSetVRegion() {
		return CreateErrLogsExportTaskReq_VRegion_DEFAULT
	}
	return *p.VRegion
}

func (p *CreateErrLogsExportTaskReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateErrLogsExportTaskReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateErrLogsExportTaskReq) GetDataFormat() (v DataFormat) {
	return p.DataFormat
}

func (p *CreateErrLogsExportTaskReq) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *CreateErrLogsExportTaskReq) GetEndTime() (v int64) {
	return p.EndTime
}

func (p *CreateErrLogsExportTaskReq) GetCompression() (v Compression) {
	return p.Compression
}

var CreateErrLogsExportTaskReq_SearchParam_DEFAULT *ErrLogSearchParam

func (p *CreateErrLogsExportTaskReq) GetSearchParam() (v *ErrLogSearchParam) {
	if !p.IsSetSearchParam() {
		return CreateErrLogsExportTaskReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}
func (p *CreateErrLogsExportTaskReq) SetTaskName(val string) {
	p.TaskName = val
}
func (p *CreateErrLogsExportTaskReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *CreateErrLogsExportTaskReq) SetVRegion(val *string) {
	p.VRegion = val
}
func (p *CreateErrLogsExportTaskReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateErrLogsExportTaskReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateErrLogsExportTaskReq) SetDataFormat(val DataFormat) {
	p.DataFormat = val
}
func (p *CreateErrLogsExportTaskReq) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *CreateErrLogsExportTaskReq) SetEndTime(val int64) {
	p.EndTime = val
}
func (p *CreateErrLogsExportTaskReq) SetCompression(val Compression) {
	p.Compression = val
}
func (p *CreateErrLogsExportTaskReq) SetSearchParam(val *ErrLogSearchParam) {
	p.SearchParam = val
}

var fieldIDToName_CreateErrLogsExportTaskReq = map[int16]string{
	1:  "TaskName",
	2:  "RegionId",
	3:  "VRegion",
	4:  "InstanceId",
	5:  "InstanceType",
	6:  "DataFormat",
	7:  "StartTime",
	8:  "EndTime",
	9:  "Compression",
	10: "SearchParam",
}

func (p *CreateErrLogsExportTaskReq) IsSetVRegion() bool {
	return p.VRegion != nil
}

func (p *CreateErrLogsExportTaskReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *CreateErrLogsExportTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateErrLogsExportTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskName bool = false
	var issetRegionId bool = false
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetDataFormat bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetCompression bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataFormat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCompression = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDataFormat {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetCompression {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateErrLogsExportTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateErrLogsExportTaskReq[fieldId]))
}

func (p *CreateErrLogsExportTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskName = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VRegion = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field DataFormat
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DataFormat(v)
	}
	p.DataFormat = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField9(iprot thrift.TProtocol) error {

	var _field Compression
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = Compression(v)
	}
	p.Compression = _field
	return nil
}
func (p *CreateErrLogsExportTaskReq) ReadField10(iprot thrift.TProtocol) error {
	_field := NewErrLogSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}

func (p *CreateErrLogsExportTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateErrLogsExportTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateErrLogsExportTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVRegion() {
		if err = oprot.WriteFieldBegin("VRegion", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VRegion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataFormat", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DataFormat)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Compression", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Compression)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateErrLogsExportTaskReq(%+v)", *p)

}

func (p *CreateErrLogsExportTaskReq) DeepEqual(ano *CreateErrLogsExportTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskName) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.VRegion) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.DataFormat) {
		return false
	}
	if !p.Field7DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.Compression) {
		return false
	}
	if !p.Field10DeepEqual(ano.SearchParam) {
		return false
	}
	return true
}

func (p *CreateErrLogsExportTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field3DeepEqual(src *string) bool {

	if p.VRegion == src {
		return true
	} else if p.VRegion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.VRegion, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field5DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field6DeepEqual(src DataFormat) bool {

	if p.DataFormat != src {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field7DeepEqual(src int64) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field8DeepEqual(src int64) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field9DeepEqual(src Compression) bool {

	if p.Compression != src {
		return false
	}
	return true
}
func (p *CreateErrLogsExportTaskReq) Field10DeepEqual(src *ErrLogSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}

type CreateErrLogsExportTaskResp struct {
	TaskId string `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
}

func NewCreateErrLogsExportTaskResp() *CreateErrLogsExportTaskResp {
	return &CreateErrLogsExportTaskResp{}
}

func (p *CreateErrLogsExportTaskResp) InitDefault() {
}

func (p *CreateErrLogsExportTaskResp) GetTaskId() (v string) {
	return p.TaskId
}
func (p *CreateErrLogsExportTaskResp) SetTaskId(val string) {
	p.TaskId = val
}

var fieldIDToName_CreateErrLogsExportTaskResp = map[int16]string{
	1: "TaskId",
}

func (p *CreateErrLogsExportTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateErrLogsExportTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateErrLogsExportTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateErrLogsExportTaskResp[fieldId]))
}

func (p *CreateErrLogsExportTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}

func (p *CreateErrLogsExportTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateErrLogsExportTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateErrLogsExportTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateErrLogsExportTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateErrLogsExportTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateErrLogsExportTaskResp(%+v)", *p)

}

func (p *CreateErrLogsExportTaskResp) DeepEqual(ano *CreateErrLogsExportTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *CreateErrLogsExportTaskResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}

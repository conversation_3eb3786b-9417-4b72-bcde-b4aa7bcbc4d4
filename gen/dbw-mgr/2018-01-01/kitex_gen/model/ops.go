// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeOpsListReq struct {
}

func NewDescribeOpsListReq() *DescribeOpsListReq {
	return &DescribeOpsListReq{}
}

func (p *DescribeOpsListReq) InitDefault() {
}

var fieldIDToName_DescribeOpsListReq = map[int16]string{}

func (p *DescribeOpsListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeOpsListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeOpsListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeOpsListReq")

	if err = oprot.WriteStructBegin("DescribeOpsListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeOpsListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeOpsListReq(%+v)", *p)

}

func (p *DescribeOpsListReq) DeepEqual(ano *DescribeOpsListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeOpsListResp struct {
	OpsInterfaces []*OpsInterface `thrift:"OpsInterfaces,1,required" frugal:"1,required,list<OpsInterface>" json:"OpsInterfaces"`
}

func NewDescribeOpsListResp() *DescribeOpsListResp {
	return &DescribeOpsListResp{}
}

func (p *DescribeOpsListResp) InitDefault() {
}

func (p *DescribeOpsListResp) GetOpsInterfaces() (v []*OpsInterface) {
	return p.OpsInterfaces
}
func (p *DescribeOpsListResp) SetOpsInterfaces(val []*OpsInterface) {
	p.OpsInterfaces = val
}

var fieldIDToName_DescribeOpsListResp = map[int16]string{
	1: "OpsInterfaces",
}

func (p *DescribeOpsListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeOpsListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOpsInterfaces bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOpsInterfaces = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOpsInterfaces {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeOpsListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeOpsListResp[fieldId]))
}

func (p *DescribeOpsListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*OpsInterface, 0, size)
	values := make([]OpsInterface, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OpsInterfaces = _field
	return nil
}

func (p *DescribeOpsListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeOpsListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeOpsListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeOpsListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OpsInterfaces", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OpsInterfaces)); err != nil {
		return err
	}
	for _, v := range p.OpsInterfaces {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeOpsListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeOpsListResp(%+v)", *p)

}

func (p *DescribeOpsListResp) DeepEqual(ano *DescribeOpsListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.OpsInterfaces) {
		return false
	}
	return true
}

func (p *DescribeOpsListResp) Field1DeepEqual(src []*OpsInterface) bool {

	if len(p.OpsInterfaces) != len(src) {
		return false
	}
	for i, v := range p.OpsInterfaces {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type OpsInterface struct {
	ActionName string `thrift:"ActionName,1,required" frugal:"1,required,string" json:"ActionName"`
	ActionDemo string `thrift:"ActionDemo,2,required" frugal:"2,required,string" json:"ActionDemo"`
	ActionDesc string `thrift:"ActionDesc,3,required" frugal:"3,required,string" json:"ActionDesc"`
}

func NewOpsInterface() *OpsInterface {
	return &OpsInterface{}
}

func (p *OpsInterface) InitDefault() {
}

func (p *OpsInterface) GetActionName() (v string) {
	return p.ActionName
}

func (p *OpsInterface) GetActionDemo() (v string) {
	return p.ActionDemo
}

func (p *OpsInterface) GetActionDesc() (v string) {
	return p.ActionDesc
}
func (p *OpsInterface) SetActionName(val string) {
	p.ActionName = val
}
func (p *OpsInterface) SetActionDemo(val string) {
	p.ActionDemo = val
}
func (p *OpsInterface) SetActionDesc(val string) {
	p.ActionDesc = val
}

var fieldIDToName_OpsInterface = map[int16]string{
	1: "ActionName",
	2: "ActionDemo",
	3: "ActionDesc",
}

func (p *OpsInterface) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OpsInterface")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetActionName bool = false
	var issetActionDemo bool = false
	var issetActionDesc bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetActionName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetActionDemo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetActionDesc = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetActionName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetActionDemo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetActionDesc {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpsInterface[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_OpsInterface[fieldId]))
}

func (p *OpsInterface) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ActionName = _field
	return nil
}
func (p *OpsInterface) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ActionDemo = _field
	return nil
}
func (p *OpsInterface) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ActionDesc = _field
	return nil
}

func (p *OpsInterface) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OpsInterface")

	var fieldId int16
	if err = oprot.WriteStructBegin("OpsInterface"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpsInterface) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ActionName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ActionName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpsInterface) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ActionDemo", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ActionDemo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OpsInterface) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ActionDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ActionDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *OpsInterface) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpsInterface(%+v)", *p)

}

func (p *OpsInterface) DeepEqual(ano *OpsInterface) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ActionName) {
		return false
	}
	if !p.Field2DeepEqual(ano.ActionDemo) {
		return false
	}
	if !p.Field3DeepEqual(ano.ActionDesc) {
		return false
	}
	return true
}

func (p *OpsInterface) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ActionName, src) != 0 {
		return false
	}
	return true
}
func (p *OpsInterface) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ActionDemo, src) != 0 {
		return false
	}
	return true
}
func (p *OpsInterface) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ActionDesc, src) != 0 {
		return false
	}
	return true
}

type BatchOpenInstanceFunctionReq struct {
	TenantId                string                   `thrift:"TenantId,1,required" frugal:"1,required,string" json:"TenantId"`
	IsSameConfig            bool                     `thrift:"IsSameConfig,2,required" frugal:"2,required,bool" json:"IsSameConfig"`
	CreateFullSqlReqs       []*CreateFullSqlOrderReq `thrift:"CreateFullSqlReqs,3,optional" frugal:"3,optional,list<CreateFullSqlOrderReq>" json:"CreateFullSqlReqs,omitempty"`
	FollowInstanceIDs       []string                 `thrift:"FollowInstanceIDs,4,optional" frugal:"4,optional,list<string>" json:"FollowInstanceIDs,omitempty"`
	InstanceType            *DSType                  `thrift:"InstanceType,5,optional" frugal:"5,optional,DSType" json:"InstanceType,omitempty"`
	Ttl                     *int32                   `thrift:"Ttl,6,optional" frugal:"6,optional,i32" json:"Ttl,omitempty"`
	LabelType               *LabelType               `thrift:"LabelType,7,optional" frugal:"7,optional,LabelType" json:"LabelType,omitempty"`
	DefaultCloseType        *CloseType               `thrift:"DefaultCloseType,8,optional" frugal:"8,optional,CloseType" json:"DefaultCloseType,omitempty"`
	LogProductType          *LogProductType          `thrift:"LogProductType,9,optional" frugal:"9,optional,LogProductType" json:"LogProductType,omitempty"`
	CustomStorageSqlMethods []FullSqlMethod          `thrift:"CustomStorageSqlMethods,10,optional" frugal:"10,optional,list<FullSqlMethod>" json:"CustomStorageSqlMethods,omitempty"`
	Tags                    []*TagObject             `thrift:"Tags,11,optional" frugal:"11,optional,list<TagObject>" json:"Tags,omitempty"`
}

func NewBatchOpenInstanceFunctionReq() *BatchOpenInstanceFunctionReq {
	return &BatchOpenInstanceFunctionReq{}
}

func (p *BatchOpenInstanceFunctionReq) InitDefault() {
}

func (p *BatchOpenInstanceFunctionReq) GetTenantId() (v string) {
	return p.TenantId
}

func (p *BatchOpenInstanceFunctionReq) GetIsSameConfig() (v bool) {
	return p.IsSameConfig
}

var BatchOpenInstanceFunctionReq_CreateFullSqlReqs_DEFAULT []*CreateFullSqlOrderReq

func (p *BatchOpenInstanceFunctionReq) GetCreateFullSqlReqs() (v []*CreateFullSqlOrderReq) {
	if !p.IsSetCreateFullSqlReqs() {
		return BatchOpenInstanceFunctionReq_CreateFullSqlReqs_DEFAULT
	}
	return p.CreateFullSqlReqs
}

var BatchOpenInstanceFunctionReq_FollowInstanceIDs_DEFAULT []string

func (p *BatchOpenInstanceFunctionReq) GetFollowInstanceIDs() (v []string) {
	if !p.IsSetFollowInstanceIDs() {
		return BatchOpenInstanceFunctionReq_FollowInstanceIDs_DEFAULT
	}
	return p.FollowInstanceIDs
}

var BatchOpenInstanceFunctionReq_InstanceType_DEFAULT DSType

func (p *BatchOpenInstanceFunctionReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return BatchOpenInstanceFunctionReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var BatchOpenInstanceFunctionReq_Ttl_DEFAULT int32

func (p *BatchOpenInstanceFunctionReq) GetTtl() (v int32) {
	if !p.IsSetTtl() {
		return BatchOpenInstanceFunctionReq_Ttl_DEFAULT
	}
	return *p.Ttl
}

var BatchOpenInstanceFunctionReq_LabelType_DEFAULT LabelType

func (p *BatchOpenInstanceFunctionReq) GetLabelType() (v LabelType) {
	if !p.IsSetLabelType() {
		return BatchOpenInstanceFunctionReq_LabelType_DEFAULT
	}
	return *p.LabelType
}

var BatchOpenInstanceFunctionReq_DefaultCloseType_DEFAULT CloseType

func (p *BatchOpenInstanceFunctionReq) GetDefaultCloseType() (v CloseType) {
	if !p.IsSetDefaultCloseType() {
		return BatchOpenInstanceFunctionReq_DefaultCloseType_DEFAULT
	}
	return *p.DefaultCloseType
}

var BatchOpenInstanceFunctionReq_LogProductType_DEFAULT LogProductType

func (p *BatchOpenInstanceFunctionReq) GetLogProductType() (v LogProductType) {
	if !p.IsSetLogProductType() {
		return BatchOpenInstanceFunctionReq_LogProductType_DEFAULT
	}
	return *p.LogProductType
}

var BatchOpenInstanceFunctionReq_CustomStorageSqlMethods_DEFAULT []FullSqlMethod

func (p *BatchOpenInstanceFunctionReq) GetCustomStorageSqlMethods() (v []FullSqlMethod) {
	if !p.IsSetCustomStorageSqlMethods() {
		return BatchOpenInstanceFunctionReq_CustomStorageSqlMethods_DEFAULT
	}
	return p.CustomStorageSqlMethods
}

var BatchOpenInstanceFunctionReq_Tags_DEFAULT []*TagObject

func (p *BatchOpenInstanceFunctionReq) GetTags() (v []*TagObject) {
	if !p.IsSetTags() {
		return BatchOpenInstanceFunctionReq_Tags_DEFAULT
	}
	return p.Tags
}
func (p *BatchOpenInstanceFunctionReq) SetTenantId(val string) {
	p.TenantId = val
}
func (p *BatchOpenInstanceFunctionReq) SetIsSameConfig(val bool) {
	p.IsSameConfig = val
}
func (p *BatchOpenInstanceFunctionReq) SetCreateFullSqlReqs(val []*CreateFullSqlOrderReq) {
	p.CreateFullSqlReqs = val
}
func (p *BatchOpenInstanceFunctionReq) SetFollowInstanceIDs(val []string) {
	p.FollowInstanceIDs = val
}
func (p *BatchOpenInstanceFunctionReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *BatchOpenInstanceFunctionReq) SetTtl(val *int32) {
	p.Ttl = val
}
func (p *BatchOpenInstanceFunctionReq) SetLabelType(val *LabelType) {
	p.LabelType = val
}
func (p *BatchOpenInstanceFunctionReq) SetDefaultCloseType(val *CloseType) {
	p.DefaultCloseType = val
}
func (p *BatchOpenInstanceFunctionReq) SetLogProductType(val *LogProductType) {
	p.LogProductType = val
}
func (p *BatchOpenInstanceFunctionReq) SetCustomStorageSqlMethods(val []FullSqlMethod) {
	p.CustomStorageSqlMethods = val
}
func (p *BatchOpenInstanceFunctionReq) SetTags(val []*TagObject) {
	p.Tags = val
}

var fieldIDToName_BatchOpenInstanceFunctionReq = map[int16]string{
	1:  "TenantId",
	2:  "IsSameConfig",
	3:  "CreateFullSqlReqs",
	4:  "FollowInstanceIDs",
	5:  "InstanceType",
	6:  "Ttl",
	7:  "LabelType",
	8:  "DefaultCloseType",
	9:  "LogProductType",
	10: "CustomStorageSqlMethods",
	11: "Tags",
}

func (p *BatchOpenInstanceFunctionReq) IsSetCreateFullSqlReqs() bool {
	return p.CreateFullSqlReqs != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetFollowInstanceIDs() bool {
	return p.FollowInstanceIDs != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetTtl() bool {
	return p.Ttl != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetLabelType() bool {
	return p.LabelType != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetDefaultCloseType() bool {
	return p.DefaultCloseType != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetLogProductType() bool {
	return p.LogProductType != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetCustomStorageSqlMethods() bool {
	return p.CustomStorageSqlMethods != nil
}

func (p *BatchOpenInstanceFunctionReq) IsSetTags() bool {
	return p.Tags != nil
}

func (p *BatchOpenInstanceFunctionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchOpenInstanceFunctionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTenantId bool = false
	var issetIsSameConfig bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsSameConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTenantId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIsSameConfig {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BatchOpenInstanceFunctionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BatchOpenInstanceFunctionReq[fieldId]))
}

func (p *BatchOpenInstanceFunctionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsSameConfig = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CreateFullSqlOrderReq, 0, size)
	values := make([]CreateFullSqlOrderReq, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CreateFullSqlReqs = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FollowInstanceIDs = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Ttl = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *LabelType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LabelType(v)
		_field = &tmp
	}
	p.LabelType = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *CloseType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CloseType(v)
		_field = &tmp
	}
	p.DefaultCloseType = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *LogProductType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LogProductType(v)
		_field = &tmp
	}
	p.LogProductType = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]FullSqlMethod, 0, size)
	for i := 0; i < size; i++ {

		var _elem FullSqlMethod
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = FullSqlMethod(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CustomStorageSqlMethods = _field
	return nil
}
func (p *BatchOpenInstanceFunctionReq) ReadField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TagObject, 0, size)
	values := make([]TagObject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}

func (p *BatchOpenInstanceFunctionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchOpenInstanceFunctionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("BatchOpenInstanceFunctionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsSameConfig", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsSameConfig); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateFullSqlReqs() {
		if err = oprot.WriteFieldBegin("CreateFullSqlReqs", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreateFullSqlReqs)); err != nil {
			return err
		}
		for _, v := range p.CreateFullSqlReqs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFollowInstanceIDs() {
		if err = oprot.WriteFieldBegin("FollowInstanceIDs", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FollowInstanceIDs)); err != nil {
			return err
		}
		for _, v := range p.FollowInstanceIDs {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTtl() {
		if err = oprot.WriteFieldBegin("Ttl", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Ttl); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetLabelType() {
		if err = oprot.WriteFieldBegin("LabelType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LabelType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDefaultCloseType() {
		if err = oprot.WriteFieldBegin("DefaultCloseType", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DefaultCloseType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogProductType() {
		if err = oprot.WriteFieldBegin("LogProductType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LogProductType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCustomStorageSqlMethods() {
		if err = oprot.WriteFieldBegin("CustomStorageSqlMethods", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CustomStorageSqlMethods)); err != nil {
			return err
		}
		for _, v := range p.CustomStorageSqlMethods {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchOpenInstanceFunctionReq(%+v)", *p)

}

func (p *BatchOpenInstanceFunctionReq) DeepEqual(ano *BatchOpenInstanceFunctionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field2DeepEqual(ano.IsSameConfig) {
		return false
	}
	if !p.Field3DeepEqual(ano.CreateFullSqlReqs) {
		return false
	}
	if !p.Field4DeepEqual(ano.FollowInstanceIDs) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field6DeepEqual(ano.Ttl) {
		return false
	}
	if !p.Field7DeepEqual(ano.LabelType) {
		return false
	}
	if !p.Field8DeepEqual(ano.DefaultCloseType) {
		return false
	}
	if !p.Field9DeepEqual(ano.LogProductType) {
		return false
	}
	if !p.Field10DeepEqual(ano.CustomStorageSqlMethods) {
		return false
	}
	if !p.Field11DeepEqual(ano.Tags) {
		return false
	}
	return true
}

func (p *BatchOpenInstanceFunctionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field2DeepEqual(src bool) bool {

	if p.IsSameConfig != src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field3DeepEqual(src []*CreateFullSqlOrderReq) bool {

	if len(p.CreateFullSqlReqs) != len(src) {
		return false
	}
	for i, v := range p.CreateFullSqlReqs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field4DeepEqual(src []string) bool {

	if len(p.FollowInstanceIDs) != len(src) {
		return false
	}
	for i, v := range p.FollowInstanceIDs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field5DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field6DeepEqual(src *int32) bool {

	if p.Ttl == src {
		return true
	} else if p.Ttl == nil || src == nil {
		return false
	}
	if *p.Ttl != *src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field7DeepEqual(src *LabelType) bool {

	if p.LabelType == src {
		return true
	} else if p.LabelType == nil || src == nil {
		return false
	}
	if *p.LabelType != *src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field8DeepEqual(src *CloseType) bool {

	if p.DefaultCloseType == src {
		return true
	} else if p.DefaultCloseType == nil || src == nil {
		return false
	}
	if *p.DefaultCloseType != *src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field9DeepEqual(src *LogProductType) bool {

	if p.LogProductType == src {
		return true
	} else if p.LogProductType == nil || src == nil {
		return false
	}
	if *p.LogProductType != *src {
		return false
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field10DeepEqual(src []FullSqlMethod) bool {

	if len(p.CustomStorageSqlMethods) != len(src) {
		return false
	}
	for i, v := range p.CustomStorageSqlMethods {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *BatchOpenInstanceFunctionReq) Field11DeepEqual(src []*TagObject) bool {

	if len(p.Tags) != len(src) {
		return false
	}
	for i, v := range p.Tags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type BatchOpenInstanceFunctionResp struct {
	CreateFullSqlOpenInfos []*CreateFullSqlOpenInfo `thrift:"CreateFullSqlOpenInfos,1,required" frugal:"1,required,list<CreateFullSqlOpenInfo>" json:"CreateFullSqlOpenInfos"`
}

func NewBatchOpenInstanceFunctionResp() *BatchOpenInstanceFunctionResp {
	return &BatchOpenInstanceFunctionResp{}
}

func (p *BatchOpenInstanceFunctionResp) InitDefault() {
}

func (p *BatchOpenInstanceFunctionResp) GetCreateFullSqlOpenInfos() (v []*CreateFullSqlOpenInfo) {
	return p.CreateFullSqlOpenInfos
}
func (p *BatchOpenInstanceFunctionResp) SetCreateFullSqlOpenInfos(val []*CreateFullSqlOpenInfo) {
	p.CreateFullSqlOpenInfos = val
}

var fieldIDToName_BatchOpenInstanceFunctionResp = map[int16]string{
	1: "CreateFullSqlOpenInfos",
}

func (p *BatchOpenInstanceFunctionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchOpenInstanceFunctionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCreateFullSqlOpenInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateFullSqlOpenInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCreateFullSqlOpenInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BatchOpenInstanceFunctionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BatchOpenInstanceFunctionResp[fieldId]))
}

func (p *BatchOpenInstanceFunctionResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CreateFullSqlOpenInfo, 0, size)
	values := make([]CreateFullSqlOpenInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CreateFullSqlOpenInfos = _field
	return nil
}

func (p *BatchOpenInstanceFunctionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchOpenInstanceFunctionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("BatchOpenInstanceFunctionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateFullSqlOpenInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreateFullSqlOpenInfos)); err != nil {
		return err
	}
	for _, v := range p.CreateFullSqlOpenInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *BatchOpenInstanceFunctionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchOpenInstanceFunctionResp(%+v)", *p)

}

func (p *BatchOpenInstanceFunctionResp) DeepEqual(ano *BatchOpenInstanceFunctionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CreateFullSqlOpenInfos) {
		return false
	}
	return true
}

func (p *BatchOpenInstanceFunctionResp) Field1DeepEqual(src []*CreateFullSqlOpenInfo) bool {

	if len(p.CreateFullSqlOpenInfos) != len(src) {
		return false
	}
	for i, v := range p.CreateFullSqlOpenInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateFullSqlOpenInfo struct {
	PreCheckCreateFullSqlResp *PreCheckCreateFullSqlResp `thrift:"PreCheckCreateFullSqlResp,1,required" frugal:"1,required,PreCheckCreateFullSqlResp" json:"PreCheckCreateFullSqlResp"`
	FollowInstanceID          string                     `thrift:"FollowInstanceID,2,required" frugal:"2,required,string" json:"FollowInstanceID"`
	AuditServiceID            string                     `thrift:"AuditServiceID,3,required" frugal:"3,required,string" json:"AuditServiceID"`
	OrderID                   string                     `thrift:"OrderID,4,required" frugal:"4,required,string" json:"OrderID"`
	CreateOrderErrorMessage   string                     `thrift:"CreateOrderErrorMessage,5,required" frugal:"5,required,string" json:"CreateOrderErrorMessage"`
}

func NewCreateFullSqlOpenInfo() *CreateFullSqlOpenInfo {
	return &CreateFullSqlOpenInfo{}
}

func (p *CreateFullSqlOpenInfo) InitDefault() {
}

var CreateFullSqlOpenInfo_PreCheckCreateFullSqlResp_DEFAULT *PreCheckCreateFullSqlResp

func (p *CreateFullSqlOpenInfo) GetPreCheckCreateFullSqlResp() (v *PreCheckCreateFullSqlResp) {
	if !p.IsSetPreCheckCreateFullSqlResp() {
		return CreateFullSqlOpenInfo_PreCheckCreateFullSqlResp_DEFAULT
	}
	return p.PreCheckCreateFullSqlResp
}

func (p *CreateFullSqlOpenInfo) GetFollowInstanceID() (v string) {
	return p.FollowInstanceID
}

func (p *CreateFullSqlOpenInfo) GetAuditServiceID() (v string) {
	return p.AuditServiceID
}

func (p *CreateFullSqlOpenInfo) GetOrderID() (v string) {
	return p.OrderID
}

func (p *CreateFullSqlOpenInfo) GetCreateOrderErrorMessage() (v string) {
	return p.CreateOrderErrorMessage
}
func (p *CreateFullSqlOpenInfo) SetPreCheckCreateFullSqlResp(val *PreCheckCreateFullSqlResp) {
	p.PreCheckCreateFullSqlResp = val
}
func (p *CreateFullSqlOpenInfo) SetFollowInstanceID(val string) {
	p.FollowInstanceID = val
}
func (p *CreateFullSqlOpenInfo) SetAuditServiceID(val string) {
	p.AuditServiceID = val
}
func (p *CreateFullSqlOpenInfo) SetOrderID(val string) {
	p.OrderID = val
}
func (p *CreateFullSqlOpenInfo) SetCreateOrderErrorMessage(val string) {
	p.CreateOrderErrorMessage = val
}

var fieldIDToName_CreateFullSqlOpenInfo = map[int16]string{
	1: "PreCheckCreateFullSqlResp",
	2: "FollowInstanceID",
	3: "AuditServiceID",
	4: "OrderID",
	5: "CreateOrderErrorMessage",
}

func (p *CreateFullSqlOpenInfo) IsSetPreCheckCreateFullSqlResp() bool {
	return p.PreCheckCreateFullSqlResp != nil
}

func (p *CreateFullSqlOpenInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFullSqlOpenInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPreCheckCreateFullSqlResp bool = false
	var issetFollowInstanceID bool = false
	var issetAuditServiceID bool = false
	var issetOrderID bool = false
	var issetCreateOrderErrorMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetPreCheckCreateFullSqlResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetFollowInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAuditServiceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateOrderErrorMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPreCheckCreateFullSqlResp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetFollowInstanceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAuditServiceID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetOrderID {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateOrderErrorMessage {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateFullSqlOpenInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateFullSqlOpenInfo[fieldId]))
}

func (p *CreateFullSqlOpenInfo) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPreCheckCreateFullSqlResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PreCheckCreateFullSqlResp = _field
	return nil
}
func (p *CreateFullSqlOpenInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FollowInstanceID = _field
	return nil
}
func (p *CreateFullSqlOpenInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AuditServiceID = _field
	return nil
}
func (p *CreateFullSqlOpenInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *CreateFullSqlOpenInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateOrderErrorMessage = _field
	return nil
}

func (p *CreateFullSqlOpenInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateFullSqlOpenInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateFullSqlOpenInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PreCheckCreateFullSqlResp", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PreCheckCreateFullSqlResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FollowInstanceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FollowInstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AuditServiceID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AuditServiceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderID", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateOrderErrorMessage", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateOrderErrorMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateFullSqlOpenInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFullSqlOpenInfo(%+v)", *p)

}

func (p *CreateFullSqlOpenInfo) DeepEqual(ano *CreateFullSqlOpenInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PreCheckCreateFullSqlResp) {
		return false
	}
	if !p.Field2DeepEqual(ano.FollowInstanceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.AuditServiceID) {
		return false
	}
	if !p.Field4DeepEqual(ano.OrderID) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateOrderErrorMessage) {
		return false
	}
	return true
}

func (p *CreateFullSqlOpenInfo) Field1DeepEqual(src *PreCheckCreateFullSqlResp) bool {

	if !p.PreCheckCreateFullSqlResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateFullSqlOpenInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.FollowInstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFullSqlOpenInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AuditServiceID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFullSqlOpenInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.OrderID, src) != 0 {
		return false
	}
	return true
}
func (p *CreateFullSqlOpenInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CreateOrderErrorMessage, src) != 0 {
		return false
	}
	return true
}

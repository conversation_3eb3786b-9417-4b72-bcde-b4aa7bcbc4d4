// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SqlTaskStatus int64

const (
	SqlTaskStatus_Init     SqlTaskStatus = 0
	SqlTaskStatus_Pending  SqlTaskStatus = 1
	SqlTaskStatus_Precheck SqlTaskStatus = 2
	SqlTaskStatus_Running  SqlTaskStatus = 3
	SqlTaskStatus_Failed   SqlTaskStatus = 4
	SqlTaskStatus_Success  SqlTaskStatus = 5
	SqlTaskStatus_Pause    SqlTaskStatus = 6
	SqlTaskStatus_Stop     SqlTaskStatus = 7
)

func (p SqlTaskStatus) String() string {
	switch p {
	case SqlTaskStatus_Init:
		return "Init"
	case SqlTaskStatus_Pending:
		return "Pending"
	case SqlTaskStatus_Precheck:
		return "Precheck"
	case SqlTaskStatus_Running:
		return "Running"
	case SqlTaskStatus_Failed:
		return "Failed"
	case SqlTaskStatus_Success:
		return "Success"
	case SqlTaskStatus_Pause:
		return "Pause"
	case SqlTaskStatus_Stop:
		return "Stop"
	}
	return "<UNSET>"
}

func SqlTaskStatusFromString(s string) (SqlTaskStatus, error) {
	switch s {
	case "Init":
		return SqlTaskStatus_Init, nil
	case "Pending":
		return SqlTaskStatus_Pending, nil
	case "Precheck":
		return SqlTaskStatus_Precheck, nil
	case "Running":
		return SqlTaskStatus_Running, nil
	case "Failed":
		return SqlTaskStatus_Failed, nil
	case "Success":
		return SqlTaskStatus_Success, nil
	case "Pause":
		return SqlTaskStatus_Pause, nil
	case "Stop":
		return SqlTaskStatus_Stop, nil
	}
	return SqlTaskStatus(0), fmt.Errorf("not a valid SqlTaskStatus string")
}

func SqlTaskStatusPtr(v SqlTaskStatus) *SqlTaskStatus { return &v }

func (p SqlTaskStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlTaskStatus) UnmarshalText(text []byte) error {
	q, err := SqlTaskStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlTaskType int64

const (
	SqlTaskType_OnlineDDL   SqlTaskType = 0
	SqlTaskType_FreeLockDML SqlTaskType = 1
)

func (p SqlTaskType) String() string {
	switch p {
	case SqlTaskType_OnlineDDL:
		return "OnlineDDL"
	case SqlTaskType_FreeLockDML:
		return "FreeLockDML"
	}
	return "<UNSET>"
}

func SqlTaskTypeFromString(s string) (SqlTaskType, error) {
	switch s {
	case "OnlineDDL":
		return SqlTaskType_OnlineDDL, nil
	case "FreeLockDML":
		return SqlTaskType_FreeLockDML, nil
	}
	return SqlTaskType(0), fmt.Errorf("not a valid SqlTaskType string")
}

func SqlTaskTypePtr(v SqlTaskType) *SqlTaskType { return &v }

func (p SqlTaskType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlTaskType) UnmarshalText(text []byte) error {
	q, err := SqlTaskTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CreateSqlTaskReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType *DSType      `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
	TableName    *string      `thrift:"TableName,3,optional" frugal:"3,optional,string" json:"TableName,omitempty"`
	DBName       string       `thrift:"DBName,4,required" frugal:"4,required,string" json:"DBName"`
	ExecSQL      string       `thrift:"ExecSQL,5,required" frugal:"5,required,string" json:"ExecSQL"`
	UserName     *string      `thrift:"UserName,6,optional" frugal:"6,optional,string" json:"UserName,omitempty"`
	Password     *string      `thrift:"Password,7,optional" frugal:"7,optional,string" json:"Password,omitempty"`
	Comment      *string      `thrift:"Comment,8,optional" frugal:"8,optional,string" json:"Comment,omitempty"`
	SqlTaskType  *SqlTaskType `thrift:"SqlTaskType,9,optional" frugal:"9,optional,SqlTaskType" json:"SqlTaskType,omitempty"`
	SqlTaskInfo  *SqlTaskInfo `thrift:"SqlTaskInfo,10,optional" frugal:"10,optional,SqlTaskInfo" json:"SqlTaskInfo,omitempty"`
}

func NewCreateSqlTaskReq() *CreateSqlTaskReq {
	return &CreateSqlTaskReq{}
}

func (p *CreateSqlTaskReq) InitDefault() {
}

func (p *CreateSqlTaskReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateSqlTaskReq_InstanceType_DEFAULT DSType

func (p *CreateSqlTaskReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return CreateSqlTaskReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var CreateSqlTaskReq_TableName_DEFAULT string

func (p *CreateSqlTaskReq) GetTableName() (v string) {
	if !p.IsSetTableName() {
		return CreateSqlTaskReq_TableName_DEFAULT
	}
	return *p.TableName
}

func (p *CreateSqlTaskReq) GetDBName() (v string) {
	return p.DBName
}

func (p *CreateSqlTaskReq) GetExecSQL() (v string) {
	return p.ExecSQL
}

var CreateSqlTaskReq_UserName_DEFAULT string

func (p *CreateSqlTaskReq) GetUserName() (v string) {
	if !p.IsSetUserName() {
		return CreateSqlTaskReq_UserName_DEFAULT
	}
	return *p.UserName
}

var CreateSqlTaskReq_Password_DEFAULT string

func (p *CreateSqlTaskReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return CreateSqlTaskReq_Password_DEFAULT
	}
	return *p.Password
}

var CreateSqlTaskReq_Comment_DEFAULT string

func (p *CreateSqlTaskReq) GetComment() (v string) {
	if !p.IsSetComment() {
		return CreateSqlTaskReq_Comment_DEFAULT
	}
	return *p.Comment
}

var CreateSqlTaskReq_SqlTaskType_DEFAULT SqlTaskType

func (p *CreateSqlTaskReq) GetSqlTaskType() (v SqlTaskType) {
	if !p.IsSetSqlTaskType() {
		return CreateSqlTaskReq_SqlTaskType_DEFAULT
	}
	return *p.SqlTaskType
}

var CreateSqlTaskReq_SqlTaskInfo_DEFAULT *SqlTaskInfo

func (p *CreateSqlTaskReq) GetSqlTaskInfo() (v *SqlTaskInfo) {
	if !p.IsSetSqlTaskInfo() {
		return CreateSqlTaskReq_SqlTaskInfo_DEFAULT
	}
	return p.SqlTaskInfo
}
func (p *CreateSqlTaskReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateSqlTaskReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *CreateSqlTaskReq) SetTableName(val *string) {
	p.TableName = val
}
func (p *CreateSqlTaskReq) SetDBName(val string) {
	p.DBName = val
}
func (p *CreateSqlTaskReq) SetExecSQL(val string) {
	p.ExecSQL = val
}
func (p *CreateSqlTaskReq) SetUserName(val *string) {
	p.UserName = val
}
func (p *CreateSqlTaskReq) SetPassword(val *string) {
	p.Password = val
}
func (p *CreateSqlTaskReq) SetComment(val *string) {
	p.Comment = val
}
func (p *CreateSqlTaskReq) SetSqlTaskType(val *SqlTaskType) {
	p.SqlTaskType = val
}
func (p *CreateSqlTaskReq) SetSqlTaskInfo(val *SqlTaskInfo) {
	p.SqlTaskInfo = val
}

var fieldIDToName_CreateSqlTaskReq = map[int16]string{
	1:  "InstanceId",
	2:  "InstanceType",
	3:  "TableName",
	4:  "DBName",
	5:  "ExecSQL",
	6:  "UserName",
	7:  "Password",
	8:  "Comment",
	9:  "SqlTaskType",
	10: "SqlTaskInfo",
}

func (p *CreateSqlTaskReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *CreateSqlTaskReq) IsSetTableName() bool {
	return p.TableName != nil
}

func (p *CreateSqlTaskReq) IsSetUserName() bool {
	return p.UserName != nil
}

func (p *CreateSqlTaskReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *CreateSqlTaskReq) IsSetComment() bool {
	return p.Comment != nil
}

func (p *CreateSqlTaskReq) IsSetSqlTaskType() bool {
	return p.SqlTaskType != nil
}

func (p *CreateSqlTaskReq) IsSetSqlTaskInfo() bool {
	return p.SqlTaskInfo != nil
}

func (p *CreateSqlTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDBName bool = false
	var issetExecSQL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecSQL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetExecSQL {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSqlTaskReq[fieldId]))
}

func (p *CreateSqlTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TableName = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecSQL = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserName = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *SqlTaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlTaskType(v)
		_field = &tmp
	}
	p.SqlTaskType = _field
	return nil
}
func (p *CreateSqlTaskReq) ReadField10(iprot thrift.TProtocol) error {
	_field := NewSqlTaskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SqlTaskInfo = _field
	return nil
}

func (p *CreateSqlTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSqlTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableName() {
		if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TableName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecSQL", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecSQL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserName() {
		if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskType() {
		if err = oprot.WriteFieldBegin("SqlTaskType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SqlTaskType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateSqlTaskReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskInfo() {
		if err = oprot.WriteFieldBegin("SqlTaskInfo", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SqlTaskInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateSqlTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlTaskReq(%+v)", *p)

}

func (p *CreateSqlTaskReq) DeepEqual(ano *CreateSqlTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field4DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExecSQL) {
		return false
	}
	if !p.Field6DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field7DeepEqual(ano.Password) {
		return false
	}
	if !p.Field8DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field9DeepEqual(ano.SqlTaskType) {
		return false
	}
	if !p.Field10DeepEqual(ano.SqlTaskInfo) {
		return false
	}
	return true
}

func (p *CreateSqlTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field3DeepEqual(src *string) bool {

	if p.TableName == src {
		return true
	} else if p.TableName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TableName, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ExecSQL, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field6DeepEqual(src *string) bool {

	if p.UserName == src {
		return true
	} else if p.UserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserName, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field7DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field8DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field9DeepEqual(src *SqlTaskType) bool {

	if p.SqlTaskType == src {
		return true
	} else if p.SqlTaskType == nil || src == nil {
		return false
	}
	if *p.SqlTaskType != *src {
		return false
	}
	return true
}
func (p *CreateSqlTaskReq) Field10DeepEqual(src *SqlTaskInfo) bool {

	if !p.SqlTaskInfo.DeepEqual(src) {
		return false
	}
	return true
}

type SqlTaskInfo struct {
	ExecuteNow            *bool   `thrift:"ExecuteNow,1,optional" frugal:"1,optional,bool" json:"ExecuteNow,omitempty"`
	ScheduleTime          *string `thrift:"ScheduleTime,2,optional" frugal:"2,optional,string" json:"ScheduleTime,omitempty"`
	IsCreateShardingTable *bool   `thrift:"IsCreateShardingTable,3,optional" frugal:"3,optional,bool" json:"IsCreateShardingTable,omitempty"`
	ShardingKeyName       *string `thrift:"ShardingKeyName,4,optional" frugal:"4,optional,string" json:"ShardingKeyName,omitempty"`
	ShardingKeyType       *string `thrift:"ShardingKeyType,5,optional" frugal:"5,optional,string" json:"ShardingKeyType,omitempty"`
	AccountName           *string `thrift:"AccountName,6,optional" frugal:"6,optional,string" json:"AccountName,omitempty"`
	KillLongTxn           *bool   `thrift:"KillLongTxn,7,optional" frugal:"7,optional,bool" json:"KillLongTxn,omitempty"`
	RenameDisallowWindow  *string `thrift:"RenameDisallowWindow,8,optional" frugal:"8,optional,string" json:"RenameDisallowWindow,omitempty"`
	RplDelayCheckRule     *string `thrift:"RplDelayCheckRule,9,optional" frugal:"9,optional,string" json:"RplDelayCheckRule,omitempty"`
	BatchSize             *int32  `thrift:"BatchSize,10,optional" frugal:"10,optional,i32" json:"BatchSize,omitempty"`
	SleepTimeMs           *int32  `thrift:"SleepTimeMs,11,optional" frugal:"11,optional,i32" json:"SleepTimeMs,omitempty"`
	IsEnableDelayCheck    *bool   `thrift:"IsEnableDelayCheck,12,optional" frugal:"12,optional,bool" json:"IsEnableDelayCheck,omitempty"`
	ReplicaDelaySeconds   *int32  `thrift:"ReplicaDelaySeconds,13,optional" frugal:"13,optional,i32" json:"ReplicaDelaySeconds,omitempty"`
	DBBatchNum            *int32  `thrift:"DBBatchNum,14,optional" frugal:"14,optional,i32" json:"DBBatchNum,omitempty"`
	ExecutableStartTime   *int64  `thrift:"ExecutableStartTime,15,optional" frugal:"15,optional,i64" json:"ExecutableStartTime,omitempty"`
	ExecutableEndTime     *int64  `thrift:"ExecutableEndTime,16,optional" frugal:"16,optional,i64" json:"ExecutableEndTime,omitempty"`
}

func NewSqlTaskInfo() *SqlTaskInfo {
	return &SqlTaskInfo{}
}

func (p *SqlTaskInfo) InitDefault() {
}

var SqlTaskInfo_ExecuteNow_DEFAULT bool

func (p *SqlTaskInfo) GetExecuteNow() (v bool) {
	if !p.IsSetExecuteNow() {
		return SqlTaskInfo_ExecuteNow_DEFAULT
	}
	return *p.ExecuteNow
}

var SqlTaskInfo_ScheduleTime_DEFAULT string

func (p *SqlTaskInfo) GetScheduleTime() (v string) {
	if !p.IsSetScheduleTime() {
		return SqlTaskInfo_ScheduleTime_DEFAULT
	}
	return *p.ScheduleTime
}

var SqlTaskInfo_IsCreateShardingTable_DEFAULT bool

func (p *SqlTaskInfo) GetIsCreateShardingTable() (v bool) {
	if !p.IsSetIsCreateShardingTable() {
		return SqlTaskInfo_IsCreateShardingTable_DEFAULT
	}
	return *p.IsCreateShardingTable
}

var SqlTaskInfo_ShardingKeyName_DEFAULT string

func (p *SqlTaskInfo) GetShardingKeyName() (v string) {
	if !p.IsSetShardingKeyName() {
		return SqlTaskInfo_ShardingKeyName_DEFAULT
	}
	return *p.ShardingKeyName
}

var SqlTaskInfo_ShardingKeyType_DEFAULT string

func (p *SqlTaskInfo) GetShardingKeyType() (v string) {
	if !p.IsSetShardingKeyType() {
		return SqlTaskInfo_ShardingKeyType_DEFAULT
	}
	return *p.ShardingKeyType
}

var SqlTaskInfo_AccountName_DEFAULT string

func (p *SqlTaskInfo) GetAccountName() (v string) {
	if !p.IsSetAccountName() {
		return SqlTaskInfo_AccountName_DEFAULT
	}
	return *p.AccountName
}

var SqlTaskInfo_KillLongTxn_DEFAULT bool

func (p *SqlTaskInfo) GetKillLongTxn() (v bool) {
	if !p.IsSetKillLongTxn() {
		return SqlTaskInfo_KillLongTxn_DEFAULT
	}
	return *p.KillLongTxn
}

var SqlTaskInfo_RenameDisallowWindow_DEFAULT string

func (p *SqlTaskInfo) GetRenameDisallowWindow() (v string) {
	if !p.IsSetRenameDisallowWindow() {
		return SqlTaskInfo_RenameDisallowWindow_DEFAULT
	}
	return *p.RenameDisallowWindow
}

var SqlTaskInfo_RplDelayCheckRule_DEFAULT string

func (p *SqlTaskInfo) GetRplDelayCheckRule() (v string) {
	if !p.IsSetRplDelayCheckRule() {
		return SqlTaskInfo_RplDelayCheckRule_DEFAULT
	}
	return *p.RplDelayCheckRule
}

var SqlTaskInfo_BatchSize_DEFAULT int32

func (p *SqlTaskInfo) GetBatchSize() (v int32) {
	if !p.IsSetBatchSize() {
		return SqlTaskInfo_BatchSize_DEFAULT
	}
	return *p.BatchSize
}

var SqlTaskInfo_SleepTimeMs_DEFAULT int32

func (p *SqlTaskInfo) GetSleepTimeMs() (v int32) {
	if !p.IsSetSleepTimeMs() {
		return SqlTaskInfo_SleepTimeMs_DEFAULT
	}
	return *p.SleepTimeMs
}

var SqlTaskInfo_IsEnableDelayCheck_DEFAULT bool

func (p *SqlTaskInfo) GetIsEnableDelayCheck() (v bool) {
	if !p.IsSetIsEnableDelayCheck() {
		return SqlTaskInfo_IsEnableDelayCheck_DEFAULT
	}
	return *p.IsEnableDelayCheck
}

var SqlTaskInfo_ReplicaDelaySeconds_DEFAULT int32

func (p *SqlTaskInfo) GetReplicaDelaySeconds() (v int32) {
	if !p.IsSetReplicaDelaySeconds() {
		return SqlTaskInfo_ReplicaDelaySeconds_DEFAULT
	}
	return *p.ReplicaDelaySeconds
}

var SqlTaskInfo_DBBatchNum_DEFAULT int32

func (p *SqlTaskInfo) GetDBBatchNum() (v int32) {
	if !p.IsSetDBBatchNum() {
		return SqlTaskInfo_DBBatchNum_DEFAULT
	}
	return *p.DBBatchNum
}

var SqlTaskInfo_ExecutableStartTime_DEFAULT int64

func (p *SqlTaskInfo) GetExecutableStartTime() (v int64) {
	if !p.IsSetExecutableStartTime() {
		return SqlTaskInfo_ExecutableStartTime_DEFAULT
	}
	return *p.ExecutableStartTime
}

var SqlTaskInfo_ExecutableEndTime_DEFAULT int64

func (p *SqlTaskInfo) GetExecutableEndTime() (v int64) {
	if !p.IsSetExecutableEndTime() {
		return SqlTaskInfo_ExecutableEndTime_DEFAULT
	}
	return *p.ExecutableEndTime
}
func (p *SqlTaskInfo) SetExecuteNow(val *bool) {
	p.ExecuteNow = val
}
func (p *SqlTaskInfo) SetScheduleTime(val *string) {
	p.ScheduleTime = val
}
func (p *SqlTaskInfo) SetIsCreateShardingTable(val *bool) {
	p.IsCreateShardingTable = val
}
func (p *SqlTaskInfo) SetShardingKeyName(val *string) {
	p.ShardingKeyName = val
}
func (p *SqlTaskInfo) SetShardingKeyType(val *string) {
	p.ShardingKeyType = val
}
func (p *SqlTaskInfo) SetAccountName(val *string) {
	p.AccountName = val
}
func (p *SqlTaskInfo) SetKillLongTxn(val *bool) {
	p.KillLongTxn = val
}
func (p *SqlTaskInfo) SetRenameDisallowWindow(val *string) {
	p.RenameDisallowWindow = val
}
func (p *SqlTaskInfo) SetRplDelayCheckRule(val *string) {
	p.RplDelayCheckRule = val
}
func (p *SqlTaskInfo) SetBatchSize(val *int32) {
	p.BatchSize = val
}
func (p *SqlTaskInfo) SetSleepTimeMs(val *int32) {
	p.SleepTimeMs = val
}
func (p *SqlTaskInfo) SetIsEnableDelayCheck(val *bool) {
	p.IsEnableDelayCheck = val
}
func (p *SqlTaskInfo) SetReplicaDelaySeconds(val *int32) {
	p.ReplicaDelaySeconds = val
}
func (p *SqlTaskInfo) SetDBBatchNum(val *int32) {
	p.DBBatchNum = val
}
func (p *SqlTaskInfo) SetExecutableStartTime(val *int64) {
	p.ExecutableStartTime = val
}
func (p *SqlTaskInfo) SetExecutableEndTime(val *int64) {
	p.ExecutableEndTime = val
}

var fieldIDToName_SqlTaskInfo = map[int16]string{
	1:  "ExecuteNow",
	2:  "ScheduleTime",
	3:  "IsCreateShardingTable",
	4:  "ShardingKeyName",
	5:  "ShardingKeyType",
	6:  "AccountName",
	7:  "KillLongTxn",
	8:  "RenameDisallowWindow",
	9:  "RplDelayCheckRule",
	10: "BatchSize",
	11: "SleepTimeMs",
	12: "IsEnableDelayCheck",
	13: "ReplicaDelaySeconds",
	14: "DBBatchNum",
	15: "ExecutableStartTime",
	16: "ExecutableEndTime",
}

func (p *SqlTaskInfo) IsSetExecuteNow() bool {
	return p.ExecuteNow != nil
}

func (p *SqlTaskInfo) IsSetScheduleTime() bool {
	return p.ScheduleTime != nil
}

func (p *SqlTaskInfo) IsSetIsCreateShardingTable() bool {
	return p.IsCreateShardingTable != nil
}

func (p *SqlTaskInfo) IsSetShardingKeyName() bool {
	return p.ShardingKeyName != nil
}

func (p *SqlTaskInfo) IsSetShardingKeyType() bool {
	return p.ShardingKeyType != nil
}

func (p *SqlTaskInfo) IsSetAccountName() bool {
	return p.AccountName != nil
}

func (p *SqlTaskInfo) IsSetKillLongTxn() bool {
	return p.KillLongTxn != nil
}

func (p *SqlTaskInfo) IsSetRenameDisallowWindow() bool {
	return p.RenameDisallowWindow != nil
}

func (p *SqlTaskInfo) IsSetRplDelayCheckRule() bool {
	return p.RplDelayCheckRule != nil
}

func (p *SqlTaskInfo) IsSetBatchSize() bool {
	return p.BatchSize != nil
}

func (p *SqlTaskInfo) IsSetSleepTimeMs() bool {
	return p.SleepTimeMs != nil
}

func (p *SqlTaskInfo) IsSetIsEnableDelayCheck() bool {
	return p.IsEnableDelayCheck != nil
}

func (p *SqlTaskInfo) IsSetReplicaDelaySeconds() bool {
	return p.ReplicaDelaySeconds != nil
}

func (p *SqlTaskInfo) IsSetDBBatchNum() bool {
	return p.DBBatchNum != nil
}

func (p *SqlTaskInfo) IsSetExecutableStartTime() bool {
	return p.ExecutableStartTime != nil
}

func (p *SqlTaskInfo) IsSetExecutableEndTime() bool {
	return p.ExecutableEndTime != nil
}

func (p *SqlTaskInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlTaskInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlTaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlTaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteNow = _field
	return nil
}
func (p *SqlTaskInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ScheduleTime = _field
	return nil
}
func (p *SqlTaskInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsCreateShardingTable = _field
	return nil
}
func (p *SqlTaskInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingKeyName = _field
	return nil
}
func (p *SqlTaskInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShardingKeyType = _field
	return nil
}
func (p *SqlTaskInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountName = _field
	return nil
}
func (p *SqlTaskInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KillLongTxn = _field
	return nil
}
func (p *SqlTaskInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RenameDisallowWindow = _field
	return nil
}
func (p *SqlTaskInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RplDelayCheckRule = _field
	return nil
}
func (p *SqlTaskInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BatchSize = _field
	return nil
}
func (p *SqlTaskInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SleepTimeMs = _field
	return nil
}
func (p *SqlTaskInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsEnableDelayCheck = _field
	return nil
}
func (p *SqlTaskInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReplicaDelaySeconds = _field
	return nil
}
func (p *SqlTaskInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBBatchNum = _field
	return nil
}
func (p *SqlTaskInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecutableStartTime = _field
	return nil
}
func (p *SqlTaskInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecutableEndTime = _field
	return nil
}

func (p *SqlTaskInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlTaskInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlTaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlTaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteNow() {
		if err = oprot.WriteFieldBegin("ExecuteNow", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ExecuteNow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetScheduleTime() {
		if err = oprot.WriteFieldBegin("ScheduleTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ScheduleTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsCreateShardingTable() {
		if err = oprot.WriteFieldBegin("IsCreateShardingTable", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsCreateShardingTable); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingKeyName() {
		if err = oprot.WriteFieldBegin("ShardingKeyName", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingKeyName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetShardingKeyType() {
		if err = oprot.WriteFieldBegin("ShardingKeyType", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShardingKeyType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountName() {
		if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetKillLongTxn() {
		if err = oprot.WriteFieldBegin("KillLongTxn", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.KillLongTxn); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRenameDisallowWindow() {
		if err = oprot.WriteFieldBegin("RenameDisallowWindow", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RenameDisallowWindow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRplDelayCheckRule() {
		if err = oprot.WriteFieldBegin("RplDelayCheckRule", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RplDelayCheckRule); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchSize() {
		if err = oprot.WriteFieldBegin("BatchSize", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BatchSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetSleepTimeMs() {
		if err = oprot.WriteFieldBegin("SleepTimeMs", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SleepTimeMs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsEnableDelayCheck() {
		if err = oprot.WriteFieldBegin("IsEnableDelayCheck", thrift.BOOL, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsEnableDelayCheck); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetReplicaDelaySeconds() {
		if err = oprot.WriteFieldBegin("ReplicaDelaySeconds", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ReplicaDelaySeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBBatchNum() {
		if err = oprot.WriteFieldBegin("DBBatchNum", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.DBBatchNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecutableStartTime() {
		if err = oprot.WriteFieldBegin("ExecutableStartTime", thrift.I64, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExecutableStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *SqlTaskInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecutableEndTime() {
		if err = oprot.WriteFieldBegin("ExecutableEndTime", thrift.I64, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExecutableEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *SqlTaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlTaskInfo(%+v)", *p)

}

func (p *SqlTaskInfo) DeepEqual(ano *SqlTaskInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExecuteNow) {
		return false
	}
	if !p.Field2DeepEqual(ano.ScheduleTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsCreateShardingTable) {
		return false
	}
	if !p.Field4DeepEqual(ano.ShardingKeyName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ShardingKeyType) {
		return false
	}
	if !p.Field6DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field7DeepEqual(ano.KillLongTxn) {
		return false
	}
	if !p.Field8DeepEqual(ano.RenameDisallowWindow) {
		return false
	}
	if !p.Field9DeepEqual(ano.RplDelayCheckRule) {
		return false
	}
	if !p.Field10DeepEqual(ano.BatchSize) {
		return false
	}
	if !p.Field11DeepEqual(ano.SleepTimeMs) {
		return false
	}
	if !p.Field12DeepEqual(ano.IsEnableDelayCheck) {
		return false
	}
	if !p.Field13DeepEqual(ano.ReplicaDelaySeconds) {
		return false
	}
	if !p.Field14DeepEqual(ano.DBBatchNum) {
		return false
	}
	if !p.Field15DeepEqual(ano.ExecutableStartTime) {
		return false
	}
	if !p.Field16DeepEqual(ano.ExecutableEndTime) {
		return false
	}
	return true
}

func (p *SqlTaskInfo) Field1DeepEqual(src *bool) bool {

	if p.ExecuteNow == src {
		return true
	} else if p.ExecuteNow == nil || src == nil {
		return false
	}
	if *p.ExecuteNow != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field2DeepEqual(src *string) bool {

	if p.ScheduleTime == src {
		return true
	} else if p.ScheduleTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ScheduleTime, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field3DeepEqual(src *bool) bool {

	if p.IsCreateShardingTable == src {
		return true
	} else if p.IsCreateShardingTable == nil || src == nil {
		return false
	}
	if *p.IsCreateShardingTable != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field4DeepEqual(src *string) bool {

	if p.ShardingKeyName == src {
		return true
	} else if p.ShardingKeyName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingKeyName, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field5DeepEqual(src *string) bool {

	if p.ShardingKeyType == src {
		return true
	} else if p.ShardingKeyType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ShardingKeyType, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field6DeepEqual(src *string) bool {

	if p.AccountName == src {
		return true
	} else if p.AccountName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountName, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field7DeepEqual(src *bool) bool {

	if p.KillLongTxn == src {
		return true
	} else if p.KillLongTxn == nil || src == nil {
		return false
	}
	if *p.KillLongTxn != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field8DeepEqual(src *string) bool {

	if p.RenameDisallowWindow == src {
		return true
	} else if p.RenameDisallowWindow == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RenameDisallowWindow, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field9DeepEqual(src *string) bool {

	if p.RplDelayCheckRule == src {
		return true
	} else if p.RplDelayCheckRule == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RplDelayCheckRule, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field10DeepEqual(src *int32) bool {

	if p.BatchSize == src {
		return true
	} else if p.BatchSize == nil || src == nil {
		return false
	}
	if *p.BatchSize != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field11DeepEqual(src *int32) bool {

	if p.SleepTimeMs == src {
		return true
	} else if p.SleepTimeMs == nil || src == nil {
		return false
	}
	if *p.SleepTimeMs != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field12DeepEqual(src *bool) bool {

	if p.IsEnableDelayCheck == src {
		return true
	} else if p.IsEnableDelayCheck == nil || src == nil {
		return false
	}
	if *p.IsEnableDelayCheck != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field13DeepEqual(src *int32) bool {

	if p.ReplicaDelaySeconds == src {
		return true
	} else if p.ReplicaDelaySeconds == nil || src == nil {
		return false
	}
	if *p.ReplicaDelaySeconds != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field14DeepEqual(src *int32) bool {

	if p.DBBatchNum == src {
		return true
	} else if p.DBBatchNum == nil || src == nil {
		return false
	}
	if *p.DBBatchNum != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field15DeepEqual(src *int64) bool {

	if p.ExecutableStartTime == src {
		return true
	} else if p.ExecutableStartTime == nil || src == nil {
		return false
	}
	if *p.ExecutableStartTime != *src {
		return false
	}
	return true
}
func (p *SqlTaskInfo) Field16DeepEqual(src *int64) bool {

	if p.ExecutableEndTime == src {
		return true
	} else if p.ExecutableEndTime == nil || src == nil {
		return false
	}
	if *p.ExecutableEndTime != *src {
		return false
	}
	return true
}

type CreateSqlTaskResp struct {
	SqlTaskId string `thrift:"SqlTaskId,1,required" frugal:"1,required,string" json:"SqlTaskId"`
}

func NewCreateSqlTaskResp() *CreateSqlTaskResp {
	return &CreateSqlTaskResp{}
}

func (p *CreateSqlTaskResp) InitDefault() {
}

func (p *CreateSqlTaskResp) GetSqlTaskId() (v string) {
	return p.SqlTaskId
}
func (p *CreateSqlTaskResp) SetSqlTaskId(val string) {
	p.SqlTaskId = val
}

var fieldIDToName_CreateSqlTaskResp = map[int16]string{
	1: "SqlTaskId",
}

func (p *CreateSqlTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSqlTaskResp[fieldId]))
}

func (p *CreateSqlTaskResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlTaskId = _field
	return nil
}

func (p *CreateSqlTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSqlTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSqlTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlTaskResp(%+v)", *p)

}

func (p *CreateSqlTaskResp) DeepEqual(ano *CreateSqlTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlTaskId) {
		return false
	}
	return true
}

func (p *CreateSqlTaskResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlTaskId, src) != 0 {
		return false
	}
	return true
}

type DescribeSqlTasksReq struct {
	InstanceId      *string        `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	SqlTaskId       *string        `thrift:"SqlTaskId,2,optional" frugal:"2,optional,string" json:"SqlTaskId,omitempty"`
	InstanceType    *DSType        `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	SqlTaskStatus   *SqlTaskStatus `thrift:"SqlTaskStatus,4,optional" frugal:"4,optional,SqlTaskStatus" json:"SqlTaskStatus,omitempty"`
	CreateTimeStart *int32         `thrift:"CreateTimeStart,5,optional" frugal:"5,optional,i32" json:"CreateTimeStart,omitempty"`
	CreateTimeEnd   *int32         `thrift:"CreateTimeEnd,6,optional" frugal:"6,optional,i32" json:"CreateTimeEnd,omitempty"`
	PageNumber      *int32         `thrift:"PageNumber,7,optional" frugal:"7,optional,i32" json:"PageNumber,omitempty"`
	PageSize        *int32         `thrift:"PageSize,8,optional" frugal:"8,optional,i32" json:"PageSize,omitempty"`
	SqlTaskType     *SqlTaskType   `thrift:"SqlTaskType,9,optional" frugal:"9,optional,SqlTaskType" json:"SqlTaskType,omitempty"`
}

func NewDescribeSqlTasksReq() *DescribeSqlTasksReq {
	return &DescribeSqlTasksReq{}
}

func (p *DescribeSqlTasksReq) InitDefault() {
}

var DescribeSqlTasksReq_InstanceId_DEFAULT string

func (p *DescribeSqlTasksReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeSqlTasksReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeSqlTasksReq_SqlTaskId_DEFAULT string

func (p *DescribeSqlTasksReq) GetSqlTaskId() (v string) {
	if !p.IsSetSqlTaskId() {
		return DescribeSqlTasksReq_SqlTaskId_DEFAULT
	}
	return *p.SqlTaskId
}

var DescribeSqlTasksReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlTasksReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlTasksReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSqlTasksReq_SqlTaskStatus_DEFAULT SqlTaskStatus

func (p *DescribeSqlTasksReq) GetSqlTaskStatus() (v SqlTaskStatus) {
	if !p.IsSetSqlTaskStatus() {
		return DescribeSqlTasksReq_SqlTaskStatus_DEFAULT
	}
	return *p.SqlTaskStatus
}

var DescribeSqlTasksReq_CreateTimeStart_DEFAULT int32

func (p *DescribeSqlTasksReq) GetCreateTimeStart() (v int32) {
	if !p.IsSetCreateTimeStart() {
		return DescribeSqlTasksReq_CreateTimeStart_DEFAULT
	}
	return *p.CreateTimeStart
}

var DescribeSqlTasksReq_CreateTimeEnd_DEFAULT int32

func (p *DescribeSqlTasksReq) GetCreateTimeEnd() (v int32) {
	if !p.IsSetCreateTimeEnd() {
		return DescribeSqlTasksReq_CreateTimeEnd_DEFAULT
	}
	return *p.CreateTimeEnd
}

var DescribeSqlTasksReq_PageNumber_DEFAULT int32

func (p *DescribeSqlTasksReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSqlTasksReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSqlTasksReq_PageSize_DEFAULT int32

func (p *DescribeSqlTasksReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSqlTasksReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSqlTasksReq_SqlTaskType_DEFAULT SqlTaskType

func (p *DescribeSqlTasksReq) GetSqlTaskType() (v SqlTaskType) {
	if !p.IsSetSqlTaskType() {
		return DescribeSqlTasksReq_SqlTaskType_DEFAULT
	}
	return *p.SqlTaskType
}
func (p *DescribeSqlTasksReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeSqlTasksReq) SetSqlTaskId(val *string) {
	p.SqlTaskId = val
}
func (p *DescribeSqlTasksReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSqlTasksReq) SetSqlTaskStatus(val *SqlTaskStatus) {
	p.SqlTaskStatus = val
}
func (p *DescribeSqlTasksReq) SetCreateTimeStart(val *int32) {
	p.CreateTimeStart = val
}
func (p *DescribeSqlTasksReq) SetCreateTimeEnd(val *int32) {
	p.CreateTimeEnd = val
}
func (p *DescribeSqlTasksReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSqlTasksReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSqlTasksReq) SetSqlTaskType(val *SqlTaskType) {
	p.SqlTaskType = val
}

var fieldIDToName_DescribeSqlTasksReq = map[int16]string{
	1: "InstanceId",
	2: "SqlTaskId",
	3: "InstanceType",
	4: "SqlTaskStatus",
	5: "CreateTimeStart",
	6: "CreateTimeEnd",
	7: "PageNumber",
	8: "PageSize",
	9: "SqlTaskType",
}

func (p *DescribeSqlTasksReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeSqlTasksReq) IsSetSqlTaskId() bool {
	return p.SqlTaskId != nil
}

func (p *DescribeSqlTasksReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlTasksReq) IsSetSqlTaskStatus() bool {
	return p.SqlTaskStatus != nil
}

func (p *DescribeSqlTasksReq) IsSetCreateTimeStart() bool {
	return p.CreateTimeStart != nil
}

func (p *DescribeSqlTasksReq) IsSetCreateTimeEnd() bool {
	return p.CreateTimeEnd != nil
}

func (p *DescribeSqlTasksReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSqlTasksReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSqlTasksReq) IsSetSqlTaskType() bool {
	return p.SqlTaskType != nil
}

func (p *DescribeSqlTasksReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTasksReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTasksReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlTasksReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlTaskId = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SqlTaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlTaskStatus(v)
		_field = &tmp
	}
	p.SqlTaskStatus = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTimeStart = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTimeEnd = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSqlTasksReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *SqlTaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlTaskType(v)
		_field = &tmp
	}
	p.SqlTaskType = _field
	return nil
}

func (p *DescribeSqlTasksReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTasksReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTasksReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskId() {
		if err = oprot.WriteFieldBegin("SqlTaskId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlTaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskStatus() {
		if err = oprot.WriteFieldBegin("SqlTaskStatus", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SqlTaskStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeStart() {
		if err = oprot.WriteFieldBegin("CreateTimeStart", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.CreateTimeStart); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeEnd() {
		if err = oprot.WriteFieldBegin("CreateTimeEnd", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.CreateTimeEnd); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskType() {
		if err = oprot.WriteFieldBegin("SqlTaskType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SqlTaskType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSqlTasksReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTasksReq(%+v)", *p)

}

func (p *DescribeSqlTasksReq) DeepEqual(ano *DescribeSqlTasksReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlTaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.SqlTaskStatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateTimeStart) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTimeEnd) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field8DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field9DeepEqual(ano.SqlTaskType) {
		return false
	}
	return true
}

func (p *DescribeSqlTasksReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field2DeepEqual(src *string) bool {

	if p.SqlTaskId == src {
		return true
	} else if p.SqlTaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlTaskId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field4DeepEqual(src *SqlTaskStatus) bool {

	if p.SqlTaskStatus == src {
		return true
	} else if p.SqlTaskStatus == nil || src == nil {
		return false
	}
	if *p.SqlTaskStatus != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field5DeepEqual(src *int32) bool {

	if p.CreateTimeStart == src {
		return true
	} else if p.CreateTimeStart == nil || src == nil {
		return false
	}
	if *p.CreateTimeStart != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field6DeepEqual(src *int32) bool {

	if p.CreateTimeEnd == src {
		return true
	} else if p.CreateTimeEnd == nil || src == nil {
		return false
	}
	if *p.CreateTimeEnd != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field7DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field8DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksReq) Field9DeepEqual(src *SqlTaskType) bool {

	if p.SqlTaskType == src {
		return true
	} else if p.SqlTaskType == nil || src == nil {
		return false
	}
	if *p.SqlTaskType != *src {
		return false
	}
	return true
}

type DescribeSqlTasksResp struct {
	Total    int32      `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	SqlTasks []*SqlTask `thrift:"SqlTasks,2,required" frugal:"2,required,list<SqlTask>" json:"SqlTasks"`
}

func NewDescribeSqlTasksResp() *DescribeSqlTasksResp {
	return &DescribeSqlTasksResp{}
}

func (p *DescribeSqlTasksResp) InitDefault() {
}

func (p *DescribeSqlTasksResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeSqlTasksResp) GetSqlTasks() (v []*SqlTask) {
	return p.SqlTasks
}
func (p *DescribeSqlTasksResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeSqlTasksResp) SetSqlTasks(val []*SqlTask) {
	p.SqlTasks = val
}

var fieldIDToName_DescribeSqlTasksResp = map[int16]string{
	1: "Total",
	2: "SqlTasks",
}

func (p *DescribeSqlTasksResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTasksResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetSqlTasks bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlTasks = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSqlTasks {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTasksResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlTasksResp[fieldId]))
}

func (p *DescribeSqlTasksResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeSqlTasksResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SqlTask, 0, size)
	values := make([]SqlTask, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlTasks = _field
	return nil
}

func (p *DescribeSqlTasksResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTasksResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTasksResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTasksResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTasksResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTasks", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SqlTasks)); err != nil {
		return err
	}
	for _, v := range p.SqlTasks {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlTasksResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTasksResp(%+v)", *p)

}

func (p *DescribeSqlTasksResp) DeepEqual(ano *DescribeSqlTasksResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlTasks) {
		return false
	}
	return true
}

func (p *DescribeSqlTasksResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeSqlTasksResp) Field2DeepEqual(src []*SqlTask) bool {

	if len(p.SqlTasks) != len(src) {
		return false
	}
	for i, v := range p.SqlTasks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeSqlTaskReq struct {
	SqlTaskId    string       `thrift:"SqlTaskId,1,required" frugal:"1,required,string" json:"SqlTaskId"`
	InstanceType *DSType      `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
	SqlTaskType  *SqlTaskType `thrift:"SqlTaskType,3,optional" frugal:"3,optional,SqlTaskType" json:"SqlTaskType,omitempty"`
}

func NewDescribeSqlTaskReq() *DescribeSqlTaskReq {
	return &DescribeSqlTaskReq{}
}

func (p *DescribeSqlTaskReq) InitDefault() {
}

func (p *DescribeSqlTaskReq) GetSqlTaskId() (v string) {
	return p.SqlTaskId
}

var DescribeSqlTaskReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlTaskReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlTaskReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSqlTaskReq_SqlTaskType_DEFAULT SqlTaskType

func (p *DescribeSqlTaskReq) GetSqlTaskType() (v SqlTaskType) {
	if !p.IsSetSqlTaskType() {
		return DescribeSqlTaskReq_SqlTaskType_DEFAULT
	}
	return *p.SqlTaskType
}
func (p *DescribeSqlTaskReq) SetSqlTaskId(val string) {
	p.SqlTaskId = val
}
func (p *DescribeSqlTaskReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSqlTaskReq) SetSqlTaskType(val *SqlTaskType) {
	p.SqlTaskType = val
}

var fieldIDToName_DescribeSqlTaskReq = map[int16]string{
	1: "SqlTaskId",
	2: "InstanceType",
	3: "SqlTaskType",
}

func (p *DescribeSqlTaskReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlTaskReq) IsSetSqlTaskType() bool {
	return p.SqlTaskType != nil
}

func (p *DescribeSqlTaskReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTaskReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlTaskReq[fieldId]))
}

func (p *DescribeSqlTaskReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlTaskId = _field
	return nil
}
func (p *DescribeSqlTaskReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSqlTaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *SqlTaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlTaskType(v)
		_field = &tmp
	}
	p.SqlTaskType = _field
	return nil
}

func (p *DescribeSqlTaskReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTaskReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlTaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlTaskType() {
		if err = oprot.WriteFieldBegin("SqlTaskType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SqlTaskType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlTaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTaskReq(%+v)", *p)

}

func (p *DescribeSqlTaskReq) DeepEqual(ano *DescribeSqlTaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlTaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlTaskType) {
		return false
	}
	return true
}

func (p *DescribeSqlTaskReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlTaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlTaskReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlTaskReq) Field3DeepEqual(src *SqlTaskType) bool {

	if p.SqlTaskType == src {
		return true
	} else if p.SqlTaskType == nil || src == nil {
		return false
	}
	if *p.SqlTaskType != *src {
		return false
	}
	return true
}

type DescribeSqlTaskResp struct {
	SqlTask *SqlTask `thrift:"SqlTask,1,required" frugal:"1,required,SqlTask" json:"SqlTask"`
}

func NewDescribeSqlTaskResp() *DescribeSqlTaskResp {
	return &DescribeSqlTaskResp{}
}

func (p *DescribeSqlTaskResp) InitDefault() {
}

var DescribeSqlTaskResp_SqlTask_DEFAULT *SqlTask

func (p *DescribeSqlTaskResp) GetSqlTask() (v *SqlTask) {
	if !p.IsSetSqlTask() {
		return DescribeSqlTaskResp_SqlTask_DEFAULT
	}
	return p.SqlTask
}
func (p *DescribeSqlTaskResp) SetSqlTask(val *SqlTask) {
	p.SqlTask = val
}

var fieldIDToName_DescribeSqlTaskResp = map[int16]string{
	1: "SqlTask",
}

func (p *DescribeSqlTaskResp) IsSetSqlTask() bool {
	return p.SqlTask != nil
}

func (p *DescribeSqlTaskResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTaskResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlTask bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlTask = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlTask {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTaskResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlTaskResp[fieldId]))
}

func (p *DescribeSqlTaskResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSqlTask()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SqlTask = _field
	return nil
}

func (p *DescribeSqlTaskResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTaskResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTaskResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTaskResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTask", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SqlTask.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTaskResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTaskResp(%+v)", *p)

}

func (p *DescribeSqlTaskResp) DeepEqual(ano *DescribeSqlTaskResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlTask) {
		return false
	}
	return true
}

func (p *DescribeSqlTaskResp) Field1DeepEqual(src *SqlTask) bool {

	if !p.SqlTask.DeepEqual(src) {
		return false
	}
	return true
}

type SqlTask struct {
	InstanceId    string        `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	SqlTaskId     string        `thrift:"SqlTaskId,2" frugal:"2,default,string" json:"SqlTaskId"`
	TenantId      string        `thrift:"TenantId,3" frugal:"3,default,string" json:"TenantId"`
	UserId        string        `thrift:"UserId,4" frugal:"4,default,string" json:"UserId"`
	OrderId       string        `thrift:"OrderId,5" frugal:"5,default,string" json:"OrderId"`
	DBName        string        `thrift:"DBName,6" frugal:"6,default,string" json:"DBName"`
	TableName     string        `thrift:"TableName,7" frugal:"7,default,string" json:"TableName"`
	ExecSQL       string        `thrift:"ExecSQL,8" frugal:"8,default,string" json:"ExecSQL"`
	Comment       string        `thrift:"Comment,9" frugal:"9,default,string" json:"Comment"`
	CreateTime    int32         `thrift:"CreateTime,10" frugal:"10,default,i32" json:"CreateTime"`
	FinishTime    int32         `thrift:"FinishTime,11" frugal:"11,default,i32" json:"FinishTime"`
	SqlTaskStatus SqlTaskStatus `thrift:"SqlTaskStatus,12" frugal:"12,default,SqlTaskStatus" json:"SqlTaskStatus"`
	Result_       string        `thrift:"Result,13" frugal:"13,default,string" json:"Result"`
	Progress      int32         `thrift:"Progress,14" frugal:"14,default,i32" json:"Progress"`
	SqlTaskType   SqlTaskType   `thrift:"SqlTaskType,15" frugal:"15,default,SqlTaskType" json:"SqlTaskType"`
	RunningInfo   string        `thrift:"RunningInfo,16" frugal:"16,default,string" json:"RunningInfo"`
	AffectedRows  string        `thrift:"AffectedRows,17" frugal:"17,default,string" json:"AffectedRows"`
}

func NewSqlTask() *SqlTask {
	return &SqlTask{}
}

func (p *SqlTask) InitDefault() {
}

func (p *SqlTask) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SqlTask) GetSqlTaskId() (v string) {
	return p.SqlTaskId
}

func (p *SqlTask) GetTenantId() (v string) {
	return p.TenantId
}

func (p *SqlTask) GetUserId() (v string) {
	return p.UserId
}

func (p *SqlTask) GetOrderId() (v string) {
	return p.OrderId
}

func (p *SqlTask) GetDBName() (v string) {
	return p.DBName
}

func (p *SqlTask) GetTableName() (v string) {
	return p.TableName
}

func (p *SqlTask) GetExecSQL() (v string) {
	return p.ExecSQL
}

func (p *SqlTask) GetComment() (v string) {
	return p.Comment
}

func (p *SqlTask) GetCreateTime() (v int32) {
	return p.CreateTime
}

func (p *SqlTask) GetFinishTime() (v int32) {
	return p.FinishTime
}

func (p *SqlTask) GetSqlTaskStatus() (v SqlTaskStatus) {
	return p.SqlTaskStatus
}

func (p *SqlTask) GetResult_() (v string) {
	return p.Result_
}

func (p *SqlTask) GetProgress() (v int32) {
	return p.Progress
}

func (p *SqlTask) GetSqlTaskType() (v SqlTaskType) {
	return p.SqlTaskType
}

func (p *SqlTask) GetRunningInfo() (v string) {
	return p.RunningInfo
}

func (p *SqlTask) GetAffectedRows() (v string) {
	return p.AffectedRows
}
func (p *SqlTask) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SqlTask) SetSqlTaskId(val string) {
	p.SqlTaskId = val
}
func (p *SqlTask) SetTenantId(val string) {
	p.TenantId = val
}
func (p *SqlTask) SetUserId(val string) {
	p.UserId = val
}
func (p *SqlTask) SetOrderId(val string) {
	p.OrderId = val
}
func (p *SqlTask) SetDBName(val string) {
	p.DBName = val
}
func (p *SqlTask) SetTableName(val string) {
	p.TableName = val
}
func (p *SqlTask) SetExecSQL(val string) {
	p.ExecSQL = val
}
func (p *SqlTask) SetComment(val string) {
	p.Comment = val
}
func (p *SqlTask) SetCreateTime(val int32) {
	p.CreateTime = val
}
func (p *SqlTask) SetFinishTime(val int32) {
	p.FinishTime = val
}
func (p *SqlTask) SetSqlTaskStatus(val SqlTaskStatus) {
	p.SqlTaskStatus = val
}
func (p *SqlTask) SetResult_(val string) {
	p.Result_ = val
}
func (p *SqlTask) SetProgress(val int32) {
	p.Progress = val
}
func (p *SqlTask) SetSqlTaskType(val SqlTaskType) {
	p.SqlTaskType = val
}
func (p *SqlTask) SetRunningInfo(val string) {
	p.RunningInfo = val
}
func (p *SqlTask) SetAffectedRows(val string) {
	p.AffectedRows = val
}

var fieldIDToName_SqlTask = map[int16]string{
	1:  "InstanceId",
	2:  "SqlTaskId",
	3:  "TenantId",
	4:  "UserId",
	5:  "OrderId",
	6:  "DBName",
	7:  "TableName",
	8:  "ExecSQL",
	9:  "Comment",
	10: "CreateTime",
	11: "FinishTime",
	12: "SqlTaskStatus",
	13: "Result",
	14: "Progress",
	15: "SqlTaskType",
	16: "RunningInfo",
	17: "AffectedRows",
}

func (p *SqlTask) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlTask")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlTask[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlTask) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SqlTask) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlTaskId = _field
	return nil
}
func (p *SqlTask) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *SqlTask) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserId = _field
	return nil
}
func (p *SqlTask) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderId = _field
	return nil
}
func (p *SqlTask) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *SqlTask) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *SqlTask) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecSQL = _field
	return nil
}
func (p *SqlTask) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Comment = _field
	return nil
}
func (p *SqlTask) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *SqlTask) ReadField11(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinishTime = _field
	return nil
}
func (p *SqlTask) ReadField12(iprot thrift.TProtocol) error {

	var _field SqlTaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SqlTaskStatus(v)
	}
	p.SqlTaskStatus = _field
	return nil
}
func (p *SqlTask) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Result_ = _field
	return nil
}
func (p *SqlTask) ReadField14(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Progress = _field
	return nil
}
func (p *SqlTask) ReadField15(iprot thrift.TProtocol) error {

	var _field SqlTaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SqlTaskType(v)
	}
	p.SqlTaskType = _field
	return nil
}
func (p *SqlTask) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RunningInfo = _field
	return nil
}
func (p *SqlTask) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AffectedRows = _field
	return nil
}

func (p *SqlTask) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlTask")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlTask"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTaskId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecSQL", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecSQL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SqlTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Comment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SqlTask) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SqlTask) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FinishTime", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinishTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SqlTask) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTaskStatus", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SqlTaskStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SqlTask) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Result", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Result_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SqlTask) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Progress", thrift.I32, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Progress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SqlTask) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTaskType", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SqlTaskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *SqlTask) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RunningInfo", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RunningInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *SqlTask) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AffectedRows", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AffectedRows); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *SqlTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlTask(%+v)", *p)

}

func (p *SqlTask) DeepEqual(ano *SqlTask) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlTaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field4DeepEqual(ano.UserId) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderId) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field7DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field8DeepEqual(ano.ExecSQL) {
		return false
	}
	if !p.Field9DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field11DeepEqual(ano.FinishTime) {
		return false
	}
	if !p.Field12DeepEqual(ano.SqlTaskStatus) {
		return false
	}
	if !p.Field13DeepEqual(ano.Result_) {
		return false
	}
	if !p.Field14DeepEqual(ano.Progress) {
		return false
	}
	if !p.Field15DeepEqual(ano.SqlTaskType) {
		return false
	}
	if !p.Field16DeepEqual(ano.RunningInfo) {
		return false
	}
	if !p.Field17DeepEqual(ano.AffectedRows) {
		return false
	}
	return true
}

func (p *SqlTask) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SqlTaskId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field4DeepEqual(src string) bool {

	if strings.Compare(p.UserId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field5DeepEqual(src string) bool {

	if strings.Compare(p.OrderId, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field6DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field7DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field8DeepEqual(src string) bool {

	if strings.Compare(p.ExecSQL, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Comment, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field10DeepEqual(src int32) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}
func (p *SqlTask) Field11DeepEqual(src int32) bool {

	if p.FinishTime != src {
		return false
	}
	return true
}
func (p *SqlTask) Field12DeepEqual(src SqlTaskStatus) bool {

	if p.SqlTaskStatus != src {
		return false
	}
	return true
}
func (p *SqlTask) Field13DeepEqual(src string) bool {

	if strings.Compare(p.Result_, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field14DeepEqual(src int32) bool {

	if p.Progress != src {
		return false
	}
	return true
}
func (p *SqlTask) Field15DeepEqual(src SqlTaskType) bool {

	if p.SqlTaskType != src {
		return false
	}
	return true
}
func (p *SqlTask) Field16DeepEqual(src string) bool {

	if strings.Compare(p.RunningInfo, src) != 0 {
		return false
	}
	return true
}
func (p *SqlTask) Field17DeepEqual(src string) bool {

	if strings.Compare(p.AffectedRows, src) != 0 {
		return false
	}
	return true
}

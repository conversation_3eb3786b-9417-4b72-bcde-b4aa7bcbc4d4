// Code generated by Kitex v1.18.1. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *SqlSearchParam) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlSearchParam[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SqlSearchParam) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]SqlType, 0, size)
	for i := 0; i < size; i++ {
		var _elem SqlType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = SqlType(v)
		}

		_field = append(_field, _elem)
	}
	p.SqlType = _field
	return offset, nil
}

func (p *SqlSearchParam) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]RuleState, 0, size)
	for i := 0; i < size; i++ {
		var _elem RuleState
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = RuleState(v)
		}

		_field = append(_field, _elem)
	}
	p.RuleState = _field
	return offset, nil
}

func (p *SqlSearchParam) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *EndpointType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := EndpointType(v)
		_field = &tmp
	}
	p.EndpointType = _field
	return offset, nil
}

func (p *SqlSearchParam) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleType(v)
		_field = &tmp
	}
	p.ThrottleType = _field
	return offset, nil
}

func (p *SqlSearchParam) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleTarget
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleTarget(v)
		_field = &tmp
	}
	p.ThrottleTarget = _field
	return offset, nil
}

func (p *SqlSearchParam) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TaskId = _field
	return offset, nil
}

func (p *SqlSearchParam) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SqlSearchParam) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SqlSearchParam) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SqlSearchParam) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.SqlType {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *SqlSearchParam) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRuleState() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.RuleState {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *SqlSearchParam) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndpointType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.EndpointType))
	}
	return offset
}

func (p *SqlSearchParam) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleType))
	}
	return offset
}

func (p *SqlSearchParam) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleTarget() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleTarget))
	}
	return offset
}

func (p *SqlSearchParam) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTaskId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TaskId)
	}
	return offset
}

func (p *SqlSearchParam) field1Length() int {
	l := 0
	if p.IsSetSqlType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.SqlType {
			_ = v
			l += thrift.Binary.I32Length()
		}
	}
	return l
}

func (p *SqlSearchParam) field2Length() int {
	l := 0
	if p.IsSetRuleState() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.RuleState {
			_ = v
			l += thrift.Binary.I32Length()
		}
	}
	return l
}

func (p *SqlSearchParam) field3Length() int {
	l := 0
	if p.IsSetEndpointType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *SqlSearchParam) field4Length() int {
	l := 0
	if p.IsSetThrottleType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *SqlSearchParam) field5Length() int {
	l := 0
	if p.IsSetThrottleTarget() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *SqlSearchParam) field6Length() int {
	l := 0
	if p.IsSetTaskId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TaskId)
	}
	return l
}

func (p *SqlSearchParam) DeepCopy(s interface{}) error {
	src, ok := s.(*SqlSearchParam)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlType != nil {
		p.SqlType = make([]SqlType, 0, len(src.SqlType))
		for _, elem := range src.SqlType {
			var _elem SqlType
			_elem = elem
			p.SqlType = append(p.SqlType, _elem)
		}
	}

	if src.RuleState != nil {
		p.RuleState = make([]RuleState, 0, len(src.RuleState))
		for _, elem := range src.RuleState {
			var _elem RuleState
			_elem = elem
			p.RuleState = append(p.RuleState, _elem)
		}
	}

	if src.EndpointType != nil {
		tmp := *src.EndpointType
		p.EndpointType = &tmp
	}

	if src.ThrottleType != nil {
		tmp := *src.ThrottleType
		p.ThrottleType = &tmp
	}

	if src.ThrottleTarget != nil {
		tmp := *src.ThrottleTarget
		p.ThrottleTarget = &tmp
	}

	if src.TaskId != nil {
		var tmp string
		if *src.TaskId != "" {
			tmp = kutils.StringDeepCopy(*src.TaskId)
		}
		p.TaskId = &tmp
	}

	return nil
}

func (p *GetSqlConcurrencyControlStatusReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlConcurrencyControlStatusReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GetSqlConcurrencyControlStatusReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *GetSqlConcurrencyControlStatusReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *GetSqlConcurrencyControlStatusReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetSqlConcurrencyControlStatusReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetSqlConcurrencyControlStatusReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetSqlConcurrencyControlStatusReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *GetSqlConcurrencyControlStatusReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *GetSqlConcurrencyControlStatusReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *GetSqlConcurrencyControlStatusReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *GetSqlConcurrencyControlStatusReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GetSqlConcurrencyControlStatusReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	return nil
}

func (p *GetSqlConcurrencyControlStatusResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatus bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetStatus {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlConcurrencyControlStatusResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_GetSqlConcurrencyControlStatusResp[fieldId]))
}

func (p *GetSqlConcurrencyControlStatusResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Status = _field
	return offset, nil
}

func (p *GetSqlConcurrencyControlStatusResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetSqlConcurrencyControlStatusResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetSqlConcurrencyControlStatusResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetSqlConcurrencyControlStatusResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Status)
	return offset
}

func (p *GetSqlConcurrencyControlStatusResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *GetSqlConcurrencyControlStatusResp) DeepCopy(s interface{}) error {
	src, ok := s.(*GetSqlConcurrencyControlStatusResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Status = src.Status

	return nil
}

func (p *EnableSqlConcurrencyControlReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EnableSqlConcurrencyControlReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *EnableSqlConcurrencyControlReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *EnableSqlConcurrencyControlReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *EnableSqlConcurrencyControlReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *EnableSqlConcurrencyControlReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *EnableSqlConcurrencyControlReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *EnableSqlConcurrencyControlReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *EnableSqlConcurrencyControlReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *EnableSqlConcurrencyControlReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *EnableSqlConcurrencyControlReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *EnableSqlConcurrencyControlReq) DeepCopy(s interface{}) error {
	src, ok := s.(*EnableSqlConcurrencyControlReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	return nil
}

func (p *EnableSqlConcurrencyControlResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *EnableSqlConcurrencyControlResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *EnableSqlConcurrencyControlResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *EnableSqlConcurrencyControlResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *EnableSqlConcurrencyControlResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *DisableSqlConcurrencyControlReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DisableSqlConcurrencyControlReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DisableSqlConcurrencyControlReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DisableSqlConcurrencyControlReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DisableSqlConcurrencyControlReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DisableSqlConcurrencyControlReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DisableSqlConcurrencyControlReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DisableSqlConcurrencyControlReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DisableSqlConcurrencyControlReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DisableSqlConcurrencyControlReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DisableSqlConcurrencyControlReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DisableSqlConcurrencyControlReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DisableSqlConcurrencyControlReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	return nil
}

func (p *DisableSqlConcurrencyControlResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DisableSqlConcurrencyControlResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DisableSqlConcurrencyControlResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DisableSqlConcurrencyControlResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DisableSqlConcurrencyControlResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConcurrencyCount bool = false
	var issetEffectiveTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetConcurrencyCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetConcurrencyCount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateSqlConcurrencyControlRuleReq[fieldId]))
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *SqlType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SqlType(v)
		_field = &tmp
	}
	p.SqlType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ConcurrencyCount = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EffectiveTime = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.KeyWords = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleThreshold = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *LinkType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleType(v)
		_field = &tmp
	}
	p.ThrottleType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EndpointId = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *EndpointType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := EndpointType(v)
		_field = &tmp
	}
	p.EndpointType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleHost = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleDB = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleSqlText = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleFingerPrint = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field *TerminationType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := TerminationType(v)
		_field = &tmp
	}
	p.TerminationType = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleMode
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleTarget
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleTarget(v)
		_field = &tmp
	}
	p.ThrottleTarget = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField19(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem int64
		if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.GroupIds = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Desc = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateSqlConcurrencyControlRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSqlType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SqlType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ConcurrencyCount)
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.EffectiveTime)
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetKeyWords() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.KeyWords)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleThreshold() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.ThrottleThreshold)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLinkType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.LinkType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndpointId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.EndpointId)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndpointType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.EndpointType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleHost() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleHost)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleDB() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleDB)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleSqlText() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleSqlText)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleFingerPrint() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleFingerPrint)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTerminationType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 16)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.TerminationType))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleMode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 17)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleMode))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleTarget() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 18)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleTarget))
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetGroupIds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 19)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.GroupIds {
			length++
			offset += thrift.Binary.WriteI64(buf[offset:], v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I64, length)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDesc() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Desc)
	}
	return offset
}

func (p *CreateSqlConcurrencyControlRuleReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field2Length() int {
	l := 0
	if p.IsSetSqlType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field5Length() int {
	l := 0
	if p.IsSetKeyWords() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.KeyWords)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field6Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field7Length() int {
	l := 0
	if p.IsSetThrottleThreshold() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field8Length() int {
	l := 0
	if p.IsSetLinkType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field9Length() int {
	l := 0
	if p.IsSetThrottleType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field10Length() int {
	l := 0
	if p.IsSetEndpointId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.EndpointId)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field11Length() int {
	l := 0
	if p.IsSetEndpointType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field12Length() int {
	l := 0
	if p.IsSetThrottleHost() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleHost)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field13Length() int {
	l := 0
	if p.IsSetThrottleDB() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleDB)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field14Length() int {
	l := 0
	if p.IsSetThrottleSqlText() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleSqlText)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field15Length() int {
	l := 0
	if p.IsSetThrottleFingerPrint() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleFingerPrint)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field16Length() int {
	l := 0
	if p.IsSetTerminationType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field17Length() int {
	l := 0
	if p.IsSetThrottleMode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field18Length() int {
	l := 0
	if p.IsSetThrottleTarget() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field19Length() int {
	l := 0
	if p.IsSetGroupIds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		l +=
			thrift.Binary.I64Length() * len(p.GroupIds)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field20Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) field21Length() int {
	l := 0
	if p.IsSetDesc() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Desc)
	}
	return l
}

func (p *CreateSqlConcurrencyControlRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateSqlConcurrencyControlRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.SqlType != nil {
		tmp := *src.SqlType
		p.SqlType = &tmp
	}

	p.ConcurrencyCount = src.ConcurrencyCount

	p.EffectiveTime = src.EffectiveTime

	if src.KeyWords != nil {
		var tmp string
		if *src.KeyWords != "" {
			tmp = kutils.StringDeepCopy(*src.KeyWords)
		}
		p.KeyWords = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.ThrottleThreshold != nil {
		tmp := *src.ThrottleThreshold
		p.ThrottleThreshold = &tmp
	}

	if src.LinkType != nil {
		tmp := *src.LinkType
		p.LinkType = &tmp
	}

	if src.ThrottleType != nil {
		tmp := *src.ThrottleType
		p.ThrottleType = &tmp
	}

	if src.EndpointId != nil {
		var tmp string
		if *src.EndpointId != "" {
			tmp = kutils.StringDeepCopy(*src.EndpointId)
		}
		p.EndpointId = &tmp
	}

	if src.EndpointType != nil {
		tmp := *src.EndpointType
		p.EndpointType = &tmp
	}

	if src.ThrottleHost != nil {
		var tmp string
		if *src.ThrottleHost != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleHost)
		}
		p.ThrottleHost = &tmp
	}

	if src.ThrottleDB != nil {
		var tmp string
		if *src.ThrottleDB != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleDB)
		}
		p.ThrottleDB = &tmp
	}

	if src.ThrottleSqlText != nil {
		var tmp string
		if *src.ThrottleSqlText != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleSqlText)
		}
		p.ThrottleSqlText = &tmp
	}

	if src.ThrottleFingerPrint != nil {
		var tmp string
		if *src.ThrottleFingerPrint != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleFingerPrint)
		}
		p.ThrottleFingerPrint = &tmp
	}

	if src.TerminationType != nil {
		tmp := *src.TerminationType
		p.TerminationType = &tmp
	}

	if src.ThrottleMode != nil {
		tmp := *src.ThrottleMode
		p.ThrottleMode = &tmp
	}

	if src.ThrottleTarget != nil {
		tmp := *src.ThrottleTarget
		p.ThrottleTarget = &tmp
	}

	if src.GroupIds != nil {
		p.GroupIds = make([]int64, 0, len(src.GroupIds))
		for _, elem := range src.GroupIds {
			var _elem int64
			_elem = elem
			p.GroupIds = append(p.GroupIds, _elem)
		}
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	if src.Desc != nil {
		var tmp string
		if *src.Desc != "" {
			tmp = kutils.StringDeepCopy(*src.Desc)
		}
		p.Desc = &tmp
	}

	return nil
}

func (p *CreateSqlConcurrencyControlRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRuleId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlConcurrencyControlRuleResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateSqlConcurrencyControlRuleResp[fieldId]))
}

func (p *CreateSqlConcurrencyControlRuleResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleId = _field
	return offset, nil
}

func (p *CreateSqlConcurrencyControlRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateSqlConcurrencyControlRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateSqlConcurrencyControlRuleResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateSqlConcurrencyControlRuleResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleId)
	return offset
}

func (p *CreateSqlConcurrencyControlRuleResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CreateSqlConcurrencyControlRuleResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateSqlConcurrencyControlRuleResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.RuleId = src.RuleId

	return nil
}

func (p *StopSqlConcurrencyControlRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *StopSqlConcurrencyControlRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *StopSqlConcurrencyControlRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.TaskId = _field
	return offset, nil
}

func (p *StopSqlConcurrencyControlRuleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *StopSqlConcurrencyControlRuleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *StopSqlConcurrencyControlRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *StopSqlConcurrencyControlRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *StopSqlConcurrencyControlRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *StopSqlConcurrencyControlRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *StopSqlConcurrencyControlRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTaskId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.TaskId {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *StopSqlConcurrencyControlRuleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *StopSqlConcurrencyControlRuleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *StopSqlConcurrencyControlRuleReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *StopSqlConcurrencyControlRuleReq) field2Length() int {
	l := 0
	if p.IsSetTaskId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.TaskId {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *StopSqlConcurrencyControlRuleReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *StopSqlConcurrencyControlRuleReq) field4Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *StopSqlConcurrencyControlRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*StopSqlConcurrencyControlRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.TaskId != nil {
		p.TaskId = make([]string, 0, len(src.TaskId))
		for _, elem := range src.TaskId {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.TaskId = append(p.TaskId, _elem)
		}
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *StopSqlConcurrencyControlRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *StopSqlConcurrencyControlRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *StopSqlConcurrencyControlRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *StopSqlConcurrencyControlRuleResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *StopSqlConcurrencyControlRuleResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *RestartSqlConcurrencyControlRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestartSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *RestartSqlConcurrencyControlRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TaskId = _field
	return offset, nil
}

func (p *RestartSqlConcurrencyControlRuleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *RestartSqlConcurrencyControlRuleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *RestartSqlConcurrencyControlRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestartSqlConcurrencyControlRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestartSqlConcurrencyControlRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestartSqlConcurrencyControlRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *RestartSqlConcurrencyControlRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTaskId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TaskId)
	}
	return offset
}

func (p *RestartSqlConcurrencyControlRuleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *RestartSqlConcurrencyControlRuleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *RestartSqlConcurrencyControlRuleReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *RestartSqlConcurrencyControlRuleReq) field2Length() int {
	l := 0
	if p.IsSetTaskId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TaskId)
	}
	return l
}

func (p *RestartSqlConcurrencyControlRuleReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RestartSqlConcurrencyControlRuleReq) field4Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *RestartSqlConcurrencyControlRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RestartSqlConcurrencyControlRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.TaskId != nil {
		var tmp string
		if *src.TaskId != "" {
			tmp = kutils.StringDeepCopy(*src.TaskId)
		}
		p.TaskId = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *RestartSqlConcurrencyControlRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RestartSqlConcurrencyControlRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RestartSqlConcurrencyControlRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RestartSqlConcurrencyControlRuleResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RestartSqlConcurrencyControlRuleResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.TaskId = _field
	return offset, nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteSqlConcurrencyControlRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteSqlConcurrencyControlRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTaskId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.TaskId {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DeleteSqlConcurrencyControlRuleReq) field2Length() int {
	l := 0
	if p.IsSetTaskId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.TaskId {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DeleteSqlConcurrencyControlRuleReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DeleteSqlConcurrencyControlRuleReq) field4Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DeleteSqlConcurrencyControlRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteSqlConcurrencyControlRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.TaskId != nil {
		p.TaskId = make([]string, 0, len(src.TaskId))
		for _, elem := range src.TaskId {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.TaskId = append(p.TaskId, _elem)
		}
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DeleteSqlConcurrencyControlRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteSqlConcurrencyControlRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteSqlConcurrencyControlRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteSqlConcurrencyControlRuleResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteSqlConcurrencyControlRuleResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRulesReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewSqlSearchParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SearchParam = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageNumber = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PageSize = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *SortBy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *OrderByForDBRule
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := OrderByForDBRule(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field *LinkType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleMode
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlConcurrencyControlRulesReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSearchParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
		offset += p.SearchParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageNumber() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageNumber)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPageSize() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.PageSize)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortBy))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderBy))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLinkType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.LinkType))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleMode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleMode))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field2Length() int {
	l := 0
	if p.IsSetSearchParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.SearchParam.BLength()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field3Length() int {
	l := 0
	if p.IsSetPageNumber() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field4Length() int {
	l := 0
	if p.IsSetPageSize() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field5Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field6Length() int {
	l := 0
	if p.IsSetOrderBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field7Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field8Length() int {
	l := 0
	if p.IsSetLinkType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field9Length() int {
	l := 0
	if p.IsSetThrottleMode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) field10Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlConcurrencyControlRulesReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	var _searchParam *SqlSearchParam
	if src.SearchParam != nil {
		_searchParam = &SqlSearchParam{}
		if err := _searchParam.DeepCopy(src.SearchParam); err != nil {
			return err
		}
	}
	p.SearchParam = _searchParam

	if src.PageNumber != nil {
		tmp := *src.PageNumber
		p.PageNumber = &tmp
	}

	if src.PageSize != nil {
		tmp := *src.PageSize
		p.PageSize = &tmp
	}

	if src.SortBy != nil {
		tmp := *src.SortBy
		p.SortBy = &tmp
	}

	if src.OrderBy != nil {
		tmp := *src.OrderBy
		p.OrderBy = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.LinkType != nil {
		tmp := *src.LinkType
		p.LinkType = &tmp
	}

	if src.ThrottleMode != nil {
		tmp := *src.ThrottleMode
		p.ThrottleMode = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *RuleDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetSqlType bool = false
	var issetRuleState bool = false
	var issetKeyWord bool = false
	var issetCreateTime bool = false
	var issetRemainingTime bool = false
	var issetEndTime bool = false
	var issetConcurrencyCount bool = false
	var issetEffectiveTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleState = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetKeyWord = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRemainingTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetConcurrencyCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSqlType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRuleState {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetKeyWord {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetRemainingTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetConcurrencyCount {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RuleDetail[fieldId]))
}

func (p *RuleDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TaskId = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field SqlType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = SqlType(v)
	}
	p.SqlType = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field RuleState
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = RuleState(v)
	}
	p.RuleState = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.KeyWord = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RemainingTime = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EndTime = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ConcurrencyCount = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EffectiveTime = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EndpointType = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EndpointId = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleType = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleHost = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleDB = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleSqlText = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleFingerPrint = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleThreshold = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RejectCount = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleTarget = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ThrottleMode = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Desc = _field
	return offset, nil
}

func (p *RuleDetail) FastReadField22(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TerminationType = _field
	return offset, nil
}

func (p *RuleDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RuleDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RuleDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RuleDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TaskId)
	return offset
}

func (p *RuleDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.SqlType))
	return offset
}

func (p *RuleDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RuleState))
	return offset
}

func (p *RuleDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.KeyWord)
	return offset
}

func (p *RuleDetail) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateTime)
	return offset
}

func (p *RuleDetail) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], p.RemainingTime)
	return offset
}

func (p *RuleDetail) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.EndTime)
	return offset
}

func (p *RuleDetail) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ConcurrencyCount)
	return offset
}

func (p *RuleDetail) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
	offset += thrift.Binary.WriteI32(buf[offset:], p.EffectiveTime)
	return offset
}

func (p *RuleDetail) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndpointType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.EndpointType)
	}
	return offset
}

func (p *RuleDetail) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndpointId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.EndpointId)
	}
	return offset
}

func (p *RuleDetail) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleType)
	}
	return offset
}

func (p *RuleDetail) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleHost() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleHost)
	}
	return offset
}

func (p *RuleDetail) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleDB() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleDB)
	}
	return offset
}

func (p *RuleDetail) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleSqlText() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleSqlText)
	}
	return offset
}

func (p *RuleDetail) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleFingerPrint() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 16)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleFingerPrint)
	}
	return offset
}

func (p *RuleDetail) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleThreshold() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 17)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.ThrottleThreshold)
	}
	return offset
}

func (p *RuleDetail) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRejectCount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 18)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.RejectCount)
	}
	return offset
}

func (p *RuleDetail) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleTarget() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 19)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleTarget)
	}
	return offset
}

func (p *RuleDetail) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleMode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ThrottleMode)
	}
	return offset
}

func (p *RuleDetail) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDesc() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Desc)
	}
	return offset
}

func (p *RuleDetail) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTerminationType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 22)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.TerminationType)
	}
	return offset
}

func (p *RuleDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TaskId)
	return l
}

func (p *RuleDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RuleDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RuleDetail) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.KeyWord)
	return l
}

func (p *RuleDetail) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateTime)
	return l
}

func (p *RuleDetail) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RuleDetail) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.EndTime)
	return l
}

func (p *RuleDetail) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RuleDetail) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RuleDetail) field10Length() int {
	l := 0
	if p.IsSetEndpointType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.EndpointType)
	}
	return l
}

func (p *RuleDetail) field11Length() int {
	l := 0
	if p.IsSetEndpointId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.EndpointId)
	}
	return l
}

func (p *RuleDetail) field12Length() int {
	l := 0
	if p.IsSetThrottleType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleType)
	}
	return l
}

func (p *RuleDetail) field13Length() int {
	l := 0
	if p.IsSetThrottleHost() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleHost)
	}
	return l
}

func (p *RuleDetail) field14Length() int {
	l := 0
	if p.IsSetThrottleDB() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleDB)
	}
	return l
}

func (p *RuleDetail) field15Length() int {
	l := 0
	if p.IsSetThrottleSqlText() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleSqlText)
	}
	return l
}

func (p *RuleDetail) field16Length() int {
	l := 0
	if p.IsSetThrottleFingerPrint() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleFingerPrint)
	}
	return l
}

func (p *RuleDetail) field17Length() int {
	l := 0
	if p.IsSetThrottleThreshold() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *RuleDetail) field18Length() int {
	l := 0
	if p.IsSetRejectCount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *RuleDetail) field19Length() int {
	l := 0
	if p.IsSetThrottleTarget() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleTarget)
	}
	return l
}

func (p *RuleDetail) field20Length() int {
	l := 0
	if p.IsSetThrottleMode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ThrottleMode)
	}
	return l
}

func (p *RuleDetail) field21Length() int {
	l := 0
	if p.IsSetDesc() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Desc)
	}
	return l
}

func (p *RuleDetail) field22Length() int {
	l := 0
	if p.IsSetTerminationType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.TerminationType)
	}
	return l
}

func (p *RuleDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*RuleDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TaskId != "" {
		p.TaskId = kutils.StringDeepCopy(src.TaskId)
	}

	p.SqlType = src.SqlType

	p.RuleState = src.RuleState

	if src.KeyWord != "" {
		p.KeyWord = kutils.StringDeepCopy(src.KeyWord)
	}

	if src.CreateTime != "" {
		p.CreateTime = kutils.StringDeepCopy(src.CreateTime)
	}

	p.RemainingTime = src.RemainingTime

	if src.EndTime != "" {
		p.EndTime = kutils.StringDeepCopy(src.EndTime)
	}

	p.ConcurrencyCount = src.ConcurrencyCount

	p.EffectiveTime = src.EffectiveTime

	if src.EndpointType != nil {
		var tmp string
		if *src.EndpointType != "" {
			tmp = kutils.StringDeepCopy(*src.EndpointType)
		}
		p.EndpointType = &tmp
	}

	if src.EndpointId != nil {
		var tmp string
		if *src.EndpointId != "" {
			tmp = kutils.StringDeepCopy(*src.EndpointId)
		}
		p.EndpointId = &tmp
	}

	if src.ThrottleType != nil {
		var tmp string
		if *src.ThrottleType != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleType)
		}
		p.ThrottleType = &tmp
	}

	if src.ThrottleHost != nil {
		var tmp string
		if *src.ThrottleHost != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleHost)
		}
		p.ThrottleHost = &tmp
	}

	if src.ThrottleDB != nil {
		var tmp string
		if *src.ThrottleDB != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleDB)
		}
		p.ThrottleDB = &tmp
	}

	if src.ThrottleSqlText != nil {
		var tmp string
		if *src.ThrottleSqlText != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleSqlText)
		}
		p.ThrottleSqlText = &tmp
	}

	if src.ThrottleFingerPrint != nil {
		var tmp string
		if *src.ThrottleFingerPrint != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleFingerPrint)
		}
		p.ThrottleFingerPrint = &tmp
	}

	if src.ThrottleThreshold != nil {
		tmp := *src.ThrottleThreshold
		p.ThrottleThreshold = &tmp
	}

	if src.RejectCount != nil {
		tmp := *src.RejectCount
		p.RejectCount = &tmp
	}

	if src.ThrottleTarget != nil {
		var tmp string
		if *src.ThrottleTarget != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleTarget)
		}
		p.ThrottleTarget = &tmp
	}

	if src.ThrottleMode != nil {
		var tmp string
		if *src.ThrottleMode != "" {
			tmp = kutils.StringDeepCopy(*src.ThrottleMode)
		}
		p.ThrottleMode = &tmp
	}

	if src.Desc != nil {
		var tmp string
		if *src.Desc != "" {
			tmp = kutils.StringDeepCopy(*src.Desc)
		}
		p.Desc = &tmp
	}

	if src.TerminationType != nil {
		var tmp string
		if *src.TerminationType != "" {
			tmp = kutils.StringDeepCopy(*src.TerminationType)
		}
		p.TerminationType = &tmp
	}

	return nil
}

func (p *DescribeSqlConcurrencyControlRulesResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleDetails bool = false
	var issetTotal bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleDetails = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRuleDetails {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRulesResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRulesResp[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRulesResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*RuleDetail, 0, size)
	values := make([]RuleDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RuleDetails = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRulesResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlConcurrencyControlRulesResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlConcurrencyControlRulesResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RuleDetails {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeSqlConcurrencyControlRulesResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RuleDetails {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRulesResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeSqlConcurrencyControlRulesResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlConcurrencyControlRulesResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RuleDetails != nil {
		p.RuleDetails = make([]*RuleDetail, 0, len(src.RuleDetails))
		for _, elem := range src.RuleDetails {
			var _elem *RuleDetail
			if elem != nil {
				_elem = &RuleDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RuleDetails = append(p.RuleDetails, _elem)
		}
	}

	p.Total = src.Total

	return nil
}

func (p *DescribeSqlKeywordsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKeywordsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeSqlKeywordsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Sql = _field
	return offset, nil
}

func (p *DescribeSqlKeywordsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeSqlKeywordsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *LinkType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return offset, nil
}

func (p *DescribeSqlKeywordsReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeSqlKeywordsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlKeywordsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlKeywordsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlKeywordsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSql() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Sql)
	}
	return offset
}

func (p *DescribeSqlKeywordsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DescribeSqlKeywordsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLinkType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.LinkType))
	}
	return offset
}

func (p *DescribeSqlKeywordsReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeSqlKeywordsReq) field1Length() int {
	l := 0
	if p.IsSetSql() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Sql)
	}
	return l
}

func (p *DescribeSqlKeywordsReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlKeywordsReq) field3Length() int {
	l := 0
	if p.IsSetLinkType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlKeywordsReq) field4Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeSqlKeywordsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlKeywordsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Sql != nil {
		var tmp string
		if *src.Sql != "" {
			tmp = kutils.StringDeepCopy(*src.Sql)
		}
		p.Sql = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.LinkType != nil {
		tmp := *src.LinkType
		p.LinkType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DescribeSqlKeywordsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKeyWords bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetKeyWords = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetKeyWords {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKeywordsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlKeywordsResp[fieldId]))
}

func (p *DescribeSqlKeywordsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.KeyWords = _field
	return offset, nil
}

func (p *DescribeSqlKeywordsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlKeywordsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlKeywordsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlKeywordsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.KeyWords)
	return offset
}

func (p *DescribeSqlKeywordsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.KeyWords)
	return l
}

func (p *DescribeSqlKeywordsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlKeywordsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.KeyWords != "" {
		p.KeyWords = kutils.StringDeepCopy(src.KeyWords)
	}

	return nil
}

func (p *DescribeSqlFingerPrintReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSql bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSql = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSql {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlFingerPrintReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlFingerPrintReq[fieldId]))
}

func (p *DescribeSqlFingerPrintReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Sql = _field
	return offset, nil
}

func (p *DescribeSqlFingerPrintReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeSqlFingerPrintReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeSqlFingerPrintReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlFingerPrintReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlFingerPrintReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlFingerPrintReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Sql)
	return offset
}

func (p *DescribeSqlFingerPrintReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DescribeSqlFingerPrintReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeSqlFingerPrintReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Sql)
	return l
}

func (p *DescribeSqlFingerPrintReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlFingerPrintReq) field3Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeSqlFingerPrintReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlFingerPrintReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Sql != "" {
		p.Sql = kutils.StringDeepCopy(src.Sql)
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DescribeSqlFingerPrintResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlFingerPrint bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlFingerPrint = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlFingerPrint {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlFingerPrintResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlFingerPrintResp[fieldId]))
}

func (p *DescribeSqlFingerPrintResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlFingerPrint = _field
	return offset, nil
}

func (p *DescribeSqlFingerPrintResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlFingerPrintResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlFingerPrintResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlFingerPrintResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlFingerPrint)
	return offset
}

func (p *DescribeSqlFingerPrintResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlFingerPrint)
	return l
}

func (p *DescribeSqlFingerPrintResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlFingerPrintResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlFingerPrint != "" {
		p.SqlFingerPrint = kutils.StringDeepCopy(src.SqlFingerPrint)
	}

	return nil
}

func (p *DescribeSqlTypeReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlText bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlText {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTypeReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlTypeReq[fieldId]))
}

func (p *DescribeSqlTypeReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlText = _field
	return offset, nil
}

func (p *DescribeSqlTypeReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeSqlTypeReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *DescribeSqlTypeReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlTypeReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlTypeReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlTypeReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlText)
	return offset
}

func (p *DescribeSqlTypeReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DescribeSqlTypeReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *DescribeSqlTypeReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlText)
	return l
}

func (p *DescribeSqlTypeReq) field2Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlTypeReq) field3Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *DescribeSqlTypeReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlTypeReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlText != "" {
		p.SqlText = kutils.StringDeepCopy(src.SqlText)
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *DescribeSqlTypeResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlType bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSqlType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSqlType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTypeResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlTypeResp[fieldId]))
}

func (p *DescribeSqlTypeResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SqlType = _field
	return offset, nil
}

func (p *DescribeSqlTypeResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlTypeResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlTypeResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlTypeResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SqlType)
	return offset
}

func (p *DescribeSqlTypeResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SqlType)
	return l
}

func (p *DescribeSqlTypeResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlTypeResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SqlType != "" {
		p.SqlType = kutils.StringDeepCopy(src.SqlType)
	}

	return nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTaskId bool = false
	var issetThreshold bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetThreshold = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetThreshold {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifySqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifySqlConcurrencyControlRuleReq[fieldId]))
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TaskId = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Threshold = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *ThrottleMode
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *ModifySqlConcurrencyControlRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifySqlConcurrencyControlRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TaskId)
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Threshold)
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetThrottleMode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.ThrottleMode))
	}
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRegionId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RegionId)
	}
	return offset
}

func (p *ModifySqlConcurrencyControlRuleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TaskId)
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) field5Length() int {
	l := 0
	if p.IsSetThrottleMode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) field6Length() int {
	l := 0
	if p.IsSetRegionId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RegionId)
	}
	return l
}

func (p *ModifySqlConcurrencyControlRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifySqlConcurrencyControlRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.TaskId != "" {
		p.TaskId = kutils.StringDeepCopy(src.TaskId)
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	p.Threshold = src.Threshold

	if src.ThrottleMode != nil {
		tmp := *src.ThrottleMode
		p.ThrottleMode = &tmp
	}

	if src.RegionId != nil {
		var tmp string
		if *src.RegionId != "" {
			tmp = kutils.StringDeepCopy(*src.RegionId)
		}
		p.RegionId = &tmp
	}

	return nil
}

func (p *ModifySqlConcurrencyControlRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ModifySqlConcurrencyControlRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifySqlConcurrencyControlRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifySqlConcurrencyControlRuleResp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifySqlConcurrencyControlRuleResp) DeepCopy(s interface{}) error {

	return nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRuleDetailReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRuleDetailReq[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TaskId = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *DSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TaskId)
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InstanceType))
	}
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TaskId)
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) field2Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) field3Length() int {
	l := 0
	if p.IsSetInstanceType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlConcurrencyControlRuleDetailReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TaskId != "" {
		p.TaskId = kutils.StringDeepCopy(src.TaskId)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceType != nil {
		tmp := *src.InstanceType
		p.InstanceType = &tmp
	}

	return nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRejectCount bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetDuration bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRejectCount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDuration = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRejectCount {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDuration {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRuleDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRuleDetailResp[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RejectCount = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StartTime = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EndTime = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Duration = _field
	return offset, nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RejectCount)
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StartTime)
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.EndTime)
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Duration)
	return offset
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StartTime)
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.EndTime)
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeSqlConcurrencyControlRuleDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.RejectCount = src.RejectCount

	if src.StartTime != "" {
		p.StartTime = kutils.StringDeepCopy(src.StartTime)
	}

	if src.EndTime != "" {
		p.EndTime = kutils.StringDeepCopy(src.EndTime)
	}

	p.Duration = src.Duration

	return nil
}

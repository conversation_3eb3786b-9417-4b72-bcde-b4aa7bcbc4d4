// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SqlReviewStatus int64

const (
	SqlReviewStatus_WAITING_REVIEW  SqlReviewStatus = 0
	SqlReviewStatus_REVIEW_FINISHED SqlReviewStatus = 1
	SqlReviewStatus_WAITING_APPROVE SqlReviewStatus = 2
	SqlReviewStatus_APPROVE_SUCCESS SqlReviewStatus = 3
	SqlReviewStatus_APPROVE_REJECT  SqlReviewStatus = 4
	SqlReviewStatus_REVIEW_FAILED   SqlReviewStatus = 5
)

func (p SqlReviewStatus) String() string {
	switch p {
	case SqlReviewStatus_WAITING_REVIEW:
		return "WAITING_REVIEW"
	case SqlReviewStatus_REVIEW_FINISHED:
		return "REVIEW_FINISHED"
	case SqlReviewStatus_WAITING_APPROVE:
		return "WAITING_APPROVE"
	case SqlReviewStatus_APPROVE_SUCCESS:
		return "APPROVE_SUCCESS"
	case SqlReviewStatus_APPROVE_REJECT:
		return "APPROVE_REJECT"
	case SqlReviewStatus_REVIEW_FAILED:
		return "REVIEW_FAILED"
	}
	return "<UNSET>"
}

func SqlReviewStatusFromString(s string) (SqlReviewStatus, error) {
	switch s {
	case "WAITING_REVIEW":
		return SqlReviewStatus_WAITING_REVIEW, nil
	case "REVIEW_FINISHED":
		return SqlReviewStatus_REVIEW_FINISHED, nil
	case "WAITING_APPROVE":
		return SqlReviewStatus_WAITING_APPROVE, nil
	case "APPROVE_SUCCESS":
		return SqlReviewStatus_APPROVE_SUCCESS, nil
	case "APPROVE_REJECT":
		return SqlReviewStatus_APPROVE_REJECT, nil
	case "REVIEW_FAILED":
		return SqlReviewStatus_REVIEW_FAILED, nil
	}
	return SqlReviewStatus(0), fmt.Errorf("not a valid SqlReviewStatus string")
}

func SqlReviewStatusPtr(v SqlReviewStatus) *SqlReviewStatus { return &v }

func (p SqlReviewStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlReviewStatus) UnmarshalText(text []byte) error {
	q, err := SqlReviewStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlReviewDetailStatus int64

const (
	SqlReviewDetailStatus_WAITING_REVIEW  SqlReviewDetailStatus = 0
	SqlReviewDetailStatus_REVIEW_FINISHED SqlReviewDetailStatus = 1
)

func (p SqlReviewDetailStatus) String() string {
	switch p {
	case SqlReviewDetailStatus_WAITING_REVIEW:
		return "WAITING_REVIEW"
	case SqlReviewDetailStatus_REVIEW_FINISHED:
		return "REVIEW_FINISHED"
	}
	return "<UNSET>"
}

func SqlReviewDetailStatusFromString(s string) (SqlReviewDetailStatus, error) {
	switch s {
	case "WAITING_REVIEW":
		return SqlReviewDetailStatus_WAITING_REVIEW, nil
	case "REVIEW_FINISHED":
		return SqlReviewDetailStatus_REVIEW_FINISHED, nil
	}
	return SqlReviewDetailStatus(0), fmt.Errorf("not a valid SqlReviewDetailStatus string")
}

func SqlReviewDetailStatusPtr(v SqlReviewDetailStatus) *SqlReviewDetailStatus { return &v }

func (p SqlReviewDetailStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlReviewDetailStatus) UnmarshalText(text []byte) error {
	q, err := SqlReviewDetailStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForSqlReview int64

const (
	OrderByForSqlReview_CreateTime OrderByForSqlReview = 0
	OrderByForSqlReview_UpdateTime OrderByForSqlReview = 1
)

func (p OrderByForSqlReview) String() string {
	switch p {
	case OrderByForSqlReview_CreateTime:
		return "CreateTime"
	case OrderByForSqlReview_UpdateTime:
		return "UpdateTime"
	}
	return "<UNSET>"
}

func OrderByForSqlReviewFromString(s string) (OrderByForSqlReview, error) {
	switch s {
	case "CreateTime":
		return OrderByForSqlReview_CreateTime, nil
	case "UpdateTime":
		return OrderByForSqlReview_UpdateTime, nil
	}
	return OrderByForSqlReview(0), fmt.Errorf("not a valid OrderByForSqlReview string")
}

func OrderByForSqlReviewPtr(v OrderByForSqlReview) *OrderByForSqlReview { return &v }

func (p OrderByForSqlReview) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForSqlReview) UnmarshalText(text []byte) error {
	q, err := OrderByForSqlReviewFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlReviewListType int64

const (
	SqlReviewListType_CreatedByMe  SqlReviewListType = 0
	SqlReviewListType_ApprovedByMe SqlReviewListType = 1
	SqlReviewListType_All          SqlReviewListType = 2
)

func (p SqlReviewListType) String() string {
	switch p {
	case SqlReviewListType_CreatedByMe:
		return "CreatedByMe"
	case SqlReviewListType_ApprovedByMe:
		return "ApprovedByMe"
	case SqlReviewListType_All:
		return "All"
	}
	return "<UNSET>"
}

func SqlReviewListTypeFromString(s string) (SqlReviewListType, error) {
	switch s {
	case "CreatedByMe":
		return SqlReviewListType_CreatedByMe, nil
	case "ApprovedByMe":
		return SqlReviewListType_ApprovedByMe, nil
	case "All":
		return SqlReviewListType_All, nil
	}
	return SqlReviewListType(0), fmt.Errorf("not a valid SqlReviewListType string")
}

func SqlReviewListTypePtr(v SqlReviewListType) *SqlReviewListType { return &v }

func (p SqlReviewListType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlReviewListType) UnmarshalText(text []byte) error {
	q, err := SqlReviewListTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlReview struct {
	ID             string `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	Name           string `thrift:"Name,2,required" frugal:"2,required,string" json:"Name"`
	DbName         string `thrift:"DbName,3,required" frugal:"3,required,string" json:"DbName"`
	InstanceType   string `thrift:"InstanceType,4,required" frugal:"4,required,string" json:"InstanceType"`
	InstanceID     string `thrift:"InstanceID,5,required" frugal:"5,required,string" json:"InstanceID"`
	ReviewSource   int8   `thrift:"ReviewSource,6,required" frugal:"6,required,i8" json:"ReviewSource"`
	ReviewContent  string `thrift:"ReviewContent,7,required" frugal:"7,required,string" json:"ReviewContent"`
	ReviewStatus   int8   `thrift:"ReviewStatus,8,required" frugal:"8,required,i8" json:"ReviewStatus"`
	Comment        string `thrift:"Comment,9,required" frugal:"9,required,string" json:"Comment"`
	CreateUserName string `thrift:"CreateUserName,10,required" frugal:"10,required,string" json:"CreateUserName"`
	CreateUserID   string `thrift:"CreateUserID,11,required" frugal:"11,required,string" json:"CreateUserID"`
	CreateTime     int64  `thrift:"CreateTime,12,required" frugal:"12,required,i64" json:"CreateTime"`
	UpdateTime     int64  `thrift:"UpdateTime,13,required" frugal:"13,required,i64" json:"UpdateTime"`
}

func NewSqlReview() *SqlReview {
	return &SqlReview{}
}

func (p *SqlReview) InitDefault() {
}

func (p *SqlReview) GetID() (v string) {
	return p.ID
}

func (p *SqlReview) GetName() (v string) {
	return p.Name
}

func (p *SqlReview) GetDbName() (v string) {
	return p.DbName
}

func (p *SqlReview) GetInstanceType() (v string) {
	return p.InstanceType
}

func (p *SqlReview) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *SqlReview) GetReviewSource() (v int8) {
	return p.ReviewSource
}

func (p *SqlReview) GetReviewContent() (v string) {
	return p.ReviewContent
}

func (p *SqlReview) GetReviewStatus() (v int8) {
	return p.ReviewStatus
}

func (p *SqlReview) GetComment() (v string) {
	return p.Comment
}

func (p *SqlReview) GetCreateUserName() (v string) {
	return p.CreateUserName
}

func (p *SqlReview) GetCreateUserID() (v string) {
	return p.CreateUserID
}

func (p *SqlReview) GetCreateTime() (v int64) {
	return p.CreateTime
}

func (p *SqlReview) GetUpdateTime() (v int64) {
	return p.UpdateTime
}
func (p *SqlReview) SetID(val string) {
	p.ID = val
}
func (p *SqlReview) SetName(val string) {
	p.Name = val
}
func (p *SqlReview) SetDbName(val string) {
	p.DbName = val
}
func (p *SqlReview) SetInstanceType(val string) {
	p.InstanceType = val
}
func (p *SqlReview) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *SqlReview) SetReviewSource(val int8) {
	p.ReviewSource = val
}
func (p *SqlReview) SetReviewContent(val string) {
	p.ReviewContent = val
}
func (p *SqlReview) SetReviewStatus(val int8) {
	p.ReviewStatus = val
}
func (p *SqlReview) SetComment(val string) {
	p.Comment = val
}
func (p *SqlReview) SetCreateUserName(val string) {
	p.CreateUserName = val
}
func (p *SqlReview) SetCreateUserID(val string) {
	p.CreateUserID = val
}
func (p *SqlReview) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *SqlReview) SetUpdateTime(val int64) {
	p.UpdateTime = val
}

var fieldIDToName_SqlReview = map[int16]string{
	1:  "ID",
	2:  "Name",
	3:  "DbName",
	4:  "InstanceType",
	5:  "InstanceID",
	6:  "ReviewSource",
	7:  "ReviewContent",
	8:  "ReviewStatus",
	9:  "Comment",
	10: "CreateUserName",
	11: "CreateUserID",
	12: "CreateTime",
	13: "UpdateTime",
}

func (p *SqlReview) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReview")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetDbName bool = false
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetReviewSource bool = false
	var issetReviewContent bool = false
	var issetReviewStatus bool = false
	var issetComment bool = false
	var issetCreateUserName bool = false
	var issetCreateUserID bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetReviewSource {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetReviewContent {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetReviewStatus {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetComment {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserName {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserID {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReview[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlReview[fieldId]))
}

func (p *SqlReview) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *SqlReview) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *SqlReview) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *SqlReview) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceType = _field
	return nil
}
func (p *SqlReview) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *SqlReview) ReadField6(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewSource = _field
	return nil
}
func (p *SqlReview) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewContent = _field
	return nil
}
func (p *SqlReview) ReadField8(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewStatus = _field
	return nil
}
func (p *SqlReview) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Comment = _field
	return nil
}
func (p *SqlReview) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserName = _field
	return nil
}
func (p *SqlReview) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserID = _field
	return nil
}
func (p *SqlReview) ReadField12(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *SqlReview) ReadField13(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *SqlReview) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReview")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReview"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReview) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReview) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReview) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlReview) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlReview) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlReview) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewSource", thrift.BYTE, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.ReviewSource); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlReview) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewContent", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReviewContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlReview) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewStatus", thrift.BYTE, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.ReviewStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SqlReview) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Comment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SqlReview) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SqlReview) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserID", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SqlReview) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I64, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SqlReview) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.I64, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SqlReview) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReview(%+v)", *p)

}

func (p *SqlReview) DeepEqual(ano *SqlReview) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field6DeepEqual(ano.ReviewSource) {
		return false
	}
	if !p.Field7DeepEqual(ano.ReviewContent) {
		return false
	}
	if !p.Field8DeepEqual(ano.ReviewStatus) {
		return false
	}
	if !p.Field9DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field11DeepEqual(ano.CreateUserID) {
		return false
	}
	if !p.Field12DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field13DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *SqlReview) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceType, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field5DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field6DeepEqual(src int8) bool {

	if p.ReviewSource != src {
		return false
	}
	return true
}
func (p *SqlReview) Field7DeepEqual(src string) bool {

	if strings.Compare(p.ReviewContent, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field8DeepEqual(src int8) bool {

	if p.ReviewStatus != src {
		return false
	}
	return true
}
func (p *SqlReview) Field9DeepEqual(src string) bool {

	if strings.Compare(p.Comment, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field10DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserName, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field11DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserID, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReview) Field12DeepEqual(src int64) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}
func (p *SqlReview) Field13DeepEqual(src int64) bool {

	if p.UpdateTime != src {
		return false
	}
	return true
}

type SqlReviewDetail struct {
	ID                  string         `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	ReviewID            string         `thrift:"ReviewID,2,required" frugal:"2,required,string" json:"ReviewID"`
	SqlStatement        string         `thrift:"SqlStatement,3,required" frugal:"3,required,string" json:"SqlStatement"`
	Status              int8           `thrift:"status,4,required" frugal:"4,required,i8" json:"status"`
	HighRiskCount       int8           `thrift:"HighRiskCount,5,required" frugal:"5,required,i8" json:"HighRiskCount"`
	MiddleRiskCount     int8           `thrift:"MiddleRiskCount,6,required" frugal:"6,required,i8" json:"MiddleRiskCount"`
	LowRiskCount        int8           `thrift:"LowRiskCount,7,required" frugal:"7,required,i8" json:"LowRiskCount"`
	IndexAdviceCount    int8           `thrift:"IndexAdviceCount,8,required" frugal:"8,required,i8" json:"IndexAdviceCount"`
	ReviewDetailContent *ReviewResult_ `thrift:"ReviewDetailContent,9,required" frugal:"9,required,ReviewResult_" json:"ReviewDetailContent"`
	CreateTime          int64          `thrift:"CreateTime,10,required" frugal:"10,required,i64" json:"CreateTime"`
	UpdateTime          int64          `thrift:"UpdateTime,11,required" frugal:"11,required,i64" json:"UpdateTime"`
}

func NewSqlReviewDetail() *SqlReviewDetail {
	return &SqlReviewDetail{}
}

func (p *SqlReviewDetail) InitDefault() {
}

func (p *SqlReviewDetail) GetID() (v string) {
	return p.ID
}

func (p *SqlReviewDetail) GetReviewID() (v string) {
	return p.ReviewID
}

func (p *SqlReviewDetail) GetSqlStatement() (v string) {
	return p.SqlStatement
}

func (p *SqlReviewDetail) GetStatus() (v int8) {
	return p.Status
}

func (p *SqlReviewDetail) GetHighRiskCount() (v int8) {
	return p.HighRiskCount
}

func (p *SqlReviewDetail) GetMiddleRiskCount() (v int8) {
	return p.MiddleRiskCount
}

func (p *SqlReviewDetail) GetLowRiskCount() (v int8) {
	return p.LowRiskCount
}

func (p *SqlReviewDetail) GetIndexAdviceCount() (v int8) {
	return p.IndexAdviceCount
}

var SqlReviewDetail_ReviewDetailContent_DEFAULT *ReviewResult_

func (p *SqlReviewDetail) GetReviewDetailContent() (v *ReviewResult_) {
	if !p.IsSetReviewDetailContent() {
		return SqlReviewDetail_ReviewDetailContent_DEFAULT
	}
	return p.ReviewDetailContent
}

func (p *SqlReviewDetail) GetCreateTime() (v int64) {
	return p.CreateTime
}

func (p *SqlReviewDetail) GetUpdateTime() (v int64) {
	return p.UpdateTime
}
func (p *SqlReviewDetail) SetID(val string) {
	p.ID = val
}
func (p *SqlReviewDetail) SetReviewID(val string) {
	p.ReviewID = val
}
func (p *SqlReviewDetail) SetSqlStatement(val string) {
	p.SqlStatement = val
}
func (p *SqlReviewDetail) SetStatus(val int8) {
	p.Status = val
}
func (p *SqlReviewDetail) SetHighRiskCount(val int8) {
	p.HighRiskCount = val
}
func (p *SqlReviewDetail) SetMiddleRiskCount(val int8) {
	p.MiddleRiskCount = val
}
func (p *SqlReviewDetail) SetLowRiskCount(val int8) {
	p.LowRiskCount = val
}
func (p *SqlReviewDetail) SetIndexAdviceCount(val int8) {
	p.IndexAdviceCount = val
}
func (p *SqlReviewDetail) SetReviewDetailContent(val *ReviewResult_) {
	p.ReviewDetailContent = val
}
func (p *SqlReviewDetail) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *SqlReviewDetail) SetUpdateTime(val int64) {
	p.UpdateTime = val
}

var fieldIDToName_SqlReviewDetail = map[int16]string{
	1:  "ID",
	2:  "ReviewID",
	3:  "SqlStatement",
	4:  "status",
	5:  "HighRiskCount",
	6:  "MiddleRiskCount",
	7:  "LowRiskCount",
	8:  "IndexAdviceCount",
	9:  "ReviewDetailContent",
	10: "CreateTime",
	11: "UpdateTime",
}

func (p *SqlReviewDetail) IsSetReviewDetailContent() bool {
	return p.ReviewDetailContent != nil
}

func (p *SqlReviewDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetReviewID bool = false
	var issetSqlStatement bool = false
	var issetStatus bool = false
	var issetHighRiskCount bool = false
	var issetMiddleRiskCount bool = false
	var issetLowRiskCount bool = false
	var issetIndexAdviceCount bool = false
	var issetReviewDetailContent bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetHighRiskCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetMiddleRiskCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetLowRiskCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexAdviceCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewDetailContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetReviewID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSqlStatement {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetHighRiskCount {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetMiddleRiskCount {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLowRiskCount {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetIndexAdviceCount {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetReviewDetailContent {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SqlReviewDetail[fieldId]))
}

func (p *SqlReviewDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *SqlReviewDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewID = _field
	return nil
}
func (p *SqlReviewDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlStatement = _field
	return nil
}
func (p *SqlReviewDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}
func (p *SqlReviewDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HighRiskCount = _field
	return nil
}
func (p *SqlReviewDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MiddleRiskCount = _field
	return nil
}
func (p *SqlReviewDetail) ReadField7(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LowRiskCount = _field
	return nil
}
func (p *SqlReviewDetail) ReadField8(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexAdviceCount = _field
	return nil
}
func (p *SqlReviewDetail) ReadField9(iprot thrift.TProtocol) error {
	_field := NewReviewResult_()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ReviewDetailContent = _field
	return nil
}
func (p *SqlReviewDetail) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *SqlReviewDetail) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *SqlReviewDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReviewDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReviewDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReviewID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlStatement", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlStatement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.BYTE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HighRiskCount", thrift.BYTE, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.HighRiskCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MiddleRiskCount", thrift.BYTE, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.MiddleRiskCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LowRiskCount", thrift.BYTE, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.LowRiskCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexAdviceCount", thrift.BYTE, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.IndexAdviceCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewDetailContent", thrift.STRUCT, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ReviewDetailContent.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SqlReviewDetail) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SqlReviewDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReviewDetail(%+v)", *p)

}

func (p *SqlReviewDetail) DeepEqual(ano *SqlReviewDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ReviewID) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlStatement) {
		return false
	}
	if !p.Field4DeepEqual(ano.Status) {
		return false
	}
	if !p.Field5DeepEqual(ano.HighRiskCount) {
		return false
	}
	if !p.Field6DeepEqual(ano.MiddleRiskCount) {
		return false
	}
	if !p.Field7DeepEqual(ano.LowRiskCount) {
		return false
	}
	if !p.Field8DeepEqual(ano.IndexAdviceCount) {
		return false
	}
	if !p.Field9DeepEqual(ano.ReviewDetailContent) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field11DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *SqlReviewDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ReviewID, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SqlStatement, src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field4DeepEqual(src int8) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field5DeepEqual(src int8) bool {

	if p.HighRiskCount != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field6DeepEqual(src int8) bool {

	if p.MiddleRiskCount != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field7DeepEqual(src int8) bool {

	if p.LowRiskCount != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field8DeepEqual(src int8) bool {

	if p.IndexAdviceCount != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field9DeepEqual(src *ReviewResult_) bool {

	if !p.ReviewDetailContent.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field10DeepEqual(src int64) bool {

	if p.CreateTime != src {
		return false
	}
	return true
}
func (p *SqlReviewDetail) Field11DeepEqual(src int64) bool {

	if p.UpdateTime != src {
		return false
	}
	return true
}

type DescribeSqlReviewListReq struct {
	PageNumber           *int32                `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize             *int32                `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	SqlReviewSearchParam *SqlReviewSearchParam `thrift:"SqlReviewSearchParam,3,optional" frugal:"3,optional,SqlReviewSearchParam" json:"SqlReviewSearchParam,omitempty"`
	SortBy               *SortBy               `thrift:"SortBy,4,optional" frugal:"4,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy              *OrderByForSqlReview  `thrift:"OrderBy,5,optional" frugal:"5,optional,OrderByForSqlReview" json:"OrderBy,omitempty"`
	ListType             *SqlReviewListType    `thrift:"ListType,6,optional" frugal:"6,optional,SqlReviewListType" json:"ListType,omitempty"`
}

func NewDescribeSqlReviewListReq() *DescribeSqlReviewListReq {
	return &DescribeSqlReviewListReq{}
}

func (p *DescribeSqlReviewListReq) InitDefault() {
}

var DescribeSqlReviewListReq_PageNumber_DEFAULT int32

func (p *DescribeSqlReviewListReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSqlReviewListReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSqlReviewListReq_PageSize_DEFAULT int32

func (p *DescribeSqlReviewListReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSqlReviewListReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSqlReviewListReq_SqlReviewSearchParam_DEFAULT *SqlReviewSearchParam

func (p *DescribeSqlReviewListReq) GetSqlReviewSearchParam() (v *SqlReviewSearchParam) {
	if !p.IsSetSqlReviewSearchParam() {
		return DescribeSqlReviewListReq_SqlReviewSearchParam_DEFAULT
	}
	return p.SqlReviewSearchParam
}

var DescribeSqlReviewListReq_SortBy_DEFAULT SortBy

func (p *DescribeSqlReviewListReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeSqlReviewListReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeSqlReviewListReq_OrderBy_DEFAULT OrderByForSqlReview

func (p *DescribeSqlReviewListReq) GetOrderBy() (v OrderByForSqlReview) {
	if !p.IsSetOrderBy() {
		return DescribeSqlReviewListReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeSqlReviewListReq_ListType_DEFAULT SqlReviewListType

func (p *DescribeSqlReviewListReq) GetListType() (v SqlReviewListType) {
	if !p.IsSetListType() {
		return DescribeSqlReviewListReq_ListType_DEFAULT
	}
	return *p.ListType
}
func (p *DescribeSqlReviewListReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSqlReviewListReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSqlReviewListReq) SetSqlReviewSearchParam(val *SqlReviewSearchParam) {
	p.SqlReviewSearchParam = val
}
func (p *DescribeSqlReviewListReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeSqlReviewListReq) SetOrderBy(val *OrderByForSqlReview) {
	p.OrderBy = val
}
func (p *DescribeSqlReviewListReq) SetListType(val *SqlReviewListType) {
	p.ListType = val
}

var fieldIDToName_DescribeSqlReviewListReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "SqlReviewSearchParam",
	4: "SortBy",
	5: "OrderBy",
	6: "ListType",
}

func (p *DescribeSqlReviewListReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSqlReviewListReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSqlReviewListReq) IsSetSqlReviewSearchParam() bool {
	return p.SqlReviewSearchParam != nil
}

func (p *DescribeSqlReviewListReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeSqlReviewListReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeSqlReviewListReq) IsSetListType() bool {
	return p.ListType != nil
}

func (p *DescribeSqlReviewListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSqlReviewListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSqlReviewListReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewSqlReviewSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SqlReviewSearchParam = _field
	return nil
}
func (p *DescribeSqlReviewListReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeSqlReviewListReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *OrderByForSqlReview
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForSqlReview(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeSqlReviewListReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *SqlReviewListType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlReviewListType(v)
		_field = &tmp
	}
	p.ListType = _field
	return nil
}

func (p *DescribeSqlReviewListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlReviewSearchParam() {
		if err = oprot.WriteFieldBegin("SqlReviewSearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SqlReviewSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetListType() {
		if err = oprot.WriteFieldBegin("ListType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ListType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSqlReviewListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewListReq(%+v)", *p)

}

func (p *DescribeSqlReviewListReq) DeepEqual(ano *DescribeSqlReviewListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlReviewSearchParam) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.ListType) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewListReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewListReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewListReq) Field3DeepEqual(src *SqlReviewSearchParam) bool {

	if !p.SqlReviewSearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSqlReviewListReq) Field4DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewListReq) Field5DeepEqual(src *OrderByForSqlReview) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewListReq) Field6DeepEqual(src *SqlReviewListType) bool {

	if p.ListType == src {
		return true
	} else if p.ListType == nil || src == nil {
		return false
	}
	if *p.ListType != *src {
		return false
	}
	return true
}

type DescribeSqlReviewListResp struct {
	SqlReviewList []*SqlReview `thrift:"SqlReviewList,1,required" frugal:"1,required,list<SqlReview>" json:"SqlReviewList"`
	Total         int64        `thrift:"Total,2,required" frugal:"2,required,i64" json:"Total"`
}

func NewDescribeSqlReviewListResp() *DescribeSqlReviewListResp {
	return &DescribeSqlReviewListResp{}
}

func (p *DescribeSqlReviewListResp) InitDefault() {
}

func (p *DescribeSqlReviewListResp) GetSqlReviewList() (v []*SqlReview) {
	return p.SqlReviewList
}

func (p *DescribeSqlReviewListResp) GetTotal() (v int64) {
	return p.Total
}
func (p *DescribeSqlReviewListResp) SetSqlReviewList(val []*SqlReview) {
	p.SqlReviewList = val
}
func (p *DescribeSqlReviewListResp) SetTotal(val int64) {
	p.Total = val
}

var fieldIDToName_DescribeSqlReviewListResp = map[int16]string{
	1: "SqlReviewList",
	2: "Total",
}

func (p *DescribeSqlReviewListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlReviewList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlReviewList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlReviewListResp[fieldId]))
}

func (p *DescribeSqlReviewListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SqlReview, 0, size)
	values := make([]SqlReview, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlReviewList = _field
	return nil
}
func (p *DescribeSqlReviewListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSqlReviewListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlReviewList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SqlReviewList)); err != nil {
		return err
	}
	for _, v := range p.SqlReviewList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlReviewListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewListResp(%+v)", *p)

}

func (p *DescribeSqlReviewListResp) DeepEqual(ano *DescribeSqlReviewListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlReviewList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewListResp) Field1DeepEqual(src []*SqlReview) bool {

	if len(p.SqlReviewList) != len(src) {
		return false
	}
	for i, v := range p.SqlReviewList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlReviewListResp) Field2DeepEqual(src int64) bool {

	if p.Total != src {
		return false
	}
	return true
}

type SqlReviewSearchParam struct {
	ID             *string `thrift:"ID,1,optional" frugal:"1,optional,string" json:"ID,omitempty"`
	ReviewStatus   *int8   `thrift:"ReviewStatus,2,optional" frugal:"2,optional,i8" json:"ReviewStatus,omitempty"`
	Name           *string `thrift:"Name,3,optional" frugal:"3,optional,string" json:"Name,omitempty"`
	InstanceType   *string `thrift:"InstanceType,4,optional" frugal:"4,optional,string" json:"InstanceType,omitempty"`
	InstanceId     *string `thrift:"InstanceId,5,optional" frugal:"5,optional,string" json:"InstanceId,omitempty"`
	CreateUserId   *string `thrift:"CreateUserId,6,optional" frugal:"6,optional,string" json:"CreateUserId,omitempty"`
	CreateUserName *string `thrift:"CreateUserName,7,optional" frugal:"7,optional,string" json:"CreateUserName,omitempty"`
	Comment        *string `thrift:"Comment,8,optional" frugal:"8,optional,string" json:"Comment,omitempty"`
}

func NewSqlReviewSearchParam() *SqlReviewSearchParam {
	return &SqlReviewSearchParam{}
}

func (p *SqlReviewSearchParam) InitDefault() {
}

var SqlReviewSearchParam_ID_DEFAULT string

func (p *SqlReviewSearchParam) GetID() (v string) {
	if !p.IsSetID() {
		return SqlReviewSearchParam_ID_DEFAULT
	}
	return *p.ID
}

var SqlReviewSearchParam_ReviewStatus_DEFAULT int8

func (p *SqlReviewSearchParam) GetReviewStatus() (v int8) {
	if !p.IsSetReviewStatus() {
		return SqlReviewSearchParam_ReviewStatus_DEFAULT
	}
	return *p.ReviewStatus
}

var SqlReviewSearchParam_Name_DEFAULT string

func (p *SqlReviewSearchParam) GetName() (v string) {
	if !p.IsSetName() {
		return SqlReviewSearchParam_Name_DEFAULT
	}
	return *p.Name
}

var SqlReviewSearchParam_InstanceType_DEFAULT string

func (p *SqlReviewSearchParam) GetInstanceType() (v string) {
	if !p.IsSetInstanceType() {
		return SqlReviewSearchParam_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var SqlReviewSearchParam_InstanceId_DEFAULT string

func (p *SqlReviewSearchParam) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return SqlReviewSearchParam_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var SqlReviewSearchParam_CreateUserId_DEFAULT string

func (p *SqlReviewSearchParam) GetCreateUserId() (v string) {
	if !p.IsSetCreateUserId() {
		return SqlReviewSearchParam_CreateUserId_DEFAULT
	}
	return *p.CreateUserId
}

var SqlReviewSearchParam_CreateUserName_DEFAULT string

func (p *SqlReviewSearchParam) GetCreateUserName() (v string) {
	if !p.IsSetCreateUserName() {
		return SqlReviewSearchParam_CreateUserName_DEFAULT
	}
	return *p.CreateUserName
}

var SqlReviewSearchParam_Comment_DEFAULT string

func (p *SqlReviewSearchParam) GetComment() (v string) {
	if !p.IsSetComment() {
		return SqlReviewSearchParam_Comment_DEFAULT
	}
	return *p.Comment
}
func (p *SqlReviewSearchParam) SetID(val *string) {
	p.ID = val
}
func (p *SqlReviewSearchParam) SetReviewStatus(val *int8) {
	p.ReviewStatus = val
}
func (p *SqlReviewSearchParam) SetName(val *string) {
	p.Name = val
}
func (p *SqlReviewSearchParam) SetInstanceType(val *string) {
	p.InstanceType = val
}
func (p *SqlReviewSearchParam) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *SqlReviewSearchParam) SetCreateUserId(val *string) {
	p.CreateUserId = val
}
func (p *SqlReviewSearchParam) SetCreateUserName(val *string) {
	p.CreateUserName = val
}
func (p *SqlReviewSearchParam) SetComment(val *string) {
	p.Comment = val
}

var fieldIDToName_SqlReviewSearchParam = map[int16]string{
	1: "ID",
	2: "ReviewStatus",
	3: "Name",
	4: "InstanceType",
	5: "InstanceId",
	6: "CreateUserId",
	7: "CreateUserName",
	8: "Comment",
}

func (p *SqlReviewSearchParam) IsSetID() bool {
	return p.ID != nil
}

func (p *SqlReviewSearchParam) IsSetReviewStatus() bool {
	return p.ReviewStatus != nil
}

func (p *SqlReviewSearchParam) IsSetName() bool {
	return p.Name != nil
}

func (p *SqlReviewSearchParam) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *SqlReviewSearchParam) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *SqlReviewSearchParam) IsSetCreateUserId() bool {
	return p.CreateUserId != nil
}

func (p *SqlReviewSearchParam) IsSetCreateUserName() bool {
	return p.CreateUserName != nil
}

func (p *SqlReviewSearchParam) IsSetComment() bool {
	return p.Comment != nil
}

func (p *SqlReviewSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlReviewSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ID = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReviewStatus = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceType = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserId = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserName = _field
	return nil
}
func (p *SqlReviewSearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}

func (p *SqlReviewSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReviewSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetID() {
		if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetReviewStatus() {
		if err = oprot.WriteFieldBegin("ReviewStatus", thrift.BYTE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.ReviewStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserId() {
		if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserName() {
		if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SqlReviewSearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SqlReviewSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReviewSearchParam(%+v)", *p)

}

func (p *SqlReviewSearchParam) DeepEqual(ano *SqlReviewSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ReviewStatus) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field8DeepEqual(ano.Comment) {
		return false
	}
	return true
}

func (p *SqlReviewSearchParam) Field1DeepEqual(src *string) bool {

	if p.ID == src {
		return true
	} else if p.ID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ID, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field2DeepEqual(src *int8) bool {

	if p.ReviewStatus == src {
		return true
	} else if p.ReviewStatus == nil || src == nil {
		return false
	}
	if *p.ReviewStatus != *src {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field3DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field4DeepEqual(src *string) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceType, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field5DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field6DeepEqual(src *string) bool {

	if p.CreateUserId == src {
		return true
	} else if p.CreateUserId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserId, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field7DeepEqual(src *string) bool {

	if p.CreateUserName == src {
		return true
	} else if p.CreateUserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserName, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewSearchParam) Field8DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}

type AddSqlReviewReq struct {
	Name           string       `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	InstanceType   InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	InstanceID     string       `thrift:"InstanceID,3,required" frugal:"3,required,string" json:"InstanceID"`
	DbName         string       `thrift:"DbName,4,required" frugal:"4,required,string" json:"DbName"`
	ReviewSource   int8         `thrift:"ReviewSource,5,required" frugal:"5,required,i8" json:"ReviewSource"`
	ReviewContent  string       `thrift:"ReviewContent,6,required" frugal:"6,required,string" json:"ReviewContent"`
	CreateUserName string       `thrift:"CreateUserName,7,required" frugal:"7,required,string" json:"CreateUserName"`
	Comment        *string      `thrift:"Comment,8,optional" frugal:"8,optional,string" json:"Comment,omitempty"`
}

func NewAddSqlReviewReq() *AddSqlReviewReq {
	return &AddSqlReviewReq{}
}

func (p *AddSqlReviewReq) InitDefault() {
}

func (p *AddSqlReviewReq) GetName() (v string) {
	return p.Name
}

func (p *AddSqlReviewReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *AddSqlReviewReq) GetInstanceID() (v string) {
	return p.InstanceID
}

func (p *AddSqlReviewReq) GetDbName() (v string) {
	return p.DbName
}

func (p *AddSqlReviewReq) GetReviewSource() (v int8) {
	return p.ReviewSource
}

func (p *AddSqlReviewReq) GetReviewContent() (v string) {
	return p.ReviewContent
}

func (p *AddSqlReviewReq) GetCreateUserName() (v string) {
	return p.CreateUserName
}

var AddSqlReviewReq_Comment_DEFAULT string

func (p *AddSqlReviewReq) GetComment() (v string) {
	if !p.IsSetComment() {
		return AddSqlReviewReq_Comment_DEFAULT
	}
	return *p.Comment
}
func (p *AddSqlReviewReq) SetName(val string) {
	p.Name = val
}
func (p *AddSqlReviewReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *AddSqlReviewReq) SetInstanceID(val string) {
	p.InstanceID = val
}
func (p *AddSqlReviewReq) SetDbName(val string) {
	p.DbName = val
}
func (p *AddSqlReviewReq) SetReviewSource(val int8) {
	p.ReviewSource = val
}
func (p *AddSqlReviewReq) SetReviewContent(val string) {
	p.ReviewContent = val
}
func (p *AddSqlReviewReq) SetCreateUserName(val string) {
	p.CreateUserName = val
}
func (p *AddSqlReviewReq) SetComment(val *string) {
	p.Comment = val
}

var fieldIDToName_AddSqlReviewReq = map[int16]string{
	1: "Name",
	2: "InstanceType",
	3: "InstanceID",
	4: "DbName",
	5: "ReviewSource",
	6: "ReviewContent",
	7: "CreateUserName",
	8: "Comment",
}

func (p *AddSqlReviewReq) IsSetComment() bool {
	return p.Comment != nil
}

func (p *AddSqlReviewReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddSqlReviewReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetInstanceType bool = false
	var issetInstanceID bool = false
	var issetDbName bool = false
	var issetReviewSource bool = false
	var issetReviewContent bool = false
	var issetCreateUserName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetReviewContent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetReviewSource {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetReviewContent {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserName {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddSqlReviewReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AddSqlReviewReq[fieldId]))
}

func (p *AddSqlReviewReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewSource = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReviewContent = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserName = _field
	return nil
}
func (p *AddSqlReviewReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}

func (p *AddSqlReviewReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddSqlReviewReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AddSqlReviewReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewSource", thrift.BYTE, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.ReviewSource); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReviewContent", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReviewContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AddSqlReviewReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AddSqlReviewReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddSqlReviewReq(%+v)", *p)

}

func (p *AddSqlReviewReq) DeepEqual(ano *AddSqlReviewReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field4DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ReviewSource) {
		return false
	}
	if !p.Field6DeepEqual(ano.ReviewContent) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field8DeepEqual(ano.Comment) {
		return false
	}
	return true
}

func (p *AddSqlReviewReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceID, src) != 0 {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field5DeepEqual(src int8) bool {

	if p.ReviewSource != src {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.ReviewContent, src) != 0 {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field7DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserName, src) != 0 {
		return false
	}
	return true
}
func (p *AddSqlReviewReq) Field8DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}

type AddSqlReviewResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewAddSqlReviewResp() *AddSqlReviewResp {
	return &AddSqlReviewResp{}
}

func (p *AddSqlReviewResp) InitDefault() {
}

func (p *AddSqlReviewResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *AddSqlReviewResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_AddSqlReviewResp = map[int16]string{
	1: "Success",
}

func (p *AddSqlReviewResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddSqlReviewResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddSqlReviewResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AddSqlReviewResp[fieldId]))
}

func (p *AddSqlReviewResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *AddSqlReviewResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddSqlReviewResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AddSqlReviewResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddSqlReviewResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AddSqlReviewResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddSqlReviewResp(%+v)", *p)

}

func (p *AddSqlReviewResp) DeepEqual(ano *AddSqlReviewResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AddSqlReviewResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

type SubmitApproveSqlReviewReq struct {
	SqlReviewID string `thrift:"SqlReviewID,1,required" frugal:"1,required,string" json:"SqlReviewID"`
}

func NewSubmitApproveSqlReviewReq() *SubmitApproveSqlReviewReq {
	return &SubmitApproveSqlReviewReq{}
}

func (p *SubmitApproveSqlReviewReq) InitDefault() {
}

func (p *SubmitApproveSqlReviewReq) GetSqlReviewID() (v string) {
	return p.SqlReviewID
}
func (p *SubmitApproveSqlReviewReq) SetSqlReviewID(val string) {
	p.SqlReviewID = val
}

var fieldIDToName_SubmitApproveSqlReviewReq = map[int16]string{
	1: "SqlReviewID",
}

func (p *SubmitApproveSqlReviewReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitApproveSqlReviewReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlReviewID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlReviewID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitApproveSqlReviewReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SubmitApproveSqlReviewReq[fieldId]))
}

func (p *SubmitApproveSqlReviewReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlReviewID = _field
	return nil
}

func (p *SubmitApproveSqlReviewReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitApproveSqlReviewReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitApproveSqlReviewReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SubmitApproveSqlReviewReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlReviewID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlReviewID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SubmitApproveSqlReviewReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitApproveSqlReviewReq(%+v)", *p)

}

func (p *SubmitApproveSqlReviewReq) DeepEqual(ano *SubmitApproveSqlReviewReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlReviewID) {
		return false
	}
	return true
}

func (p *SubmitApproveSqlReviewReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlReviewID, src) != 0 {
		return false
	}
	return true
}

type SubmitApproveSqlReviewResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewSubmitApproveSqlReviewResp() *SubmitApproveSqlReviewResp {
	return &SubmitApproveSqlReviewResp{}
}

func (p *SubmitApproveSqlReviewResp) InitDefault() {
}

func (p *SubmitApproveSqlReviewResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *SubmitApproveSqlReviewResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_SubmitApproveSqlReviewResp = map[int16]string{
	1: "Success",
}

func (p *SubmitApproveSqlReviewResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitApproveSqlReviewResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitApproveSqlReviewResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SubmitApproveSqlReviewResp[fieldId]))
}

func (p *SubmitApproveSqlReviewResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *SubmitApproveSqlReviewResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitApproveSqlReviewResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitApproveSqlReviewResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SubmitApproveSqlReviewResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SubmitApproveSqlReviewResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitApproveSqlReviewResp(%+v)", *p)

}

func (p *SubmitApproveSqlReviewResp) DeepEqual(ano *SubmitApproveSqlReviewResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *SubmitApproveSqlReviewResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

type DescribeSqlReviewDetailListReq struct {
	PageNumber                 *int32                      `thrift:"PageNumber,1,optional" frugal:"1,optional,i32" json:"PageNumber,omitempty"`
	PageSize                   *int32                      `thrift:"PageSize,2,optional" frugal:"2,optional,i32" json:"PageSize,omitempty"`
	SqlReviewDetailSearchParam *SqlReviewDetailSearchParam `thrift:"SqlReviewDetailSearchParam,3,optional" frugal:"3,optional,SqlReviewDetailSearchParam" json:"SqlReviewDetailSearchParam,omitempty"`
}

func NewDescribeSqlReviewDetailListReq() *DescribeSqlReviewDetailListReq {
	return &DescribeSqlReviewDetailListReq{}
}

func (p *DescribeSqlReviewDetailListReq) InitDefault() {
}

var DescribeSqlReviewDetailListReq_PageNumber_DEFAULT int32

func (p *DescribeSqlReviewDetailListReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSqlReviewDetailListReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSqlReviewDetailListReq_PageSize_DEFAULT int32

func (p *DescribeSqlReviewDetailListReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSqlReviewDetailListReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSqlReviewDetailListReq_SqlReviewDetailSearchParam_DEFAULT *SqlReviewDetailSearchParam

func (p *DescribeSqlReviewDetailListReq) GetSqlReviewDetailSearchParam() (v *SqlReviewDetailSearchParam) {
	if !p.IsSetSqlReviewDetailSearchParam() {
		return DescribeSqlReviewDetailListReq_SqlReviewDetailSearchParam_DEFAULT
	}
	return p.SqlReviewDetailSearchParam
}
func (p *DescribeSqlReviewDetailListReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSqlReviewDetailListReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSqlReviewDetailListReq) SetSqlReviewDetailSearchParam(val *SqlReviewDetailSearchParam) {
	p.SqlReviewDetailSearchParam = val
}

var fieldIDToName_DescribeSqlReviewDetailListReq = map[int16]string{
	1: "PageNumber",
	2: "PageSize",
	3: "SqlReviewDetailSearchParam",
}

func (p *DescribeSqlReviewDetailListReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSqlReviewDetailListReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSqlReviewDetailListReq) IsSetSqlReviewDetailSearchParam() bool {
	return p.SqlReviewDetailSearchParam != nil
}

func (p *DescribeSqlReviewDetailListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSqlReviewDetailListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSqlReviewDetailListReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewSqlReviewDetailSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SqlReviewDetailSearchParam = _field
	return nil
}

func (p *DescribeSqlReviewDetailListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewDetailListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlReviewDetailSearchParam() {
		if err = oprot.WriteFieldBegin("SqlReviewDetailSearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SqlReviewDetailSearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewDetailListReq(%+v)", *p)

}

func (p *DescribeSqlReviewDetailListReq) DeepEqual(ano *DescribeSqlReviewDetailListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlReviewDetailSearchParam) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewDetailListReq) Field1DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailListReq) Field2DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailListReq) Field3DeepEqual(src *SqlReviewDetailSearchParam) bool {

	if !p.SqlReviewDetailSearchParam.DeepEqual(src) {
		return false
	}
	return true
}

type SqlReviewDetailSearchParam struct {
	ReviewID   *string `thrift:"ReviewID,1,optional" frugal:"1,optional,string" json:"ReviewID,omitempty"`
	RuleLevel  *string `thrift:"RuleLevel,2,optional" frugal:"2,optional,string" json:"RuleLevel,omitempty"`
	ActionName *string `thrift:"ActionName,3,optional" frugal:"3,optional,string" json:"ActionName,omitempty"`
}

func NewSqlReviewDetailSearchParam() *SqlReviewDetailSearchParam {
	return &SqlReviewDetailSearchParam{}
}

func (p *SqlReviewDetailSearchParam) InitDefault() {
}

var SqlReviewDetailSearchParam_ReviewID_DEFAULT string

func (p *SqlReviewDetailSearchParam) GetReviewID() (v string) {
	if !p.IsSetReviewID() {
		return SqlReviewDetailSearchParam_ReviewID_DEFAULT
	}
	return *p.ReviewID
}

var SqlReviewDetailSearchParam_RuleLevel_DEFAULT string

func (p *SqlReviewDetailSearchParam) GetRuleLevel() (v string) {
	if !p.IsSetRuleLevel() {
		return SqlReviewDetailSearchParam_RuleLevel_DEFAULT
	}
	return *p.RuleLevel
}

var SqlReviewDetailSearchParam_ActionName_DEFAULT string

func (p *SqlReviewDetailSearchParam) GetActionName() (v string) {
	if !p.IsSetActionName() {
		return SqlReviewDetailSearchParam_ActionName_DEFAULT
	}
	return *p.ActionName
}
func (p *SqlReviewDetailSearchParam) SetReviewID(val *string) {
	p.ReviewID = val
}
func (p *SqlReviewDetailSearchParam) SetRuleLevel(val *string) {
	p.RuleLevel = val
}
func (p *SqlReviewDetailSearchParam) SetActionName(val *string) {
	p.ActionName = val
}

var fieldIDToName_SqlReviewDetailSearchParam = map[int16]string{
	1: "ReviewID",
	2: "RuleLevel",
	3: "ActionName",
}

func (p *SqlReviewDetailSearchParam) IsSetReviewID() bool {
	return p.ReviewID != nil
}

func (p *SqlReviewDetailSearchParam) IsSetRuleLevel() bool {
	return p.RuleLevel != nil
}

func (p *SqlReviewDetailSearchParam) IsSetActionName() bool {
	return p.ActionName != nil
}

func (p *SqlReviewDetailSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewDetailSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewDetailSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlReviewDetailSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReviewID = _field
	return nil
}
func (p *SqlReviewDetailSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RuleLevel = _field
	return nil
}
func (p *SqlReviewDetailSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ActionName = _field
	return nil
}

func (p *SqlReviewDetailSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewDetailSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReviewDetailSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReviewDetailSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetReviewID() {
		if err = oprot.WriteFieldBegin("ReviewID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReviewID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReviewDetailSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleLevel() {
		if err = oprot.WriteFieldBegin("RuleLevel", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RuleLevel); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReviewDetailSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionName() {
		if err = oprot.WriteFieldBegin("ActionName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ActionName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlReviewDetailSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReviewDetailSearchParam(%+v)", *p)

}

func (p *SqlReviewDetailSearchParam) DeepEqual(ano *SqlReviewDetailSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ReviewID) {
		return false
	}
	if !p.Field2DeepEqual(ano.RuleLevel) {
		return false
	}
	if !p.Field3DeepEqual(ano.ActionName) {
		return false
	}
	return true
}

func (p *SqlReviewDetailSearchParam) Field1DeepEqual(src *string) bool {

	if p.ReviewID == src {
		return true
	} else if p.ReviewID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReviewID, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewDetailSearchParam) Field2DeepEqual(src *string) bool {

	if p.RuleLevel == src {
		return true
	} else if p.RuleLevel == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RuleLevel, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewDetailSearchParam) Field3DeepEqual(src *string) bool {

	if p.ActionName == src {
		return true
	} else if p.ActionName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ActionName, *src) != 0 {
		return false
	}
	return true
}

type DescribeSqlReviewDetailListResp struct {
	SqlReviewDetailList []*SqlReviewDetail `thrift:"SqlReviewDetailList,1,required" frugal:"1,required,list<SqlReviewDetail>" json:"SqlReviewDetailList"`
	Total               int64              `thrift:"Total,2,required" frugal:"2,required,i64" json:"Total"`
}

func NewDescribeSqlReviewDetailListResp() *DescribeSqlReviewDetailListResp {
	return &DescribeSqlReviewDetailListResp{}
}

func (p *DescribeSqlReviewDetailListResp) InitDefault() {
}

func (p *DescribeSqlReviewDetailListResp) GetSqlReviewDetailList() (v []*SqlReviewDetail) {
	return p.SqlReviewDetailList
}

func (p *DescribeSqlReviewDetailListResp) GetTotal() (v int64) {
	return p.Total
}
func (p *DescribeSqlReviewDetailListResp) SetSqlReviewDetailList(val []*SqlReviewDetail) {
	p.SqlReviewDetailList = val
}
func (p *DescribeSqlReviewDetailListResp) SetTotal(val int64) {
	p.Total = val
}

var fieldIDToName_DescribeSqlReviewDetailListResp = map[int16]string{
	1: "SqlReviewDetailList",
	2: "Total",
}

func (p *DescribeSqlReviewDetailListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailListResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlReviewDetailList bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlReviewDetailList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlReviewDetailList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlReviewDetailListResp[fieldId]))
}

func (p *DescribeSqlReviewDetailListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SqlReviewDetail, 0, size)
	values := make([]SqlReviewDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlReviewDetailList = _field
	return nil
}
func (p *DescribeSqlReviewDetailListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSqlReviewDetailListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewDetailListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlReviewDetailList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SqlReviewDetailList)); err != nil {
		return err
	}
	for _, v := range p.SqlReviewDetailList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewDetailListResp(%+v)", *p)

}

func (p *DescribeSqlReviewDetailListResp) DeepEqual(ano *DescribeSqlReviewDetailListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlReviewDetailList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewDetailListResp) Field1DeepEqual(src []*SqlReviewDetail) bool {

	if len(p.SqlReviewDetailList) != len(src) {
		return false
	}
	for i, v := range p.SqlReviewDetailList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlReviewDetailListResp) Field2DeepEqual(src int64) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeSqlReviewDetailSummaryResultReq struct {
	ReviewID *string `thrift:"ReviewID,1,optional" frugal:"1,optional,string" json:"ReviewID,omitempty"`
}

func NewDescribeSqlReviewDetailSummaryResultReq() *DescribeSqlReviewDetailSummaryResultReq {
	return &DescribeSqlReviewDetailSummaryResultReq{}
}

func (p *DescribeSqlReviewDetailSummaryResultReq) InitDefault() {
}

var DescribeSqlReviewDetailSummaryResultReq_ReviewID_DEFAULT string

func (p *DescribeSqlReviewDetailSummaryResultReq) GetReviewID() (v string) {
	if !p.IsSetReviewID() {
		return DescribeSqlReviewDetailSummaryResultReq_ReviewID_DEFAULT
	}
	return *p.ReviewID
}
func (p *DescribeSqlReviewDetailSummaryResultReq) SetReviewID(val *string) {
	p.ReviewID = val
}

var fieldIDToName_DescribeSqlReviewDetailSummaryResultReq = map[int16]string{
	1: "ReviewID",
}

func (p *DescribeSqlReviewDetailSummaryResultReq) IsSetReviewID() bool {
	return p.ReviewID != nil
}

func (p *DescribeSqlReviewDetailSummaryResultReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailSummaryResultReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailSummaryResultReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReviewID = _field
	return nil
}

func (p *DescribeSqlReviewDetailSummaryResultReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailSummaryResultReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewDetailSummaryResultReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetReviewID() {
		if err = oprot.WriteFieldBegin("ReviewID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReviewID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewDetailSummaryResultReq(%+v)", *p)

}

func (p *DescribeSqlReviewDetailSummaryResultReq) DeepEqual(ano *DescribeSqlReviewDetailSummaryResultReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ReviewID) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewDetailSummaryResultReq) Field1DeepEqual(src *string) bool {

	if p.ReviewID == src {
		return true
	} else if p.ReviewID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReviewID, *src) != 0 {
		return false
	}
	return true
}

type DescribeSqlReviewDetailSummaryResultResp struct {
	HighRiskCount      *int8                 `thrift:"HighRiskCount,1,optional" frugal:"1,optional,i8" json:"HighRiskCount,omitempty"`
	MiddleRiskCount    *int8                 `thrift:"MiddleRiskCount,2,optional" frugal:"2,optional,i8" json:"MiddleRiskCount,omitempty"`
	LowRiskCount       *int8                 `thrift:"LowRiskCount,3,optional" frugal:"3,optional,i8" json:"LowRiskCount,omitempty"`
	IndexAdviceCount   int8                  `thrift:"IndexAdviceCount,4,required" frugal:"4,required,i8" json:"IndexAdviceCount"`
	HighRiskContent    []*CheckResult_       `thrift:"HighRiskContent,5,optional" frugal:"5,optional,list<CheckResult_>" json:"HighRiskContent,omitempty"`
	MiddleRiskContent  []*CheckResult_       `thrift:"MiddleRiskContent,6,optional" frugal:"6,optional,list<CheckResult_>" json:"MiddleRiskContent,omitempty"`
	LowRiskContent     []*CheckResult_       `thrift:"LowRiskContent,7,optional" frugal:"7,optional,list<CheckResult_>" json:"LowRiskContent,omitempty"`
	IndexAdviceContent []*IndexAdviceResult_ `thrift:"IndexAdviceContent,8,optional" frugal:"8,optional,list<IndexAdviceResult_>" json:"IndexAdviceContent,omitempty"`
}

func NewDescribeSqlReviewDetailSummaryResultResp() *DescribeSqlReviewDetailSummaryResultResp {
	return &DescribeSqlReviewDetailSummaryResultResp{}
}

func (p *DescribeSqlReviewDetailSummaryResultResp) InitDefault() {
}

var DescribeSqlReviewDetailSummaryResultResp_HighRiskCount_DEFAULT int8

func (p *DescribeSqlReviewDetailSummaryResultResp) GetHighRiskCount() (v int8) {
	if !p.IsSetHighRiskCount() {
		return DescribeSqlReviewDetailSummaryResultResp_HighRiskCount_DEFAULT
	}
	return *p.HighRiskCount
}

var DescribeSqlReviewDetailSummaryResultResp_MiddleRiskCount_DEFAULT int8

func (p *DescribeSqlReviewDetailSummaryResultResp) GetMiddleRiskCount() (v int8) {
	if !p.IsSetMiddleRiskCount() {
		return DescribeSqlReviewDetailSummaryResultResp_MiddleRiskCount_DEFAULT
	}
	return *p.MiddleRiskCount
}

var DescribeSqlReviewDetailSummaryResultResp_LowRiskCount_DEFAULT int8

func (p *DescribeSqlReviewDetailSummaryResultResp) GetLowRiskCount() (v int8) {
	if !p.IsSetLowRiskCount() {
		return DescribeSqlReviewDetailSummaryResultResp_LowRiskCount_DEFAULT
	}
	return *p.LowRiskCount
}

func (p *DescribeSqlReviewDetailSummaryResultResp) GetIndexAdviceCount() (v int8) {
	return p.IndexAdviceCount
}

var DescribeSqlReviewDetailSummaryResultResp_HighRiskContent_DEFAULT []*CheckResult_

func (p *DescribeSqlReviewDetailSummaryResultResp) GetHighRiskContent() (v []*CheckResult_) {
	if !p.IsSetHighRiskContent() {
		return DescribeSqlReviewDetailSummaryResultResp_HighRiskContent_DEFAULT
	}
	return p.HighRiskContent
}

var DescribeSqlReviewDetailSummaryResultResp_MiddleRiskContent_DEFAULT []*CheckResult_

func (p *DescribeSqlReviewDetailSummaryResultResp) GetMiddleRiskContent() (v []*CheckResult_) {
	if !p.IsSetMiddleRiskContent() {
		return DescribeSqlReviewDetailSummaryResultResp_MiddleRiskContent_DEFAULT
	}
	return p.MiddleRiskContent
}

var DescribeSqlReviewDetailSummaryResultResp_LowRiskContent_DEFAULT []*CheckResult_

func (p *DescribeSqlReviewDetailSummaryResultResp) GetLowRiskContent() (v []*CheckResult_) {
	if !p.IsSetLowRiskContent() {
		return DescribeSqlReviewDetailSummaryResultResp_LowRiskContent_DEFAULT
	}
	return p.LowRiskContent
}

var DescribeSqlReviewDetailSummaryResultResp_IndexAdviceContent_DEFAULT []*IndexAdviceResult_

func (p *DescribeSqlReviewDetailSummaryResultResp) GetIndexAdviceContent() (v []*IndexAdviceResult_) {
	if !p.IsSetIndexAdviceContent() {
		return DescribeSqlReviewDetailSummaryResultResp_IndexAdviceContent_DEFAULT
	}
	return p.IndexAdviceContent
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetHighRiskCount(val *int8) {
	p.HighRiskCount = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetMiddleRiskCount(val *int8) {
	p.MiddleRiskCount = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetLowRiskCount(val *int8) {
	p.LowRiskCount = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetIndexAdviceCount(val int8) {
	p.IndexAdviceCount = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetHighRiskContent(val []*CheckResult_) {
	p.HighRiskContent = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetMiddleRiskContent(val []*CheckResult_) {
	p.MiddleRiskContent = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetLowRiskContent(val []*CheckResult_) {
	p.LowRiskContent = val
}
func (p *DescribeSqlReviewDetailSummaryResultResp) SetIndexAdviceContent(val []*IndexAdviceResult_) {
	p.IndexAdviceContent = val
}

var fieldIDToName_DescribeSqlReviewDetailSummaryResultResp = map[int16]string{
	1: "HighRiskCount",
	2: "MiddleRiskCount",
	3: "LowRiskCount",
	4: "IndexAdviceCount",
	5: "HighRiskContent",
	6: "MiddleRiskContent",
	7: "LowRiskContent",
	8: "IndexAdviceContent",
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetHighRiskCount() bool {
	return p.HighRiskCount != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetMiddleRiskCount() bool {
	return p.MiddleRiskCount != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetLowRiskCount() bool {
	return p.LowRiskCount != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetHighRiskContent() bool {
	return p.HighRiskContent != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetMiddleRiskContent() bool {
	return p.MiddleRiskContent != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetLowRiskContent() bool {
	return p.LowRiskContent != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) IsSetIndexAdviceContent() bool {
	return p.IndexAdviceContent != nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailSummaryResultResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIndexAdviceCount bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetIndexAdviceCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIndexAdviceCount {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlReviewDetailSummaryResultResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlReviewDetailSummaryResultResp[fieldId]))
}

func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField1(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HighRiskCount = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MiddleRiskCount = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField3(iprot thrift.TProtocol) error {

	var _field *int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LowRiskCount = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IndexAdviceCount = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.HighRiskContent = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MiddleRiskContent = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.LowRiskContent = _field
	return nil
}
func (p *DescribeSqlReviewDetailSummaryResultResp) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IndexAdviceContent = _field
	return nil
}

func (p *DescribeSqlReviewDetailSummaryResultResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlReviewDetailSummaryResultResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlReviewDetailSummaryResultResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetHighRiskCount() {
		if err = oprot.WriteFieldBegin("HighRiskCount", thrift.BYTE, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.HighRiskCount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMiddleRiskCount() {
		if err = oprot.WriteFieldBegin("MiddleRiskCount", thrift.BYTE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.MiddleRiskCount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLowRiskCount() {
		if err = oprot.WriteFieldBegin("LowRiskCount", thrift.BYTE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteByte(*p.LowRiskCount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IndexAdviceCount", thrift.BYTE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.IndexAdviceCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetHighRiskContent() {
		if err = oprot.WriteFieldBegin("HighRiskContent", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.HighRiskContent)); err != nil {
			return err
		}
		for _, v := range p.HighRiskContent {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetMiddleRiskContent() {
		if err = oprot.WriteFieldBegin("MiddleRiskContent", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MiddleRiskContent)); err != nil {
			return err
		}
		for _, v := range p.MiddleRiskContent {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetLowRiskContent() {
		if err = oprot.WriteFieldBegin("LowRiskContent", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.LowRiskContent)); err != nil {
			return err
		}
		for _, v := range p.LowRiskContent {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetIndexAdviceContent() {
		if err = oprot.WriteFieldBegin("IndexAdviceContent", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.IndexAdviceContent)); err != nil {
			return err
		}
		for _, v := range p.IndexAdviceContent {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSqlReviewDetailSummaryResultResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlReviewDetailSummaryResultResp(%+v)", *p)

}

func (p *DescribeSqlReviewDetailSummaryResultResp) DeepEqual(ano *DescribeSqlReviewDetailSummaryResultResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HighRiskCount) {
		return false
	}
	if !p.Field2DeepEqual(ano.MiddleRiskCount) {
		return false
	}
	if !p.Field3DeepEqual(ano.LowRiskCount) {
		return false
	}
	if !p.Field4DeepEqual(ano.IndexAdviceCount) {
		return false
	}
	if !p.Field5DeepEqual(ano.HighRiskContent) {
		return false
	}
	if !p.Field6DeepEqual(ano.MiddleRiskContent) {
		return false
	}
	if !p.Field7DeepEqual(ano.LowRiskContent) {
		return false
	}
	if !p.Field8DeepEqual(ano.IndexAdviceContent) {
		return false
	}
	return true
}

func (p *DescribeSqlReviewDetailSummaryResultResp) Field1DeepEqual(src *int8) bool {

	if p.HighRiskCount == src {
		return true
	} else if p.HighRiskCount == nil || src == nil {
		return false
	}
	if *p.HighRiskCount != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field2DeepEqual(src *int8) bool {

	if p.MiddleRiskCount == src {
		return true
	} else if p.MiddleRiskCount == nil || src == nil {
		return false
	}
	if *p.MiddleRiskCount != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field3DeepEqual(src *int8) bool {

	if p.LowRiskCount == src {
		return true
	} else if p.LowRiskCount == nil || src == nil {
		return false
	}
	if *p.LowRiskCount != *src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field4DeepEqual(src int8) bool {

	if p.IndexAdviceCount != src {
		return false
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field5DeepEqual(src []*CheckResult_) bool {

	if len(p.HighRiskContent) != len(src) {
		return false
	}
	for i, v := range p.HighRiskContent {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field6DeepEqual(src []*CheckResult_) bool {

	if len(p.MiddleRiskContent) != len(src) {
		return false
	}
	for i, v := range p.MiddleRiskContent {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field7DeepEqual(src []*CheckResult_) bool {

	if len(p.LowRiskContent) != len(src) {
		return false
	}
	for i, v := range p.LowRiskContent {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlReviewDetailSummaryResultResp) Field8DeepEqual(src []*IndexAdviceResult_) bool {

	if len(p.IndexAdviceContent) != len(src) {
		return false
	}
	for i, v := range p.IndexAdviceContent {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SqlReviewBySingleReq struct {
	InstanceID   *string `thrift:"InstanceID,1,optional" frugal:"1,optional,string" json:"InstanceID,omitempty"`
	DbName       *string `thrift:"DbName,2,optional" frugal:"2,optional,string" json:"DbName,omitempty"`
	InstanceType *string `thrift:"InstanceType,3,optional" frugal:"3,optional,string" json:"InstanceType,omitempty"`
	SqlStatement *string `thrift:"sqlStatement,4,optional" frugal:"4,optional,string" json:"sqlStatement,omitempty"`
}

func NewSqlReviewBySingleReq() *SqlReviewBySingleReq {
	return &SqlReviewBySingleReq{}
}

func (p *SqlReviewBySingleReq) InitDefault() {
}

var SqlReviewBySingleReq_InstanceID_DEFAULT string

func (p *SqlReviewBySingleReq) GetInstanceID() (v string) {
	if !p.IsSetInstanceID() {
		return SqlReviewBySingleReq_InstanceID_DEFAULT
	}
	return *p.InstanceID
}

var SqlReviewBySingleReq_DbName_DEFAULT string

func (p *SqlReviewBySingleReq) GetDbName() (v string) {
	if !p.IsSetDbName() {
		return SqlReviewBySingleReq_DbName_DEFAULT
	}
	return *p.DbName
}

var SqlReviewBySingleReq_InstanceType_DEFAULT string

func (p *SqlReviewBySingleReq) GetInstanceType() (v string) {
	if !p.IsSetInstanceType() {
		return SqlReviewBySingleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var SqlReviewBySingleReq_SqlStatement_DEFAULT string

func (p *SqlReviewBySingleReq) GetSqlStatement() (v string) {
	if !p.IsSetSqlStatement() {
		return SqlReviewBySingleReq_SqlStatement_DEFAULT
	}
	return *p.SqlStatement
}
func (p *SqlReviewBySingleReq) SetInstanceID(val *string) {
	p.InstanceID = val
}
func (p *SqlReviewBySingleReq) SetDbName(val *string) {
	p.DbName = val
}
func (p *SqlReviewBySingleReq) SetInstanceType(val *string) {
	p.InstanceType = val
}
func (p *SqlReviewBySingleReq) SetSqlStatement(val *string) {
	p.SqlStatement = val
}

var fieldIDToName_SqlReviewBySingleReq = map[int16]string{
	1: "InstanceID",
	2: "DbName",
	3: "InstanceType",
	4: "sqlStatement",
}

func (p *SqlReviewBySingleReq) IsSetInstanceID() bool {
	return p.InstanceID != nil
}

func (p *SqlReviewBySingleReq) IsSetDbName() bool {
	return p.DbName != nil
}

func (p *SqlReviewBySingleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *SqlReviewBySingleReq) IsSetSqlStatement() bool {
	return p.SqlStatement != nil
}

func (p *SqlReviewBySingleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewBySingleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewBySingleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlReviewBySingleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceID = _field
	return nil
}
func (p *SqlReviewBySingleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DbName = _field
	return nil
}
func (p *SqlReviewBySingleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceType = _field
	return nil
}
func (p *SqlReviewBySingleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlStatement = _field
	return nil
}

func (p *SqlReviewBySingleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewBySingleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReviewBySingleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReviewBySingleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceID() {
		if err = oprot.WriteFieldBegin("InstanceID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReviewBySingleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDbName() {
		if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DbName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReviewBySingleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlReviewBySingleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlStatement() {
		if err = oprot.WriteFieldBegin("sqlStatement", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlStatement); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlReviewBySingleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReviewBySingleReq(%+v)", *p)

}

func (p *SqlReviewBySingleReq) DeepEqual(ano *SqlReviewBySingleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.SqlStatement) {
		return false
	}
	return true
}

func (p *SqlReviewBySingleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceID == src {
		return true
	} else if p.InstanceID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceID, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewBySingleReq) Field2DeepEqual(src *string) bool {

	if p.DbName == src {
		return true
	} else if p.DbName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DbName, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewBySingleReq) Field3DeepEqual(src *string) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceType, *src) != 0 {
		return false
	}
	return true
}
func (p *SqlReviewBySingleReq) Field4DeepEqual(src *string) bool {

	if p.SqlStatement == src {
		return true
	} else if p.SqlStatement == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlStatement, *src) != 0 {
		return false
	}
	return true
}

type SqlReviewBySingleResp struct {
	IndexAdviceResult_ []*IndexAdviceResult_ `thrift:"IndexAdviceResult,1,optional" frugal:"1,optional,list<IndexAdviceResult_>" json:"IndexAdviceResult,omitempty"`
	CheckResultList    []*CheckResult_       `thrift:"CheckResultList,2,optional" frugal:"2,optional,list<CheckResult_>" json:"CheckResultList,omitempty"`
}

func NewSqlReviewBySingleResp() *SqlReviewBySingleResp {
	return &SqlReviewBySingleResp{}
}

func (p *SqlReviewBySingleResp) InitDefault() {
}

var SqlReviewBySingleResp_IndexAdviceResult__DEFAULT []*IndexAdviceResult_

func (p *SqlReviewBySingleResp) GetIndexAdviceResult_() (v []*IndexAdviceResult_) {
	if !p.IsSetIndexAdviceResult_() {
		return SqlReviewBySingleResp_IndexAdviceResult__DEFAULT
	}
	return p.IndexAdviceResult_
}

var SqlReviewBySingleResp_CheckResultList_DEFAULT []*CheckResult_

func (p *SqlReviewBySingleResp) GetCheckResultList() (v []*CheckResult_) {
	if !p.IsSetCheckResultList() {
		return SqlReviewBySingleResp_CheckResultList_DEFAULT
	}
	return p.CheckResultList
}
func (p *SqlReviewBySingleResp) SetIndexAdviceResult_(val []*IndexAdviceResult_) {
	p.IndexAdviceResult_ = val
}
func (p *SqlReviewBySingleResp) SetCheckResultList(val []*CheckResult_) {
	p.CheckResultList = val
}

var fieldIDToName_SqlReviewBySingleResp = map[int16]string{
	1: "IndexAdviceResult",
	2: "CheckResultList",
}

func (p *SqlReviewBySingleResp) IsSetIndexAdviceResult_() bool {
	return p.IndexAdviceResult_ != nil
}

func (p *SqlReviewBySingleResp) IsSetCheckResultList() bool {
	return p.CheckResultList != nil
}

func (p *SqlReviewBySingleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewBySingleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlReviewBySingleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlReviewBySingleResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IndexAdviceResult_ = _field
	return nil
}
func (p *SqlReviewBySingleResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CheckResultList = _field
	return nil
}

func (p *SqlReviewBySingleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlReviewBySingleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlReviewBySingleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlReviewBySingleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIndexAdviceResult_() {
		if err = oprot.WriteFieldBegin("IndexAdviceResult", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.IndexAdviceResult_)); err != nil {
			return err
		}
		for _, v := range p.IndexAdviceResult_ {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlReviewBySingleResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCheckResultList() {
		if err = oprot.WriteFieldBegin("CheckResultList", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CheckResultList)); err != nil {
			return err
		}
		for _, v := range p.CheckResultList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlReviewBySingleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlReviewBySingleResp(%+v)", *p)

}

func (p *SqlReviewBySingleResp) DeepEqual(ano *SqlReviewBySingleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IndexAdviceResult_) {
		return false
	}
	if !p.Field2DeepEqual(ano.CheckResultList) {
		return false
	}
	return true
}

func (p *SqlReviewBySingleResp) Field1DeepEqual(src []*IndexAdviceResult_) bool {

	if len(p.IndexAdviceResult_) != len(src) {
		return false
	}
	for i, v := range p.IndexAdviceResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SqlReviewBySingleResp) Field2DeepEqual(src []*CheckResult_) bool {

	if len(p.CheckResultList) != len(src) {
		return false
	}
	for i, v := range p.CheckResultList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ReviewResult_ struct {
	IndexAdviceResult_ []*IndexAdviceResult_ `thrift:"IndexAdviceResult,1,optional" frugal:"1,optional,list<IndexAdviceResult_>" json:"IndexAdviceResult,omitempty"`
	CheckResultList    []*CheckResult_       `thrift:"CheckResultList,2,optional" frugal:"2,optional,list<CheckResult_>" json:"CheckResultList,omitempty"`
}

func NewReviewResult_() *ReviewResult_ {
	return &ReviewResult_{}
}

func (p *ReviewResult_) InitDefault() {
}

var ReviewResult__IndexAdviceResult__DEFAULT []*IndexAdviceResult_

func (p *ReviewResult_) GetIndexAdviceResult_() (v []*IndexAdviceResult_) {
	if !p.IsSetIndexAdviceResult_() {
		return ReviewResult__IndexAdviceResult__DEFAULT
	}
	return p.IndexAdviceResult_
}

var ReviewResult__CheckResultList_DEFAULT []*CheckResult_

func (p *ReviewResult_) GetCheckResultList() (v []*CheckResult_) {
	if !p.IsSetCheckResultList() {
		return ReviewResult__CheckResultList_DEFAULT
	}
	return p.CheckResultList
}
func (p *ReviewResult_) SetIndexAdviceResult_(val []*IndexAdviceResult_) {
	p.IndexAdviceResult_ = val
}
func (p *ReviewResult_) SetCheckResultList(val []*CheckResult_) {
	p.CheckResultList = val
}

var fieldIDToName_ReviewResult_ = map[int16]string{
	1: "IndexAdviceResult",
	2: "CheckResultList",
}

func (p *ReviewResult_) IsSetIndexAdviceResult_() bool {
	return p.IndexAdviceResult_ != nil
}

func (p *ReviewResult_) IsSetCheckResultList() bool {
	return p.CheckResultList != nil
}

func (p *ReviewResult_) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReviewResult_")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReviewResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ReviewResult_) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IndexAdviceResult_, 0, size)
	values := make([]IndexAdviceResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IndexAdviceResult_ = _field
	return nil
}
func (p *ReviewResult_) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckResult_, 0, size)
	values := make([]CheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CheckResultList = _field
	return nil
}

func (p *ReviewResult_) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ReviewResult_")

	var fieldId int16
	if err = oprot.WriteStructBegin("ReviewResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReviewResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIndexAdviceResult_() {
		if err = oprot.WriteFieldBegin("IndexAdviceResult", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.IndexAdviceResult_)); err != nil {
			return err
		}
		for _, v := range p.IndexAdviceResult_ {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ReviewResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCheckResultList() {
		if err = oprot.WriteFieldBegin("CheckResultList", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CheckResultList)); err != nil {
			return err
		}
		for _, v := range p.CheckResultList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ReviewResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReviewResult_(%+v)", *p)

}

func (p *ReviewResult_) DeepEqual(ano *ReviewResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IndexAdviceResult_) {
		return false
	}
	if !p.Field2DeepEqual(ano.CheckResultList) {
		return false
	}
	return true
}

func (p *ReviewResult_) Field1DeepEqual(src []*IndexAdviceResult_) bool {

	if len(p.IndexAdviceResult_) != len(src) {
		return false
	}
	for i, v := range p.IndexAdviceResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ReviewResult_) Field2DeepEqual(src []*CheckResult_) bool {

	if len(p.CheckResultList) != len(src) {
		return false
	}
	for i, v := range p.CheckResultList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type IndexAdviceResult_ struct {
	TableName          string   `thrift:"TableName,1,required" frugal:"1,required,string" json:"TableName"`
	DDLs               []string `thrift:"DDLs,2,required" frugal:"2,required,list<string>" json:"DDLs"`
	AdviceIndexColumns []string `thrift:"AdviceIndexColumns,3,required" frugal:"3,required,list<string>" json:"AdviceIndexColumns"`
	SQLTemplates       []string `thrift:"SQLTemplates,4,optional" frugal:"4,optional,list<string>" json:"SQLTemplates,omitempty"`
	SQL                *string  `thrift:"SQL,5,optional" frugal:"5,optional,string" json:"SQL,omitempty"`
}

func NewIndexAdviceResult_() *IndexAdviceResult_ {
	return &IndexAdviceResult_{}
}

func (p *IndexAdviceResult_) InitDefault() {
}

func (p *IndexAdviceResult_) GetTableName() (v string) {
	return p.TableName
}

func (p *IndexAdviceResult_) GetDDLs() (v []string) {
	return p.DDLs
}

func (p *IndexAdviceResult_) GetAdviceIndexColumns() (v []string) {
	return p.AdviceIndexColumns
}

var IndexAdviceResult__SQLTemplates_DEFAULT []string

func (p *IndexAdviceResult_) GetSQLTemplates() (v []string) {
	if !p.IsSetSQLTemplates() {
		return IndexAdviceResult__SQLTemplates_DEFAULT
	}
	return p.SQLTemplates
}

var IndexAdviceResult__SQL_DEFAULT string

func (p *IndexAdviceResult_) GetSQL() (v string) {
	if !p.IsSetSQL() {
		return IndexAdviceResult__SQL_DEFAULT
	}
	return *p.SQL
}
func (p *IndexAdviceResult_) SetTableName(val string) {
	p.TableName = val
}
func (p *IndexAdviceResult_) SetDDLs(val []string) {
	p.DDLs = val
}
func (p *IndexAdviceResult_) SetAdviceIndexColumns(val []string) {
	p.AdviceIndexColumns = val
}
func (p *IndexAdviceResult_) SetSQLTemplates(val []string) {
	p.SQLTemplates = val
}
func (p *IndexAdviceResult_) SetSQL(val *string) {
	p.SQL = val
}

var fieldIDToName_IndexAdviceResult_ = map[int16]string{
	1: "TableName",
	2: "DDLs",
	3: "AdviceIndexColumns",
	4: "SQLTemplates",
	5: "SQL",
}

func (p *IndexAdviceResult_) IsSetSQLTemplates() bool {
	return p.SQLTemplates != nil
}

func (p *IndexAdviceResult_) IsSetSQL() bool {
	return p.SQL != nil
}

func (p *IndexAdviceResult_) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexAdviceResult_")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTableName bool = false
	var issetDDLs bool = false
	var issetAdviceIndexColumns bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDDLs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAdviceIndexColumns = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTableName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDDLs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAdviceIndexColumns {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndexAdviceResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_IndexAdviceResult_[fieldId]))
}

func (p *IndexAdviceResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *IndexAdviceResult_) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DDLs = _field
	return nil
}
func (p *IndexAdviceResult_) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AdviceIndexColumns = _field
	return nil
}
func (p *IndexAdviceResult_) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLTemplates = _field
	return nil
}
func (p *IndexAdviceResult_) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SQL = _field
	return nil
}

func (p *IndexAdviceResult_) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("IndexAdviceResult_")

	var fieldId int16
	if err = oprot.WriteStructBegin("IndexAdviceResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IndexAdviceResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *IndexAdviceResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DDLs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.DDLs)); err != nil {
		return err
	}
	for _, v := range p.DDLs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *IndexAdviceResult_) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AdviceIndexColumns", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.AdviceIndexColumns)); err != nil {
		return err
	}
	for _, v := range p.AdviceIndexColumns {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IndexAdviceResult_) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSQLTemplates() {
		if err = oprot.WriteFieldBegin("SQLTemplates", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SQLTemplates)); err != nil {
			return err
		}
		for _, v := range p.SQLTemplates {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *IndexAdviceResult_) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSQL() {
		if err = oprot.WriteFieldBegin("SQL", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SQL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *IndexAdviceResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdviceResult_(%+v)", *p)

}

func (p *IndexAdviceResult_) DeepEqual(ano *IndexAdviceResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field2DeepEqual(ano.DDLs) {
		return false
	}
	if !p.Field3DeepEqual(ano.AdviceIndexColumns) {
		return false
	}
	if !p.Field4DeepEqual(ano.SQLTemplates) {
		return false
	}
	if !p.Field5DeepEqual(ano.SQL) {
		return false
	}
	return true
}

func (p *IndexAdviceResult_) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *IndexAdviceResult_) Field2DeepEqual(src []string) bool {

	if len(p.DDLs) != len(src) {
		return false
	}
	for i, v := range p.DDLs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *IndexAdviceResult_) Field3DeepEqual(src []string) bool {

	if len(p.AdviceIndexColumns) != len(src) {
		return false
	}
	for i, v := range p.AdviceIndexColumns {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *IndexAdviceResult_) Field4DeepEqual(src []string) bool {

	if len(p.SQLTemplates) != len(src) {
		return false
	}
	for i, v := range p.SQLTemplates {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *IndexAdviceResult_) Field5DeepEqual(src *string) bool {

	if p.SQL == src {
		return true
	} else if p.SQL == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SQL, *src) != 0 {
		return false
	}
	return true
}

type CheckResult_ struct {
	ErrorMsg   *string `thrift:"ErrorMsg,1,optional" frugal:"1,optional,string" json:"ErrorMsg,omitempty"`
	ActionName *string `thrift:"ActionName,2,optional" frugal:"2,optional,string" json:"ActionName,omitempty"`
	Level      *string `thrift:"Level,3,optional" frugal:"3,optional,string" json:"Level,omitempty"`
}

func NewCheckResult_() *CheckResult_ {
	return &CheckResult_{}
}

func (p *CheckResult_) InitDefault() {
}

var CheckResult__ErrorMsg_DEFAULT string

func (p *CheckResult_) GetErrorMsg() (v string) {
	if !p.IsSetErrorMsg() {
		return CheckResult__ErrorMsg_DEFAULT
	}
	return *p.ErrorMsg
}

var CheckResult__ActionName_DEFAULT string

func (p *CheckResult_) GetActionName() (v string) {
	if !p.IsSetActionName() {
		return CheckResult__ActionName_DEFAULT
	}
	return *p.ActionName
}

var CheckResult__Level_DEFAULT string

func (p *CheckResult_) GetLevel() (v string) {
	if !p.IsSetLevel() {
		return CheckResult__Level_DEFAULT
	}
	return *p.Level
}
func (p *CheckResult_) SetErrorMsg(val *string) {
	p.ErrorMsg = val
}
func (p *CheckResult_) SetActionName(val *string) {
	p.ActionName = val
}
func (p *CheckResult_) SetLevel(val *string) {
	p.Level = val
}

var fieldIDToName_CheckResult_ = map[int16]string{
	1: "ErrorMsg",
	2: "ActionName",
	3: "Level",
}

func (p *CheckResult_) IsSetErrorMsg() bool {
	return p.ErrorMsg != nil
}

func (p *CheckResult_) IsSetActionName() bool {
	return p.ActionName != nil
}

func (p *CheckResult_) IsSetLevel() bool {
	return p.Level != nil
}

func (p *CheckResult_) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckResult_")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorMsg = _field
	return nil
}
func (p *CheckResult_) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ActionName = _field
	return nil
}
func (p *CheckResult_) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Level = _field
	return nil
}

func (p *CheckResult_) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckResult_")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorMsg() {
		if err = oprot.WriteFieldBegin("ErrorMsg", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorMsg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionName() {
		if err = oprot.WriteFieldBegin("ActionName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ActionName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckResult_) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLevel() {
		if err = oprot.WriteFieldBegin("Level", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Level); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckResult_(%+v)", *p)

}

func (p *CheckResult_) DeepEqual(ano *CheckResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ErrorMsg) {
		return false
	}
	if !p.Field2DeepEqual(ano.ActionName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Level) {
		return false
	}
	return true
}

func (p *CheckResult_) Field1DeepEqual(src *string) bool {

	if p.ErrorMsg == src {
		return true
	} else if p.ErrorMsg == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorMsg, *src) != 0 {
		return false
	}
	return true
}
func (p *CheckResult_) Field2DeepEqual(src *string) bool {

	if p.ActionName == src {
		return true
	} else if p.ActionName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ActionName, *src) != 0 {
		return false
	}
	return true
}
func (p *CheckResult_) Field3DeepEqual(src *string) bool {

	if p.Level == src {
		return true
	} else if p.Level == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Level, *src) != 0 {
		return false
	}
	return true
}

type DescribeReviewDetailActionListReq struct {
	ReviewID *string `thrift:"ReviewID,1,optional" frugal:"1,optional,string" json:"ReviewID,omitempty"`
}

func NewDescribeReviewDetailActionListReq() *DescribeReviewDetailActionListReq {
	return &DescribeReviewDetailActionListReq{}
}

func (p *DescribeReviewDetailActionListReq) InitDefault() {
}

var DescribeReviewDetailActionListReq_ReviewID_DEFAULT string

func (p *DescribeReviewDetailActionListReq) GetReviewID() (v string) {
	if !p.IsSetReviewID() {
		return DescribeReviewDetailActionListReq_ReviewID_DEFAULT
	}
	return *p.ReviewID
}
func (p *DescribeReviewDetailActionListReq) SetReviewID(val *string) {
	p.ReviewID = val
}

var fieldIDToName_DescribeReviewDetailActionListReq = map[int16]string{
	1: "ReviewID",
}

func (p *DescribeReviewDetailActionListReq) IsSetReviewID() bool {
	return p.ReviewID != nil
}

func (p *DescribeReviewDetailActionListReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeReviewDetailActionListReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeReviewDetailActionListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeReviewDetailActionListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReviewID = _field
	return nil
}

func (p *DescribeReviewDetailActionListReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeReviewDetailActionListReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeReviewDetailActionListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeReviewDetailActionListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetReviewID() {
		if err = oprot.WriteFieldBegin("ReviewID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReviewID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeReviewDetailActionListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeReviewDetailActionListReq(%+v)", *p)

}

func (p *DescribeReviewDetailActionListReq) DeepEqual(ano *DescribeReviewDetailActionListReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ReviewID) {
		return false
	}
	return true
}

func (p *DescribeReviewDetailActionListReq) Field1DeepEqual(src *string) bool {

	if p.ReviewID == src {
		return true
	} else if p.ReviewID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReviewID, *src) != 0 {
		return false
	}
	return true
}

type DescribeReviewDetailActionListResp struct {
	ActionNameList []string `thrift:"ActionNameList,1,optional" frugal:"1,optional,list<string>" json:"ActionNameList,omitempty"`
}

func NewDescribeReviewDetailActionListResp() *DescribeReviewDetailActionListResp {
	return &DescribeReviewDetailActionListResp{}
}

func (p *DescribeReviewDetailActionListResp) InitDefault() {
}

var DescribeReviewDetailActionListResp_ActionNameList_DEFAULT []string

func (p *DescribeReviewDetailActionListResp) GetActionNameList() (v []string) {
	if !p.IsSetActionNameList() {
		return DescribeReviewDetailActionListResp_ActionNameList_DEFAULT
	}
	return p.ActionNameList
}
func (p *DescribeReviewDetailActionListResp) SetActionNameList(val []string) {
	p.ActionNameList = val
}

var fieldIDToName_DescribeReviewDetailActionListResp = map[int16]string{
	1: "ActionNameList",
}

func (p *DescribeReviewDetailActionListResp) IsSetActionNameList() bool {
	return p.ActionNameList != nil
}

func (p *DescribeReviewDetailActionListResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeReviewDetailActionListResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeReviewDetailActionListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeReviewDetailActionListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ActionNameList = _field
	return nil
}

func (p *DescribeReviewDetailActionListResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeReviewDetailActionListResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeReviewDetailActionListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeReviewDetailActionListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionNameList() {
		if err = oprot.WriteFieldBegin("ActionNameList", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ActionNameList)); err != nil {
			return err
		}
		for _, v := range p.ActionNameList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeReviewDetailActionListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeReviewDetailActionListResp(%+v)", *p)

}

func (p *DescribeReviewDetailActionListResp) DeepEqual(ano *DescribeReviewDetailActionListResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ActionNameList) {
		return false
	}
	return true
}

func (p *DescribeReviewDetailActionListResp) Field1DeepEqual(src []string) bool {

	if len(p.ActionNameList) != len(src) {
		return false
	}
	for i, v := range p.ActionNameList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

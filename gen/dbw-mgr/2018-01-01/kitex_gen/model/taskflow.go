// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type OrderBy int64

const (
	OrderBy_CreateTime      OrderBy = 0
	OrderBy_LastExecuteTime OrderBy = 1
)

func (p OrderBy) String() string {
	switch p {
	case OrderBy_CreateTime:
		return "CreateTime"
	case OrderBy_LastExecuteTime:
		return "LastExecuteTime"
	}
	return "<UNSET>"
}

func OrderByFromString(s string) (OrderBy, error) {
	switch s {
	case "CreateTime":
		return OrderBy_CreateTime, nil
	case "LastExecuteTime":
		return OrderBy_LastExecuteTime, nil
	}
	return OrderBy(0), fmt.Errorf("not a valid OrderBy string")
}

func OrderByPtr(v OrderBy) *OrderBy { return &v }

func (p OrderBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderBy) UnmarshalText(text []byte) error {
	q, err := OrderByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type State int64

const (
	State_Enabled  State = 1
	State_Disabled State = 2
	State_Expired  State = 3
)

func (p State) String() string {
	switch p {
	case State_Enabled:
		return "Enabled"
	case State_Disabled:
		return "Disabled"
	case State_Expired:
		return "Expired"
	}
	return "<UNSET>"
}

func StateFromString(s string) (State, error) {
	switch s {
	case "Enabled":
		return State_Enabled, nil
	case "Disabled":
		return State_Disabled, nil
	case "Expired":
		return State_Expired, nil
	}
	return State(0), fmt.Errorf("not a valid State string")
}

func StatePtr(v State) *State { return &v }

func (p State) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *State) UnmarshalText(text []byte) error {
	q, err := StateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type JobOrderBy int64

const (
	JobOrderBy_StartTime JobOrderBy = 0
	JobOrderBy_EndTime   JobOrderBy = 1
)

func (p JobOrderBy) String() string {
	switch p {
	case JobOrderBy_StartTime:
		return "StartTime"
	case JobOrderBy_EndTime:
		return "EndTime"
	}
	return "<UNSET>"
}

func JobOrderByFromString(s string) (JobOrderBy, error) {
	switch s {
	case "StartTime":
		return JobOrderBy_StartTime, nil
	case "EndTime":
		return JobOrderBy_EndTime, nil
	}
	return JobOrderBy(0), fmt.Errorf("not a valid JobOrderBy string")
}

func JobOrderByPtr(v JobOrderBy) *JobOrderBy { return &v }

func (p JobOrderBy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *JobOrderBy) UnmarshalText(text []byte) error {
	q, err := JobOrderByFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type JobState int64

const (
	JobState_NotStarted JobState = 0
	JobState_Executing  JobState = 1
	JobState_Successed  JobState = 2
	JobState_Failed     JobState = 3
)

func (p JobState) String() string {
	switch p {
	case JobState_NotStarted:
		return "NotStarted"
	case JobState_Executing:
		return "Executing"
	case JobState_Successed:
		return "Successed"
	case JobState_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func JobStateFromString(s string) (JobState, error) {
	switch s {
	case "NotStarted":
		return JobState_NotStarted, nil
	case "Executing":
		return JobState_Executing, nil
	case "Successed":
		return JobState_Successed, nil
	case "Failed":
		return JobState_Failed, nil
	}
	return JobState(0), fmt.Errorf("not a valid JobState string")
}

func JobStatePtr(v JobState) *JobState { return &v }

func (p JobState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *JobState) UnmarshalText(text []byte) error {
	q, err := JobStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ExecuteMethod int64

const (
	ExecuteMethod_Fixed ExecuteMethod = 1
	ExecuteMethod_Cycle ExecuteMethod = 2
)

func (p ExecuteMethod) String() string {
	switch p {
	case ExecuteMethod_Fixed:
		return "Fixed"
	case ExecuteMethod_Cycle:
		return "Cycle"
	}
	return "<UNSET>"
}

func ExecuteMethodFromString(s string) (ExecuteMethod, error) {
	switch s {
	case "Fixed":
		return ExecuteMethod_Fixed, nil
	case "Cycle":
		return ExecuteMethod_Cycle, nil
	}
	return ExecuteMethod(0), fmt.Errorf("not a valid ExecuteMethod string")
}

func ExecuteMethodPtr(v ExecuteMethod) *ExecuteMethod { return &v }

func (p ExecuteMethod) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ExecuteMethod) UnmarshalText(text []byte) error {
	q, err := ExecuteMethodFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TaskType int64

const (
	TaskType_SingleInstance TaskType = 1
)

func (p TaskType) String() string {
	switch p {
	case TaskType_SingleInstance:
		return "SingleInstance"
	}
	return "<UNSET>"
}

func TaskTypeFromString(s string) (TaskType, error) {
	switch s {
	case "SingleInstance":
		return TaskType_SingleInstance, nil
	}
	return TaskType(0), fmt.Errorf("not a valid TaskType string")
}

func TaskTypePtr(v TaskType) *TaskType { return &v }

func (p TaskType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TaskType) UnmarshalText(text []byte) error {
	q, err := TaskTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type IntervalUnit int64

const (
	IntervalUnit_Minute IntervalUnit = 1
	IntervalUnit_Hour   IntervalUnit = 2
	IntervalUnit_Day    IntervalUnit = 3
	IntervalUnit_Second IntervalUnit = 4
)

func (p IntervalUnit) String() string {
	switch p {
	case IntervalUnit_Minute:
		return "Minute"
	case IntervalUnit_Hour:
		return "Hour"
	case IntervalUnit_Day:
		return "Day"
	case IntervalUnit_Second:
		return "Second"
	}
	return "<UNSET>"
}

func IntervalUnitFromString(s string) (IntervalUnit, error) {
	switch s {
	case "Minute":
		return IntervalUnit_Minute, nil
	case "Hour":
		return IntervalUnit_Hour, nil
	case "Day":
		return IntervalUnit_Day, nil
	case "Second":
		return IntervalUnit_Second, nil
	}
	return IntervalUnit(0), fmt.Errorf("not a valid IntervalUnit string")
}

func IntervalUnitPtr(v IntervalUnit) *IntervalUnit { return &v }

func (p IntervalUnit) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *IntervalUnit) UnmarshalText(text []byte) error {
	q, err := IntervalUnitFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type StillRunningPolicy int64

const (
	StillRunningPolicy_Skip     StillRunningPolicy = 1
	StillRunningPolicy_Continue StillRunningPolicy = 2
	StillRunningPolicy_Delay    StillRunningPolicy = 3
)

func (p StillRunningPolicy) String() string {
	switch p {
	case StillRunningPolicy_Skip:
		return "Skip"
	case StillRunningPolicy_Continue:
		return "Continue"
	case StillRunningPolicy_Delay:
		return "Delay"
	}
	return "<UNSET>"
}

func StillRunningPolicyFromString(s string) (StillRunningPolicy, error) {
	switch s {
	case "Skip":
		return StillRunningPolicy_Skip, nil
	case "Continue":
		return StillRunningPolicy_Continue, nil
	case "Delay":
		return StillRunningPolicy_Delay, nil
	}
	return StillRunningPolicy(0), fmt.Errorf("not a valid StillRunningPolicy string")
}

func StillRunningPolicyPtr(v StillRunningPolicy) *StillRunningPolicy { return &v }

func (p StillRunningPolicy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *StillRunningPolicy) UnmarshalText(text []byte) error {
	q, err := StillRunningPolicyFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeTaskFlowsReq struct {
	Filter      *Filter  `thrift:"Filter,1,optional" frugal:"1,optional,Filter" json:"Filter,omitempty"`
	SearchParam *string  `thrift:"SearchParam,2,optional" frugal:"2,optional,string" json:"SearchParam,omitempty"`
	SortBy      *SortBy  `thrift:"SortBy,3,optional" frugal:"3,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy     *OrderBy `thrift:"OrderBy,4,optional" frugal:"4,optional,OrderBy" json:"OrderBy,omitempty"`
	PageSize    *int32   `thrift:"PageSize,5,optional" frugal:"5,optional,i32" json:"PageSize,omitempty"`
	PageNumber  *int32   `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
}

func NewDescribeTaskFlowsReq() *DescribeTaskFlowsReq {
	return &DescribeTaskFlowsReq{}
}

func (p *DescribeTaskFlowsReq) InitDefault() {
}

var DescribeTaskFlowsReq_Filter_DEFAULT *Filter

func (p *DescribeTaskFlowsReq) GetFilter() (v *Filter) {
	if !p.IsSetFilter() {
		return DescribeTaskFlowsReq_Filter_DEFAULT
	}
	return p.Filter
}

var DescribeTaskFlowsReq_SearchParam_DEFAULT string

func (p *DescribeTaskFlowsReq) GetSearchParam() (v string) {
	if !p.IsSetSearchParam() {
		return DescribeTaskFlowsReq_SearchParam_DEFAULT
	}
	return *p.SearchParam
}

var DescribeTaskFlowsReq_SortBy_DEFAULT SortBy

func (p *DescribeTaskFlowsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeTaskFlowsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeTaskFlowsReq_OrderBy_DEFAULT OrderBy

func (p *DescribeTaskFlowsReq) GetOrderBy() (v OrderBy) {
	if !p.IsSetOrderBy() {
		return DescribeTaskFlowsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeTaskFlowsReq_PageSize_DEFAULT int32

func (p *DescribeTaskFlowsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTaskFlowsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTaskFlowsReq_PageNumber_DEFAULT int32

func (p *DescribeTaskFlowsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTaskFlowsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}
func (p *DescribeTaskFlowsReq) SetFilter(val *Filter) {
	p.Filter = val
}
func (p *DescribeTaskFlowsReq) SetSearchParam(val *string) {
	p.SearchParam = val
}
func (p *DescribeTaskFlowsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeTaskFlowsReq) SetOrderBy(val *OrderBy) {
	p.OrderBy = val
}
func (p *DescribeTaskFlowsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTaskFlowsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}

var fieldIDToName_DescribeTaskFlowsReq = map[int16]string{
	1: "Filter",
	2: "SearchParam",
	3: "SortBy",
	4: "OrderBy",
	5: "PageSize",
	6: "PageNumber",
}

func (p *DescribeTaskFlowsReq) IsSetFilter() bool {
	return p.Filter != nil
}

func (p *DescribeTaskFlowsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeTaskFlowsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeTaskFlowsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeTaskFlowsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTaskFlowsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTaskFlowsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskFlowsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Filter = _field
	return nil
}
func (p *DescribeTaskFlowsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeTaskFlowsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeTaskFlowsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *OrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderBy(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeTaskFlowsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTaskFlowsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}

func (p *DescribeTaskFlowsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskFlowsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilter() {
		if err = oprot.WriteFieldBegin("Filter", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Filter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SearchParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTaskFlowsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskFlowsReq(%+v)", *p)

}

func (p *DescribeTaskFlowsReq) DeepEqual(ano *DescribeTaskFlowsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Filter) {
		return false
	}
	if !p.Field2DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field3DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field4DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	return true
}

func (p *DescribeTaskFlowsReq) Field1DeepEqual(src *Filter) bool {

	if !p.Filter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTaskFlowsReq) Field2DeepEqual(src *string) bool {

	if p.SearchParam == src {
		return true
	} else if p.SearchParam == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SearchParam, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTaskFlowsReq) Field3DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeTaskFlowsReq) Field4DeepEqual(src *OrderBy) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeTaskFlowsReq) Field5DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTaskFlowsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}

type DescribeTaskFlowsResp struct {
	TaskFlows []*TaskFlow `thrift:"TaskFlows,1,required" frugal:"1,required,list<TaskFlow>" json:"TaskFlows"`
	Total     int32       `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeTaskFlowsResp() *DescribeTaskFlowsResp {
	return &DescribeTaskFlowsResp{}
}

func (p *DescribeTaskFlowsResp) InitDefault() {
}

func (p *DescribeTaskFlowsResp) GetTaskFlows() (v []*TaskFlow) {
	return p.TaskFlows
}

func (p *DescribeTaskFlowsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTaskFlowsResp) SetTaskFlows(val []*TaskFlow) {
	p.TaskFlows = val
}
func (p *DescribeTaskFlowsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTaskFlowsResp = map[int16]string{
	1: "TaskFlows",
	2: "Total",
}

func (p *DescribeTaskFlowsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlows bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlows {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskFlowsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTaskFlowsResp[fieldId]))
}

func (p *DescribeTaskFlowsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TaskFlow, 0, size)
	values := make([]TaskFlow, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskFlows = _field
	return nil
}
func (p *DescribeTaskFlowsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTaskFlowsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskFlowsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskFlowsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlows", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskFlows)); err != nil {
		return err
	}
	for _, v := range p.TaskFlows {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskFlowsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTaskFlowsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskFlowsResp(%+v)", *p)

}

func (p *DescribeTaskFlowsResp) DeepEqual(ano *DescribeTaskFlowsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlows) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTaskFlowsResp) Field1DeepEqual(src []*TaskFlow) bool {

	if len(p.TaskFlows) != len(src) {
		return false
	}
	for i, v := range p.TaskFlows {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTaskFlowsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type TaskFlow struct {
	ID                string          `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	Name              string          `thrift:"Name,2,required" frugal:"2,required,string" json:"Name"`
	Creator           string          `thrift:"Creator,3,required" frugal:"3,required,string" json:"Creator"`
	TaskType          TaskType        `thrift:"TaskType,4,required" frugal:"4,required,TaskType" json:"TaskType"`
	InstanceId        string          `thrift:"InstanceId,5,required" frugal:"5,required,string" json:"InstanceId"`
	InstanceType      InstanceType    `thrift:"InstanceType,6,required" frugal:"6,required,InstanceType" json:"InstanceType"`
	CreateTime        string          `thrift:"CreateTime,7,required" frugal:"7,required,string" json:"CreateTime"`
	LastExecuteTime   string          `thrift:"LastExecuteTime,8,required" frugal:"8,required,string" json:"LastExecuteTime"`
	CycleConfig       *CycleConfig    `thrift:"CycleConfig,9,required" frugal:"9,required,CycleConfig" json:"CycleConfig"`
	InstanceConfig    *InstanceConfig `thrift:"InstanceConfig,10,required" frugal:"10,required,InstanceConfig" json:"InstanceConfig"`
	LastExecuteState  JobState        `thrift:"LastExecuteState,11,required" frugal:"11,required,JobState" json:"LastExecuteState"`
	ExecutedTimes     string          `thrift:"ExecutedTimes,12,required" frugal:"12,required,string" json:"ExecutedTimes"`
	FailedTimes       string          `thrift:"FailedTimes,13,required" frugal:"13,required,string" json:"FailedTimes"`
	EnableTransaction bool            `thrift:"EnableTransaction,14,required" frugal:"14,required,bool" json:"EnableTransaction"`
	IgnoreError       bool            `thrift:"IgnoreError,15,required" frugal:"15,required,bool" json:"IgnoreError"`
	Statement         string          `thrift:"Statement,16,required" frugal:"16,required,string" json:"Statement"`
	Comment           string          `thrift:"Comment,17,required" frugal:"17,required,string" json:"Comment"`
	State             State           `thrift:"State,18,required" frugal:"18,required,State" json:"State"`
	ExecuteTime       string          `thrift:"ExecuteTime,19,required" frugal:"19,required,string" json:"ExecuteTime"`
}

func NewTaskFlow() *TaskFlow {
	return &TaskFlow{}
}

func (p *TaskFlow) InitDefault() {
}

func (p *TaskFlow) GetID() (v string) {
	return p.ID
}

func (p *TaskFlow) GetName() (v string) {
	return p.Name
}

func (p *TaskFlow) GetCreator() (v string) {
	return p.Creator
}

func (p *TaskFlow) GetTaskType() (v TaskType) {
	return p.TaskType
}

func (p *TaskFlow) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *TaskFlow) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *TaskFlow) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *TaskFlow) GetLastExecuteTime() (v string) {
	return p.LastExecuteTime
}

var TaskFlow_CycleConfig_DEFAULT *CycleConfig

func (p *TaskFlow) GetCycleConfig() (v *CycleConfig) {
	if !p.IsSetCycleConfig() {
		return TaskFlow_CycleConfig_DEFAULT
	}
	return p.CycleConfig
}

var TaskFlow_InstanceConfig_DEFAULT *InstanceConfig

func (p *TaskFlow) GetInstanceConfig() (v *InstanceConfig) {
	if !p.IsSetInstanceConfig() {
		return TaskFlow_InstanceConfig_DEFAULT
	}
	return p.InstanceConfig
}

func (p *TaskFlow) GetLastExecuteState() (v JobState) {
	return p.LastExecuteState
}

func (p *TaskFlow) GetExecutedTimes() (v string) {
	return p.ExecutedTimes
}

func (p *TaskFlow) GetFailedTimes() (v string) {
	return p.FailedTimes
}

func (p *TaskFlow) GetEnableTransaction() (v bool) {
	return p.EnableTransaction
}

func (p *TaskFlow) GetIgnoreError() (v bool) {
	return p.IgnoreError
}

func (p *TaskFlow) GetStatement() (v string) {
	return p.Statement
}

func (p *TaskFlow) GetComment() (v string) {
	return p.Comment
}

func (p *TaskFlow) GetState() (v State) {
	return p.State
}

func (p *TaskFlow) GetExecuteTime() (v string) {
	return p.ExecuteTime
}
func (p *TaskFlow) SetID(val string) {
	p.ID = val
}
func (p *TaskFlow) SetName(val string) {
	p.Name = val
}
func (p *TaskFlow) SetCreator(val string) {
	p.Creator = val
}
func (p *TaskFlow) SetTaskType(val TaskType) {
	p.TaskType = val
}
func (p *TaskFlow) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *TaskFlow) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *TaskFlow) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *TaskFlow) SetLastExecuteTime(val string) {
	p.LastExecuteTime = val
}
func (p *TaskFlow) SetCycleConfig(val *CycleConfig) {
	p.CycleConfig = val
}
func (p *TaskFlow) SetInstanceConfig(val *InstanceConfig) {
	p.InstanceConfig = val
}
func (p *TaskFlow) SetLastExecuteState(val JobState) {
	p.LastExecuteState = val
}
func (p *TaskFlow) SetExecutedTimes(val string) {
	p.ExecutedTimes = val
}
func (p *TaskFlow) SetFailedTimes(val string) {
	p.FailedTimes = val
}
func (p *TaskFlow) SetEnableTransaction(val bool) {
	p.EnableTransaction = val
}
func (p *TaskFlow) SetIgnoreError(val bool) {
	p.IgnoreError = val
}
func (p *TaskFlow) SetStatement(val string) {
	p.Statement = val
}
func (p *TaskFlow) SetComment(val string) {
	p.Comment = val
}
func (p *TaskFlow) SetState(val State) {
	p.State = val
}
func (p *TaskFlow) SetExecuteTime(val string) {
	p.ExecuteTime = val
}

var fieldIDToName_TaskFlow = map[int16]string{
	1:  "ID",
	2:  "Name",
	3:  "Creator",
	4:  "TaskType",
	5:  "InstanceId",
	6:  "InstanceType",
	7:  "CreateTime",
	8:  "LastExecuteTime",
	9:  "CycleConfig",
	10: "InstanceConfig",
	11: "LastExecuteState",
	12: "ExecutedTimes",
	13: "FailedTimes",
	14: "EnableTransaction",
	15: "IgnoreError",
	16: "Statement",
	17: "Comment",
	18: "State",
	19: "ExecuteTime",
}

func (p *TaskFlow) IsSetCycleConfig() bool {
	return p.CycleConfig != nil
}

func (p *TaskFlow) IsSetInstanceConfig() bool {
	return p.InstanceConfig != nil
}

func (p *TaskFlow) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TaskFlow")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetCreator bool = false
	var issetTaskType bool = false
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetCreateTime bool = false
	var issetLastExecuteTime bool = false
	var issetCycleConfig bool = false
	var issetInstanceConfig bool = false
	var issetLastExecuteState bool = false
	var issetExecutedTimes bool = false
	var issetFailedTimes bool = false
	var issetEnableTransaction bool = false
	var issetIgnoreError bool = false
	var issetStatement bool = false
	var issetComment bool = false
	var issetState bool = false
	var issetExecuteTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastExecuteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCycleConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastExecuteState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecutedTimes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetFailedTimes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableTransaction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetIgnoreError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetComment = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCreator {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTaskType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetLastExecuteTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetCycleConfig {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetInstanceConfig {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetLastExecuteState {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetExecutedTimes {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetFailedTimes {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetEnableTransaction {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetIgnoreError {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetStatement {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetComment {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetState {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetExecuteTime {
		fieldId = 19
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TaskFlow[fieldId]))
}

func (p *TaskFlow) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *TaskFlow) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *TaskFlow) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Creator = _field
	return nil
}
func (p *TaskFlow) ReadField4(iprot thrift.TProtocol) error {

	var _field TaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TaskType(v)
	}
	p.TaskType = _field
	return nil
}
func (p *TaskFlow) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *TaskFlow) ReadField6(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *TaskFlow) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *TaskFlow) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastExecuteTime = _field
	return nil
}
func (p *TaskFlow) ReadField9(iprot thrift.TProtocol) error {
	_field := NewCycleConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CycleConfig = _field
	return nil
}
func (p *TaskFlow) ReadField10(iprot thrift.TProtocol) error {
	_field := NewInstanceConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceConfig = _field
	return nil
}
func (p *TaskFlow) ReadField11(iprot thrift.TProtocol) error {

	var _field JobState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = JobState(v)
	}
	p.LastExecuteState = _field
	return nil
}
func (p *TaskFlow) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecutedTimes = _field
	return nil
}
func (p *TaskFlow) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FailedTimes = _field
	return nil
}
func (p *TaskFlow) ReadField14(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EnableTransaction = _field
	return nil
}
func (p *TaskFlow) ReadField15(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IgnoreError = _field
	return nil
}
func (p *TaskFlow) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}
func (p *TaskFlow) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Comment = _field
	return nil
}
func (p *TaskFlow) ReadField18(iprot thrift.TProtocol) error {

	var _field State
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = State(v)
	}
	p.State = _field
	return nil
}
func (p *TaskFlow) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteTime = _field
	return nil
}

func (p *TaskFlow) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TaskFlow")

	var fieldId int16
	if err = oprot.WriteStructBegin("TaskFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskFlow) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TaskFlow) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Creator", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Creator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TaskFlow) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TaskFlow) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TaskFlow) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TaskFlow) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TaskFlow) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastExecuteTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastExecuteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TaskFlow) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CycleConfig", thrift.STRUCT, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CycleConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *TaskFlow) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceConfig", thrift.STRUCT, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InstanceConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *TaskFlow) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastExecuteState", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LastExecuteState)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *TaskFlow) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecutedTimes", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecutedTimes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *TaskFlow) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FailedTimes", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FailedTimes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *TaskFlow) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableTransaction", thrift.BOOL, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.EnableTransaction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *TaskFlow) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IgnoreError", thrift.BOOL, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IgnoreError); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *TaskFlow) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *TaskFlow) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Comment); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *TaskFlow) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("State", thrift.I32, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.State)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *TaskFlow) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteTime", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecuteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *TaskFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskFlow(%+v)", *p)

}

func (p *TaskFlow) DeepEqual(ano *TaskFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.Creator) {
		return false
	}
	if !p.Field4DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.LastExecuteTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.CycleConfig) {
		return false
	}
	if !p.Field10DeepEqual(ano.InstanceConfig) {
		return false
	}
	if !p.Field11DeepEqual(ano.LastExecuteState) {
		return false
	}
	if !p.Field12DeepEqual(ano.ExecutedTimes) {
		return false
	}
	if !p.Field13DeepEqual(ano.FailedTimes) {
		return false
	}
	if !p.Field14DeepEqual(ano.EnableTransaction) {
		return false
	}
	if !p.Field15DeepEqual(ano.IgnoreError) {
		return false
	}
	if !p.Field16DeepEqual(ano.Statement) {
		return false
	}
	if !p.Field17DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field18DeepEqual(ano.State) {
		return false
	}
	if !p.Field19DeepEqual(ano.ExecuteTime) {
		return false
	}
	return true
}

func (p *TaskFlow) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Creator, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field4DeepEqual(src TaskType) bool {

	if p.TaskType != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field5DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field6DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field7DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field8DeepEqual(src string) bool {

	if strings.Compare(p.LastExecuteTime, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field9DeepEqual(src *CycleConfig) bool {

	if !p.CycleConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TaskFlow) Field10DeepEqual(src *InstanceConfig) bool {

	if !p.InstanceConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TaskFlow) Field11DeepEqual(src JobState) bool {

	if p.LastExecuteState != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field12DeepEqual(src string) bool {

	if strings.Compare(p.ExecutedTimes, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field13DeepEqual(src string) bool {

	if strings.Compare(p.FailedTimes, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field14DeepEqual(src bool) bool {

	if p.EnableTransaction != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field15DeepEqual(src bool) bool {

	if p.IgnoreError != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field16DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field17DeepEqual(src string) bool {

	if strings.Compare(p.Comment, src) != 0 {
		return false
	}
	return true
}
func (p *TaskFlow) Field18DeepEqual(src State) bool {

	if p.State != src {
		return false
	}
	return true
}
func (p *TaskFlow) Field19DeepEqual(src string) bool {

	if strings.Compare(p.ExecuteTime, src) != 0 {
		return false
	}
	return true
}

type Filter struct {
	State            *State    `thrift:"State,1,optional" frugal:"1,optional,State" json:"State,omitempty"`
	LastExecuteState *JobState `thrift:"LastExecuteState,2,optional" frugal:"2,optional,JobState" json:"LastExecuteState,omitempty"`
	InstanceId       *string   `thrift:"InstanceId,3,optional" frugal:"3,optional,string" json:"InstanceId,omitempty"`
}

func NewFilter() *Filter {
	return &Filter{}
}

func (p *Filter) InitDefault() {
}

var Filter_State_DEFAULT State

func (p *Filter) GetState() (v State) {
	if !p.IsSetState() {
		return Filter_State_DEFAULT
	}
	return *p.State
}

var Filter_LastExecuteState_DEFAULT JobState

func (p *Filter) GetLastExecuteState() (v JobState) {
	if !p.IsSetLastExecuteState() {
		return Filter_LastExecuteState_DEFAULT
	}
	return *p.LastExecuteState
}

var Filter_InstanceId_DEFAULT string

func (p *Filter) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return Filter_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *Filter) SetState(val *State) {
	p.State = val
}
func (p *Filter) SetLastExecuteState(val *JobState) {
	p.LastExecuteState = val
}
func (p *Filter) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_Filter = map[int16]string{
	1: "State",
	2: "LastExecuteState",
	3: "InstanceId",
}

func (p *Filter) IsSetState() bool {
	return p.State != nil
}

func (p *Filter) IsSetLastExecuteState() bool {
	return p.LastExecuteState != nil
}

func (p *Filter) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *Filter) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Filter")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Filter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Filter) ReadField1(iprot thrift.TProtocol) error {

	var _field *State
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := State(v)
		_field = &tmp
	}
	p.State = _field
	return nil
}
func (p *Filter) ReadField2(iprot thrift.TProtocol) error {

	var _field *JobState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := JobState(v)
		_field = &tmp
	}
	p.LastExecuteState = _field
	return nil
}
func (p *Filter) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *Filter) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Filter")

	var fieldId int16
	if err = oprot.WriteStructBegin("Filter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Filter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetState() {
		if err = oprot.WriteFieldBegin("State", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.State)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Filter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLastExecuteState() {
		if err = oprot.WriteFieldBegin("LastExecuteState", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LastExecuteState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Filter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Filter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Filter(%+v)", *p)

}

func (p *Filter) DeepEqual(ano *Filter) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.State) {
		return false
	}
	if !p.Field2DeepEqual(ano.LastExecuteState) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *Filter) Field1DeepEqual(src *State) bool {

	if p.State == src {
		return true
	} else if p.State == nil || src == nil {
		return false
	}
	if *p.State != *src {
		return false
	}
	return true
}
func (p *Filter) Field2DeepEqual(src *JobState) bool {

	if p.LastExecuteState == src {
		return true
	} else if p.LastExecuteState == nil || src == nil {
		return false
	}
	if *p.LastExecuteState != *src {
		return false
	}
	return true
}
func (p *Filter) Field3DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type DescribeTaskFlowReq struct {
	TaskFlowID string `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
}

func NewDescribeTaskFlowReq() *DescribeTaskFlowReq {
	return &DescribeTaskFlowReq{}
}

func (p *DescribeTaskFlowReq) InitDefault() {
}

func (p *DescribeTaskFlowReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}
func (p *DescribeTaskFlowReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}

var fieldIDToName_DescribeTaskFlowReq = map[int16]string{
	1: "TaskFlowID",
}

func (p *DescribeTaskFlowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskFlowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTaskFlowReq[fieldId]))
}

func (p *DescribeTaskFlowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}

func (p *DescribeTaskFlowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskFlowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskFlowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskFlowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskFlowReq(%+v)", *p)

}

func (p *DescribeTaskFlowReq) DeepEqual(ano *DescribeTaskFlowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	return true
}

func (p *DescribeTaskFlowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}

type DescribeTaskFlowResp struct {
	TaskFlow *TaskFlow `thrift:"TaskFlow,1,required" frugal:"1,required,TaskFlow" json:"TaskFlow"`
}

func NewDescribeTaskFlowResp() *DescribeTaskFlowResp {
	return &DescribeTaskFlowResp{}
}

func (p *DescribeTaskFlowResp) InitDefault() {
}

var DescribeTaskFlowResp_TaskFlow_DEFAULT *TaskFlow

func (p *DescribeTaskFlowResp) GetTaskFlow() (v *TaskFlow) {
	if !p.IsSetTaskFlow() {
		return DescribeTaskFlowResp_TaskFlow_DEFAULT
	}
	return p.TaskFlow
}
func (p *DescribeTaskFlowResp) SetTaskFlow(val *TaskFlow) {
	p.TaskFlow = val
}

var fieldIDToName_DescribeTaskFlowResp = map[int16]string{
	1: "TaskFlow",
}

func (p *DescribeTaskFlowResp) IsSetTaskFlow() bool {
	return p.TaskFlow != nil
}

func (p *DescribeTaskFlowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlow bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlow = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlow {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskFlowResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTaskFlowResp[fieldId]))
}

func (p *DescribeTaskFlowResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTaskFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskFlow = _field
	return nil
}

func (p *DescribeTaskFlowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskFlowResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskFlowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskFlowResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlow", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TaskFlow.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskFlowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskFlowResp(%+v)", *p)

}

func (p *DescribeTaskFlowResp) DeepEqual(ano *DescribeTaskFlowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlow) {
		return false
	}
	return true
}

func (p *DescribeTaskFlowResp) Field1DeepEqual(src *TaskFlow) bool {

	if !p.TaskFlow.DeepEqual(src) {
		return false
	}
	return true
}

type DescribeTaskExecuteRecordsReq struct {
	TaskFlowID  string      `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
	PageNumber  *int32      `thrift:"PageNumber,2,optional" frugal:"2,optional,i32" json:"PageNumber,omitempty"`
	PageSize    *int32      `thrift:"PageSize,3,optional" frugal:"3,optional,i32" json:"PageSize,omitempty"`
	Filter      *JobFilter  `thrift:"Filter,4,optional" frugal:"4,optional,JobFilter" json:"Filter,omitempty"`
	OrderBy     *JobOrderBy `thrift:"OrderBy,5,optional" frugal:"5,optional,JobOrderBy" json:"OrderBy,omitempty"`
	SearchParam *string     `thrift:"SearchParam,6,optional" frugal:"6,optional,string" json:"SearchParam,omitempty"`
	SortBy      *SortBy     `thrift:"SortBy,7,optional" frugal:"7,optional,SortBy" json:"SortBy,omitempty"`
}

func NewDescribeTaskExecuteRecordsReq() *DescribeTaskExecuteRecordsReq {
	return &DescribeTaskExecuteRecordsReq{}
}

func (p *DescribeTaskExecuteRecordsReq) InitDefault() {
}

func (p *DescribeTaskExecuteRecordsReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}

var DescribeTaskExecuteRecordsReq_PageNumber_DEFAULT int32

func (p *DescribeTaskExecuteRecordsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTaskExecuteRecordsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTaskExecuteRecordsReq_PageSize_DEFAULT int32

func (p *DescribeTaskExecuteRecordsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTaskExecuteRecordsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTaskExecuteRecordsReq_Filter_DEFAULT *JobFilter

func (p *DescribeTaskExecuteRecordsReq) GetFilter() (v *JobFilter) {
	if !p.IsSetFilter() {
		return DescribeTaskExecuteRecordsReq_Filter_DEFAULT
	}
	return p.Filter
}

var DescribeTaskExecuteRecordsReq_OrderBy_DEFAULT JobOrderBy

func (p *DescribeTaskExecuteRecordsReq) GetOrderBy() (v JobOrderBy) {
	if !p.IsSetOrderBy() {
		return DescribeTaskExecuteRecordsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeTaskExecuteRecordsReq_SearchParam_DEFAULT string

func (p *DescribeTaskExecuteRecordsReq) GetSearchParam() (v string) {
	if !p.IsSetSearchParam() {
		return DescribeTaskExecuteRecordsReq_SearchParam_DEFAULT
	}
	return *p.SearchParam
}

var DescribeTaskExecuteRecordsReq_SortBy_DEFAULT SortBy

func (p *DescribeTaskExecuteRecordsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeTaskExecuteRecordsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}
func (p *DescribeTaskExecuteRecordsReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}
func (p *DescribeTaskExecuteRecordsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTaskExecuteRecordsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTaskExecuteRecordsReq) SetFilter(val *JobFilter) {
	p.Filter = val
}
func (p *DescribeTaskExecuteRecordsReq) SetOrderBy(val *JobOrderBy) {
	p.OrderBy = val
}
func (p *DescribeTaskExecuteRecordsReq) SetSearchParam(val *string) {
	p.SearchParam = val
}
func (p *DescribeTaskExecuteRecordsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}

var fieldIDToName_DescribeTaskExecuteRecordsReq = map[int16]string{
	1: "TaskFlowID",
	2: "PageNumber",
	3: "PageSize",
	4: "Filter",
	5: "OrderBy",
	6: "SearchParam",
	7: "SortBy",
}

func (p *DescribeTaskExecuteRecordsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTaskExecuteRecordsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTaskExecuteRecordsReq) IsSetFilter() bool {
	return p.Filter != nil
}

func (p *DescribeTaskExecuteRecordsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeTaskExecuteRecordsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeTaskExecuteRecordsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeTaskExecuteRecordsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskExecuteRecordsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskExecuteRecordsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTaskExecuteRecordsReq[fieldId]))
}

func (p *DescribeTaskExecuteRecordsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewJobFilter()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Filter = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *JobOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := JobOrderBy(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}

func (p *DescribeTaskExecuteRecordsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskExecuteRecordsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskExecuteRecordsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilter() {
		if err = oprot.WriteFieldBegin("Filter", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Filter.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SearchParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskExecuteRecordsReq(%+v)", *p)

}

func (p *DescribeTaskExecuteRecordsReq) DeepEqual(ano *DescribeTaskExecuteRecordsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.Filter) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field7DeepEqual(ano.SortBy) {
		return false
	}
	return true
}

func (p *DescribeTaskExecuteRecordsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field2DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field3DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field4DeepEqual(src *JobFilter) bool {

	if !p.Filter.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field5DeepEqual(src *JobOrderBy) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field6DeepEqual(src *string) bool {

	if p.SearchParam == src {
		return true
	} else if p.SearchParam == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SearchParam, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTaskExecuteRecordsReq) Field7DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}

type DescribeTaskExecuteRecordsResp struct {
	ExecuteRecords []*ExecuteRecord `thrift:"ExecuteRecords,1,required" frugal:"1,required,list<ExecuteRecord>" json:"ExecuteRecords"`
	Total          int32            `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeTaskExecuteRecordsResp() *DescribeTaskExecuteRecordsResp {
	return &DescribeTaskExecuteRecordsResp{}
}

func (p *DescribeTaskExecuteRecordsResp) InitDefault() {
}

func (p *DescribeTaskExecuteRecordsResp) GetExecuteRecords() (v []*ExecuteRecord) {
	return p.ExecuteRecords
}

func (p *DescribeTaskExecuteRecordsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTaskExecuteRecordsResp) SetExecuteRecords(val []*ExecuteRecord) {
	p.ExecuteRecords = val
}
func (p *DescribeTaskExecuteRecordsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTaskExecuteRecordsResp = map[int16]string{
	1: "ExecuteRecords",
	2: "Total",
}

func (p *DescribeTaskExecuteRecordsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskExecuteRecordsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExecuteRecords bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteRecords = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExecuteRecords {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTaskExecuteRecordsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTaskExecuteRecordsResp[fieldId]))
}

func (p *DescribeTaskExecuteRecordsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExecuteRecord, 0, size)
	values := make([]ExecuteRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ExecuteRecords = _field
	return nil
}
func (p *DescribeTaskExecuteRecordsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTaskExecuteRecordsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTaskExecuteRecordsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTaskExecuteRecordsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteRecords", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ExecuteRecords)); err != nil {
		return err
	}
	for _, v := range p.ExecuteRecords {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTaskExecuteRecordsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTaskExecuteRecordsResp(%+v)", *p)

}

func (p *DescribeTaskExecuteRecordsResp) DeepEqual(ano *DescribeTaskExecuteRecordsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExecuteRecords) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTaskExecuteRecordsResp) Field1DeepEqual(src []*ExecuteRecord) bool {

	if len(p.ExecuteRecords) != len(src) {
		return false
	}
	for i, v := range p.ExecuteRecords {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTaskExecuteRecordsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type JobFilter struct {
	JobState *JobState `thrift:"JobState,1,optional" frugal:"1,optional,JobState" json:"JobState,omitempty"`
}

func NewJobFilter() *JobFilter {
	return &JobFilter{}
}

func (p *JobFilter) InitDefault() {
}

var JobFilter_JobState_DEFAULT JobState

func (p *JobFilter) GetJobState() (v JobState) {
	if !p.IsSetJobState() {
		return JobFilter_JobState_DEFAULT
	}
	return *p.JobState
}
func (p *JobFilter) SetJobState(val *JobState) {
	p.JobState = val
}

var fieldIDToName_JobFilter = map[int16]string{
	1: "JobState",
}

func (p *JobFilter) IsSetJobState() bool {
	return p.JobState != nil
}

func (p *JobFilter) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("JobFilter")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_JobFilter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *JobFilter) ReadField1(iprot thrift.TProtocol) error {

	var _field *JobState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := JobState(v)
		_field = &tmp
	}
	p.JobState = _field
	return nil
}

func (p *JobFilter) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("JobFilter")

	var fieldId int16
	if err = oprot.WriteStructBegin("JobFilter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *JobFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetJobState() {
		if err = oprot.WriteFieldBegin("JobState", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.JobState)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *JobFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("JobFilter(%+v)", *p)

}

func (p *JobFilter) DeepEqual(ano *JobFilter) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.JobState) {
		return false
	}
	return true
}

func (p *JobFilter) Field1DeepEqual(src *JobState) bool {

	if p.JobState == src {
		return true
	} else if p.JobState == nil || src == nil {
		return false
	}
	if *p.JobState != *src {
		return false
	}
	return true
}

type ExecuteRecord struct {
	RecordID  string   `thrift:"RecordID,1,required" frugal:"1,required,string" json:"RecordID"`
	StartTime string   `thrift:"StartTime,2,required" frugal:"2,required,string" json:"StartTime"`
	EndTime   *string  `thrift:"EndTime,3,optional" frugal:"3,optional,string" json:"EndTime,omitempty"`
	State     JobState `thrift:"State,4,required" frugal:"4,required,JobState" json:"State"`
	Reason    string   `thrift:"Reason,5,required" frugal:"5,required,string" json:"Reason"`
}

func NewExecuteRecord() *ExecuteRecord {
	return &ExecuteRecord{}
}

func (p *ExecuteRecord) InitDefault() {
}

func (p *ExecuteRecord) GetRecordID() (v string) {
	return p.RecordID
}

func (p *ExecuteRecord) GetStartTime() (v string) {
	return p.StartTime
}

var ExecuteRecord_EndTime_DEFAULT string

func (p *ExecuteRecord) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return ExecuteRecord_EndTime_DEFAULT
	}
	return *p.EndTime
}

func (p *ExecuteRecord) GetState() (v JobState) {
	return p.State
}

func (p *ExecuteRecord) GetReason() (v string) {
	return p.Reason
}
func (p *ExecuteRecord) SetRecordID(val string) {
	p.RecordID = val
}
func (p *ExecuteRecord) SetStartTime(val string) {
	p.StartTime = val
}
func (p *ExecuteRecord) SetEndTime(val *string) {
	p.EndTime = val
}
func (p *ExecuteRecord) SetState(val JobState) {
	p.State = val
}
func (p *ExecuteRecord) SetReason(val string) {
	p.Reason = val
}

var fieldIDToName_ExecuteRecord = map[int16]string{
	1: "RecordID",
	2: "StartTime",
	3: "EndTime",
	4: "State",
	5: "Reason",
}

func (p *ExecuteRecord) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *ExecuteRecord) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteRecord")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRecordID bool = false
	var issetStartTime bool = false
	var issetState bool = false
	var issetReason bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRecordID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetReason = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRecordID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetState {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetReason {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteRecord[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteRecord[fieldId]))
}

func (p *ExecuteRecord) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *ExecuteRecord) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *ExecuteRecord) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *ExecuteRecord) ReadField4(iprot thrift.TProtocol) error {

	var _field JobState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = JobState(v)
	}
	p.State = _field
	return nil
}
func (p *ExecuteRecord) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}

func (p *ExecuteRecord) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteRecord")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteRecord"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RecordID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecuteRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ExecuteRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("State", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.State)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ExecuteRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Reason", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecuteRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteRecord(%+v)", *p)

}

func (p *ExecuteRecord) DeepEqual(ano *ExecuteRecord) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RecordID) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.State) {
		return false
	}
	if !p.Field5DeepEqual(ano.Reason) {
		return false
	}
	return true
}

func (p *ExecuteRecord) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RecordID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteRecord) Field2DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteRecord) Field3DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteRecord) Field4DeepEqual(src JobState) bool {

	if p.State != src {
		return false
	}
	return true
}
func (p *ExecuteRecord) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Reason, src) != 0 {
		return false
	}
	return true
}

type CreateTaskFlowReq struct {
	Name              string          `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	TaskType          TaskType        `thrift:"TaskType,2,required" frugal:"2,required,TaskType" json:"TaskType"`
	InstanceId        string          `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
	InstanceType      InstanceType    `thrift:"InstanceType,4,required" frugal:"4,required,InstanceType" json:"InstanceType"`
	ExecuteMethod     ExecuteMethod   `thrift:"ExecuteMethod,5,required" frugal:"5,required,ExecuteMethod" json:"ExecuteMethod"`
	ExecuteTime       *string         `thrift:"ExecuteTime,6,optional" frugal:"6,optional,string" json:"ExecuteTime,omitempty"`
	CycleConfig       *CycleConfig    `thrift:"CycleConfig,7,optional" frugal:"7,optional,CycleConfig" json:"CycleConfig,omitempty"`
	InstanceConfig    *InstanceConfig `thrift:"InstanceConfig,8,required" frugal:"8,required,InstanceConfig" json:"InstanceConfig"`
	RelatedTaskFlow   *string         `thrift:"RelatedTaskFlow,9,optional" frugal:"9,optional,string" json:"RelatedTaskFlow,omitempty"`
	EnableTransaction *bool           `thrift:"EnableTransaction,10,optional" frugal:"10,optional,bool" json:"EnableTransaction,omitempty"`
	IgnoreError       *bool           `thrift:"IgnoreError,11,optional" frugal:"11,optional,bool" json:"IgnoreError,omitempty"`
	Statement         string          `thrift:"Statement,12,required" frugal:"12,required,string" json:"Statement"`
	Comment           *string         `thrift:"Comment,13,optional" frugal:"13,optional,string" json:"Comment,omitempty"`
}

func NewCreateTaskFlowReq() *CreateTaskFlowReq {
	return &CreateTaskFlowReq{}
}

func (p *CreateTaskFlowReq) InitDefault() {
}

func (p *CreateTaskFlowReq) GetName() (v string) {
	return p.Name
}

func (p *CreateTaskFlowReq) GetTaskType() (v TaskType) {
	return p.TaskType
}

func (p *CreateTaskFlowReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateTaskFlowReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateTaskFlowReq) GetExecuteMethod() (v ExecuteMethod) {
	return p.ExecuteMethod
}

var CreateTaskFlowReq_ExecuteTime_DEFAULT string

func (p *CreateTaskFlowReq) GetExecuteTime() (v string) {
	if !p.IsSetExecuteTime() {
		return CreateTaskFlowReq_ExecuteTime_DEFAULT
	}
	return *p.ExecuteTime
}

var CreateTaskFlowReq_CycleConfig_DEFAULT *CycleConfig

func (p *CreateTaskFlowReq) GetCycleConfig() (v *CycleConfig) {
	if !p.IsSetCycleConfig() {
		return CreateTaskFlowReq_CycleConfig_DEFAULT
	}
	return p.CycleConfig
}

var CreateTaskFlowReq_InstanceConfig_DEFAULT *InstanceConfig

func (p *CreateTaskFlowReq) GetInstanceConfig() (v *InstanceConfig) {
	if !p.IsSetInstanceConfig() {
		return CreateTaskFlowReq_InstanceConfig_DEFAULT
	}
	return p.InstanceConfig
}

var CreateTaskFlowReq_RelatedTaskFlow_DEFAULT string

func (p *CreateTaskFlowReq) GetRelatedTaskFlow() (v string) {
	if !p.IsSetRelatedTaskFlow() {
		return CreateTaskFlowReq_RelatedTaskFlow_DEFAULT
	}
	return *p.RelatedTaskFlow
}

var CreateTaskFlowReq_EnableTransaction_DEFAULT bool

func (p *CreateTaskFlowReq) GetEnableTransaction() (v bool) {
	if !p.IsSetEnableTransaction() {
		return CreateTaskFlowReq_EnableTransaction_DEFAULT
	}
	return *p.EnableTransaction
}

var CreateTaskFlowReq_IgnoreError_DEFAULT bool

func (p *CreateTaskFlowReq) GetIgnoreError() (v bool) {
	if !p.IsSetIgnoreError() {
		return CreateTaskFlowReq_IgnoreError_DEFAULT
	}
	return *p.IgnoreError
}

func (p *CreateTaskFlowReq) GetStatement() (v string) {
	return p.Statement
}

var CreateTaskFlowReq_Comment_DEFAULT string

func (p *CreateTaskFlowReq) GetComment() (v string) {
	if !p.IsSetComment() {
		return CreateTaskFlowReq_Comment_DEFAULT
	}
	return *p.Comment
}
func (p *CreateTaskFlowReq) SetName(val string) {
	p.Name = val
}
func (p *CreateTaskFlowReq) SetTaskType(val TaskType) {
	p.TaskType = val
}
func (p *CreateTaskFlowReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateTaskFlowReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateTaskFlowReq) SetExecuteMethod(val ExecuteMethod) {
	p.ExecuteMethod = val
}
func (p *CreateTaskFlowReq) SetExecuteTime(val *string) {
	p.ExecuteTime = val
}
func (p *CreateTaskFlowReq) SetCycleConfig(val *CycleConfig) {
	p.CycleConfig = val
}
func (p *CreateTaskFlowReq) SetInstanceConfig(val *InstanceConfig) {
	p.InstanceConfig = val
}
func (p *CreateTaskFlowReq) SetRelatedTaskFlow(val *string) {
	p.RelatedTaskFlow = val
}
func (p *CreateTaskFlowReq) SetEnableTransaction(val *bool) {
	p.EnableTransaction = val
}
func (p *CreateTaskFlowReq) SetIgnoreError(val *bool) {
	p.IgnoreError = val
}
func (p *CreateTaskFlowReq) SetStatement(val string) {
	p.Statement = val
}
func (p *CreateTaskFlowReq) SetComment(val *string) {
	p.Comment = val
}

var fieldIDToName_CreateTaskFlowReq = map[int16]string{
	1:  "Name",
	2:  "TaskType",
	3:  "InstanceId",
	4:  "InstanceType",
	5:  "ExecuteMethod",
	6:  "ExecuteTime",
	7:  "CycleConfig",
	8:  "InstanceConfig",
	9:  "RelatedTaskFlow",
	10: "EnableTransaction",
	11: "IgnoreError",
	12: "Statement",
	13: "Comment",
}

func (p *CreateTaskFlowReq) IsSetExecuteTime() bool {
	return p.ExecuteTime != nil
}

func (p *CreateTaskFlowReq) IsSetCycleConfig() bool {
	return p.CycleConfig != nil
}

func (p *CreateTaskFlowReq) IsSetInstanceConfig() bool {
	return p.InstanceConfig != nil
}

func (p *CreateTaskFlowReq) IsSetRelatedTaskFlow() bool {
	return p.RelatedTaskFlow != nil
}

func (p *CreateTaskFlowReq) IsSetEnableTransaction() bool {
	return p.EnableTransaction != nil
}

func (p *CreateTaskFlowReq) IsSetIgnoreError() bool {
	return p.IgnoreError != nil
}

func (p *CreateTaskFlowReq) IsSetComment() bool {
	return p.Comment != nil
}

func (p *CreateTaskFlowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTaskFlowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetTaskType bool = false
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetExecuteMethod bool = false
	var issetInstanceConfig bool = false
	var issetStatement bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteMethod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTaskType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetExecuteMethod {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInstanceConfig {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetStatement {
		fieldId = 12
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTaskFlowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTaskFlowReq[fieldId]))
}

func (p *CreateTaskFlowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField2(iprot thrift.TProtocol) error {

	var _field TaskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TaskType(v)
	}
	p.TaskType = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField4(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField5(iprot thrift.TProtocol) error {

	var _field ExecuteMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecuteMethod(v)
	}
	p.ExecuteMethod = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteTime = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField7(iprot thrift.TProtocol) error {
	_field := NewCycleConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CycleConfig = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField8(iprot thrift.TProtocol) error {
	_field := NewInstanceConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceConfig = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RelatedTaskFlow = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableTransaction = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IgnoreError = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}
func (p *CreateTaskFlowReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}

func (p *CreateTaskFlowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTaskFlowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTaskFlowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteMethod", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ExecuteMethod)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteTime() {
		if err = oprot.WriteFieldBegin("ExecuteTime", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExecuteTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCycleConfig() {
		if err = oprot.WriteFieldBegin("CycleConfig", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CycleConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceConfig", thrift.STRUCT, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InstanceConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelatedTaskFlow() {
		if err = oprot.WriteFieldBegin("RelatedTaskFlow", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RelatedTaskFlow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableTransaction() {
		if err = oprot.WriteFieldBegin("EnableTransaction", thrift.BOOL, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableTransaction); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetIgnoreError() {
		if err = oprot.WriteFieldBegin("IgnoreError", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IgnoreError); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *CreateTaskFlowReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *CreateTaskFlowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTaskFlowReq(%+v)", *p)

}

func (p *CreateTaskFlowReq) DeepEqual(ano *CreateTaskFlowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExecuteMethod) {
		return false
	}
	if !p.Field6DeepEqual(ano.ExecuteTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.CycleConfig) {
		return false
	}
	if !p.Field8DeepEqual(ano.InstanceConfig) {
		return false
	}
	if !p.Field9DeepEqual(ano.RelatedTaskFlow) {
		return false
	}
	if !p.Field10DeepEqual(ano.EnableTransaction) {
		return false
	}
	if !p.Field11DeepEqual(ano.IgnoreError) {
		return false
	}
	if !p.Field12DeepEqual(ano.Statement) {
		return false
	}
	if !p.Field13DeepEqual(ano.Comment) {
		return false
	}
	return true
}

func (p *CreateTaskFlowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field2DeepEqual(src TaskType) bool {

	if p.TaskType != src {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field4DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field5DeepEqual(src ExecuteMethod) bool {

	if p.ExecuteMethod != src {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field6DeepEqual(src *string) bool {

	if p.ExecuteTime == src {
		return true
	} else if p.ExecuteTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ExecuteTime, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field7DeepEqual(src *CycleConfig) bool {

	if !p.CycleConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field8DeepEqual(src *InstanceConfig) bool {

	if !p.InstanceConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field9DeepEqual(src *string) bool {

	if p.RelatedTaskFlow == src {
		return true
	} else if p.RelatedTaskFlow == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RelatedTaskFlow, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field10DeepEqual(src *bool) bool {

	if p.EnableTransaction == src {
		return true
	} else if p.EnableTransaction == nil || src == nil {
		return false
	}
	if *p.EnableTransaction != *src {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field11DeepEqual(src *bool) bool {

	if p.IgnoreError == src {
		return true
	} else if p.IgnoreError == nil || src == nil {
		return false
	}
	if *p.IgnoreError != *src {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field12DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTaskFlowReq) Field13DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}

type CreateTaskFlowResp struct {
	TaskFlowID string `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
}

func NewCreateTaskFlowResp() *CreateTaskFlowResp {
	return &CreateTaskFlowResp{}
}

func (p *CreateTaskFlowResp) InitDefault() {
}

func (p *CreateTaskFlowResp) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}
func (p *CreateTaskFlowResp) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}

var fieldIDToName_CreateTaskFlowResp = map[int16]string{
	1: "TaskFlowID",
}

func (p *CreateTaskFlowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTaskFlowResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTaskFlowResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTaskFlowResp[fieldId]))
}

func (p *CreateTaskFlowResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}

func (p *CreateTaskFlowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTaskFlowResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTaskFlowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTaskFlowResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTaskFlowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTaskFlowResp(%+v)", *p)

}

func (p *CreateTaskFlowResp) DeepEqual(ano *CreateTaskFlowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	return true
}

func (p *CreateTaskFlowResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}

type CycleConfig struct {
	StartTime          string             `thrift:"StartTime,1,required" frugal:"1,required,string" json:"StartTime"`
	EndTime            *string            `thrift:"EndTime,2,optional" frugal:"2,optional,string" json:"EndTime,omitempty"`
	IntervalUnit       IntervalUnit       `thrift:"IntervalUnit,3,required" frugal:"3,required,IntervalUnit" json:"IntervalUnit"`
	Interval           int8               `thrift:"Interval,4,required" frugal:"4,required,i8" json:"Interval"`
	StillRunningPolicy StillRunningPolicy `thrift:"StillRunningPolicy,5,required" frugal:"5,required,StillRunningPolicy" json:"StillRunningPolicy"`
}

func NewCycleConfig() *CycleConfig {
	return &CycleConfig{}
}

func (p *CycleConfig) InitDefault() {
}

func (p *CycleConfig) GetStartTime() (v string) {
	return p.StartTime
}

var CycleConfig_EndTime_DEFAULT string

func (p *CycleConfig) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return CycleConfig_EndTime_DEFAULT
	}
	return *p.EndTime
}

func (p *CycleConfig) GetIntervalUnit() (v IntervalUnit) {
	return p.IntervalUnit
}

func (p *CycleConfig) GetInterval() (v int8) {
	return p.Interval
}

func (p *CycleConfig) GetStillRunningPolicy() (v StillRunningPolicy) {
	return p.StillRunningPolicy
}
func (p *CycleConfig) SetStartTime(val string) {
	p.StartTime = val
}
func (p *CycleConfig) SetEndTime(val *string) {
	p.EndTime = val
}
func (p *CycleConfig) SetIntervalUnit(val IntervalUnit) {
	p.IntervalUnit = val
}
func (p *CycleConfig) SetInterval(val int8) {
	p.Interval = val
}
func (p *CycleConfig) SetStillRunningPolicy(val StillRunningPolicy) {
	p.StillRunningPolicy = val
}

var fieldIDToName_CycleConfig = map[int16]string{
	1: "StartTime",
	2: "EndTime",
	3: "IntervalUnit",
	4: "Interval",
	5: "StillRunningPolicy",
}

func (p *CycleConfig) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *CycleConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CycleConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStartTime bool = false
	var issetIntervalUnit bool = false
	var issetInterval bool = false
	var issetStillRunningPolicy bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIntervalUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInterval = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStillRunningPolicy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStartTime {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIntervalUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInterval {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStillRunningPolicy {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CycleConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CycleConfig[fieldId]))
}

func (p *CycleConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *CycleConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *CycleConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field IntervalUnit
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = IntervalUnit(v)
	}
	p.IntervalUnit = _field
	return nil
}
func (p *CycleConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Interval = _field
	return nil
}
func (p *CycleConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field StillRunningPolicy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StillRunningPolicy(v)
	}
	p.StillRunningPolicy = _field
	return nil
}

func (p *CycleConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CycleConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("CycleConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CycleConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CycleConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CycleConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IntervalUnit", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.IntervalUnit)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CycleConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Interval", thrift.BYTE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.Interval); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CycleConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StillRunningPolicy", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.StillRunningPolicy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CycleConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CycleConfig(%+v)", *p)

}

func (p *CycleConfig) DeepEqual(ano *CycleConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field2DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.IntervalUnit) {
		return false
	}
	if !p.Field4DeepEqual(ano.Interval) {
		return false
	}
	if !p.Field5DeepEqual(ano.StillRunningPolicy) {
		return false
	}
	return true
}

func (p *CycleConfig) Field1DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *CycleConfig) Field2DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *CycleConfig) Field3DeepEqual(src IntervalUnit) bool {

	if p.IntervalUnit != src {
		return false
	}
	return true
}
func (p *CycleConfig) Field4DeepEqual(src int8) bool {

	if p.Interval != src {
		return false
	}
	return true
}
func (p *CycleConfig) Field5DeepEqual(src StillRunningPolicy) bool {

	if p.StillRunningPolicy != src {
		return false
	}
	return true
}

type InstanceConfig struct {
	UserName *string `thrift:"UserName,1,optional" frugal:"1,optional,string" json:"UserName,omitempty"`
	Password *string `thrift:"Password,2,optional" frugal:"2,optional,string" json:"Password,omitempty"`
	Database string  `thrift:"Database,3,required" frugal:"3,required,string" json:"Database"`
}

func NewInstanceConfig() *InstanceConfig {
	return &InstanceConfig{}
}

func (p *InstanceConfig) InitDefault() {
}

var InstanceConfig_UserName_DEFAULT string

func (p *InstanceConfig) GetUserName() (v string) {
	if !p.IsSetUserName() {
		return InstanceConfig_UserName_DEFAULT
	}
	return *p.UserName
}

var InstanceConfig_Password_DEFAULT string

func (p *InstanceConfig) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return InstanceConfig_Password_DEFAULT
	}
	return *p.Password
}

func (p *InstanceConfig) GetDatabase() (v string) {
	return p.Database
}
func (p *InstanceConfig) SetUserName(val *string) {
	p.UserName = val
}
func (p *InstanceConfig) SetPassword(val *string) {
	p.Password = val
}
func (p *InstanceConfig) SetDatabase(val string) {
	p.Database = val
}

var fieldIDToName_InstanceConfig = map[int16]string{
	1: "UserName",
	2: "Password",
	3: "Database",
}

func (p *InstanceConfig) IsSetUserName() bool {
	return p.UserName != nil
}

func (p *InstanceConfig) IsSetPassword() bool {
	return p.Password != nil
}

func (p *InstanceConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatabase bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabase = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatabase {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InstanceConfig[fieldId]))
}

func (p *InstanceConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserName = _field
	return nil
}
func (p *InstanceConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *InstanceConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Database = _field
	return nil
}

func (p *InstanceConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("InstanceConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserName() {
		if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InstanceConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InstanceConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Database", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Database); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InstanceConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceConfig(%+v)", *p)

}

func (p *InstanceConfig) DeepEqual(ano *InstanceConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Password) {
		return false
	}
	if !p.Field3DeepEqual(ano.Database) {
		return false
	}
	return true
}

func (p *InstanceConfig) Field1DeepEqual(src *string) bool {

	if p.UserName == src {
		return true
	} else if p.UserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserName, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceConfig) Field2DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceConfig) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Database, src) != 0 {
		return false
	}
	return true
}

type UpdateTaskFlowReq struct {
	TaskFlowID  string       `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
	State       *State       `thrift:"State,2,optional" frugal:"2,optional,State" json:"State,omitempty"`
	Name        *string      `thrift:"Name,3,optional" frugal:"3,optional,string" json:"Name,omitempty"`
	Statement   *string      `thrift:"Statement,4,optional" frugal:"4,optional,string" json:"Statement,omitempty"`
	Comment     *string      `thrift:"Comment,5,optional" frugal:"5,optional,string" json:"Comment,omitempty"`
	ExecuteTime *string      `thrift:"ExecuteTime,6,optional" frugal:"6,optional,string" json:"ExecuteTime,omitempty"`
	CycleConfig *CycleConfig `thrift:"CycleConfig,7,optional" frugal:"7,optional,CycleConfig" json:"CycleConfig,omitempty"`
}

func NewUpdateTaskFlowReq() *UpdateTaskFlowReq {
	return &UpdateTaskFlowReq{}
}

func (p *UpdateTaskFlowReq) InitDefault() {
}

func (p *UpdateTaskFlowReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}

var UpdateTaskFlowReq_State_DEFAULT State

func (p *UpdateTaskFlowReq) GetState() (v State) {
	if !p.IsSetState() {
		return UpdateTaskFlowReq_State_DEFAULT
	}
	return *p.State
}

var UpdateTaskFlowReq_Name_DEFAULT string

func (p *UpdateTaskFlowReq) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateTaskFlowReq_Name_DEFAULT
	}
	return *p.Name
}

var UpdateTaskFlowReq_Statement_DEFAULT string

func (p *UpdateTaskFlowReq) GetStatement() (v string) {
	if !p.IsSetStatement() {
		return UpdateTaskFlowReq_Statement_DEFAULT
	}
	return *p.Statement
}

var UpdateTaskFlowReq_Comment_DEFAULT string

func (p *UpdateTaskFlowReq) GetComment() (v string) {
	if !p.IsSetComment() {
		return UpdateTaskFlowReq_Comment_DEFAULT
	}
	return *p.Comment
}

var UpdateTaskFlowReq_ExecuteTime_DEFAULT string

func (p *UpdateTaskFlowReq) GetExecuteTime() (v string) {
	if !p.IsSetExecuteTime() {
		return UpdateTaskFlowReq_ExecuteTime_DEFAULT
	}
	return *p.ExecuteTime
}

var UpdateTaskFlowReq_CycleConfig_DEFAULT *CycleConfig

func (p *UpdateTaskFlowReq) GetCycleConfig() (v *CycleConfig) {
	if !p.IsSetCycleConfig() {
		return UpdateTaskFlowReq_CycleConfig_DEFAULT
	}
	return p.CycleConfig
}
func (p *UpdateTaskFlowReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}
func (p *UpdateTaskFlowReq) SetState(val *State) {
	p.State = val
}
func (p *UpdateTaskFlowReq) SetName(val *string) {
	p.Name = val
}
func (p *UpdateTaskFlowReq) SetStatement(val *string) {
	p.Statement = val
}
func (p *UpdateTaskFlowReq) SetComment(val *string) {
	p.Comment = val
}
func (p *UpdateTaskFlowReq) SetExecuteTime(val *string) {
	p.ExecuteTime = val
}
func (p *UpdateTaskFlowReq) SetCycleConfig(val *CycleConfig) {
	p.CycleConfig = val
}

var fieldIDToName_UpdateTaskFlowReq = map[int16]string{
	1: "TaskFlowID",
	2: "State",
	3: "Name",
	4: "Statement",
	5: "Comment",
	6: "ExecuteTime",
	7: "CycleConfig",
}

func (p *UpdateTaskFlowReq) IsSetState() bool {
	return p.State != nil
}

func (p *UpdateTaskFlowReq) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateTaskFlowReq) IsSetStatement() bool {
	return p.Statement != nil
}

func (p *UpdateTaskFlowReq) IsSetComment() bool {
	return p.Comment != nil
}

func (p *UpdateTaskFlowReq) IsSetExecuteTime() bool {
	return p.ExecuteTime != nil
}

func (p *UpdateTaskFlowReq) IsSetCycleConfig() bool {
	return p.CycleConfig != nil
}

func (p *UpdateTaskFlowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateTaskFlowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateTaskFlowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateTaskFlowReq[fieldId]))
}

func (p *UpdateTaskFlowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *State
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := State(v)
		_field = &tmp
	}
	p.State = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Statement = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteTime = _field
	return nil
}
func (p *UpdateTaskFlowReq) ReadField7(iprot thrift.TProtocol) error {
	_field := NewCycleConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CycleConfig = _field
	return nil
}

func (p *UpdateTaskFlowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateTaskFlowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateTaskFlowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetState() {
		if err = oprot.WriteFieldBegin("State", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.State)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatement() {
		if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Statement); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteTime() {
		if err = oprot.WriteFieldBegin("ExecuteTime", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExecuteTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCycleConfig() {
		if err = oprot.WriteFieldBegin("CycleConfig", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CycleConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *UpdateTaskFlowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskFlowReq(%+v)", *p)

}

func (p *UpdateTaskFlowReq) DeepEqual(ano *UpdateTaskFlowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	if !p.Field2DeepEqual(ano.State) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.Statement) {
		return false
	}
	if !p.Field5DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field6DeepEqual(ano.ExecuteTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.CycleConfig) {
		return false
	}
	return true
}

func (p *UpdateTaskFlowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field2DeepEqual(src *State) bool {

	if p.State == src {
		return true
	} else if p.State == nil || src == nil {
		return false
	}
	if *p.State != *src {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field3DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field4DeepEqual(src *string) bool {

	if p.Statement == src {
		return true
	} else if p.Statement == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Statement, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field5DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field6DeepEqual(src *string) bool {

	if p.ExecuteTime == src {
		return true
	} else if p.ExecuteTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ExecuteTime, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateTaskFlowReq) Field7DeepEqual(src *CycleConfig) bool {

	if !p.CycleConfig.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateTaskFlowResp struct {
}

func NewUpdateTaskFlowResp() *UpdateTaskFlowResp {
	return &UpdateTaskFlowResp{}
}

func (p *UpdateTaskFlowResp) InitDefault() {
}

var fieldIDToName_UpdateTaskFlowResp = map[int16]string{}

func (p *UpdateTaskFlowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateTaskFlowResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateTaskFlowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateTaskFlowResp")

	if err = oprot.WriteStructBegin("UpdateTaskFlowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateTaskFlowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskFlowResp(%+v)", *p)

}

func (p *UpdateTaskFlowResp) DeepEqual(ano *UpdateTaskFlowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteTaskFlowReq struct {
	TaskFlowID string `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
}

func NewDeleteTaskFlowReq() *DeleteTaskFlowReq {
	return &DeleteTaskFlowReq{}
}

func (p *DeleteTaskFlowReq) InitDefault() {
}

func (p *DeleteTaskFlowReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}
func (p *DeleteTaskFlowReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}

var fieldIDToName_DeleteTaskFlowReq = map[int16]string{
	1: "TaskFlowID",
}

func (p *DeleteTaskFlowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteTaskFlowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteTaskFlowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteTaskFlowReq[fieldId]))
}

func (p *DeleteTaskFlowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}

func (p *DeleteTaskFlowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteTaskFlowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteTaskFlowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteTaskFlowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteTaskFlowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTaskFlowReq(%+v)", *p)

}

func (p *DeleteTaskFlowReq) DeepEqual(ano *DeleteTaskFlowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	return true
}

func (p *DeleteTaskFlowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}

type DeleteTaskFlowResp struct {
}

func NewDeleteTaskFlowResp() *DeleteTaskFlowResp {
	return &DeleteTaskFlowResp{}
}

func (p *DeleteTaskFlowResp) InitDefault() {
}

var fieldIDToName_DeleteTaskFlowResp = map[int16]string{}

func (p *DeleteTaskFlowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteTaskFlowResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteTaskFlowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteTaskFlowResp")

	if err = oprot.WriteStructBegin("DeleteTaskFlowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteTaskFlowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTaskFlowResp(%+v)", *p)

}

func (p *DeleteTaskFlowResp) DeepEqual(ano *DeleteTaskFlowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeInstanceDatabasesReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	UserName     *string      `thrift:"UserName,3,optional" frugal:"3,optional,string" json:"UserName,omitempty"`
	Password     *string      `thrift:"Password,4,optional" frugal:"4,optional,string" json:"Password,omitempty"`
	PageNumber   *int32       `thrift:"PageNumber,5,optional" frugal:"5,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32       `thrift:"PageSize,6,optional" frugal:"6,optional,i32" json:"PageSize,omitempty"`
	SearchParam  *string      `thrift:"SearchParam,7,optional" frugal:"7,optional,string" json:"SearchParam,omitempty"`
	RegionId     *string      `thrift:"RegionId,8,optional" frugal:"8,optional,string" json:"RegionId,omitempty"`
}

func NewDescribeInstanceDatabasesReq() *DescribeInstanceDatabasesReq {
	return &DescribeInstanceDatabasesReq{}
}

func (p *DescribeInstanceDatabasesReq) InitDefault() {
}

func (p *DescribeInstanceDatabasesReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeInstanceDatabasesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeInstanceDatabasesReq_UserName_DEFAULT string

func (p *DescribeInstanceDatabasesReq) GetUserName() (v string) {
	if !p.IsSetUserName() {
		return DescribeInstanceDatabasesReq_UserName_DEFAULT
	}
	return *p.UserName
}

var DescribeInstanceDatabasesReq_Password_DEFAULT string

func (p *DescribeInstanceDatabasesReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return DescribeInstanceDatabasesReq_Password_DEFAULT
	}
	return *p.Password
}

var DescribeInstanceDatabasesReq_PageNumber_DEFAULT int32

func (p *DescribeInstanceDatabasesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeInstanceDatabasesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeInstanceDatabasesReq_PageSize_DEFAULT int32

func (p *DescribeInstanceDatabasesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeInstanceDatabasesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeInstanceDatabasesReq_SearchParam_DEFAULT string

func (p *DescribeInstanceDatabasesReq) GetSearchParam() (v string) {
	if !p.IsSetSearchParam() {
		return DescribeInstanceDatabasesReq_SearchParam_DEFAULT
	}
	return *p.SearchParam
}

var DescribeInstanceDatabasesReq_RegionId_DEFAULT string

func (p *DescribeInstanceDatabasesReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeInstanceDatabasesReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeInstanceDatabasesReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeInstanceDatabasesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeInstanceDatabasesReq) SetUserName(val *string) {
	p.UserName = val
}
func (p *DescribeInstanceDatabasesReq) SetPassword(val *string) {
	p.Password = val
}
func (p *DescribeInstanceDatabasesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeInstanceDatabasesReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeInstanceDatabasesReq) SetSearchParam(val *string) {
	p.SearchParam = val
}
func (p *DescribeInstanceDatabasesReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeInstanceDatabasesReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "UserName",
	4: "Password",
	5: "PageNumber",
	6: "PageSize",
	7: "SearchParam",
	8: "RegionId",
}

func (p *DescribeInstanceDatabasesReq) IsSetUserName() bool {
	return p.UserName != nil
}

func (p *DescribeInstanceDatabasesReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *DescribeInstanceDatabasesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeInstanceDatabasesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeInstanceDatabasesReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeInstanceDatabasesReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeInstanceDatabasesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDatabasesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDatabasesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceDatabasesReq[fieldId]))
}

func (p *DescribeInstanceDatabasesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserName = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeInstanceDatabasesReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeInstanceDatabasesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDatabasesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDatabasesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserName() {
		if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SearchParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDatabasesReq(%+v)", *p)

}

func (p *DescribeInstanceDatabasesReq) DeepEqual(ano *DescribeInstanceDatabasesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Password) {
		return false
	}
	if !p.Field5DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field7DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field8DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeInstanceDatabasesReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field3DeepEqual(src *string) bool {

	if p.UserName == src {
		return true
	} else if p.UserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field4DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field5DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field6DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field7DeepEqual(src *string) bool {

	if p.SearchParam == src {
		return true
	} else if p.SearchParam == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SearchParam, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceDatabasesReq) Field8DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceDatabasesResp struct {
	Databases []string `thrift:"Databases,1,required" frugal:"1,required,list<string>" json:"Databases"`
	Total     int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeInstanceDatabasesResp() *DescribeInstanceDatabasesResp {
	return &DescribeInstanceDatabasesResp{}
}

func (p *DescribeInstanceDatabasesResp) InitDefault() {
}

func (p *DescribeInstanceDatabasesResp) GetDatabases() (v []string) {
	return p.Databases
}

func (p *DescribeInstanceDatabasesResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeInstanceDatabasesResp) SetDatabases(val []string) {
	p.Databases = val
}
func (p *DescribeInstanceDatabasesResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeInstanceDatabasesResp = map[int16]string{
	1: "Databases",
	2: "Total",
}

func (p *DescribeInstanceDatabasesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDatabasesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatabases bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabases = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatabases {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDatabasesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceDatabasesResp[fieldId]))
}

func (p *DescribeInstanceDatabasesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Databases = _field
	return nil
}
func (p *DescribeInstanceDatabasesResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeInstanceDatabasesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDatabasesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDatabasesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDatabasesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Databases", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Databases)); err != nil {
		return err
	}
	for _, v := range p.Databases {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceDatabasesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDatabasesResp(%+v)", *p)

}

func (p *DescribeInstanceDatabasesResp) DeepEqual(ano *DescribeInstanceDatabasesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Databases) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeInstanceDatabasesResp) Field1DeepEqual(src []string) bool {

	if len(p.Databases) != len(src) {
		return false
	}
	for i, v := range p.Databases {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeInstanceDatabasesResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeExecuteLogsReq struct {
	TaskFlowID string `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
	RecordID   string `thrift:"RecordID,2,required" frugal:"2,required,string" json:"RecordID"`
}

func NewDescribeExecuteLogsReq() *DescribeExecuteLogsReq {
	return &DescribeExecuteLogsReq{}
}

func (p *DescribeExecuteLogsReq) InitDefault() {
}

func (p *DescribeExecuteLogsReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}

func (p *DescribeExecuteLogsReq) GetRecordID() (v string) {
	return p.RecordID
}
func (p *DescribeExecuteLogsReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}
func (p *DescribeExecuteLogsReq) SetRecordID(val string) {
	p.RecordID = val
}

var fieldIDToName_DescribeExecuteLogsReq = map[int16]string{
	1: "TaskFlowID",
	2: "RecordID",
}

func (p *DescribeExecuteLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExecuteLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false
	var issetRecordID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRecordID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRecordID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeExecuteLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeExecuteLogsReq[fieldId]))
}

func (p *DescribeExecuteLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}
func (p *DescribeExecuteLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}

func (p *DescribeExecuteLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExecuteLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeExecuteLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeExecuteLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeExecuteLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RecordID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeExecuteLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeExecuteLogsReq(%+v)", *p)

}

func (p *DescribeExecuteLogsReq) DeepEqual(ano *DescribeExecuteLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	if !p.Field2DeepEqual(ano.RecordID) {
		return false
	}
	return true
}

func (p *DescribeExecuteLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeExecuteLogsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RecordID, src) != 0 {
		return false
	}
	return true
}

type DescribeExecuteLogsResp struct {
	Logs []*Log `thrift:"Logs,1,required" frugal:"1,required,list<Log>" json:"Logs"`
}

func NewDescribeExecuteLogsResp() *DescribeExecuteLogsResp {
	return &DescribeExecuteLogsResp{}
}

func (p *DescribeExecuteLogsResp) InitDefault() {
}

func (p *DescribeExecuteLogsResp) GetLogs() (v []*Log) {
	return p.Logs
}
func (p *DescribeExecuteLogsResp) SetLogs(val []*Log) {
	p.Logs = val
}

var fieldIDToName_DescribeExecuteLogsResp = map[int16]string{
	1: "Logs",
}

func (p *DescribeExecuteLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExecuteLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLogs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeExecuteLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeExecuteLogsResp[fieldId]))
}

func (p *DescribeExecuteLogsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Log, 0, size)
	values := make([]Log, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Logs = _field
	return nil
}

func (p *DescribeExecuteLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeExecuteLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeExecuteLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeExecuteLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Logs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Logs)); err != nil {
		return err
	}
	for _, v := range p.Logs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeExecuteLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeExecuteLogsResp(%+v)", *p)

}

func (p *DescribeExecuteLogsResp) DeepEqual(ano *DescribeExecuteLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Logs) {
		return false
	}
	return true
}

func (p *DescribeExecuteLogsResp) Field1DeepEqual(src []*Log) bool {

	if len(p.Logs) != len(src) {
		return false
	}
	for i, v := range p.Logs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type Log struct {
	TimeStamp string `thrift:"TimeStamp,1,required" frugal:"1,required,string" json:"TimeStamp"`
	Message   string `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
}

func NewLog() *Log {
	return &Log{}
}

func (p *Log) InitDefault() {
}

func (p *Log) GetTimeStamp() (v string) {
	return p.TimeStamp
}

func (p *Log) GetMessage() (v string) {
	return p.Message
}
func (p *Log) SetTimeStamp(val string) {
	p.TimeStamp = val
}
func (p *Log) SetMessage(val string) {
	p.Message = val
}

var fieldIDToName_Log = map[int16]string{
	1: "TimeStamp",
	2: "Message",
}

func (p *Log) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Log")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimeStamp bool = false
	var issetMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeStamp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimeStamp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Log[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Log[fieldId]))
}

func (p *Log) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeStamp = _field
	return nil
}
func (p *Log) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *Log) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Log")

	var fieldId int16
	if err = oprot.WriteStructBegin("Log"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Log) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeStamp", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeStamp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Log) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Log) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Log(%+v)", *p)

}

func (p *Log) DeepEqual(ano *Log) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TimeStamp) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	return true
}

func (p *Log) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TimeStamp, src) != 0 {
		return false
	}
	return true
}
func (p *Log) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}

type TriggerTaskFlowReq struct {
	TaskFlowID string `thrift:"TaskFlowID,1,required" frugal:"1,required,string" json:"TaskFlowID"`
}

func NewTriggerTaskFlowReq() *TriggerTaskFlowReq {
	return &TriggerTaskFlowReq{}
}

func (p *TriggerTaskFlowReq) InitDefault() {
}

func (p *TriggerTaskFlowReq) GetTaskFlowID() (v string) {
	return p.TaskFlowID
}
func (p *TriggerTaskFlowReq) SetTaskFlowID(val string) {
	p.TaskFlowID = val
}

var fieldIDToName_TriggerTaskFlowReq = map[int16]string{
	1: "TaskFlowID",
}

func (p *TriggerTaskFlowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerTaskFlowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskFlowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskFlowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskFlowID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TriggerTaskFlowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TriggerTaskFlowReq[fieldId]))
}

func (p *TriggerTaskFlowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskFlowID = _field
	return nil
}

func (p *TriggerTaskFlowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerTaskFlowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("TriggerTaskFlowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerTaskFlowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskFlowID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TriggerTaskFlowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerTaskFlowReq(%+v)", *p)

}

func (p *TriggerTaskFlowReq) DeepEqual(ano *TriggerTaskFlowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskFlowID) {
		return false
	}
	return true
}

func (p *TriggerTaskFlowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskFlowID, src) != 0 {
		return false
	}
	return true
}

type TriggerTaskFlowResp struct {
}

func NewTriggerTaskFlowResp() *TriggerTaskFlowResp {
	return &TriggerTaskFlowResp{}
}

func (p *TriggerTaskFlowResp) InitDefault() {
}

var fieldIDToName_TriggerTaskFlowResp = map[int16]string{}

func (p *TriggerTaskFlowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerTaskFlowResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TriggerTaskFlowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TriggerTaskFlowResp")

	if err = oprot.WriteStructBegin("TriggerTaskFlowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerTaskFlowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerTaskFlowResp(%+v)", *p)

}

func (p *TriggerTaskFlowResp) DeepEqual(ano *TriggerTaskFlowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

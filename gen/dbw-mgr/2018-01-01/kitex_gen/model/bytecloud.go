// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ServiceTreeInfo struct {
	PSM          string   `thrift:"PSM,1,required" frugal:"1,required,string" json:"PSM"`
	NodeId       string   `thrift:"NodeId,2,required" frugal:"2,required,string" json:"NodeId"`
	CNPath       string   `thrift:"CNPath,3,required" frugal:"3,required,string" json:"CNPath"`
	I18nPath     string   `thrift:"I18nPath,4,required" frugal:"4,required,string" json:"I18nPath"`
	ParentID     string   `thrift:"ParentID,5,required" frugal:"5,required,string" json:"ParentID"`
	AllParentIds []string `thrift:"AllParentIds,6,optional" frugal:"6,optional,list<string>" json:"AllParentIds,omitempty"`
}

func NewServiceTreeInfo() *ServiceTreeInfo {
	return &ServiceTreeInfo{}
}

func (p *ServiceTreeInfo) InitDefault() {
}

func (p *ServiceTreeInfo) GetPSM() (v string) {
	return p.PSM
}

func (p *ServiceTreeInfo) GetNodeId() (v string) {
	return p.NodeId
}

func (p *ServiceTreeInfo) GetCNPath() (v string) {
	return p.CNPath
}

func (p *ServiceTreeInfo) GetI18nPath() (v string) {
	return p.I18nPath
}

func (p *ServiceTreeInfo) GetParentID() (v string) {
	return p.ParentID
}

var ServiceTreeInfo_AllParentIds_DEFAULT []string

func (p *ServiceTreeInfo) GetAllParentIds() (v []string) {
	if !p.IsSetAllParentIds() {
		return ServiceTreeInfo_AllParentIds_DEFAULT
	}
	return p.AllParentIds
}
func (p *ServiceTreeInfo) SetPSM(val string) {
	p.PSM = val
}
func (p *ServiceTreeInfo) SetNodeId(val string) {
	p.NodeId = val
}
func (p *ServiceTreeInfo) SetCNPath(val string) {
	p.CNPath = val
}
func (p *ServiceTreeInfo) SetI18nPath(val string) {
	p.I18nPath = val
}
func (p *ServiceTreeInfo) SetParentID(val string) {
	p.ParentID = val
}
func (p *ServiceTreeInfo) SetAllParentIds(val []string) {
	p.AllParentIds = val
}

var fieldIDToName_ServiceTreeInfo = map[int16]string{
	1: "PSM",
	2: "NodeId",
	3: "CNPath",
	4: "I18nPath",
	5: "ParentID",
	6: "AllParentIds",
}

func (p *ServiceTreeInfo) IsSetAllParentIds() bool {
	return p.AllParentIds != nil
}

func (p *ServiceTreeInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ServiceTreeInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPSM bool = false
	var issetNodeId bool = false
	var issetCNPath bool = false
	var issetI18nPath bool = false
	var issetParentID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetPSM = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCNPath = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetI18nPath = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetParentID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPSM {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCNPath {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetI18nPath {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetParentID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ServiceTreeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ServiceTreeInfo[fieldId]))
}

func (p *ServiceTreeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PSM = _field
	return nil
}
func (p *ServiceTreeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeId = _field
	return nil
}
func (p *ServiceTreeInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CNPath = _field
	return nil
}
func (p *ServiceTreeInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.I18nPath = _field
	return nil
}
func (p *ServiceTreeInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParentID = _field
	return nil
}
func (p *ServiceTreeInfo) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AllParentIds = _field
	return nil
}

func (p *ServiceTreeInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ServiceTreeInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ServiceTreeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PSM", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PSM); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CNPath", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CNPath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("I18nPath", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.I18nPath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParentID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParentID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ServiceTreeInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllParentIds() {
		if err = oprot.WriteFieldBegin("AllParentIds", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AllParentIds)); err != nil {
			return err
		}
		for _, v := range p.AllParentIds {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ServiceTreeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ServiceTreeInfo(%+v)", *p)

}

func (p *ServiceTreeInfo) DeepEqual(ano *ServiceTreeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PSM) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.CNPath) {
		return false
	}
	if !p.Field4DeepEqual(ano.I18nPath) {
		return false
	}
	if !p.Field5DeepEqual(ano.ParentID) {
		return false
	}
	if !p.Field6DeepEqual(ano.AllParentIds) {
		return false
	}
	return true
}

func (p *ServiceTreeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.PSM, src) != 0 {
		return false
	}
	return true
}
func (p *ServiceTreeInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NodeId, src) != 0 {
		return false
	}
	return true
}
func (p *ServiceTreeInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CNPath, src) != 0 {
		return false
	}
	return true
}
func (p *ServiceTreeInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.I18nPath, src) != 0 {
		return false
	}
	return true
}
func (p *ServiceTreeInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ParentID, src) != 0 {
		return false
	}
	return true
}
func (p *ServiceTreeInfo) Field6DeepEqual(src []string) bool {

	if len(p.AllParentIds) != len(src) {
		return false
	}
	for i, v := range p.AllParentIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DescribeDbTreeMountInfoReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType *InstanceType `thrift:"InstanceType,2,optional" frugal:"2,optional,InstanceType" json:"InstanceType,omitempty"`
	RegionId     string        `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
}

func NewDescribeDbTreeMountInfoReq() *DescribeDbTreeMountInfoReq {
	return &DescribeDbTreeMountInfoReq{}
}

func (p *DescribeDbTreeMountInfoReq) InitDefault() {
}

func (p *DescribeDbTreeMountInfoReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeDbTreeMountInfoReq_InstanceType_DEFAULT InstanceType

func (p *DescribeDbTreeMountInfoReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DescribeDbTreeMountInfoReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

func (p *DescribeDbTreeMountInfoReq) GetRegionId() (v string) {
	return p.RegionId
}
func (p *DescribeDbTreeMountInfoReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeDbTreeMountInfoReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDbTreeMountInfoReq) SetRegionId(val string) {
	p.RegionId = val
}

var fieldIDToName_DescribeDbTreeMountInfoReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "RegionId",
}

func (p *DescribeDbTreeMountInfoReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeDbTreeMountInfoReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDbTreeMountInfoReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDbTreeMountInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDbTreeMountInfoReq[fieldId]))
}

func (p *DescribeDbTreeMountInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeDbTreeMountInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDbTreeMountInfoReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeDbTreeMountInfoReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDbTreeMountInfoReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDbTreeMountInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDbTreeMountInfoReq(%+v)", *p)

}

func (p *DescribeDbTreeMountInfoReq) DeepEqual(ano *DescribeDbTreeMountInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeDbTreeMountInfoReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDbTreeMountInfoReq) Field2DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeDbTreeMountInfoReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}

type DescribeDbTreeMountInfoResp struct {
	ServiceTreeInfos []*ServiceTreeInfo `thrift:"ServiceTreeInfos,1,required" frugal:"1,required,list<ServiceTreeInfo>" json:"ServiceTreeInfos"`
}

func NewDescribeDbTreeMountInfoResp() *DescribeDbTreeMountInfoResp {
	return &DescribeDbTreeMountInfoResp{}
}

func (p *DescribeDbTreeMountInfoResp) InitDefault() {
}

func (p *DescribeDbTreeMountInfoResp) GetServiceTreeInfos() (v []*ServiceTreeInfo) {
	return p.ServiceTreeInfos
}
func (p *DescribeDbTreeMountInfoResp) SetServiceTreeInfos(val []*ServiceTreeInfo) {
	p.ServiceTreeInfos = val
}

var fieldIDToName_DescribeDbTreeMountInfoResp = map[int16]string{
	1: "ServiceTreeInfos",
}

func (p *DescribeDbTreeMountInfoResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDbTreeMountInfoResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetServiceTreeInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetServiceTreeInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetServiceTreeInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDbTreeMountInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDbTreeMountInfoResp[fieldId]))
}

func (p *DescribeDbTreeMountInfoResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ServiceTreeInfo, 0, size)
	values := make([]ServiceTreeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ServiceTreeInfos = _field
	return nil
}

func (p *DescribeDbTreeMountInfoResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDbTreeMountInfoResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDbTreeMountInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ServiceTreeInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ServiceTreeInfos)); err != nil {
		return err
	}
	for _, v := range p.ServiceTreeInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDbTreeMountInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDbTreeMountInfoResp(%+v)", *p)

}

func (p *DescribeDbTreeMountInfoResp) DeepEqual(ano *DescribeDbTreeMountInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ServiceTreeInfos) {
		return false
	}
	return true
}

func (p *DescribeDbTreeMountInfoResp) Field1DeepEqual(src []*ServiceTreeInfo) bool {

	if len(p.ServiceTreeInfos) != len(src) {
		return false
	}
	for i, v := range p.ServiceTreeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

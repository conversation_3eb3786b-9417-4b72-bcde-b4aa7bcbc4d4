// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ScheduleMethod int64

const (
	ScheduleMethod_FixedTime ScheduleMethod = 0
	ScheduleMethod_Cycle     ScheduleMethod = 1
)

func (p ScheduleMethod) String() string {
	switch p {
	case ScheduleMethod_FixedTime:
		return "FixedTime"
	case ScheduleMethod_Cycle:
		return "Cycle"
	}
	return "<UNSET>"
}

func ScheduleMethodFromString(s string) (ScheduleMethod, error) {
	switch s {
	case "FixedTime":
		return ScheduleMethod_FixedTime, nil
	case "Cycle":
		return ScheduleMethod_Cycle, nil
	}
	return ScheduleMethod(0), fmt.Errorf("not a valid ScheduleMethod string")
}

func ScheduleMethodPtr(v ScheduleMethod) *ScheduleMethod { return &v }

func (p ScheduleMethod) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ScheduleMethod) UnmarshalText(text []byte) error {
	q, err := ScheduleMethodFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type Unit int64

const (
	Unit_NoneUnit Unit = 0
	Unit_Year     Unit = 1
	Unit_Month    Unit = 2
	Unit_Week     Unit = 3
	Unit_DAY      Unit = 4
	Unit_Hour     Unit = 5
	Unit_Minute   Unit = 6
	Unit_Second   Unit = 7
)

func (p Unit) String() string {
	switch p {
	case Unit_NoneUnit:
		return "NoneUnit"
	case Unit_Year:
		return "Year"
	case Unit_Month:
		return "Month"
	case Unit_Week:
		return "Week"
	case Unit_DAY:
		return "DAY"
	case Unit_Hour:
		return "Hour"
	case Unit_Minute:
		return "Minute"
	case Unit_Second:
		return "Second"
	}
	return "<UNSET>"
}

func UnitFromString(s string) (Unit, error) {
	switch s {
	case "NoneUnit":
		return Unit_NoneUnit, nil
	case "Year":
		return Unit_Year, nil
	case "Month":
		return Unit_Month, nil
	case "Week":
		return Unit_Week, nil
	case "DAY":
		return Unit_DAY, nil
	case "Hour":
		return Unit_Hour, nil
	case "Minute":
		return Unit_Minute, nil
	case "Second":
		return Unit_Second, nil
	}
	return Unit(0), fmt.Errorf("not a valid Unit string")
}

func UnitPtr(v Unit) *Unit { return &v }

func (p Unit) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *Unit) UnmarshalText(text []byte) error {
	q, err := UnitFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type EventState int64

const (
	EventState_Enable         EventState = 0
	EventState_Disable        EventState = 1
	EventState_DisableOnSlave EventState = 2
)

func (p EventState) String() string {
	switch p {
	case EventState_Enable:
		return "Enable"
	case EventState_Disable:
		return "Disable"
	case EventState_DisableOnSlave:
		return "DisableOnSlave"
	}
	return "<UNSET>"
}

func EventStateFromString(s string) (EventState, error) {
	switch s {
	case "Enable":
		return EventState_Enable, nil
	case "Disable":
		return EventState_Disable, nil
	case "DisableOnSlave":
		return EventState_DisableOnSlave, nil
	}
	return EventState(0), fmt.Errorf("not a valid EventState string")
}

func EventStatePtr(v EventState) *EventState { return &v }

func (p EventState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *EventState) UnmarshalText(text []byte) error {
	q, err := EventStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CreateEventReq struct {
	SessionId string     `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB        string     `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	EventMeta *EventMeta `thrift:"EventMeta,3,required" frugal:"3,required,EventMeta" json:"EventMeta"`
}

func NewCreateEventReq() *CreateEventReq {
	return &CreateEventReq{}
}

func (p *CreateEventReq) InitDefault() {
}

func (p *CreateEventReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *CreateEventReq) GetDB() (v string) {
	return p.DB
}

var CreateEventReq_EventMeta_DEFAULT *EventMeta

func (p *CreateEventReq) GetEventMeta() (v *EventMeta) {
	if !p.IsSetEventMeta() {
		return CreateEventReq_EventMeta_DEFAULT
	}
	return p.EventMeta
}
func (p *CreateEventReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *CreateEventReq) SetDB(val string) {
	p.DB = val
}
func (p *CreateEventReq) SetEventMeta(val *EventMeta) {
	p.EventMeta = val
}

var fieldIDToName_CreateEventReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "EventMeta",
}

func (p *CreateEventReq) IsSetEventMeta() bool {
	return p.EventMeta != nil
}

func (p *CreateEventReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateEventReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false
	var issetEventMeta bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventMeta = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEventMeta {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateEventReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateEventReq[fieldId]))
}

func (p *CreateEventReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *CreateEventReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *CreateEventReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewEventMeta()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.EventMeta = _field
	return nil
}

func (p *CreateEventReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateEventReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateEventReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateEventReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateEventReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateEventReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventMeta", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.EventMeta.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateEventReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateEventReq(%+v)", *p)

}

func (p *CreateEventReq) DeepEqual(ano *CreateEventReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.EventMeta) {
		return false
	}
	return true
}

func (p *CreateEventReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateEventReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *CreateEventReq) Field3DeepEqual(src *EventMeta) bool {

	if !p.EventMeta.DeepEqual(src) {
		return false
	}
	return true
}

type CreateEventResp struct {
	Statement string `thrift:"Statement,1,required" frugal:"1,required,string" json:"Statement"`
}

func NewCreateEventResp() *CreateEventResp {
	return &CreateEventResp{}
}

func (p *CreateEventResp) InitDefault() {
}

func (p *CreateEventResp) GetStatement() (v string) {
	return p.Statement
}
func (p *CreateEventResp) SetStatement(val string) {
	p.Statement = val
}

var fieldIDToName_CreateEventResp = map[int16]string{
	1: "Statement",
}

func (p *CreateEventResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateEventResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatement bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStatement {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateEventResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateEventResp[fieldId]))
}

func (p *CreateEventResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}

func (p *CreateEventResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateEventResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateEventResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateEventResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateEventResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateEventResp(%+v)", *p)

}

func (p *CreateEventResp) DeepEqual(ano *CreateEventResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Statement) {
		return false
	}
	return true
}

func (p *CreateEventResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}

type EventMeta struct {
	Name           string         `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	ScheduleMethod ScheduleMethod `thrift:"ScheduleMethod,2,required" frugal:"2,required,ScheduleMethod" json:"ScheduleMethod"`
	TimeStamp      *string        `thrift:"TimeStamp,3,optional" frugal:"3,optional,string" json:"TimeStamp,omitempty"`
	Interval       *Interval      `thrift:"Interval,4,optional" frugal:"4,optional,Interval" json:"Interval,omitempty"`
	Preserve       bool           `thrift:"Preserve,5,required" frugal:"5,required,bool" json:"Preserve"`
	EventState     EventState     `thrift:"EventState,6,required" frugal:"6,required,EventState" json:"EventState"`
	Do             string         `thrift:"Do,7,required" frugal:"7,required,string" json:"Do"`
	Comment        *string        `thrift:"Comment,8,optional" frugal:"8,optional,string" json:"Comment,omitempty"`
	StartTime      *string        `thrift:"StartTime,9,optional" frugal:"9,optional,string" json:"StartTime,omitempty"`
	EndTime        *string        `thrift:"EndTime,10,optional" frugal:"10,optional,string" json:"EndTime,omitempty"`
}

func NewEventMeta() *EventMeta {
	return &EventMeta{}
}

func (p *EventMeta) InitDefault() {
}

func (p *EventMeta) GetName() (v string) {
	return p.Name
}

func (p *EventMeta) GetScheduleMethod() (v ScheduleMethod) {
	return p.ScheduleMethod
}

var EventMeta_TimeStamp_DEFAULT string

func (p *EventMeta) GetTimeStamp() (v string) {
	if !p.IsSetTimeStamp() {
		return EventMeta_TimeStamp_DEFAULT
	}
	return *p.TimeStamp
}

var EventMeta_Interval_DEFAULT *Interval

func (p *EventMeta) GetInterval() (v *Interval) {
	if !p.IsSetInterval() {
		return EventMeta_Interval_DEFAULT
	}
	return p.Interval
}

func (p *EventMeta) GetPreserve() (v bool) {
	return p.Preserve
}

func (p *EventMeta) GetEventState() (v EventState) {
	return p.EventState
}

func (p *EventMeta) GetDo() (v string) {
	return p.Do
}

var EventMeta_Comment_DEFAULT string

func (p *EventMeta) GetComment() (v string) {
	if !p.IsSetComment() {
		return EventMeta_Comment_DEFAULT
	}
	return *p.Comment
}

var EventMeta_StartTime_DEFAULT string

func (p *EventMeta) GetStartTime() (v string) {
	if !p.IsSetStartTime() {
		return EventMeta_StartTime_DEFAULT
	}
	return *p.StartTime
}

var EventMeta_EndTime_DEFAULT string

func (p *EventMeta) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return EventMeta_EndTime_DEFAULT
	}
	return *p.EndTime
}
func (p *EventMeta) SetName(val string) {
	p.Name = val
}
func (p *EventMeta) SetScheduleMethod(val ScheduleMethod) {
	p.ScheduleMethod = val
}
func (p *EventMeta) SetTimeStamp(val *string) {
	p.TimeStamp = val
}
func (p *EventMeta) SetInterval(val *Interval) {
	p.Interval = val
}
func (p *EventMeta) SetPreserve(val bool) {
	p.Preserve = val
}
func (p *EventMeta) SetEventState(val EventState) {
	p.EventState = val
}
func (p *EventMeta) SetDo(val string) {
	p.Do = val
}
func (p *EventMeta) SetComment(val *string) {
	p.Comment = val
}
func (p *EventMeta) SetStartTime(val *string) {
	p.StartTime = val
}
func (p *EventMeta) SetEndTime(val *string) {
	p.EndTime = val
}

var fieldIDToName_EventMeta = map[int16]string{
	1:  "Name",
	2:  "ScheduleMethod",
	3:  "TimeStamp",
	4:  "Interval",
	5:  "Preserve",
	6:  "EventState",
	7:  "Do",
	8:  "Comment",
	9:  "StartTime",
	10: "EndTime",
}

func (p *EventMeta) IsSetTimeStamp() bool {
	return p.TimeStamp != nil
}

func (p *EventMeta) IsSetInterval() bool {
	return p.Interval != nil
}

func (p *EventMeta) IsSetComment() bool {
	return p.Comment != nil
}

func (p *EventMeta) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *EventMeta) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *EventMeta) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EventMeta")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetScheduleMethod bool = false
	var issetPreserve bool = false
	var issetEventState bool = false
	var issetDo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetScheduleMethod = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPreserve = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetDo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetScheduleMethod {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPreserve {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEventState {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetDo {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EventMeta[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_EventMeta[fieldId]))
}

func (p *EventMeta) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *EventMeta) ReadField2(iprot thrift.TProtocol) error {

	var _field ScheduleMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ScheduleMethod(v)
	}
	p.ScheduleMethod = _field
	return nil
}
func (p *EventMeta) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TimeStamp = _field
	return nil
}
func (p *EventMeta) ReadField4(iprot thrift.TProtocol) error {
	_field := NewInterval()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Interval = _field
	return nil
}
func (p *EventMeta) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Preserve = _field
	return nil
}
func (p *EventMeta) ReadField6(iprot thrift.TProtocol) error {

	var _field EventState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = EventState(v)
	}
	p.EventState = _field
	return nil
}
func (p *EventMeta) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Do = _field
	return nil
}
func (p *EventMeta) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Comment = _field
	return nil
}
func (p *EventMeta) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *EventMeta) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}

func (p *EventMeta) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EventMeta")

	var fieldId int16
	if err = oprot.WriteStructBegin("EventMeta"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EventMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *EventMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScheduleMethod", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ScheduleMethod)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *EventMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeStamp() {
		if err = oprot.WriteFieldBegin("TimeStamp", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TimeStamp); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *EventMeta) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInterval() {
		if err = oprot.WriteFieldBegin("Interval", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Interval.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *EventMeta) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Preserve", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Preserve); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *EventMeta) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventState", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EventState)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *EventMeta) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Do", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Do); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *EventMeta) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetComment() {
		if err = oprot.WriteFieldBegin("Comment", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Comment); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *EventMeta) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *EventMeta) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *EventMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventMeta(%+v)", *p)

}

func (p *EventMeta) DeepEqual(ano *EventMeta) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.ScheduleMethod) {
		return false
	}
	if !p.Field3DeepEqual(ano.TimeStamp) {
		return false
	}
	if !p.Field4DeepEqual(ano.Interval) {
		return false
	}
	if !p.Field5DeepEqual(ano.Preserve) {
		return false
	}
	if !p.Field6DeepEqual(ano.EventState) {
		return false
	}
	if !p.Field7DeepEqual(ano.Do) {
		return false
	}
	if !p.Field8DeepEqual(ano.Comment) {
		return false
	}
	if !p.Field9DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.EndTime) {
		return false
	}
	return true
}

func (p *EventMeta) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *EventMeta) Field2DeepEqual(src ScheduleMethod) bool {

	if p.ScheduleMethod != src {
		return false
	}
	return true
}
func (p *EventMeta) Field3DeepEqual(src *string) bool {

	if p.TimeStamp == src {
		return true
	} else if p.TimeStamp == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TimeStamp, *src) != 0 {
		return false
	}
	return true
}
func (p *EventMeta) Field4DeepEqual(src *Interval) bool {

	if !p.Interval.DeepEqual(src) {
		return false
	}
	return true
}
func (p *EventMeta) Field5DeepEqual(src bool) bool {

	if p.Preserve != src {
		return false
	}
	return true
}
func (p *EventMeta) Field6DeepEqual(src EventState) bool {

	if p.EventState != src {
		return false
	}
	return true
}
func (p *EventMeta) Field7DeepEqual(src string) bool {

	if strings.Compare(p.Do, src) != 0 {
		return false
	}
	return true
}
func (p *EventMeta) Field8DeepEqual(src *string) bool {

	if p.Comment == src {
		return true
	} else if p.Comment == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Comment, *src) != 0 {
		return false
	}
	return true
}
func (p *EventMeta) Field9DeepEqual(src *string) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *EventMeta) Field10DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}

type Interval struct {
	Unit   Unit  `thrift:"Unit,1,required" frugal:"1,required,Unit" json:"Unit"`
	Number int32 `thrift:"Number,2,required" frugal:"2,required,i32" json:"Number"`
}

func NewInterval() *Interval {
	return &Interval{}
}

func (p *Interval) InitDefault() {
}

func (p *Interval) GetUnit() (v Unit) {
	return p.Unit
}

func (p *Interval) GetNumber() (v int32) {
	return p.Number
}
func (p *Interval) SetUnit(val Unit) {
	p.Unit = val
}
func (p *Interval) SetNumber(val int32) {
	p.Number = val
}

var fieldIDToName_Interval = map[int16]string{
	1: "Unit",
	2: "Number",
}

func (p *Interval) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Interval")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUnit bool = false
	var issetNumber bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUnit {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNumber {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Interval[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Interval[fieldId]))
}

func (p *Interval) ReadField1(iprot thrift.TProtocol) error {

	var _field Unit
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = Unit(v)
	}
	p.Unit = _field
	return nil
}
func (p *Interval) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Number = _field
	return nil
}

func (p *Interval) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Interval")

	var fieldId int16
	if err = oprot.WriteStructBegin("Interval"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Interval) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Unit)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Interval) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Number", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Number); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Interval) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Interval(%+v)", *p)

}

func (p *Interval) DeepEqual(ano *Interval) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Unit) {
		return false
	}
	if !p.Field2DeepEqual(ano.Number) {
		return false
	}
	return true
}

func (p *Interval) Field1DeepEqual(src Unit) bool {

	if p.Unit != src {
		return false
	}
	return true
}
func (p *Interval) Field2DeepEqual(src int32) bool {

	if p.Number != src {
		return false
	}
	return true
}

type DescribeEventReq struct {
	SessionId string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB        string `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	EventName string `thrift:"EventName,3,required" frugal:"3,required,string" json:"EventName"`
}

func NewDescribeEventReq() *DescribeEventReq {
	return &DescribeEventReq{}
}

func (p *DescribeEventReq) InitDefault() {
}

func (p *DescribeEventReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DescribeEventReq) GetDB() (v string) {
	return p.DB
}

func (p *DescribeEventReq) GetEventName() (v string) {
	return p.EventName
}
func (p *DescribeEventReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DescribeEventReq) SetDB(val string) {
	p.DB = val
}
func (p *DescribeEventReq) SetEventName(val string) {
	p.EventName = val
}

var fieldIDToName_DescribeEventReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "EventName",
}

func (p *DescribeEventReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false
	var issetEventName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEventName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeEventReq[fieldId]))
}

func (p *DescribeEventReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DescribeEventReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DescribeEventReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventName = _field
	return nil
}

func (p *DescribeEventReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeEventReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeEventReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventReq(%+v)", *p)

}

func (p *DescribeEventReq) DeepEqual(ano *DescribeEventReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.EventName) {
		return false
	}
	return true
}

func (p *DescribeEventReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EventName, src) != 0 {
		return false
	}
	return true
}

type DescribeEventResp struct {
	EventInfo *EventMeta `thrift:"EventInfo,1,required" frugal:"1,required,EventMeta" json:"EventInfo"`
}

func NewDescribeEventResp() *DescribeEventResp {
	return &DescribeEventResp{}
}

func (p *DescribeEventResp) InitDefault() {
}

var DescribeEventResp_EventInfo_DEFAULT *EventMeta

func (p *DescribeEventResp) GetEventInfo() (v *EventMeta) {
	if !p.IsSetEventInfo() {
		return DescribeEventResp_EventInfo_DEFAULT
	}
	return p.EventInfo
}
func (p *DescribeEventResp) SetEventInfo(val *EventMeta) {
	p.EventInfo = val
}

var fieldIDToName_DescribeEventResp = map[int16]string{
	1: "EventInfo",
}

func (p *DescribeEventResp) IsSetEventInfo() bool {
	return p.EventInfo != nil
}

func (p *DescribeEventResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEventInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEventInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeEventResp[fieldId]))
}

func (p *DescribeEventResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewEventMeta()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.EventInfo = _field
	return nil
}

func (p *DescribeEventResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.EventInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventResp(%+v)", *p)

}

func (p *DescribeEventResp) DeepEqual(ano *DescribeEventResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventInfo) {
		return false
	}
	return true
}

func (p *DescribeEventResp) Field1DeepEqual(src *EventMeta) bool {

	if !p.EventInfo.DeepEqual(src) {
		return false
	}
	return true
}

type EventInfo struct {
	Name                string `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	SQLMode             string `thrift:"SQLMode,2,required" frugal:"2,required,string" json:"SQLMode"`
	CreateEvent         string `thrift:"CreateEvent,3,required" frugal:"3,required,string" json:"CreateEvent"`
	CharacterSetClient  string `thrift:"CharacterSetClient,4,required" frugal:"4,required,string" json:"CharacterSetClient"`
	CollationConnection string `thrift:"CollationConnection,5,required" frugal:"5,required,string" json:"CollationConnection"`
	DatabaseCollation   string `thrift:"DatabaseCollation,6,required" frugal:"6,required,string" json:"DatabaseCollation"`
}

func NewEventInfo() *EventInfo {
	return &EventInfo{}
}

func (p *EventInfo) InitDefault() {
}

func (p *EventInfo) GetName() (v string) {
	return p.Name
}

func (p *EventInfo) GetSQLMode() (v string) {
	return p.SQLMode
}

func (p *EventInfo) GetCreateEvent() (v string) {
	return p.CreateEvent
}

func (p *EventInfo) GetCharacterSetClient() (v string) {
	return p.CharacterSetClient
}

func (p *EventInfo) GetCollationConnection() (v string) {
	return p.CollationConnection
}

func (p *EventInfo) GetDatabaseCollation() (v string) {
	return p.DatabaseCollation
}
func (p *EventInfo) SetName(val string) {
	p.Name = val
}
func (p *EventInfo) SetSQLMode(val string) {
	p.SQLMode = val
}
func (p *EventInfo) SetCreateEvent(val string) {
	p.CreateEvent = val
}
func (p *EventInfo) SetCharacterSetClient(val string) {
	p.CharacterSetClient = val
}
func (p *EventInfo) SetCollationConnection(val string) {
	p.CollationConnection = val
}
func (p *EventInfo) SetDatabaseCollation(val string) {
	p.DatabaseCollation = val
}

var fieldIDToName_EventInfo = map[int16]string{
	1: "Name",
	2: "SQLMode",
	3: "CreateEvent",
	4: "CharacterSetClient",
	5: "CollationConnection",
	6: "DatabaseCollation",
}

func (p *EventInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EventInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetSQLMode bool = false
	var issetCreateEvent bool = false
	var issetCharacterSetClient bool = false
	var issetCollationConnection bool = false
	var issetDatabaseCollation bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateEvent = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCharacterSetClient = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCollationConnection = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabaseCollation = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSQLMode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCreateEvent {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCharacterSetClient {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCollationConnection {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDatabaseCollation {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EventInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_EventInfo[fieldId]))
}

func (p *EventInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *EventInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLMode = _field
	return nil
}
func (p *EventInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateEvent = _field
	return nil
}
func (p *EventInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CharacterSetClient = _field
	return nil
}
func (p *EventInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CollationConnection = _field
	return nil
}
func (p *EventInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatabaseCollation = _field
	return nil
}

func (p *EventInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EventInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("EventInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EventInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *EventInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLMode", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQLMode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *EventInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateEvent", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateEvent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *EventInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CharacterSetClient", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CharacterSetClient); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *EventInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CollationConnection", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CollationConnection); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *EventInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabaseCollation", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatabaseCollation); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *EventInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventInfo(%+v)", *p)

}

func (p *EventInfo) DeepEqual(ano *EventInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.SQLMode) {
		return false
	}
	if !p.Field3DeepEqual(ano.CreateEvent) {
		return false
	}
	if !p.Field4DeepEqual(ano.CharacterSetClient) {
		return false
	}
	if !p.Field5DeepEqual(ano.CollationConnection) {
		return false
	}
	if !p.Field6DeepEqual(ano.DatabaseCollation) {
		return false
	}
	return true
}

func (p *EventInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *EventInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SQLMode, src) != 0 {
		return false
	}
	return true
}
func (p *EventInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CreateEvent, src) != 0 {
		return false
	}
	return true
}
func (p *EventInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CharacterSetClient, src) != 0 {
		return false
	}
	return true
}
func (p *EventInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CollationConnection, src) != 0 {
		return false
	}
	return true
}
func (p *EventInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.DatabaseCollation, src) != 0 {
		return false
	}
	return true
}

type DescribeEventsReq struct {
	SessionId  string  `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB         string  `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	PageNumber *int32  `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize   *int32  `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	Query      *string `thrift:"Query,5,optional" frugal:"5,optional,string" json:"Query,omitempty"`
}

func NewDescribeEventsReq() *DescribeEventsReq {
	return &DescribeEventsReq{}
}

func (p *DescribeEventsReq) InitDefault() {
}

func (p *DescribeEventsReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DescribeEventsReq) GetDB() (v string) {
	return p.DB
}

var DescribeEventsReq_PageNumber_DEFAULT int32

func (p *DescribeEventsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeEventsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeEventsReq_PageSize_DEFAULT int32

func (p *DescribeEventsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeEventsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeEventsReq_Query_DEFAULT string

func (p *DescribeEventsReq) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return DescribeEventsReq_Query_DEFAULT
	}
	return *p.Query
}
func (p *DescribeEventsReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DescribeEventsReq) SetDB(val string) {
	p.DB = val
}
func (p *DescribeEventsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeEventsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeEventsReq) SetQuery(val *string) {
	p.Query = val
}

var fieldIDToName_DescribeEventsReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "PageNumber",
	4: "PageSize",
	5: "Query",
}

func (p *DescribeEventsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeEventsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeEventsReq) IsSetQuery() bool {
	return p.Query != nil
}

func (p *DescribeEventsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeEventsReq[fieldId]))
}

func (p *DescribeEventsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DescribeEventsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DescribeEventsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeEventsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeEventsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Query = _field
	return nil
}

func (p *DescribeEventsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeEventsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetQuery() {
		if err = oprot.WriteFieldBegin("Query", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Query); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeEventsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventsReq(%+v)", *p)

}

func (p *DescribeEventsReq) DeepEqual(ano *DescribeEventsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.Query) {
		return false
	}
	return true
}

func (p *DescribeEventsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeEventsReq) Field5DeepEqual(src *string) bool {

	if p.Query == src {
		return true
	} else if p.Query == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Query, *src) != 0 {
		return false
	}
	return true
}

type DescribeEventsResp struct {
	Items []string `thrift:"Items,1,required" frugal:"1,required,list<string>" json:"Items"`
	Total int32    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeEventsResp() *DescribeEventsResp {
	return &DescribeEventsResp{}
}

func (p *DescribeEventsResp) InitDefault() {
}

func (p *DescribeEventsResp) GetItems() (v []string) {
	return p.Items
}

func (p *DescribeEventsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeEventsResp) SetItems(val []string) {
	p.Items = val
}
func (p *DescribeEventsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeEventsResp = map[int16]string{
	1: "Items",
	2: "Total",
}

func (p *DescribeEventsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItems bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeEventsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeEventsResp[fieldId]))
}

func (p *DescribeEventsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Items = _field
	return nil
}
func (p *DescribeEventsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeEventsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeEventsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeEventsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeEventsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Items", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Items)); err != nil {
		return err
	}
	for _, v := range p.Items {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeEventsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeEventsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeEventsResp(%+v)", *p)

}

func (p *DescribeEventsResp) DeepEqual(ano *DescribeEventsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Items) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeEventsResp) Field1DeepEqual(src []string) bool {

	if len(p.Items) != len(src) {
		return false
	}
	for i, v := range p.Items {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DescribeEventsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DropEventReq struct {
	SessionId string `thrift:"SessionId,1,required" frugal:"1,required,string" json:"SessionId"`
	DB        string `thrift:"DB,2,required" frugal:"2,required,string" json:"DB"`
	EventName string `thrift:"EventName,3,required" frugal:"3,required,string" json:"EventName"`
}

func NewDropEventReq() *DropEventReq {
	return &DropEventReq{}
}

func (p *DropEventReq) InitDefault() {
}

func (p *DropEventReq) GetSessionId() (v string) {
	return p.SessionId
}

func (p *DropEventReq) GetDB() (v string) {
	return p.DB
}

func (p *DropEventReq) GetEventName() (v string) {
	return p.EventName
}
func (p *DropEventReq) SetSessionId(val string) {
	p.SessionId = val
}
func (p *DropEventReq) SetDB(val string) {
	p.DB = val
}
func (p *DropEventReq) SetEventName(val string) {
	p.EventName = val
}

var fieldIDToName_DropEventReq = map[int16]string{
	1: "SessionId",
	2: "DB",
	3: "EventName",
}

func (p *DropEventReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DropEventReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSessionId bool = false
	var issetDB bool = false
	var issetEventName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDB = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEventName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSessionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDB {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEventName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DropEventReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DropEventReq[fieldId]))
}

func (p *DropEventReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionId = _field
	return nil
}
func (p *DropEventReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DB = _field
	return nil
}
func (p *DropEventReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EventName = _field
	return nil
}

func (p *DropEventReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DropEventReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DropEventReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DropEventReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DropEventReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DB", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DropEventReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EventName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DropEventReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DropEventReq(%+v)", *p)

}

func (p *DropEventReq) DeepEqual(ano *DropEventReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SessionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.DB) {
		return false
	}
	if !p.Field3DeepEqual(ano.EventName) {
		return false
	}
	return true
}

func (p *DropEventReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SessionId, src) != 0 {
		return false
	}
	return true
}
func (p *DropEventReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DB, src) != 0 {
		return false
	}
	return true
}
func (p *DropEventReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EventName, src) != 0 {
		return false
	}
	return true
}

type DropEventResp struct {
	Statement string `thrift:"Statement,1,required" frugal:"1,required,string" json:"Statement"`
}

func NewDropEventResp() *DropEventResp {
	return &DropEventResp{}
}

func (p *DropEventResp) InitDefault() {
}

func (p *DropEventResp) GetStatement() (v string) {
	return p.Statement
}
func (p *DropEventResp) SetStatement(val string) {
	p.Statement = val
}

var fieldIDToName_DropEventResp = map[int16]string{
	1: "Statement",
}

func (p *DropEventResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DropEventResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatement bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatement = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStatement {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DropEventResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DropEventResp[fieldId]))
}

func (p *DropEventResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Statement = _field
	return nil
}

func (p *DropEventResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DropEventResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DropEventResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DropEventResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Statement", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Statement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DropEventResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DropEventResp(%+v)", *p)

}

func (p *DropEventResp) DeepEqual(ano *DropEventResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Statement) {
		return false
	}
	return true
}

func (p *DropEventResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Statement, src) != 0 {
		return false
	}
	return true
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *ComputeSettings) IsValid() error {
	if p.AutoScalingLimitMinCU != nil {
		if *p.AutoScalingLimitMinCU < float64(0) {
			return fmt.Errorf("field AutoScalingLimitMinCU ge rule failed, current value: %v", *p.AutoScalingLimitMinCU)
		}
		if *p.AutoScalingLimitMinCU > float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMinCU le rule failed, current value: %v", *p.AutoScalingLimitMinCU)
		}
	}
	if p.AutoScalingLimitMaxCU != nil {
		if *p.AutoScalingLimitMaxCU < float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMaxCU ge rule failed, current value: %v", *p.AutoScalingLimitMaxCU)
		}
		if *p.AutoScalingLimitMaxCU > float64(256) {
			return fmt.Errorf("field AutoScalingLimitMaxCU le rule failed, current value: %v", *p.AutoScalingLimitMaxCU)
		}
	}
	if p.SuspendTimeoutSeconds != nil {
		if *p.SuspendTimeoutSeconds < int32(-1) {
			return fmt.Errorf("field SuspendTimeoutSeconds ge rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
		if *p.SuspendTimeoutSeconds > int32(604800) {
			return fmt.Errorf("field SuspendTimeoutSeconds le rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
	}
	return nil
}
func (p *Compute) IsValid() error {
	return nil
}
func (p *CreateComputeReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if p.BranchId != nil {
		if len(*p.BranchId) > int(64) {
			return fmt.Errorf("field BranchId max_len rule failed, current value: %d", len(*p.BranchId))
		}
		_src1 := "^[0-9A-Za-z-]+$"
		if ok, _ := regexp.MatchString(_src1, *p.BranchId); !ok {
			return fmt.Errorf("field BranchId pattern rule failed, current value: %v", *p.BranchId)
		}
	}
	if p.ComputeName != nil {
		if len(*p.ComputeName) > int(64) {
			return fmt.Errorf("field ComputeName max_len rule failed, current value: %d", len(*p.ComputeName))
		}
		_src2 := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
		if ok, _ := regexp.MatchString(_src2, *p.ComputeName); !ok {
			return fmt.Errorf("field ComputeName pattern rule failed, current value: %v", *p.ComputeName)
		}
	}
	if p.ComputeSettings == nil {
		return fmt.Errorf("field ComputeSettings not_nil rule failed")
	}
	if err := p.ComputeSettings.IsValid(); err != nil {
		return fmt.Errorf("field ComputeSettings not valid, %w", err)
	}
	return nil
}
func (p *CreateComputeResp) IsValid() error {
	if p.Compute != nil {
		if err := p.Compute.IsValid(); err != nil {
			return fmt.Errorf("field Compute not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeComputesReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if p.BranchId != nil {
		if len(*p.BranchId) > int(64) {
			return fmt.Errorf("field BranchId max_len rule failed, current value: %d", len(*p.BranchId))
		}
		_src1 := "^[0-9A-Za-z-]+$"
		if ok, _ := regexp.MatchString(_src1, *p.BranchId); !ok {
			return fmt.Errorf("field BranchId pattern rule failed, current value: %v", *p.BranchId)
		}
	}
	return nil
}
func (p *DescribeComputesResp) IsValid() error {
	return nil
}
func (p *DescribeComputeDetailReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if len(p.ComputeId) < int(2) {
		return fmt.Errorf("field ComputeId min_len rule failed, current value: %d", len(p.ComputeId))
	}
	if len(p.ComputeId) > int(64) {
		return fmt.Errorf("field ComputeId max_len rule failed, current value: %d", len(p.ComputeId))
	}
	_src1 := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src1, p.ComputeId); !ok {
		return fmt.Errorf("field ComputeId pattern rule failed, current value: %v", p.ComputeId)
	}
	return nil
}
func (p *DescribeComputeDetailResp) IsValid() error {
	if p.Compute != nil {
		if err := p.Compute.IsValid(); err != nil {
			return fmt.Errorf("field Compute not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteComputeReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if len(p.ComputeId) < int(2) {
		return fmt.Errorf("field ComputeId min_len rule failed, current value: %d", len(p.ComputeId))
	}
	if len(p.ComputeId) > int(64) {
		return fmt.Errorf("field ComputeId max_len rule failed, current value: %d", len(p.ComputeId))
	}
	_src1 := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src1, p.ComputeId); !ok {
		return fmt.Errorf("field ComputeId pattern rule failed, current value: %v", p.ComputeId)
	}
	return nil
}
func (p *DeleteComputeResp) IsValid() error {
	return nil
}
func (p *ModifyComputeNameReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if len(p.ComputeId) < int(2) {
		return fmt.Errorf("field ComputeId min_len rule failed, current value: %d", len(p.ComputeId))
	}
	if len(p.ComputeId) > int(64) {
		return fmt.Errorf("field ComputeId max_len rule failed, current value: %d", len(p.ComputeId))
	}
	_src1 := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src1, p.ComputeId); !ok {
		return fmt.Errorf("field ComputeId pattern rule failed, current value: %v", p.ComputeId)
	}
	if len(p.ComputeName) < int(1) {
		return fmt.Errorf("field ComputeName min_len rule failed, current value: %d", len(p.ComputeName))
	}
	if len(p.ComputeName) > int(128) {
		return fmt.Errorf("field ComputeName max_len rule failed, current value: %d", len(p.ComputeName))
	}
	_src2 := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
	if ok, _ := regexp.MatchString(_src2, p.ComputeName); !ok {
		return fmt.Errorf("field ComputeName pattern rule failed, current value: %v", p.ComputeName)
	}
	return nil
}
func (p *ModifyComputeNameResp) IsValid() error {
	return nil
}
func (p *ModifyComputeSpecReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	_src := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src, p.InstanceId); !ok {
		return fmt.Errorf("field InstanceId pattern rule failed, current value: %v", p.InstanceId)
	}
	if len(p.ComputeId) < int(2) {
		return fmt.Errorf("field ComputeId min_len rule failed, current value: %d", len(p.ComputeId))
	}
	if len(p.ComputeId) > int(64) {
		return fmt.Errorf("field ComputeId max_len rule failed, current value: %d", len(p.ComputeId))
	}
	_src1 := "^[0-9A-Za-z-]+$"
	if ok, _ := regexp.MatchString(_src1, p.ComputeId); !ok {
		return fmt.Errorf("field ComputeId pattern rule failed, current value: %v", p.ComputeId)
	}
	if p.AutoScalingLimitMinCU != nil {
		if *p.AutoScalingLimitMinCU < float64(0) {
			return fmt.Errorf("field AutoScalingLimitMinCU ge rule failed, current value: %v", *p.AutoScalingLimitMinCU)
		}
		if *p.AutoScalingLimitMinCU > float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMinCU le rule failed, current value: %v", *p.AutoScalingLimitMinCU)
		}
	}
	if p.AutoScalingLimitMaxCU != nil {
		if *p.AutoScalingLimitMaxCU < float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMaxCU ge rule failed, current value: %v", *p.AutoScalingLimitMaxCU)
		}
		if *p.AutoScalingLimitMaxCU > float64(256) {
			return fmt.Errorf("field AutoScalingLimitMaxCU le rule failed, current value: %v", *p.AutoScalingLimitMaxCU)
		}
	}
	if p.SuspendTimeoutSeconds != nil {
		if *p.SuspendTimeoutSeconds < int32(-1) {
			return fmt.Errorf("field SuspendTimeoutSeconds ge rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
		if *p.SuspendTimeoutSeconds > int32(604800) {
			return fmt.Errorf("field SuspendTimeoutSeconds le rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
	}
	return nil
}
func (p *ModifyComputeSpecResp) IsValid() error {
	if p.Compute != nil {
		if err := p.Compute.IsValid(); err != nil {
			return fmt.Errorf("field Compute not valid, %w", err)
		}
	}
	return nil
}

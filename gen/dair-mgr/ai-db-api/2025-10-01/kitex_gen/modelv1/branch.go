// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type BranchStatus int64

const (
	BranchStatus_Unknown    BranchStatus = 0
	BranchStatus_Init       BranchStatus = 1
	BranchStatus_Activating BranchStatus = 2
	BranchStatus_Ready      BranchStatus = 3
	BranchStatus_Archived   BranchStatus = 4
	BranchStatus_Deleted    BranchStatus = 5
	BranchStatus_Resetting  BranchStatus = 6
)

func (p BranchStatus) String() string {
	switch p {
	case BranchStatus_Unknown:
		return "Unknown"
	case BranchStatus_Init:
		return "Init"
	case BranchStatus_Activating:
		return "Activating"
	case BranchStatus_Ready:
		return "Ready"
	case BranchStatus_Archived:
		return "Archived"
	case BranchStatus_Deleted:
		return "Deleted"
	case BranchStatus_Resetting:
		return "Resetting"
	}
	return "<UNSET>"
}

func BranchStatusFromString(s string) (BranchStatus, error) {
	switch s {
	case "Unknown":
		return BranchStatus_Unknown, nil
	case "Init":
		return BranchStatus_Init, nil
	case "Activating":
		return BranchStatus_Activating, nil
	case "Ready":
		return BranchStatus_Ready, nil
	case "Archived":
		return BranchStatus_Archived, nil
	case "Deleted":
		return BranchStatus_Deleted, nil
	case "Resetting":
		return BranchStatus_Resetting, nil
	}
	return BranchStatus(0), fmt.Errorf("not a valid BranchStatus string")
}

func BranchStatusPtr(v BranchStatus) *BranchStatus { return &v }

func (p BranchStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *BranchStatus) UnmarshalText(text []byte) error {
	q, err := BranchStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type BranchInitSource int64

const (
	BranchInitSource_ParentData BranchInitSource = 0
	BranchInitSource_SchemeOnly BranchInitSource = 1
)

func (p BranchInitSource) String() string {
	switch p {
	case BranchInitSource_ParentData:
		return "ParentData"
	case BranchInitSource_SchemeOnly:
		return "SchemeOnly"
	}
	return "<UNSET>"
}

func BranchInitSourceFromString(s string) (BranchInitSource, error) {
	switch s {
	case "ParentData":
		return BranchInitSource_ParentData, nil
	case "SchemeOnly":
		return BranchInitSource_SchemeOnly, nil
	}
	return BranchInitSource(0), fmt.Errorf("not a valid BranchInitSource string")
}

func BranchInitSourcePtr(v BranchInitSource) *BranchInitSource { return &v }

func (p BranchInitSource) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *BranchInitSource) UnmarshalText(text []byte) error {
	q, err := BranchInitSourceFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type BranchSettings struct {
	Name       *string           `thrift:"Name,1,optional" frugal:"1,optional,string" json:"Name,omitempty"`
	ParentId   *string           `thrift:"ParentId,2,optional" frugal:"2,optional,string" json:"ParentId,omitempty"`
	ParentLSN  *string           `thrift:"ParentLSN,3,optional" frugal:"3,optional,string" json:"ParentLSN,omitempty"`
	ParentTime *string           `thrift:"ParentTime,4,optional" frugal:"4,optional,string" json:"ParentTime,omitempty"`
	Protected  *bool             `thrift:"Protected,5,optional" frugal:"5,optional,bool" json:"Protected,omitempty"`
	Archived   *bool             `thrift:"Archived,6,optional" frugal:"6,optional,bool" json:"Archived,omitempty"`
	InitSource *BranchInitSource `thrift:"InitSource,7,optional" frugal:"7,optional,BranchInitSource" json:"InitSource,omitempty"`
}

func NewBranchSettings() *BranchSettings {
	return &BranchSettings{}
}

func (p *BranchSettings) InitDefault() {
}

var BranchSettings_Name_DEFAULT string

func (p *BranchSettings) GetName() (v string) {
	if !p.IsSetName() {
		return BranchSettings_Name_DEFAULT
	}
	return *p.Name
}

var BranchSettings_ParentId_DEFAULT string

func (p *BranchSettings) GetParentId() (v string) {
	if !p.IsSetParentId() {
		return BranchSettings_ParentId_DEFAULT
	}
	return *p.ParentId
}

var BranchSettings_ParentLSN_DEFAULT string

func (p *BranchSettings) GetParentLSN() (v string) {
	if !p.IsSetParentLSN() {
		return BranchSettings_ParentLSN_DEFAULT
	}
	return *p.ParentLSN
}

var BranchSettings_ParentTime_DEFAULT string

func (p *BranchSettings) GetParentTime() (v string) {
	if !p.IsSetParentTime() {
		return BranchSettings_ParentTime_DEFAULT
	}
	return *p.ParentTime
}

var BranchSettings_Protected_DEFAULT bool

func (p *BranchSettings) GetProtected() (v bool) {
	if !p.IsSetProtected() {
		return BranchSettings_Protected_DEFAULT
	}
	return *p.Protected
}

var BranchSettings_Archived_DEFAULT bool

func (p *BranchSettings) GetArchived() (v bool) {
	if !p.IsSetArchived() {
		return BranchSettings_Archived_DEFAULT
	}
	return *p.Archived
}

var BranchSettings_InitSource_DEFAULT BranchInitSource

func (p *BranchSettings) GetInitSource() (v BranchInitSource) {
	if !p.IsSetInitSource() {
		return BranchSettings_InitSource_DEFAULT
	}
	return *p.InitSource
}
func (p *BranchSettings) SetName(val *string) {
	p.Name = val
}
func (p *BranchSettings) SetParentId(val *string) {
	p.ParentId = val
}
func (p *BranchSettings) SetParentLSN(val *string) {
	p.ParentLSN = val
}
func (p *BranchSettings) SetParentTime(val *string) {
	p.ParentTime = val
}
func (p *BranchSettings) SetProtected(val *bool) {
	p.Protected = val
}
func (p *BranchSettings) SetArchived(val *bool) {
	p.Archived = val
}
func (p *BranchSettings) SetInitSource(val *BranchInitSource) {
	p.InitSource = val
}

var fieldIDToName_BranchSettings = map[int16]string{
	1: "Name",
	2: "ParentId",
	3: "ParentLSN",
	4: "ParentTime",
	5: "Protected",
	6: "Archived",
	7: "InitSource",
}

func (p *BranchSettings) IsSetName() bool {
	return p.Name != nil
}

func (p *BranchSettings) IsSetParentId() bool {
	return p.ParentId != nil
}

func (p *BranchSettings) IsSetParentLSN() bool {
	return p.ParentLSN != nil
}

func (p *BranchSettings) IsSetParentTime() bool {
	return p.ParentTime != nil
}

func (p *BranchSettings) IsSetProtected() bool {
	return p.Protected != nil
}

func (p *BranchSettings) IsSetArchived() bool {
	return p.Archived != nil
}

func (p *BranchSettings) IsSetInitSource() bool {
	return p.InitSource != nil
}

func (p *BranchSettings) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BranchSettings")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BranchSettings[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BranchSettings) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *BranchSettings) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParentId = _field
	return nil
}
func (p *BranchSettings) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParentLSN = _field
	return nil
}
func (p *BranchSettings) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ParentTime = _field
	return nil
}
func (p *BranchSettings) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Protected = _field
	return nil
}
func (p *BranchSettings) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Archived = _field
	return nil
}
func (p *BranchSettings) ReadField7(iprot thrift.TProtocol) error {

	var _field *BranchInitSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BranchInitSource(v)
		_field = &tmp
	}
	p.InitSource = _field
	return nil
}

func (p *BranchSettings) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BranchSettings")

	var fieldId int16
	if err = oprot.WriteStructBegin("BranchSettings"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BranchSettings) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *BranchSettings) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetParentId() {
		if err = oprot.WriteFieldBegin("ParentId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParentId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BranchSettings) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetParentLSN() {
		if err = oprot.WriteFieldBegin("ParentLSN", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParentLSN); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *BranchSettings) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetParentTime() {
		if err = oprot.WriteFieldBegin("ParentTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ParentTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *BranchSettings) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetProtected() {
		if err = oprot.WriteFieldBegin("Protected", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Protected); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BranchSettings) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetArchived() {
		if err = oprot.WriteFieldBegin("Archived", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Archived); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *BranchSettings) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInitSource() {
		if err = oprot.WriteFieldBegin("InitSource", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InitSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *BranchSettings) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BranchSettings(%+v)", *p)

}

func (p *BranchSettings) DeepEqual(ano *BranchSettings) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.ParentId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ParentLSN) {
		return false
	}
	if !p.Field4DeepEqual(ano.ParentTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.Protected) {
		return false
	}
	if !p.Field6DeepEqual(ano.Archived) {
		return false
	}
	if !p.Field7DeepEqual(ano.InitSource) {
		return false
	}
	return true
}

func (p *BranchSettings) Field1DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *BranchSettings) Field2DeepEqual(src *string) bool {

	if p.ParentId == src {
		return true
	} else if p.ParentId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParentId, *src) != 0 {
		return false
	}
	return true
}
func (p *BranchSettings) Field3DeepEqual(src *string) bool {

	if p.ParentLSN == src {
		return true
	} else if p.ParentLSN == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParentLSN, *src) != 0 {
		return false
	}
	return true
}
func (p *BranchSettings) Field4DeepEqual(src *string) bool {

	if p.ParentTime == src {
		return true
	} else if p.ParentTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ParentTime, *src) != 0 {
		return false
	}
	return true
}
func (p *BranchSettings) Field5DeepEqual(src *bool) bool {

	if p.Protected == src {
		return true
	} else if p.Protected == nil || src == nil {
		return false
	}
	if *p.Protected != *src {
		return false
	}
	return true
}
func (p *BranchSettings) Field6DeepEqual(src *bool) bool {

	if p.Archived == src {
		return true
	} else if p.Archived == nil || src == nil {
		return false
	}
	if *p.Archived != *src {
		return false
	}
	return true
}
func (p *BranchSettings) Field7DeepEqual(src *BranchInitSource) bool {

	if p.InitSource == src {
		return true
	} else if p.InitSource == nil || src == nil {
		return false
	}
	if *p.InitSource != *src {
		return false
	}
	return true
}

type Branch struct {
	InstanceId        string         `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId          string         `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
	BranchName        string         `thrift:"BranchName,3,required" frugal:"3,required,string" json:"BranchName"`
	StartParentLSN    *string        `thrift:"StartParentLSN,4,optional" frugal:"4,optional,string" json:"StartParentLSN,omitempty"`
	StartParentTime   *string        `thrift:"StartParentTime,5,optional" frugal:"5,optional,string" json:"StartParentTime,omitempty"`
	BranchStatus      BranchStatus   `thrift:"BranchStatus,6,required" frugal:"6,required,BranchStatus" json:"BranchStatus"`
	CreateTime        string         `thrift:"CreateTime,7,required" frugal:"7,required,string" json:"CreateTime"`
	UpdateTime        string         `thrift:"UpdateTime,8,required" frugal:"8,required,string" json:"UpdateTime"`
	StatusChangedTime string         `thrift:"StatusChangedTime,9,required" frugal:"9,required,string" json:"StatusChangedTime"`
	LastResetTime     string         `thrift:"LastResetTime,10,required" frugal:"10,required,string" json:"LastResetTime"`
	CreationSource    CreationSource `thrift:"CreationSource,11,required" frugal:"11,required,CreationSource" json:"CreationSource"`
	Default           bool           `thrift:"Default,12,required" frugal:"12,required,bool" json:"Default"`
	Protected         bool           `thrift:"Protected,13,required" frugal:"13,required,bool" json:"Protected"`
	Archived          bool           `thrift:"Archived,14,required" frugal:"14,required,bool" json:"Archived"`
	BranchUsage       *BranchUsage   `thrift:"BranchUsage,15,required" frugal:"15,required,BranchUsage" json:"BranchUsage"`
	ParentBranch      *Branch        `thrift:"ParentBranch,16,optional" frugal:"16,optional,Branch" json:"ParentBranch,omitempty"`
}

func NewBranch() *Branch {
	return &Branch{}
}

func (p *Branch) InitDefault() {
}

func (p *Branch) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *Branch) GetBranchId() (v string) {
	return p.BranchId
}

func (p *Branch) GetBranchName() (v string) {
	return p.BranchName
}

var Branch_StartParentLSN_DEFAULT string

func (p *Branch) GetStartParentLSN() (v string) {
	if !p.IsSetStartParentLSN() {
		return Branch_StartParentLSN_DEFAULT
	}
	return *p.StartParentLSN
}

var Branch_StartParentTime_DEFAULT string

func (p *Branch) GetStartParentTime() (v string) {
	if !p.IsSetStartParentTime() {
		return Branch_StartParentTime_DEFAULT
	}
	return *p.StartParentTime
}

func (p *Branch) GetBranchStatus() (v BranchStatus) {
	return p.BranchStatus
}

func (p *Branch) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *Branch) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *Branch) GetStatusChangedTime() (v string) {
	return p.StatusChangedTime
}

func (p *Branch) GetLastResetTime() (v string) {
	return p.LastResetTime
}

func (p *Branch) GetCreationSource() (v CreationSource) {
	return p.CreationSource
}

func (p *Branch) GetDefault() (v bool) {
	return p.Default
}

func (p *Branch) GetProtected() (v bool) {
	return p.Protected
}

func (p *Branch) GetArchived() (v bool) {
	return p.Archived
}

var Branch_BranchUsage_DEFAULT *BranchUsage

func (p *Branch) GetBranchUsage() (v *BranchUsage) {
	if !p.IsSetBranchUsage() {
		return Branch_BranchUsage_DEFAULT
	}
	return p.BranchUsage
}

var Branch_ParentBranch_DEFAULT *Branch

func (p *Branch) GetParentBranch() (v *Branch) {
	if !p.IsSetParentBranch() {
		return Branch_ParentBranch_DEFAULT
	}
	return p.ParentBranch
}
func (p *Branch) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *Branch) SetBranchId(val string) {
	p.BranchId = val
}
func (p *Branch) SetBranchName(val string) {
	p.BranchName = val
}
func (p *Branch) SetStartParentLSN(val *string) {
	p.StartParentLSN = val
}
func (p *Branch) SetStartParentTime(val *string) {
	p.StartParentTime = val
}
func (p *Branch) SetBranchStatus(val BranchStatus) {
	p.BranchStatus = val
}
func (p *Branch) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *Branch) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *Branch) SetStatusChangedTime(val string) {
	p.StatusChangedTime = val
}
func (p *Branch) SetLastResetTime(val string) {
	p.LastResetTime = val
}
func (p *Branch) SetCreationSource(val CreationSource) {
	p.CreationSource = val
}
func (p *Branch) SetDefault(val bool) {
	p.Default = val
}
func (p *Branch) SetProtected(val bool) {
	p.Protected = val
}
func (p *Branch) SetArchived(val bool) {
	p.Archived = val
}
func (p *Branch) SetBranchUsage(val *BranchUsage) {
	p.BranchUsage = val
}
func (p *Branch) SetParentBranch(val *Branch) {
	p.ParentBranch = val
}

var fieldIDToName_Branch = map[int16]string{
	1:  "InstanceId",
	2:  "BranchId",
	3:  "BranchName",
	4:  "StartParentLSN",
	5:  "StartParentTime",
	6:  "BranchStatus",
	7:  "CreateTime",
	8:  "UpdateTime",
	9:  "StatusChangedTime",
	10: "LastResetTime",
	11: "CreationSource",
	12: "Default",
	13: "Protected",
	14: "Archived",
	15: "BranchUsage",
	16: "ParentBranch",
}

func (p *Branch) IsSetStartParentLSN() bool {
	return p.StartParentLSN != nil
}

func (p *Branch) IsSetStartParentTime() bool {
	return p.StartParentTime != nil
}

func (p *Branch) IsSetBranchUsage() bool {
	return p.BranchUsage != nil
}

func (p *Branch) IsSetParentBranch() bool {
	return p.ParentBranch != nil
}

func (p *Branch) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Branch")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetBranchName bool = false
	var issetBranchStatus bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetStatusChangedTime bool = false
	var issetLastResetTime bool = false
	var issetCreationSource bool = false
	var issetDefault bool = false
	var issetProtected bool = false
	var issetArchived bool = false
	var issetBranchUsage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatusChangedTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastResetTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreationSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetDefault = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetProtected = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetArchived = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBranchName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchStatus {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetStatusChangedTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetLastResetTime {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCreationSource {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetDefault {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetProtected {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetArchived {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetBranchUsage {
		fieldId = 15
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Branch[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Branch[fieldId]))
}

func (p *Branch) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *Branch) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}
func (p *Branch) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchName = _field
	return nil
}
func (p *Branch) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartParentLSN = _field
	return nil
}
func (p *Branch) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartParentTime = _field
	return nil
}
func (p *Branch) ReadField6(iprot thrift.TProtocol) error {

	var _field BranchStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BranchStatus(v)
	}
	p.BranchStatus = _field
	return nil
}
func (p *Branch) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *Branch) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *Branch) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatusChangedTime = _field
	return nil
}
func (p *Branch) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastResetTime = _field
	return nil
}
func (p *Branch) ReadField11(iprot thrift.TProtocol) error {

	var _field CreationSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CreationSource(v)
	}
	p.CreationSource = _field
	return nil
}
func (p *Branch) ReadField12(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Default = _field
	return nil
}
func (p *Branch) ReadField13(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Protected = _field
	return nil
}
func (p *Branch) ReadField14(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Archived = _field
	return nil
}
func (p *Branch) ReadField15(iprot thrift.TProtocol) error {
	_field := NewBranchUsage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BranchUsage = _field
	return nil
}
func (p *Branch) ReadField16(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ParentBranch = _field
	return nil
}

func (p *Branch) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Branch")

	var fieldId int16
	if err = oprot.WriteStructBegin("Branch"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Branch) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Branch) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Branch) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Branch) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartParentLSN() {
		if err = oprot.WriteFieldBegin("StartParentLSN", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartParentLSN); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Branch) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartParentTime() {
		if err = oprot.WriteFieldBegin("StartParentTime", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartParentTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Branch) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchStatus", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BranchStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Branch) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Branch) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Branch) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatusChangedTime", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatusChangedTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Branch) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastResetTime", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastResetTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Branch) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreationSource", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CreationSource)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *Branch) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Default", thrift.BOOL, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Default); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Branch) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Protected", thrift.BOOL, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Protected); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Branch) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Archived", thrift.BOOL, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Archived); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *Branch) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchUsage", thrift.STRUCT, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BranchUsage.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *Branch) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetParentBranch() {
		if err = oprot.WriteFieldBegin("ParentBranch", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ParentBranch.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *Branch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Branch(%+v)", *p)

}

func (p *Branch) DeepEqual(ano *Branch) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.BranchName) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartParentLSN) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartParentTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.BranchStatus) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.StatusChangedTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.LastResetTime) {
		return false
	}
	if !p.Field11DeepEqual(ano.CreationSource) {
		return false
	}
	if !p.Field12DeepEqual(ano.Default) {
		return false
	}
	if !p.Field13DeepEqual(ano.Protected) {
		return false
	}
	if !p.Field14DeepEqual(ano.Archived) {
		return false
	}
	if !p.Field15DeepEqual(ano.BranchUsage) {
		return false
	}
	if !p.Field16DeepEqual(ano.ParentBranch) {
		return false
	}
	return true
}

func (p *Branch) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field3DeepEqual(src string) bool {

	if strings.Compare(p.BranchName, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field4DeepEqual(src *string) bool {

	if p.StartParentLSN == src {
		return true
	} else if p.StartParentLSN == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartParentLSN, *src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field5DeepEqual(src *string) bool {

	if p.StartParentTime == src {
		return true
	} else if p.StartParentTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartParentTime, *src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field6DeepEqual(src BranchStatus) bool {

	if p.BranchStatus != src {
		return false
	}
	return true
}
func (p *Branch) Field7DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field8DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field9DeepEqual(src string) bool {

	if strings.Compare(p.StatusChangedTime, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field10DeepEqual(src string) bool {

	if strings.Compare(p.LastResetTime, src) != 0 {
		return false
	}
	return true
}
func (p *Branch) Field11DeepEqual(src CreationSource) bool {

	if p.CreationSource != src {
		return false
	}
	return true
}
func (p *Branch) Field12DeepEqual(src bool) bool {

	if p.Default != src {
		return false
	}
	return true
}
func (p *Branch) Field13DeepEqual(src bool) bool {

	if p.Protected != src {
		return false
	}
	return true
}
func (p *Branch) Field14DeepEqual(src bool) bool {

	if p.Archived != src {
		return false
	}
	return true
}
func (p *Branch) Field15DeepEqual(src *BranchUsage) bool {

	if !p.BranchUsage.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Branch) Field16DeepEqual(src *Branch) bool {

	if !p.ParentBranch.DeepEqual(src) {
		return false
	}
	return true
}

type BranchUsage struct {
	InstanceId         string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId           string `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
	DataSizeUsedBytes  int64  `thrift:"DataSizeUsedBytes,3,required" frugal:"3,required,i64" json:"DataSizeUsedBytes"`
	DataSizeTotalBytes int64  `thrift:"DataSizeTotalBytes,4,required" frugal:"4,required,i64" json:"DataSizeTotalBytes"`
	ComputeTimeSeconds int64  `thrift:"ComputeTimeSeconds,5,required" frugal:"5,required,i64" json:"ComputeTimeSeconds"`
	StatTime           string `thrift:"StatTime,6,required" frugal:"6,required,string" json:"StatTime"`
	LastRunningTime    string `thrift:"LastRunningTime,7,required" frugal:"7,required,string" json:"LastRunningTime"`
}

func NewBranchUsage() *BranchUsage {
	return &BranchUsage{}
}

func (p *BranchUsage) InitDefault() {
}

func (p *BranchUsage) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *BranchUsage) GetBranchId() (v string) {
	return p.BranchId
}

func (p *BranchUsage) GetDataSizeUsedBytes() (v int64) {
	return p.DataSizeUsedBytes
}

func (p *BranchUsage) GetDataSizeTotalBytes() (v int64) {
	return p.DataSizeTotalBytes
}

func (p *BranchUsage) GetComputeTimeSeconds() (v int64) {
	return p.ComputeTimeSeconds
}

func (p *BranchUsage) GetStatTime() (v string) {
	return p.StatTime
}

func (p *BranchUsage) GetLastRunningTime() (v string) {
	return p.LastRunningTime
}
func (p *BranchUsage) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *BranchUsage) SetBranchId(val string) {
	p.BranchId = val
}
func (p *BranchUsage) SetDataSizeUsedBytes(val int64) {
	p.DataSizeUsedBytes = val
}
func (p *BranchUsage) SetDataSizeTotalBytes(val int64) {
	p.DataSizeTotalBytes = val
}
func (p *BranchUsage) SetComputeTimeSeconds(val int64) {
	p.ComputeTimeSeconds = val
}
func (p *BranchUsage) SetStatTime(val string) {
	p.StatTime = val
}
func (p *BranchUsage) SetLastRunningTime(val string) {
	p.LastRunningTime = val
}

var fieldIDToName_BranchUsage = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "DataSizeUsedBytes",
	4: "DataSizeTotalBytes",
	5: "ComputeTimeSeconds",
	6: "StatTime",
	7: "LastRunningTime",
}

func (p *BranchUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BranchUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetStatTime bool = false
	var issetLastRunningTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastRunningTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeUsedBytes {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLastRunningTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BranchUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BranchUsage[fieldId]))
}

func (p *BranchUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *BranchUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}
func (p *BranchUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return nil
}
func (p *BranchUsage) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return nil
}
func (p *BranchUsage) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return nil
}
func (p *BranchUsage) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatTime = _field
	return nil
}
func (p *BranchUsage) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastRunningTime = _field
	return nil
}

func (p *BranchUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BranchUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("BranchUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BranchUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *BranchUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BranchUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeUsedBytes", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeUsedBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *BranchUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeTotalBytes", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeTotalBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *BranchUsage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeTimeSeconds", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ComputeTimeSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BranchUsage) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *BranchUsage) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastRunningTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastRunningTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *BranchUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BranchUsage(%+v)", *p)

}

func (p *BranchUsage) DeepEqual(ano *BranchUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DataSizeUsedBytes) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataSizeTotalBytes) {
		return false
	}
	if !p.Field5DeepEqual(ano.ComputeTimeSeconds) {
		return false
	}
	if !p.Field6DeepEqual(ano.StatTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.LastRunningTime) {
		return false
	}
	return true
}

func (p *BranchUsage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *BranchUsage) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}
func (p *BranchUsage) Field3DeepEqual(src int64) bool {

	if p.DataSizeUsedBytes != src {
		return false
	}
	return true
}
func (p *BranchUsage) Field4DeepEqual(src int64) bool {

	if p.DataSizeTotalBytes != src {
		return false
	}
	return true
}
func (p *BranchUsage) Field5DeepEqual(src int64) bool {

	if p.ComputeTimeSeconds != src {
		return false
	}
	return true
}
func (p *BranchUsage) Field6DeepEqual(src string) bool {

	if strings.Compare(p.StatTime, src) != 0 {
		return false
	}
	return true
}
func (p *BranchUsage) Field7DeepEqual(src string) bool {

	if strings.Compare(p.LastRunningTime, src) != 0 {
		return false
	}
	return true
}

type CreateBranchReq struct {
	InstanceId      *string            `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	BranchSettings  *BranchSettings    `thrift:"BranchSettings,2,optional" frugal:"2,optional,BranchSettings" json:"BranchSettings,omitempty"`
	ComputeSettings []*ComputeSettings `thrift:"ComputeSettings,3,optional" frugal:"3,optional,list<ComputeSettings>" json:"ComputeSettings,omitempty"`
}

func NewCreateBranchReq() *CreateBranchReq {
	return &CreateBranchReq{}
}

func (p *CreateBranchReq) InitDefault() {
}

var CreateBranchReq_InstanceId_DEFAULT string

func (p *CreateBranchReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return CreateBranchReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var CreateBranchReq_BranchSettings_DEFAULT *BranchSettings

func (p *CreateBranchReq) GetBranchSettings() (v *BranchSettings) {
	if !p.IsSetBranchSettings() {
		return CreateBranchReq_BranchSettings_DEFAULT
	}
	return p.BranchSettings
}

var CreateBranchReq_ComputeSettings_DEFAULT []*ComputeSettings

func (p *CreateBranchReq) GetComputeSettings() (v []*ComputeSettings) {
	if !p.IsSetComputeSettings() {
		return CreateBranchReq_ComputeSettings_DEFAULT
	}
	return p.ComputeSettings
}
func (p *CreateBranchReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *CreateBranchReq) SetBranchSettings(val *BranchSettings) {
	p.BranchSettings = val
}
func (p *CreateBranchReq) SetComputeSettings(val []*ComputeSettings) {
	p.ComputeSettings = val
}

var fieldIDToName_CreateBranchReq = map[int16]string{
	1: "InstanceId",
	2: "BranchSettings",
	3: "ComputeSettings",
}

func (p *CreateBranchReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *CreateBranchReq) IsSetBranchSettings() bool {
	return p.BranchSettings != nil
}

func (p *CreateBranchReq) IsSetComputeSettings() bool {
	return p.ComputeSettings != nil
}

func (p *CreateBranchReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBranchReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBranchReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateBranchReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateBranchReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewBranchSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BranchSettings = _field
	return nil
}
func (p *CreateBranchReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ComputeSettings, 0, size)
	values := make([]ComputeSettings, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ComputeSettings = _field
	return nil
}

func (p *CreateBranchReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBranchReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBranchReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBranchReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBranchReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchSettings() {
		if err = oprot.WriteFieldBegin("BranchSettings", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BranchSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateBranchReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSettings() {
		if err = oprot.WriteFieldBegin("ComputeSettings", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ComputeSettings)); err != nil {
			return err
		}
		for _, v := range p.ComputeSettings {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateBranchReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBranchReq(%+v)", *p)

}

func (p *CreateBranchReq) DeepEqual(ano *CreateBranchReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchSettings) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeSettings) {
		return false
	}
	return true
}

func (p *CreateBranchReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateBranchReq) Field2DeepEqual(src *BranchSettings) bool {

	if !p.BranchSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateBranchReq) Field3DeepEqual(src []*ComputeSettings) bool {

	if len(p.ComputeSettings) != len(src) {
		return false
	}
	for i, v := range p.ComputeSettings {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateBranchResp struct {
	InstanceId string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId   string  `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
	Branch     *Branch `thrift:"Branch,3,required" frugal:"3,required,Branch" json:"Branch"`
}

func NewCreateBranchResp() *CreateBranchResp {
	return &CreateBranchResp{}
}

func (p *CreateBranchResp) InitDefault() {
}

func (p *CreateBranchResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateBranchResp) GetBranchId() (v string) {
	return p.BranchId
}

var CreateBranchResp_Branch_DEFAULT *Branch

func (p *CreateBranchResp) GetBranch() (v *Branch) {
	if !p.IsSetBranch() {
		return CreateBranchResp_Branch_DEFAULT
	}
	return p.Branch
}
func (p *CreateBranchResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateBranchResp) SetBranchId(val string) {
	p.BranchId = val
}
func (p *CreateBranchResp) SetBranch(val *Branch) {
	p.Branch = val
}

var fieldIDToName_CreateBranchResp = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "Branch",
}

func (p *CreateBranchResp) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *CreateBranchResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBranchResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetBranch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBranch {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBranchResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBranchResp[fieldId]))
}

func (p *CreateBranchResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateBranchResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}
func (p *CreateBranchResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Branch = _field
	return nil
}

func (p *CreateBranchResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateBranchResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBranchResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBranchResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateBranchResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateBranchResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branch", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Branch.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateBranchResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBranchResp(%+v)", *p)

}

func (p *CreateBranchResp) DeepEqual(ano *CreateBranchResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Branch) {
		return false
	}
	return true
}

func (p *CreateBranchResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBranchResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBranchResp) Field3DeepEqual(src *Branch) bool {

	if !p.Branch.DeepEqual(src) {
		return false
	}
	return true
}

type DescribeBranchesReq struct {
	InstanceId *string        `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	Search     *string        `thrift:"Search,2,optional" frugal:"2,optional,string" json:"Search,omitempty"`
	SortBy     *string        `thrift:"SortBy,3,optional" frugal:"3,optional,string" json:"SortBy,omitempty"`
	SortOrder  *SortOrder     `thrift:"SortOrder,4,optional" frugal:"4,optional,SortOrder" json:"SortOrder,omitempty"`
	Offset     *int32         `thrift:"Offset,5,optional" frugal:"5,optional,i32" json:"Offset,omitempty"`
	Limit      *int32         `thrift:"Limit,6,optional" frugal:"6,optional,i32" json:"Limit,omitempty"`
	Filters    []*QueryFilter `thrift:"Filters,7,optional" frugal:"7,optional,list<QueryFilter>" json:"Filters,omitempty"`
}

func NewDescribeBranchesReq() *DescribeBranchesReq {
	return &DescribeBranchesReq{}
}

func (p *DescribeBranchesReq) InitDefault() {
}

var DescribeBranchesReq_InstanceId_DEFAULT string

func (p *DescribeBranchesReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeBranchesReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeBranchesReq_Search_DEFAULT string

func (p *DescribeBranchesReq) GetSearch() (v string) {
	if !p.IsSetSearch() {
		return DescribeBranchesReq_Search_DEFAULT
	}
	return *p.Search
}

var DescribeBranchesReq_SortBy_DEFAULT string

func (p *DescribeBranchesReq) GetSortBy() (v string) {
	if !p.IsSetSortBy() {
		return DescribeBranchesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeBranchesReq_SortOrder_DEFAULT SortOrder

func (p *DescribeBranchesReq) GetSortOrder() (v SortOrder) {
	if !p.IsSetSortOrder() {
		return DescribeBranchesReq_SortOrder_DEFAULT
	}
	return *p.SortOrder
}

var DescribeBranchesReq_Offset_DEFAULT int32

func (p *DescribeBranchesReq) GetOffset() (v int32) {
	if !p.IsSetOffset() {
		return DescribeBranchesReq_Offset_DEFAULT
	}
	return *p.Offset
}

var DescribeBranchesReq_Limit_DEFAULT int32

func (p *DescribeBranchesReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return DescribeBranchesReq_Limit_DEFAULT
	}
	return *p.Limit
}

var DescribeBranchesReq_Filters_DEFAULT []*QueryFilter

func (p *DescribeBranchesReq) GetFilters() (v []*QueryFilter) {
	if !p.IsSetFilters() {
		return DescribeBranchesReq_Filters_DEFAULT
	}
	return p.Filters
}
func (p *DescribeBranchesReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeBranchesReq) SetSearch(val *string) {
	p.Search = val
}
func (p *DescribeBranchesReq) SetSortBy(val *string) {
	p.SortBy = val
}
func (p *DescribeBranchesReq) SetSortOrder(val *SortOrder) {
	p.SortOrder = val
}
func (p *DescribeBranchesReq) SetOffset(val *int32) {
	p.Offset = val
}
func (p *DescribeBranchesReq) SetLimit(val *int32) {
	p.Limit = val
}
func (p *DescribeBranchesReq) SetFilters(val []*QueryFilter) {
	p.Filters = val
}

var fieldIDToName_DescribeBranchesReq = map[int16]string{
	1: "InstanceId",
	2: "Search",
	3: "SortBy",
	4: "SortOrder",
	5: "Offset",
	6: "Limit",
	7: "Filters",
}

func (p *DescribeBranchesReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeBranchesReq) IsSetSearch() bool {
	return p.Search != nil
}

func (p *DescribeBranchesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeBranchesReq) IsSetSortOrder() bool {
	return p.SortOrder != nil
}

func (p *DescribeBranchesReq) IsSetOffset() bool {
	return p.Offset != nil
}

func (p *DescribeBranchesReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *DescribeBranchesReq) IsSetFilters() bool {
	return p.Filters != nil
}

func (p *DescribeBranchesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeBranchesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Search = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortOrder
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Offset = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *DescribeBranchesReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*QueryFilter, 0, size)
	values := make([]QueryFilter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Filters = _field
	return nil
}

func (p *DescribeBranchesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearch() {
		if err = oprot.WriteFieldBegin("Search", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Search); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SortBy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortOrder() {
		if err = oprot.WriteFieldBegin("SortOrder", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortOrder)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOffset() {
		if err = oprot.WriteFieldBegin("Offset", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Offset); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeBranchesReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilters() {
		if err = oprot.WriteFieldBegin("Filters", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Filters)); err != nil {
			return err
		}
		for _, v := range p.Filters {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeBranchesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBranchesReq(%+v)", *p)

}

func (p *DescribeBranchesReq) DeepEqual(ano *DescribeBranchesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Search) {
		return false
	}
	if !p.Field3DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortOrder) {
		return false
	}
	if !p.Field5DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field6DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field7DeepEqual(ano.Filters) {
		return false
	}
	return true
}

func (p *DescribeBranchesReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field2DeepEqual(src *string) bool {

	if p.Search == src {
		return true
	} else if p.Search == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Search, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field3DeepEqual(src *string) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SortBy, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field4DeepEqual(src *SortOrder) bool {

	if p.SortOrder == src {
		return true
	} else if p.SortOrder == nil || src == nil {
		return false
	}
	if *p.SortOrder != *src {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field5DeepEqual(src *int32) bool {

	if p.Offset == src {
		return true
	} else if p.Offset == nil || src == nil {
		return false
	}
	if *p.Offset != *src {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field6DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}
func (p *DescribeBranchesReq) Field7DeepEqual(src []*QueryFilter) bool {

	if len(p.Filters) != len(src) {
		return false
	}
	for i, v := range p.Filters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeBranchesResp struct {
	Total    int32     `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Branches []*Branch `thrift:"Branches,2,required" frugal:"2,required,list<Branch>" json:"Branches"`
}

func NewDescribeBranchesResp() *DescribeBranchesResp {
	return &DescribeBranchesResp{}
}

func (p *DescribeBranchesResp) InitDefault() {
}

func (p *DescribeBranchesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeBranchesResp) GetBranches() (v []*Branch) {
	return p.Branches
}
func (p *DescribeBranchesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeBranchesResp) SetBranches(val []*Branch) {
	p.Branches = val
}

var fieldIDToName_DescribeBranchesResp = map[int16]string{
	1: "Total",
	2: "Branches",
}

func (p *DescribeBranchesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBranches bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranches = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranches {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBranchesResp[fieldId]))
}

func (p *DescribeBranchesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeBranchesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Branch, 0, size)
	values := make([]Branch, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Branches = _field
	return nil
}

func (p *DescribeBranchesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBranchesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBranchesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branches", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Branches)); err != nil {
		return err
	}
	for _, v := range p.Branches {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBranchesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBranchesResp(%+v)", *p)

}

func (p *DescribeBranchesResp) DeepEqual(ano *DescribeBranchesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Branches) {
		return false
	}
	return true
}

func (p *DescribeBranchesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeBranchesResp) Field2DeepEqual(src []*Branch) bool {

	if len(p.Branches) != len(src) {
		return false
	}
	for i, v := range p.Branches {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeChildBranchesReq struct {
	InstanceId     *string    `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	ParentBranchId string     `thrift:"ParentBranchId,2,required" frugal:"2,required,string" json:"ParentBranchId"`
	SortBy         *string    `thrift:"SortBy,3,optional" frugal:"3,optional,string" json:"SortBy,omitempty"`
	SortOrder      *SortOrder `thrift:"SortOrder,4,optional" frugal:"4,optional,SortOrder" json:"SortOrder,omitempty"`
	Offset         *int32     `thrift:"Offset,5,optional" frugal:"5,optional,i32" json:"Offset,omitempty"`
	Limit          *int32     `thrift:"Limit,6,optional" frugal:"6,optional,i32" json:"Limit,omitempty"`
}

func NewDescribeChildBranchesReq() *DescribeChildBranchesReq {
	return &DescribeChildBranchesReq{}
}

func (p *DescribeChildBranchesReq) InitDefault() {
}

var DescribeChildBranchesReq_InstanceId_DEFAULT string

func (p *DescribeChildBranchesReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeChildBranchesReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

func (p *DescribeChildBranchesReq) GetParentBranchId() (v string) {
	return p.ParentBranchId
}

var DescribeChildBranchesReq_SortBy_DEFAULT string

func (p *DescribeChildBranchesReq) GetSortBy() (v string) {
	if !p.IsSetSortBy() {
		return DescribeChildBranchesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeChildBranchesReq_SortOrder_DEFAULT SortOrder

func (p *DescribeChildBranchesReq) GetSortOrder() (v SortOrder) {
	if !p.IsSetSortOrder() {
		return DescribeChildBranchesReq_SortOrder_DEFAULT
	}
	return *p.SortOrder
}

var DescribeChildBranchesReq_Offset_DEFAULT int32

func (p *DescribeChildBranchesReq) GetOffset() (v int32) {
	if !p.IsSetOffset() {
		return DescribeChildBranchesReq_Offset_DEFAULT
	}
	return *p.Offset
}

var DescribeChildBranchesReq_Limit_DEFAULT int32

func (p *DescribeChildBranchesReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return DescribeChildBranchesReq_Limit_DEFAULT
	}
	return *p.Limit
}
func (p *DescribeChildBranchesReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeChildBranchesReq) SetParentBranchId(val string) {
	p.ParentBranchId = val
}
func (p *DescribeChildBranchesReq) SetSortBy(val *string) {
	p.SortBy = val
}
func (p *DescribeChildBranchesReq) SetSortOrder(val *SortOrder) {
	p.SortOrder = val
}
func (p *DescribeChildBranchesReq) SetOffset(val *int32) {
	p.Offset = val
}
func (p *DescribeChildBranchesReq) SetLimit(val *int32) {
	p.Limit = val
}

var fieldIDToName_DescribeChildBranchesReq = map[int16]string{
	1: "InstanceId",
	2: "ParentBranchId",
	3: "SortBy",
	4: "SortOrder",
	5: "Offset",
	6: "Limit",
}

func (p *DescribeChildBranchesReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeChildBranchesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeChildBranchesReq) IsSetSortOrder() bool {
	return p.SortOrder != nil
}

func (p *DescribeChildBranchesReq) IsSetOffset() bool {
	return p.Offset != nil
}

func (p *DescribeChildBranchesReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *DescribeChildBranchesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChildBranchesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetParentBranchId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetParentBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetParentBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChildBranchesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeChildBranchesReq[fieldId]))
}

func (p *DescribeChildBranchesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeChildBranchesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParentBranchId = _field
	return nil
}
func (p *DescribeChildBranchesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeChildBranchesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortOrder
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return nil
}
func (p *DescribeChildBranchesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Offset = _field
	return nil
}
func (p *DescribeChildBranchesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}

func (p *DescribeChildBranchesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChildBranchesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChildBranchesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParentBranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParentBranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SortBy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortOrder() {
		if err = oprot.WriteFieldBegin("SortOrder", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortOrder)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOffset() {
		if err = oprot.WriteFieldBegin("Offset", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Offset); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeChildBranchesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeChildBranchesReq(%+v)", *p)

}

func (p *DescribeChildBranchesReq) DeepEqual(ano *DescribeChildBranchesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ParentBranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortOrder) {
		return false
	}
	if !p.Field5DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field6DeepEqual(ano.Limit) {
		return false
	}
	return true
}

func (p *DescribeChildBranchesReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeChildBranchesReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ParentBranchId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeChildBranchesReq) Field3DeepEqual(src *string) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SortBy, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeChildBranchesReq) Field4DeepEqual(src *SortOrder) bool {

	if p.SortOrder == src {
		return true
	} else if p.SortOrder == nil || src == nil {
		return false
	}
	if *p.SortOrder != *src {
		return false
	}
	return true
}
func (p *DescribeChildBranchesReq) Field5DeepEqual(src *int32) bool {

	if p.Offset == src {
		return true
	} else if p.Offset == nil || src == nil {
		return false
	}
	if *p.Offset != *src {
		return false
	}
	return true
}
func (p *DescribeChildBranchesReq) Field6DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}

type DescribeChildBranchesResp struct {
	Total    int32     `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Branches []*Branch `thrift:"Branches,2,required" frugal:"2,required,list<Branch>" json:"Branches"`
}

func NewDescribeChildBranchesResp() *DescribeChildBranchesResp {
	return &DescribeChildBranchesResp{}
}

func (p *DescribeChildBranchesResp) InitDefault() {
}

func (p *DescribeChildBranchesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeChildBranchesResp) GetBranches() (v []*Branch) {
	return p.Branches
}
func (p *DescribeChildBranchesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeChildBranchesResp) SetBranches(val []*Branch) {
	p.Branches = val
}

var fieldIDToName_DescribeChildBranchesResp = map[int16]string{
	1: "Total",
	2: "Branches",
}

func (p *DescribeChildBranchesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChildBranchesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBranches bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranches = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranches {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChildBranchesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeChildBranchesResp[fieldId]))
}

func (p *DescribeChildBranchesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeChildBranchesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Branch, 0, size)
	values := make([]Branch, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Branches = _field
	return nil
}

func (p *DescribeChildBranchesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeChildBranchesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChildBranchesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeChildBranchesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeChildBranchesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branches", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Branches)); err != nil {
		return err
	}
	for _, v := range p.Branches {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeChildBranchesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeChildBranchesResp(%+v)", *p)

}

func (p *DescribeChildBranchesResp) DeepEqual(ano *DescribeChildBranchesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Branches) {
		return false
	}
	return true
}

func (p *DescribeChildBranchesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeChildBranchesResp) Field2DeepEqual(src []*Branch) bool {

	if len(p.Branches) != len(src) {
		return false
	}
	for i, v := range p.Branches {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeBranchDetailReq struct {
	InstanceId *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	BranchId   string  `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
}

func NewDescribeBranchDetailReq() *DescribeBranchDetailReq {
	return &DescribeBranchDetailReq{}
}

func (p *DescribeBranchDetailReq) InitDefault() {
}

var DescribeBranchDetailReq_InstanceId_DEFAULT string

func (p *DescribeBranchDetailReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeBranchDetailReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

func (p *DescribeBranchDetailReq) GetBranchId() (v string) {
	return p.BranchId
}
func (p *DescribeBranchDetailReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeBranchDetailReq) SetBranchId(val string) {
	p.BranchId = val
}

var fieldIDToName_DescribeBranchDetailReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *DescribeBranchDetailReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeBranchDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranchId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBranchDetailReq[fieldId]))
}

func (p *DescribeBranchDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBranchDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}

func (p *DescribeBranchDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBranchDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBranchDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBranchDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBranchDetailReq(%+v)", *p)

}

func (p *DescribeBranchDetailReq) DeepEqual(ano *DescribeBranchDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *DescribeBranchDetailReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBranchDetailReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}

type DescribeBranchDetailResp struct {
	Branch *Branch `thrift:"Branch,2,required" frugal:"2,required,Branch" json:"Branch"`
}

func NewDescribeBranchDetailResp() *DescribeBranchDetailResp {
	return &DescribeBranchDetailResp{}
}

func (p *DescribeBranchDetailResp) InitDefault() {
}

var DescribeBranchDetailResp_Branch_DEFAULT *Branch

func (p *DescribeBranchDetailResp) GetBranch() (v *Branch) {
	if !p.IsSetBranch() {
		return DescribeBranchDetailResp_Branch_DEFAULT
	}
	return p.Branch
}
func (p *DescribeBranchDetailResp) SetBranch(val *Branch) {
	p.Branch = val
}

var fieldIDToName_DescribeBranchDetailResp = map[int16]string{
	2: "Branch",
}

func (p *DescribeBranchDetailResp) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *DescribeBranchDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBranch {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBranchDetailResp[fieldId]))
}

func (p *DescribeBranchDetailResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Branch = _field
	return nil
}

func (p *DescribeBranchDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBranchDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBranchDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branch", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Branch.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBranchDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBranchDetailResp(%+v)", *p)

}

func (p *DescribeBranchDetailResp) DeepEqual(ano *DescribeBranchDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field2DeepEqual(ano.Branch) {
		return false
	}
	return true
}

func (p *DescribeBranchDetailResp) Field2DeepEqual(src *Branch) bool {

	if !p.Branch.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteBranchReq struct {
	InstanceId *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	BranchId   *string `thrift:"BranchId,2,optional" frugal:"2,optional,string" json:"BranchId,omitempty"`
}

func NewDeleteBranchReq() *DeleteBranchReq {
	return &DeleteBranchReq{}
}

func (p *DeleteBranchReq) InitDefault() {
}

var DeleteBranchReq_InstanceId_DEFAULT string

func (p *DeleteBranchReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DeleteBranchReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DeleteBranchReq_BranchId_DEFAULT string

func (p *DeleteBranchReq) GetBranchId() (v string) {
	if !p.IsSetBranchId() {
		return DeleteBranchReq_BranchId_DEFAULT
	}
	return *p.BranchId
}
func (p *DeleteBranchReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DeleteBranchReq) SetBranchId(val *string) {
	p.BranchId = val
}

var fieldIDToName_DeleteBranchReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *DeleteBranchReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DeleteBranchReq) IsSetBranchId() bool {
	return p.BranchId != nil
}

func (p *DeleteBranchReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBranchReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBranchReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteBranchReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteBranchReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchId = _field
	return nil
}

func (p *DeleteBranchReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBranchReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBranchReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteBranchReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteBranchReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchId() {
		if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteBranchReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteBranchReq(%+v)", *p)

}

func (p *DeleteBranchReq) DeepEqual(ano *DeleteBranchReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *DeleteBranchReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DeleteBranchReq) Field2DeepEqual(src *string) bool {

	if p.BranchId == src {
		return true
	} else if p.BranchId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchId, *src) != 0 {
		return false
	}
	return true
}

type DeleteBranchResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId   string `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
}

func NewDeleteBranchResp() *DeleteBranchResp {
	return &DeleteBranchResp{}
}

func (p *DeleteBranchResp) InitDefault() {
}

func (p *DeleteBranchResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteBranchResp) GetBranchId() (v string) {
	return p.BranchId
}
func (p *DeleteBranchResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteBranchResp) SetBranchId(val string) {
	p.BranchId = val
}

var fieldIDToName_DeleteBranchResp = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *DeleteBranchResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBranchResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBranchResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteBranchResp[fieldId]))
}

func (p *DeleteBranchResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteBranchResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}

func (p *DeleteBranchResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteBranchResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBranchResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteBranchResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteBranchResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteBranchResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteBranchResp(%+v)", *p)

}

func (p *DeleteBranchResp) DeepEqual(ano *DeleteBranchResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *DeleteBranchResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteBranchResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}

type UpdateBranchReq struct {
	InstanceId *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	BranchId   *string `thrift:"BranchId,2,optional" frugal:"2,optional,string" json:"BranchId,omitempty"`
	Name       *string `thrift:"Name,3,optional" frugal:"3,optional,string" json:"Name,omitempty"`
	Protected  *bool   `thrift:"Protected,4,optional" frugal:"4,optional,bool" json:"Protected,omitempty"`
}

func NewUpdateBranchReq() *UpdateBranchReq {
	return &UpdateBranchReq{}
}

func (p *UpdateBranchReq) InitDefault() {
}

var UpdateBranchReq_InstanceId_DEFAULT string

func (p *UpdateBranchReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return UpdateBranchReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var UpdateBranchReq_BranchId_DEFAULT string

func (p *UpdateBranchReq) GetBranchId() (v string) {
	if !p.IsSetBranchId() {
		return UpdateBranchReq_BranchId_DEFAULT
	}
	return *p.BranchId
}

var UpdateBranchReq_Name_DEFAULT string

func (p *UpdateBranchReq) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateBranchReq_Name_DEFAULT
	}
	return *p.Name
}

var UpdateBranchReq_Protected_DEFAULT bool

func (p *UpdateBranchReq) GetProtected() (v bool) {
	if !p.IsSetProtected() {
		return UpdateBranchReq_Protected_DEFAULT
	}
	return *p.Protected
}
func (p *UpdateBranchReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *UpdateBranchReq) SetBranchId(val *string) {
	p.BranchId = val
}
func (p *UpdateBranchReq) SetName(val *string) {
	p.Name = val
}
func (p *UpdateBranchReq) SetProtected(val *bool) {
	p.Protected = val
}

var fieldIDToName_UpdateBranchReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "Name",
	4: "Protected",
}

func (p *UpdateBranchReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *UpdateBranchReq) IsSetBranchId() bool {
	return p.BranchId != nil
}

func (p *UpdateBranchReq) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateBranchReq) IsSetProtected() bool {
	return p.Protected != nil
}

func (p *UpdateBranchReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateBranchReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBranchReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateBranchReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *UpdateBranchReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchId = _field
	return nil
}
func (p *UpdateBranchReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *UpdateBranchReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Protected = _field
	return nil
}

func (p *UpdateBranchReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateBranchReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBranchReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateBranchReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateBranchReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchId() {
		if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateBranchReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateBranchReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetProtected() {
		if err = oprot.WriteFieldBegin("Protected", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Protected); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UpdateBranchReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateBranchReq(%+v)", *p)

}

func (p *UpdateBranchReq) DeepEqual(ano *UpdateBranchReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.Protected) {
		return false
	}
	return true
}

func (p *UpdateBranchReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateBranchReq) Field2DeepEqual(src *string) bool {

	if p.BranchId == src {
		return true
	} else if p.BranchId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchId, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateBranchReq) Field3DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateBranchReq) Field4DeepEqual(src *bool) bool {

	if p.Protected == src {
		return true
	} else if p.Protected == nil || src == nil {
		return false
	}
	if *p.Protected != *src {
		return false
	}
	return true
}

type UpdateBranchResp struct {
	Branch *Branch `thrift:"Branch,1,required" frugal:"1,required,Branch" json:"Branch"`
}

func NewUpdateBranchResp() *UpdateBranchResp {
	return &UpdateBranchResp{}
}

func (p *UpdateBranchResp) InitDefault() {
}

var UpdateBranchResp_Branch_DEFAULT *Branch

func (p *UpdateBranchResp) GetBranch() (v *Branch) {
	if !p.IsSetBranch() {
		return UpdateBranchResp_Branch_DEFAULT
	}
	return p.Branch
}
func (p *UpdateBranchResp) SetBranch(val *Branch) {
	p.Branch = val
}

var fieldIDToName_UpdateBranchResp = map[int16]string{
	1: "Branch",
}

func (p *UpdateBranchResp) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *UpdateBranchResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateBranchResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBranchResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateBranchResp[fieldId]))
}

func (p *UpdateBranchResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Branch = _field
	return nil
}

func (p *UpdateBranchResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateBranchResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBranchResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateBranchResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branch", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Branch.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateBranchResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateBranchResp(%+v)", *p)

}

func (p *UpdateBranchResp) DeepEqual(ano *UpdateBranchResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Branch) {
		return false
	}
	return true
}

func (p *UpdateBranchResp) Field1DeepEqual(src *Branch) bool {

	if !p.Branch.DeepEqual(src) {
		return false
	}
	return true
}

type SetAsDefaultBranchReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId   string `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
}

func NewSetAsDefaultBranchReq() *SetAsDefaultBranchReq {
	return &SetAsDefaultBranchReq{}
}

func (p *SetAsDefaultBranchReq) InitDefault() {
}

func (p *SetAsDefaultBranchReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SetAsDefaultBranchReq) GetBranchId() (v string) {
	return p.BranchId
}
func (p *SetAsDefaultBranchReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SetAsDefaultBranchReq) SetBranchId(val string) {
	p.BranchId = val
}

var fieldIDToName_SetAsDefaultBranchReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *SetAsDefaultBranchReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SetAsDefaultBranchReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SetAsDefaultBranchReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SetAsDefaultBranchReq[fieldId]))
}

func (p *SetAsDefaultBranchReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SetAsDefaultBranchReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}

func (p *SetAsDefaultBranchReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SetAsDefaultBranchReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SetAsDefaultBranchReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SetAsDefaultBranchReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SetAsDefaultBranchReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SetAsDefaultBranchReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetAsDefaultBranchReq(%+v)", *p)

}

func (p *SetAsDefaultBranchReq) DeepEqual(ano *SetAsDefaultBranchReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *SetAsDefaultBranchReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SetAsDefaultBranchReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}

type SetAsDefaultBranchResp struct {
	Branch *Branch `thrift:"Branch,1,required" frugal:"1,required,Branch" json:"Branch"`
}

func NewSetAsDefaultBranchResp() *SetAsDefaultBranchResp {
	return &SetAsDefaultBranchResp{}
}

func (p *SetAsDefaultBranchResp) InitDefault() {
}

var SetAsDefaultBranchResp_Branch_DEFAULT *Branch

func (p *SetAsDefaultBranchResp) GetBranch() (v *Branch) {
	if !p.IsSetBranch() {
		return SetAsDefaultBranchResp_Branch_DEFAULT
	}
	return p.Branch
}
func (p *SetAsDefaultBranchResp) SetBranch(val *Branch) {
	p.Branch = val
}

var fieldIDToName_SetAsDefaultBranchResp = map[int16]string{
	1: "Branch",
}

func (p *SetAsDefaultBranchResp) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *SetAsDefaultBranchResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SetAsDefaultBranchResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SetAsDefaultBranchResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SetAsDefaultBranchResp[fieldId]))
}

func (p *SetAsDefaultBranchResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Branch = _field
	return nil
}

func (p *SetAsDefaultBranchResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SetAsDefaultBranchResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SetAsDefaultBranchResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SetAsDefaultBranchResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branch", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Branch.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SetAsDefaultBranchResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetAsDefaultBranchResp(%+v)", *p)

}

func (p *SetAsDefaultBranchResp) DeepEqual(ano *SetAsDefaultBranchResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Branch) {
		return false
	}
	return true
}

func (p *SetAsDefaultBranchResp) Field1DeepEqual(src *Branch) bool {

	if !p.Branch.DeepEqual(src) {
		return false
	}
	return true
}

type ResetBranchReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId   string `thrift:"BranchId,2,required" frugal:"2,required,string" json:"BranchId"`
}

func NewResetBranchReq() *ResetBranchReq {
	return &ResetBranchReq{}
}

func (p *ResetBranchReq) InitDefault() {
}

func (p *ResetBranchReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ResetBranchReq) GetBranchId() (v string) {
	return p.BranchId
}
func (p *ResetBranchReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ResetBranchReq) SetBranchId(val string) {
	p.BranchId = val
}

var fieldIDToName_ResetBranchReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *ResetBranchReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetBranchReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetBranchReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResetBranchReq[fieldId]))
}

func (p *ResetBranchReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ResetBranchReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}

func (p *ResetBranchReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetBranchReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBranchReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResetBranchReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResetBranchReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ResetBranchReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetBranchReq(%+v)", *p)

}

func (p *ResetBranchReq) DeepEqual(ano *ResetBranchReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *ResetBranchReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ResetBranchReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}

type ResetBranchResp struct {
	Branch *Branch `thrift:"Branch,1,required" frugal:"1,required,Branch" json:"Branch"`
}

func NewResetBranchResp() *ResetBranchResp {
	return &ResetBranchResp{}
}

func (p *ResetBranchResp) InitDefault() {
}

var ResetBranchResp_Branch_DEFAULT *Branch

func (p *ResetBranchResp) GetBranch() (v *Branch) {
	if !p.IsSetBranch() {
		return ResetBranchResp_Branch_DEFAULT
	}
	return p.Branch
}
func (p *ResetBranchResp) SetBranch(val *Branch) {
	p.Branch = val
}

var fieldIDToName_ResetBranchResp = map[int16]string{
	1: "Branch",
}

func (p *ResetBranchResp) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *ResetBranchResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetBranchResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetBranchResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResetBranchResp[fieldId]))
}

func (p *ResetBranchResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBranch()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Branch = _field
	return nil
}

func (p *ResetBranchResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ResetBranchResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBranchResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResetBranchResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Branch", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Branch.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResetBranchResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetBranchResp(%+v)", *p)

}

func (p *ResetBranchResp) DeepEqual(ano *ResetBranchResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Branch) {
		return false
	}
	return true
}

func (p *ResetBranchResp) Field1DeepEqual(src *Branch) bool {

	if !p.Branch.DeepEqual(src) {
		return false
	}
	return true
}

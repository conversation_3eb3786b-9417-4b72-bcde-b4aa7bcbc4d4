// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *Instance) IsValid() error {
	if p.InstanceSetting != nil {
		if err := p.InstanceSetting.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSetting not valid, %w", err)
		}
	}
	if p.ComputeSettings != nil {
		if err := p.ComputeSettings.IsValid(); err != nil {
			return fmt.Errorf("field ComputeSettings not valid, %w", err)
		}
	}
	if p.InstanceUsage != nil {
		if err := p.InstanceUsage.IsValid(); err != nil {
			return fmt.Errorf("field InstanceUsage not valid, %w", err)
		}
	}
	return nil
}
func (p *DefaultInstanceSettings) IsValid() error {
	if p.HistoryRetentionHours != nil {
		if *p.HistoryRetentionHours < int32(1) {
			return fmt.Errorf("field HistoryRetentionHours ge rule failed, current value: %v", *p.HistoryRetentionHours)
		}
		if *p.HistoryRetentionHours > int32(720) {
			return fmt.Errorf("field HistoryRetentionHours le rule failed, current value: %v", *p.HistoryRetentionHours)
		}
	}
	return nil
}
func (p *DefaultComputeSettings) IsValid() error {
	if p.AutoScalingLimitMinCU != nil {
		if *p.AutoScalingLimitMinCU < float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMinCU ge rule failed, current value: %v", *p.AutoScalingLimitMinCU)
		}
	}
	if p.AutoScalingLimitMaxCU != nil {
		if *p.AutoScalingLimitMaxCU < float64(0.25) {
			return fmt.Errorf("field AutoScalingLimitMaxCU ge rule failed, current value: %v", *p.AutoScalingLimitMaxCU)
		}
	}
	if p.SuspendTimeoutSeconds != nil {
		if *p.SuspendTimeoutSeconds < int32(-1) {
			return fmt.Errorf("field SuspendTimeoutSeconds ge rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
		if *p.SuspendTimeoutSeconds > int32(604800) {
			return fmt.Errorf("field SuspendTimeoutSeconds le rule failed, current value: %v", *p.SuspendTimeoutSeconds)
		}
	}
	return nil
}
func (p *DefaultBranchSettings) IsValid() error {
	return nil
}
func (p *InstanceUsage) IsValid() error {
	return nil
}
func (p *CreateInstanceReq) IsValid() error {
	if p.DBEngineType != nil {
		if p.DBEngineType.String() == "<UNSET>" {
			return fmt.Errorf("field DBEngineType defined_only rule failed")
		}
	}
	if p.DBEngineVersion == nil {
		return fmt.Errorf("field DBEngineVersion not_nil rule failed")
	}
	if p.DBEngineVersion.String() == "<UNSET>" {
		return fmt.Errorf("field DBEngineVersion defined_only rule failed")
	}
	if p.InstanceName != nil {
		if len(*p.InstanceName) < int(1) {
			return fmt.Errorf("field InstanceName min_len rule failed, current value: %d", len(*p.InstanceName))
		}
		if len(*p.InstanceName) > int(128) {
			return fmt.Errorf("field InstanceName max_len rule failed, current value: %d", len(*p.InstanceName))
		}
		_src := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
		if ok, _ := regexp.MatchString(_src, *p.InstanceName); !ok {
			return fmt.Errorf("field InstanceName pattern rule failed, current value: %v", *p.InstanceName)
		}
	}
	if p.InstanceSettings != nil {
		if err := p.InstanceSettings.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSettings not valid, %w", err)
		}
	}
	if p.ComputeSettings != nil {
		if err := p.ComputeSettings.IsValid(); err != nil {
			return fmt.Errorf("field ComputeSettings not valid, %w", err)
		}
	}
	if p.BranchSettings != nil {
		if err := p.BranchSettings.IsValid(); err != nil {
			return fmt.Errorf("field BranchSettings not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateInstanceResp) IsValid() error {
	if p.Instance != nil {
		if err := p.Instance.IsValid(); err != nil {
			return fmt.Errorf("field Instance not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeInstancesReq) IsValid() error {
	if p.SortOrder != nil {
		if p.SortOrder.String() == "<UNSET>" {
			return fmt.Errorf("field SortOrder defined_only rule failed")
		}
	}
	return nil
}
func (p *DescribeInstancesResp) IsValid() error {
	return nil
}
func (p *DescribeInstanceDetailReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	return nil
}
func (p *DescribeInstanceDetailResp) IsValid() error {
	if p.Instance != nil {
		if err := p.Instance.IsValid(); err != nil {
			return fmt.Errorf("field Instance not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteInstanceReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	return nil
}
func (p *DeleteInstanceResp) IsValid() error {
	return nil
}
func (p *ModifyInstanceNameReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.InstanceName == nil {
		return fmt.Errorf("field InstanceName not_nil rule failed")
	}
	if len(*p.InstanceName) < int(1) {
		return fmt.Errorf("field InstanceName min_len rule failed, current value: %d", len(*p.InstanceName))
	}
	if len(*p.InstanceName) > int(128) {
		return fmt.Errorf("field InstanceName max_len rule failed, current value: %d", len(*p.InstanceName))
	}
	_src := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
	if ok, _ := regexp.MatchString(_src, *p.InstanceName); !ok {
		return fmt.Errorf("field InstanceName pattern rule failed, current value: %v", *p.InstanceName)
	}
	return nil
}
func (p *ModifyInstanceNameResp) IsValid() error {
	if p.Instance != nil {
		if err := p.Instance.IsValid(); err != nil {
			return fmt.Errorf("field Instance not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyInstanceSettingsReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.InstanceSettings != nil {
		if err := p.InstanceSettings.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSettings not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyInstanceSettingsResp) IsValid() error {
	if p.Instance != nil {
		if err := p.Instance.IsValid(); err != nil {
			return fmt.Errorf("field Instance not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyComputeSettingsReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.ComputeSettings != nil {
		if err := p.ComputeSettings.IsValid(); err != nil {
			return fmt.Errorf("field ComputeSettings not valid, %w", err)
		}
	}
	return nil
}
func (p *ModifyComputeSettingsResp) IsValid() error {
	if p.Instance != nil {
		if err := p.Instance.IsValid(); err != nil {
			return fmt.Errorf("field Instance not valid, %w", err)
		}
	}
	return nil
}

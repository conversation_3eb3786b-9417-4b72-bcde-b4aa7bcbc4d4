// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type Database struct {
	InstanceId    string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId      *string `thrift:"BranchId,2,optional" frugal:"2,optional,string" json:"BranchId,omitempty"`
	DatabaseName  string  `thrift:"DatabaseName,3,required" frugal:"3,required,string" json:"DatabaseName"`
	DatabaseDesc  *string `thrift:"DatabaseDesc,4,optional" frugal:"4,optional,string" json:"DatabaseDesc,omitempty"`
	DatabaseOwner *string `thrift:"DatabaseOwner,5,optional" frugal:"5,optional,string" json:"DatabaseOwner,omitempty"`
	CreateTime    string  `thrift:"CreateTime,6,required" frugal:"6,required,string" json:"CreateTime"`
	UpdateTime    string  `thrift:"UpdateTime,7,required" frugal:"7,required,string" json:"UpdateTime"`
}

func NewDatabase() *Database {
	return &Database{}
}

func (p *Database) InitDefault() {
}

func (p *Database) GetInstanceId() (v string) {
	return p.InstanceId
}

var Database_BranchId_DEFAULT string

func (p *Database) GetBranchId() (v string) {
	if !p.IsSetBranchId() {
		return Database_BranchId_DEFAULT
	}
	return *p.BranchId
}

func (p *Database) GetDatabaseName() (v string) {
	return p.DatabaseName
}

var Database_DatabaseDesc_DEFAULT string

func (p *Database) GetDatabaseDesc() (v string) {
	if !p.IsSetDatabaseDesc() {
		return Database_DatabaseDesc_DEFAULT
	}
	return *p.DatabaseDesc
}

var Database_DatabaseOwner_DEFAULT string

func (p *Database) GetDatabaseOwner() (v string) {
	if !p.IsSetDatabaseOwner() {
		return Database_DatabaseOwner_DEFAULT
	}
	return *p.DatabaseOwner
}

func (p *Database) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *Database) GetUpdateTime() (v string) {
	return p.UpdateTime
}
func (p *Database) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *Database) SetBranchId(val *string) {
	p.BranchId = val
}
func (p *Database) SetDatabaseName(val string) {
	p.DatabaseName = val
}
func (p *Database) SetDatabaseDesc(val *string) {
	p.DatabaseDesc = val
}
func (p *Database) SetDatabaseOwner(val *string) {
	p.DatabaseOwner = val
}
func (p *Database) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *Database) SetUpdateTime(val string) {
	p.UpdateTime = val
}

var fieldIDToName_Database = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "DatabaseName",
	4: "DatabaseDesc",
	5: "DatabaseOwner",
	6: "CreateTime",
	7: "UpdateTime",
}

func (p *Database) IsSetBranchId() bool {
	return p.BranchId != nil
}

func (p *Database) IsSetDatabaseDesc() bool {
	return p.DatabaseDesc != nil
}

func (p *Database) IsSetDatabaseOwner() bool {
	return p.DatabaseOwner != nil
}

func (p *Database) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Database")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetDatabaseName bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabaseName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatabaseName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Database[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Database[fieldId]))
}

func (p *Database) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *Database) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchId = _field
	return nil
}
func (p *Database) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatabaseName = _field
	return nil
}
func (p *Database) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseDesc = _field
	return nil
}
func (p *Database) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseOwner = _field
	return nil
}
func (p *Database) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *Database) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *Database) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Database")

	var fieldId int16
	if err = oprot.WriteStructBegin("Database"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Database) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Database) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchId() {
		if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Database) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatabaseName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Database) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseDesc() {
		if err = oprot.WriteFieldBegin("DatabaseDesc", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Database) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseOwner() {
		if err = oprot.WriteFieldBegin("DatabaseOwner", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseOwner); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Database) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Database) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Database) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Database(%+v)", *p)

}

func (p *Database) DeepEqual(ano *Database) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field4DeepEqual(ano.DatabaseDesc) {
		return false
	}
	if !p.Field5DeepEqual(ano.DatabaseOwner) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.UpdateTime) {
		return false
	}
	return true
}

func (p *Database) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field2DeepEqual(src *string) bool {

	if p.BranchId == src {
		return true
	} else if p.BranchId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchId, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field3DeepEqual(src string) bool {

	if strings.Compare(p.DatabaseName, src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field4DeepEqual(src *string) bool {

	if p.DatabaseDesc == src {
		return true
	} else if p.DatabaseDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field5DeepEqual(src *string) bool {

	if p.DatabaseOwner == src {
		return true
	} else if p.DatabaseOwner == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseOwner, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field7DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}

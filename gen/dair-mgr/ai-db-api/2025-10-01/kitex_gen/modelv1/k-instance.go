// Code generated by Kitex v1.18.1. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *Instance) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetRegionId bool = false
	var issetZoneIds bool = false
	var issetProjectId bool = false
	var issetAccountId bool = false
	var issetDBEngineType bool = false
	var issetDBEngineVersion bool = false
	var issetDBEngineMinorVersion bool = false
	var issetInstanceSetting bool = false
	var issetComputeSettings bool = false
	var issetStorageType bool = false
	var issetStorageSize bool = false
	var issetInstanceUsage bool = false
	var issetInstanceStatus bool = false
	var issetCreationSource bool = false
	var issetInstanceTags bool = false
	var issetStatusChangedTime bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetLastActiveTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetZoneIds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProjectId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAccountId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDBEngineType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDBEngineVersion = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDBEngineMinorVersion = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceSetting = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetComputeSettings = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStorageType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStorageSize = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceUsage = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreationSource = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceTags = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatusChangedTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLastActiveTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetZoneIds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetProjectId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAccountId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineVersion {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineMinorVersion {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetInstanceSetting {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetComputeSettings {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetStorageType {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetStorageSize {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetInstanceUsage {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetInstanceStatus {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetCreationSource {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetInstanceTags {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetStatusChangedTime {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 20
		goto RequiredFieldNotSetError
	}

	if !issetLastActiveTime {
		fieldId = 21
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Instance[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_Instance[fieldId]))
}

func (p *Instance) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *Instance) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *Instance) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RegionId = _field
	return offset, nil
}

func (p *Instance) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.ZoneIds = _field
	return offset, nil
}

func (p *Instance) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProjectId = _field
	return offset, nil
}

func (p *Instance) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AccountId = _field
	return offset, nil
}

func (p *Instance) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field DBEngineType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DBEngineType(v)
	}
	p.DBEngineType = _field
	return offset, nil
}

func (p *Instance) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field DBEngineVersion
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DBEngineVersion(v)
	}
	p.DBEngineVersion = _field
	return offset, nil
}

func (p *Instance) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field DBEngineMinorVersion
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = DBEngineMinorVersion(v)
	}
	p.DBEngineMinorVersion = _field
	return offset, nil
}

func (p *Instance) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultInstanceSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.InstanceSetting = _field
	return offset, nil
}

func (p *Instance) FastReadField11(buf []byte) (int, error) {
	offset := 0
	_field := NewComputeSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ComputeSettings = _field
	return offset, nil
}

func (p *Instance) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field StorageType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = StorageType(v)
	}
	p.StorageType = _field
	return offset, nil
}

func (p *Instance) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StorageSize = _field
	return offset, nil
}

func (p *Instance) FastReadField14(buf []byte) (int, error) {
	offset := 0
	_field := NewInstanceUsage()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.InstanceUsage = _field
	return offset, nil
}

func (p *Instance) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field InstanceStatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = InstanceStatus(v)
	}
	p.InstanceStatus = _field
	return offset, nil
}

func (p *Instance) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field CreationSource
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = CreationSource(v)
	}
	p.CreationSource = _field
	return offset, nil
}

func (p *Instance) FastReadField17(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Tag, 0, size)
	values := make([]Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.InstanceTags = _field
	return offset, nil
}

func (p *Instance) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StatusChangedTime = _field
	return offset, nil
}

func (p *Instance) FastReadField19(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *Instance) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *Instance) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LastActiveTime = _field
	return offset, nil
}

func (p *Instance) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Instance) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Instance) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Instance) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *Instance) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceName)
	return offset
}

func (p *Instance) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RegionId)
	return offset
}

func (p *Instance) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ZoneIds {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *Instance) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ProjectId)
	return offset
}

func (p *Instance) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AccountId)
	return offset
}

func (p *Instance) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DBEngineType))
	return offset
}

func (p *Instance) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DBEngineVersion))
	return offset
}

func (p *Instance) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.DBEngineMinorVersion))
	return offset
}

func (p *Instance) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.InstanceSetting.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *Instance) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 11)
	offset += p.ComputeSettings.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *Instance) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.StorageType))
	return offset
}

func (p *Instance) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 13)
	offset += thrift.Binary.WriteI32(buf[offset:], p.StorageSize)
	return offset
}

func (p *Instance) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 14)
	offset += p.InstanceUsage.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *Instance) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InstanceStatus))
	return offset
}

func (p *Instance) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 16)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.CreationSource))
	return offset
}

func (p *Instance) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 17)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.InstanceTags {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *Instance) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 18)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StatusChangedTime)
	return offset
}

func (p *Instance) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 19)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateTime)
	return offset
}

func (p *Instance) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UpdateTime)
	return offset
}

func (p *Instance) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LastActiveTime)
	return offset
}

func (p *Instance) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *Instance) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceName)
	return l
}

func (p *Instance) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RegionId)
	return l
}

func (p *Instance) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ZoneIds {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *Instance) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ProjectId)
	return l
}

func (p *Instance) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AccountId)
	return l
}

func (p *Instance) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.InstanceSetting.BLength()
	return l
}

func (p *Instance) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ComputeSettings.BLength()
	return l
}

func (p *Instance) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.InstanceUsage.BLength()
	return l
}

func (p *Instance) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Instance) field17Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.InstanceTags {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *Instance) field18Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StatusChangedTime)
	return l
}

func (p *Instance) field19Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateTime)
	return l
}

func (p *Instance) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UpdateTime)
	return l
}

func (p *Instance) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LastActiveTime)
	return l
}

func (p *Instance) DeepCopy(s interface{}) error {
	src, ok := s.(*Instance)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.InstanceName != "" {
		p.InstanceName = kutils.StringDeepCopy(src.InstanceName)
	}

	if src.RegionId != "" {
		p.RegionId = kutils.StringDeepCopy(src.RegionId)
	}

	if src.ZoneIds != nil {
		p.ZoneIds = make([]string, 0, len(src.ZoneIds))
		for _, elem := range src.ZoneIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.ZoneIds = append(p.ZoneIds, _elem)
		}
	}

	if src.ProjectId != "" {
		p.ProjectId = kutils.StringDeepCopy(src.ProjectId)
	}

	if src.AccountId != "" {
		p.AccountId = kutils.StringDeepCopy(src.AccountId)
	}

	p.DBEngineType = src.DBEngineType

	p.DBEngineVersion = src.DBEngineVersion

	p.DBEngineMinorVersion = src.DBEngineMinorVersion

	var _instanceSetting *DefaultInstanceSettings
	if src.InstanceSetting != nil {
		_instanceSetting = &DefaultInstanceSettings{}
		if err := _instanceSetting.DeepCopy(src.InstanceSetting); err != nil {
			return err
		}
	}
	p.InstanceSetting = _instanceSetting

	var _computeSettings *ComputeSettings
	if src.ComputeSettings != nil {
		_computeSettings = &ComputeSettings{}
		if err := _computeSettings.DeepCopy(src.ComputeSettings); err != nil {
			return err
		}
	}
	p.ComputeSettings = _computeSettings

	p.StorageType = src.StorageType

	p.StorageSize = src.StorageSize

	var _instanceUsage *InstanceUsage
	if src.InstanceUsage != nil {
		_instanceUsage = &InstanceUsage{}
		if err := _instanceUsage.DeepCopy(src.InstanceUsage); err != nil {
			return err
		}
	}
	p.InstanceUsage = _instanceUsage

	p.InstanceStatus = src.InstanceStatus

	p.CreationSource = src.CreationSource

	if src.InstanceTags != nil {
		p.InstanceTags = make([]*Tag, 0, len(src.InstanceTags))
		for _, elem := range src.InstanceTags {
			var _elem *Tag
			if elem != nil {
				_elem = &Tag{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.InstanceTags = append(p.InstanceTags, _elem)
		}
	}

	if src.StatusChangedTime != "" {
		p.StatusChangedTime = kutils.StringDeepCopy(src.StatusChangedTime)
	}

	if src.CreateTime != "" {
		p.CreateTime = kutils.StringDeepCopy(src.CreateTime)
	}

	if src.UpdateTime != "" {
		p.UpdateTime = kutils.StringDeepCopy(src.UpdateTime)
	}

	if src.LastActiveTime != "" {
		p.LastActiveTime = kutils.StringDeepCopy(src.LastActiveTime)
	}

	return nil
}

func (p *DefaultInstanceSettings) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultInstanceSettings[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DefaultInstanceSettings) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.HistoryRetentionHours = _field
	return offset, nil
}

func (p *DefaultInstanceSettings) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DeletionProtection = _field
	return offset, nil
}

func (p *DefaultInstanceSettings) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DefaultInstanceSettings) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DefaultInstanceSettings) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DefaultInstanceSettings) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetHistoryRetentionHours() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.HistoryRetentionHours)
	}
	return offset
}

func (p *DefaultInstanceSettings) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDeletionProtection() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 2)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.DeletionProtection)
	}
	return offset
}

func (p *DefaultInstanceSettings) field1Length() int {
	l := 0
	if p.IsSetHistoryRetentionHours() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DefaultInstanceSettings) field2Length() int {
	l := 0
	if p.IsSetDeletionProtection() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *DefaultInstanceSettings) DeepCopy(s interface{}) error {
	src, ok := s.(*DefaultInstanceSettings)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.HistoryRetentionHours != nil {
		tmp := *src.HistoryRetentionHours
		p.HistoryRetentionHours = &tmp
	}

	if src.DeletionProtection != nil {
		tmp := *src.DeletionProtection
		p.DeletionProtection = &tmp
	}

	return nil
}

func (p *DefaultComputeSettings) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultComputeSettings[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DefaultComputeSettings) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.AutoScalingLimitMinCU = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *float64
	if v, l, err := thrift.Binary.ReadDouble(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.AutoScalingLimitMaxCU = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SuspendTimeoutSeconds = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ComputeSpecCode = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ZoneId = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.DBEngineParams = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastReadField8(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.DBProxyParams = _field
	return offset, nil
}

func (p *DefaultComputeSettings) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DefaultComputeSettings) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DefaultComputeSettings) BLength() int {
	l := 0
	if p != nil {
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DefaultComputeSettings) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAutoScalingLimitMinCU() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 2)
		offset += thrift.Binary.WriteDouble(buf[offset:], *p.AutoScalingLimitMinCU)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAutoScalingLimitMaxCU() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.DOUBLE, 3)
		offset += thrift.Binary.WriteDouble(buf[offset:], *p.AutoScalingLimitMaxCU)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuspendTimeoutSeconds() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.SuspendTimeoutSeconds)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComputeSpecCode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ComputeSpecCode)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetZoneId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ZoneId)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBEngineParams() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 7)
		mapBeginOffset := offset
		offset += thrift.Binary.MapBeginLength()
		var length int
		for k, v := range p.DBEngineParams {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	}
	return offset
}

func (p *DefaultComputeSettings) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBProxyParams() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 8)
		mapBeginOffset := offset
		offset += thrift.Binary.MapBeginLength()
		var length int
		for k, v := range p.DBProxyParams {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	}
	return offset
}

func (p *DefaultComputeSettings) field2Length() int {
	l := 0
	if p.IsSetAutoScalingLimitMinCU() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.DoubleLength()
	}
	return l
}

func (p *DefaultComputeSettings) field3Length() int {
	l := 0
	if p.IsSetAutoScalingLimitMaxCU() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.DoubleLength()
	}
	return l
}

func (p *DefaultComputeSettings) field4Length() int {
	l := 0
	if p.IsSetSuspendTimeoutSeconds() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DefaultComputeSettings) field5Length() int {
	l := 0
	if p.IsSetComputeSpecCode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ComputeSpecCode)
	}
	return l
}

func (p *DefaultComputeSettings) field6Length() int {
	l := 0
	if p.IsSetZoneId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ZoneId)
	}
	return l
}

func (p *DefaultComputeSettings) field7Length() int {
	l := 0
	if p.IsSetDBEngineParams() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.MapBeginLength()
		for k, v := range p.DBEngineParams {
			_, _ = k, v

			l += thrift.Binary.StringLengthNocopy(k)
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DefaultComputeSettings) field8Length() int {
	l := 0
	if p.IsSetDBProxyParams() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.MapBeginLength()
		for k, v := range p.DBProxyParams {
			_, _ = k, v

			l += thrift.Binary.StringLengthNocopy(k)
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *DefaultComputeSettings) DeepCopy(s interface{}) error {
	src, ok := s.(*DefaultComputeSettings)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.AutoScalingLimitMinCU != nil {
		tmp := *src.AutoScalingLimitMinCU
		p.AutoScalingLimitMinCU = &tmp
	}

	if src.AutoScalingLimitMaxCU != nil {
		tmp := *src.AutoScalingLimitMaxCU
		p.AutoScalingLimitMaxCU = &tmp
	}

	if src.SuspendTimeoutSeconds != nil {
		tmp := *src.SuspendTimeoutSeconds
		p.SuspendTimeoutSeconds = &tmp
	}

	if src.ComputeSpecCode != nil {
		var tmp string
		if *src.ComputeSpecCode != "" {
			tmp = kutils.StringDeepCopy(*src.ComputeSpecCode)
		}
		p.ComputeSpecCode = &tmp
	}

	if src.ZoneId != nil {
		var tmp string
		if *src.ZoneId != "" {
			tmp = kutils.StringDeepCopy(*src.ZoneId)
		}
		p.ZoneId = &tmp
	}

	if src.DBEngineParams != nil {
		p.DBEngineParams = make(map[string]string, len(src.DBEngineParams))
		for key, val := range src.DBEngineParams {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.DBEngineParams[_key] = _val
		}
	}

	if src.DBProxyParams != nil {
		p.DBProxyParams = make(map[string]string, len(src.DBProxyParams))
		for key, val := range src.DBProxyParams {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.DBProxyParams[_key] = _val
		}
	}

	return nil
}

func (p *DefaultBranchSettings) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultBranchSettings[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DefaultBranchSettings) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BranchName = _field
	return offset, nil
}

func (p *DefaultBranchSettings) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.DatabaseName = _field
	return offset, nil
}

func (p *DefaultBranchSettings) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.AccountName = _field
	return offset, nil
}

func (p *DefaultBranchSettings) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DefaultBranchSettings) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DefaultBranchSettings) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DefaultBranchSettings) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBranchName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BranchName)
	}
	return offset
}

func (p *DefaultBranchSettings) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDatabaseName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.DatabaseName)
	}
	return offset
}

func (p *DefaultBranchSettings) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAccountName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.AccountName)
	}
	return offset
}

func (p *DefaultBranchSettings) field1Length() int {
	l := 0
	if p.IsSetBranchName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BranchName)
	}
	return l
}

func (p *DefaultBranchSettings) field2Length() int {
	l := 0
	if p.IsSetDatabaseName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.DatabaseName)
	}
	return l
}

func (p *DefaultBranchSettings) field3Length() int {
	l := 0
	if p.IsSetAccountName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.AccountName)
	}
	return l
}

func (p *DefaultBranchSettings) DeepCopy(s interface{}) error {
	src, ok := s.(*DefaultBranchSettings)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.BranchName != nil {
		var tmp string
		if *src.BranchName != "" {
			tmp = kutils.StringDeepCopy(*src.BranchName)
		}
		p.BranchName = &tmp
	}

	if src.DatabaseName != nil {
		var tmp string
		if *src.DatabaseName != "" {
			tmp = kutils.StringDeepCopy(*src.DatabaseName)
		}
		p.DatabaseName = &tmp
	}

	if src.AccountName != nil {
		var tmp string
		if *src.AccountName != "" {
			tmp = kutils.StringDeepCopy(*src.AccountName)
		}
		p.AccountName = &tmp
	}

	return nil
}

func (p *InstanceUsage) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetBranchComputeTimeSeconds bool = false
	var issetBranchCreatedNum bool = false
	var issetStatTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchComputeTimeSeconds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchCreatedNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDataSizeUsedBytes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchComputeTimeSeconds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBranchCreatedNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceUsage[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_InstanceUsage[fieldId]))
}

func (p *InstanceUsage) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return offset, nil
}

func (p *InstanceUsage) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return offset, nil
}

func (p *InstanceUsage) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return offset, nil
}

func (p *InstanceUsage) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchComputeTimeSeconds = _field
	return offset, nil
}

func (p *InstanceUsage) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchCreatedNum = _field
	return offset, nil
}

func (p *InstanceUsage) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StatTime = _field
	return offset, nil
}

func (p *InstanceUsage) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *InstanceUsage) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *InstanceUsage) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *InstanceUsage) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeUsedBytes)
	return offset
}

func (p *InstanceUsage) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeTotalBytes)
	return offset
}

func (p *InstanceUsage) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ComputeTimeSeconds)
	return offset
}

func (p *InstanceUsage) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.BranchComputeTimeSeconds)
	return offset
}

func (p *InstanceUsage) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 5)
	offset += thrift.Binary.WriteI64(buf[offset:], p.BranchCreatedNum)
	return offset
}

func (p *InstanceUsage) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StatTime)
	return offset
}

func (p *InstanceUsage) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InstanceUsage) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InstanceUsage) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InstanceUsage) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InstanceUsage) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InstanceUsage) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StatTime)
	return l
}

func (p *InstanceUsage) DeepCopy(s interface{}) error {
	src, ok := s.(*InstanceUsage)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.DataSizeUsedBytes = src.DataSizeUsedBytes

	p.DataSizeTotalBytes = src.DataSizeTotalBytes

	p.ComputeTimeSeconds = src.ComputeTimeSeconds

	p.BranchComputeTimeSeconds = src.BranchComputeTimeSeconds

	p.BranchCreatedNum = src.BranchCreatedNum

	if src.StatTime != "" {
		p.StatTime = kutils.StringDeepCopy(src.StatTime)
	}

	return nil
}

func (p *CreateInstanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInstanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CreateInstanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *DBEngineType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DBEngineType(v)
		_field = &tmp
	}
	p.DBEngineType = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *DBEngineVersion
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultInstanceSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.InstanceSettings = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultComputeSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ComputeSettings = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField6(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultBranchSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BranchSettings = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Tag, 0, size)
	values := make([]Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.InstanceTags = _field
	return offset, nil
}

func (p *CreateInstanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateInstanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateInstanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateInstanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBEngineType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.DBEngineType))
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDBEngineVersion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.DBEngineVersion))
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 4)
		offset += p.InstanceSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComputeSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 5)
		offset += p.ComputeSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBranchSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 6)
		offset += p.BranchSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateInstanceReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceTags() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 7)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.InstanceTags {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *CreateInstanceReq) field1Length() int {
	l := 0
	if p.IsSetDBEngineType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateInstanceReq) field2Length() int {
	l := 0
	if p.IsSetDBEngineVersion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CreateInstanceReq) field3Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *CreateInstanceReq) field4Length() int {
	l := 0
	if p.IsSetInstanceSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.InstanceSettings.BLength()
	}
	return l
}

func (p *CreateInstanceReq) field5Length() int {
	l := 0
	if p.IsSetComputeSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.ComputeSettings.BLength()
	}
	return l
}

func (p *CreateInstanceReq) field6Length() int {
	l := 0
	if p.IsSetBranchSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BranchSettings.BLength()
	}
	return l
}

func (p *CreateInstanceReq) field7Length() int {
	l := 0
	if p.IsSetInstanceTags() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.InstanceTags {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *CreateInstanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateInstanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DBEngineType != nil {
		tmp := *src.DBEngineType
		p.DBEngineType = &tmp
	}

	if src.DBEngineVersion != nil {
		tmp := *src.DBEngineVersion
		p.DBEngineVersion = &tmp
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	var _instanceSettings *DefaultInstanceSettings
	if src.InstanceSettings != nil {
		_instanceSettings = &DefaultInstanceSettings{}
		if err := _instanceSettings.DeepCopy(src.InstanceSettings); err != nil {
			return err
		}
	}
	p.InstanceSettings = _instanceSettings

	var _computeSettings *DefaultComputeSettings
	if src.ComputeSettings != nil {
		_computeSettings = &DefaultComputeSettings{}
		if err := _computeSettings.DeepCopy(src.ComputeSettings); err != nil {
			return err
		}
	}
	p.ComputeSettings = _computeSettings

	var _branchSettings *DefaultBranchSettings
	if src.BranchSettings != nil {
		_branchSettings = &DefaultBranchSettings{}
		if err := _branchSettings.DeepCopy(src.BranchSettings); err != nil {
			return err
		}
	}
	p.BranchSettings = _branchSettings

	if src.InstanceTags != nil {
		p.InstanceTags = make([]*Tag, 0, len(src.InstanceTags))
		for _, elem := range src.InstanceTags {
			var _elem *Tag
			if elem != nil {
				_elem = &Tag{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.InstanceTags = append(p.InstanceTags, _elem)
		}
	}

	return nil
}

func (p *CreateInstanceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInstanceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateInstanceResp[fieldId]))
}

func (p *CreateInstanceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateInstanceResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewInstance()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Instance = _field
	return offset, nil
}

func (p *CreateInstanceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateInstanceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateInstanceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateInstanceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateInstanceResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.Instance.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateInstanceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateInstanceResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Instance.BLength()
	return l
}

func (p *CreateInstanceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateInstanceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	var _instance *Instance
	if src.Instance != nil {
		_instance = &Instance{}
		if err := _instance.DeepCopy(src.Instance); err != nil {
			return err
		}
	}
	p.Instance = _instance

	return nil
}

func (p *DescribeInstancesReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancesReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeInstancesReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Search = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *SortOrder
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Offset = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Limit = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*QueryFilter, 0, size)
	values := make([]QueryFilter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Filters = _field
	return offset, nil
}

func (p *DescribeInstancesReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeInstancesReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeInstancesReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeInstancesReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSearch() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Search)
	}
	return offset
}

func (p *DescribeInstancesReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SortBy)
	}
	return offset
}

func (p *DescribeInstancesReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortOrder() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortOrder))
	}
	return offset
}

func (p *DescribeInstancesReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOffset() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Offset)
	}
	return offset
}

func (p *DescribeInstancesReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLimit() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Limit)
	}
	return offset
}

func (p *DescribeInstancesReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFilters() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 6)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Filters {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeInstancesReq) field1Length() int {
	l := 0
	if p.IsSetSearch() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Search)
	}
	return l
}

func (p *DescribeInstancesReq) field2Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SortBy)
	}
	return l
}

func (p *DescribeInstancesReq) field3Length() int {
	l := 0
	if p.IsSetSortOrder() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeInstancesReq) field4Length() int {
	l := 0
	if p.IsSetOffset() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeInstancesReq) field5Length() int {
	l := 0
	if p.IsSetLimit() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeInstancesReq) field6Length() int {
	l := 0
	if p.IsSetFilters() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Filters {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeInstancesReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeInstancesReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Search != nil {
		var tmp string
		if *src.Search != "" {
			tmp = kutils.StringDeepCopy(*src.Search)
		}
		p.Search = &tmp
	}

	if src.SortBy != nil {
		var tmp string
		if *src.SortBy != "" {
			tmp = kutils.StringDeepCopy(*src.SortBy)
		}
		p.SortBy = &tmp
	}

	if src.SortOrder != nil {
		tmp := *src.SortOrder
		p.SortOrder = &tmp
	}

	if src.Offset != nil {
		tmp := *src.Offset
		p.Offset = &tmp
	}

	if src.Limit != nil {
		tmp := *src.Limit
		p.Limit = &tmp
	}

	if src.Filters != nil {
		p.Filters = make([]*QueryFilter, 0, len(src.Filters))
		for _, elem := range src.Filters {
			var _elem *QueryFilter
			if elem != nil {
				_elem = &QueryFilter{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Filters = append(p.Filters, _elem)
		}
	}

	return nil
}

func (p *DescribeInstancesResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetInstances bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstances = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstances {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancesResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeInstancesResp[fieldId]))
}

func (p *DescribeInstancesResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeInstancesResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Instance, 0, size)
	values := make([]Instance, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Instances = _field
	return offset, nil
}

func (p *DescribeInstancesResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeInstancesResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeInstancesResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeInstancesResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeInstancesResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Instances {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeInstancesResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeInstancesResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Instances {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeInstancesResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeInstancesResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.Instances != nil {
		p.Instances = make([]*Instance, 0, len(src.Instances))
		for _, elem := range src.Instances {
			var _elem *Instance
			if elem != nil {
				_elem = &Instance{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Instances = append(p.Instances, _elem)
		}
	}

	return nil
}

func (p *DescribeInstanceDetailReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeInstanceDetailReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeInstanceDetailReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeInstanceDetailReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeInstanceDetailReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeInstanceDetailReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeInstanceDetailReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeInstanceDetailReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeInstanceDetailReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	return nil
}

func (p *DescribeInstanceDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstance bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstance {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeInstanceDetailResp[fieldId]))
}

func (p *DescribeInstanceDetailResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewInstance()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Instance = _field
	return offset, nil
}

func (p *DescribeInstanceDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeInstanceDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeInstanceDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeInstanceDetailResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Instance.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DescribeInstanceDetailResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Instance.BLength()
	return l
}

func (p *DescribeInstanceDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeInstanceDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _instance *Instance
	if src.Instance != nil {
		_instance = &Instance{}
		if err := _instance.DeepCopy(src.Instance); err != nil {
			return err
		}
	}
	p.Instance = _instance

	return nil
}

func (p *DeleteInstanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteInstanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteInstanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteInstanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteInstanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteInstanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteInstanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DeleteInstanceReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DeleteInstanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteInstanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	return nil
}

func (p *DeleteInstanceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteInstanceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteInstanceResp[fieldId]))
}

func (p *DeleteInstanceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteInstanceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteInstanceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteInstanceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteInstanceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DeleteInstanceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DeleteInstanceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteInstanceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	return nil
}

func (p *ModifyInstanceNameReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceNameReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ModifyInstanceNameReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyInstanceNameReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceName = _field
	return offset, nil
}

func (p *ModifyInstanceNameReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyInstanceNameReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyInstanceNameReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyInstanceNameReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *ModifyInstanceNameReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceName)
	}
	return offset
}

func (p *ModifyInstanceNameReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *ModifyInstanceNameReq) field2Length() int {
	l := 0
	if p.IsSetInstanceName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceName)
	}
	return l
}

func (p *ModifyInstanceNameReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyInstanceNameReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.InstanceName != nil {
		var tmp string
		if *src.InstanceName != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceName)
		}
		p.InstanceName = &tmp
	}

	return nil
}

func (p *ModifyInstanceNameResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceNameResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyInstanceNameResp[fieldId]))
}

func (p *ModifyInstanceNameResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyInstanceNameResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewInstance()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Instance = _field
	return offset, nil
}

func (p *ModifyInstanceNameResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyInstanceNameResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyInstanceNameResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyInstanceNameResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyInstanceNameResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.Instance.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ModifyInstanceNameResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyInstanceNameResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Instance.BLength()
	return l
}

func (p *ModifyInstanceNameResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyInstanceNameResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	var _instance *Instance
	if src.Instance != nil {
		_instance = &Instance{}
		if err := _instance.DeepCopy(src.Instance); err != nil {
			return err
		}
	}
	p.Instance = _instance

	return nil
}

func (p *ModifyInstanceSettingsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceSettingsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ModifyInstanceSettingsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyInstanceSettingsReq) FastReadField3(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultInstanceSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.InstanceSettings = _field
	return offset, nil
}

func (p *ModifyInstanceSettingsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyInstanceSettingsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyInstanceSettingsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyInstanceSettingsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *ModifyInstanceSettingsReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 3)
		offset += p.InstanceSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ModifyInstanceSettingsReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *ModifyInstanceSettingsReq) field3Length() int {
	l := 0
	if p.IsSetInstanceSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.InstanceSettings.BLength()
	}
	return l
}

func (p *ModifyInstanceSettingsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyInstanceSettingsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	var _instanceSettings *DefaultInstanceSettings
	if src.InstanceSettings != nil {
		_instanceSettings = &DefaultInstanceSettings{}
		if err := _instanceSettings.DeepCopy(src.InstanceSettings); err != nil {
			return err
		}
	}
	p.InstanceSettings = _instanceSettings

	return nil
}

func (p *ModifyInstanceSettingsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceSettingsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyInstanceSettingsResp[fieldId]))
}

func (p *ModifyInstanceSettingsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyInstanceSettingsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewInstance()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Instance = _field
	return offset, nil
}

func (p *ModifyInstanceSettingsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyInstanceSettingsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyInstanceSettingsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyInstanceSettingsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyInstanceSettingsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.Instance.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ModifyInstanceSettingsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyInstanceSettingsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Instance.BLength()
	return l
}

func (p *ModifyInstanceSettingsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyInstanceSettingsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	var _instance *Instance
	if src.Instance != nil {
		_instance = &Instance{}
		if err := _instance.DeepCopy(src.Instance); err != nil {
			return err
		}
	}
	p.Instance = _instance

	return nil
}

func (p *ModifyComputeSettingsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSettingsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ModifyComputeSettingsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyComputeSettingsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewDefaultComputeSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ComputeSettings = _field
	return offset, nil
}

func (p *ModifyComputeSettingsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyComputeSettingsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyComputeSettingsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyComputeSettingsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *ModifyComputeSettingsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComputeSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
		offset += p.ComputeSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ModifyComputeSettingsReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *ModifyComputeSettingsReq) field2Length() int {
	l := 0
	if p.IsSetComputeSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.ComputeSettings.BLength()
	}
	return l
}

func (p *ModifyComputeSettingsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyComputeSettingsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	var _computeSettings *DefaultComputeSettings
	if src.ComputeSettings != nil {
		_computeSettings = &DefaultComputeSettings{}
		if err := _computeSettings.DeepCopy(src.ComputeSettings); err != nil {
			return err
		}
	}
	p.ComputeSettings = _computeSettings

	return nil
}

func (p *ModifyComputeSettingsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSettingsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ModifyComputeSettingsResp[fieldId]))
}

func (p *ModifyComputeSettingsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ModifyComputeSettingsResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewInstance()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Instance = _field
	return offset, nil
}

func (p *ModifyComputeSettingsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ModifyComputeSettingsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ModifyComputeSettingsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ModifyComputeSettingsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ModifyComputeSettingsResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.Instance.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ModifyComputeSettingsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ModifyComputeSettingsResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Instance.BLength()
	return l
}

func (p *ModifyComputeSettingsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ModifyComputeSettingsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	var _instance *Instance
	if src.Instance != nil {
		_instance = &Instance{}
		if err := _instance.DeepCopy(src.Instance); err != nil {
			return err
		}
	}
	p.Instance = _instance

	return nil
}

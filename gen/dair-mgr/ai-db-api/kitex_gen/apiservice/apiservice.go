// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package apiservice

import (
	"code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/modelv1"
	apache_warning "code.byted.org/kitex/apache_monitor"
	"context"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type APIServiceV1 interface {
	CreateInstance(ctx context.Context, req *modelv1.CreateInstanceReq) (r *modelv1.CreateInstanceResp, err error)

	DescribeInstances(ctx context.Context, req *modelv1.DescribeInstancesReq) (r *modelv1.DescribeInstancesResp, err error)

	DescribeInstanceDetail(ctx context.Context, req *modelv1.DescribeInstanceDetailReq) (r *modelv1.DescribeInstanceDetailResp, err error)

	DeleteInstance(ctx context.Context, req *modelv1.DeleteInstanceReq) (r *modelv1.DeleteInstanceResp, err error)

	ModifyInstanceName(ctx context.Context, req *modelv1.ModifyInstanceNameReq) (r *modelv1.ModifyInstanceNameResp, err error)

	ModifyInstanceSettings(ctx context.Context, req *modelv1.ModifyInstanceSettingsReq) (r *modelv1.ModifyInstanceSettingsResp, err error)

	ModifyComputeSettings(ctx context.Context, req *modelv1.ModifyComputeSettingsReq) (r *modelv1.ModifyComputeSettingsResp, err error)

	CreateBranch(ctx context.Context, req *modelv1.CreateBranchReq) (r *modelv1.CreateBranchResp, err error)

	DescribeBranches(ctx context.Context, req *modelv1.DescribeBranchesReq) (r *modelv1.DescribeBranchesResp, err error)

	DescribeChildBranches(ctx context.Context, req *modelv1.DescribeChildBranchesReq) (r *modelv1.DescribeChildBranchesResp, err error)

	DescribeBranchDetail(ctx context.Context, req *modelv1.DescribeBranchDetailReq) (r *modelv1.DescribeBranchDetailResp, err error)

	DeleteBranch(ctx context.Context, req *modelv1.DeleteBranchReq) (r *modelv1.DeleteBranchResp, err error)

	UpdateBranch(ctx context.Context, req *modelv1.UpdateBranchReq) (r *modelv1.UpdateBranchResp, err error)

	SetAsDefaultBranch(ctx context.Context, req *modelv1.SetAsDefaultBranchReq) (r *modelv1.SetAsDefaultBranchResp, err error)

	ResetBranch(ctx context.Context, req *modelv1.ResetBranchReq) (r *modelv1.ResetBranchResp, err error)

	CreateCompute(ctx context.Context, req *modelv1.CreateComputeReq) (r *modelv1.CreateComputeResp, err error)

	DescribeComputes(ctx context.Context, req *modelv1.DescribeComputesReq) (r *modelv1.DescribeComputesResp, err error)

	DescribeComputeDetail(ctx context.Context, req *modelv1.DescribeComputeDetailReq) (r *modelv1.DescribeComputeDetailResp, err error)

	DeleteCompute(ctx context.Context, req *modelv1.DeleteComputeReq) (r *modelv1.DeleteComputeResp, err error)

	ModifyComputeName(ctx context.Context, req *modelv1.ModifyComputeNameReq) (r *modelv1.ModifyComputeNameResp, err error)

	ModifyComputeSpec(ctx context.Context, req *modelv1.ModifyComputeSpecReq) (r *modelv1.ModifyComputeSpecResp, err error)

	DescribeAccountUsage(ctx context.Context, req *modelv1.DescribeAccountUsageReq) (r *modelv1.DescribeAccountUsageResp, err error)

	CheckMgrSelfHealth(ctx context.Context, req *modelv1.CheckMgrSelfHealthReq) (r *modelv1.CheckMgrSelfHealthResp, err error)
}

type APIServiceV1CreateInstanceArgs struct {
	Req *modelv1.CreateInstanceReq `thrift:"req,1" frugal:"1,default,modelv1.CreateInstanceReq" json:"req"`
}

func NewAPIServiceV1CreateInstanceArgs() *APIServiceV1CreateInstanceArgs {
	return &APIServiceV1CreateInstanceArgs{}
}

func (p *APIServiceV1CreateInstanceArgs) InitDefault() {
}

var APIServiceV1CreateInstanceArgs_Req_DEFAULT *modelv1.CreateInstanceReq

func (p *APIServiceV1CreateInstanceArgs) GetReq() (v *modelv1.CreateInstanceReq) {
	if !p.IsSetReq() {
		return APIServiceV1CreateInstanceArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1CreateInstanceArgs) SetReq(val *modelv1.CreateInstanceReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1CreateInstanceArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1CreateInstanceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1CreateInstanceArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateInstanceArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateInstanceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateInstanceReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1CreateInstanceArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateInstanceArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInstance_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateInstanceArgs(%+v)", *p)

}

func (p *APIServiceV1CreateInstanceArgs) DeepEqual(ano *APIServiceV1CreateInstanceArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateInstanceArgs) Field1DeepEqual(src *modelv1.CreateInstanceReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CreateInstanceResult struct {
	Success *modelv1.CreateInstanceResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.CreateInstanceResp" json:"success,omitempty"`
}

func NewAPIServiceV1CreateInstanceResult() *APIServiceV1CreateInstanceResult {
	return &APIServiceV1CreateInstanceResult{}
}

func (p *APIServiceV1CreateInstanceResult) InitDefault() {
}

var APIServiceV1CreateInstanceResult_Success_DEFAULT *modelv1.CreateInstanceResp

func (p *APIServiceV1CreateInstanceResult) GetSuccess() (v *modelv1.CreateInstanceResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1CreateInstanceResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1CreateInstanceResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.CreateInstanceResp)
}

var fieldIDToName_APIServiceV1CreateInstanceResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1CreateInstanceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1CreateInstanceResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateInstanceResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateInstanceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateInstanceResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1CreateInstanceResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateInstanceResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInstance_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1CreateInstanceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateInstanceResult(%+v)", *p)

}

func (p *APIServiceV1CreateInstanceResult) DeepEqual(ano *APIServiceV1CreateInstanceResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateInstanceResult) Field0DeepEqual(src *modelv1.CreateInstanceResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeInstancesArgs struct {
	Req *modelv1.DescribeInstancesReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeInstancesReq" json:"req"`
}

func NewAPIServiceV1DescribeInstancesArgs() *APIServiceV1DescribeInstancesArgs {
	return &APIServiceV1DescribeInstancesArgs{}
}

func (p *APIServiceV1DescribeInstancesArgs) InitDefault() {
}

var APIServiceV1DescribeInstancesArgs_Req_DEFAULT *modelv1.DescribeInstancesReq

func (p *APIServiceV1DescribeInstancesArgs) GetReq() (v *modelv1.DescribeInstancesReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeInstancesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeInstancesArgs) SetReq(val *modelv1.DescribeInstancesReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeInstancesArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeInstancesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeInstancesArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstancesArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeInstancesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeInstancesReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeInstancesArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstancesArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstances_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeInstancesArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeInstancesArgs) DeepEqual(ano *APIServiceV1DescribeInstancesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeInstancesArgs) Field1DeepEqual(src *modelv1.DescribeInstancesReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeInstancesResult struct {
	Success *modelv1.DescribeInstancesResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeInstancesResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeInstancesResult() *APIServiceV1DescribeInstancesResult {
	return &APIServiceV1DescribeInstancesResult{}
}

func (p *APIServiceV1DescribeInstancesResult) InitDefault() {
}

var APIServiceV1DescribeInstancesResult_Success_DEFAULT *modelv1.DescribeInstancesResp

func (p *APIServiceV1DescribeInstancesResult) GetSuccess() (v *modelv1.DescribeInstancesResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeInstancesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeInstancesResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeInstancesResp)
}

var fieldIDToName_APIServiceV1DescribeInstancesResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeInstancesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeInstancesResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstancesResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeInstancesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeInstancesResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeInstancesResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstancesResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstances_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeInstancesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeInstancesResult(%+v)", *p)

}

func (p *APIServiceV1DescribeInstancesResult) DeepEqual(ano *APIServiceV1DescribeInstancesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeInstancesResult) Field0DeepEqual(src *modelv1.DescribeInstancesResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeInstanceDetailArgs struct {
	Req *modelv1.DescribeInstanceDetailReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeInstanceDetailReq" json:"req"`
}

func NewAPIServiceV1DescribeInstanceDetailArgs() *APIServiceV1DescribeInstanceDetailArgs {
	return &APIServiceV1DescribeInstanceDetailArgs{}
}

func (p *APIServiceV1DescribeInstanceDetailArgs) InitDefault() {
}

var APIServiceV1DescribeInstanceDetailArgs_Req_DEFAULT *modelv1.DescribeInstanceDetailReq

func (p *APIServiceV1DescribeInstanceDetailArgs) GetReq() (v *modelv1.DescribeInstanceDetailReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeInstanceDetailArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeInstanceDetailArgs) SetReq(val *modelv1.DescribeInstanceDetailReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeInstanceDetailArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeInstanceDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeInstanceDetailArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstanceDetailArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeInstanceDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeInstanceDetailReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeInstanceDetailArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstanceDetailArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeInstanceDetailArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeInstanceDetailArgs) DeepEqual(ano *APIServiceV1DescribeInstanceDetailArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeInstanceDetailArgs) Field1DeepEqual(src *modelv1.DescribeInstanceDetailReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeInstanceDetailResult struct {
	Success *modelv1.DescribeInstanceDetailResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeInstanceDetailResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeInstanceDetailResult() *APIServiceV1DescribeInstanceDetailResult {
	return &APIServiceV1DescribeInstanceDetailResult{}
}

func (p *APIServiceV1DescribeInstanceDetailResult) InitDefault() {
}

var APIServiceV1DescribeInstanceDetailResult_Success_DEFAULT *modelv1.DescribeInstanceDetailResp

func (p *APIServiceV1DescribeInstanceDetailResult) GetSuccess() (v *modelv1.DescribeInstanceDetailResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeInstanceDetailResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeInstanceDetailResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeInstanceDetailResp)
}

var fieldIDToName_APIServiceV1DescribeInstanceDetailResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeInstanceDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeInstanceDetailResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstanceDetailResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeInstanceDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeInstanceDetailResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeInstanceDetailResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeInstanceDetailResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeInstanceDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeInstanceDetailResult(%+v)", *p)

}

func (p *APIServiceV1DescribeInstanceDetailResult) DeepEqual(ano *APIServiceV1DescribeInstanceDetailResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeInstanceDetailResult) Field0DeepEqual(src *modelv1.DescribeInstanceDetailResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteInstanceArgs struct {
	Req *modelv1.DeleteInstanceReq `thrift:"req,1" frugal:"1,default,modelv1.DeleteInstanceReq" json:"req"`
}

func NewAPIServiceV1DeleteInstanceArgs() *APIServiceV1DeleteInstanceArgs {
	return &APIServiceV1DeleteInstanceArgs{}
}

func (p *APIServiceV1DeleteInstanceArgs) InitDefault() {
}

var APIServiceV1DeleteInstanceArgs_Req_DEFAULT *modelv1.DeleteInstanceReq

func (p *APIServiceV1DeleteInstanceArgs) GetReq() (v *modelv1.DeleteInstanceReq) {
	if !p.IsSetReq() {
		return APIServiceV1DeleteInstanceArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DeleteInstanceArgs) SetReq(val *modelv1.DeleteInstanceReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DeleteInstanceArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DeleteInstanceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DeleteInstanceArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteInstanceArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteInstanceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteInstanceReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DeleteInstanceArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteInstanceArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteInstance_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteInstanceArgs(%+v)", *p)

}

func (p *APIServiceV1DeleteInstanceArgs) DeepEqual(ano *APIServiceV1DeleteInstanceArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteInstanceArgs) Field1DeepEqual(src *modelv1.DeleteInstanceReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteInstanceResult struct {
	Success *modelv1.DeleteInstanceResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DeleteInstanceResp" json:"success,omitempty"`
}

func NewAPIServiceV1DeleteInstanceResult() *APIServiceV1DeleteInstanceResult {
	return &APIServiceV1DeleteInstanceResult{}
}

func (p *APIServiceV1DeleteInstanceResult) InitDefault() {
}

var APIServiceV1DeleteInstanceResult_Success_DEFAULT *modelv1.DeleteInstanceResp

func (p *APIServiceV1DeleteInstanceResult) GetSuccess() (v *modelv1.DeleteInstanceResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DeleteInstanceResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DeleteInstanceResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DeleteInstanceResp)
}

var fieldIDToName_APIServiceV1DeleteInstanceResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DeleteInstanceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DeleteInstanceResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteInstanceResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteInstanceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteInstanceResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DeleteInstanceResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteInstanceResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteInstance_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DeleteInstanceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteInstanceResult(%+v)", *p)

}

func (p *APIServiceV1DeleteInstanceResult) DeepEqual(ano *APIServiceV1DeleteInstanceResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteInstanceResult) Field0DeepEqual(src *modelv1.DeleteInstanceResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyInstanceNameArgs struct {
	Req *modelv1.ModifyInstanceNameReq `thrift:"req,1" frugal:"1,default,modelv1.ModifyInstanceNameReq" json:"req"`
}

func NewAPIServiceV1ModifyInstanceNameArgs() *APIServiceV1ModifyInstanceNameArgs {
	return &APIServiceV1ModifyInstanceNameArgs{}
}

func (p *APIServiceV1ModifyInstanceNameArgs) InitDefault() {
}

var APIServiceV1ModifyInstanceNameArgs_Req_DEFAULT *modelv1.ModifyInstanceNameReq

func (p *APIServiceV1ModifyInstanceNameArgs) GetReq() (v *modelv1.ModifyInstanceNameReq) {
	if !p.IsSetReq() {
		return APIServiceV1ModifyInstanceNameArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ModifyInstanceNameArgs) SetReq(val *modelv1.ModifyInstanceNameReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ModifyInstanceNameArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ModifyInstanceNameArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ModifyInstanceNameArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceNameArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyInstanceNameArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyInstanceNameReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ModifyInstanceNameArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceNameArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceName_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyInstanceNameArgs(%+v)", *p)

}

func (p *APIServiceV1ModifyInstanceNameArgs) DeepEqual(ano *APIServiceV1ModifyInstanceNameArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyInstanceNameArgs) Field1DeepEqual(src *modelv1.ModifyInstanceNameReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyInstanceNameResult struct {
	Success *modelv1.ModifyInstanceNameResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ModifyInstanceNameResp" json:"success,omitempty"`
}

func NewAPIServiceV1ModifyInstanceNameResult() *APIServiceV1ModifyInstanceNameResult {
	return &APIServiceV1ModifyInstanceNameResult{}
}

func (p *APIServiceV1ModifyInstanceNameResult) InitDefault() {
}

var APIServiceV1ModifyInstanceNameResult_Success_DEFAULT *modelv1.ModifyInstanceNameResp

func (p *APIServiceV1ModifyInstanceNameResult) GetSuccess() (v *modelv1.ModifyInstanceNameResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ModifyInstanceNameResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ModifyInstanceNameResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ModifyInstanceNameResp)
}

var fieldIDToName_APIServiceV1ModifyInstanceNameResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ModifyInstanceNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ModifyInstanceNameResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceNameResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyInstanceNameResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyInstanceNameResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ModifyInstanceNameResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceNameResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceName_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyInstanceNameResult(%+v)", *p)

}

func (p *APIServiceV1ModifyInstanceNameResult) DeepEqual(ano *APIServiceV1ModifyInstanceNameResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyInstanceNameResult) Field0DeepEqual(src *modelv1.ModifyInstanceNameResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyInstanceSettingsArgs struct {
	Req *modelv1.ModifyInstanceSettingsReq `thrift:"req,1" frugal:"1,default,modelv1.ModifyInstanceSettingsReq" json:"req"`
}

func NewAPIServiceV1ModifyInstanceSettingsArgs() *APIServiceV1ModifyInstanceSettingsArgs {
	return &APIServiceV1ModifyInstanceSettingsArgs{}
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) InitDefault() {
}

var APIServiceV1ModifyInstanceSettingsArgs_Req_DEFAULT *modelv1.ModifyInstanceSettingsReq

func (p *APIServiceV1ModifyInstanceSettingsArgs) GetReq() (v *modelv1.ModifyInstanceSettingsReq) {
	if !p.IsSetReq() {
		return APIServiceV1ModifyInstanceSettingsArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ModifyInstanceSettingsArgs) SetReq(val *modelv1.ModifyInstanceSettingsReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ModifyInstanceSettingsArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceSettingsArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyInstanceSettingsArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyInstanceSettingsReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceSettingsArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceSettings_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyInstanceSettingsArgs(%+v)", *p)

}

func (p *APIServiceV1ModifyInstanceSettingsArgs) DeepEqual(ano *APIServiceV1ModifyInstanceSettingsArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyInstanceSettingsArgs) Field1DeepEqual(src *modelv1.ModifyInstanceSettingsReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyInstanceSettingsResult struct {
	Success *modelv1.ModifyInstanceSettingsResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ModifyInstanceSettingsResp" json:"success,omitempty"`
}

func NewAPIServiceV1ModifyInstanceSettingsResult() *APIServiceV1ModifyInstanceSettingsResult {
	return &APIServiceV1ModifyInstanceSettingsResult{}
}

func (p *APIServiceV1ModifyInstanceSettingsResult) InitDefault() {
}

var APIServiceV1ModifyInstanceSettingsResult_Success_DEFAULT *modelv1.ModifyInstanceSettingsResp

func (p *APIServiceV1ModifyInstanceSettingsResult) GetSuccess() (v *modelv1.ModifyInstanceSettingsResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ModifyInstanceSettingsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ModifyInstanceSettingsResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ModifyInstanceSettingsResp)
}

var fieldIDToName_APIServiceV1ModifyInstanceSettingsResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ModifyInstanceSettingsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ModifyInstanceSettingsResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceSettingsResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyInstanceSettingsResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyInstanceSettingsResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ModifyInstanceSettingsResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyInstanceSettingsResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceSettings_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ModifyInstanceSettingsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyInstanceSettingsResult(%+v)", *p)

}

func (p *APIServiceV1ModifyInstanceSettingsResult) DeepEqual(ano *APIServiceV1ModifyInstanceSettingsResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyInstanceSettingsResult) Field0DeepEqual(src *modelv1.ModifyInstanceSettingsResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeSettingsArgs struct {
	Req *modelv1.ModifyComputeSettingsReq `thrift:"req,1" frugal:"1,default,modelv1.ModifyComputeSettingsReq" json:"req"`
}

func NewAPIServiceV1ModifyComputeSettingsArgs() *APIServiceV1ModifyComputeSettingsArgs {
	return &APIServiceV1ModifyComputeSettingsArgs{}
}

func (p *APIServiceV1ModifyComputeSettingsArgs) InitDefault() {
}

var APIServiceV1ModifyComputeSettingsArgs_Req_DEFAULT *modelv1.ModifyComputeSettingsReq

func (p *APIServiceV1ModifyComputeSettingsArgs) GetReq() (v *modelv1.ModifyComputeSettingsReq) {
	if !p.IsSetReq() {
		return APIServiceV1ModifyComputeSettingsArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ModifyComputeSettingsArgs) SetReq(val *modelv1.ModifyComputeSettingsReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ModifyComputeSettingsArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ModifyComputeSettingsArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ModifyComputeSettingsArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSettingsArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeSettingsArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeSettingsReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ModifyComputeSettingsArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSettingsArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSettings_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeSettingsArgs(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeSettingsArgs) DeepEqual(ano *APIServiceV1ModifyComputeSettingsArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeSettingsArgs) Field1DeepEqual(src *modelv1.ModifyComputeSettingsReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeSettingsResult struct {
	Success *modelv1.ModifyComputeSettingsResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ModifyComputeSettingsResp" json:"success,omitempty"`
}

func NewAPIServiceV1ModifyComputeSettingsResult() *APIServiceV1ModifyComputeSettingsResult {
	return &APIServiceV1ModifyComputeSettingsResult{}
}

func (p *APIServiceV1ModifyComputeSettingsResult) InitDefault() {
}

var APIServiceV1ModifyComputeSettingsResult_Success_DEFAULT *modelv1.ModifyComputeSettingsResp

func (p *APIServiceV1ModifyComputeSettingsResult) GetSuccess() (v *modelv1.ModifyComputeSettingsResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ModifyComputeSettingsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ModifyComputeSettingsResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ModifyComputeSettingsResp)
}

var fieldIDToName_APIServiceV1ModifyComputeSettingsResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ModifyComputeSettingsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ModifyComputeSettingsResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSettingsResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeSettingsResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeSettingsResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ModifyComputeSettingsResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSettingsResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSettings_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSettingsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeSettingsResult(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeSettingsResult) DeepEqual(ano *APIServiceV1ModifyComputeSettingsResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeSettingsResult) Field0DeepEqual(src *modelv1.ModifyComputeSettingsResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CreateBranchArgs struct {
	Req *modelv1.CreateBranchReq `thrift:"req,1" frugal:"1,default,modelv1.CreateBranchReq" json:"req"`
}

func NewAPIServiceV1CreateBranchArgs() *APIServiceV1CreateBranchArgs {
	return &APIServiceV1CreateBranchArgs{}
}

func (p *APIServiceV1CreateBranchArgs) InitDefault() {
}

var APIServiceV1CreateBranchArgs_Req_DEFAULT *modelv1.CreateBranchReq

func (p *APIServiceV1CreateBranchArgs) GetReq() (v *modelv1.CreateBranchReq) {
	if !p.IsSetReq() {
		return APIServiceV1CreateBranchArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1CreateBranchArgs) SetReq(val *modelv1.CreateBranchReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1CreateBranchArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1CreateBranchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1CreateBranchArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateBranchArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateBranchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateBranchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateBranchReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1CreateBranchArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateBranchArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBranch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateBranchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1CreateBranchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateBranchArgs(%+v)", *p)

}

func (p *APIServiceV1CreateBranchArgs) DeepEqual(ano *APIServiceV1CreateBranchArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateBranchArgs) Field1DeepEqual(src *modelv1.CreateBranchReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CreateBranchResult struct {
	Success *modelv1.CreateBranchResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.CreateBranchResp" json:"success,omitempty"`
}

func NewAPIServiceV1CreateBranchResult() *APIServiceV1CreateBranchResult {
	return &APIServiceV1CreateBranchResult{}
}

func (p *APIServiceV1CreateBranchResult) InitDefault() {
}

var APIServiceV1CreateBranchResult_Success_DEFAULT *modelv1.CreateBranchResp

func (p *APIServiceV1CreateBranchResult) GetSuccess() (v *modelv1.CreateBranchResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1CreateBranchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1CreateBranchResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.CreateBranchResp)
}

var fieldIDToName_APIServiceV1CreateBranchResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1CreateBranchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1CreateBranchResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateBranchResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateBranchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateBranchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateBranchResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1CreateBranchResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateBranchResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBranch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateBranchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1CreateBranchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateBranchResult(%+v)", *p)

}

func (p *APIServiceV1CreateBranchResult) DeepEqual(ano *APIServiceV1CreateBranchResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateBranchResult) Field0DeepEqual(src *modelv1.CreateBranchResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeBranchesArgs struct {
	Req *modelv1.DescribeBranchesReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeBranchesReq" json:"req"`
}

func NewAPIServiceV1DescribeBranchesArgs() *APIServiceV1DescribeBranchesArgs {
	return &APIServiceV1DescribeBranchesArgs{}
}

func (p *APIServiceV1DescribeBranchesArgs) InitDefault() {
}

var APIServiceV1DescribeBranchesArgs_Req_DEFAULT *modelv1.DescribeBranchesReq

func (p *APIServiceV1DescribeBranchesArgs) GetReq() (v *modelv1.DescribeBranchesReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeBranchesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeBranchesArgs) SetReq(val *modelv1.DescribeBranchesReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeBranchesArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeBranchesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeBranchesArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchesArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeBranchesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeBranchesReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeBranchesArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchesArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranches_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeBranchesArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeBranchesArgs) DeepEqual(ano *APIServiceV1DescribeBranchesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeBranchesArgs) Field1DeepEqual(src *modelv1.DescribeBranchesReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeBranchesResult struct {
	Success *modelv1.DescribeBranchesResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeBranchesResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeBranchesResult() *APIServiceV1DescribeBranchesResult {
	return &APIServiceV1DescribeBranchesResult{}
}

func (p *APIServiceV1DescribeBranchesResult) InitDefault() {
}

var APIServiceV1DescribeBranchesResult_Success_DEFAULT *modelv1.DescribeBranchesResp

func (p *APIServiceV1DescribeBranchesResult) GetSuccess() (v *modelv1.DescribeBranchesResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeBranchesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeBranchesResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeBranchesResp)
}

var fieldIDToName_APIServiceV1DescribeBranchesResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeBranchesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeBranchesResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchesResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeBranchesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeBranchesResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeBranchesResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchesResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranches_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeBranchesResult(%+v)", *p)

}

func (p *APIServiceV1DescribeBranchesResult) DeepEqual(ano *APIServiceV1DescribeBranchesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeBranchesResult) Field0DeepEqual(src *modelv1.DescribeBranchesResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeChildBranchesArgs struct {
	Req *modelv1.DescribeChildBranchesReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeChildBranchesReq" json:"req"`
}

func NewAPIServiceV1DescribeChildBranchesArgs() *APIServiceV1DescribeChildBranchesArgs {
	return &APIServiceV1DescribeChildBranchesArgs{}
}

func (p *APIServiceV1DescribeChildBranchesArgs) InitDefault() {
}

var APIServiceV1DescribeChildBranchesArgs_Req_DEFAULT *modelv1.DescribeChildBranchesReq

func (p *APIServiceV1DescribeChildBranchesArgs) GetReq() (v *modelv1.DescribeChildBranchesReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeChildBranchesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeChildBranchesArgs) SetReq(val *modelv1.DescribeChildBranchesReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeChildBranchesArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeChildBranchesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeChildBranchesArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeChildBranchesArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeChildBranchesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeChildBranchesReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeChildBranchesArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeChildBranchesArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChildBranches_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeChildBranchesArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeChildBranchesArgs) DeepEqual(ano *APIServiceV1DescribeChildBranchesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeChildBranchesArgs) Field1DeepEqual(src *modelv1.DescribeChildBranchesReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeChildBranchesResult struct {
	Success *modelv1.DescribeChildBranchesResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeChildBranchesResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeChildBranchesResult() *APIServiceV1DescribeChildBranchesResult {
	return &APIServiceV1DescribeChildBranchesResult{}
}

func (p *APIServiceV1DescribeChildBranchesResult) InitDefault() {
}

var APIServiceV1DescribeChildBranchesResult_Success_DEFAULT *modelv1.DescribeChildBranchesResp

func (p *APIServiceV1DescribeChildBranchesResult) GetSuccess() (v *modelv1.DescribeChildBranchesResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeChildBranchesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeChildBranchesResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeChildBranchesResp)
}

var fieldIDToName_APIServiceV1DescribeChildBranchesResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeChildBranchesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeChildBranchesResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeChildBranchesResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeChildBranchesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeChildBranchesResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeChildBranchesResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeChildBranchesResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeChildBranches_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeChildBranchesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeChildBranchesResult(%+v)", *p)

}

func (p *APIServiceV1DescribeChildBranchesResult) DeepEqual(ano *APIServiceV1DescribeChildBranchesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeChildBranchesResult) Field0DeepEqual(src *modelv1.DescribeChildBranchesResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeBranchDetailArgs struct {
	Req *modelv1.DescribeBranchDetailReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeBranchDetailReq" json:"req"`
}

func NewAPIServiceV1DescribeBranchDetailArgs() *APIServiceV1DescribeBranchDetailArgs {
	return &APIServiceV1DescribeBranchDetailArgs{}
}

func (p *APIServiceV1DescribeBranchDetailArgs) InitDefault() {
}

var APIServiceV1DescribeBranchDetailArgs_Req_DEFAULT *modelv1.DescribeBranchDetailReq

func (p *APIServiceV1DescribeBranchDetailArgs) GetReq() (v *modelv1.DescribeBranchDetailReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeBranchDetailArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeBranchDetailArgs) SetReq(val *modelv1.DescribeBranchDetailReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeBranchDetailArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeBranchDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeBranchDetailArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchDetailArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeBranchDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeBranchDetailReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeBranchDetailArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchDetailArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeBranchDetailArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeBranchDetailArgs) DeepEqual(ano *APIServiceV1DescribeBranchDetailArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeBranchDetailArgs) Field1DeepEqual(src *modelv1.DescribeBranchDetailReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeBranchDetailResult struct {
	Success *modelv1.DescribeBranchDetailResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeBranchDetailResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeBranchDetailResult() *APIServiceV1DescribeBranchDetailResult {
	return &APIServiceV1DescribeBranchDetailResult{}
}

func (p *APIServiceV1DescribeBranchDetailResult) InitDefault() {
}

var APIServiceV1DescribeBranchDetailResult_Success_DEFAULT *modelv1.DescribeBranchDetailResp

func (p *APIServiceV1DescribeBranchDetailResult) GetSuccess() (v *modelv1.DescribeBranchDetailResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeBranchDetailResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeBranchDetailResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeBranchDetailResp)
}

var fieldIDToName_APIServiceV1DescribeBranchDetailResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeBranchDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeBranchDetailResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchDetailResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeBranchDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeBranchDetailResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeBranchDetailResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeBranchDetailResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBranchDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeBranchDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeBranchDetailResult(%+v)", *p)

}

func (p *APIServiceV1DescribeBranchDetailResult) DeepEqual(ano *APIServiceV1DescribeBranchDetailResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeBranchDetailResult) Field0DeepEqual(src *modelv1.DescribeBranchDetailResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteBranchArgs struct {
	Req *modelv1.DeleteBranchReq `thrift:"req,1" frugal:"1,default,modelv1.DeleteBranchReq" json:"req"`
}

func NewAPIServiceV1DeleteBranchArgs() *APIServiceV1DeleteBranchArgs {
	return &APIServiceV1DeleteBranchArgs{}
}

func (p *APIServiceV1DeleteBranchArgs) InitDefault() {
}

var APIServiceV1DeleteBranchArgs_Req_DEFAULT *modelv1.DeleteBranchReq

func (p *APIServiceV1DeleteBranchArgs) GetReq() (v *modelv1.DeleteBranchReq) {
	if !p.IsSetReq() {
		return APIServiceV1DeleteBranchArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DeleteBranchArgs) SetReq(val *modelv1.DeleteBranchReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DeleteBranchArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DeleteBranchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DeleteBranchArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteBranchArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteBranchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteBranchReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DeleteBranchArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteBranchArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBranch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteBranchArgs(%+v)", *p)

}

func (p *APIServiceV1DeleteBranchArgs) DeepEqual(ano *APIServiceV1DeleteBranchArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteBranchArgs) Field1DeepEqual(src *modelv1.DeleteBranchReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteBranchResult struct {
	Success *modelv1.DeleteBranchResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DeleteBranchResp" json:"success,omitempty"`
}

func NewAPIServiceV1DeleteBranchResult() *APIServiceV1DeleteBranchResult {
	return &APIServiceV1DeleteBranchResult{}
}

func (p *APIServiceV1DeleteBranchResult) InitDefault() {
}

var APIServiceV1DeleteBranchResult_Success_DEFAULT *modelv1.DeleteBranchResp

func (p *APIServiceV1DeleteBranchResult) GetSuccess() (v *modelv1.DeleteBranchResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DeleteBranchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DeleteBranchResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DeleteBranchResp)
}

var fieldIDToName_APIServiceV1DeleteBranchResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DeleteBranchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DeleteBranchResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteBranchResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteBranchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteBranchResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DeleteBranchResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteBranchResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteBranch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DeleteBranchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteBranchResult(%+v)", *p)

}

func (p *APIServiceV1DeleteBranchResult) DeepEqual(ano *APIServiceV1DeleteBranchResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteBranchResult) Field0DeepEqual(src *modelv1.DeleteBranchResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1UpdateBranchArgs struct {
	Req *modelv1.UpdateBranchReq `thrift:"req,1" frugal:"1,default,modelv1.UpdateBranchReq" json:"req"`
}

func NewAPIServiceV1UpdateBranchArgs() *APIServiceV1UpdateBranchArgs {
	return &APIServiceV1UpdateBranchArgs{}
}

func (p *APIServiceV1UpdateBranchArgs) InitDefault() {
}

var APIServiceV1UpdateBranchArgs_Req_DEFAULT *modelv1.UpdateBranchReq

func (p *APIServiceV1UpdateBranchArgs) GetReq() (v *modelv1.UpdateBranchReq) {
	if !p.IsSetReq() {
		return APIServiceV1UpdateBranchArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1UpdateBranchArgs) SetReq(val *modelv1.UpdateBranchReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1UpdateBranchArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1UpdateBranchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1UpdateBranchArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1UpdateBranchArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1UpdateBranchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewUpdateBranchReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1UpdateBranchArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1UpdateBranchArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBranch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1UpdateBranchArgs(%+v)", *p)

}

func (p *APIServiceV1UpdateBranchArgs) DeepEqual(ano *APIServiceV1UpdateBranchArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1UpdateBranchArgs) Field1DeepEqual(src *modelv1.UpdateBranchReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1UpdateBranchResult struct {
	Success *modelv1.UpdateBranchResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.UpdateBranchResp" json:"success,omitempty"`
}

func NewAPIServiceV1UpdateBranchResult() *APIServiceV1UpdateBranchResult {
	return &APIServiceV1UpdateBranchResult{}
}

func (p *APIServiceV1UpdateBranchResult) InitDefault() {
}

var APIServiceV1UpdateBranchResult_Success_DEFAULT *modelv1.UpdateBranchResp

func (p *APIServiceV1UpdateBranchResult) GetSuccess() (v *modelv1.UpdateBranchResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1UpdateBranchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1UpdateBranchResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.UpdateBranchResp)
}

var fieldIDToName_APIServiceV1UpdateBranchResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1UpdateBranchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1UpdateBranchResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1UpdateBranchResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1UpdateBranchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewUpdateBranchResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1UpdateBranchResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1UpdateBranchResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBranch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1UpdateBranchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1UpdateBranchResult(%+v)", *p)

}

func (p *APIServiceV1UpdateBranchResult) DeepEqual(ano *APIServiceV1UpdateBranchResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1UpdateBranchResult) Field0DeepEqual(src *modelv1.UpdateBranchResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1SetAsDefaultBranchArgs struct {
	Req *modelv1.SetAsDefaultBranchReq `thrift:"req,1" frugal:"1,default,modelv1.SetAsDefaultBranchReq" json:"req"`
}

func NewAPIServiceV1SetAsDefaultBranchArgs() *APIServiceV1SetAsDefaultBranchArgs {
	return &APIServiceV1SetAsDefaultBranchArgs{}
}

func (p *APIServiceV1SetAsDefaultBranchArgs) InitDefault() {
}

var APIServiceV1SetAsDefaultBranchArgs_Req_DEFAULT *modelv1.SetAsDefaultBranchReq

func (p *APIServiceV1SetAsDefaultBranchArgs) GetReq() (v *modelv1.SetAsDefaultBranchReq) {
	if !p.IsSetReq() {
		return APIServiceV1SetAsDefaultBranchArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1SetAsDefaultBranchArgs) SetReq(val *modelv1.SetAsDefaultBranchReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1SetAsDefaultBranchArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1SetAsDefaultBranchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1SetAsDefaultBranchArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1SetAsDefaultBranchArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1SetAsDefaultBranchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewSetAsDefaultBranchReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1SetAsDefaultBranchArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1SetAsDefaultBranchArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("SetAsDefaultBranch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1SetAsDefaultBranchArgs(%+v)", *p)

}

func (p *APIServiceV1SetAsDefaultBranchArgs) DeepEqual(ano *APIServiceV1SetAsDefaultBranchArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1SetAsDefaultBranchArgs) Field1DeepEqual(src *modelv1.SetAsDefaultBranchReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1SetAsDefaultBranchResult struct {
	Success *modelv1.SetAsDefaultBranchResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.SetAsDefaultBranchResp" json:"success,omitempty"`
}

func NewAPIServiceV1SetAsDefaultBranchResult() *APIServiceV1SetAsDefaultBranchResult {
	return &APIServiceV1SetAsDefaultBranchResult{}
}

func (p *APIServiceV1SetAsDefaultBranchResult) InitDefault() {
}

var APIServiceV1SetAsDefaultBranchResult_Success_DEFAULT *modelv1.SetAsDefaultBranchResp

func (p *APIServiceV1SetAsDefaultBranchResult) GetSuccess() (v *modelv1.SetAsDefaultBranchResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1SetAsDefaultBranchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1SetAsDefaultBranchResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.SetAsDefaultBranchResp)
}

var fieldIDToName_APIServiceV1SetAsDefaultBranchResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1SetAsDefaultBranchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1SetAsDefaultBranchResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1SetAsDefaultBranchResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1SetAsDefaultBranchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewSetAsDefaultBranchResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1SetAsDefaultBranchResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1SetAsDefaultBranchResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("SetAsDefaultBranch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1SetAsDefaultBranchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1SetAsDefaultBranchResult(%+v)", *p)

}

func (p *APIServiceV1SetAsDefaultBranchResult) DeepEqual(ano *APIServiceV1SetAsDefaultBranchResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1SetAsDefaultBranchResult) Field0DeepEqual(src *modelv1.SetAsDefaultBranchResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ResetBranchArgs struct {
	Req *modelv1.ResetBranchReq `thrift:"req,1" frugal:"1,default,modelv1.ResetBranchReq" json:"req"`
}

func NewAPIServiceV1ResetBranchArgs() *APIServiceV1ResetBranchArgs {
	return &APIServiceV1ResetBranchArgs{}
}

func (p *APIServiceV1ResetBranchArgs) InitDefault() {
}

var APIServiceV1ResetBranchArgs_Req_DEFAULT *modelv1.ResetBranchReq

func (p *APIServiceV1ResetBranchArgs) GetReq() (v *modelv1.ResetBranchReq) {
	if !p.IsSetReq() {
		return APIServiceV1ResetBranchArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ResetBranchArgs) SetReq(val *modelv1.ResetBranchReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ResetBranchArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ResetBranchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ResetBranchArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ResetBranchArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ResetBranchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ResetBranchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewResetBranchReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ResetBranchArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ResetBranchArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBranch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ResetBranchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ResetBranchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ResetBranchArgs(%+v)", *p)

}

func (p *APIServiceV1ResetBranchArgs) DeepEqual(ano *APIServiceV1ResetBranchArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ResetBranchArgs) Field1DeepEqual(src *modelv1.ResetBranchReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ResetBranchResult struct {
	Success *modelv1.ResetBranchResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ResetBranchResp" json:"success,omitempty"`
}

func NewAPIServiceV1ResetBranchResult() *APIServiceV1ResetBranchResult {
	return &APIServiceV1ResetBranchResult{}
}

func (p *APIServiceV1ResetBranchResult) InitDefault() {
}

var APIServiceV1ResetBranchResult_Success_DEFAULT *modelv1.ResetBranchResp

func (p *APIServiceV1ResetBranchResult) GetSuccess() (v *modelv1.ResetBranchResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ResetBranchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ResetBranchResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ResetBranchResp)
}

var fieldIDToName_APIServiceV1ResetBranchResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ResetBranchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ResetBranchResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ResetBranchResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ResetBranchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ResetBranchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewResetBranchResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ResetBranchResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ResetBranchResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBranch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ResetBranchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ResetBranchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ResetBranchResult(%+v)", *p)

}

func (p *APIServiceV1ResetBranchResult) DeepEqual(ano *APIServiceV1ResetBranchResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ResetBranchResult) Field0DeepEqual(src *modelv1.ResetBranchResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CreateComputeArgs struct {
	Req *modelv1.CreateComputeReq `thrift:"req,1" frugal:"1,default,modelv1.CreateComputeReq" json:"req"`
}

func NewAPIServiceV1CreateComputeArgs() *APIServiceV1CreateComputeArgs {
	return &APIServiceV1CreateComputeArgs{}
}

func (p *APIServiceV1CreateComputeArgs) InitDefault() {
}

var APIServiceV1CreateComputeArgs_Req_DEFAULT *modelv1.CreateComputeReq

func (p *APIServiceV1CreateComputeArgs) GetReq() (v *modelv1.CreateComputeReq) {
	if !p.IsSetReq() {
		return APIServiceV1CreateComputeArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1CreateComputeArgs) SetReq(val *modelv1.CreateComputeReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1CreateComputeArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1CreateComputeArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1CreateComputeArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateComputeArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateComputeArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateComputeArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateComputeReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1CreateComputeArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateComputeArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCompute_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateComputeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1CreateComputeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateComputeArgs(%+v)", *p)

}

func (p *APIServiceV1CreateComputeArgs) DeepEqual(ano *APIServiceV1CreateComputeArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateComputeArgs) Field1DeepEqual(src *modelv1.CreateComputeReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CreateComputeResult struct {
	Success *modelv1.CreateComputeResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.CreateComputeResp" json:"success,omitempty"`
}

func NewAPIServiceV1CreateComputeResult() *APIServiceV1CreateComputeResult {
	return &APIServiceV1CreateComputeResult{}
}

func (p *APIServiceV1CreateComputeResult) InitDefault() {
}

var APIServiceV1CreateComputeResult_Success_DEFAULT *modelv1.CreateComputeResp

func (p *APIServiceV1CreateComputeResult) GetSuccess() (v *modelv1.CreateComputeResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1CreateComputeResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1CreateComputeResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.CreateComputeResp)
}

var fieldIDToName_APIServiceV1CreateComputeResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1CreateComputeResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1CreateComputeResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateComputeResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CreateComputeResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CreateComputeResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewCreateComputeResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1CreateComputeResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CreateComputeResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateCompute_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CreateComputeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1CreateComputeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CreateComputeResult(%+v)", *p)

}

func (p *APIServiceV1CreateComputeResult) DeepEqual(ano *APIServiceV1CreateComputeResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1CreateComputeResult) Field0DeepEqual(src *modelv1.CreateComputeResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeComputesArgs struct {
	Req *modelv1.DescribeComputesReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeComputesReq" json:"req"`
}

func NewAPIServiceV1DescribeComputesArgs() *APIServiceV1DescribeComputesArgs {
	return &APIServiceV1DescribeComputesArgs{}
}

func (p *APIServiceV1DescribeComputesArgs) InitDefault() {
}

var APIServiceV1DescribeComputesArgs_Req_DEFAULT *modelv1.DescribeComputesReq

func (p *APIServiceV1DescribeComputesArgs) GetReq() (v *modelv1.DescribeComputesReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeComputesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeComputesArgs) SetReq(val *modelv1.DescribeComputesReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeComputesArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeComputesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeComputesArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputesArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeComputesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeComputesReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeComputesArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputesArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputes_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeComputesArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeComputesArgs) DeepEqual(ano *APIServiceV1DescribeComputesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeComputesArgs) Field1DeepEqual(src *modelv1.DescribeComputesReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeComputesResult struct {
	Success *modelv1.DescribeComputesResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeComputesResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeComputesResult() *APIServiceV1DescribeComputesResult {
	return &APIServiceV1DescribeComputesResult{}
}

func (p *APIServiceV1DescribeComputesResult) InitDefault() {
}

var APIServiceV1DescribeComputesResult_Success_DEFAULT *modelv1.DescribeComputesResp

func (p *APIServiceV1DescribeComputesResult) GetSuccess() (v *modelv1.DescribeComputesResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeComputesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeComputesResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeComputesResp)
}

var fieldIDToName_APIServiceV1DescribeComputesResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeComputesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeComputesResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputesResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeComputesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeComputesResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeComputesResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputesResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputes_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeComputesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeComputesResult(%+v)", *p)

}

func (p *APIServiceV1DescribeComputesResult) DeepEqual(ano *APIServiceV1DescribeComputesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeComputesResult) Field0DeepEqual(src *modelv1.DescribeComputesResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeComputeDetailArgs struct {
	Req *modelv1.DescribeComputeDetailReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeComputeDetailReq" json:"req"`
}

func NewAPIServiceV1DescribeComputeDetailArgs() *APIServiceV1DescribeComputeDetailArgs {
	return &APIServiceV1DescribeComputeDetailArgs{}
}

func (p *APIServiceV1DescribeComputeDetailArgs) InitDefault() {
}

var APIServiceV1DescribeComputeDetailArgs_Req_DEFAULT *modelv1.DescribeComputeDetailReq

func (p *APIServiceV1DescribeComputeDetailArgs) GetReq() (v *modelv1.DescribeComputeDetailReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeComputeDetailArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeComputeDetailArgs) SetReq(val *modelv1.DescribeComputeDetailReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeComputeDetailArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeComputeDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeComputeDetailArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputeDetailArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeComputeDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeComputeDetailReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeComputeDetailArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputeDetailArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputeDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeComputeDetailArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeComputeDetailArgs) DeepEqual(ano *APIServiceV1DescribeComputeDetailArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeComputeDetailArgs) Field1DeepEqual(src *modelv1.DescribeComputeDetailReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeComputeDetailResult struct {
	Success *modelv1.DescribeComputeDetailResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeComputeDetailResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeComputeDetailResult() *APIServiceV1DescribeComputeDetailResult {
	return &APIServiceV1DescribeComputeDetailResult{}
}

func (p *APIServiceV1DescribeComputeDetailResult) InitDefault() {
}

var APIServiceV1DescribeComputeDetailResult_Success_DEFAULT *modelv1.DescribeComputeDetailResp

func (p *APIServiceV1DescribeComputeDetailResult) GetSuccess() (v *modelv1.DescribeComputeDetailResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeComputeDetailResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeComputeDetailResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeComputeDetailResp)
}

var fieldIDToName_APIServiceV1DescribeComputeDetailResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeComputeDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeComputeDetailResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputeDetailResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeComputeDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeComputeDetailResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeComputeDetailResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeComputeDetailResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputeDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeComputeDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeComputeDetailResult(%+v)", *p)

}

func (p *APIServiceV1DescribeComputeDetailResult) DeepEqual(ano *APIServiceV1DescribeComputeDetailResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeComputeDetailResult) Field0DeepEqual(src *modelv1.DescribeComputeDetailResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteComputeArgs struct {
	Req *modelv1.DeleteComputeReq `thrift:"req,1" frugal:"1,default,modelv1.DeleteComputeReq" json:"req"`
}

func NewAPIServiceV1DeleteComputeArgs() *APIServiceV1DeleteComputeArgs {
	return &APIServiceV1DeleteComputeArgs{}
}

func (p *APIServiceV1DeleteComputeArgs) InitDefault() {
}

var APIServiceV1DeleteComputeArgs_Req_DEFAULT *modelv1.DeleteComputeReq

func (p *APIServiceV1DeleteComputeArgs) GetReq() (v *modelv1.DeleteComputeReq) {
	if !p.IsSetReq() {
		return APIServiceV1DeleteComputeArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DeleteComputeArgs) SetReq(val *modelv1.DeleteComputeReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DeleteComputeArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DeleteComputeArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DeleteComputeArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteComputeArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteComputeArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteComputeReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DeleteComputeArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteComputeArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCompute_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteComputeArgs(%+v)", *p)

}

func (p *APIServiceV1DeleteComputeArgs) DeepEqual(ano *APIServiceV1DeleteComputeArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteComputeArgs) Field1DeepEqual(src *modelv1.DeleteComputeReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DeleteComputeResult struct {
	Success *modelv1.DeleteComputeResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DeleteComputeResp" json:"success,omitempty"`
}

func NewAPIServiceV1DeleteComputeResult() *APIServiceV1DeleteComputeResult {
	return &APIServiceV1DeleteComputeResult{}
}

func (p *APIServiceV1DeleteComputeResult) InitDefault() {
}

var APIServiceV1DeleteComputeResult_Success_DEFAULT *modelv1.DeleteComputeResp

func (p *APIServiceV1DeleteComputeResult) GetSuccess() (v *modelv1.DeleteComputeResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DeleteComputeResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DeleteComputeResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DeleteComputeResp)
}

var fieldIDToName_APIServiceV1DeleteComputeResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DeleteComputeResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DeleteComputeResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteComputeResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DeleteComputeResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDeleteComputeResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DeleteComputeResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DeleteComputeResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCompute_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DeleteComputeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DeleteComputeResult(%+v)", *p)

}

func (p *APIServiceV1DeleteComputeResult) DeepEqual(ano *APIServiceV1DeleteComputeResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DeleteComputeResult) Field0DeepEqual(src *modelv1.DeleteComputeResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeNameArgs struct {
	Req *modelv1.ModifyComputeNameReq `thrift:"req,1" frugal:"1,default,modelv1.ModifyComputeNameReq" json:"req"`
}

func NewAPIServiceV1ModifyComputeNameArgs() *APIServiceV1ModifyComputeNameArgs {
	return &APIServiceV1ModifyComputeNameArgs{}
}

func (p *APIServiceV1ModifyComputeNameArgs) InitDefault() {
}

var APIServiceV1ModifyComputeNameArgs_Req_DEFAULT *modelv1.ModifyComputeNameReq

func (p *APIServiceV1ModifyComputeNameArgs) GetReq() (v *modelv1.ModifyComputeNameReq) {
	if !p.IsSetReq() {
		return APIServiceV1ModifyComputeNameArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ModifyComputeNameArgs) SetReq(val *modelv1.ModifyComputeNameReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ModifyComputeNameArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ModifyComputeNameArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ModifyComputeNameArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeNameArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeNameArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeNameReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ModifyComputeNameArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeNameArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeName_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeNameArgs(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeNameArgs) DeepEqual(ano *APIServiceV1ModifyComputeNameArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeNameArgs) Field1DeepEqual(src *modelv1.ModifyComputeNameReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeNameResult struct {
	Success *modelv1.ModifyComputeNameResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ModifyComputeNameResp" json:"success,omitempty"`
}

func NewAPIServiceV1ModifyComputeNameResult() *APIServiceV1ModifyComputeNameResult {
	return &APIServiceV1ModifyComputeNameResult{}
}

func (p *APIServiceV1ModifyComputeNameResult) InitDefault() {
}

var APIServiceV1ModifyComputeNameResult_Success_DEFAULT *modelv1.ModifyComputeNameResp

func (p *APIServiceV1ModifyComputeNameResult) GetSuccess() (v *modelv1.ModifyComputeNameResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ModifyComputeNameResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ModifyComputeNameResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ModifyComputeNameResp)
}

var fieldIDToName_APIServiceV1ModifyComputeNameResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ModifyComputeNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ModifyComputeNameResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeNameResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeNameResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeNameResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ModifyComputeNameResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeNameResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeName_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeNameResult(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeNameResult) DeepEqual(ano *APIServiceV1ModifyComputeNameResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeNameResult) Field0DeepEqual(src *modelv1.ModifyComputeNameResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeSpecArgs struct {
	Req *modelv1.ModifyComputeSpecReq `thrift:"req,1" frugal:"1,default,modelv1.ModifyComputeSpecReq" json:"req"`
}

func NewAPIServiceV1ModifyComputeSpecArgs() *APIServiceV1ModifyComputeSpecArgs {
	return &APIServiceV1ModifyComputeSpecArgs{}
}

func (p *APIServiceV1ModifyComputeSpecArgs) InitDefault() {
}

var APIServiceV1ModifyComputeSpecArgs_Req_DEFAULT *modelv1.ModifyComputeSpecReq

func (p *APIServiceV1ModifyComputeSpecArgs) GetReq() (v *modelv1.ModifyComputeSpecReq) {
	if !p.IsSetReq() {
		return APIServiceV1ModifyComputeSpecArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1ModifyComputeSpecArgs) SetReq(val *modelv1.ModifyComputeSpecReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1ModifyComputeSpecArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1ModifyComputeSpecArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1ModifyComputeSpecArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSpecArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeSpecArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeSpecReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1ModifyComputeSpecArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSpecArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSpec_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeSpecArgs(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeSpecArgs) DeepEqual(ano *APIServiceV1ModifyComputeSpecArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeSpecArgs) Field1DeepEqual(src *modelv1.ModifyComputeSpecReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1ModifyComputeSpecResult struct {
	Success *modelv1.ModifyComputeSpecResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.ModifyComputeSpecResp" json:"success,omitempty"`
}

func NewAPIServiceV1ModifyComputeSpecResult() *APIServiceV1ModifyComputeSpecResult {
	return &APIServiceV1ModifyComputeSpecResult{}
}

func (p *APIServiceV1ModifyComputeSpecResult) InitDefault() {
}

var APIServiceV1ModifyComputeSpecResult_Success_DEFAULT *modelv1.ModifyComputeSpecResp

func (p *APIServiceV1ModifyComputeSpecResult) GetSuccess() (v *modelv1.ModifyComputeSpecResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1ModifyComputeSpecResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1ModifyComputeSpecResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.ModifyComputeSpecResp)
}

var fieldIDToName_APIServiceV1ModifyComputeSpecResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1ModifyComputeSpecResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1ModifyComputeSpecResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSpecResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1ModifyComputeSpecResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewModifyComputeSpecResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1ModifyComputeSpecResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1ModifyComputeSpecResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSpec_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1ModifyComputeSpecResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1ModifyComputeSpecResult(%+v)", *p)

}

func (p *APIServiceV1ModifyComputeSpecResult) DeepEqual(ano *APIServiceV1ModifyComputeSpecResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1ModifyComputeSpecResult) Field0DeepEqual(src *modelv1.ModifyComputeSpecResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeAccountUsageArgs struct {
	Req *modelv1.DescribeAccountUsageReq `thrift:"req,1" frugal:"1,default,modelv1.DescribeAccountUsageReq" json:"req"`
}

func NewAPIServiceV1DescribeAccountUsageArgs() *APIServiceV1DescribeAccountUsageArgs {
	return &APIServiceV1DescribeAccountUsageArgs{}
}

func (p *APIServiceV1DescribeAccountUsageArgs) InitDefault() {
}

var APIServiceV1DescribeAccountUsageArgs_Req_DEFAULT *modelv1.DescribeAccountUsageReq

func (p *APIServiceV1DescribeAccountUsageArgs) GetReq() (v *modelv1.DescribeAccountUsageReq) {
	if !p.IsSetReq() {
		return APIServiceV1DescribeAccountUsageArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1DescribeAccountUsageArgs) SetReq(val *modelv1.DescribeAccountUsageReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1DescribeAccountUsageArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1DescribeAccountUsageArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1DescribeAccountUsageArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeAccountUsageArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeAccountUsageArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeAccountUsageReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1DescribeAccountUsageArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeAccountUsageArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAccountUsage_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeAccountUsageArgs(%+v)", *p)

}

func (p *APIServiceV1DescribeAccountUsageArgs) DeepEqual(ano *APIServiceV1DescribeAccountUsageArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeAccountUsageArgs) Field1DeepEqual(src *modelv1.DescribeAccountUsageReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1DescribeAccountUsageResult struct {
	Success *modelv1.DescribeAccountUsageResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.DescribeAccountUsageResp" json:"success,omitempty"`
}

func NewAPIServiceV1DescribeAccountUsageResult() *APIServiceV1DescribeAccountUsageResult {
	return &APIServiceV1DescribeAccountUsageResult{}
}

func (p *APIServiceV1DescribeAccountUsageResult) InitDefault() {
}

var APIServiceV1DescribeAccountUsageResult_Success_DEFAULT *modelv1.DescribeAccountUsageResp

func (p *APIServiceV1DescribeAccountUsageResult) GetSuccess() (v *modelv1.DescribeAccountUsageResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1DescribeAccountUsageResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1DescribeAccountUsageResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.DescribeAccountUsageResp)
}

var fieldIDToName_APIServiceV1DescribeAccountUsageResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1DescribeAccountUsageResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1DescribeAccountUsageResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeAccountUsageResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1DescribeAccountUsageResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewDescribeAccountUsageResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1DescribeAccountUsageResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1DescribeAccountUsageResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAccountUsage_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1DescribeAccountUsageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1DescribeAccountUsageResult(%+v)", *p)

}

func (p *APIServiceV1DescribeAccountUsageResult) DeepEqual(ano *APIServiceV1DescribeAccountUsageResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1DescribeAccountUsageResult) Field0DeepEqual(src *modelv1.DescribeAccountUsageResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CheckMgrSelfHealthArgs struct {
	Req *modelv1.CheckMgrSelfHealthReq `thrift:"req,1" frugal:"1,default,modelv1.CheckMgrSelfHealthReq" json:"req"`
}

func NewAPIServiceV1CheckMgrSelfHealthArgs() *APIServiceV1CheckMgrSelfHealthArgs {
	return &APIServiceV1CheckMgrSelfHealthArgs{}
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) InitDefault() {
}

var APIServiceV1CheckMgrSelfHealthArgs_Req_DEFAULT *modelv1.CheckMgrSelfHealthReq

func (p *APIServiceV1CheckMgrSelfHealthArgs) GetReq() (v *modelv1.CheckMgrSelfHealthReq) {
	if !p.IsSetReq() {
		return APIServiceV1CheckMgrSelfHealthArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *APIServiceV1CheckMgrSelfHealthArgs) SetReq(val *modelv1.CheckMgrSelfHealthReq) {
	p.Req = val
}

var fieldIDToName_APIServiceV1CheckMgrSelfHealthArgs = map[int16]string{
	1: "req",
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CheckMgrSelfHealthArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CheckMgrSelfHealthArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := modelv1.NewCheckMgrSelfHealthReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CheckMgrSelfHealthArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckMgrSelfHealth_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CheckMgrSelfHealthArgs(%+v)", *p)

}

func (p *APIServiceV1CheckMgrSelfHealthArgs) DeepEqual(ano *APIServiceV1CheckMgrSelfHealthArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *APIServiceV1CheckMgrSelfHealthArgs) Field1DeepEqual(src *modelv1.CheckMgrSelfHealthReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type APIServiceV1CheckMgrSelfHealthResult struct {
	Success *modelv1.CheckMgrSelfHealthResp `thrift:"success,0,optional" frugal:"0,optional,modelv1.CheckMgrSelfHealthResp" json:"success,omitempty"`
}

func NewAPIServiceV1CheckMgrSelfHealthResult() *APIServiceV1CheckMgrSelfHealthResult {
	return &APIServiceV1CheckMgrSelfHealthResult{}
}

func (p *APIServiceV1CheckMgrSelfHealthResult) InitDefault() {
}

var APIServiceV1CheckMgrSelfHealthResult_Success_DEFAULT *modelv1.CheckMgrSelfHealthResp

func (p *APIServiceV1CheckMgrSelfHealthResult) GetSuccess() (v *modelv1.CheckMgrSelfHealthResp) {
	if !p.IsSetSuccess() {
		return APIServiceV1CheckMgrSelfHealthResult_Success_DEFAULT
	}
	return p.Success
}
func (p *APIServiceV1CheckMgrSelfHealthResult) SetSuccess(x interface{}) {
	p.Success = x.(*modelv1.CheckMgrSelfHealthResp)
}

var fieldIDToName_APIServiceV1CheckMgrSelfHealthResult = map[int16]string{
	0: "success",
}

func (p *APIServiceV1CheckMgrSelfHealthResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *APIServiceV1CheckMgrSelfHealthResult) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CheckMgrSelfHealthResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIServiceV1CheckMgrSelfHealthResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthResult) ReadField0(iprot thrift.TProtocol) error {
	_field := modelv1.NewCheckMgrSelfHealthResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *APIServiceV1CheckMgrSelfHealthResult) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("APIServiceV1CheckMgrSelfHealthResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckMgrSelfHealth_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *APIServiceV1CheckMgrSelfHealthResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIServiceV1CheckMgrSelfHealthResult(%+v)", *p)

}

func (p *APIServiceV1CheckMgrSelfHealthResult) DeepEqual(ano *APIServiceV1CheckMgrSelfHealthResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *APIServiceV1CheckMgrSelfHealthResult) Field0DeepEqual(src *modelv1.CheckMgrSelfHealthResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

// Code generated by Kitex v1.18.1. DO NOT EDIT.
package apiservicev1

import (
	apiservice "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/apiservice"
	server "code.byted.org/kite/kitex/server"
)

// NewServer creates a server.Server with the given handler and options.
func NewServer(handler apiservice.APIServiceV1, opts ...server.Option) server.Server {
	var options []server.Option

	options = append(options, opts...)
	options = append(options, server.WithCompatibleMiddlewareForUnary())

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

func RegisterService(svr server.Server, handler apiservice.APIServiceV1, opts ...server.RegisterOption) error {
	return svr.RegisterService(serviceInfo(), handler, opts...)
}

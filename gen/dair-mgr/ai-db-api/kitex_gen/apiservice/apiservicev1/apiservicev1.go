// Code generated by Kitex v1.18.1. DO NOT EDIT.

package apiservicev1

import (
	apiservice "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/apiservice"
	modelv1 "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/modelv1"
	client "code.byted.org/kite/kitex/client"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateInstance": kitex.NewMethodInfo(
		createInstanceHandler,
		newAPIServiceV1CreateInstanceArgs,
		newAPIServiceV1CreateInstanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeInstances": kitex.NewMethodInfo(
		describeInstancesH<PERSON><PERSON>,
		newAPIServiceV1DescribeInstancesArgs,
		newAPIServiceV1DescribeInstancesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeInstanceDetail": kitex.NewMethodInfo(
		describeInstanceDetailHandler,
		newAPIServiceV1DescribeInstanceDetailArgs,
		newAPIServiceV1DescribeInstanceDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteInstance": kitex.NewMethodInfo(
		deleteInstanceHandler,
		newAPIServiceV1DeleteInstanceArgs,
		newAPIServiceV1DeleteInstanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyInstanceName": kitex.NewMethodInfo(
		modifyInstanceNameHandler,
		newAPIServiceV1ModifyInstanceNameArgs,
		newAPIServiceV1ModifyInstanceNameResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyInstanceSettings": kitex.NewMethodInfo(
		modifyInstanceSettingsHandler,
		newAPIServiceV1ModifyInstanceSettingsArgs,
		newAPIServiceV1ModifyInstanceSettingsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyComputeSettings": kitex.NewMethodInfo(
		modifyComputeSettingsHandler,
		newAPIServiceV1ModifyComputeSettingsArgs,
		newAPIServiceV1ModifyComputeSettingsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateBranch": kitex.NewMethodInfo(
		createBranchHandler,
		newAPIServiceV1CreateBranchArgs,
		newAPIServiceV1CreateBranchResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeBranches": kitex.NewMethodInfo(
		describeBranchesHandler,
		newAPIServiceV1DescribeBranchesArgs,
		newAPIServiceV1DescribeBranchesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeChildBranches": kitex.NewMethodInfo(
		describeChildBranchesHandler,
		newAPIServiceV1DescribeChildBranchesArgs,
		newAPIServiceV1DescribeChildBranchesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeBranchDetail": kitex.NewMethodInfo(
		describeBranchDetailHandler,
		newAPIServiceV1DescribeBranchDetailArgs,
		newAPIServiceV1DescribeBranchDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteBranch": kitex.NewMethodInfo(
		deleteBranchHandler,
		newAPIServiceV1DeleteBranchArgs,
		newAPIServiceV1DeleteBranchResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateBranch": kitex.NewMethodInfo(
		updateBranchHandler,
		newAPIServiceV1UpdateBranchArgs,
		newAPIServiceV1UpdateBranchResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SetAsDefaultBranch": kitex.NewMethodInfo(
		setAsDefaultBranchHandler,
		newAPIServiceV1SetAsDefaultBranchArgs,
		newAPIServiceV1SetAsDefaultBranchResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ResetBranch": kitex.NewMethodInfo(
		resetBranchHandler,
		newAPIServiceV1ResetBranchArgs,
		newAPIServiceV1ResetBranchResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateCompute": kitex.NewMethodInfo(
		createComputeHandler,
		newAPIServiceV1CreateComputeArgs,
		newAPIServiceV1CreateComputeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeComputes": kitex.NewMethodInfo(
		describeComputesHandler,
		newAPIServiceV1DescribeComputesArgs,
		newAPIServiceV1DescribeComputesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeComputeDetail": kitex.NewMethodInfo(
		describeComputeDetailHandler,
		newAPIServiceV1DescribeComputeDetailArgs,
		newAPIServiceV1DescribeComputeDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteCompute": kitex.NewMethodInfo(
		deleteComputeHandler,
		newAPIServiceV1DeleteComputeArgs,
		newAPIServiceV1DeleteComputeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyComputeName": kitex.NewMethodInfo(
		modifyComputeNameHandler,
		newAPIServiceV1ModifyComputeNameArgs,
		newAPIServiceV1ModifyComputeNameResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyComputeSpec": kitex.NewMethodInfo(
		modifyComputeSpecHandler,
		newAPIServiceV1ModifyComputeSpecArgs,
		newAPIServiceV1ModifyComputeSpecResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DescribeAccountUsage": kitex.NewMethodInfo(
		describeAccountUsageHandler,
		newAPIServiceV1DescribeAccountUsageArgs,
		newAPIServiceV1DescribeAccountUsageResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckMgrSelfHealth": kitex.NewMethodInfo(
		checkMgrSelfHealthHandler,
		newAPIServiceV1CheckMgrSelfHealthArgs,
		newAPIServiceV1CheckMgrSelfHealthResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	aPIServiceV1ServiceInfo                = NewServiceInfo()
	aPIServiceV1ServiceInfoForClient       = NewServiceInfoForClient()
	aPIServiceV1ServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return aPIServiceV1ServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return aPIServiceV1ServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return aPIServiceV1ServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "APIServiceV1"
	handlerType := (*apiservice.APIServiceV1)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "apiservice",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.18.1",
		Extra:           extra,
	}
	return svcInfo
}

func createInstanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1CreateInstanceArgs)
	realResult := result.(*apiservice.APIServiceV1CreateInstanceResult)
	success, err := handler.(apiservice.APIServiceV1).CreateInstance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1CreateInstanceArgs() interface{} {
	return apiservice.NewAPIServiceV1CreateInstanceArgs()
}

func newAPIServiceV1CreateInstanceResult() interface{} {
	return apiservice.NewAPIServiceV1CreateInstanceResult()
}

func describeInstancesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeInstancesArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeInstancesResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeInstances(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeInstancesArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeInstancesArgs()
}

func newAPIServiceV1DescribeInstancesResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeInstancesResult()
}

func describeInstanceDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeInstanceDetailArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeInstanceDetailResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeInstanceDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeInstanceDetailArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeInstanceDetailArgs()
}

func newAPIServiceV1DescribeInstanceDetailResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeInstanceDetailResult()
}

func deleteInstanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DeleteInstanceArgs)
	realResult := result.(*apiservice.APIServiceV1DeleteInstanceResult)
	success, err := handler.(apiservice.APIServiceV1).DeleteInstance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DeleteInstanceArgs() interface{} {
	return apiservice.NewAPIServiceV1DeleteInstanceArgs()
}

func newAPIServiceV1DeleteInstanceResult() interface{} {
	return apiservice.NewAPIServiceV1DeleteInstanceResult()
}

func modifyInstanceNameHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ModifyInstanceNameArgs)
	realResult := result.(*apiservice.APIServiceV1ModifyInstanceNameResult)
	success, err := handler.(apiservice.APIServiceV1).ModifyInstanceName(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ModifyInstanceNameArgs() interface{} {
	return apiservice.NewAPIServiceV1ModifyInstanceNameArgs()
}

func newAPIServiceV1ModifyInstanceNameResult() interface{} {
	return apiservice.NewAPIServiceV1ModifyInstanceNameResult()
}

func modifyInstanceSettingsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ModifyInstanceSettingsArgs)
	realResult := result.(*apiservice.APIServiceV1ModifyInstanceSettingsResult)
	success, err := handler.(apiservice.APIServiceV1).ModifyInstanceSettings(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ModifyInstanceSettingsArgs() interface{} {
	return apiservice.NewAPIServiceV1ModifyInstanceSettingsArgs()
}

func newAPIServiceV1ModifyInstanceSettingsResult() interface{} {
	return apiservice.NewAPIServiceV1ModifyInstanceSettingsResult()
}

func modifyComputeSettingsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ModifyComputeSettingsArgs)
	realResult := result.(*apiservice.APIServiceV1ModifyComputeSettingsResult)
	success, err := handler.(apiservice.APIServiceV1).ModifyComputeSettings(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ModifyComputeSettingsArgs() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeSettingsArgs()
}

func newAPIServiceV1ModifyComputeSettingsResult() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeSettingsResult()
}

func createBranchHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1CreateBranchArgs)
	realResult := result.(*apiservice.APIServiceV1CreateBranchResult)
	success, err := handler.(apiservice.APIServiceV1).CreateBranch(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1CreateBranchArgs() interface{} {
	return apiservice.NewAPIServiceV1CreateBranchArgs()
}

func newAPIServiceV1CreateBranchResult() interface{} {
	return apiservice.NewAPIServiceV1CreateBranchResult()
}

func describeBranchesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeBranchesArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeBranchesResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeBranches(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeBranchesArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeBranchesArgs()
}

func newAPIServiceV1DescribeBranchesResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeBranchesResult()
}

func describeChildBranchesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeChildBranchesArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeChildBranchesResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeChildBranches(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeChildBranchesArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeChildBranchesArgs()
}

func newAPIServiceV1DescribeChildBranchesResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeChildBranchesResult()
}

func describeBranchDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeBranchDetailArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeBranchDetailResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeBranchDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeBranchDetailArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeBranchDetailArgs()
}

func newAPIServiceV1DescribeBranchDetailResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeBranchDetailResult()
}

func deleteBranchHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DeleteBranchArgs)
	realResult := result.(*apiservice.APIServiceV1DeleteBranchResult)
	success, err := handler.(apiservice.APIServiceV1).DeleteBranch(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DeleteBranchArgs() interface{} {
	return apiservice.NewAPIServiceV1DeleteBranchArgs()
}

func newAPIServiceV1DeleteBranchResult() interface{} {
	return apiservice.NewAPIServiceV1DeleteBranchResult()
}

func updateBranchHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1UpdateBranchArgs)
	realResult := result.(*apiservice.APIServiceV1UpdateBranchResult)
	success, err := handler.(apiservice.APIServiceV1).UpdateBranch(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1UpdateBranchArgs() interface{} {
	return apiservice.NewAPIServiceV1UpdateBranchArgs()
}

func newAPIServiceV1UpdateBranchResult() interface{} {
	return apiservice.NewAPIServiceV1UpdateBranchResult()
}

func setAsDefaultBranchHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1SetAsDefaultBranchArgs)
	realResult := result.(*apiservice.APIServiceV1SetAsDefaultBranchResult)
	success, err := handler.(apiservice.APIServiceV1).SetAsDefaultBranch(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1SetAsDefaultBranchArgs() interface{} {
	return apiservice.NewAPIServiceV1SetAsDefaultBranchArgs()
}

func newAPIServiceV1SetAsDefaultBranchResult() interface{} {
	return apiservice.NewAPIServiceV1SetAsDefaultBranchResult()
}

func resetBranchHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ResetBranchArgs)
	realResult := result.(*apiservice.APIServiceV1ResetBranchResult)
	success, err := handler.(apiservice.APIServiceV1).ResetBranch(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ResetBranchArgs() interface{} {
	return apiservice.NewAPIServiceV1ResetBranchArgs()
}

func newAPIServiceV1ResetBranchResult() interface{} {
	return apiservice.NewAPIServiceV1ResetBranchResult()
}

func createComputeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1CreateComputeArgs)
	realResult := result.(*apiservice.APIServiceV1CreateComputeResult)
	success, err := handler.(apiservice.APIServiceV1).CreateCompute(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1CreateComputeArgs() interface{} {
	return apiservice.NewAPIServiceV1CreateComputeArgs()
}

func newAPIServiceV1CreateComputeResult() interface{} {
	return apiservice.NewAPIServiceV1CreateComputeResult()
}

func describeComputesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeComputesArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeComputesResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeComputes(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeComputesArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeComputesArgs()
}

func newAPIServiceV1DescribeComputesResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeComputesResult()
}

func describeComputeDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeComputeDetailArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeComputeDetailResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeComputeDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeComputeDetailArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeComputeDetailArgs()
}

func newAPIServiceV1DescribeComputeDetailResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeComputeDetailResult()
}

func deleteComputeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DeleteComputeArgs)
	realResult := result.(*apiservice.APIServiceV1DeleteComputeResult)
	success, err := handler.(apiservice.APIServiceV1).DeleteCompute(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DeleteComputeArgs() interface{} {
	return apiservice.NewAPIServiceV1DeleteComputeArgs()
}

func newAPIServiceV1DeleteComputeResult() interface{} {
	return apiservice.NewAPIServiceV1DeleteComputeResult()
}

func modifyComputeNameHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ModifyComputeNameArgs)
	realResult := result.(*apiservice.APIServiceV1ModifyComputeNameResult)
	success, err := handler.(apiservice.APIServiceV1).ModifyComputeName(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ModifyComputeNameArgs() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeNameArgs()
}

func newAPIServiceV1ModifyComputeNameResult() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeNameResult()
}

func modifyComputeSpecHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1ModifyComputeSpecArgs)
	realResult := result.(*apiservice.APIServiceV1ModifyComputeSpecResult)
	success, err := handler.(apiservice.APIServiceV1).ModifyComputeSpec(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1ModifyComputeSpecArgs() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeSpecArgs()
}

func newAPIServiceV1ModifyComputeSpecResult() interface{} {
	return apiservice.NewAPIServiceV1ModifyComputeSpecResult()
}

func describeAccountUsageHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1DescribeAccountUsageArgs)
	realResult := result.(*apiservice.APIServiceV1DescribeAccountUsageResult)
	success, err := handler.(apiservice.APIServiceV1).DescribeAccountUsage(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1DescribeAccountUsageArgs() interface{} {
	return apiservice.NewAPIServiceV1DescribeAccountUsageArgs()
}

func newAPIServiceV1DescribeAccountUsageResult() interface{} {
	return apiservice.NewAPIServiceV1DescribeAccountUsageResult()
}

func checkMgrSelfHealthHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*apiservice.APIServiceV1CheckMgrSelfHealthArgs)
	realResult := result.(*apiservice.APIServiceV1CheckMgrSelfHealthResult)
	success, err := handler.(apiservice.APIServiceV1).CheckMgrSelfHealth(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAPIServiceV1CheckMgrSelfHealthArgs() interface{} {
	return apiservice.NewAPIServiceV1CheckMgrSelfHealthArgs()
}

func newAPIServiceV1CheckMgrSelfHealthResult() interface{} {
	return apiservice.NewAPIServiceV1CheckMgrSelfHealthResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateInstance(ctx context.Context, req *modelv1.CreateInstanceReq) (r *modelv1.CreateInstanceResp, err error) {
	var _args apiservice.APIServiceV1CreateInstanceArgs
	_args.Req = req
	var _result apiservice.APIServiceV1CreateInstanceResult
	if err = p.c.Call(ctx, "CreateInstance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeInstances(ctx context.Context, req *modelv1.DescribeInstancesReq) (r *modelv1.DescribeInstancesResp, err error) {
	var _args apiservice.APIServiceV1DescribeInstancesArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeInstancesResult
	if err = p.c.Call(ctx, "DescribeInstances", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeInstanceDetail(ctx context.Context, req *modelv1.DescribeInstanceDetailReq) (r *modelv1.DescribeInstanceDetailResp, err error) {
	var _args apiservice.APIServiceV1DescribeInstanceDetailArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeInstanceDetailResult
	if err = p.c.Call(ctx, "DescribeInstanceDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteInstance(ctx context.Context, req *modelv1.DeleteInstanceReq) (r *modelv1.DeleteInstanceResp, err error) {
	var _args apiservice.APIServiceV1DeleteInstanceArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DeleteInstanceResult
	if err = p.c.Call(ctx, "DeleteInstance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyInstanceName(ctx context.Context, req *modelv1.ModifyInstanceNameReq) (r *modelv1.ModifyInstanceNameResp, err error) {
	var _args apiservice.APIServiceV1ModifyInstanceNameArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ModifyInstanceNameResult
	if err = p.c.Call(ctx, "ModifyInstanceName", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyInstanceSettings(ctx context.Context, req *modelv1.ModifyInstanceSettingsReq) (r *modelv1.ModifyInstanceSettingsResp, err error) {
	var _args apiservice.APIServiceV1ModifyInstanceSettingsArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ModifyInstanceSettingsResult
	if err = p.c.Call(ctx, "ModifyInstanceSettings", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyComputeSettings(ctx context.Context, req *modelv1.ModifyComputeSettingsReq) (r *modelv1.ModifyComputeSettingsResp, err error) {
	var _args apiservice.APIServiceV1ModifyComputeSettingsArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ModifyComputeSettingsResult
	if err = p.c.Call(ctx, "ModifyComputeSettings", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateBranch(ctx context.Context, req *modelv1.CreateBranchReq) (r *modelv1.CreateBranchResp, err error) {
	var _args apiservice.APIServiceV1CreateBranchArgs
	_args.Req = req
	var _result apiservice.APIServiceV1CreateBranchResult
	if err = p.c.Call(ctx, "CreateBranch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeBranches(ctx context.Context, req *modelv1.DescribeBranchesReq) (r *modelv1.DescribeBranchesResp, err error) {
	var _args apiservice.APIServiceV1DescribeBranchesArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeBranchesResult
	if err = p.c.Call(ctx, "DescribeBranches", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeChildBranches(ctx context.Context, req *modelv1.DescribeChildBranchesReq) (r *modelv1.DescribeChildBranchesResp, err error) {
	var _args apiservice.APIServiceV1DescribeChildBranchesArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeChildBranchesResult
	if err = p.c.Call(ctx, "DescribeChildBranches", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeBranchDetail(ctx context.Context, req *modelv1.DescribeBranchDetailReq) (r *modelv1.DescribeBranchDetailResp, err error) {
	var _args apiservice.APIServiceV1DescribeBranchDetailArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeBranchDetailResult
	if err = p.c.Call(ctx, "DescribeBranchDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteBranch(ctx context.Context, req *modelv1.DeleteBranchReq) (r *modelv1.DeleteBranchResp, err error) {
	var _args apiservice.APIServiceV1DeleteBranchArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DeleteBranchResult
	if err = p.c.Call(ctx, "DeleteBranch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateBranch(ctx context.Context, req *modelv1.UpdateBranchReq) (r *modelv1.UpdateBranchResp, err error) {
	var _args apiservice.APIServiceV1UpdateBranchArgs
	_args.Req = req
	var _result apiservice.APIServiceV1UpdateBranchResult
	if err = p.c.Call(ctx, "UpdateBranch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SetAsDefaultBranch(ctx context.Context, req *modelv1.SetAsDefaultBranchReq) (r *modelv1.SetAsDefaultBranchResp, err error) {
	var _args apiservice.APIServiceV1SetAsDefaultBranchArgs
	_args.Req = req
	var _result apiservice.APIServiceV1SetAsDefaultBranchResult
	if err = p.c.Call(ctx, "SetAsDefaultBranch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ResetBranch(ctx context.Context, req *modelv1.ResetBranchReq) (r *modelv1.ResetBranchResp, err error) {
	var _args apiservice.APIServiceV1ResetBranchArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ResetBranchResult
	if err = p.c.Call(ctx, "ResetBranch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateCompute(ctx context.Context, req *modelv1.CreateComputeReq) (r *modelv1.CreateComputeResp, err error) {
	var _args apiservice.APIServiceV1CreateComputeArgs
	_args.Req = req
	var _result apiservice.APIServiceV1CreateComputeResult
	if err = p.c.Call(ctx, "CreateCompute", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeComputes(ctx context.Context, req *modelv1.DescribeComputesReq) (r *modelv1.DescribeComputesResp, err error) {
	var _args apiservice.APIServiceV1DescribeComputesArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeComputesResult
	if err = p.c.Call(ctx, "DescribeComputes", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeComputeDetail(ctx context.Context, req *modelv1.DescribeComputeDetailReq) (r *modelv1.DescribeComputeDetailResp, err error) {
	var _args apiservice.APIServiceV1DescribeComputeDetailArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeComputeDetailResult
	if err = p.c.Call(ctx, "DescribeComputeDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteCompute(ctx context.Context, req *modelv1.DeleteComputeReq) (r *modelv1.DeleteComputeResp, err error) {
	var _args apiservice.APIServiceV1DeleteComputeArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DeleteComputeResult
	if err = p.c.Call(ctx, "DeleteCompute", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyComputeName(ctx context.Context, req *modelv1.ModifyComputeNameReq) (r *modelv1.ModifyComputeNameResp, err error) {
	var _args apiservice.APIServiceV1ModifyComputeNameArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ModifyComputeNameResult
	if err = p.c.Call(ctx, "ModifyComputeName", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyComputeSpec(ctx context.Context, req *modelv1.ModifyComputeSpecReq) (r *modelv1.ModifyComputeSpecResp, err error) {
	var _args apiservice.APIServiceV1ModifyComputeSpecArgs
	_args.Req = req
	var _result apiservice.APIServiceV1ModifyComputeSpecResult
	if err = p.c.Call(ctx, "ModifyComputeSpec", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DescribeAccountUsage(ctx context.Context, req *modelv1.DescribeAccountUsageReq) (r *modelv1.DescribeAccountUsageResp, err error) {
	var _args apiservice.APIServiceV1DescribeAccountUsageArgs
	_args.Req = req
	var _result apiservice.APIServiceV1DescribeAccountUsageResult
	if err = p.c.Call(ctx, "DescribeAccountUsage", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckMgrSelfHealth(ctx context.Context, req *modelv1.CheckMgrSelfHealthReq) (r *modelv1.CheckMgrSelfHealthResp, err error) {
	var _args apiservice.APIServiceV1CheckMgrSelfHealthArgs
	_args.Req = req
	var _result apiservice.APIServiceV1CheckMgrSelfHealthResult
	if err = p.c.Call(ctx, "CheckMgrSelfHealth", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

// Code generated by Kitex v1.18.1. DO NOT EDIT.

package apiservicev1

import (
	modelv1 "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/modelv1"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateInstance(ctx context.Context, req *modelv1.CreateInstanceReq, callOptions ...callopt.Option) (r *modelv1.CreateInstanceResp, err error)
	DescribeInstances(ctx context.Context, req *modelv1.DescribeInstancesReq, callOptions ...callopt.Option) (r *modelv1.DescribeInstancesResp, err error)
	DescribeInstanceDetail(ctx context.Context, req *modelv1.DescribeInstanceDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeInstanceDetailResp, err error)
	DeleteInstance(ctx context.Context, req *modelv1.DeleteInstanceReq, callOptions ...callopt.Option) (r *modelv1.DeleteInstanceResp, err error)
	ModifyInstanceName(ctx context.Context, req *modelv1.ModifyInstanceNameReq, callOptions ...callopt.Option) (r *modelv1.ModifyInstanceNameResp, err error)
	ModifyInstanceSettings(ctx context.Context, req *modelv1.ModifyInstanceSettingsReq, callOptions ...callopt.Option) (r *modelv1.ModifyInstanceSettingsResp, err error)
	ModifyComputeSettings(ctx context.Context, req *modelv1.ModifyComputeSettingsReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeSettingsResp, err error)
	CreateBranch(ctx context.Context, req *modelv1.CreateBranchReq, callOptions ...callopt.Option) (r *modelv1.CreateBranchResp, err error)
	DescribeBranches(ctx context.Context, req *modelv1.DescribeBranchesReq, callOptions ...callopt.Option) (r *modelv1.DescribeBranchesResp, err error)
	DescribeChildBranches(ctx context.Context, req *modelv1.DescribeChildBranchesReq, callOptions ...callopt.Option) (r *modelv1.DescribeChildBranchesResp, err error)
	DescribeBranchDetail(ctx context.Context, req *modelv1.DescribeBranchDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeBranchDetailResp, err error)
	DeleteBranch(ctx context.Context, req *modelv1.DeleteBranchReq, callOptions ...callopt.Option) (r *modelv1.DeleteBranchResp, err error)
	UpdateBranch(ctx context.Context, req *modelv1.UpdateBranchReq, callOptions ...callopt.Option) (r *modelv1.UpdateBranchResp, err error)
	SetAsDefaultBranch(ctx context.Context, req *modelv1.SetAsDefaultBranchReq, callOptions ...callopt.Option) (r *modelv1.SetAsDefaultBranchResp, err error)
	ResetBranch(ctx context.Context, req *modelv1.ResetBranchReq, callOptions ...callopt.Option) (r *modelv1.ResetBranchResp, err error)
	CreateCompute(ctx context.Context, req *modelv1.CreateComputeReq, callOptions ...callopt.Option) (r *modelv1.CreateComputeResp, err error)
	DescribeComputes(ctx context.Context, req *modelv1.DescribeComputesReq, callOptions ...callopt.Option) (r *modelv1.DescribeComputesResp, err error)
	DescribeComputeDetail(ctx context.Context, req *modelv1.DescribeComputeDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeComputeDetailResp, err error)
	DeleteCompute(ctx context.Context, req *modelv1.DeleteComputeReq, callOptions ...callopt.Option) (r *modelv1.DeleteComputeResp, err error)
	ModifyComputeName(ctx context.Context, req *modelv1.ModifyComputeNameReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeNameResp, err error)
	ModifyComputeSpec(ctx context.Context, req *modelv1.ModifyComputeSpecReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeSpecResp, err error)
	DescribeAccountUsage(ctx context.Context, req *modelv1.DescribeAccountUsageReq, callOptions ...callopt.Option) (r *modelv1.DescribeAccountUsageResp, err error)
	CheckMgrSelfHealth(ctx context.Context, req *modelv1.CheckMgrSelfHealthReq, callOptions ...callopt.Option) (r *modelv1.CheckMgrSelfHealthResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kAPIServiceV1Client{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kAPIServiceV1Client struct {
	*kClient
}

func (p *kAPIServiceV1Client) CreateInstance(ctx context.Context, req *modelv1.CreateInstanceReq, callOptions ...callopt.Option) (r *modelv1.CreateInstanceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateInstance(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeInstances(ctx context.Context, req *modelv1.DescribeInstancesReq, callOptions ...callopt.Option) (r *modelv1.DescribeInstancesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeInstances(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeInstanceDetail(ctx context.Context, req *modelv1.DescribeInstanceDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeInstanceDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeInstanceDetail(ctx, req)
}

func (p *kAPIServiceV1Client) DeleteInstance(ctx context.Context, req *modelv1.DeleteInstanceReq, callOptions ...callopt.Option) (r *modelv1.DeleteInstanceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteInstance(ctx, req)
}

func (p *kAPIServiceV1Client) ModifyInstanceName(ctx context.Context, req *modelv1.ModifyInstanceNameReq, callOptions ...callopt.Option) (r *modelv1.ModifyInstanceNameResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyInstanceName(ctx, req)
}

func (p *kAPIServiceV1Client) ModifyInstanceSettings(ctx context.Context, req *modelv1.ModifyInstanceSettingsReq, callOptions ...callopt.Option) (r *modelv1.ModifyInstanceSettingsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyInstanceSettings(ctx, req)
}

func (p *kAPIServiceV1Client) ModifyComputeSettings(ctx context.Context, req *modelv1.ModifyComputeSettingsReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeSettingsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyComputeSettings(ctx, req)
}

func (p *kAPIServiceV1Client) CreateBranch(ctx context.Context, req *modelv1.CreateBranchReq, callOptions ...callopt.Option) (r *modelv1.CreateBranchResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateBranch(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeBranches(ctx context.Context, req *modelv1.DescribeBranchesReq, callOptions ...callopt.Option) (r *modelv1.DescribeBranchesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeBranches(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeChildBranches(ctx context.Context, req *modelv1.DescribeChildBranchesReq, callOptions ...callopt.Option) (r *modelv1.DescribeChildBranchesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeChildBranches(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeBranchDetail(ctx context.Context, req *modelv1.DescribeBranchDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeBranchDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeBranchDetail(ctx, req)
}

func (p *kAPIServiceV1Client) DeleteBranch(ctx context.Context, req *modelv1.DeleteBranchReq, callOptions ...callopt.Option) (r *modelv1.DeleteBranchResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteBranch(ctx, req)
}

func (p *kAPIServiceV1Client) UpdateBranch(ctx context.Context, req *modelv1.UpdateBranchReq, callOptions ...callopt.Option) (r *modelv1.UpdateBranchResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateBranch(ctx, req)
}

func (p *kAPIServiceV1Client) SetAsDefaultBranch(ctx context.Context, req *modelv1.SetAsDefaultBranchReq, callOptions ...callopt.Option) (r *modelv1.SetAsDefaultBranchResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SetAsDefaultBranch(ctx, req)
}

func (p *kAPIServiceV1Client) ResetBranch(ctx context.Context, req *modelv1.ResetBranchReq, callOptions ...callopt.Option) (r *modelv1.ResetBranchResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ResetBranch(ctx, req)
}

func (p *kAPIServiceV1Client) CreateCompute(ctx context.Context, req *modelv1.CreateComputeReq, callOptions ...callopt.Option) (r *modelv1.CreateComputeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateCompute(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeComputes(ctx context.Context, req *modelv1.DescribeComputesReq, callOptions ...callopt.Option) (r *modelv1.DescribeComputesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeComputes(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeComputeDetail(ctx context.Context, req *modelv1.DescribeComputeDetailReq, callOptions ...callopt.Option) (r *modelv1.DescribeComputeDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeComputeDetail(ctx, req)
}

func (p *kAPIServiceV1Client) DeleteCompute(ctx context.Context, req *modelv1.DeleteComputeReq, callOptions ...callopt.Option) (r *modelv1.DeleteComputeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteCompute(ctx, req)
}

func (p *kAPIServiceV1Client) ModifyComputeName(ctx context.Context, req *modelv1.ModifyComputeNameReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeNameResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyComputeName(ctx, req)
}

func (p *kAPIServiceV1Client) ModifyComputeSpec(ctx context.Context, req *modelv1.ModifyComputeSpecReq, callOptions ...callopt.Option) (r *modelv1.ModifyComputeSpecResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyComputeSpec(ctx, req)
}

func (p *kAPIServiceV1Client) DescribeAccountUsage(ctx context.Context, req *modelv1.DescribeAccountUsageReq, callOptions ...callopt.Option) (r *modelv1.DescribeAccountUsageResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DescribeAccountUsage(ctx, req)
}

func (p *kAPIServiceV1Client) CheckMgrSelfHealth(ctx context.Context, req *modelv1.CheckMgrSelfHealthReq, callOptions ...callopt.Option) (r *modelv1.CheckMgrSelfHealthResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckMgrSelfHealth(ctx, req)
}

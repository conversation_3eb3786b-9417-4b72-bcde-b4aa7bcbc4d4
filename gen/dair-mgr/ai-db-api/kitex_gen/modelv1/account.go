// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DescribeAccountUsageReq struct {
	AccountId *string `thrift:"AccountId,1,optional" frugal:"1,optional,string" json:"AccountId,omitempty"`
}

func NewDescribeAccountUsageReq() *DescribeAccountUsageReq {
	return &DescribeAccountUsageReq{}
}

func (p *DescribeAccountUsageReq) InitDefault() {
}

var DescribeAccountUsageReq_AccountId_DEFAULT string

func (p *DescribeAccountUsageReq) GetAccountId() (v string) {
	if !p.IsSetAccountId() {
		return DescribeAccountUsageReq_AccountId_DEFAULT
	}
	return *p.AccountId
}
func (p *DescribeAccountUsageReq) SetAccountId(val *string) {
	p.AccountId = val
}

var fieldIDToName_DescribeAccountUsageReq = map[int16]string{
	1: "AccountId",
}

func (p *DescribeAccountUsageReq) IsSetAccountId() bool {
	return p.AccountId != nil
}

func (p *DescribeAccountUsageReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAccountUsageReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAccountUsageReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAccountUsageReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountId = _field
	return nil
}

func (p *DescribeAccountUsageReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAccountUsageReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAccountUsageReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAccountUsageReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountId() {
		if err = oprot.WriteFieldBegin("AccountId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAccountUsageReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAccountUsageReq(%+v)", *p)

}

func (p *DescribeAccountUsageReq) DeepEqual(ano *DescribeAccountUsageReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AccountId) {
		return false
	}
	return true
}

func (p *DescribeAccountUsageReq) Field1DeepEqual(src *string) bool {

	if p.AccountId == src {
		return true
	} else if p.AccountId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountId, *src) != 0 {
		return false
	}
	return true
}

type DescribeAccountUsageResp struct {
	AccountId      string                         `thrift:"AccountId,1,required" frugal:"1,required,string" json:"AccountId"`
	AccountUsage   *AccountUsage                  `thrift:"AccountUsage,2,required" frugal:"2,required,AccountUsage" json:"AccountUsage"`
	DBEngineUsages map[DBEngineType]*AccountUsage `thrift:"DBEngineUsages,3,required" frugal:"3,required,map<DBEngineType:AccountUsage>" json:"DBEngineUsages"`
}

func NewDescribeAccountUsageResp() *DescribeAccountUsageResp {
	return &DescribeAccountUsageResp{}
}

func (p *DescribeAccountUsageResp) InitDefault() {
}

func (p *DescribeAccountUsageResp) GetAccountId() (v string) {
	return p.AccountId
}

var DescribeAccountUsageResp_AccountUsage_DEFAULT *AccountUsage

func (p *DescribeAccountUsageResp) GetAccountUsage() (v *AccountUsage) {
	if !p.IsSetAccountUsage() {
		return DescribeAccountUsageResp_AccountUsage_DEFAULT
	}
	return p.AccountUsage
}

func (p *DescribeAccountUsageResp) GetDBEngineUsages() (v map[DBEngineType]*AccountUsage) {
	return p.DBEngineUsages
}
func (p *DescribeAccountUsageResp) SetAccountId(val string) {
	p.AccountId = val
}
func (p *DescribeAccountUsageResp) SetAccountUsage(val *AccountUsage) {
	p.AccountUsage = val
}
func (p *DescribeAccountUsageResp) SetDBEngineUsages(val map[DBEngineType]*AccountUsage) {
	p.DBEngineUsages = val
}

var fieldIDToName_DescribeAccountUsageResp = map[int16]string{
	1: "AccountId",
	2: "AccountUsage",
	3: "DBEngineUsages",
}

func (p *DescribeAccountUsageResp) IsSetAccountUsage() bool {
	return p.AccountUsage != nil
}

func (p *DescribeAccountUsageResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAccountUsageResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAccountId bool = false
	var issetAccountUsage bool = false
	var issetDBEngineUsages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineUsages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAccountId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountUsage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineUsages {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAccountUsageResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAccountUsageResp[fieldId]))
}

func (p *DescribeAccountUsageResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountId = _field
	return nil
}
func (p *DescribeAccountUsageResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewAccountUsage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AccountUsage = _field
	return nil
}
func (p *DescribeAccountUsageResp) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[DBEngineType]*AccountUsage, size)
	values := make([]AccountUsage, size)
	for i := 0; i < size; i++ {
		var _key DBEngineType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = DBEngineType(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.DBEngineUsages = _field
	return nil
}

func (p *DescribeAccountUsageResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAccountUsageResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAccountUsageResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAccountUsageResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAccountUsageResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountUsage", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.AccountUsage.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAccountUsageResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineUsages", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.DBEngineUsages)); err != nil {
		return err
	}
	for k, v := range p.DBEngineUsages {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAccountUsageResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAccountUsageResp(%+v)", *p)

}

func (p *DescribeAccountUsageResp) DeepEqual(ano *DescribeAccountUsageResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AccountId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountUsage) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBEngineUsages) {
		return false
	}
	return true
}

func (p *DescribeAccountUsageResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AccountId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAccountUsageResp) Field2DeepEqual(src *AccountUsage) bool {

	if !p.AccountUsage.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeAccountUsageResp) Field3DeepEqual(src map[DBEngineType]*AccountUsage) bool {

	if len(p.DBEngineUsages) != len(src) {
		return false
	}
	for k, v := range p.DBEngineUsages {
		_src := src[k]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AccountUsage struct {
	DataSizeUsedBytes        int64  `thrift:"DataSizeUsedBytes,1,required" frugal:"1,required,i64" json:"DataSizeUsedBytes"`
	DataSizeTotalBytes       int64  `thrift:"DataSizeTotalBytes,2,required" frugal:"2,required,i64" json:"DataSizeTotalBytes"`
	ComputeTimeSeconds       int64  `thrift:"ComputeTimeSeconds,3,required" frugal:"3,required,i64" json:"ComputeTimeSeconds"`
	BranchComputeTimeSeconds int64  `thrift:"BranchComputeTimeSeconds,4,required" frugal:"4,required,i64" json:"BranchComputeTimeSeconds"`
	BranchCreatedNum         int64  `thrift:"BranchCreatedNum,5,required" frugal:"5,required,i64" json:"BranchCreatedNum"`
	StatTime                 string `thrift:"StatTime,6,required" frugal:"6,required,string" json:"StatTime"`
}

func NewAccountUsage() *AccountUsage {
	return &AccountUsage{}
}

func (p *AccountUsage) InitDefault() {
}

func (p *AccountUsage) GetDataSizeUsedBytes() (v int64) {
	return p.DataSizeUsedBytes
}

func (p *AccountUsage) GetDataSizeTotalBytes() (v int64) {
	return p.DataSizeTotalBytes
}

func (p *AccountUsage) GetComputeTimeSeconds() (v int64) {
	return p.ComputeTimeSeconds
}

func (p *AccountUsage) GetBranchComputeTimeSeconds() (v int64) {
	return p.BranchComputeTimeSeconds
}

func (p *AccountUsage) GetBranchCreatedNum() (v int64) {
	return p.BranchCreatedNum
}

func (p *AccountUsage) GetStatTime() (v string) {
	return p.StatTime
}
func (p *AccountUsage) SetDataSizeUsedBytes(val int64) {
	p.DataSizeUsedBytes = val
}
func (p *AccountUsage) SetDataSizeTotalBytes(val int64) {
	p.DataSizeTotalBytes = val
}
func (p *AccountUsage) SetComputeTimeSeconds(val int64) {
	p.ComputeTimeSeconds = val
}
func (p *AccountUsage) SetBranchComputeTimeSeconds(val int64) {
	p.BranchComputeTimeSeconds = val
}
func (p *AccountUsage) SetBranchCreatedNum(val int64) {
	p.BranchCreatedNum = val
}
func (p *AccountUsage) SetStatTime(val string) {
	p.StatTime = val
}

var fieldIDToName_AccountUsage = map[int16]string{
	1: "DataSizeUsedBytes",
	2: "DataSizeTotalBytes",
	3: "ComputeTimeSeconds",
	4: "BranchComputeTimeSeconds",
	5: "BranchCreatedNum",
	6: "StatTime",
}

func (p *AccountUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AccountUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetBranchComputeTimeSeconds bool = false
	var issetBranchCreatedNum bool = false
	var issetStatTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchComputeTimeSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchCreatedNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataSizeUsedBytes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchComputeTimeSeconds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBranchCreatedNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AccountUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AccountUsage[fieldId]))
}

func (p *AccountUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return nil
}
func (p *AccountUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return nil
}
func (p *AccountUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return nil
}
func (p *AccountUsage) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchComputeTimeSeconds = _field
	return nil
}
func (p *AccountUsage) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchCreatedNum = _field
	return nil
}
func (p *AccountUsage) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatTime = _field
	return nil
}

func (p *AccountUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AccountUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("AccountUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AccountUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeUsedBytes", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeUsedBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AccountUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeTotalBytes", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeTotalBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AccountUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeTimeSeconds", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ComputeTimeSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AccountUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchComputeTimeSeconds", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BranchComputeTimeSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AccountUsage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchCreatedNum", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BranchCreatedNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AccountUsage) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AccountUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountUsage(%+v)", *p)

}

func (p *AccountUsage) DeepEqual(ano *AccountUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataSizeUsedBytes) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataSizeTotalBytes) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeTimeSeconds) {
		return false
	}
	if !p.Field4DeepEqual(ano.BranchComputeTimeSeconds) {
		return false
	}
	if !p.Field5DeepEqual(ano.BranchCreatedNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.StatTime) {
		return false
	}
	return true
}

func (p *AccountUsage) Field1DeepEqual(src int64) bool {

	if p.DataSizeUsedBytes != src {
		return false
	}
	return true
}
func (p *AccountUsage) Field2DeepEqual(src int64) bool {

	if p.DataSizeTotalBytes != src {
		return false
	}
	return true
}
func (p *AccountUsage) Field3DeepEqual(src int64) bool {

	if p.ComputeTimeSeconds != src {
		return false
	}
	return true
}
func (p *AccountUsage) Field4DeepEqual(src int64) bool {

	if p.BranchComputeTimeSeconds != src {
		return false
	}
	return true
}
func (p *AccountUsage) Field5DeepEqual(src int64) bool {

	if p.BranchCreatedNum != src {
		return false
	}
	return true
}
func (p *AccountUsage) Field6DeepEqual(src string) bool {

	if strings.Compare(p.StatTime, src) != 0 {
		return false
	}
	return true
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *BranchSettings) IsValid() error {
	if p.Name == nil {
		return fmt.Errorf("field Name not_nil rule failed")
	}
	if len(*p.Name) < int(2) {
		return fmt.Errorf("field Name min_len rule failed, current value: %d", len(*p.Name))
	}
	if len(*p.Name) > int(64) {
		return fmt.Errorf("field Name max_len rule failed, current value: %d", len(*p.Name))
	}
	_src := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
	if ok, _ := regexp.MatchString(_src, *p.Name); !ok {
		return fmt.Errorf("field Name pattern rule failed, current value: %v", *p.Name)
	}
	if p.InitSource != nil {
		if p.InitSource.String() == "<UNSET>" {
			return fmt.Errorf("field InitSource defined_only rule failed")
		}
	}
	return nil
}
func (p *Branch) IsValid() error {
	if p.BranchUsage != nil {
		if err := p.BranchUsage.IsValid(); err != nil {
			return fmt.Errorf("field BranchUsage not valid, %w", err)
		}
	}
	if p.ParentBranch != nil {
		if err := p.ParentBranch.IsValid(); err != nil {
			return fmt.Errorf("field ParentBranch not valid, %w", err)
		}
	}
	return nil
}
func (p *BranchUsage) IsValid() error {
	return nil
}
func (p *CreateBranchReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.BranchSettings != nil {
		if err := p.BranchSettings.IsValid(); err != nil {
			return fmt.Errorf("field BranchSettings not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateBranchResp) IsValid() error {
	if p.Branch != nil {
		if err := p.Branch.IsValid(); err != nil {
			return fmt.Errorf("field Branch not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeBranchesReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.SortOrder != nil {
		if p.SortOrder.String() == "<UNSET>" {
			return fmt.Errorf("field SortOrder defined_only rule failed")
		}
	}
	return nil
}
func (p *DescribeBranchesResp) IsValid() error {
	return nil
}
func (p *DescribeChildBranchesReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.SortOrder != nil {
		if p.SortOrder.String() == "<UNSET>" {
			return fmt.Errorf("field SortOrder defined_only rule failed")
		}
	}
	return nil
}
func (p *DescribeChildBranchesResp) IsValid() error {
	return nil
}
func (p *DescribeBranchDetailReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	return nil
}
func (p *DescribeBranchDetailResp) IsValid() error {
	if p.Branch != nil {
		if err := p.Branch.IsValid(); err != nil {
			return fmt.Errorf("field Branch not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteBranchReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.BranchId == nil {
		return fmt.Errorf("field BranchId not_nil rule failed")
	}
	return nil
}
func (p *DeleteBranchResp) IsValid() error {
	return nil
}
func (p *UpdateBranchReq) IsValid() error {
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.BranchId == nil {
		return fmt.Errorf("field BranchId not_nil rule failed")
	}
	if p.Name != nil {
		if len(*p.Name) < int(2) {
			return fmt.Errorf("field Name min_len rule failed, current value: %d", len(*p.Name))
		}
		if len(*p.Name) > int(64) {
			return fmt.Errorf("field Name max_len rule failed, current value: %d", len(*p.Name))
		}
		_src := "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$"
		if ok, _ := regexp.MatchString(_src, *p.Name); !ok {
			return fmt.Errorf("field Name pattern rule failed, current value: %v", *p.Name)
		}
	}
	return nil
}
func (p *UpdateBranchResp) IsValid() error {
	if p.Branch != nil {
		if err := p.Branch.IsValid(); err != nil {
			return fmt.Errorf("field Branch not valid, %w", err)
		}
	}
	return nil
}
func (p *SetAsDefaultBranchReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.BranchId) < int(2) {
		return fmt.Errorf("field BranchId min_len rule failed, current value: %d", len(p.BranchId))
	}
	if len(p.BranchId) > int(64) {
		return fmt.Errorf("field BranchId max_len rule failed, current value: %d", len(p.BranchId))
	}
	return nil
}
func (p *SetAsDefaultBranchResp) IsValid() error {
	if p.Branch != nil {
		if err := p.Branch.IsValid(); err != nil {
			return fmt.Errorf("field Branch not valid, %w", err)
		}
	}
	return nil
}
func (p *ResetBranchReq) IsValid() error {
	if len(p.InstanceId) < int(2) {
		return fmt.Errorf("field InstanceId min_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	if len(p.BranchId) < int(2) {
		return fmt.Errorf("field BranchId min_len rule failed, current value: %d", len(p.BranchId))
	}
	if len(p.BranchId) > int(64) {
		return fmt.Errorf("field BranchId max_len rule failed, current value: %d", len(p.BranchId))
	}
	return nil
}
func (p *ResetBranchResp) IsValid() error {
	if p.Branch != nil {
		if err := p.Branch.IsValid(); err != nil {
			return fmt.Errorf("field Branch not valid, %w", err)
		}
	}
	return nil
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type Resource struct {
	Region           string  `thrift:"Region,1,required" frugal:"1,required,string" json:"Region"`
	K8sDomain        string  `thrift:"K8sDomain,2,required" frugal:"2,required,string" json:"K8sDomain"`
	NodePoolName     string  `thrift:"NodePoolName,3,required" frugal:"3,required,string" json:"NodePoolName"`
	Product          string  `thrift:"Product,4,required" frugal:"4,required,string" json:"Product"`
	ResourceID       string  `thrift:"ResourceID,5,required" frugal:"5,required,string" json:"ResourceID"`
	AccountID        int32   `thrift:"AccountID,6,required" frugal:"6,required,i32" json:"AccountID"`
	OnlyForceUpgrade *bool   `thrift:"OnlyForceUpgrade,8,optional" frugal:"8,optional,bool" json:"OnlyForceUpgrade,omitempty"`
	PackageName      *string `thrift:"PackageName,9,optional" frugal:"9,optional,string" json:"PackageName,omitempty"`
	CreateAt         *string `thrift:"CreateAt,10,optional" frugal:"10,optional,string" json:"CreateAt,omitempty"`
	AccountID64      *int64  `thrift:"AccountID64,11,optional" frugal:"11,optional,i64" json:"AccountID64,omitempty"`
	EntranceName     *string `thrift:"EntranceName,12,optional" frugal:"12,optional,string" json:"EntranceName,omitempty"`
}

func NewResource() *Resource {
	return &Resource{}
}

func (p *Resource) InitDefault() {
}

func (p *Resource) GetRegion() (v string) {
	return p.Region
}

func (p *Resource) GetK8sDomain() (v string) {
	return p.K8sDomain
}

func (p *Resource) GetNodePoolName() (v string) {
	return p.NodePoolName
}

func (p *Resource) GetProduct() (v string) {
	return p.Product
}

func (p *Resource) GetResourceID() (v string) {
	return p.ResourceID
}

func (p *Resource) GetAccountID() (v int32) {
	return p.AccountID
}

var Resource_OnlyForceUpgrade_DEFAULT bool

func (p *Resource) GetOnlyForceUpgrade() (v bool) {
	if !p.IsSetOnlyForceUpgrade() {
		return Resource_OnlyForceUpgrade_DEFAULT
	}
	return *p.OnlyForceUpgrade
}

var Resource_PackageName_DEFAULT string

func (p *Resource) GetPackageName() (v string) {
	if !p.IsSetPackageName() {
		return Resource_PackageName_DEFAULT
	}
	return *p.PackageName
}

var Resource_CreateAt_DEFAULT string

func (p *Resource) GetCreateAt() (v string) {
	if !p.IsSetCreateAt() {
		return Resource_CreateAt_DEFAULT
	}
	return *p.CreateAt
}

var Resource_AccountID64_DEFAULT int64

func (p *Resource) GetAccountID64() (v int64) {
	if !p.IsSetAccountID64() {
		return Resource_AccountID64_DEFAULT
	}
	return *p.AccountID64
}

var Resource_EntranceName_DEFAULT string

func (p *Resource) GetEntranceName() (v string) {
	if !p.IsSetEntranceName() {
		return Resource_EntranceName_DEFAULT
	}
	return *p.EntranceName
}
func (p *Resource) SetRegion(val string) {
	p.Region = val
}
func (p *Resource) SetK8sDomain(val string) {
	p.K8sDomain = val
}
func (p *Resource) SetNodePoolName(val string) {
	p.NodePoolName = val
}
func (p *Resource) SetProduct(val string) {
	p.Product = val
}
func (p *Resource) SetResourceID(val string) {
	p.ResourceID = val
}
func (p *Resource) SetAccountID(val int32) {
	p.AccountID = val
}
func (p *Resource) SetOnlyForceUpgrade(val *bool) {
	p.OnlyForceUpgrade = val
}
func (p *Resource) SetPackageName(val *string) {
	p.PackageName = val
}
func (p *Resource) SetCreateAt(val *string) {
	p.CreateAt = val
}
func (p *Resource) SetAccountID64(val *int64) {
	p.AccountID64 = val
}
func (p *Resource) SetEntranceName(val *string) {
	p.EntranceName = val
}

var fieldIDToName_Resource = map[int16]string{
	1:  "Region",
	2:  "K8sDomain",
	3:  "NodePoolName",
	4:  "Product",
	5:  "ResourceID",
	6:  "AccountID",
	8:  "OnlyForceUpgrade",
	9:  "PackageName",
	10: "CreateAt",
	11: "AccountID64",
	12: "EntranceName",
}

func (p *Resource) IsSetOnlyForceUpgrade() bool {
	return p.OnlyForceUpgrade != nil
}

func (p *Resource) IsSetPackageName() bool {
	return p.PackageName != nil
}

func (p *Resource) IsSetCreateAt() bool {
	return p.CreateAt != nil
}

func (p *Resource) IsSetAccountID64() bool {
	return p.AccountID64 != nil
}

func (p *Resource) IsSetEntranceName() bool {
	return p.EntranceName != nil
}

func (p *Resource) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Resource")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegion bool = false
	var issetK8sDomain bool = false
	var issetNodePoolName bool = false
	var issetProduct bool = false
	var issetResourceID bool = false
	var issetAccountID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetK8sDomain = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodePoolName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetProduct = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetResourceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegion {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetK8sDomain {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodePoolName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetProduct {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetResourceID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAccountID {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Resource[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Resource[fieldId]))
}

func (p *Resource) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *Resource) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.K8sDomain = _field
	return nil
}
func (p *Resource) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodePoolName = _field
	return nil
}
func (p *Resource) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Product = _field
	return nil
}
func (p *Resource) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResourceID = _field
	return nil
}
func (p *Resource) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountID = _field
	return nil
}
func (p *Resource) ReadField8(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OnlyForceUpgrade = _field
	return nil
}
func (p *Resource) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PackageName = _field
	return nil
}
func (p *Resource) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateAt = _field
	return nil
}
func (p *Resource) ReadField11(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountID64 = _field
	return nil
}
func (p *Resource) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EntranceName = _field
	return nil
}

func (p *Resource) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Resource")

	var fieldId int16
	if err = oprot.WriteStructBegin("Resource"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Resource) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Resource) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("K8sDomain", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.K8sDomain); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Resource) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodePoolName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodePoolName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Resource) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Product", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Product); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Resource) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResourceID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResourceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Resource) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountID", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Resource) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetOnlyForceUpgrade() {
		if err = oprot.WriteFieldBegin("OnlyForceUpgrade", thrift.BOOL, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.OnlyForceUpgrade); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Resource) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetPackageName() {
		if err = oprot.WriteFieldBegin("PackageName", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PackageName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Resource) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateAt() {
		if err = oprot.WriteFieldBegin("CreateAt", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Resource) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountID64() {
		if err = oprot.WriteFieldBegin("AccountID64", thrift.I64, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AccountID64); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *Resource) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetEntranceName() {
		if err = oprot.WriteFieldBegin("EntranceName", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EntranceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Resource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Resource(%+v)", *p)

}

func (p *Resource) DeepEqual(ano *Resource) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Region) {
		return false
	}
	if !p.Field2DeepEqual(ano.K8sDomain) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodePoolName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Product) {
		return false
	}
	if !p.Field5DeepEqual(ano.ResourceID) {
		return false
	}
	if !p.Field6DeepEqual(ano.AccountID) {
		return false
	}
	if !p.Field8DeepEqual(ano.OnlyForceUpgrade) {
		return false
	}
	if !p.Field9DeepEqual(ano.PackageName) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreateAt) {
		return false
	}
	if !p.Field11DeepEqual(ano.AccountID64) {
		return false
	}
	if !p.Field12DeepEqual(ano.EntranceName) {
		return false
	}
	return true
}

func (p *Resource) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field2DeepEqual(src string) bool {

	if strings.Compare(p.K8sDomain, src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field3DeepEqual(src string) bool {

	if strings.Compare(p.NodePoolName, src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Product, src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ResourceID, src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field6DeepEqual(src int32) bool {

	if p.AccountID != src {
		return false
	}
	return true
}
func (p *Resource) Field8DeepEqual(src *bool) bool {

	if p.OnlyForceUpgrade == src {
		return true
	} else if p.OnlyForceUpgrade == nil || src == nil {
		return false
	}
	if *p.OnlyForceUpgrade != *src {
		return false
	}
	return true
}
func (p *Resource) Field9DeepEqual(src *string) bool {

	if p.PackageName == src {
		return true
	} else if p.PackageName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.PackageName, *src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field10DeepEqual(src *string) bool {

	if p.CreateAt == src {
		return true
	} else if p.CreateAt == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateAt, *src) != 0 {
		return false
	}
	return true
}
func (p *Resource) Field11DeepEqual(src *int64) bool {

	if p.AccountID64 == src {
		return true
	} else if p.AccountID64 == nil || src == nil {
		return false
	}
	if *p.AccountID64 != *src {
		return false
	}
	return true
}
func (p *Resource) Field12DeepEqual(src *string) bool {

	if p.EntranceName == src {
		return true
	} else if p.EntranceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EntranceName, *src) != 0 {
		return false
	}
	return true
}

type CheckHealthInfo struct {
	CurrentVersionSetID int32                    `thrift:"CurrentVersionSetID,1,required" frugal:"1,required,i32" json:"CurrentVersionSetID"`
	CurrentVersion      string                   `thrift:"CurrentVersion,2,required" frugal:"2,required,string" json:"CurrentVersion"`
	TargetVersionSetID  int32                    `thrift:"TargetVersionSetID,3,required" frugal:"3,required,i32" json:"TargetVersionSetID"`
	TragetVersion       string                   `thrift:"TragetVersion,4,required" frugal:"4,required,string" json:"TragetVersion"`
	Resource            *Resource                `thrift:"Resource,5,required" frugal:"5,required,Resource" json:"Resource"`
	RelyResourceInfo    []*CheckRelyResourceInfo `thrift:"RelyResourceInfo,6,optional" frugal:"6,optional,list<CheckRelyResourceInfo>" json:"RelyResourceInfo,omitempty"`
}

func NewCheckHealthInfo() *CheckHealthInfo {
	return &CheckHealthInfo{}
}

func (p *CheckHealthInfo) InitDefault() {
}

func (p *CheckHealthInfo) GetCurrentVersionSetID() (v int32) {
	return p.CurrentVersionSetID
}

func (p *CheckHealthInfo) GetCurrentVersion() (v string) {
	return p.CurrentVersion
}

func (p *CheckHealthInfo) GetTargetVersionSetID() (v int32) {
	return p.TargetVersionSetID
}

func (p *CheckHealthInfo) GetTragetVersion() (v string) {
	return p.TragetVersion
}

var CheckHealthInfo_Resource_DEFAULT *Resource

func (p *CheckHealthInfo) GetResource() (v *Resource) {
	if !p.IsSetResource() {
		return CheckHealthInfo_Resource_DEFAULT
	}
	return p.Resource
}

var CheckHealthInfo_RelyResourceInfo_DEFAULT []*CheckRelyResourceInfo

func (p *CheckHealthInfo) GetRelyResourceInfo() (v []*CheckRelyResourceInfo) {
	if !p.IsSetRelyResourceInfo() {
		return CheckHealthInfo_RelyResourceInfo_DEFAULT
	}
	return p.RelyResourceInfo
}
func (p *CheckHealthInfo) SetCurrentVersionSetID(val int32) {
	p.CurrentVersionSetID = val
}
func (p *CheckHealthInfo) SetCurrentVersion(val string) {
	p.CurrentVersion = val
}
func (p *CheckHealthInfo) SetTargetVersionSetID(val int32) {
	p.TargetVersionSetID = val
}
func (p *CheckHealthInfo) SetTragetVersion(val string) {
	p.TragetVersion = val
}
func (p *CheckHealthInfo) SetResource(val *Resource) {
	p.Resource = val
}
func (p *CheckHealthInfo) SetRelyResourceInfo(val []*CheckRelyResourceInfo) {
	p.RelyResourceInfo = val
}

var fieldIDToName_CheckHealthInfo = map[int16]string{
	1: "CurrentVersionSetID",
	2: "CurrentVersion",
	3: "TargetVersionSetID",
	4: "TragetVersion",
	5: "Resource",
	6: "RelyResourceInfo",
}

func (p *CheckHealthInfo) IsSetResource() bool {
	return p.Resource != nil
}

func (p *CheckHealthInfo) IsSetRelyResourceInfo() bool {
	return p.RelyResourceInfo != nil
}

func (p *CheckHealthInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckHealthInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCurrentVersionSetID bool = false
	var issetCurrentVersion bool = false
	var issetTargetVersionSetID bool = false
	var issetTragetVersion bool = false
	var issetResource bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentVersionSetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTargetVersionSetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTragetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetResource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCurrentVersionSetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCurrentVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTargetVersionSetID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTragetVersion {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetResource {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckHealthInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckHealthInfo[fieldId]))
}

func (p *CheckHealthInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentVersionSetID = _field
	return nil
}
func (p *CheckHealthInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentVersion = _field
	return nil
}
func (p *CheckHealthInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TargetVersionSetID = _field
	return nil
}
func (p *CheckHealthInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TragetVersion = _field
	return nil
}
func (p *CheckHealthInfo) ReadField5(iprot thrift.TProtocol) error {
	_field := NewResource()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Resource = _field
	return nil
}
func (p *CheckHealthInfo) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckRelyResourceInfo, 0, size)
	values := make([]CheckRelyResourceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RelyResourceInfo = _field
	return nil
}

func (p *CheckHealthInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckHealthInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckHealthInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckHealthInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentVersionSetID", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CurrentVersionSetID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckHealthInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentVersion", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CurrentVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckHealthInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TargetVersionSetID", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TargetVersionSetID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckHealthInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TragetVersion", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TragetVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CheckHealthInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Resource", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Resource.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CheckHealthInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelyResourceInfo() {
		if err = oprot.WriteFieldBegin("RelyResourceInfo", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelyResourceInfo)); err != nil {
			return err
		}
		for _, v := range p.RelyResourceInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CheckHealthInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckHealthInfo(%+v)", *p)

}

func (p *CheckHealthInfo) DeepEqual(ano *CheckHealthInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CurrentVersionSetID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CurrentVersion) {
		return false
	}
	if !p.Field3DeepEqual(ano.TargetVersionSetID) {
		return false
	}
	if !p.Field4DeepEqual(ano.TragetVersion) {
		return false
	}
	if !p.Field5DeepEqual(ano.Resource) {
		return false
	}
	if !p.Field6DeepEqual(ano.RelyResourceInfo) {
		return false
	}
	return true
}

func (p *CheckHealthInfo) Field1DeepEqual(src int32) bool {

	if p.CurrentVersionSetID != src {
		return false
	}
	return true
}
func (p *CheckHealthInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CurrentVersion, src) != 0 {
		return false
	}
	return true
}
func (p *CheckHealthInfo) Field3DeepEqual(src int32) bool {

	if p.TargetVersionSetID != src {
		return false
	}
	return true
}
func (p *CheckHealthInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TragetVersion, src) != 0 {
		return false
	}
	return true
}
func (p *CheckHealthInfo) Field5DeepEqual(src *Resource) bool {

	if !p.Resource.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CheckHealthInfo) Field6DeepEqual(src []*CheckRelyResourceInfo) bool {

	if len(p.RelyResourceInfo) != len(src) {
		return false
	}
	for i, v := range p.RelyResourceInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CheckRelyResourceInfo struct {
	VersionSetID int32     `thrift:"VersionSetID,1,required" frugal:"1,required,i32" json:"VersionSetID"`
	Version      string    `thrift:"Version,2,required" frugal:"2,required,string" json:"Version"`
	Resource     *Resource `thrift:"Resource,3,required" frugal:"3,required,Resource" json:"Resource"`
}

func NewCheckRelyResourceInfo() *CheckRelyResourceInfo {
	return &CheckRelyResourceInfo{}
}

func (p *CheckRelyResourceInfo) InitDefault() {
}

func (p *CheckRelyResourceInfo) GetVersionSetID() (v int32) {
	return p.VersionSetID
}

func (p *CheckRelyResourceInfo) GetVersion() (v string) {
	return p.Version
}

var CheckRelyResourceInfo_Resource_DEFAULT *Resource

func (p *CheckRelyResourceInfo) GetResource() (v *Resource) {
	if !p.IsSetResource() {
		return CheckRelyResourceInfo_Resource_DEFAULT
	}
	return p.Resource
}
func (p *CheckRelyResourceInfo) SetVersionSetID(val int32) {
	p.VersionSetID = val
}
func (p *CheckRelyResourceInfo) SetVersion(val string) {
	p.Version = val
}
func (p *CheckRelyResourceInfo) SetResource(val *Resource) {
	p.Resource = val
}

var fieldIDToName_CheckRelyResourceInfo = map[int16]string{
	1: "VersionSetID",
	2: "Version",
	3: "Resource",
}

func (p *CheckRelyResourceInfo) IsSetResource() bool {
	return p.Resource != nil
}

func (p *CheckRelyResourceInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckRelyResourceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetVersionSetID bool = false
	var issetVersion bool = false
	var issetResource bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersionSetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetResource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetVersionSetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetResource {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckRelyResourceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckRelyResourceInfo[fieldId]))
}

func (p *CheckRelyResourceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionSetID = _field
	return nil
}
func (p *CheckRelyResourceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *CheckRelyResourceInfo) ReadField3(iprot thrift.TProtocol) error {
	_field := NewResource()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Resource = _field
	return nil
}

func (p *CheckRelyResourceInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckRelyResourceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckRelyResourceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckRelyResourceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VersionSetID", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.VersionSetID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckRelyResourceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckRelyResourceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Resource", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Resource.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckRelyResourceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckRelyResourceInfo(%+v)", *p)

}

func (p *CheckRelyResourceInfo) DeepEqual(ano *CheckRelyResourceInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.VersionSetID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.Resource) {
		return false
	}
	return true
}

func (p *CheckRelyResourceInfo) Field1DeepEqual(src int32) bool {

	if p.VersionSetID != src {
		return false
	}
	return true
}
func (p *CheckRelyResourceInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *CheckRelyResourceInfo) Field3DeepEqual(src *Resource) bool {

	if !p.Resource.DeepEqual(src) {
		return false
	}
	return true
}

type CheckMgrSelfHealthReq struct {
	Tag          string           `thrift:"Tag,1,required" frugal:"1,required,string" json:"Tag"`
	Info         *CheckHealthInfo `thrift:"Info,2,optional" frugal:"2,optional,CheckHealthInfo" json:"Info,omitempty"`
	UpgradeToken *string          `thrift:"UpgradeToken,3,optional" frugal:"3,optional,string" json:"UpgradeToken,omitempty"`
	Rollback     *bool            `thrift:"Rollback,4,optional" frugal:"4,optional,bool" json:"Rollback,omitempty"`
}

func NewCheckMgrSelfHealthReq() *CheckMgrSelfHealthReq {
	return &CheckMgrSelfHealthReq{}
}

func (p *CheckMgrSelfHealthReq) InitDefault() {
}

func (p *CheckMgrSelfHealthReq) GetTag() (v string) {
	return p.Tag
}

var CheckMgrSelfHealthReq_Info_DEFAULT *CheckHealthInfo

func (p *CheckMgrSelfHealthReq) GetInfo() (v *CheckHealthInfo) {
	if !p.IsSetInfo() {
		return CheckMgrSelfHealthReq_Info_DEFAULT
	}
	return p.Info
}

var CheckMgrSelfHealthReq_UpgradeToken_DEFAULT string

func (p *CheckMgrSelfHealthReq) GetUpgradeToken() (v string) {
	if !p.IsSetUpgradeToken() {
		return CheckMgrSelfHealthReq_UpgradeToken_DEFAULT
	}
	return *p.UpgradeToken
}

var CheckMgrSelfHealthReq_Rollback_DEFAULT bool

func (p *CheckMgrSelfHealthReq) GetRollback() (v bool) {
	if !p.IsSetRollback() {
		return CheckMgrSelfHealthReq_Rollback_DEFAULT
	}
	return *p.Rollback
}
func (p *CheckMgrSelfHealthReq) SetTag(val string) {
	p.Tag = val
}
func (p *CheckMgrSelfHealthReq) SetInfo(val *CheckHealthInfo) {
	p.Info = val
}
func (p *CheckMgrSelfHealthReq) SetUpgradeToken(val *string) {
	p.UpgradeToken = val
}
func (p *CheckMgrSelfHealthReq) SetRollback(val *bool) {
	p.Rollback = val
}

var fieldIDToName_CheckMgrSelfHealthReq = map[int16]string{
	1: "Tag",
	2: "Info",
	3: "UpgradeToken",
	4: "Rollback",
}

func (p *CheckMgrSelfHealthReq) IsSetInfo() bool {
	return p.Info != nil
}

func (p *CheckMgrSelfHealthReq) IsSetUpgradeToken() bool {
	return p.UpgradeToken != nil
}

func (p *CheckMgrSelfHealthReq) IsSetRollback() bool {
	return p.Rollback != nil
}

func (p *CheckMgrSelfHealthReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckMgrSelfHealthReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTag bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTag = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTag {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckMgrSelfHealthReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckMgrSelfHealthReq[fieldId]))
}

func (p *CheckMgrSelfHealthReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Tag = _field
	return nil
}
func (p *CheckMgrSelfHealthReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewCheckHealthInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Info = _field
	return nil
}
func (p *CheckMgrSelfHealthReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpgradeToken = _field
	return nil
}
func (p *CheckMgrSelfHealthReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Rollback = _field
	return nil
}

func (p *CheckMgrSelfHealthReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckMgrSelfHealthReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckMgrSelfHealthReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckMgrSelfHealthReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tag", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Tag); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckMgrSelfHealthReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInfo() {
		if err = oprot.WriteFieldBegin("Info", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Info.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckMgrSelfHealthReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpgradeToken() {
		if err = oprot.WriteFieldBegin("UpgradeToken", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UpgradeToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckMgrSelfHealthReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRollback() {
		if err = oprot.WriteFieldBegin("Rollback", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Rollback); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CheckMgrSelfHealthReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckMgrSelfHealthReq(%+v)", *p)

}

func (p *CheckMgrSelfHealthReq) DeepEqual(ano *CheckMgrSelfHealthReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Tag) {
		return false
	}
	if !p.Field2DeepEqual(ano.Info) {
		return false
	}
	if !p.Field3DeepEqual(ano.UpgradeToken) {
		return false
	}
	if !p.Field4DeepEqual(ano.Rollback) {
		return false
	}
	return true
}

func (p *CheckMgrSelfHealthReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Tag, src) != 0 {
		return false
	}
	return true
}
func (p *CheckMgrSelfHealthReq) Field2DeepEqual(src *CheckHealthInfo) bool {

	if !p.Info.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CheckMgrSelfHealthReq) Field3DeepEqual(src *string) bool {

	if p.UpgradeToken == src {
		return true
	} else if p.UpgradeToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UpgradeToken, *src) != 0 {
		return false
	}
	return true
}
func (p *CheckMgrSelfHealthReq) Field4DeepEqual(src *bool) bool {

	if p.Rollback == src {
		return true
	} else if p.Rollback == nil || src == nil {
		return false
	}
	if *p.Rollback != *src {
		return false
	}
	return true
}

type CheckMgrSelfHealthResp struct {
	Messages          []string `thrift:"Messages,1,required" frugal:"1,required,list<string>" json:"Messages"`
	Info              []string `thrift:"Info,2,optional" frugal:"2,optional,list<string>" json:"Info,omitempty"`
	AutoRetryInterval *int32   `thrift:"AutoRetryInterval,3,optional" frugal:"3,optional,i32" json:"AutoRetryInterval,omitempty"`
}

func NewCheckMgrSelfHealthResp() *CheckMgrSelfHealthResp {
	return &CheckMgrSelfHealthResp{}
}

func (p *CheckMgrSelfHealthResp) InitDefault() {
}

func (p *CheckMgrSelfHealthResp) GetMessages() (v []string) {
	return p.Messages
}

var CheckMgrSelfHealthResp_Info_DEFAULT []string

func (p *CheckMgrSelfHealthResp) GetInfo() (v []string) {
	if !p.IsSetInfo() {
		return CheckMgrSelfHealthResp_Info_DEFAULT
	}
	return p.Info
}

var CheckMgrSelfHealthResp_AutoRetryInterval_DEFAULT int32

func (p *CheckMgrSelfHealthResp) GetAutoRetryInterval() (v int32) {
	if !p.IsSetAutoRetryInterval() {
		return CheckMgrSelfHealthResp_AutoRetryInterval_DEFAULT
	}
	return *p.AutoRetryInterval
}
func (p *CheckMgrSelfHealthResp) SetMessages(val []string) {
	p.Messages = val
}
func (p *CheckMgrSelfHealthResp) SetInfo(val []string) {
	p.Info = val
}
func (p *CheckMgrSelfHealthResp) SetAutoRetryInterval(val *int32) {
	p.AutoRetryInterval = val
}

var fieldIDToName_CheckMgrSelfHealthResp = map[int16]string{
	1: "Messages",
	2: "Info",
	3: "AutoRetryInterval",
}

func (p *CheckMgrSelfHealthResp) IsSetInfo() bool {
	return p.Info != nil
}

func (p *CheckMgrSelfHealthResp) IsSetAutoRetryInterval() bool {
	return p.AutoRetryInterval != nil
}

func (p *CheckMgrSelfHealthResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckMgrSelfHealthResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessages {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckMgrSelfHealthResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckMgrSelfHealthResp[fieldId]))
}

func (p *CheckMgrSelfHealthResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *CheckMgrSelfHealthResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Info = _field
	return nil
}
func (p *CheckMgrSelfHealthResp) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoRetryInterval = _field
	return nil
}

func (p *CheckMgrSelfHealthResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckMgrSelfHealthResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckMgrSelfHealthResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckMgrSelfHealthResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckMgrSelfHealthResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInfo() {
		if err = oprot.WriteFieldBegin("Info", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Info)); err != nil {
			return err
		}
		for _, v := range p.Info {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckMgrSelfHealthResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoRetryInterval() {
		if err = oprot.WriteFieldBegin("AutoRetryInterval", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.AutoRetryInterval); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckMgrSelfHealthResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckMgrSelfHealthResp(%+v)", *p)

}

func (p *CheckMgrSelfHealthResp) DeepEqual(ano *CheckMgrSelfHealthResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Messages) {
		return false
	}
	if !p.Field2DeepEqual(ano.Info) {
		return false
	}
	if !p.Field3DeepEqual(ano.AutoRetryInterval) {
		return false
	}
	return true
}

func (p *CheckMgrSelfHealthResp) Field1DeepEqual(src []string) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CheckMgrSelfHealthResp) Field2DeepEqual(src []string) bool {

	if len(p.Info) != len(src) {
		return false
	}
	for i, v := range p.Info {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CheckMgrSelfHealthResp) Field3DeepEqual(src *int32) bool {

	if p.AutoRetryInterval == src {
		return true
	} else if p.AutoRetryInterval == nil || src == nil {
		return false
	}
	if *p.AutoRetryInterval != *src {
		return false
	}
	return true
}

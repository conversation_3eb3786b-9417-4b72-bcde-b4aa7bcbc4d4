// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	"fmt"
)

type Action int64

const (
	Action_CreateInstance          Action = 0
	Action_DescribeInstances       Action = 1
	Action_DescribeInstancesDetail Action = 2
	Action_DeleteInstance          Action = 3
	Action_ModifyInstanceName      Action = 4
	Action_ModifyInstanceSettings  Action = 5
	Action_ModifyComputeSettings   Action = 6
	Action_CreateBranch            Action = 7
	Action_DescribeBranches        Action = 8
	Action_DescribeBranchDetail    Action = 9
	Action_DeleteBranch            Action = 10
	Action_DescribeChildBranches   Action = 11
	Action_UpdateBranch            Action = 12
	Action_SetAsDefaultBranch      Action = 13
	Action_ResetBranch             Action = 14
	Action_CreateCompute           Action = 15
	Action_DescribeComputes        Action = 16
	Action_DescribeComputeDetail   Action = 17
	Action_DeleteCompute           Action = 18
	Action_ModifyComputeName       Action = 19
	Action_ModifyComputeSpec       Action = 20
	Action_DescribeAccountUsage    Action = 21
)

func (p Action) String() string {
	switch p {
	case Action_CreateInstance:
		return "CreateInstance"
	case Action_DescribeInstances:
		return "DescribeInstances"
	case Action_DescribeInstancesDetail:
		return "DescribeInstancesDetail"
	case Action_DeleteInstance:
		return "DeleteInstance"
	case Action_ModifyInstanceName:
		return "ModifyInstanceName"
	case Action_ModifyInstanceSettings:
		return "ModifyInstanceSettings"
	case Action_ModifyComputeSettings:
		return "ModifyComputeSettings"
	case Action_CreateBranch:
		return "CreateBranch"
	case Action_DescribeBranches:
		return "DescribeBranches"
	case Action_DescribeBranchDetail:
		return "DescribeBranchDetail"
	case Action_DeleteBranch:
		return "DeleteBranch"
	case Action_DescribeChildBranches:
		return "DescribeChildBranches"
	case Action_UpdateBranch:
		return "UpdateBranch"
	case Action_SetAsDefaultBranch:
		return "SetAsDefaultBranch"
	case Action_ResetBranch:
		return "ResetBranch"
	case Action_CreateCompute:
		return "CreateCompute"
	case Action_DescribeComputes:
		return "DescribeComputes"
	case Action_DescribeComputeDetail:
		return "DescribeComputeDetail"
	case Action_DeleteCompute:
		return "DeleteCompute"
	case Action_ModifyComputeName:
		return "ModifyComputeName"
	case Action_ModifyComputeSpec:
		return "ModifyComputeSpec"
	case Action_DescribeAccountUsage:
		return "DescribeAccountUsage"
	}
	return "<UNSET>"
}

func ActionFromString(s string) (Action, error) {
	switch s {
	case "CreateInstance":
		return Action_CreateInstance, nil
	case "DescribeInstances":
		return Action_DescribeInstances, nil
	case "DescribeInstancesDetail":
		return Action_DescribeInstancesDetail, nil
	case "DeleteInstance":
		return Action_DeleteInstance, nil
	case "ModifyInstanceName":
		return Action_ModifyInstanceName, nil
	case "ModifyInstanceSettings":
		return Action_ModifyInstanceSettings, nil
	case "ModifyComputeSettings":
		return Action_ModifyComputeSettings, nil
	case "CreateBranch":
		return Action_CreateBranch, nil
	case "DescribeBranches":
		return Action_DescribeBranches, nil
	case "DescribeBranchDetail":
		return Action_DescribeBranchDetail, nil
	case "DeleteBranch":
		return Action_DeleteBranch, nil
	case "DescribeChildBranches":
		return Action_DescribeChildBranches, nil
	case "UpdateBranch":
		return Action_UpdateBranch, nil
	case "SetAsDefaultBranch":
		return Action_SetAsDefaultBranch, nil
	case "ResetBranch":
		return Action_ResetBranch, nil
	case "CreateCompute":
		return Action_CreateCompute, nil
	case "DescribeComputes":
		return Action_DescribeComputes, nil
	case "DescribeComputeDetail":
		return Action_DescribeComputeDetail, nil
	case "DeleteCompute":
		return Action_DeleteCompute, nil
	case "ModifyComputeName":
		return Action_ModifyComputeName, nil
	case "ModifyComputeSpec":
		return Action_ModifyComputeSpec, nil
	case "DescribeAccountUsage":
		return Action_DescribeAccountUsage, nil
	}
	return Action(0), fmt.Errorf("not a valid Action string")
}

func ActionPtr(v Action) *Action { return &v }

func (p Action) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *Action) UnmarshalText(text []byte) error {
	q, err := ActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

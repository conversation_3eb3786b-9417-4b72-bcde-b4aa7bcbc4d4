// Code generated by Kitex v1.18.1. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *DescribeAccountUsageReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAccountUsageReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeAccountUsageReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.AccountId = _field
	return offset, nil
}

func (p *DescribeAccountUsageReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeAccountUsageReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeAccountUsageReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeAccountUsageReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAccountId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.AccountId)
	}
	return offset
}

func (p *DescribeAccountUsageReq) field1Length() int {
	l := 0
	if p.IsSetAccountId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.AccountId)
	}
	return l
}

func (p *DescribeAccountUsageReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeAccountUsageReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.AccountId != nil {
		var tmp string
		if *src.AccountId != "" {
			tmp = kutils.StringDeepCopy(*src.AccountId)
		}
		p.AccountId = &tmp
	}

	return nil
}

func (p *DescribeAccountUsageResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAccountId bool = false
	var issetAccountUsage bool = false
	var issetDBEngineUsages bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAccountId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAccountUsage = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDBEngineUsages = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetAccountId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountUsage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineUsages {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAccountUsageResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeAccountUsageResp[fieldId]))
}

func (p *DescribeAccountUsageResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AccountId = _field
	return offset, nil
}

func (p *DescribeAccountUsageResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewAccountUsage()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.AccountUsage = _field
	return offset, nil
}

func (p *DescribeAccountUsageResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[DBEngineType]*AccountUsage, size)
	values := make([]AccountUsage, size)
	for i := 0; i < size; i++ {
		var _key DBEngineType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = DBEngineType(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.DBEngineUsages = _field
	return offset, nil
}

func (p *DescribeAccountUsageResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeAccountUsageResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeAccountUsageResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeAccountUsageResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AccountId)
	return offset
}

func (p *DescribeAccountUsageResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.AccountUsage.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DescribeAccountUsageResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 3)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.DBEngineUsages {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRUCT, length)
	return offset
}

func (p *DescribeAccountUsageResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AccountId)
	return l
}

func (p *DescribeAccountUsageResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.AccountUsage.BLength()
	return l
}

func (p *DescribeAccountUsageResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.DBEngineUsages {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += v.BLength()
	}
	return l
}

func (p *DescribeAccountUsageResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeAccountUsageResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.AccountId != "" {
		p.AccountId = kutils.StringDeepCopy(src.AccountId)
	}

	var _accountUsage *AccountUsage
	if src.AccountUsage != nil {
		_accountUsage = &AccountUsage{}
		if err := _accountUsage.DeepCopy(src.AccountUsage); err != nil {
			return err
		}
	}
	p.AccountUsage = _accountUsage

	if src.DBEngineUsages != nil {
		p.DBEngineUsages = make(map[DBEngineType]*AccountUsage, len(src.DBEngineUsages))
		for key, val := range src.DBEngineUsages {
			var _key DBEngineType
			_key = key

			var _val *AccountUsage
			if val != nil {
				_val = &AccountUsage{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.DBEngineUsages[_key] = _val
		}
	}

	return nil
}

func (p *AccountUsage) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetBranchComputeTimeSeconds bool = false
	var issetBranchCreatedNum bool = false
	var issetStatTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchComputeTimeSeconds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchCreatedNum = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetDataSizeUsedBytes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchComputeTimeSeconds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBranchCreatedNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AccountUsage[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_AccountUsage[fieldId]))
}

func (p *AccountUsage) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return offset, nil
}

func (p *AccountUsage) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return offset, nil
}

func (p *AccountUsage) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return offset, nil
}

func (p *AccountUsage) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchComputeTimeSeconds = _field
	return offset, nil
}

func (p *AccountUsage) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchCreatedNum = _field
	return offset, nil
}

func (p *AccountUsage) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StatTime = _field
	return offset, nil
}

func (p *AccountUsage) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AccountUsage) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AccountUsage) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AccountUsage) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeUsedBytes)
	return offset
}

func (p *AccountUsage) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeTotalBytes)
	return offset
}

func (p *AccountUsage) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ComputeTimeSeconds)
	return offset
}

func (p *AccountUsage) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.BranchComputeTimeSeconds)
	return offset
}

func (p *AccountUsage) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 5)
	offset += thrift.Binary.WriteI64(buf[offset:], p.BranchCreatedNum)
	return offset
}

func (p *AccountUsage) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StatTime)
	return offset
}

func (p *AccountUsage) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *AccountUsage) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *AccountUsage) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *AccountUsage) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *AccountUsage) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *AccountUsage) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StatTime)
	return l
}

func (p *AccountUsage) DeepCopy(s interface{}) error {
	src, ok := s.(*AccountUsage)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.DataSizeUsedBytes = src.DataSizeUsedBytes

	p.DataSizeTotalBytes = src.DataSizeTotalBytes

	p.ComputeTimeSeconds = src.ComputeTimeSeconds

	p.BranchComputeTimeSeconds = src.BranchComputeTimeSeconds

	p.BranchCreatedNum = src.BranchCreatedNum

	if src.StatTime != "" {
		p.StatTime = kutils.StringDeepCopy(src.StatTime)
	}

	return nil
}

// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *Resource) IsValid() error {
	return nil
}
func (p *CheckHealthInfo) IsValid() error {
	if p.Resource != nil {
		if err := p.Resource.IsValid(); err != nil {
			return fmt.Errorf("field Resource not valid, %w", err)
		}
	}
	return nil
}
func (p *CheckRelyResourceInfo) IsValid() error {
	if p.Resource != nil {
		if err := p.Resource.IsValid(); err != nil {
			return fmt.Errorf("field Resource not valid, %w", err)
		}
	}
	return nil
}
func (p *CheckMgrSelfHealthReq) IsValid() error {
	if p.Info != nil {
		if err := p.Info.IsValid(); err != nil {
			return fmt.Errorf("field Info not valid, %w", err)
		}
	}
	return nil
}
func (p *CheckMgrSelfHealthResp) IsValid() error {
	return nil
}

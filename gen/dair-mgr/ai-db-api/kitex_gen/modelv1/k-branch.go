// Code generated by Kitex v1.18.1. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *BranchSettings) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BranchSettings[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *BranchSettings) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Name = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ParentId = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ParentLSN = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ParentTime = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Protected = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Archived = _field
	return offset, nil
}

func (p *BranchSettings) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *BranchInitSource
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := BranchInitSource(v)
		_field = &tmp
	}
	p.InitSource = _field
	return offset, nil
}

func (p *BranchSettings) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BranchSettings) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BranchSettings) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BranchSettings) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Name)
	}
	return offset
}

func (p *BranchSettings) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParentId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ParentId)
	}
	return offset
}

func (p *BranchSettings) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParentLSN() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ParentLSN)
	}
	return offset
}

func (p *BranchSettings) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParentTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ParentTime)
	}
	return offset
}

func (p *BranchSettings) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProtected() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 5)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.Protected)
	}
	return offset
}

func (p *BranchSettings) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetArchived() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 6)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.Archived)
	}
	return offset
}

func (p *BranchSettings) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInitSource() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.InitSource))
	}
	return offset
}

func (p *BranchSettings) field1Length() int {
	l := 0
	if p.IsSetName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Name)
	}
	return l
}

func (p *BranchSettings) field2Length() int {
	l := 0
	if p.IsSetParentId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ParentId)
	}
	return l
}

func (p *BranchSettings) field3Length() int {
	l := 0
	if p.IsSetParentLSN() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ParentLSN)
	}
	return l
}

func (p *BranchSettings) field4Length() int {
	l := 0
	if p.IsSetParentTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ParentTime)
	}
	return l
}

func (p *BranchSettings) field5Length() int {
	l := 0
	if p.IsSetProtected() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *BranchSettings) field6Length() int {
	l := 0
	if p.IsSetArchived() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *BranchSettings) field7Length() int {
	l := 0
	if p.IsSetInitSource() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *BranchSettings) DeepCopy(s interface{}) error {
	src, ok := s.(*BranchSettings)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Name != nil {
		var tmp string
		if *src.Name != "" {
			tmp = kutils.StringDeepCopy(*src.Name)
		}
		p.Name = &tmp
	}

	if src.ParentId != nil {
		var tmp string
		if *src.ParentId != "" {
			tmp = kutils.StringDeepCopy(*src.ParentId)
		}
		p.ParentId = &tmp
	}

	if src.ParentLSN != nil {
		var tmp string
		if *src.ParentLSN != "" {
			tmp = kutils.StringDeepCopy(*src.ParentLSN)
		}
		p.ParentLSN = &tmp
	}

	if src.ParentTime != nil {
		var tmp string
		if *src.ParentTime != "" {
			tmp = kutils.StringDeepCopy(*src.ParentTime)
		}
		p.ParentTime = &tmp
	}

	if src.Protected != nil {
		tmp := *src.Protected
		p.Protected = &tmp
	}

	if src.Archived != nil {
		tmp := *src.Archived
		p.Archived = &tmp
	}

	if src.InitSource != nil {
		tmp := *src.InitSource
		p.InitSource = &tmp
	}

	return nil
}

func (p *Branch) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetBranchName bool = false
	var issetBranchStatus bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetStatusChangedTime bool = false
	var issetLastResetTime bool = false
	var issetCreationSource bool = false
	var issetDefault bool = false
	var issetProtected bool = false
	var issetArchived bool = false
	var issetBranchUsage bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatusChangedTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLastResetTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreationSource = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDefault = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProtected = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetArchived = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchUsage = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBranchName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchStatus {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetStatusChangedTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetLastResetTime {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetCreationSource {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetDefault {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetProtected {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetArchived {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetBranchUsage {
		fieldId = 15
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Branch[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_Branch[fieldId]))
}

func (p *Branch) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *Branch) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *Branch) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchName = _field
	return offset, nil
}

func (p *Branch) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StartParentLSN = _field
	return offset, nil
}

func (p *Branch) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StartParentTime = _field
	return offset, nil
}

func (p *Branch) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field BranchStatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = BranchStatus(v)
	}
	p.BranchStatus = _field
	return offset, nil
}

func (p *Branch) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *Branch) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *Branch) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StatusChangedTime = _field
	return offset, nil
}

func (p *Branch) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LastResetTime = _field
	return offset, nil
}

func (p *Branch) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field CreationSource
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = CreationSource(v)
	}
	p.CreationSource = _field
	return offset, nil
}

func (p *Branch) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Default = _field
	return offset, nil
}

func (p *Branch) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Protected = _field
	return offset, nil
}

func (p *Branch) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Archived = _field
	return offset, nil
}

func (p *Branch) FastReadField15(buf []byte) (int, error) {
	offset := 0
	_field := NewBranchUsage()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BranchUsage = _field
	return offset, nil
}

func (p *Branch) FastReadField16(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ParentBranch = _field
	return offset, nil
}

func (p *Branch) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Branch) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Branch) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Branch) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *Branch) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *Branch) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchName)
	return offset
}

func (p *Branch) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStartParentLSN() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.StartParentLSN)
	}
	return offset
}

func (p *Branch) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStartParentTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.StartParentTime)
	}
	return offset
}

func (p *Branch) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.BranchStatus))
	return offset
}

func (p *Branch) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreateTime)
	return offset
}

func (p *Branch) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UpdateTime)
	return offset
}

func (p *Branch) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StatusChangedTime)
	return offset
}

func (p *Branch) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LastResetTime)
	return offset
}

func (p *Branch) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.CreationSource))
	return offset
}

func (p *Branch) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 12)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Default)
	return offset
}

func (p *Branch) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 13)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Protected)
	return offset
}

func (p *Branch) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 14)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Archived)
	return offset
}

func (p *Branch) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 15)
	offset += p.BranchUsage.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *Branch) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParentBranch() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 16)
		offset += p.ParentBranch.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *Branch) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *Branch) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *Branch) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchName)
	return l
}

func (p *Branch) field4Length() int {
	l := 0
	if p.IsSetStartParentLSN() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.StartParentLSN)
	}
	return l
}

func (p *Branch) field5Length() int {
	l := 0
	if p.IsSetStartParentTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.StartParentTime)
	}
	return l
}

func (p *Branch) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Branch) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreateTime)
	return l
}

func (p *Branch) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UpdateTime)
	return l
}

func (p *Branch) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StatusChangedTime)
	return l
}

func (p *Branch) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LastResetTime)
	return l
}

func (p *Branch) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Branch) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *Branch) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *Branch) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *Branch) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BranchUsage.BLength()
	return l
}

func (p *Branch) field16Length() int {
	l := 0
	if p.IsSetParentBranch() {
		l += thrift.Binary.FieldBeginLength()
		l += p.ParentBranch.BLength()
	}
	return l
}

func (p *Branch) DeepCopy(s interface{}) error {
	src, ok := s.(*Branch)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	if src.BranchName != "" {
		p.BranchName = kutils.StringDeepCopy(src.BranchName)
	}

	if src.StartParentLSN != nil {
		var tmp string
		if *src.StartParentLSN != "" {
			tmp = kutils.StringDeepCopy(*src.StartParentLSN)
		}
		p.StartParentLSN = &tmp
	}

	if src.StartParentTime != nil {
		var tmp string
		if *src.StartParentTime != "" {
			tmp = kutils.StringDeepCopy(*src.StartParentTime)
		}
		p.StartParentTime = &tmp
	}

	p.BranchStatus = src.BranchStatus

	if src.CreateTime != "" {
		p.CreateTime = kutils.StringDeepCopy(src.CreateTime)
	}

	if src.UpdateTime != "" {
		p.UpdateTime = kutils.StringDeepCopy(src.UpdateTime)
	}

	if src.StatusChangedTime != "" {
		p.StatusChangedTime = kutils.StringDeepCopy(src.StatusChangedTime)
	}

	if src.LastResetTime != "" {
		p.LastResetTime = kutils.StringDeepCopy(src.LastResetTime)
	}

	p.CreationSource = src.CreationSource

	p.Default = src.Default

	p.Protected = src.Protected

	p.Archived = src.Archived

	var _branchUsage *BranchUsage
	if src.BranchUsage != nil {
		_branchUsage = &BranchUsage{}
		if err := _branchUsage.DeepCopy(src.BranchUsage); err != nil {
			return err
		}
	}
	p.BranchUsage = _branchUsage

	var _parentBranch *Branch
	if src.ParentBranch != nil {
		_parentBranch = &Branch{}
		if err := _parentBranch.DeepCopy(src.ParentBranch); err != nil {
			return err
		}
	}
	p.ParentBranch = _parentBranch

	return nil
}

func (p *BranchUsage) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetStatTime bool = false
	var issetLastRunningTime bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetLastRunningTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeUsedBytes {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetLastRunningTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BranchUsage[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_BranchUsage[fieldId]))
}

func (p *BranchUsage) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.StatTime = _field
	return offset, nil
}

func (p *BranchUsage) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LastRunningTime = _field
	return offset, nil
}

func (p *BranchUsage) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BranchUsage) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BranchUsage) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BranchUsage) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *BranchUsage) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *BranchUsage) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeUsedBytes)
	return offset
}

func (p *BranchUsage) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DataSizeTotalBytes)
	return offset
}

func (p *BranchUsage) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 5)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ComputeTimeSeconds)
	return offset
}

func (p *BranchUsage) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.StatTime)
	return offset
}

func (p *BranchUsage) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LastRunningTime)
	return offset
}

func (p *BranchUsage) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *BranchUsage) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *BranchUsage) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BranchUsage) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BranchUsage) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BranchUsage) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.StatTime)
	return l
}

func (p *BranchUsage) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LastRunningTime)
	return l
}

func (p *BranchUsage) DeepCopy(s interface{}) error {
	src, ok := s.(*BranchUsage)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	p.DataSizeUsedBytes = src.DataSizeUsedBytes

	p.DataSizeTotalBytes = src.DataSizeTotalBytes

	p.ComputeTimeSeconds = src.ComputeTimeSeconds

	if src.StatTime != "" {
		p.StatTime = kutils.StringDeepCopy(src.StatTime)
	}

	if src.LastRunningTime != "" {
		p.LastRunningTime = kutils.StringDeepCopy(src.LastRunningTime)
	}

	return nil
}

func (p *CreateBranchReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBranchReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CreateBranchReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateBranchReq) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewBranchSettings()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BranchSettings = _field
	return offset, nil
}

func (p *CreateBranchReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ComputeSettings, 0, size)
	values := make([]ComputeSettings, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ComputeSettings = _field
	return offset, nil
}

func (p *CreateBranchReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateBranchReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateBranchReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateBranchReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *CreateBranchReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBranchSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
		offset += p.BranchSettings.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateBranchReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetComputeSettings() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.ComputeSettings {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *CreateBranchReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *CreateBranchReq) field2Length() int {
	l := 0
	if p.IsSetBranchSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BranchSettings.BLength()
	}
	return l
}

func (p *CreateBranchReq) field3Length() int {
	l := 0
	if p.IsSetComputeSettings() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.ComputeSettings {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *CreateBranchReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateBranchReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	var _branchSettings *BranchSettings
	if src.BranchSettings != nil {
		_branchSettings = &BranchSettings{}
		if err := _branchSettings.DeepCopy(src.BranchSettings); err != nil {
			return err
		}
	}
	p.BranchSettings = _branchSettings

	if src.ComputeSettings != nil {
		p.ComputeSettings = make([]*ComputeSettings, 0, len(src.ComputeSettings))
		for _, elem := range src.ComputeSettings {
			var _elem *ComputeSettings
			if elem != nil {
				_elem = &ComputeSettings{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ComputeSettings = append(p.ComputeSettings, _elem)
		}
	}

	return nil
}

func (p *CreateBranchResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	var issetBranch bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBranch {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBranchResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateBranchResp[fieldId]))
}

func (p *CreateBranchResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *CreateBranchResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *CreateBranchResp) FastReadField3(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Branch = _field
	return offset, nil
}

func (p *CreateBranchResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateBranchResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateBranchResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateBranchResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *CreateBranchResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *CreateBranchResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 3)
	offset += p.Branch.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateBranchResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *CreateBranchResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *CreateBranchResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Branch.BLength()
	return l
}

func (p *CreateBranchResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateBranchResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	var _branch *Branch
	if src.Branch != nil {
		_branch = &Branch{}
		if err := _branch.DeepCopy(src.Branch); err != nil {
			return err
		}
	}
	p.Branch = _branch

	return nil
}

func (p *DescribeBranchesReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchesReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DescribeBranchesReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Search = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *SortOrder
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Offset = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Limit = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*QueryFilter, 0, size)
	values := make([]QueryFilter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Filters = _field
	return offset, nil
}

func (p *DescribeBranchesReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBranchesReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBranchesReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBranchesReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSearch() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Search)
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SortBy)
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortOrder() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortOrder))
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOffset() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Offset)
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLimit() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Limit)
	}
	return offset
}

func (p *DescribeBranchesReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFilters() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 7)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.Filters {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *DescribeBranchesReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeBranchesReq) field2Length() int {
	l := 0
	if p.IsSetSearch() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Search)
	}
	return l
}

func (p *DescribeBranchesReq) field3Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SortBy)
	}
	return l
}

func (p *DescribeBranchesReq) field4Length() int {
	l := 0
	if p.IsSetSortOrder() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBranchesReq) field5Length() int {
	l := 0
	if p.IsSetOffset() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBranchesReq) field6Length() int {
	l := 0
	if p.IsSetLimit() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeBranchesReq) field7Length() int {
	l := 0
	if p.IsSetFilters() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.Filters {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *DescribeBranchesReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBranchesReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.Search != nil {
		var tmp string
		if *src.Search != "" {
			tmp = kutils.StringDeepCopy(*src.Search)
		}
		p.Search = &tmp
	}

	if src.SortBy != nil {
		var tmp string
		if *src.SortBy != "" {
			tmp = kutils.StringDeepCopy(*src.SortBy)
		}
		p.SortBy = &tmp
	}

	if src.SortOrder != nil {
		tmp := *src.SortOrder
		p.SortOrder = &tmp
	}

	if src.Offset != nil {
		tmp := *src.Offset
		p.Offset = &tmp
	}

	if src.Limit != nil {
		tmp := *src.Limit
		p.Limit = &tmp
	}

	if src.Filters != nil {
		p.Filters = make([]*QueryFilter, 0, len(src.Filters))
		for _, elem := range src.Filters {
			var _elem *QueryFilter
			if elem != nil {
				_elem = &QueryFilter{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Filters = append(p.Filters, _elem)
		}
	}

	return nil
}

func (p *DescribeBranchesResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBranches bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranches = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranches {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchesResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBranchesResp[fieldId]))
}

func (p *DescribeBranchesResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeBranchesResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Branch, 0, size)
	values := make([]Branch, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Branches = _field
	return offset, nil
}

func (p *DescribeBranchesResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBranchesResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBranchesResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBranchesResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeBranchesResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Branches {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeBranchesResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeBranchesResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Branches {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeBranchesResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBranchesResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.Branches != nil {
		p.Branches = make([]*Branch, 0, len(src.Branches))
		for _, elem := range src.Branches {
			var _elem *Branch
			if elem != nil {
				_elem = &Branch{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Branches = append(p.Branches, _elem)
		}
	}

	return nil
}

func (p *DescribeChildBranchesReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetParentBranchId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetParentBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetParentBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChildBranchesReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeChildBranchesReq[fieldId]))
}

func (p *DescribeChildBranchesReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ParentBranchId = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SortBy = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *SortOrder
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Offset = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Limit = _field
	return offset, nil
}

func (p *DescribeChildBranchesReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeChildBranchesReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeChildBranchesReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeChildBranchesReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeChildBranchesReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ParentBranchId)
	return offset
}

func (p *DescribeChildBranchesReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortBy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SortBy)
	}
	return offset
}

func (p *DescribeChildBranchesReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSortOrder() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.SortOrder))
	}
	return offset
}

func (p *DescribeChildBranchesReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOffset() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Offset)
	}
	return offset
}

func (p *DescribeChildBranchesReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLimit() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Limit)
	}
	return offset
}

func (p *DescribeChildBranchesReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeChildBranchesReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ParentBranchId)
	return l
}

func (p *DescribeChildBranchesReq) field3Length() int {
	l := 0
	if p.IsSetSortBy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SortBy)
	}
	return l
}

func (p *DescribeChildBranchesReq) field4Length() int {
	l := 0
	if p.IsSetSortOrder() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeChildBranchesReq) field5Length() int {
	l := 0
	if p.IsSetOffset() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeChildBranchesReq) field6Length() int {
	l := 0
	if p.IsSetLimit() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *DescribeChildBranchesReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeChildBranchesReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.ParentBranchId != "" {
		p.ParentBranchId = kutils.StringDeepCopy(src.ParentBranchId)
	}

	if src.SortBy != nil {
		var tmp string
		if *src.SortBy != "" {
			tmp = kutils.StringDeepCopy(*src.SortBy)
		}
		p.SortBy = &tmp
	}

	if src.SortOrder != nil {
		tmp := *src.SortOrder
		p.SortOrder = &tmp
	}

	if src.Offset != nil {
		tmp := *src.Offset
		p.Offset = &tmp
	}

	if src.Limit != nil {
		tmp := *src.Limit
		p.Limit = &tmp
	}

	return nil
}

func (p *DescribeChildBranchesResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetBranches bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranches = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranches {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeChildBranchesResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeChildBranchesResp[fieldId]))
}

func (p *DescribeChildBranchesResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *DescribeChildBranchesResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Branch, 0, size)
	values := make([]Branch, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Branches = _field
	return offset, nil
}

func (p *DescribeChildBranchesResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeChildBranchesResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeChildBranchesResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeChildBranchesResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Total)
	return offset
}

func (p *DescribeChildBranchesResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Branches {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *DescribeChildBranchesResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *DescribeChildBranchesResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Branches {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *DescribeChildBranchesResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeChildBranchesResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Total = src.Total

	if src.Branches != nil {
		p.Branches = make([]*Branch, 0, len(src.Branches))
		for _, elem := range src.Branches {
			var _elem *Branch
			if elem != nil {
				_elem = &Branch{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Branches = append(p.Branches, _elem)
		}
	}

	return nil
}

func (p *DescribeBranchDetailReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranchId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchDetailReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBranchDetailReq[fieldId]))
}

func (p *DescribeBranchDetailReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DescribeBranchDetailReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *DescribeBranchDetailReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBranchDetailReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBranchDetailReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBranchDetailReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DescribeBranchDetailReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *DescribeBranchDetailReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DescribeBranchDetailReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *DescribeBranchDetailReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBranchDetailReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	return nil
}

func (p *DescribeBranchDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBranch {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBranchDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DescribeBranchDetailResp[fieldId]))
}

func (p *DescribeBranchDetailResp) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Branch = _field
	return offset, nil
}

func (p *DescribeBranchDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DescribeBranchDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DescribeBranchDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DescribeBranchDetailResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.Branch.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DescribeBranchDetailResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Branch.BLength()
	return l
}

func (p *DescribeBranchDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DescribeBranchDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _branch *Branch
	if src.Branch != nil {
		_branch = &Branch{}
		if err := _branch.DeepCopy(src.Branch); err != nil {
			return err
		}
	}
	p.Branch = _branch

	return nil
}

func (p *DeleteBranchReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBranchReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteBranchReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteBranchReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *DeleteBranchReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteBranchReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteBranchReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteBranchReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *DeleteBranchReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBranchId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BranchId)
	}
	return offset
}

func (p *DeleteBranchReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *DeleteBranchReq) field2Length() int {
	l := 0
	if p.IsSetBranchId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BranchId)
	}
	return l
}

func (p *DeleteBranchReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteBranchReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.BranchId != nil {
		var tmp string
		if *src.BranchId != "" {
			tmp = kutils.StringDeepCopy(*src.BranchId)
		}
		p.BranchId = &tmp
	}

	return nil
}

func (p *DeleteBranchResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteBranchResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteBranchResp[fieldId]))
}

func (p *DeleteBranchResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *DeleteBranchResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *DeleteBranchResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteBranchResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteBranchResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteBranchResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *DeleteBranchResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *DeleteBranchResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *DeleteBranchResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *DeleteBranchResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteBranchResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	return nil
}

func (p *UpdateBranchReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBranchReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpdateBranchReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *UpdateBranchReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *UpdateBranchReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Name = _field
	return offset, nil
}

func (p *UpdateBranchReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Protected = _field
	return offset, nil
}

func (p *UpdateBranchReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateBranchReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateBranchReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateBranchReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetInstanceId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.InstanceId)
	}
	return offset
}

func (p *UpdateBranchReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBranchId() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BranchId)
	}
	return offset
}

func (p *UpdateBranchReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Name)
	}
	return offset
}

func (p *UpdateBranchReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetProtected() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 4)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.Protected)
	}
	return offset
}

func (p *UpdateBranchReq) field1Length() int {
	l := 0
	if p.IsSetInstanceId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.InstanceId)
	}
	return l
}

func (p *UpdateBranchReq) field2Length() int {
	l := 0
	if p.IsSetBranchId() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BranchId)
	}
	return l
}

func (p *UpdateBranchReq) field3Length() int {
	l := 0
	if p.IsSetName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Name)
	}
	return l
}

func (p *UpdateBranchReq) field4Length() int {
	l := 0
	if p.IsSetProtected() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *UpdateBranchReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateBranchReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != nil {
		var tmp string
		if *src.InstanceId != "" {
			tmp = kutils.StringDeepCopy(*src.InstanceId)
		}
		p.InstanceId = &tmp
	}

	if src.BranchId != nil {
		var tmp string
		if *src.BranchId != "" {
			tmp = kutils.StringDeepCopy(*src.BranchId)
		}
		p.BranchId = &tmp
	}

	if src.Name != nil {
		var tmp string
		if *src.Name != "" {
			tmp = kutils.StringDeepCopy(*src.Name)
		}
		p.Name = &tmp
	}

	if src.Protected != nil {
		tmp := *src.Protected
		p.Protected = &tmp
	}

	return nil
}

func (p *UpdateBranchResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBranchResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpdateBranchResp[fieldId]))
}

func (p *UpdateBranchResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Branch = _field
	return offset, nil
}

func (p *UpdateBranchResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateBranchResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateBranchResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateBranchResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Branch.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateBranchResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Branch.BLength()
	return l
}

func (p *UpdateBranchResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateBranchResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _branch *Branch
	if src.Branch != nil {
		_branch = &Branch{}
		if err := _branch.DeepCopy(src.Branch); err != nil {
			return err
		}
	}
	p.Branch = _branch

	return nil
}

func (p *SetAsDefaultBranchReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SetAsDefaultBranchReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SetAsDefaultBranchReq[fieldId]))
}

func (p *SetAsDefaultBranchReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *SetAsDefaultBranchReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *SetAsDefaultBranchReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SetAsDefaultBranchReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SetAsDefaultBranchReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SetAsDefaultBranchReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *SetAsDefaultBranchReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *SetAsDefaultBranchReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *SetAsDefaultBranchReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *SetAsDefaultBranchReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SetAsDefaultBranchReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	return nil
}

func (p *SetAsDefaultBranchResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SetAsDefaultBranchResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_SetAsDefaultBranchResp[fieldId]))
}

func (p *SetAsDefaultBranchResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Branch = _field
	return offset, nil
}

func (p *SetAsDefaultBranchResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SetAsDefaultBranchResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SetAsDefaultBranchResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SetAsDefaultBranchResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Branch.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SetAsDefaultBranchResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Branch.BLength()
	return l
}

func (p *SetAsDefaultBranchResp) DeepCopy(s interface{}) error {
	src, ok := s.(*SetAsDefaultBranchResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _branch *Branch
	if src.Branch != nil {
		_branch = &Branch{}
		if err := _branch.DeepCopy(src.Branch); err != nil {
			return err
		}
	}
	p.Branch = _branch

	return nil
}

func (p *ResetBranchReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetBranchId bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranchId = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBranchId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetBranchReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ResetBranchReq[fieldId]))
}

func (p *ResetBranchReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InstanceId = _field
	return offset, nil
}

func (p *ResetBranchReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BranchId = _field
	return offset, nil
}

func (p *ResetBranchReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ResetBranchReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ResetBranchReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ResetBranchReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InstanceId)
	return offset
}

func (p *ResetBranchReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BranchId)
	return offset
}

func (p *ResetBranchReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InstanceId)
	return l
}

func (p *ResetBranchReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BranchId)
	return l
}

func (p *ResetBranchReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ResetBranchReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InstanceId != "" {
		p.InstanceId = kutils.StringDeepCopy(src.InstanceId)
	}

	if src.BranchId != "" {
		p.BranchId = kutils.StringDeepCopy(src.BranchId)
	}

	return nil
}

func (p *ResetBranchResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBranch bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBranch = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBranch {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResetBranchResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ResetBranchResp[fieldId]))
}

func (p *ResetBranchResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBranch()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Branch = _field
	return offset, nil
}

func (p *ResetBranchResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ResetBranchResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ResetBranchResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ResetBranchResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Branch.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ResetBranchResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Branch.BLength()
	return l
}

func (p *ResetBranchResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ResetBranchResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _branch *Branch
	if src.Branch != nil {
		_branch = &Branch{}
		if err := _branch.DeepCopy(src.Branch); err != nil {
			return err
		}
	}
	p.Branch = _branch

	return nil
}

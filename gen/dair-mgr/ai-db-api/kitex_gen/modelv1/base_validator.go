// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *Tag) IsValid() error {
	return nil
}
func (p *QueryFilter) IsValid() error {
	return nil
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type InstanceStatus int64

const (
	InstanceStatus_Unknown      InstanceStatus = 0
	InstanceStatus_Creating     InstanceStatus = 1
	InstanceStatus_CreateFailed InstanceStatus = 2
	InstanceStatus_Updating     InstanceStatus = 3
	InstanceStatus_Running      InstanceStatus = 4
	InstanceStatus_Deleted      InstanceStatus = 5
)

func (p InstanceStatus) String() string {
	switch p {
	case InstanceStatus_Unknown:
		return "Unknown"
	case InstanceStatus_Creating:
		return "Creating"
	case InstanceStatus_CreateFailed:
		return "CreateFailed"
	case InstanceStatus_Updating:
		return "Updating"
	case InstanceStatus_Running:
		return "Running"
	case InstanceStatus_Deleted:
		return "Deleted"
	}
	return "<UNSET>"
}

func InstanceStatusFromString(s string) (InstanceStatus, error) {
	switch s {
	case "Unknown":
		return InstanceStatus_Unknown, nil
	case "Creating":
		return InstanceStatus_Creating, nil
	case "CreateFailed":
		return InstanceStatus_CreateFailed, nil
	case "Updating":
		return InstanceStatus_Updating, nil
	case "Running":
		return InstanceStatus_Running, nil
	case "Deleted":
		return InstanceStatus_Deleted, nil
	}
	return InstanceStatus(0), fmt.Errorf("not a valid InstanceStatus string")
}

func InstanceStatusPtr(v InstanceStatus) *InstanceStatus { return &v }

func (p InstanceStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InstanceStatus) UnmarshalText(text []byte) error {
	q, err := InstanceStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type Instance struct {
	InstanceId           string                   `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceName         string                   `thrift:"InstanceName,2,required" frugal:"2,required,string" json:"InstanceName"`
	RegionId             string                   `thrift:"RegionId,3,required" frugal:"3,required,string" json:"RegionId"`
	ZoneIds              []string                 `thrift:"ZoneIds,4,required" frugal:"4,required,list<string>" json:"ZoneIds"`
	ProjectId            string                   `thrift:"ProjectId,5,required" frugal:"5,required,string" json:"ProjectId"`
	AccountId            string                   `thrift:"AccountId,6,required" frugal:"6,required,string" json:"AccountId"`
	DBEngineType         DBEngineType             `thrift:"DBEngineType,7,required" frugal:"7,required,DBEngineType" json:"DBEngineType"`
	DBEngineVersion      DBEngineVersion          `thrift:"DBEngineVersion,8,required" frugal:"8,required,DBEngineVersion" json:"DBEngineVersion"`
	DBEngineMinorVersion DBEngineMinorVersion     `thrift:"DBEngineMinorVersion,9,required" frugal:"9,required,DBEngineMinorVersion" json:"DBEngineMinorVersion"`
	InstanceSetting      *DefaultInstanceSettings `thrift:"InstanceSetting,10,required" frugal:"10,required,DefaultInstanceSettings" json:"InstanceSetting"`
	ComputeSettings      *ComputeSettings         `thrift:"ComputeSettings,11,required" frugal:"11,required,ComputeSettings" json:"ComputeSettings"`
	StorageType          StorageType              `thrift:"StorageType,12,required" frugal:"12,required,StorageType" json:"StorageType"`
	StorageSize          int32                    `thrift:"StorageSize,13,required" frugal:"13,required,i32" json:"StorageSize"`
	InstanceUsage        *InstanceUsage           `thrift:"InstanceUsage,14,required" frugal:"14,required,InstanceUsage" json:"InstanceUsage"`
	InstanceStatus       InstanceStatus           `thrift:"InstanceStatus,15,required" frugal:"15,required,InstanceStatus" json:"InstanceStatus"`
	CreationSource       CreationSource           `thrift:"CreationSource,16,required" frugal:"16,required,CreationSource" json:"CreationSource"`
	InstanceTags         []*Tag                   `thrift:"InstanceTags,17,required" frugal:"17,required,list<Tag>" json:"InstanceTags"`
	StatusChangedTime    string                   `thrift:"StatusChangedTime,18,required" frugal:"18,required,string" json:"StatusChangedTime"`
	CreateTime           string                   `thrift:"CreateTime,19,required" frugal:"19,required,string" json:"CreateTime"`
	UpdateTime           string                   `thrift:"UpdateTime,20,required" frugal:"20,required,string" json:"UpdateTime"`
	LastActiveTime       string                   `thrift:"LastActiveTime,21,required" frugal:"21,required,string" json:"LastActiveTime"`
}

func NewInstance() *Instance {
	return &Instance{}
}

func (p *Instance) InitDefault() {
}

func (p *Instance) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *Instance) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *Instance) GetRegionId() (v string) {
	return p.RegionId
}

func (p *Instance) GetZoneIds() (v []string) {
	return p.ZoneIds
}

func (p *Instance) GetProjectId() (v string) {
	return p.ProjectId
}

func (p *Instance) GetAccountId() (v string) {
	return p.AccountId
}

func (p *Instance) GetDBEngineType() (v DBEngineType) {
	return p.DBEngineType
}

func (p *Instance) GetDBEngineVersion() (v DBEngineVersion) {
	return p.DBEngineVersion
}

func (p *Instance) GetDBEngineMinorVersion() (v DBEngineMinorVersion) {
	return p.DBEngineMinorVersion
}

var Instance_InstanceSetting_DEFAULT *DefaultInstanceSettings

func (p *Instance) GetInstanceSetting() (v *DefaultInstanceSettings) {
	if !p.IsSetInstanceSetting() {
		return Instance_InstanceSetting_DEFAULT
	}
	return p.InstanceSetting
}

var Instance_ComputeSettings_DEFAULT *ComputeSettings

func (p *Instance) GetComputeSettings() (v *ComputeSettings) {
	if !p.IsSetComputeSettings() {
		return Instance_ComputeSettings_DEFAULT
	}
	return p.ComputeSettings
}

func (p *Instance) GetStorageType() (v StorageType) {
	return p.StorageType
}

func (p *Instance) GetStorageSize() (v int32) {
	return p.StorageSize
}

var Instance_InstanceUsage_DEFAULT *InstanceUsage

func (p *Instance) GetInstanceUsage() (v *InstanceUsage) {
	if !p.IsSetInstanceUsage() {
		return Instance_InstanceUsage_DEFAULT
	}
	return p.InstanceUsage
}

func (p *Instance) GetInstanceStatus() (v InstanceStatus) {
	return p.InstanceStatus
}

func (p *Instance) GetCreationSource() (v CreationSource) {
	return p.CreationSource
}

func (p *Instance) GetInstanceTags() (v []*Tag) {
	return p.InstanceTags
}

func (p *Instance) GetStatusChangedTime() (v string) {
	return p.StatusChangedTime
}

func (p *Instance) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *Instance) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *Instance) GetLastActiveTime() (v string) {
	return p.LastActiveTime
}
func (p *Instance) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *Instance) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *Instance) SetRegionId(val string) {
	p.RegionId = val
}
func (p *Instance) SetZoneIds(val []string) {
	p.ZoneIds = val
}
func (p *Instance) SetProjectId(val string) {
	p.ProjectId = val
}
func (p *Instance) SetAccountId(val string) {
	p.AccountId = val
}
func (p *Instance) SetDBEngineType(val DBEngineType) {
	p.DBEngineType = val
}
func (p *Instance) SetDBEngineVersion(val DBEngineVersion) {
	p.DBEngineVersion = val
}
func (p *Instance) SetDBEngineMinorVersion(val DBEngineMinorVersion) {
	p.DBEngineMinorVersion = val
}
func (p *Instance) SetInstanceSetting(val *DefaultInstanceSettings) {
	p.InstanceSetting = val
}
func (p *Instance) SetComputeSettings(val *ComputeSettings) {
	p.ComputeSettings = val
}
func (p *Instance) SetStorageType(val StorageType) {
	p.StorageType = val
}
func (p *Instance) SetStorageSize(val int32) {
	p.StorageSize = val
}
func (p *Instance) SetInstanceUsage(val *InstanceUsage) {
	p.InstanceUsage = val
}
func (p *Instance) SetInstanceStatus(val InstanceStatus) {
	p.InstanceStatus = val
}
func (p *Instance) SetCreationSource(val CreationSource) {
	p.CreationSource = val
}
func (p *Instance) SetInstanceTags(val []*Tag) {
	p.InstanceTags = val
}
func (p *Instance) SetStatusChangedTime(val string) {
	p.StatusChangedTime = val
}
func (p *Instance) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *Instance) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *Instance) SetLastActiveTime(val string) {
	p.LastActiveTime = val
}

var fieldIDToName_Instance = map[int16]string{
	1:  "InstanceId",
	2:  "InstanceName",
	3:  "RegionId",
	4:  "ZoneIds",
	5:  "ProjectId",
	6:  "AccountId",
	7:  "DBEngineType",
	8:  "DBEngineVersion",
	9:  "DBEngineMinorVersion",
	10: "InstanceSetting",
	11: "ComputeSettings",
	12: "StorageType",
	13: "StorageSize",
	14: "InstanceUsage",
	15: "InstanceStatus",
	16: "CreationSource",
	17: "InstanceTags",
	18: "StatusChangedTime",
	19: "CreateTime",
	20: "UpdateTime",
	21: "LastActiveTime",
}

func (p *Instance) IsSetInstanceSetting() bool {
	return p.InstanceSetting != nil
}

func (p *Instance) IsSetComputeSettings() bool {
	return p.ComputeSettings != nil
}

func (p *Instance) IsSetInstanceUsage() bool {
	return p.InstanceUsage != nil
}

func (p *Instance) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Instance")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetRegionId bool = false
	var issetZoneIds bool = false
	var issetProjectId bool = false
	var issetAccountId bool = false
	var issetDBEngineType bool = false
	var issetDBEngineVersion bool = false
	var issetDBEngineMinorVersion bool = false
	var issetInstanceSetting bool = false
	var issetComputeSettings bool = false
	var issetStorageType bool = false
	var issetStorageSize bool = false
	var issetInstanceUsage bool = false
	var issetInstanceStatus bool = false
	var issetCreationSource bool = false
	var issetInstanceTags bool = false
	var issetStatusChangedTime bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetLastActiveTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetZoneIds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBEngineMinorVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceSetting = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeSettings = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetStorageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreationSource = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceTags = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatusChangedTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastActiveTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetZoneIds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetProjectId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAccountId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineVersion {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetDBEngineMinorVersion {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetInstanceSetting {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetComputeSettings {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetStorageType {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetStorageSize {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetInstanceUsage {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetInstanceStatus {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetCreationSource {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetInstanceTags {
		fieldId = 17
		goto RequiredFieldNotSetError
	}

	if !issetStatusChangedTime {
		fieldId = 18
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 19
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 20
		goto RequiredFieldNotSetError
	}

	if !issetLastActiveTime {
		fieldId = 21
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Instance[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Instance[fieldId]))
}

func (p *Instance) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *Instance) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *Instance) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *Instance) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ZoneIds = _field
	return nil
}
func (p *Instance) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectId = _field
	return nil
}
func (p *Instance) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountId = _field
	return nil
}
func (p *Instance) ReadField7(iprot thrift.TProtocol) error {

	var _field DBEngineType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBEngineType(v)
	}
	p.DBEngineType = _field
	return nil
}
func (p *Instance) ReadField8(iprot thrift.TProtocol) error {

	var _field DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBEngineVersion(v)
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *Instance) ReadField9(iprot thrift.TProtocol) error {

	var _field DBEngineMinorVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DBEngineMinorVersion(v)
	}
	p.DBEngineMinorVersion = _field
	return nil
}
func (p *Instance) ReadField10(iprot thrift.TProtocol) error {
	_field := NewDefaultInstanceSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceSetting = _field
	return nil
}
func (p *Instance) ReadField11(iprot thrift.TProtocol) error {
	_field := NewComputeSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ComputeSettings = _field
	return nil
}
func (p *Instance) ReadField12(iprot thrift.TProtocol) error {

	var _field StorageType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StorageType(v)
	}
	p.StorageType = _field
	return nil
}
func (p *Instance) ReadField13(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StorageSize = _field
	return nil
}
func (p *Instance) ReadField14(iprot thrift.TProtocol) error {
	_field := NewInstanceUsage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceUsage = _field
	return nil
}
func (p *Instance) ReadField15(iprot thrift.TProtocol) error {

	var _field InstanceStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceStatus(v)
	}
	p.InstanceStatus = _field
	return nil
}
func (p *Instance) ReadField16(iprot thrift.TProtocol) error {

	var _field CreationSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CreationSource(v)
	}
	p.CreationSource = _field
	return nil
}
func (p *Instance) ReadField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Tag, 0, size)
	values := make([]Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceTags = _field
	return nil
}
func (p *Instance) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatusChangedTime = _field
	return nil
}
func (p *Instance) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *Instance) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *Instance) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastActiveTime = _field
	return nil
}

func (p *Instance) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Instance")

	var fieldId int16
	if err = oprot.WriteStructBegin("Instance"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Instance) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Instance) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Instance) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Instance) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ZoneIds", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ZoneIds)); err != nil {
		return err
	}
	for _, v := range p.ZoneIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Instance) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Instance) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountId", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Instance) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineType", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DBEngineType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Instance) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DBEngineVersion)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Instance) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBEngineMinorVersion", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DBEngineMinorVersion)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Instance) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceSetting", thrift.STRUCT, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InstanceSetting.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Instance) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeSettings", thrift.STRUCT, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ComputeSettings.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *Instance) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageType", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.StorageType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Instance) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StorageSize", thrift.I32, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StorageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Instance) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceUsage", thrift.STRUCT, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InstanceUsage.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *Instance) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceStatus", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *Instance) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreationSource", thrift.I32, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CreationSource)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *Instance) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceTags", thrift.LIST, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceTags)); err != nil {
		return err
	}
	for _, v := range p.InstanceTags {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *Instance) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatusChangedTime", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatusChangedTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *Instance) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *Instance) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *Instance) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastActiveTime", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastActiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *Instance) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Instance(%+v)", *p)

}

func (p *Instance) DeepEqual(ano *Instance) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.ZoneIds) {
		return false
	}
	if !p.Field5DeepEqual(ano.ProjectId) {
		return false
	}
	if !p.Field6DeepEqual(ano.AccountId) {
		return false
	}
	if !p.Field7DeepEqual(ano.DBEngineType) {
		return false
	}
	if !p.Field8DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field9DeepEqual(ano.DBEngineMinorVersion) {
		return false
	}
	if !p.Field10DeepEqual(ano.InstanceSetting) {
		return false
	}
	if !p.Field11DeepEqual(ano.ComputeSettings) {
		return false
	}
	if !p.Field12DeepEqual(ano.StorageType) {
		return false
	}
	if !p.Field13DeepEqual(ano.StorageSize) {
		return false
	}
	if !p.Field14DeepEqual(ano.InstanceUsage) {
		return false
	}
	if !p.Field15DeepEqual(ano.InstanceStatus) {
		return false
	}
	if !p.Field16DeepEqual(ano.CreationSource) {
		return false
	}
	if !p.Field17DeepEqual(ano.InstanceTags) {
		return false
	}
	if !p.Field18DeepEqual(ano.StatusChangedTime) {
		return false
	}
	if !p.Field19DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field20DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field21DeepEqual(ano.LastActiveTime) {
		return false
	}
	return true
}

func (p *Instance) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field4DeepEqual(src []string) bool {

	if len(p.ZoneIds) != len(src) {
		return false
	}
	for i, v := range p.ZoneIds {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *Instance) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ProjectId, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field6DeepEqual(src string) bool {

	if strings.Compare(p.AccountId, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field7DeepEqual(src DBEngineType) bool {

	if p.DBEngineType != src {
		return false
	}
	return true
}
func (p *Instance) Field8DeepEqual(src DBEngineVersion) bool {

	if p.DBEngineVersion != src {
		return false
	}
	return true
}
func (p *Instance) Field9DeepEqual(src DBEngineMinorVersion) bool {

	if p.DBEngineMinorVersion != src {
		return false
	}
	return true
}
func (p *Instance) Field10DeepEqual(src *DefaultInstanceSettings) bool {

	if !p.InstanceSetting.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Instance) Field11DeepEqual(src *ComputeSettings) bool {

	if !p.ComputeSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Instance) Field12DeepEqual(src StorageType) bool {

	if p.StorageType != src {
		return false
	}
	return true
}
func (p *Instance) Field13DeepEqual(src int32) bool {

	if p.StorageSize != src {
		return false
	}
	return true
}
func (p *Instance) Field14DeepEqual(src *InstanceUsage) bool {

	if !p.InstanceUsage.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Instance) Field15DeepEqual(src InstanceStatus) bool {

	if p.InstanceStatus != src {
		return false
	}
	return true
}
func (p *Instance) Field16DeepEqual(src CreationSource) bool {

	if p.CreationSource != src {
		return false
	}
	return true
}
func (p *Instance) Field17DeepEqual(src []*Tag) bool {

	if len(p.InstanceTags) != len(src) {
		return false
	}
	for i, v := range p.InstanceTags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Instance) Field18DeepEqual(src string) bool {

	if strings.Compare(p.StatusChangedTime, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field19DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field20DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Instance) Field21DeepEqual(src string) bool {

	if strings.Compare(p.LastActiveTime, src) != 0 {
		return false
	}
	return true
}

type DefaultInstanceSettings struct {
	HistoryRetentionHours *int32 `thrift:"HistoryRetentionHours,1,optional" frugal:"1,optional,i32" json:"HistoryRetentionHours,omitempty"`
	DeletionProtection    *bool  `thrift:"DeletionProtection,2,optional" frugal:"2,optional,bool" json:"DeletionProtection,omitempty"`
}

func NewDefaultInstanceSettings() *DefaultInstanceSettings {
	return &DefaultInstanceSettings{}
}

func (p *DefaultInstanceSettings) InitDefault() {
}

var DefaultInstanceSettings_HistoryRetentionHours_DEFAULT int32

func (p *DefaultInstanceSettings) GetHistoryRetentionHours() (v int32) {
	if !p.IsSetHistoryRetentionHours() {
		return DefaultInstanceSettings_HistoryRetentionHours_DEFAULT
	}
	return *p.HistoryRetentionHours
}

var DefaultInstanceSettings_DeletionProtection_DEFAULT bool

func (p *DefaultInstanceSettings) GetDeletionProtection() (v bool) {
	if !p.IsSetDeletionProtection() {
		return DefaultInstanceSettings_DeletionProtection_DEFAULT
	}
	return *p.DeletionProtection
}
func (p *DefaultInstanceSettings) SetHistoryRetentionHours(val *int32) {
	p.HistoryRetentionHours = val
}
func (p *DefaultInstanceSettings) SetDeletionProtection(val *bool) {
	p.DeletionProtection = val
}

var fieldIDToName_DefaultInstanceSettings = map[int16]string{
	1: "HistoryRetentionHours",
	2: "DeletionProtection",
}

func (p *DefaultInstanceSettings) IsSetHistoryRetentionHours() bool {
	return p.HistoryRetentionHours != nil
}

func (p *DefaultInstanceSettings) IsSetDeletionProtection() bool {
	return p.DeletionProtection != nil
}

func (p *DefaultInstanceSettings) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultInstanceSettings")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultInstanceSettings[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DefaultInstanceSettings) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HistoryRetentionHours = _field
	return nil
}
func (p *DefaultInstanceSettings) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DeletionProtection = _field
	return nil
}

func (p *DefaultInstanceSettings) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultInstanceSettings")

	var fieldId int16
	if err = oprot.WriteStructBegin("DefaultInstanceSettings"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DefaultInstanceSettings) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetHistoryRetentionHours() {
		if err = oprot.WriteFieldBegin("HistoryRetentionHours", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HistoryRetentionHours); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DefaultInstanceSettings) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeletionProtection() {
		if err = oprot.WriteFieldBegin("DeletionProtection", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DeletionProtection); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DefaultInstanceSettings) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DefaultInstanceSettings(%+v)", *p)

}

func (p *DefaultInstanceSettings) DeepEqual(ano *DefaultInstanceSettings) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HistoryRetentionHours) {
		return false
	}
	if !p.Field2DeepEqual(ano.DeletionProtection) {
		return false
	}
	return true
}

func (p *DefaultInstanceSettings) Field1DeepEqual(src *int32) bool {

	if p.HistoryRetentionHours == src {
		return true
	} else if p.HistoryRetentionHours == nil || src == nil {
		return false
	}
	if *p.HistoryRetentionHours != *src {
		return false
	}
	return true
}
func (p *DefaultInstanceSettings) Field2DeepEqual(src *bool) bool {

	if p.DeletionProtection == src {
		return true
	} else if p.DeletionProtection == nil || src == nil {
		return false
	}
	if *p.DeletionProtection != *src {
		return false
	}
	return true
}

type DefaultComputeSettings struct {
	AutoScalingLimitMinCU *float64          `thrift:"AutoScalingLimitMinCU,2,optional" frugal:"2,optional,double" json:"AutoScalingLimitMinCU,omitempty"`
	AutoScalingLimitMaxCU *float64          `thrift:"AutoScalingLimitMaxCU,3,optional" frugal:"3,optional,double" json:"AutoScalingLimitMaxCU,omitempty"`
	SuspendTimeoutSeconds *int32            `thrift:"SuspendTimeoutSeconds,4,optional" frugal:"4,optional,i32" json:"SuspendTimeoutSeconds,omitempty"`
	ComputeSpecCode       *string           `thrift:"ComputeSpecCode,5,optional" frugal:"5,optional,string" json:"ComputeSpecCode,omitempty"`
	ZoneId                *string           `thrift:"ZoneId,6,optional" frugal:"6,optional,string" json:"ZoneId,omitempty"`
	DBEngineParams        map[string]string `thrift:"DBEngineParams,7,optional" frugal:"7,optional,map<string:string>" json:"DBEngineParams,omitempty"`
	DBProxyParams         map[string]string `thrift:"DBProxyParams,8,optional" frugal:"8,optional,map<string:string>" json:"DBProxyParams,omitempty"`
}

func NewDefaultComputeSettings() *DefaultComputeSettings {
	return &DefaultComputeSettings{}
}

func (p *DefaultComputeSettings) InitDefault() {
}

var DefaultComputeSettings_AutoScalingLimitMinCU_DEFAULT float64

func (p *DefaultComputeSettings) GetAutoScalingLimitMinCU() (v float64) {
	if !p.IsSetAutoScalingLimitMinCU() {
		return DefaultComputeSettings_AutoScalingLimitMinCU_DEFAULT
	}
	return *p.AutoScalingLimitMinCU
}

var DefaultComputeSettings_AutoScalingLimitMaxCU_DEFAULT float64

func (p *DefaultComputeSettings) GetAutoScalingLimitMaxCU() (v float64) {
	if !p.IsSetAutoScalingLimitMaxCU() {
		return DefaultComputeSettings_AutoScalingLimitMaxCU_DEFAULT
	}
	return *p.AutoScalingLimitMaxCU
}

var DefaultComputeSettings_SuspendTimeoutSeconds_DEFAULT int32

func (p *DefaultComputeSettings) GetSuspendTimeoutSeconds() (v int32) {
	if !p.IsSetSuspendTimeoutSeconds() {
		return DefaultComputeSettings_SuspendTimeoutSeconds_DEFAULT
	}
	return *p.SuspendTimeoutSeconds
}

var DefaultComputeSettings_ComputeSpecCode_DEFAULT string

func (p *DefaultComputeSettings) GetComputeSpecCode() (v string) {
	if !p.IsSetComputeSpecCode() {
		return DefaultComputeSettings_ComputeSpecCode_DEFAULT
	}
	return *p.ComputeSpecCode
}

var DefaultComputeSettings_ZoneId_DEFAULT string

func (p *DefaultComputeSettings) GetZoneId() (v string) {
	if !p.IsSetZoneId() {
		return DefaultComputeSettings_ZoneId_DEFAULT
	}
	return *p.ZoneId
}

var DefaultComputeSettings_DBEngineParams_DEFAULT map[string]string

func (p *DefaultComputeSettings) GetDBEngineParams() (v map[string]string) {
	if !p.IsSetDBEngineParams() {
		return DefaultComputeSettings_DBEngineParams_DEFAULT
	}
	return p.DBEngineParams
}

var DefaultComputeSettings_DBProxyParams_DEFAULT map[string]string

func (p *DefaultComputeSettings) GetDBProxyParams() (v map[string]string) {
	if !p.IsSetDBProxyParams() {
		return DefaultComputeSettings_DBProxyParams_DEFAULT
	}
	return p.DBProxyParams
}
func (p *DefaultComputeSettings) SetAutoScalingLimitMinCU(val *float64) {
	p.AutoScalingLimitMinCU = val
}
func (p *DefaultComputeSettings) SetAutoScalingLimitMaxCU(val *float64) {
	p.AutoScalingLimitMaxCU = val
}
func (p *DefaultComputeSettings) SetSuspendTimeoutSeconds(val *int32) {
	p.SuspendTimeoutSeconds = val
}
func (p *DefaultComputeSettings) SetComputeSpecCode(val *string) {
	p.ComputeSpecCode = val
}
func (p *DefaultComputeSettings) SetZoneId(val *string) {
	p.ZoneId = val
}
func (p *DefaultComputeSettings) SetDBEngineParams(val map[string]string) {
	p.DBEngineParams = val
}
func (p *DefaultComputeSettings) SetDBProxyParams(val map[string]string) {
	p.DBProxyParams = val
}

var fieldIDToName_DefaultComputeSettings = map[int16]string{
	2: "AutoScalingLimitMinCU",
	3: "AutoScalingLimitMaxCU",
	4: "SuspendTimeoutSeconds",
	5: "ComputeSpecCode",
	6: "ZoneId",
	7: "DBEngineParams",
	8: "DBProxyParams",
}

func (p *DefaultComputeSettings) IsSetAutoScalingLimitMinCU() bool {
	return p.AutoScalingLimitMinCU != nil
}

func (p *DefaultComputeSettings) IsSetAutoScalingLimitMaxCU() bool {
	return p.AutoScalingLimitMaxCU != nil
}

func (p *DefaultComputeSettings) IsSetSuspendTimeoutSeconds() bool {
	return p.SuspendTimeoutSeconds != nil
}

func (p *DefaultComputeSettings) IsSetComputeSpecCode() bool {
	return p.ComputeSpecCode != nil
}

func (p *DefaultComputeSettings) IsSetZoneId() bool {
	return p.ZoneId != nil
}

func (p *DefaultComputeSettings) IsSetDBEngineParams() bool {
	return p.DBEngineParams != nil
}

func (p *DefaultComputeSettings) IsSetDBProxyParams() bool {
	return p.DBProxyParams != nil
}

func (p *DefaultComputeSettings) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultComputeSettings")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultComputeSettings[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DefaultComputeSettings) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMinCU = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMaxCU = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuspendTimeoutSeconds = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ComputeSpecCode = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ZoneId = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.DBEngineParams = _field
	return nil
}
func (p *DefaultComputeSettings) ReadField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.DBProxyParams = _field
	return nil
}

func (p *DefaultComputeSettings) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultComputeSettings")

	var fieldId int16
	if err = oprot.WriteStructBegin("DefaultComputeSettings"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMinCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMinCU", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMinCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMaxCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMaxCU", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMaxCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuspendTimeoutSeconds() {
		if err = oprot.WriteFieldBegin("SuspendTimeoutSeconds", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SuspendTimeoutSeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSpecCode() {
		if err = oprot.WriteFieldBegin("ComputeSpecCode", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ComputeSpecCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetZoneId() {
		if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ZoneId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineParams() {
		if err = oprot.WriteFieldBegin("DBEngineParams", thrift.MAP, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DBEngineParams)); err != nil {
			return err
		}
		for k, v := range p.DBEngineParams {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DefaultComputeSettings) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBProxyParams() {
		if err = oprot.WriteFieldBegin("DBProxyParams", thrift.MAP, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DBProxyParams)); err != nil {
			return err
		}
		for k, v := range p.DBProxyParams {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DefaultComputeSettings) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DefaultComputeSettings(%+v)", *p)

}

func (p *DefaultComputeSettings) DeepEqual(ano *DefaultComputeSettings) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field2DeepEqual(ano.AutoScalingLimitMinCU) {
		return false
	}
	if !p.Field3DeepEqual(ano.AutoScalingLimitMaxCU) {
		return false
	}
	if !p.Field4DeepEqual(ano.SuspendTimeoutSeconds) {
		return false
	}
	if !p.Field5DeepEqual(ano.ComputeSpecCode) {
		return false
	}
	if !p.Field6DeepEqual(ano.ZoneId) {
		return false
	}
	if !p.Field7DeepEqual(ano.DBEngineParams) {
		return false
	}
	if !p.Field8DeepEqual(ano.DBProxyParams) {
		return false
	}
	return true
}

func (p *DefaultComputeSettings) Field2DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMinCU == src {
		return true
	} else if p.AutoScalingLimitMinCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMinCU != *src {
		return false
	}
	return true
}
func (p *DefaultComputeSettings) Field3DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMaxCU == src {
		return true
	} else if p.AutoScalingLimitMaxCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMaxCU != *src {
		return false
	}
	return true
}
func (p *DefaultComputeSettings) Field4DeepEqual(src *int32) bool {

	if p.SuspendTimeoutSeconds == src {
		return true
	} else if p.SuspendTimeoutSeconds == nil || src == nil {
		return false
	}
	if *p.SuspendTimeoutSeconds != *src {
		return false
	}
	return true
}
func (p *DefaultComputeSettings) Field5DeepEqual(src *string) bool {

	if p.ComputeSpecCode == src {
		return true
	} else if p.ComputeSpecCode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ComputeSpecCode, *src) != 0 {
		return false
	}
	return true
}
func (p *DefaultComputeSettings) Field6DeepEqual(src *string) bool {

	if p.ZoneId == src {
		return true
	} else if p.ZoneId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ZoneId, *src) != 0 {
		return false
	}
	return true
}
func (p *DefaultComputeSettings) Field7DeepEqual(src map[string]string) bool {

	if len(p.DBEngineParams) != len(src) {
		return false
	}
	for k, v := range p.DBEngineParams {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DefaultComputeSettings) Field8DeepEqual(src map[string]string) bool {

	if len(p.DBProxyParams) != len(src) {
		return false
	}
	for k, v := range p.DBProxyParams {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DefaultBranchSettings struct {
	BranchName   *string `thrift:"BranchName,1,optional" frugal:"1,optional,string" json:"BranchName,omitempty"`
	DatabaseName *string `thrift:"DatabaseName,2,optional" frugal:"2,optional,string" json:"DatabaseName,omitempty"`
	AccountName  *string `thrift:"AccountName,3,optional" frugal:"3,optional,string" json:"AccountName,omitempty"`
}

func NewDefaultBranchSettings() *DefaultBranchSettings {
	return &DefaultBranchSettings{}
}

func (p *DefaultBranchSettings) InitDefault() {
}

var DefaultBranchSettings_BranchName_DEFAULT string

func (p *DefaultBranchSettings) GetBranchName() (v string) {
	if !p.IsSetBranchName() {
		return DefaultBranchSettings_BranchName_DEFAULT
	}
	return *p.BranchName
}

var DefaultBranchSettings_DatabaseName_DEFAULT string

func (p *DefaultBranchSettings) GetDatabaseName() (v string) {
	if !p.IsSetDatabaseName() {
		return DefaultBranchSettings_DatabaseName_DEFAULT
	}
	return *p.DatabaseName
}

var DefaultBranchSettings_AccountName_DEFAULT string

func (p *DefaultBranchSettings) GetAccountName() (v string) {
	if !p.IsSetAccountName() {
		return DefaultBranchSettings_AccountName_DEFAULT
	}
	return *p.AccountName
}
func (p *DefaultBranchSettings) SetBranchName(val *string) {
	p.BranchName = val
}
func (p *DefaultBranchSettings) SetDatabaseName(val *string) {
	p.DatabaseName = val
}
func (p *DefaultBranchSettings) SetAccountName(val *string) {
	p.AccountName = val
}

var fieldIDToName_DefaultBranchSettings = map[int16]string{
	1: "BranchName",
	2: "DatabaseName",
	3: "AccountName",
}

func (p *DefaultBranchSettings) IsSetBranchName() bool {
	return p.BranchName != nil
}

func (p *DefaultBranchSettings) IsSetDatabaseName() bool {
	return p.DatabaseName != nil
}

func (p *DefaultBranchSettings) IsSetAccountName() bool {
	return p.AccountName != nil
}

func (p *DefaultBranchSettings) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultBranchSettings")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultBranchSettings[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DefaultBranchSettings) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchName = _field
	return nil
}
func (p *DefaultBranchSettings) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatabaseName = _field
	return nil
}
func (p *DefaultBranchSettings) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountName = _field
	return nil
}

func (p *DefaultBranchSettings) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DefaultBranchSettings")

	var fieldId int16
	if err = oprot.WriteStructBegin("DefaultBranchSettings"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DefaultBranchSettings) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchName() {
		if err = oprot.WriteFieldBegin("BranchName", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DefaultBranchSettings) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseName() {
		if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatabaseName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DefaultBranchSettings) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountName() {
		if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DefaultBranchSettings) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DefaultBranchSettings(%+v)", *p)

}

func (p *DefaultBranchSettings) DeepEqual(ano *DefaultBranchSettings) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BranchName) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountName) {
		return false
	}
	return true
}

func (p *DefaultBranchSettings) Field1DeepEqual(src *string) bool {

	if p.BranchName == src {
		return true
	} else if p.BranchName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchName, *src) != 0 {
		return false
	}
	return true
}
func (p *DefaultBranchSettings) Field2DeepEqual(src *string) bool {

	if p.DatabaseName == src {
		return true
	} else if p.DatabaseName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatabaseName, *src) != 0 {
		return false
	}
	return true
}
func (p *DefaultBranchSettings) Field3DeepEqual(src *string) bool {

	if p.AccountName == src {
		return true
	} else if p.AccountName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountName, *src) != 0 {
		return false
	}
	return true
}

type InstanceUsage struct {
	DataSizeUsedBytes        int64  `thrift:"DataSizeUsedBytes,1,required" frugal:"1,required,i64" json:"DataSizeUsedBytes"`
	DataSizeTotalBytes       int64  `thrift:"DataSizeTotalBytes,2,required" frugal:"2,required,i64" json:"DataSizeTotalBytes"`
	ComputeTimeSeconds       int64  `thrift:"ComputeTimeSeconds,3,required" frugal:"3,required,i64" json:"ComputeTimeSeconds"`
	BranchComputeTimeSeconds int64  `thrift:"BranchComputeTimeSeconds,4,required" frugal:"4,required,i64" json:"BranchComputeTimeSeconds"`
	BranchCreatedNum         int64  `thrift:"BranchCreatedNum,5,required" frugal:"5,required,i64" json:"BranchCreatedNum"`
	StatTime                 string `thrift:"StatTime,6,required" frugal:"6,required,string" json:"StatTime"`
}

func NewInstanceUsage() *InstanceUsage {
	return &InstanceUsage{}
}

func (p *InstanceUsage) InitDefault() {
}

func (p *InstanceUsage) GetDataSizeUsedBytes() (v int64) {
	return p.DataSizeUsedBytes
}

func (p *InstanceUsage) GetDataSizeTotalBytes() (v int64) {
	return p.DataSizeTotalBytes
}

func (p *InstanceUsage) GetComputeTimeSeconds() (v int64) {
	return p.ComputeTimeSeconds
}

func (p *InstanceUsage) GetBranchComputeTimeSeconds() (v int64) {
	return p.BranchComputeTimeSeconds
}

func (p *InstanceUsage) GetBranchCreatedNum() (v int64) {
	return p.BranchCreatedNum
}

func (p *InstanceUsage) GetStatTime() (v string) {
	return p.StatTime
}
func (p *InstanceUsage) SetDataSizeUsedBytes(val int64) {
	p.DataSizeUsedBytes = val
}
func (p *InstanceUsage) SetDataSizeTotalBytes(val int64) {
	p.DataSizeTotalBytes = val
}
func (p *InstanceUsage) SetComputeTimeSeconds(val int64) {
	p.ComputeTimeSeconds = val
}
func (p *InstanceUsage) SetBranchComputeTimeSeconds(val int64) {
	p.BranchComputeTimeSeconds = val
}
func (p *InstanceUsage) SetBranchCreatedNum(val int64) {
	p.BranchCreatedNum = val
}
func (p *InstanceUsage) SetStatTime(val string) {
	p.StatTime = val
}

var fieldIDToName_InstanceUsage = map[int16]string{
	1: "DataSizeUsedBytes",
	2: "DataSizeTotalBytes",
	3: "ComputeTimeSeconds",
	4: "BranchComputeTimeSeconds",
	5: "BranchCreatedNum",
	6: "StatTime",
}

func (p *InstanceUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataSizeUsedBytes bool = false
	var issetDataSizeTotalBytes bool = false
	var issetComputeTimeSeconds bool = false
	var issetBranchComputeTimeSeconds bool = false
	var issetBranchCreatedNum bool = false
	var issetStatTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeUsedBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataSizeTotalBytes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeTimeSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchComputeTimeSeconds = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetBranchCreatedNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataSizeUsedBytes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDataSizeTotalBytes {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComputeTimeSeconds {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetBranchComputeTimeSeconds {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetBranchCreatedNum {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStatTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InstanceUsage[fieldId]))
}

func (p *InstanceUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeUsedBytes = _field
	return nil
}
func (p *InstanceUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DataSizeTotalBytes = _field
	return nil
}
func (p *InstanceUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeTimeSeconds = _field
	return nil
}
func (p *InstanceUsage) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchComputeTimeSeconds = _field
	return nil
}
func (p *InstanceUsage) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchCreatedNum = _field
	return nil
}
func (p *InstanceUsage) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatTime = _field
	return nil
}

func (p *InstanceUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("InstanceUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeUsedBytes", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeUsedBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InstanceUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataSizeTotalBytes", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DataSizeTotalBytes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InstanceUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeTimeSeconds", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ComputeTimeSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InstanceUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchComputeTimeSeconds", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BranchComputeTimeSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InstanceUsage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchCreatedNum", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BranchCreatedNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InstanceUsage) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InstanceUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceUsage(%+v)", *p)

}

func (p *InstanceUsage) DeepEqual(ano *InstanceUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataSizeUsedBytes) {
		return false
	}
	if !p.Field2DeepEqual(ano.DataSizeTotalBytes) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeTimeSeconds) {
		return false
	}
	if !p.Field4DeepEqual(ano.BranchComputeTimeSeconds) {
		return false
	}
	if !p.Field5DeepEqual(ano.BranchCreatedNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.StatTime) {
		return false
	}
	return true
}

func (p *InstanceUsage) Field1DeepEqual(src int64) bool {

	if p.DataSizeUsedBytes != src {
		return false
	}
	return true
}
func (p *InstanceUsage) Field2DeepEqual(src int64) bool {

	if p.DataSizeTotalBytes != src {
		return false
	}
	return true
}
func (p *InstanceUsage) Field3DeepEqual(src int64) bool {

	if p.ComputeTimeSeconds != src {
		return false
	}
	return true
}
func (p *InstanceUsage) Field4DeepEqual(src int64) bool {

	if p.BranchComputeTimeSeconds != src {
		return false
	}
	return true
}
func (p *InstanceUsage) Field5DeepEqual(src int64) bool {

	if p.BranchCreatedNum != src {
		return false
	}
	return true
}
func (p *InstanceUsage) Field6DeepEqual(src string) bool {

	if strings.Compare(p.StatTime, src) != 0 {
		return false
	}
	return true
}

type CreateInstanceReq struct {
	DBEngineType     *DBEngineType            `thrift:"DBEngineType,1,optional" frugal:"1,optional,DBEngineType" json:"DBEngineType,omitempty"`
	DBEngineVersion  *DBEngineVersion         `thrift:"DBEngineVersion,2,optional" frugal:"2,optional,DBEngineVersion" json:"DBEngineVersion,omitempty"`
	InstanceName     *string                  `thrift:"InstanceName,3,optional" frugal:"3,optional,string" json:"InstanceName,omitempty"`
	InstanceSettings *DefaultInstanceSettings `thrift:"InstanceSettings,4,optional" frugal:"4,optional,DefaultInstanceSettings" json:"InstanceSettings,omitempty"`
	ComputeSettings  *DefaultComputeSettings  `thrift:"ComputeSettings,5,optional" frugal:"5,optional,DefaultComputeSettings" json:"ComputeSettings,omitempty"`
	BranchSettings   *DefaultBranchSettings   `thrift:"BranchSettings,6,optional" frugal:"6,optional,DefaultBranchSettings" json:"BranchSettings,omitempty"`
	InstanceTags     []*Tag                   `thrift:"InstanceTags,7,optional" frugal:"7,optional,list<Tag>" json:"InstanceTags,omitempty"`
}

func NewCreateInstanceReq() *CreateInstanceReq {
	return &CreateInstanceReq{}
}

func (p *CreateInstanceReq) InitDefault() {
}

var CreateInstanceReq_DBEngineType_DEFAULT DBEngineType

func (p *CreateInstanceReq) GetDBEngineType() (v DBEngineType) {
	if !p.IsSetDBEngineType() {
		return CreateInstanceReq_DBEngineType_DEFAULT
	}
	return *p.DBEngineType
}

var CreateInstanceReq_DBEngineVersion_DEFAULT DBEngineVersion

func (p *CreateInstanceReq) GetDBEngineVersion() (v DBEngineVersion) {
	if !p.IsSetDBEngineVersion() {
		return CreateInstanceReq_DBEngineVersion_DEFAULT
	}
	return *p.DBEngineVersion
}

var CreateInstanceReq_InstanceName_DEFAULT string

func (p *CreateInstanceReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return CreateInstanceReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var CreateInstanceReq_InstanceSettings_DEFAULT *DefaultInstanceSettings

func (p *CreateInstanceReq) GetInstanceSettings() (v *DefaultInstanceSettings) {
	if !p.IsSetInstanceSettings() {
		return CreateInstanceReq_InstanceSettings_DEFAULT
	}
	return p.InstanceSettings
}

var CreateInstanceReq_ComputeSettings_DEFAULT *DefaultComputeSettings

func (p *CreateInstanceReq) GetComputeSettings() (v *DefaultComputeSettings) {
	if !p.IsSetComputeSettings() {
		return CreateInstanceReq_ComputeSettings_DEFAULT
	}
	return p.ComputeSettings
}

var CreateInstanceReq_BranchSettings_DEFAULT *DefaultBranchSettings

func (p *CreateInstanceReq) GetBranchSettings() (v *DefaultBranchSettings) {
	if !p.IsSetBranchSettings() {
		return CreateInstanceReq_BranchSettings_DEFAULT
	}
	return p.BranchSettings
}

var CreateInstanceReq_InstanceTags_DEFAULT []*Tag

func (p *CreateInstanceReq) GetInstanceTags() (v []*Tag) {
	if !p.IsSetInstanceTags() {
		return CreateInstanceReq_InstanceTags_DEFAULT
	}
	return p.InstanceTags
}
func (p *CreateInstanceReq) SetDBEngineType(val *DBEngineType) {
	p.DBEngineType = val
}
func (p *CreateInstanceReq) SetDBEngineVersion(val *DBEngineVersion) {
	p.DBEngineVersion = val
}
func (p *CreateInstanceReq) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *CreateInstanceReq) SetInstanceSettings(val *DefaultInstanceSettings) {
	p.InstanceSettings = val
}
func (p *CreateInstanceReq) SetComputeSettings(val *DefaultComputeSettings) {
	p.ComputeSettings = val
}
func (p *CreateInstanceReq) SetBranchSettings(val *DefaultBranchSettings) {
	p.BranchSettings = val
}
func (p *CreateInstanceReq) SetInstanceTags(val []*Tag) {
	p.InstanceTags = val
}

var fieldIDToName_CreateInstanceReq = map[int16]string{
	1: "DBEngineType",
	2: "DBEngineVersion",
	3: "InstanceName",
	4: "InstanceSettings",
	5: "ComputeSettings",
	6: "BranchSettings",
	7: "InstanceTags",
}

func (p *CreateInstanceReq) IsSetDBEngineType() bool {
	return p.DBEngineType != nil
}

func (p *CreateInstanceReq) IsSetDBEngineVersion() bool {
	return p.DBEngineVersion != nil
}

func (p *CreateInstanceReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *CreateInstanceReq) IsSetInstanceSettings() bool {
	return p.InstanceSettings != nil
}

func (p *CreateInstanceReq) IsSetComputeSettings() bool {
	return p.ComputeSettings != nil
}

func (p *CreateInstanceReq) IsSetBranchSettings() bool {
	return p.BranchSettings != nil
}

func (p *CreateInstanceReq) IsSetInstanceTags() bool {
	return p.InstanceTags != nil
}

func (p *CreateInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *DBEngineType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngineType(v)
		_field = &tmp
	}
	p.DBEngineType = _field
	return nil
}
func (p *CreateInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DBEngineVersion
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DBEngineVersion(v)
		_field = &tmp
	}
	p.DBEngineVersion = _field
	return nil
}
func (p *CreateInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *CreateInstanceReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewDefaultInstanceSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceSettings = _field
	return nil
}
func (p *CreateInstanceReq) ReadField5(iprot thrift.TProtocol) error {
	_field := NewDefaultComputeSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ComputeSettings = _field
	return nil
}
func (p *CreateInstanceReq) ReadField6(iprot thrift.TProtocol) error {
	_field := NewDefaultBranchSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BranchSettings = _field
	return nil
}
func (p *CreateInstanceReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Tag, 0, size)
	values := make([]Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceTags = _field
	return nil
}

func (p *CreateInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineType() {
		if err = oprot.WriteFieldBegin("DBEngineType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DBEngineType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineVersion() {
		if err = oprot.WriteFieldBegin("DBEngineVersion", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DBEngineVersion)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceSettings() {
		if err = oprot.WriteFieldBegin("InstanceSettings", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InstanceSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSettings() {
		if err = oprot.WriteFieldBegin("ComputeSettings", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ComputeSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchSettings() {
		if err = oprot.WriteFieldBegin("BranchSettings", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BranchSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceTags() {
		if err = oprot.WriteFieldBegin("InstanceTags", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceTags)); err != nil {
			return err
		}
		for _, v := range p.InstanceTags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateInstanceReq(%+v)", *p)

}

func (p *CreateInstanceReq) DeepEqual(ano *CreateInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBEngineType) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBEngineVersion) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceSettings) {
		return false
	}
	if !p.Field5DeepEqual(ano.ComputeSettings) {
		return false
	}
	if !p.Field6DeepEqual(ano.BranchSettings) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceTags) {
		return false
	}
	return true
}

func (p *CreateInstanceReq) Field1DeepEqual(src *DBEngineType) bool {

	if p.DBEngineType == src {
		return true
	} else if p.DBEngineType == nil || src == nil {
		return false
	}
	if *p.DBEngineType != *src {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field2DeepEqual(src *DBEngineVersion) bool {

	if p.DBEngineVersion == src {
		return true
	} else if p.DBEngineVersion == nil || src == nil {
		return false
	}
	if *p.DBEngineVersion != *src {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field3DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field4DeepEqual(src *DefaultInstanceSettings) bool {

	if !p.InstanceSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field5DeepEqual(src *DefaultComputeSettings) bool {

	if !p.ComputeSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field6DeepEqual(src *DefaultBranchSettings) bool {

	if !p.BranchSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateInstanceReq) Field7DeepEqual(src []*Tag) bool {

	if len(p.InstanceTags) != len(src) {
		return false
	}
	for i, v := range p.InstanceTags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type CreateInstanceResp struct {
	InstanceId string    `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	Instance   *Instance `thrift:"Instance,2,required" frugal:"2,required,Instance" json:"Instance"`
}

func NewCreateInstanceResp() *CreateInstanceResp {
	return &CreateInstanceResp{}
}

func (p *CreateInstanceResp) InitDefault() {
}

func (p *CreateInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateInstanceResp_Instance_DEFAULT *Instance

func (p *CreateInstanceResp) GetInstance() (v *Instance) {
	if !p.IsSetInstance() {
		return CreateInstanceResp_Instance_DEFAULT
	}
	return p.Instance
}
func (p *CreateInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateInstanceResp) SetInstance(val *Instance) {
	p.Instance = val
}

var fieldIDToName_CreateInstanceResp = map[int16]string{
	1: "InstanceId",
	2: "Instance",
}

func (p *CreateInstanceResp) IsSetInstance() bool {
	return p.Instance != nil
}

func (p *CreateInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateInstanceResp[fieldId]))
}

func (p *CreateInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateInstanceResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstance()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Instance = _field
	return nil
}

func (p *CreateInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateInstanceResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instance", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Instance.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateInstanceResp(%+v)", *p)

}

func (p *CreateInstanceResp) DeepEqual(ano *CreateInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Instance) {
		return false
	}
	return true
}

func (p *CreateInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateInstanceResp) Field2DeepEqual(src *Instance) bool {

	if !p.Instance.DeepEqual(src) {
		return false
	}
	return true
}

type DescribeInstancesReq struct {
	Search    *string        `thrift:"Search,1,optional" frugal:"1,optional,string" json:"Search,omitempty"`
	SortBy    *string        `thrift:"SortBy,2,optional" frugal:"2,optional,string" json:"SortBy,omitempty"`
	SortOrder *SortOrder     `thrift:"SortOrder,3,optional" frugal:"3,optional,SortOrder" json:"SortOrder,omitempty"`
	Offset    *int32         `thrift:"Offset,4,optional" frugal:"4,optional,i32" json:"Offset,omitempty"`
	Limit     *int32         `thrift:"Limit,5,optional" frugal:"5,optional,i32" json:"Limit,omitempty"`
	Filters   []*QueryFilter `thrift:"Filters,6,optional" frugal:"6,optional,list<QueryFilter>" json:"Filters,omitempty"`
}

func NewDescribeInstancesReq() *DescribeInstancesReq {
	return &DescribeInstancesReq{}
}

func (p *DescribeInstancesReq) InitDefault() {
}

var DescribeInstancesReq_Search_DEFAULT string

func (p *DescribeInstancesReq) GetSearch() (v string) {
	if !p.IsSetSearch() {
		return DescribeInstancesReq_Search_DEFAULT
	}
	return *p.Search
}

var DescribeInstancesReq_SortBy_DEFAULT string

func (p *DescribeInstancesReq) GetSortBy() (v string) {
	if !p.IsSetSortBy() {
		return DescribeInstancesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeInstancesReq_SortOrder_DEFAULT SortOrder

func (p *DescribeInstancesReq) GetSortOrder() (v SortOrder) {
	if !p.IsSetSortOrder() {
		return DescribeInstancesReq_SortOrder_DEFAULT
	}
	return *p.SortOrder
}

var DescribeInstancesReq_Offset_DEFAULT int32

func (p *DescribeInstancesReq) GetOffset() (v int32) {
	if !p.IsSetOffset() {
		return DescribeInstancesReq_Offset_DEFAULT
	}
	return *p.Offset
}

var DescribeInstancesReq_Limit_DEFAULT int32

func (p *DescribeInstancesReq) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return DescribeInstancesReq_Limit_DEFAULT
	}
	return *p.Limit
}

var DescribeInstancesReq_Filters_DEFAULT []*QueryFilter

func (p *DescribeInstancesReq) GetFilters() (v []*QueryFilter) {
	if !p.IsSetFilters() {
		return DescribeInstancesReq_Filters_DEFAULT
	}
	return p.Filters
}
func (p *DescribeInstancesReq) SetSearch(val *string) {
	p.Search = val
}
func (p *DescribeInstancesReq) SetSortBy(val *string) {
	p.SortBy = val
}
func (p *DescribeInstancesReq) SetSortOrder(val *SortOrder) {
	p.SortOrder = val
}
func (p *DescribeInstancesReq) SetOffset(val *int32) {
	p.Offset = val
}
func (p *DescribeInstancesReq) SetLimit(val *int32) {
	p.Limit = val
}
func (p *DescribeInstancesReq) SetFilters(val []*QueryFilter) {
	p.Filters = val
}

var fieldIDToName_DescribeInstancesReq = map[int16]string{
	1: "Search",
	2: "SortBy",
	3: "SortOrder",
	4: "Offset",
	5: "Limit",
	6: "Filters",
}

func (p *DescribeInstancesReq) IsSetSearch() bool {
	return p.Search != nil
}

func (p *DescribeInstancesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeInstancesReq) IsSetSortOrder() bool {
	return p.SortOrder != nil
}

func (p *DescribeInstancesReq) IsSetOffset() bool {
	return p.Offset != nil
}

func (p *DescribeInstancesReq) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *DescribeInstancesReq) IsSetFilters() bool {
	return p.Filters != nil
}

func (p *DescribeInstancesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeInstancesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Search = _field
	return nil
}
func (p *DescribeInstancesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeInstancesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *SortOrder
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortOrder(v)
		_field = &tmp
	}
	p.SortOrder = _field
	return nil
}
func (p *DescribeInstancesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Offset = _field
	return nil
}
func (p *DescribeInstancesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *DescribeInstancesReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*QueryFilter, 0, size)
	values := make([]QueryFilter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Filters = _field
	return nil
}

func (p *DescribeInstancesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstancesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearch() {
		if err = oprot.WriteFieldBegin("Search", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Search); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SortBy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortOrder() {
		if err = oprot.WriteFieldBegin("SortOrder", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortOrder)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOffset() {
		if err = oprot.WriteFieldBegin("Offset", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Offset); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstancesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilters() {
		if err = oprot.WriteFieldBegin("Filters", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Filters)); err != nil {
			return err
		}
		for _, v := range p.Filters {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeInstancesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstancesReq(%+v)", *p)

}

func (p *DescribeInstancesReq) DeepEqual(ano *DescribeInstancesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Search) {
		return false
	}
	if !p.Field2DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field3DeepEqual(ano.SortOrder) {
		return false
	}
	if !p.Field4DeepEqual(ano.Offset) {
		return false
	}
	if !p.Field5DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field6DeepEqual(ano.Filters) {
		return false
	}
	return true
}

func (p *DescribeInstancesReq) Field1DeepEqual(src *string) bool {

	if p.Search == src {
		return true
	} else if p.Search == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Search, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstancesReq) Field2DeepEqual(src *string) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SortBy, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstancesReq) Field3DeepEqual(src *SortOrder) bool {

	if p.SortOrder == src {
		return true
	} else if p.SortOrder == nil || src == nil {
		return false
	}
	if *p.SortOrder != *src {
		return false
	}
	return true
}
func (p *DescribeInstancesReq) Field4DeepEqual(src *int32) bool {

	if p.Offset == src {
		return true
	} else if p.Offset == nil || src == nil {
		return false
	}
	if *p.Offset != *src {
		return false
	}
	return true
}
func (p *DescribeInstancesReq) Field5DeepEqual(src *int32) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}
func (p *DescribeInstancesReq) Field6DeepEqual(src []*QueryFilter) bool {

	if len(p.Filters) != len(src) {
		return false
	}
	for i, v := range p.Filters {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeInstancesResp struct {
	Total     int32       `thrift:"Total,1,required" frugal:"1,required,i32" json:"Total"`
	Instances []*Instance `thrift:"Instances,2,required" frugal:"2,required,list<Instance>" json:"Instances"`
}

func NewDescribeInstancesResp() *DescribeInstancesResp {
	return &DescribeInstancesResp{}
}

func (p *DescribeInstancesResp) InitDefault() {
}

func (p *DescribeInstancesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeInstancesResp) GetInstances() (v []*Instance) {
	return p.Instances
}
func (p *DescribeInstancesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeInstancesResp) SetInstances(val []*Instance) {
	p.Instances = val
}

var fieldIDToName_DescribeInstancesResp = map[int16]string{
	1: "Total",
	2: "Instances",
}

func (p *DescribeInstancesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTotal bool = false
	var issetInstances bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstances = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTotal {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstances {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstancesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstancesResp[fieldId]))
}

func (p *DescribeInstancesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeInstancesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Instance, 0, size)
	values := make([]Instance, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Instances = _field
	return nil
}

func (p *DescribeInstancesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstancesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstancesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstancesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstancesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instances", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Instances)); err != nil {
		return err
	}
	for _, v := range p.Instances {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstancesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstancesResp(%+v)", *p)

}

func (p *DescribeInstancesResp) DeepEqual(ano *DescribeInstancesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Instances) {
		return false
	}
	return true
}

func (p *DescribeInstancesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeInstancesResp) Field2DeepEqual(src []*Instance) bool {

	if len(p.Instances) != len(src) {
		return false
	}
	for i, v := range p.Instances {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeInstanceDetailReq struct {
	InstanceId *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
}

func NewDescribeInstanceDetailReq() *DescribeInstanceDetailReq {
	return &DescribeInstanceDetailReq{}
}

func (p *DescribeInstanceDetailReq) InitDefault() {
}

var DescribeInstanceDetailReq_InstanceId_DEFAULT string

func (p *DescribeInstanceDetailReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeInstanceDetailReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *DescribeInstanceDetailReq) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_DescribeInstanceDetailReq = map[int16]string{
	1: "InstanceId",
}

func (p *DescribeInstanceDetailReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeInstanceDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *DescribeInstanceDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDetailReq(%+v)", *p)

}

func (p *DescribeInstanceDetailReq) DeepEqual(ano *DescribeInstanceDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DescribeInstanceDetailReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceDetailResp struct {
	Instance *Instance `thrift:"Instance,1,required" frugal:"1,required,Instance" json:"Instance"`
}

func NewDescribeInstanceDetailResp() *DescribeInstanceDetailResp {
	return &DescribeInstanceDetailResp{}
}

func (p *DescribeInstanceDetailResp) InitDefault() {
}

var DescribeInstanceDetailResp_Instance_DEFAULT *Instance

func (p *DescribeInstanceDetailResp) GetInstance() (v *Instance) {
	if !p.IsSetInstance() {
		return DescribeInstanceDetailResp_Instance_DEFAULT
	}
	return p.Instance
}
func (p *DescribeInstanceDetailResp) SetInstance(val *Instance) {
	p.Instance = val
}

var fieldIDToName_DescribeInstanceDetailResp = map[int16]string{
	1: "Instance",
}

func (p *DescribeInstanceDetailResp) IsSetInstance() bool {
	return p.Instance != nil
}

func (p *DescribeInstanceDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstance bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstance {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceDetailResp[fieldId]))
}

func (p *DescribeInstanceDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInstance()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Instance = _field
	return nil
}

func (p *DescribeInstanceDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instance", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Instance.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceDetailResp(%+v)", *p)

}

func (p *DescribeInstanceDetailResp) DeepEqual(ano *DescribeInstanceDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Instance) {
		return false
	}
	return true
}

func (p *DescribeInstanceDetailResp) Field1DeepEqual(src *Instance) bool {

	if !p.Instance.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteInstanceReq struct {
	InstanceId *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
}

func NewDeleteInstanceReq() *DeleteInstanceReq {
	return &DeleteInstanceReq{}
}

func (p *DeleteInstanceReq) InitDefault() {
}

var DeleteInstanceReq_InstanceId_DEFAULT string

func (p *DeleteInstanceReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DeleteInstanceReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *DeleteInstanceReq) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_DeleteInstanceReq = map[int16]string{
	1: "InstanceId",
}

func (p *DeleteInstanceReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DeleteInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *DeleteInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteInstanceReq(%+v)", *p)

}

func (p *DeleteInstanceReq) DeepEqual(ano *DeleteInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DeleteInstanceReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type DeleteInstanceResp struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
}

func NewDeleteInstanceResp() *DeleteInstanceResp {
	return &DeleteInstanceResp{}
}

func (p *DeleteInstanceResp) InitDefault() {
}

func (p *DeleteInstanceResp) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *DeleteInstanceResp) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_DeleteInstanceResp = map[int16]string{
	1: "InstanceId",
}

func (p *DeleteInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteInstanceResp[fieldId]))
}

func (p *DeleteInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *DeleteInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteInstanceResp(%+v)", *p)

}

func (p *DeleteInstanceResp) DeepEqual(ano *DeleteInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *DeleteInstanceResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type ModifyInstanceNameReq struct {
	InstanceId   *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceName *string `thrift:"InstanceName,2,optional" frugal:"2,optional,string" json:"InstanceName,omitempty"`
}

func NewModifyInstanceNameReq() *ModifyInstanceNameReq {
	return &ModifyInstanceNameReq{}
}

func (p *ModifyInstanceNameReq) InitDefault() {
}

var ModifyInstanceNameReq_InstanceId_DEFAULT string

func (p *ModifyInstanceNameReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return ModifyInstanceNameReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var ModifyInstanceNameReq_InstanceName_DEFAULT string

func (p *ModifyInstanceNameReq) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return ModifyInstanceNameReq_InstanceName_DEFAULT
	}
	return *p.InstanceName
}
func (p *ModifyInstanceNameReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *ModifyInstanceNameReq) SetInstanceName(val *string) {
	p.InstanceName = val
}

var fieldIDToName_ModifyInstanceNameReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
}

func (p *ModifyInstanceNameReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *ModifyInstanceNameReq) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *ModifyInstanceNameReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceNameReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceNameReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyInstanceNameReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceNameReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}

func (p *ModifyInstanceNameReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceNameReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceNameReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceNameReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceNameReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInstanceNameReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceNameReq(%+v)", *p)

}

func (p *ModifyInstanceNameReq) DeepEqual(ano *ModifyInstanceNameReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	return true
}

func (p *ModifyInstanceNameReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceNameReq) Field2DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}

type ModifyInstanceNameResp struct {
	InstanceId string    `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	Instance   *Instance `thrift:"Instance,2,required" frugal:"2,required,Instance" json:"Instance"`
}

func NewModifyInstanceNameResp() *ModifyInstanceNameResp {
	return &ModifyInstanceNameResp{}
}

func (p *ModifyInstanceNameResp) InitDefault() {
}

func (p *ModifyInstanceNameResp) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyInstanceNameResp_Instance_DEFAULT *Instance

func (p *ModifyInstanceNameResp) GetInstance() (v *Instance) {
	if !p.IsSetInstance() {
		return ModifyInstanceNameResp_Instance_DEFAULT
	}
	return p.Instance
}
func (p *ModifyInstanceNameResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyInstanceNameResp) SetInstance(val *Instance) {
	p.Instance = val
}

var fieldIDToName_ModifyInstanceNameResp = map[int16]string{
	1: "InstanceId",
	2: "Instance",
}

func (p *ModifyInstanceNameResp) IsSetInstance() bool {
	return p.Instance != nil
}

func (p *ModifyInstanceNameResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceNameResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceNameResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyInstanceNameResp[fieldId]))
}

func (p *ModifyInstanceNameResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceNameResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstance()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Instance = _field
	return nil
}

func (p *ModifyInstanceNameResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceNameResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceNameResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceNameResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceNameResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instance", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Instance.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInstanceNameResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceNameResp(%+v)", *p)

}

func (p *ModifyInstanceNameResp) DeepEqual(ano *ModifyInstanceNameResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Instance) {
		return false
	}
	return true
}

func (p *ModifyInstanceNameResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceNameResp) Field2DeepEqual(src *Instance) bool {

	if !p.Instance.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyInstanceSettingsReq struct {
	InstanceId       *string                  `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceSettings *DefaultInstanceSettings `thrift:"InstanceSettings,3,optional" frugal:"3,optional,DefaultInstanceSettings" json:"InstanceSettings,omitempty"`
}

func NewModifyInstanceSettingsReq() *ModifyInstanceSettingsReq {
	return &ModifyInstanceSettingsReq{}
}

func (p *ModifyInstanceSettingsReq) InitDefault() {
}

var ModifyInstanceSettingsReq_InstanceId_DEFAULT string

func (p *ModifyInstanceSettingsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return ModifyInstanceSettingsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var ModifyInstanceSettingsReq_InstanceSettings_DEFAULT *DefaultInstanceSettings

func (p *ModifyInstanceSettingsReq) GetInstanceSettings() (v *DefaultInstanceSettings) {
	if !p.IsSetInstanceSettings() {
		return ModifyInstanceSettingsReq_InstanceSettings_DEFAULT
	}
	return p.InstanceSettings
}
func (p *ModifyInstanceSettingsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *ModifyInstanceSettingsReq) SetInstanceSettings(val *DefaultInstanceSettings) {
	p.InstanceSettings = val
}

var fieldIDToName_ModifyInstanceSettingsReq = map[int16]string{
	1: "InstanceId",
	3: "InstanceSettings",
}

func (p *ModifyInstanceSettingsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *ModifyInstanceSettingsReq) IsSetInstanceSettings() bool {
	return p.InstanceSettings != nil
}

func (p *ModifyInstanceSettingsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceSettingsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceSettingsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyInstanceSettingsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceSettingsReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewDefaultInstanceSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceSettings = _field
	return nil
}

func (p *ModifyInstanceSettingsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceSettingsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceSettingsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceSettingsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceSettingsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceSettings() {
		if err = oprot.WriteFieldBegin("InstanceSettings", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InstanceSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyInstanceSettingsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceSettingsReq(%+v)", *p)

}

func (p *ModifyInstanceSettingsReq) DeepEqual(ano *ModifyInstanceSettingsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceSettings) {
		return false
	}
	return true
}

func (p *ModifyInstanceSettingsReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceSettingsReq) Field3DeepEqual(src *DefaultInstanceSettings) bool {

	if !p.InstanceSettings.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyInstanceSettingsResp struct {
	InstanceId string    `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	Instance   *Instance `thrift:"Instance,2,required" frugal:"2,required,Instance" json:"Instance"`
}

func NewModifyInstanceSettingsResp() *ModifyInstanceSettingsResp {
	return &ModifyInstanceSettingsResp{}
}

func (p *ModifyInstanceSettingsResp) InitDefault() {
}

func (p *ModifyInstanceSettingsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyInstanceSettingsResp_Instance_DEFAULT *Instance

func (p *ModifyInstanceSettingsResp) GetInstance() (v *Instance) {
	if !p.IsSetInstance() {
		return ModifyInstanceSettingsResp_Instance_DEFAULT
	}
	return p.Instance
}
func (p *ModifyInstanceSettingsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyInstanceSettingsResp) SetInstance(val *Instance) {
	p.Instance = val
}

var fieldIDToName_ModifyInstanceSettingsResp = map[int16]string{
	1: "InstanceId",
	2: "Instance",
}

func (p *ModifyInstanceSettingsResp) IsSetInstance() bool {
	return p.Instance != nil
}

func (p *ModifyInstanceSettingsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceSettingsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInstanceSettingsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyInstanceSettingsResp[fieldId]))
}

func (p *ModifyInstanceSettingsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInstanceSettingsResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstance()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Instance = _field
	return nil
}

func (p *ModifyInstanceSettingsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInstanceSettingsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInstanceSettingsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInstanceSettingsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInstanceSettingsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instance", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Instance.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInstanceSettingsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInstanceSettingsResp(%+v)", *p)

}

func (p *ModifyInstanceSettingsResp) DeepEqual(ano *ModifyInstanceSettingsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Instance) {
		return false
	}
	return true
}

func (p *ModifyInstanceSettingsResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInstanceSettingsResp) Field2DeepEqual(src *Instance) bool {

	if !p.Instance.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyComputeSettingsReq struct {
	InstanceId      *string                 `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	ComputeSettings *DefaultComputeSettings `thrift:"ComputeSettings,2,optional" frugal:"2,optional,DefaultComputeSettings" json:"ComputeSettings,omitempty"`
}

func NewModifyComputeSettingsReq() *ModifyComputeSettingsReq {
	return &ModifyComputeSettingsReq{}
}

func (p *ModifyComputeSettingsReq) InitDefault() {
}

var ModifyComputeSettingsReq_InstanceId_DEFAULT string

func (p *ModifyComputeSettingsReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return ModifyComputeSettingsReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var ModifyComputeSettingsReq_ComputeSettings_DEFAULT *DefaultComputeSettings

func (p *ModifyComputeSettingsReq) GetComputeSettings() (v *DefaultComputeSettings) {
	if !p.IsSetComputeSettings() {
		return ModifyComputeSettingsReq_ComputeSettings_DEFAULT
	}
	return p.ComputeSettings
}
func (p *ModifyComputeSettingsReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *ModifyComputeSettingsReq) SetComputeSettings(val *DefaultComputeSettings) {
	p.ComputeSettings = val
}

var fieldIDToName_ModifyComputeSettingsReq = map[int16]string{
	1: "InstanceId",
	2: "ComputeSettings",
}

func (p *ModifyComputeSettingsReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *ModifyComputeSettingsReq) IsSetComputeSettings() bool {
	return p.ComputeSettings != nil
}

func (p *ModifyComputeSettingsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSettingsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSettingsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyComputeSettingsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeSettingsReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewDefaultComputeSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ComputeSettings = _field
	return nil
}

func (p *ModifyComputeSettingsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSettingsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSettingsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeSettingsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeSettingsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSettings() {
		if err = oprot.WriteFieldBegin("ComputeSettings", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ComputeSettings.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeSettingsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeSettingsReq(%+v)", *p)

}

func (p *ModifyComputeSettingsReq) DeepEqual(ano *ModifyComputeSettingsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeSettings) {
		return false
	}
	return true
}

func (p *ModifyComputeSettingsReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSettingsReq) Field2DeepEqual(src *DefaultComputeSettings) bool {

	if !p.ComputeSettings.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyComputeSettingsResp struct {
	InstanceId string    `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	Instance   *Instance `thrift:"Instance,2,required" frugal:"2,required,Instance" json:"Instance"`
}

func NewModifyComputeSettingsResp() *ModifyComputeSettingsResp {
	return &ModifyComputeSettingsResp{}
}

func (p *ModifyComputeSettingsResp) InitDefault() {
}

func (p *ModifyComputeSettingsResp) GetInstanceId() (v string) {
	return p.InstanceId
}

var ModifyComputeSettingsResp_Instance_DEFAULT *Instance

func (p *ModifyComputeSettingsResp) GetInstance() (v *Instance) {
	if !p.IsSetInstance() {
		return ModifyComputeSettingsResp_Instance_DEFAULT
	}
	return p.Instance
}
func (p *ModifyComputeSettingsResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyComputeSettingsResp) SetInstance(val *Instance) {
	p.Instance = val
}

var fieldIDToName_ModifyComputeSettingsResp = map[int16]string{
	1: "InstanceId",
	2: "Instance",
}

func (p *ModifyComputeSettingsResp) IsSetInstance() bool {
	return p.Instance != nil
}

func (p *ModifyComputeSettingsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSettingsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstance bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstance = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstance {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSettingsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyComputeSettingsResp[fieldId]))
}

func (p *ModifyComputeSettingsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeSettingsResp) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInstance()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Instance = _field
	return nil
}

func (p *ModifyComputeSettingsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSettingsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSettingsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeSettingsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeSettingsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instance", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Instance.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeSettingsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeSettingsResp(%+v)", *p)

}

func (p *ModifyComputeSettingsResp) DeepEqual(ano *ModifyComputeSettingsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Instance) {
		return false
	}
	return true
}

func (p *ModifyComputeSettingsResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSettingsResp) Field2DeepEqual(src *Instance) bool {

	if !p.Instance.DeepEqual(src) {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DBEngineType int64

const (
	DBEngineType_Unknown    DBEngineType = 0
	DBEngineType_MySQL      DBEngineType = 1
	DBEngineType_PostgreSQL DBEngineType = 2
	DBEngineType_veDB_MySQL DBEngineType = 3
)

func (p DBEngineType) String() string {
	switch p {
	case DBEngineType_Unknown:
		return "Unknown"
	case DBEngineType_MySQL:
		return "MySQL"
	case DBEngineType_PostgreSQL:
		return "PostgreSQL"
	case DBEngineType_veDB_MySQL:
		return "veDB_MySQL"
	}
	return "<UNSET>"
}

func DBEngineTypeFromString(s string) (DBEngineType, error) {
	switch s {
	case "Unknown":
		return DBEngineType_Unknown, nil
	case "MySQL":
		return DBEngineType_MySQL, nil
	case "PostgreSQL":
		return DBEngineType_PostgreSQL, nil
	case "veDB_MySQL":
		return DBEngineType_veDB_MySQL, nil
	}
	return DBEngineType(0), fmt.Errorf("not a valid DBEngineType string")
}

func DBEngineTypePtr(v DBEngineType) *DBEngineType { return &v }

func (p DBEngineType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DBEngineType) UnmarshalText(text []byte) error {
	q, err := DBEngineTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DBEngineVersion int64

const (
	DBEngineVersion_Unknown       DBEngineVersion = 0
	DBEngineVersion_MySQL_5_7     DBEngineVersion = 1
	DBEngineVersion_MySQL_8_0     DBEngineVersion = 2
	DBEngineVersion_PostgreSQL_14 DBEngineVersion = 3
	DBEngineVersion_PostgreSQL_15 DBEngineVersion = 4
	DBEngineVersion_PostgreSQL_16 DBEngineVersion = 5
	DBEngineVersion_PostgreSQL_17 DBEngineVersion = 6
)

func (p DBEngineVersion) String() string {
	switch p {
	case DBEngineVersion_Unknown:
		return "Unknown"
	case DBEngineVersion_MySQL_5_7:
		return "MySQL_5_7"
	case DBEngineVersion_MySQL_8_0:
		return "MySQL_8_0"
	case DBEngineVersion_PostgreSQL_14:
		return "PostgreSQL_14"
	case DBEngineVersion_PostgreSQL_15:
		return "PostgreSQL_15"
	case DBEngineVersion_PostgreSQL_16:
		return "PostgreSQL_16"
	case DBEngineVersion_PostgreSQL_17:
		return "PostgreSQL_17"
	}
	return "<UNSET>"
}

func DBEngineVersionFromString(s string) (DBEngineVersion, error) {
	switch s {
	case "Unknown":
		return DBEngineVersion_Unknown, nil
	case "MySQL_5_7":
		return DBEngineVersion_MySQL_5_7, nil
	case "MySQL_8_0":
		return DBEngineVersion_MySQL_8_0, nil
	case "PostgreSQL_14":
		return DBEngineVersion_PostgreSQL_14, nil
	case "PostgreSQL_15":
		return DBEngineVersion_PostgreSQL_15, nil
	case "PostgreSQL_16":
		return DBEngineVersion_PostgreSQL_16, nil
	case "PostgreSQL_17":
		return DBEngineVersion_PostgreSQL_17, nil
	}
	return DBEngineVersion(0), fmt.Errorf("not a valid DBEngineVersion string")
}

func DBEngineVersionPtr(v DBEngineVersion) *DBEngineVersion { return &v }

func (p DBEngineVersion) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DBEngineVersion) UnmarshalText(text []byte) error {
	q, err := DBEngineVersionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DBEngineMinorVersion int64

const (
	DBEngineMinorVersion_Unknown        DBEngineMinorVersion = 0
	DBEngineMinorVersion_veDB_MySQL_2_0 DBEngineMinorVersion = 1
	DBEngineMinorVersion_veDB_MySQL_3_0 DBEngineMinorVersion = 2
	DBEngineMinorVersion_veDB_MySQL_3_1 DBEngineMinorVersion = 3
	DBEngineMinorVersion_veDB_MySQL_3_2 DBEngineMinorVersion = 4
	DBEngineMinorVersion_veDB_MySQL_3_3 DBEngineMinorVersion = 5
)

func (p DBEngineMinorVersion) String() string {
	switch p {
	case DBEngineMinorVersion_Unknown:
		return "Unknown"
	case DBEngineMinorVersion_veDB_MySQL_2_0:
		return "veDB_MySQL_2_0"
	case DBEngineMinorVersion_veDB_MySQL_3_0:
		return "veDB_MySQL_3_0"
	case DBEngineMinorVersion_veDB_MySQL_3_1:
		return "veDB_MySQL_3_1"
	case DBEngineMinorVersion_veDB_MySQL_3_2:
		return "veDB_MySQL_3_2"
	case DBEngineMinorVersion_veDB_MySQL_3_3:
		return "veDB_MySQL_3_3"
	}
	return "<UNSET>"
}

func DBEngineMinorVersionFromString(s string) (DBEngineMinorVersion, error) {
	switch s {
	case "Unknown":
		return DBEngineMinorVersion_Unknown, nil
	case "veDB_MySQL_2_0":
		return DBEngineMinorVersion_veDB_MySQL_2_0, nil
	case "veDB_MySQL_3_0":
		return DBEngineMinorVersion_veDB_MySQL_3_0, nil
	case "veDB_MySQL_3_1":
		return DBEngineMinorVersion_veDB_MySQL_3_1, nil
	case "veDB_MySQL_3_2":
		return DBEngineMinorVersion_veDB_MySQL_3_2, nil
	case "veDB_MySQL_3_3":
		return DBEngineMinorVersion_veDB_MySQL_3_3, nil
	}
	return DBEngineMinorVersion(0), fmt.Errorf("not a valid DBEngineMinorVersion string")
}

func DBEngineMinorVersionPtr(v DBEngineMinorVersion) *DBEngineMinorVersion { return &v }

func (p DBEngineMinorVersion) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DBEngineMinorVersion) UnmarshalText(text []byte) error {
	q, err := DBEngineMinorVersionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type StorageType int64

const (
	StorageType_Unknown          StorageType = 0
	StorageType_LocalSSD         StorageType = 1
	StorageType_CloudESSD_PL0    StorageType = 2
	StorageType_CloudESSD_FlexPL StorageType = 3
	StorageType_StoragePool      StorageType = 4
)

func (p StorageType) String() string {
	switch p {
	case StorageType_Unknown:
		return "Unknown"
	case StorageType_LocalSSD:
		return "LocalSSD"
	case StorageType_CloudESSD_PL0:
		return "CloudESSD_PL0"
	case StorageType_CloudESSD_FlexPL:
		return "CloudESSD_FlexPL"
	case StorageType_StoragePool:
		return "StoragePool"
	}
	return "<UNSET>"
}

func StorageTypeFromString(s string) (StorageType, error) {
	switch s {
	case "Unknown":
		return StorageType_Unknown, nil
	case "LocalSSD":
		return StorageType_LocalSSD, nil
	case "CloudESSD_PL0":
		return StorageType_CloudESSD_PL0, nil
	case "CloudESSD_FlexPL":
		return StorageType_CloudESSD_FlexPL, nil
	case "StoragePool":
		return StorageType_StoragePool, nil
	}
	return StorageType(0), fmt.Errorf("not a valid StorageType string")
}

func StorageTypePtr(v StorageType) *StorageType { return &v }

func (p StorageType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *StorageType) UnmarshalText(text []byte) error {
	q, err := StorageTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ComputeSpecType int64

const (
	ComputeSpecType_Shared     ComputeSpecType = 0
	ComputeSpecType_Exclusive  ComputeSpecType = 1
	ComputeSpecType_Serverless ComputeSpecType = 2
)

func (p ComputeSpecType) String() string {
	switch p {
	case ComputeSpecType_Shared:
		return "Shared"
	case ComputeSpecType_Exclusive:
		return "Exclusive"
	case ComputeSpecType_Serverless:
		return "Serverless"
	}
	return "<UNSET>"
}

func ComputeSpecTypeFromString(s string) (ComputeSpecType, error) {
	switch s {
	case "Shared":
		return ComputeSpecType_Shared, nil
	case "Exclusive":
		return ComputeSpecType_Exclusive, nil
	case "Serverless":
		return ComputeSpecType_Serverless, nil
	}
	return ComputeSpecType(0), fmt.Errorf("not a valid ComputeSpecType string")
}

func ComputeSpecTypePtr(v ComputeSpecType) *ComputeSpecType { return &v }

func (p ComputeSpecType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ComputeSpecType) UnmarshalText(text []byte) error {
	q, err := ComputeSpecTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ResourceType int64

const (
	ResourceType_Instance ResourceType = 0
	ResourceType_Branch   ResourceType = 1
	ResourceType_Compute  ResourceType = 2
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_Instance:
		return "Instance"
	case ResourceType_Branch:
		return "Branch"
	case ResourceType_Compute:
		return "Compute"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "Instance":
		return ResourceType_Instance, nil
	case "Branch":
		return ResourceType_Branch, nil
	case "Compute":
		return ResourceType_Compute, nil
	}
	return ResourceType(0), fmt.Errorf("not a valid ResourceType string")
}

func ResourceTypePtr(v ResourceType) *ResourceType { return &v }

func (p ResourceType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ResourceType) UnmarshalText(text []byte) error {
	q, err := ResourceTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SortOrder int64

const (
	SortOrder_Desc SortOrder = 0
	SortOrder_Asc  SortOrder = 1
)

func (p SortOrder) String() string {
	switch p {
	case SortOrder_Desc:
		return "Desc"
	case SortOrder_Asc:
		return "Asc"
	}
	return "<UNSET>"
}

func SortOrderFromString(s string) (SortOrder, error) {
	switch s {
	case "Desc":
		return SortOrder_Desc, nil
	case "Asc":
		return SortOrder_Asc, nil
	}
	return SortOrder(0), fmt.Errorf("not a valid SortOrder string")
}

func SortOrderPtr(v SortOrder) *SortOrder { return &v }

func (p SortOrder) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SortOrder) UnmarshalText(text []byte) error {
	q, err := SortOrderFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type QueryMode int64

const (
	QueryMode_Fuzzy QueryMode = 0
	QueryMode_Exact QueryMode = 1
)

func (p QueryMode) String() string {
	switch p {
	case QueryMode_Fuzzy:
		return "Fuzzy"
	case QueryMode_Exact:
		return "Exact"
	}
	return "<UNSET>"
}

func QueryModeFromString(s string) (QueryMode, error) {
	switch s {
	case "Fuzzy":
		return QueryMode_Fuzzy, nil
	case "Exact":
		return QueryMode_Exact, nil
	}
	return QueryMode(0), fmt.Errorf("not a valid QueryMode string")
}

func QueryModePtr(v QueryMode) *QueryMode { return &v }

func (p QueryMode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *QueryMode) UnmarshalText(text []byte) error {
	q, err := QueryModeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CreationSource int64

const (
	CreationSource_Unknown   CreationSource = 0
	CreationSource_Console   CreationSource = 1
	CreationSource_SDK       CreationSource = 2
	CreationSource_CLI       CreationSource = 3
	CreationSource_Terraform CreationSource = 4
	CreationSource_MCP       CreationSource = 5
	CreationSource_Trae      CreationSource = 6
	CreationSource_Coze      CreationSource = 7
	CreationSource_AIPA      CreationSource = 8
)

func (p CreationSource) String() string {
	switch p {
	case CreationSource_Unknown:
		return "Unknown"
	case CreationSource_Console:
		return "Console"
	case CreationSource_SDK:
		return "SDK"
	case CreationSource_CLI:
		return "CLI"
	case CreationSource_Terraform:
		return "Terraform"
	case CreationSource_MCP:
		return "MCP"
	case CreationSource_Trae:
		return "Trae"
	case CreationSource_Coze:
		return "Coze"
	case CreationSource_AIPA:
		return "AIPA"
	}
	return "<UNSET>"
}

func CreationSourceFromString(s string) (CreationSource, error) {
	switch s {
	case "Unknown":
		return CreationSource_Unknown, nil
	case "Console":
		return CreationSource_Console, nil
	case "SDK":
		return CreationSource_SDK, nil
	case "CLI":
		return CreationSource_CLI, nil
	case "Terraform":
		return CreationSource_Terraform, nil
	case "MCP":
		return CreationSource_MCP, nil
	case "Trae":
		return CreationSource_Trae, nil
	case "Coze":
		return CreationSource_Coze, nil
	case "AIPA":
		return CreationSource_AIPA, nil
	}
	return CreationSource(0), fmt.Errorf("not a valid CreationSource string")
}

func CreationSourcePtr(v CreationSource) *CreationSource { return &v }

func (p CreationSource) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CreationSource) UnmarshalText(text []byte) error {
	q, err := CreationSourceFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type Tag struct {
	Key   string  `thrift:"Key,1,required" frugal:"1,required,string" json:"Key"`
	Value *string `thrift:"Value,2,optional" frugal:"2,optional,string" json:"Value,omitempty"`
}

func NewTag() *Tag {
	return &Tag{}
}

func (p *Tag) InitDefault() {
}

func (p *Tag) GetKey() (v string) {
	return p.Key
}

var Tag_Value_DEFAULT string

func (p *Tag) GetValue() (v string) {
	if !p.IsSetValue() {
		return Tag_Value_DEFAULT
	}
	return *p.Value
}
func (p *Tag) SetKey(val string) {
	p.Key = val
}
func (p *Tag) SetValue(val *string) {
	p.Value = val
}

var fieldIDToName_Tag = map[int16]string{
	1: "Key",
	2: "Value",
}

func (p *Tag) IsSetValue() bool {
	return p.Value != nil
}

func (p *Tag) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Tag")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKey bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKey {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Tag[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Tag[fieldId]))
}

func (p *Tag) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Key = _field
	return nil
}
func (p *Tag) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *Tag) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Tag")

	var fieldId int16
	if err = oprot.WriteStructBegin("Tag"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Tag) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Key); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Tag) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Tag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tag(%+v)", *p)

}

func (p *Tag) DeepEqual(ano *Tag) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Key) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *Tag) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Key, src) != 0 {
		return false
	}
	return true
}
func (p *Tag) Field2DeepEqual(src *string) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Value, *src) != 0 {
		return false
	}
	return true
}

type QueryFilter struct {
	Name  string     `thrift:"Name,1,required" frugal:"1,required,string" json:"Name"`
	Value string     `thrift:"Value,2,required" frugal:"2,required,string" json:"Value"`
	Mode  *QueryMode `thrift:"Mode,3,optional" frugal:"3,optional,QueryMode" json:"Mode,omitempty"`
}

func NewQueryFilter() *QueryFilter {
	return &QueryFilter{}
}

func (p *QueryFilter) InitDefault() {
}

func (p *QueryFilter) GetName() (v string) {
	return p.Name
}

func (p *QueryFilter) GetValue() (v string) {
	return p.Value
}

var QueryFilter_Mode_DEFAULT QueryMode

func (p *QueryFilter) GetMode() (v QueryMode) {
	if !p.IsSetMode() {
		return QueryFilter_Mode_DEFAULT
	}
	return *p.Mode
}
func (p *QueryFilter) SetName(val string) {
	p.Name = val
}
func (p *QueryFilter) SetValue(val string) {
	p.Value = val
}
func (p *QueryFilter) SetMode(val *QueryMode) {
	p.Mode = val
}

var fieldIDToName_QueryFilter = map[int16]string{
	1: "Name",
	2: "Value",
	3: "Mode",
}

func (p *QueryFilter) IsSetMode() bool {
	return p.Mode != nil
}

func (p *QueryFilter) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryFilter")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetValue bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetValue = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetValue {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFilter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_QueryFilter[fieldId]))
}

func (p *QueryFilter) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *QueryFilter) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *QueryFilter) ReadField3(iprot thrift.TProtocol) error {

	var _field *QueryMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryMode(v)
		_field = &tmp
	}
	p.Mode = _field
	return nil
}

func (p *QueryFilter) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryFilter")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFilter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *QueryFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *QueryFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMode() {
		if err = oprot.WriteFieldBegin("Mode", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Mode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *QueryFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFilter(%+v)", *p)

}

func (p *QueryFilter) DeepEqual(ano *QueryFilter) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	if !p.Field3DeepEqual(ano.Mode) {
		return false
	}
	return true
}

func (p *QueryFilter) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *QueryFilter) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Value, src) != 0 {
		return false
	}
	return true
}
func (p *QueryFilter) Field3DeepEqual(src *QueryMode) bool {

	if p.Mode == src {
		return true
	} else if p.Mode == nil || src == nil {
		return false
	}
	if *p.Mode != *src {
		return false
	}
	return true
}

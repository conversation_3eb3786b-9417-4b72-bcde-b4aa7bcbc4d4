// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ComputeStatus int64

const (
	ComputeStatus_Unknown    ComputeStatus = 0
	ComputeStatus_Init       ComputeStatus = 1
	ComputeStatus_Activating ComputeStatus = 2
	ComputeStatus_Active     ComputeStatus = 3
	ComputeStatus_Idle       ComputeStatus = 4
)

func (p ComputeStatus) String() string {
	switch p {
	case ComputeStatus_Unknown:
		return "Unknown"
	case ComputeStatus_Init:
		return "Init"
	case ComputeStatus_Activating:
		return "Activating"
	case ComputeStatus_Active:
		return "Active"
	case ComputeStatus_Idle:
		return "Idle"
	}
	return "<UNSET>"
}

func ComputeStatusFromString(s string) (ComputeStatus, error) {
	switch s {
	case "Unknown":
		return ComputeStatus_Unknown, nil
	case "Init":
		return ComputeStatus_Init, nil
	case "Activating":
		return ComputeStatus_Activating, nil
	case "Active":
		return ComputeStatus_Active, nil
	case "Idle":
		return ComputeStatus_Idle, nil
	}
	return ComputeStatus(0), fmt.Errorf("not a valid ComputeStatus string")
}

func ComputeStatusPtr(v ComputeStatus) *ComputeStatus { return &v }

func (p ComputeStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ComputeStatus) UnmarshalText(text []byte) error {
	q, err := ComputeStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ComputeRole int64

const (
	ComputeRole_Unknown   ComputeRole = 0
	ComputeRole_ReadOnly  ComputeRole = 1
	ComputeRole_ReadWrite ComputeRole = 2
	ComputeRole_Secondary ComputeRole = 3
)

func (p ComputeRole) String() string {
	switch p {
	case ComputeRole_Unknown:
		return "Unknown"
	case ComputeRole_ReadOnly:
		return "ReadOnly"
	case ComputeRole_ReadWrite:
		return "ReadWrite"
	case ComputeRole_Secondary:
		return "Secondary"
	}
	return "<UNSET>"
}

func ComputeRoleFromString(s string) (ComputeRole, error) {
	switch s {
	case "Unknown":
		return ComputeRole_Unknown, nil
	case "ReadOnly":
		return ComputeRole_ReadOnly, nil
	case "ReadWrite":
		return ComputeRole_ReadWrite, nil
	case "Secondary":
		return ComputeRole_Secondary, nil
	}
	return ComputeRole(0), fmt.Errorf("not a valid ComputeRole string")
}

func ComputeRolePtr(v ComputeRole) *ComputeRole { return &v }

func (p ComputeRole) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ComputeRole) UnmarshalText(text []byte) error {
	q, err := ComputeRoleFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ComputeSettings struct {
	ComputeRole           ComputeRole `thrift:"ComputeRole,1,required" frugal:"1,required,ComputeRole" json:"ComputeRole"`
	AutoScalingLimitMinCU *float64    `thrift:"AutoScalingLimitMinCU,2,optional" frugal:"2,optional,double" json:"AutoScalingLimitMinCU,omitempty"`
	AutoScalingLimitMaxCU *float64    `thrift:"AutoScalingLimitMaxCU,3,optional" frugal:"3,optional,double" json:"AutoScalingLimitMaxCU,omitempty"`
	SuspendTimeoutSeconds *int32      `thrift:"SuspendTimeoutSeconds,4,optional" frugal:"4,optional,i32" json:"SuspendTimeoutSeconds,omitempty"`
	ComputeSpecCode       *string     `thrift:"ComputeSpecCode,5,optional" frugal:"5,optional,string" json:"ComputeSpecCode,omitempty"`
	ZoneId                *string     `thrift:"ZoneId,6,optional" frugal:"6,optional,string" json:"ZoneId,omitempty"`
}

func NewComputeSettings() *ComputeSettings {
	return &ComputeSettings{}
}

func (p *ComputeSettings) InitDefault() {
}

func (p *ComputeSettings) GetComputeRole() (v ComputeRole) {
	return p.ComputeRole
}

var ComputeSettings_AutoScalingLimitMinCU_DEFAULT float64

func (p *ComputeSettings) GetAutoScalingLimitMinCU() (v float64) {
	if !p.IsSetAutoScalingLimitMinCU() {
		return ComputeSettings_AutoScalingLimitMinCU_DEFAULT
	}
	return *p.AutoScalingLimitMinCU
}

var ComputeSettings_AutoScalingLimitMaxCU_DEFAULT float64

func (p *ComputeSettings) GetAutoScalingLimitMaxCU() (v float64) {
	if !p.IsSetAutoScalingLimitMaxCU() {
		return ComputeSettings_AutoScalingLimitMaxCU_DEFAULT
	}
	return *p.AutoScalingLimitMaxCU
}

var ComputeSettings_SuspendTimeoutSeconds_DEFAULT int32

func (p *ComputeSettings) GetSuspendTimeoutSeconds() (v int32) {
	if !p.IsSetSuspendTimeoutSeconds() {
		return ComputeSettings_SuspendTimeoutSeconds_DEFAULT
	}
	return *p.SuspendTimeoutSeconds
}

var ComputeSettings_ComputeSpecCode_DEFAULT string

func (p *ComputeSettings) GetComputeSpecCode() (v string) {
	if !p.IsSetComputeSpecCode() {
		return ComputeSettings_ComputeSpecCode_DEFAULT
	}
	return *p.ComputeSpecCode
}

var ComputeSettings_ZoneId_DEFAULT string

func (p *ComputeSettings) GetZoneId() (v string) {
	if !p.IsSetZoneId() {
		return ComputeSettings_ZoneId_DEFAULT
	}
	return *p.ZoneId
}
func (p *ComputeSettings) SetComputeRole(val ComputeRole) {
	p.ComputeRole = val
}
func (p *ComputeSettings) SetAutoScalingLimitMinCU(val *float64) {
	p.AutoScalingLimitMinCU = val
}
func (p *ComputeSettings) SetAutoScalingLimitMaxCU(val *float64) {
	p.AutoScalingLimitMaxCU = val
}
func (p *ComputeSettings) SetSuspendTimeoutSeconds(val *int32) {
	p.SuspendTimeoutSeconds = val
}
func (p *ComputeSettings) SetComputeSpecCode(val *string) {
	p.ComputeSpecCode = val
}
func (p *ComputeSettings) SetZoneId(val *string) {
	p.ZoneId = val
}

var fieldIDToName_ComputeSettings = map[int16]string{
	1: "ComputeRole",
	2: "AutoScalingLimitMinCU",
	3: "AutoScalingLimitMaxCU",
	4: "SuspendTimeoutSeconds",
	5: "ComputeSpecCode",
	6: "ZoneId",
}

func (p *ComputeSettings) IsSetAutoScalingLimitMinCU() bool {
	return p.AutoScalingLimitMinCU != nil
}

func (p *ComputeSettings) IsSetAutoScalingLimitMaxCU() bool {
	return p.AutoScalingLimitMaxCU != nil
}

func (p *ComputeSettings) IsSetSuspendTimeoutSeconds() bool {
	return p.SuspendTimeoutSeconds != nil
}

func (p *ComputeSettings) IsSetComputeSpecCode() bool {
	return p.ComputeSpecCode != nil
}

func (p *ComputeSettings) IsSetZoneId() bool {
	return p.ZoneId != nil
}

func (p *ComputeSettings) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ComputeSettings")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetComputeRole bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeRole = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetComputeRole {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ComputeSettings[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ComputeSettings[fieldId]))
}

func (p *ComputeSettings) ReadField1(iprot thrift.TProtocol) error {

	var _field ComputeRole
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ComputeRole(v)
	}
	p.ComputeRole = _field
	return nil
}
func (p *ComputeSettings) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMinCU = _field
	return nil
}
func (p *ComputeSettings) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMaxCU = _field
	return nil
}
func (p *ComputeSettings) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuspendTimeoutSeconds = _field
	return nil
}
func (p *ComputeSettings) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ComputeSpecCode = _field
	return nil
}
func (p *ComputeSettings) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ZoneId = _field
	return nil
}

func (p *ComputeSettings) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ComputeSettings")

	var fieldId int16
	if err = oprot.WriteStructBegin("ComputeSettings"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ComputeSettings) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeRole", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ComputeRole)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ComputeSettings) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMinCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMinCU", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMinCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ComputeSettings) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMaxCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMaxCU", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMaxCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ComputeSettings) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuspendTimeoutSeconds() {
		if err = oprot.WriteFieldBegin("SuspendTimeoutSeconds", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SuspendTimeoutSeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ComputeSettings) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSpecCode() {
		if err = oprot.WriteFieldBegin("ComputeSpecCode", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ComputeSpecCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ComputeSettings) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetZoneId() {
		if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ZoneId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ComputeSettings) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ComputeSettings(%+v)", *p)

}

func (p *ComputeSettings) DeepEqual(ano *ComputeSettings) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ComputeRole) {
		return false
	}
	if !p.Field2DeepEqual(ano.AutoScalingLimitMinCU) {
		return false
	}
	if !p.Field3DeepEqual(ano.AutoScalingLimitMaxCU) {
		return false
	}
	if !p.Field4DeepEqual(ano.SuspendTimeoutSeconds) {
		return false
	}
	if !p.Field5DeepEqual(ano.ComputeSpecCode) {
		return false
	}
	if !p.Field6DeepEqual(ano.ZoneId) {
		return false
	}
	return true
}

func (p *ComputeSettings) Field1DeepEqual(src ComputeRole) bool {

	if p.ComputeRole != src {
		return false
	}
	return true
}
func (p *ComputeSettings) Field2DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMinCU == src {
		return true
	} else if p.AutoScalingLimitMinCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMinCU != *src {
		return false
	}
	return true
}
func (p *ComputeSettings) Field3DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMaxCU == src {
		return true
	} else if p.AutoScalingLimitMaxCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMaxCU != *src {
		return false
	}
	return true
}
func (p *ComputeSettings) Field4DeepEqual(src *int32) bool {

	if p.SuspendTimeoutSeconds == src {
		return true
	} else if p.SuspendTimeoutSeconds == nil || src == nil {
		return false
	}
	if *p.SuspendTimeoutSeconds != *src {
		return false
	}
	return true
}
func (p *ComputeSettings) Field5DeepEqual(src *string) bool {

	if p.ComputeSpecCode == src {
		return true
	} else if p.ComputeSpecCode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ComputeSpecCode, *src) != 0 {
		return false
	}
	return true
}
func (p *ComputeSettings) Field6DeepEqual(src *string) bool {

	if p.ZoneId == src {
		return true
	} else if p.ZoneId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ZoneId, *src) != 0 {
		return false
	}
	return true
}

type Compute struct {
	InstanceId            string         `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	BranchId              string         `thrift:"BranchId,2" frugal:"2,default,string" json:"BranchId"`
	ComputeId             string         `thrift:"ComputeId,3" frugal:"3,default,string" json:"ComputeId"`
	ComputeName           string         `thrift:"ComputeName,4" frugal:"4,default,string" json:"ComputeName"`
	ComputeStatus         ComputeStatus  `thrift:"ComputeStatus,5" frugal:"5,default,ComputeStatus" json:"ComputeStatus"`
	ComputeRole           ComputeRole    `thrift:"ComputeRole,6" frugal:"6,default,ComputeRole" json:"ComputeRole"`
	AutoScalingLimitMinCU float64        `thrift:"AutoScalingLimitMinCU,7" frugal:"7,default,double" json:"AutoScalingLimitMinCU"`
	AutoScalingLimitMaxCU float64        `thrift:"AutoScalingLimitMaxCU,8" frugal:"8,default,double" json:"AutoScalingLimitMaxCU"`
	SuspendTimeoutSeconds int32          `thrift:"SuspendTimeoutSeconds,9" frugal:"9,default,i32" json:"SuspendTimeoutSeconds"`
	ComputeSpecCode       string         `thrift:"ComputeSpecCode,10" frugal:"10,default,string" json:"ComputeSpecCode"`
	ZoneId                string         `thrift:"ZoneId,11" frugal:"11,default,string" json:"ZoneId"`
	CreationSource        CreationSource `thrift:"CreationSource,12" frugal:"12,default,CreationSource" json:"CreationSource"`
	CreateTime            string         `thrift:"CreateTime,13" frugal:"13,default,string" json:"CreateTime"`
	UpdateTime            string         `thrift:"UpdateTime,14" frugal:"14,default,string" json:"UpdateTime"`
	SuspendedTime         string         `thrift:"SuspendedTime,15" frugal:"15,default,string" json:"SuspendedTime"`
	StatusChangedTime     string         `thrift:"StatusChangedTime,16" frugal:"16,default,string" json:"StatusChangedTime"`
	LastActiveTime        string         `thrift:"LastActiveTime,17" frugal:"17,default,string" json:"LastActiveTime"`
	Disabled              bool           `thrift:"Disabled,18" frugal:"18,default,bool" json:"Disabled"`
	ComputeGroupId        string         `thrift:"ComputeGroupId,19" frugal:"19,default,string" json:"ComputeGroupId"`
	ComputeUrl            string         `thrift:"ComputeUrl,20" frugal:"20,default,string" json:"ComputeUrl"`
}

func NewCompute() *Compute {
	return &Compute{}
}

func (p *Compute) InitDefault() {
}

func (p *Compute) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *Compute) GetBranchId() (v string) {
	return p.BranchId
}

func (p *Compute) GetComputeId() (v string) {
	return p.ComputeId
}

func (p *Compute) GetComputeName() (v string) {
	return p.ComputeName
}

func (p *Compute) GetComputeStatus() (v ComputeStatus) {
	return p.ComputeStatus
}

func (p *Compute) GetComputeRole() (v ComputeRole) {
	return p.ComputeRole
}

func (p *Compute) GetAutoScalingLimitMinCU() (v float64) {
	return p.AutoScalingLimitMinCU
}

func (p *Compute) GetAutoScalingLimitMaxCU() (v float64) {
	return p.AutoScalingLimitMaxCU
}

func (p *Compute) GetSuspendTimeoutSeconds() (v int32) {
	return p.SuspendTimeoutSeconds
}

func (p *Compute) GetComputeSpecCode() (v string) {
	return p.ComputeSpecCode
}

func (p *Compute) GetZoneId() (v string) {
	return p.ZoneId
}

func (p *Compute) GetCreationSource() (v CreationSource) {
	return p.CreationSource
}

func (p *Compute) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *Compute) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *Compute) GetSuspendedTime() (v string) {
	return p.SuspendedTime
}

func (p *Compute) GetStatusChangedTime() (v string) {
	return p.StatusChangedTime
}

func (p *Compute) GetLastActiveTime() (v string) {
	return p.LastActiveTime
}

func (p *Compute) GetDisabled() (v bool) {
	return p.Disabled
}

func (p *Compute) GetComputeGroupId() (v string) {
	return p.ComputeGroupId
}

func (p *Compute) GetComputeUrl() (v string) {
	return p.ComputeUrl
}
func (p *Compute) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *Compute) SetBranchId(val string) {
	p.BranchId = val
}
func (p *Compute) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *Compute) SetComputeName(val string) {
	p.ComputeName = val
}
func (p *Compute) SetComputeStatus(val ComputeStatus) {
	p.ComputeStatus = val
}
func (p *Compute) SetComputeRole(val ComputeRole) {
	p.ComputeRole = val
}
func (p *Compute) SetAutoScalingLimitMinCU(val float64) {
	p.AutoScalingLimitMinCU = val
}
func (p *Compute) SetAutoScalingLimitMaxCU(val float64) {
	p.AutoScalingLimitMaxCU = val
}
func (p *Compute) SetSuspendTimeoutSeconds(val int32) {
	p.SuspendTimeoutSeconds = val
}
func (p *Compute) SetComputeSpecCode(val string) {
	p.ComputeSpecCode = val
}
func (p *Compute) SetZoneId(val string) {
	p.ZoneId = val
}
func (p *Compute) SetCreationSource(val CreationSource) {
	p.CreationSource = val
}
func (p *Compute) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *Compute) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *Compute) SetSuspendedTime(val string) {
	p.SuspendedTime = val
}
func (p *Compute) SetStatusChangedTime(val string) {
	p.StatusChangedTime = val
}
func (p *Compute) SetLastActiveTime(val string) {
	p.LastActiveTime = val
}
func (p *Compute) SetDisabled(val bool) {
	p.Disabled = val
}
func (p *Compute) SetComputeGroupId(val string) {
	p.ComputeGroupId = val
}
func (p *Compute) SetComputeUrl(val string) {
	p.ComputeUrl = val
}

var fieldIDToName_Compute = map[int16]string{
	1:  "InstanceId",
	2:  "BranchId",
	3:  "ComputeId",
	4:  "ComputeName",
	5:  "ComputeStatus",
	6:  "ComputeRole",
	7:  "AutoScalingLimitMinCU",
	8:  "AutoScalingLimitMaxCU",
	9:  "SuspendTimeoutSeconds",
	10: "ComputeSpecCode",
	11: "ZoneId",
	12: "CreationSource",
	13: "CreateTime",
	14: "UpdateTime",
	15: "SuspendedTime",
	16: "StatusChangedTime",
	17: "LastActiveTime",
	18: "Disabled",
	19: "ComputeGroupId",
	20: "ComputeUrl",
}

func (p *Compute) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Compute")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Compute[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Compute) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *Compute) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}
func (p *Compute) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *Compute) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeName = _field
	return nil
}
func (p *Compute) ReadField5(iprot thrift.TProtocol) error {

	var _field ComputeStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ComputeStatus(v)
	}
	p.ComputeStatus = _field
	return nil
}
func (p *Compute) ReadField6(iprot thrift.TProtocol) error {

	var _field ComputeRole
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ComputeRole(v)
	}
	p.ComputeRole = _field
	return nil
}
func (p *Compute) ReadField7(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AutoScalingLimitMinCU = _field
	return nil
}
func (p *Compute) ReadField8(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AutoScalingLimitMaxCU = _field
	return nil
}
func (p *Compute) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SuspendTimeoutSeconds = _field
	return nil
}
func (p *Compute) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeSpecCode = _field
	return nil
}
func (p *Compute) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ZoneId = _field
	return nil
}
func (p *Compute) ReadField12(iprot thrift.TProtocol) error {

	var _field CreationSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CreationSource(v)
	}
	p.CreationSource = _field
	return nil
}
func (p *Compute) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *Compute) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *Compute) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SuspendedTime = _field
	return nil
}
func (p *Compute) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatusChangedTime = _field
	return nil
}
func (p *Compute) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastActiveTime = _field
	return nil
}
func (p *Compute) ReadField18(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Disabled = _field
	return nil
}
func (p *Compute) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeGroupId = _field
	return nil
}
func (p *Compute) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeUrl = _field
	return nil
}

func (p *Compute) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("Compute")

	var fieldId int16
	if err = oprot.WriteStructBegin("Compute"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Compute) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Compute) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Compute) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Compute) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Compute) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeStatus", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ComputeStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Compute) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeRole", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ComputeRole)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Compute) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoScalingLimitMinCU", thrift.DOUBLE, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AutoScalingLimitMinCU); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Compute) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AutoScalingLimitMaxCU", thrift.DOUBLE, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AutoScalingLimitMaxCU); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Compute) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SuspendTimeoutSeconds", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SuspendTimeoutSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Compute) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeSpecCode", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeSpecCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Compute) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ZoneId", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ZoneId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *Compute) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreationSource", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CreationSource)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *Compute) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Compute) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *Compute) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SuspendedTime", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SuspendedTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *Compute) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatusChangedTime", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StatusChangedTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *Compute) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastActiveTime", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastActiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *Compute) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Disabled", thrift.BOOL, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Disabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *Compute) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeGroupId", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeGroupId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *Compute) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeUrl", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeUrl); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *Compute) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Compute(%+v)", *p)

}

func (p *Compute) DeepEqual(ano *Compute) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field4DeepEqual(ano.ComputeName) {
		return false
	}
	if !p.Field5DeepEqual(ano.ComputeStatus) {
		return false
	}
	if !p.Field6DeepEqual(ano.ComputeRole) {
		return false
	}
	if !p.Field7DeepEqual(ano.AutoScalingLimitMinCU) {
		return false
	}
	if !p.Field8DeepEqual(ano.AutoScalingLimitMaxCU) {
		return false
	}
	if !p.Field9DeepEqual(ano.SuspendTimeoutSeconds) {
		return false
	}
	if !p.Field10DeepEqual(ano.ComputeSpecCode) {
		return false
	}
	if !p.Field11DeepEqual(ano.ZoneId) {
		return false
	}
	if !p.Field12DeepEqual(ano.CreationSource) {
		return false
	}
	if !p.Field13DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field14DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field15DeepEqual(ano.SuspendedTime) {
		return false
	}
	if !p.Field16DeepEqual(ano.StatusChangedTime) {
		return false
	}
	if !p.Field17DeepEqual(ano.LastActiveTime) {
		return false
	}
	if !p.Field18DeepEqual(ano.Disabled) {
		return false
	}
	if !p.Field19DeepEqual(ano.ComputeGroupId) {
		return false
	}
	if !p.Field20DeepEqual(ano.ComputeUrl) {
		return false
	}
	return true
}

func (p *Compute) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ComputeName, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field5DeepEqual(src ComputeStatus) bool {

	if p.ComputeStatus != src {
		return false
	}
	return true
}
func (p *Compute) Field6DeepEqual(src ComputeRole) bool {

	if p.ComputeRole != src {
		return false
	}
	return true
}
func (p *Compute) Field7DeepEqual(src float64) bool {

	if p.AutoScalingLimitMinCU != src {
		return false
	}
	return true
}
func (p *Compute) Field8DeepEqual(src float64) bool {

	if p.AutoScalingLimitMaxCU != src {
		return false
	}
	return true
}
func (p *Compute) Field9DeepEqual(src int32) bool {

	if p.SuspendTimeoutSeconds != src {
		return false
	}
	return true
}
func (p *Compute) Field10DeepEqual(src string) bool {

	if strings.Compare(p.ComputeSpecCode, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field11DeepEqual(src string) bool {

	if strings.Compare(p.ZoneId, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field12DeepEqual(src CreationSource) bool {

	if p.CreationSource != src {
		return false
	}
	return true
}
func (p *Compute) Field13DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field14DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field15DeepEqual(src string) bool {

	if strings.Compare(p.SuspendedTime, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field16DeepEqual(src string) bool {

	if strings.Compare(p.StatusChangedTime, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field17DeepEqual(src string) bool {

	if strings.Compare(p.LastActiveTime, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field18DeepEqual(src bool) bool {

	if p.Disabled != src {
		return false
	}
	return true
}
func (p *Compute) Field19DeepEqual(src string) bool {

	if strings.Compare(p.ComputeGroupId, src) != 0 {
		return false
	}
	return true
}
func (p *Compute) Field20DeepEqual(src string) bool {

	if strings.Compare(p.ComputeUrl, src) != 0 {
		return false
	}
	return true
}

type CreateComputeReq struct {
	InstanceId      string            `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId        *string           `thrift:"BranchId,2,optional" frugal:"2,optional,string" json:"BranchId,omitempty"`
	ComputeName     *string           `thrift:"ComputeName,3,optional" frugal:"3,optional,string" json:"ComputeName,omitempty"`
	ComputeSettings *ComputeSettings  `thrift:"ComputeSettings,4,required" frugal:"4,required,ComputeSettings" json:"ComputeSettings"`
	DBEngineParams  map[string]string `thrift:"DBEngineParams,5,optional" frugal:"5,optional,map<string:string>" json:"DBEngineParams,omitempty"`
	DBProxyParams   map[string]string `thrift:"DBProxyParams,6,optional" frugal:"6,optional,map<string:string>" json:"DBProxyParams,omitempty"`
}

func NewCreateComputeReq() *CreateComputeReq {
	return &CreateComputeReq{}
}

func (p *CreateComputeReq) InitDefault() {
}

func (p *CreateComputeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateComputeReq_BranchId_DEFAULT string

func (p *CreateComputeReq) GetBranchId() (v string) {
	if !p.IsSetBranchId() {
		return CreateComputeReq_BranchId_DEFAULT
	}
	return *p.BranchId
}

var CreateComputeReq_ComputeName_DEFAULT string

func (p *CreateComputeReq) GetComputeName() (v string) {
	if !p.IsSetComputeName() {
		return CreateComputeReq_ComputeName_DEFAULT
	}
	return *p.ComputeName
}

var CreateComputeReq_ComputeSettings_DEFAULT *ComputeSettings

func (p *CreateComputeReq) GetComputeSettings() (v *ComputeSettings) {
	if !p.IsSetComputeSettings() {
		return CreateComputeReq_ComputeSettings_DEFAULT
	}
	return p.ComputeSettings
}

var CreateComputeReq_DBEngineParams_DEFAULT map[string]string

func (p *CreateComputeReq) GetDBEngineParams() (v map[string]string) {
	if !p.IsSetDBEngineParams() {
		return CreateComputeReq_DBEngineParams_DEFAULT
	}
	return p.DBEngineParams
}

var CreateComputeReq_DBProxyParams_DEFAULT map[string]string

func (p *CreateComputeReq) GetDBProxyParams() (v map[string]string) {
	if !p.IsSetDBProxyParams() {
		return CreateComputeReq_DBProxyParams_DEFAULT
	}
	return p.DBProxyParams
}
func (p *CreateComputeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateComputeReq) SetBranchId(val *string) {
	p.BranchId = val
}
func (p *CreateComputeReq) SetComputeName(val *string) {
	p.ComputeName = val
}
func (p *CreateComputeReq) SetComputeSettings(val *ComputeSettings) {
	p.ComputeSettings = val
}
func (p *CreateComputeReq) SetDBEngineParams(val map[string]string) {
	p.DBEngineParams = val
}
func (p *CreateComputeReq) SetDBProxyParams(val map[string]string) {
	p.DBProxyParams = val
}

var fieldIDToName_CreateComputeReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "ComputeName",
	4: "ComputeSettings",
	5: "DBEngineParams",
	6: "DBProxyParams",
}

func (p *CreateComputeReq) IsSetBranchId() bool {
	return p.BranchId != nil
}

func (p *CreateComputeReq) IsSetComputeName() bool {
	return p.ComputeName != nil
}

func (p *CreateComputeReq) IsSetComputeSettings() bool {
	return p.ComputeSettings != nil
}

func (p *CreateComputeReq) IsSetDBEngineParams() bool {
	return p.DBEngineParams != nil
}

func (p *CreateComputeReq) IsSetDBProxyParams() bool {
	return p.DBProxyParams != nil
}

func (p *CreateComputeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateComputeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetComputeSettings bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeSettings = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComputeSettings {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateComputeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateComputeReq[fieldId]))
}

func (p *CreateComputeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateComputeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchId = _field
	return nil
}
func (p *CreateComputeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ComputeName = _field
	return nil
}
func (p *CreateComputeReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewComputeSettings()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ComputeSettings = _field
	return nil
}
func (p *CreateComputeReq) ReadField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.DBEngineParams = _field
	return nil
}
func (p *CreateComputeReq) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.DBProxyParams = _field
	return nil
}

func (p *CreateComputeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateComputeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateComputeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateComputeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateComputeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchId() {
		if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateComputeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeName() {
		if err = oprot.WriteFieldBegin("ComputeName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ComputeName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateComputeReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeSettings", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ComputeSettings.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateComputeReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBEngineParams() {
		if err = oprot.WriteFieldBegin("DBEngineParams", thrift.MAP, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DBEngineParams)); err != nil {
			return err
		}
		for k, v := range p.DBEngineParams {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateComputeReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBProxyParams() {
		if err = oprot.WriteFieldBegin("DBProxyParams", thrift.MAP, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DBProxyParams)); err != nil {
			return err
		}
		for k, v := range p.DBProxyParams {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateComputeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateComputeReq(%+v)", *p)

}

func (p *CreateComputeReq) DeepEqual(ano *CreateComputeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeName) {
		return false
	}
	if !p.Field4DeepEqual(ano.ComputeSettings) {
		return false
	}
	if !p.Field5DeepEqual(ano.DBEngineParams) {
		return false
	}
	if !p.Field6DeepEqual(ano.DBProxyParams) {
		return false
	}
	return true
}

func (p *CreateComputeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeReq) Field2DeepEqual(src *string) bool {

	if p.BranchId == src {
		return true
	} else if p.BranchId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeReq) Field3DeepEqual(src *string) bool {

	if p.ComputeName == src {
		return true
	} else if p.ComputeName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ComputeName, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeReq) Field4DeepEqual(src *ComputeSettings) bool {

	if !p.ComputeSettings.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateComputeReq) Field5DeepEqual(src map[string]string) bool {

	if len(p.DBEngineParams) != len(src) {
		return false
	}
	for k, v := range p.DBEngineParams {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CreateComputeReq) Field6DeepEqual(src map[string]string) bool {

	if len(p.DBProxyParams) != len(src) {
		return false
	}
	for k, v := range p.DBProxyParams {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type CreateComputeResp struct {
	InstanceId string   `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	BranchId   string   `thrift:"BranchId,2" frugal:"2,default,string" json:"BranchId"`
	ComputeId  string   `thrift:"ComputeId,3" frugal:"3,default,string" json:"ComputeId"`
	Compute    *Compute `thrift:"Compute,4" frugal:"4,default,Compute" json:"Compute"`
}

func NewCreateComputeResp() *CreateComputeResp {
	return &CreateComputeResp{}
}

func (p *CreateComputeResp) InitDefault() {
}

func (p *CreateComputeResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateComputeResp) GetBranchId() (v string) {
	return p.BranchId
}

func (p *CreateComputeResp) GetComputeId() (v string) {
	return p.ComputeId
}

var CreateComputeResp_Compute_DEFAULT *Compute

func (p *CreateComputeResp) GetCompute() (v *Compute) {
	if !p.IsSetCompute() {
		return CreateComputeResp_Compute_DEFAULT
	}
	return p.Compute
}
func (p *CreateComputeResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateComputeResp) SetBranchId(val string) {
	p.BranchId = val
}
func (p *CreateComputeResp) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *CreateComputeResp) SetCompute(val *Compute) {
	p.Compute = val
}

var fieldIDToName_CreateComputeResp = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
	3: "ComputeId",
	4: "Compute",
}

func (p *CreateComputeResp) IsSetCompute() bool {
	return p.Compute != nil
}

func (p *CreateComputeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateComputeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateComputeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateComputeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateComputeResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BranchId = _field
	return nil
}
func (p *CreateComputeResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *CreateComputeResp) ReadField4(iprot thrift.TProtocol) error {
	_field := NewCompute()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Compute = _field
	return nil
}

func (p *CreateComputeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateComputeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateComputeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateComputeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateComputeResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BranchId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateComputeResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateComputeResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Compute", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Compute.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateComputeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateComputeResp(%+v)", *p)

}

func (p *CreateComputeResp) DeepEqual(ano *CreateComputeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Compute) {
		return false
	}
	return true
}

func (p *CreateComputeResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BranchId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateComputeResp) Field4DeepEqual(src *Compute) bool {

	if !p.Compute.DeepEqual(src) {
		return false
	}
	return true
}

type DescribeComputesReq struct {
	InstanceId string  `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	BranchId   *string `thrift:"BranchId,2,optional" frugal:"2,optional,string" json:"BranchId,omitempty"`
}

func NewDescribeComputesReq() *DescribeComputesReq {
	return &DescribeComputesReq{}
}

func (p *DescribeComputesReq) InitDefault() {
}

func (p *DescribeComputesReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DescribeComputesReq_BranchId_DEFAULT string

func (p *DescribeComputesReq) GetBranchId() (v string) {
	if !p.IsSetBranchId() {
		return DescribeComputesReq_BranchId_DEFAULT
	}
	return *p.BranchId
}
func (p *DescribeComputesReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeComputesReq) SetBranchId(val *string) {
	p.BranchId = val
}

var fieldIDToName_DescribeComputesReq = map[int16]string{
	1: "InstanceId",
	2: "BranchId",
}

func (p *DescribeComputesReq) IsSetBranchId() bool {
	return p.BranchId != nil
}

func (p *DescribeComputesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeComputesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeComputesReq[fieldId]))
}

func (p *DescribeComputesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeComputesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BranchId = _field
	return nil
}

func (p *DescribeComputesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeComputesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeComputesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBranchId() {
		if err = oprot.WriteFieldBegin("BranchId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BranchId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeComputesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeComputesReq(%+v)", *p)

}

func (p *DescribeComputesReq) DeepEqual(ano *DescribeComputesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BranchId) {
		return false
	}
	return true
}

func (p *DescribeComputesReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeComputesReq) Field2DeepEqual(src *string) bool {

	if p.BranchId == src {
		return true
	} else if p.BranchId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BranchId, *src) != 0 {
		return false
	}
	return true
}

type DescribeComputesResp struct {
	Total    int32      `thrift:"Total,1" frugal:"1,default,i32" json:"Total"`
	Computes []*Compute `thrift:"Computes,2" frugal:"2,default,list<Compute>" json:"Computes"`
}

func NewDescribeComputesResp() *DescribeComputesResp {
	return &DescribeComputesResp{}
}

func (p *DescribeComputesResp) InitDefault() {
}

func (p *DescribeComputesResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeComputesResp) GetComputes() (v []*Compute) {
	return p.Computes
}
func (p *DescribeComputesResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeComputesResp) SetComputes(val []*Compute) {
	p.Computes = val
}

var fieldIDToName_DescribeComputesResp = map[int16]string{
	1: "Total",
	2: "Computes",
}

func (p *DescribeComputesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputesResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeComputesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeComputesResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeComputesResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Compute, 0, size)
	values := make([]Compute, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Computes = _field
	return nil
}

func (p *DescribeComputesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeComputesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeComputesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Computes", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Computes)); err != nil {
		return err
	}
	for _, v := range p.Computes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeComputesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeComputesResp(%+v)", *p)

}

func (p *DescribeComputesResp) DeepEqual(ano *DescribeComputesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Total) {
		return false
	}
	if !p.Field2DeepEqual(ano.Computes) {
		return false
	}
	return true
}

func (p *DescribeComputesResp) Field1DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeComputesResp) Field2DeepEqual(src []*Compute) bool {

	if len(p.Computes) != len(src) {
		return false
	}
	for i, v := range p.Computes {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeComputeDetailReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ComputeId  string `thrift:"ComputeId,2,required" frugal:"2,required,string" json:"ComputeId"`
}

func NewDescribeComputeDetailReq() *DescribeComputeDetailReq {
	return &DescribeComputeDetailReq{}
}

func (p *DescribeComputeDetailReq) InitDefault() {
}

func (p *DescribeComputeDetailReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeComputeDetailReq) GetComputeId() (v string) {
	return p.ComputeId
}
func (p *DescribeComputeDetailReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeComputeDetailReq) SetComputeId(val string) {
	p.ComputeId = val
}

var fieldIDToName_DescribeComputeDetailReq = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
}

func (p *DescribeComputeDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputeDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetComputeId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComputeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeComputeDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeComputeDetailReq[fieldId]))
}

func (p *DescribeComputeDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeComputeDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}

func (p *DescribeComputeDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputeDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputeDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeComputeDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeComputeDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeComputeDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeComputeDetailReq(%+v)", *p)

}

func (p *DescribeComputeDetailReq) DeepEqual(ano *DescribeComputeDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	return true
}

func (p *DescribeComputeDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeComputeDetailReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}

type DescribeComputeDetailResp struct {
	Compute *Compute `thrift:"Compute,1" frugal:"1,default,Compute" json:"Compute"`
}

func NewDescribeComputeDetailResp() *DescribeComputeDetailResp {
	return &DescribeComputeDetailResp{}
}

func (p *DescribeComputeDetailResp) InitDefault() {
}

var DescribeComputeDetailResp_Compute_DEFAULT *Compute

func (p *DescribeComputeDetailResp) GetCompute() (v *Compute) {
	if !p.IsSetCompute() {
		return DescribeComputeDetailResp_Compute_DEFAULT
	}
	return p.Compute
}
func (p *DescribeComputeDetailResp) SetCompute(val *Compute) {
	p.Compute = val
}

var fieldIDToName_DescribeComputeDetailResp = map[int16]string{
	1: "Compute",
}

func (p *DescribeComputeDetailResp) IsSetCompute() bool {
	return p.Compute != nil
}

func (p *DescribeComputeDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputeDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeComputeDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeComputeDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCompute()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Compute = _field
	return nil
}

func (p *DescribeComputeDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeComputeDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeComputeDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeComputeDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Compute", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Compute.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeComputeDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeComputeDetailResp(%+v)", *p)

}

func (p *DescribeComputeDetailResp) DeepEqual(ano *DescribeComputeDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Compute) {
		return false
	}
	return true
}

func (p *DescribeComputeDetailResp) Field1DeepEqual(src *Compute) bool {

	if !p.Compute.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteComputeReq struct {
	InstanceId string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ComputeId  string `thrift:"ComputeId,2,required" frugal:"2,required,string" json:"ComputeId"`
}

func NewDeleteComputeReq() *DeleteComputeReq {
	return &DeleteComputeReq{}
}

func (p *DeleteComputeReq) InitDefault() {
}

func (p *DeleteComputeReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteComputeReq) GetComputeId() (v string) {
	return p.ComputeId
}
func (p *DeleteComputeReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteComputeReq) SetComputeId(val string) {
	p.ComputeId = val
}

var fieldIDToName_DeleteComputeReq = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
}

func (p *DeleteComputeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteComputeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetComputeId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComputeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteComputeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteComputeReq[fieldId]))
}

func (p *DeleteComputeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteComputeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}

func (p *DeleteComputeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteComputeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteComputeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteComputeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteComputeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteComputeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteComputeReq(%+v)", *p)

}

func (p *DeleteComputeReq) DeepEqual(ano *DeleteComputeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	return true
}

func (p *DeleteComputeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteComputeReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}

type DeleteComputeResp struct {
	InstanceId string `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	ComputeId  string `thrift:"ComputeId,2" frugal:"2,default,string" json:"ComputeId"`
}

func NewDeleteComputeResp() *DeleteComputeResp {
	return &DeleteComputeResp{}
}

func (p *DeleteComputeResp) InitDefault() {
}

func (p *DeleteComputeResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteComputeResp) GetComputeId() (v string) {
	return p.ComputeId
}
func (p *DeleteComputeResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteComputeResp) SetComputeId(val string) {
	p.ComputeId = val
}

var fieldIDToName_DeleteComputeResp = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
}

func (p *DeleteComputeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteComputeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteComputeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteComputeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteComputeResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}

func (p *DeleteComputeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteComputeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteComputeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteComputeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteComputeResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteComputeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteComputeResp(%+v)", *p)

}

func (p *DeleteComputeResp) DeepEqual(ano *DeleteComputeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	return true
}

func (p *DeleteComputeResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteComputeResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}

type ModifyComputeNameReq struct {
	InstanceId  string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ComputeId   string `thrift:"ComputeId,2,required" frugal:"2,required,string" json:"ComputeId"`
	ComputeName string `thrift:"ComputeName,3,required" frugal:"3,required,string" json:"ComputeName"`
}

func NewModifyComputeNameReq() *ModifyComputeNameReq {
	return &ModifyComputeNameReq{}
}

func (p *ModifyComputeNameReq) InitDefault() {
}

func (p *ModifyComputeNameReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyComputeNameReq) GetComputeId() (v string) {
	return p.ComputeId
}

func (p *ModifyComputeNameReq) GetComputeName() (v string) {
	return p.ComputeName
}
func (p *ModifyComputeNameReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyComputeNameReq) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *ModifyComputeNameReq) SetComputeName(val string) {
	p.ComputeName = val
}

var fieldIDToName_ModifyComputeNameReq = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
	3: "ComputeName",
}

func (p *ModifyComputeNameReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeNameReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetComputeId bool = false
	var issetComputeName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComputeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetComputeName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeNameReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyComputeNameReq[fieldId]))
}

func (p *ModifyComputeNameReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeNameReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *ModifyComputeNameReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeName = _field
	return nil
}

func (p *ModifyComputeNameReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeNameReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeNameReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeNameReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeNameReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeNameReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyComputeNameReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeNameReq(%+v)", *p)

}

func (p *ModifyComputeNameReq) DeepEqual(ano *ModifyComputeNameReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeName) {
		return false
	}
	return true
}

func (p *ModifyComputeNameReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeNameReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeNameReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ComputeName, src) != 0 {
		return false
	}
	return true
}

type ModifyComputeNameResp struct {
	InstanceId  string `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	ComputeId   string `thrift:"ComputeId,2" frugal:"2,default,string" json:"ComputeId"`
	ComputeName string `thrift:"ComputeName,3" frugal:"3,default,string" json:"ComputeName"`
}

func NewModifyComputeNameResp() *ModifyComputeNameResp {
	return &ModifyComputeNameResp{}
}

func (p *ModifyComputeNameResp) InitDefault() {
}

func (p *ModifyComputeNameResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyComputeNameResp) GetComputeId() (v string) {
	return p.ComputeId
}

func (p *ModifyComputeNameResp) GetComputeName() (v string) {
	return p.ComputeName
}
func (p *ModifyComputeNameResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyComputeNameResp) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *ModifyComputeNameResp) SetComputeName(val string) {
	p.ComputeName = val
}

var fieldIDToName_ModifyComputeNameResp = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
	3: "ComputeName",
}

func (p *ModifyComputeNameResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeNameResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeNameResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyComputeNameResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeNameResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *ModifyComputeNameResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeName = _field
	return nil
}

func (p *ModifyComputeNameResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeNameResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeNameResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeNameResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeNameResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeNameResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyComputeNameResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeNameResp(%+v)", *p)

}

func (p *ModifyComputeNameResp) DeepEqual(ano *ModifyComputeNameResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ComputeName) {
		return false
	}
	return true
}

func (p *ModifyComputeNameResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeNameResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeNameResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ComputeName, src) != 0 {
		return false
	}
	return true
}

type ModifyComputeSpecReq struct {
	InstanceId            string   `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	ComputeId             string   `thrift:"ComputeId,2,required" frugal:"2,required,string" json:"ComputeId"`
	AutoScalingLimitMinCU *float64 `thrift:"AutoScalingLimitMinCU,3,optional" frugal:"3,optional,double" json:"AutoScalingLimitMinCU,omitempty"`
	AutoScalingLimitMaxCU *float64 `thrift:"AutoScalingLimitMaxCU,4,optional" frugal:"4,optional,double" json:"AutoScalingLimitMaxCU,omitempty"`
	SuspendTimeoutSeconds *int32   `thrift:"SuspendTimeoutSeconds,5,optional" frugal:"5,optional,i32" json:"SuspendTimeoutSeconds,omitempty"`
	ComputeSpecCode       *string  `thrift:"ComputeSpecCode,6,optional" frugal:"6,optional,string" json:"ComputeSpecCode,omitempty"`
}

func NewModifyComputeSpecReq() *ModifyComputeSpecReq {
	return &ModifyComputeSpecReq{}
}

func (p *ModifyComputeSpecReq) InitDefault() {
}

func (p *ModifyComputeSpecReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyComputeSpecReq) GetComputeId() (v string) {
	return p.ComputeId
}

var ModifyComputeSpecReq_AutoScalingLimitMinCU_DEFAULT float64

func (p *ModifyComputeSpecReq) GetAutoScalingLimitMinCU() (v float64) {
	if !p.IsSetAutoScalingLimitMinCU() {
		return ModifyComputeSpecReq_AutoScalingLimitMinCU_DEFAULT
	}
	return *p.AutoScalingLimitMinCU
}

var ModifyComputeSpecReq_AutoScalingLimitMaxCU_DEFAULT float64

func (p *ModifyComputeSpecReq) GetAutoScalingLimitMaxCU() (v float64) {
	if !p.IsSetAutoScalingLimitMaxCU() {
		return ModifyComputeSpecReq_AutoScalingLimitMaxCU_DEFAULT
	}
	return *p.AutoScalingLimitMaxCU
}

var ModifyComputeSpecReq_SuspendTimeoutSeconds_DEFAULT int32

func (p *ModifyComputeSpecReq) GetSuspendTimeoutSeconds() (v int32) {
	if !p.IsSetSuspendTimeoutSeconds() {
		return ModifyComputeSpecReq_SuspendTimeoutSeconds_DEFAULT
	}
	return *p.SuspendTimeoutSeconds
}

var ModifyComputeSpecReq_ComputeSpecCode_DEFAULT string

func (p *ModifyComputeSpecReq) GetComputeSpecCode() (v string) {
	if !p.IsSetComputeSpecCode() {
		return ModifyComputeSpecReq_ComputeSpecCode_DEFAULT
	}
	return *p.ComputeSpecCode
}
func (p *ModifyComputeSpecReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyComputeSpecReq) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *ModifyComputeSpecReq) SetAutoScalingLimitMinCU(val *float64) {
	p.AutoScalingLimitMinCU = val
}
func (p *ModifyComputeSpecReq) SetAutoScalingLimitMaxCU(val *float64) {
	p.AutoScalingLimitMaxCU = val
}
func (p *ModifyComputeSpecReq) SetSuspendTimeoutSeconds(val *int32) {
	p.SuspendTimeoutSeconds = val
}
func (p *ModifyComputeSpecReq) SetComputeSpecCode(val *string) {
	p.ComputeSpecCode = val
}

var fieldIDToName_ModifyComputeSpecReq = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
	3: "AutoScalingLimitMinCU",
	4: "AutoScalingLimitMaxCU",
	5: "SuspendTimeoutSeconds",
	6: "ComputeSpecCode",
}

func (p *ModifyComputeSpecReq) IsSetAutoScalingLimitMinCU() bool {
	return p.AutoScalingLimitMinCU != nil
}

func (p *ModifyComputeSpecReq) IsSetAutoScalingLimitMaxCU() bool {
	return p.AutoScalingLimitMaxCU != nil
}

func (p *ModifyComputeSpecReq) IsSetSuspendTimeoutSeconds() bool {
	return p.SuspendTimeoutSeconds != nil
}

func (p *ModifyComputeSpecReq) IsSetComputeSpecCode() bool {
	return p.ComputeSpecCode != nil
}

func (p *ModifyComputeSpecReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSpecReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetComputeId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetComputeId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetComputeId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSpecReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyComputeSpecReq[fieldId]))
}

func (p *ModifyComputeSpecReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeSpecReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *ModifyComputeSpecReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMinCU = _field
	return nil
}
func (p *ModifyComputeSpecReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AutoScalingLimitMaxCU = _field
	return nil
}
func (p *ModifyComputeSpecReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuspendTimeoutSeconds = _field
	return nil
}
func (p *ModifyComputeSpecReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ComputeSpecCode = _field
	return nil
}

func (p *ModifyComputeSpecReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSpecReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSpecReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMinCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMinCU", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMinCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAutoScalingLimitMaxCU() {
		if err = oprot.WriteFieldBegin("AutoScalingLimitMaxCU", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.AutoScalingLimitMaxCU); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuspendTimeoutSeconds() {
		if err = oprot.WriteFieldBegin("SuspendTimeoutSeconds", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SuspendTimeoutSeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetComputeSpecCode() {
		if err = oprot.WriteFieldBegin("ComputeSpecCode", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ComputeSpecCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifyComputeSpecReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeSpecReq(%+v)", *p)

}

func (p *ModifyComputeSpecReq) DeepEqual(ano *ModifyComputeSpecReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.AutoScalingLimitMinCU) {
		return false
	}
	if !p.Field4DeepEqual(ano.AutoScalingLimitMaxCU) {
		return false
	}
	if !p.Field5DeepEqual(ano.SuspendTimeoutSeconds) {
		return false
	}
	if !p.Field6DeepEqual(ano.ComputeSpecCode) {
		return false
	}
	return true
}

func (p *ModifyComputeSpecReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSpecReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSpecReq) Field3DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMinCU == src {
		return true
	} else if p.AutoScalingLimitMinCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMinCU != *src {
		return false
	}
	return true
}
func (p *ModifyComputeSpecReq) Field4DeepEqual(src *float64) bool {

	if p.AutoScalingLimitMaxCU == src {
		return true
	} else if p.AutoScalingLimitMaxCU == nil || src == nil {
		return false
	}
	if *p.AutoScalingLimitMaxCU != *src {
		return false
	}
	return true
}
func (p *ModifyComputeSpecReq) Field5DeepEqual(src *int32) bool {

	if p.SuspendTimeoutSeconds == src {
		return true
	} else if p.SuspendTimeoutSeconds == nil || src == nil {
		return false
	}
	if *p.SuspendTimeoutSeconds != *src {
		return false
	}
	return true
}
func (p *ModifyComputeSpecReq) Field6DeepEqual(src *string) bool {

	if p.ComputeSpecCode == src {
		return true
	} else if p.ComputeSpecCode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ComputeSpecCode, *src) != 0 {
		return false
	}
	return true
}

type ModifyComputeSpecResp struct {
	InstanceId string   `thrift:"InstanceId,1" frugal:"1,default,string" json:"InstanceId"`
	ComputeId  string   `thrift:"ComputeId,2" frugal:"2,default,string" json:"ComputeId"`
	Compute    *Compute `thrift:"Compute,3" frugal:"3,default,Compute" json:"Compute"`
}

func NewModifyComputeSpecResp() *ModifyComputeSpecResp {
	return &ModifyComputeSpecResp{}
}

func (p *ModifyComputeSpecResp) InitDefault() {
}

func (p *ModifyComputeSpecResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifyComputeSpecResp) GetComputeId() (v string) {
	return p.ComputeId
}

var ModifyComputeSpecResp_Compute_DEFAULT *Compute

func (p *ModifyComputeSpecResp) GetCompute() (v *Compute) {
	if !p.IsSetCompute() {
		return ModifyComputeSpecResp_Compute_DEFAULT
	}
	return p.Compute
}
func (p *ModifyComputeSpecResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifyComputeSpecResp) SetComputeId(val string) {
	p.ComputeId = val
}
func (p *ModifyComputeSpecResp) SetCompute(val *Compute) {
	p.Compute = val
}

var fieldIDToName_ModifyComputeSpecResp = map[int16]string{
	1: "InstanceId",
	2: "ComputeId",
	3: "Compute",
}

func (p *ModifyComputeSpecResp) IsSetCompute() bool {
	return p.Compute != nil
}

func (p *ModifyComputeSpecResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSpecResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyComputeSpecResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyComputeSpecResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyComputeSpecResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ComputeId = _field
	return nil
}
func (p *ModifyComputeSpecResp) ReadField3(iprot thrift.TProtocol) error {
	_field := NewCompute()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Compute = _field
	return nil
}

func (p *ModifyComputeSpecResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyComputeSpecResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyComputeSpecResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyComputeSpecResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyComputeSpecResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ComputeId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ComputeId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyComputeSpecResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Compute", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Compute.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyComputeSpecResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyComputeSpecResp(%+v)", *p)

}

func (p *ModifyComputeSpecResp) DeepEqual(ano *ModifyComputeSpecResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ComputeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Compute) {
		return false
	}
	return true
}

func (p *ModifyComputeSpecResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSpecResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ComputeId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyComputeSpecResp) Field3DeepEqual(src *Compute) bool {

	if !p.Compute.DeepEqual(src) {
		return false
	}
	return true
}

// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package modelv1

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

var (
	ErrorCodeMap = map[ErrorCode]*ErrorDetail{
		ErrorCode_ServiceUnavailable: &ErrorDetail{
			HttpStatusCode: 503,
			Message:        "The request has failed due to a temporary failure of the server.",
			MessageZh:      "当前服务暂时不可用。",
		},
		ErrorCode_InternalError: &ErrorDetail{
			HttpStatusCode: 500,
			Message:        "The request processing has failed due to some unknown error, exception or failure.",
			MessageZh:      "内部错误。",
		},
		ErrorCode_ServiceBusy: &ErrorDetail{
			HttpStatusCode: 500,
			Message:        "The system is busy, please try again later.",
			MessageZh:      "系统繁忙，请稍后重试。",
		},
		ErrorCode_MissingParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The input parameter %s that is mandatory for processing this request is not supplied.",
			MessageZh:      "缺少%s参数。",
		},
		ErrorCode_InvalidParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified parameter %s is not valid.",
			MessageZh:      "参数%s值无效。",
		},
		ErrorCode_UnsupportedParameter: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified parameter %s is not support.",
			MessageZh:      "当前参数%s值不支持。",
		},
		ErrorCode_InvalidParameterFormat: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified parameter format is not valid，error detail: %s.",
			MessageZh:      "当前参数值格式错误，错误信息：%s。",
		},
		ErrorCode_UnmarshalParameterError: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "Failed to parse request parameters as JSON.",
			MessageZh:      "解析请求参数为JSON格式失败。",
		},
		ErrorCode_InstanceNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Message:        "The specified instance does not exist.",
			MessageZh:      "未找到该实例。",
		},
		ErrorCode_InvalidInstanceName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The instance name already exists.",
			MessageZh:      "实例名称已存在。",
		},
		ErrorCode_InstanceNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified account's instance number exceeds the limit.",
			MessageZh:      "当前账号的实例个数超过上限。",
		},
		ErrorCode_BranchNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Message:        "The specified branch does not exist.",
			MessageZh:      "未找到该分支。",
		},
		ErrorCode_InvalidBranchName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The branch name already exists.",
			MessageZh:      "分支名称已存在。",
		},
		ErrorCode_BranchNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified instance's branch number exceeds the limit.",
			MessageZh:      "当前实例的分支个数超过上限。",
		},
		ErrorCode_OperationDenied_BranchProtected: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The operation is not permitted due to branch is protected.",
			MessageZh:      "分支为保护分支，拒绝操作。",
		},
		ErrorCode_ComputeNotFound: &ErrorDetail{
			HttpStatusCode: 404,
			Message:        "The specified compute does not exist.",
			MessageZh:      "未找到该计算节点。",
		},
		ErrorCode_InvalidComputeName_Duplicate: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The compute name already exists.",
			MessageZh:      "算力名称已存在。",
		},
		ErrorCode_ComputeNumExceedLimit: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The specified instance's compute number exceeds the limit.",
			MessageZh:      "当前实例的计算节点个数超过上限。",
		},
		ErrorCode_OperationDenied_InstanceStatusNotRunning: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The operation is not permitted due to instance status is not running.",
			MessageZh:      "实例状态不是运行中",
		},
		ErrorCode_OperationDenied_ComputeNumTooLess: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The operation is not permitted due to compute number cannot be less than %s.",
			MessageZh:      "实例计算节点个数不能少于%s个。",
		},
		ErrorCode_AccountResourceStatNotFound: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "Unable to get resource statistics for account %s.",
			MessageZh:      "无法获取到账户%s的资源统计信息。",
		},
		ErrorCode_CreateWorkflowError: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "Create workflow error, actionName=%s.",
			MessageZh:      "创建任务流失败，操作名称=%s。",
		},
		ErrorCode_OperationDenied_InstanceHasResource: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "Instance still has %s resources, operation denied.",
			MessageZh:      "当前实例仍关联有%s资源，拒绝操作。",
		},
		ErrorCode_OperationUnsupported: &ErrorDetail{
			HttpStatusCode: 400,
			Message:        "The operation is unsupported for the specified instance.",
			MessageZh:      "操作不支持。",
		},
	}
)

type ErrorCode int64

const (
	ErrorCode_InternalError                            ErrorCode = 50000
	ErrorCode_ServiceUnavailable                       ErrorCode = 50001
	ErrorCode_ServiceBusy                              ErrorCode = 50002
	ErrorCode_MissingParameter                         ErrorCode = 40001
	ErrorCode_InvalidParameter                         ErrorCode = 40002
	ErrorCode_UnsupportedParameter                     ErrorCode = 40003
	ErrorCode_InvalidParameterFormat                   ErrorCode = 40004
	ErrorCode_UnmarshalParameterError                  ErrorCode = 40005
	ErrorCode_OperationUnsupported                     ErrorCode = 40006
	ErrorCode_InstanceNotFound                         ErrorCode = 40100
	ErrorCode_InstanceExist                            ErrorCode = 40101
	ErrorCode_InvalidInstanceName_Duplicate            ErrorCode = 40102
	ErrorCode_InstanceNumExceedLimit                   ErrorCode = 40103
	ErrorCode_OperationDenied_InstanceStatusNotRunning ErrorCode = 40104
	ErrorCode_OperationDenied_InstanceHasResource      ErrorCode = 40105
	ErrorCode_BranchNotFound                           ErrorCode = 40200
	ErrorCode_InvalidBranchName_Duplicate              ErrorCode = 40201
	ErrorCode_BranchNumExceedLimit                     ErrorCode = 40202
	ErrorCode_OperationDenied_BranchProtected          ErrorCode = 40203
	ErrorCode_ComputeNotFound                          ErrorCode = 40300
	ErrorCode_InvalidComputeName_Duplicate             ErrorCode = 40301
	ErrorCode_ComputeNumExceedLimit                    ErrorCode = 40302
	ErrorCode_OperationDenied_ComputeNumTooLess        ErrorCode = 40303
	ErrorCode_AccountResourceStatNotFound              ErrorCode = 40400
	ErrorCode_CreateWorkflowError                      ErrorCode = 40500
)

func (p ErrorCode) String() string {
	switch p {
	case ErrorCode_InternalError:
		return "InternalError"
	case ErrorCode_ServiceUnavailable:
		return "ServiceUnavailable"
	case ErrorCode_ServiceBusy:
		return "ServiceBusy"
	case ErrorCode_MissingParameter:
		return "MissingParameter"
	case ErrorCode_InvalidParameter:
		return "InvalidParameter"
	case ErrorCode_UnsupportedParameter:
		return "UnsupportedParameter"
	case ErrorCode_InvalidParameterFormat:
		return "InvalidParameterFormat"
	case ErrorCode_UnmarshalParameterError:
		return "UnmarshalParameterError"
	case ErrorCode_OperationUnsupported:
		return "OperationUnsupported"
	case ErrorCode_InstanceNotFound:
		return "InstanceNotFound"
	case ErrorCode_InstanceExist:
		return "InstanceExist"
	case ErrorCode_InvalidInstanceName_Duplicate:
		return "InvalidInstanceName_Duplicate"
	case ErrorCode_InstanceNumExceedLimit:
		return "InstanceNumExceedLimit"
	case ErrorCode_OperationDenied_InstanceStatusNotRunning:
		return "OperationDenied_InstanceStatusNotRunning"
	case ErrorCode_OperationDenied_InstanceHasResource:
		return "OperationDenied_InstanceHasResource"
	case ErrorCode_BranchNotFound:
		return "BranchNotFound"
	case ErrorCode_InvalidBranchName_Duplicate:
		return "InvalidBranchName_Duplicate"
	case ErrorCode_BranchNumExceedLimit:
		return "BranchNumExceedLimit"
	case ErrorCode_OperationDenied_BranchProtected:
		return "OperationDenied_BranchProtected"
	case ErrorCode_ComputeNotFound:
		return "ComputeNotFound"
	case ErrorCode_InvalidComputeName_Duplicate:
		return "InvalidComputeName_Duplicate"
	case ErrorCode_ComputeNumExceedLimit:
		return "ComputeNumExceedLimit"
	case ErrorCode_OperationDenied_ComputeNumTooLess:
		return "OperationDenied_ComputeNumTooLess"
	case ErrorCode_AccountResourceStatNotFound:
		return "AccountResourceStatNotFound"
	case ErrorCode_CreateWorkflowError:
		return "CreateWorkflowError"
	}
	return "<UNSET>"
}

func ErrorCodeFromString(s string) (ErrorCode, error) {
	switch s {
	case "InternalError":
		return ErrorCode_InternalError, nil
	case "ServiceUnavailable":
		return ErrorCode_ServiceUnavailable, nil
	case "ServiceBusy":
		return ErrorCode_ServiceBusy, nil
	case "MissingParameter":
		return ErrorCode_MissingParameter, nil
	case "InvalidParameter":
		return ErrorCode_InvalidParameter, nil
	case "UnsupportedParameter":
		return ErrorCode_UnsupportedParameter, nil
	case "InvalidParameterFormat":
		return ErrorCode_InvalidParameterFormat, nil
	case "UnmarshalParameterError":
		return ErrorCode_UnmarshalParameterError, nil
	case "OperationUnsupported":
		return ErrorCode_OperationUnsupported, nil
	case "InstanceNotFound":
		return ErrorCode_InstanceNotFound, nil
	case "InstanceExist":
		return ErrorCode_InstanceExist, nil
	case "InvalidInstanceName_Duplicate":
		return ErrorCode_InvalidInstanceName_Duplicate, nil
	case "InstanceNumExceedLimit":
		return ErrorCode_InstanceNumExceedLimit, nil
	case "OperationDenied_InstanceStatusNotRunning":
		return ErrorCode_OperationDenied_InstanceStatusNotRunning, nil
	case "OperationDenied_InstanceHasResource":
		return ErrorCode_OperationDenied_InstanceHasResource, nil
	case "BranchNotFound":
		return ErrorCode_BranchNotFound, nil
	case "InvalidBranchName_Duplicate":
		return ErrorCode_InvalidBranchName_Duplicate, nil
	case "BranchNumExceedLimit":
		return ErrorCode_BranchNumExceedLimit, nil
	case "OperationDenied_BranchProtected":
		return ErrorCode_OperationDenied_BranchProtected, nil
	case "ComputeNotFound":
		return ErrorCode_ComputeNotFound, nil
	case "InvalidComputeName_Duplicate":
		return ErrorCode_InvalidComputeName_Duplicate, nil
	case "ComputeNumExceedLimit":
		return ErrorCode_ComputeNumExceedLimit, nil
	case "OperationDenied_ComputeNumTooLess":
		return ErrorCode_OperationDenied_ComputeNumTooLess, nil
	case "AccountResourceStatNotFound":
		return ErrorCode_AccountResourceStatNotFound, nil
	case "CreateWorkflowError":
		return ErrorCode_CreateWorkflowError, nil
	}
	return ErrorCode(0), fmt.Errorf("not a valid ErrorCode string")
}

func ErrorCodePtr(v ErrorCode) *ErrorCode { return &v }

func (p ErrorCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrorCode) UnmarshalText(text []byte) error {
	q, err := ErrorCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrorDetail struct {
	HttpStatusCode int64  `thrift:"HttpStatusCode,1,required" frugal:"1,required,i64" json:"HttpStatusCode"`
	Message        string `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
	MessageZh      string `thrift:"MessageZh,3,required" frugal:"3,required,string" json:"MessageZh"`
}

func NewErrorDetail() *ErrorDetail {
	return &ErrorDetail{}
}

func (p *ErrorDetail) InitDefault() {
}

func (p *ErrorDetail) GetHttpStatusCode() (v int64) {
	return p.HttpStatusCode
}

func (p *ErrorDetail) GetMessage() (v string) {
	return p.Message
}

func (p *ErrorDetail) GetMessageZh() (v string) {
	return p.MessageZh
}
func (p *ErrorDetail) SetHttpStatusCode(val int64) {
	p.HttpStatusCode = val
}
func (p *ErrorDetail) SetMessage(val string) {
	p.Message = val
}
func (p *ErrorDetail) SetMessageZh(val string) {
	p.MessageZh = val
}

var fieldIDToName_ErrorDetail = map[int16]string{
	1: "HttpStatusCode",
	2: "Message",
	3: "MessageZh",
}

func (p *ErrorDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHttpStatusCode bool = false
	var issetMessage bool = false
	var issetMessageZh bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHttpStatusCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageZh = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHttpStatusCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMessageZh {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ErrorDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ErrorDetail[fieldId]))
}

func (p *ErrorDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HttpStatusCode = _field
	return nil
}
func (p *ErrorDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *ErrorDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageZh = _field
	return nil
}

func (p *ErrorDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ErrorDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ErrorDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ErrorDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HttpStatusCode", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.HttpStatusCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ErrorDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ErrorDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageZh", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageZh); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ErrorDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrorDetail(%+v)", *p)

}

func (p *ErrorDetail) DeepEqual(ano *ErrorDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HttpStatusCode) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.MessageZh) {
		return false
	}
	return true
}

func (p *ErrorDetail) Field1DeepEqual(src int64) bool {

	if p.HttpStatusCode != src {
		return false
	}
	return true
}
func (p *ErrorDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *ErrorDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.MessageZh, src) != 0 {
		return false
	}
	return true
}

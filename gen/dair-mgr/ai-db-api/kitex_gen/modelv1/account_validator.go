// Code generated by Validator v0.2.5. DO NOT EDIT.

package modelv1

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DescribeAccountUsageReq) IsValid() error {
	if p.AccountId == nil {
		return fmt.Errorf("field AccountId not_nil rule failed")
	}
	return nil
}
func (p *DescribeAccountUsageResp) IsValid() error {
	if p.AccountUsage != nil {
		if err := p.AccountUsage.IsValid(); err != nil {
			return fmt.Errorf("field AccountUsage not valid, %w", err)
		}
	}
	return nil
}
func (p *AccountUsage) IsValid() error {
	return nil
}

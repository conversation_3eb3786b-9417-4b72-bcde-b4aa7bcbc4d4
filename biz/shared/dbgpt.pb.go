// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: dbgpt.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Action int32

const (
	Generate Action = 0
)

var Action_name = map[int32]string{
	0: "Generate",
}

var Action_value = map[string]int32{
	"Generate": 0,
}

func (Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{0}
}

type SqlAssistantReq struct {
	Query           string   `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`
	Database        string   `protobuf:"bytes,2,opt,name=Database,proto3" json:"Database,omitempty"`
	Tables          []string `protobuf:"bytes,3,rep,name=Tables,proto3" json:"Tables,omitempty"`
	BizContext      string   `protobuf:"bytes,4,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	Action          Action   `protobuf:"varint,5,opt,name=Action,proto3,enum=shared.Action" json:"Action,omitempty"`
	WithExplanation bool     `protobuf:"varint,6,opt,name=WithExplanation,proto3" json:"WithExplanation,omitempty"`
}

func (m *SqlAssistantReq) Reset()      { *m = SqlAssistantReq{} }
func (*SqlAssistantReq) ProtoMessage() {}
func (*SqlAssistantReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{0}
}
func (m *SqlAssistantReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SqlAssistantReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SqlAssistantReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SqlAssistantReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SqlAssistantReq.Merge(m, src)
}
func (m *SqlAssistantReq) XXX_Size() int {
	return m.Size()
}
func (m *SqlAssistantReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SqlAssistantReq.DiscardUnknown(m)
}

var xxx_messageInfo_SqlAssistantReq proto.InternalMessageInfo

func (m *SqlAssistantReq) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *SqlAssistantReq) GetDatabase() string {
	if m != nil {
		return m.Database
	}
	return ""
}

func (m *SqlAssistantReq) GetTables() []string {
	if m != nil {
		return m.Tables
	}
	return nil
}

func (m *SqlAssistantReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *SqlAssistantReq) GetAction() Action {
	if m != nil {
		return m.Action
	}
	return Generate
}

func (m *SqlAssistantReq) GetWithExplanation() bool {
	if m != nil {
		return m.WithExplanation
	}
	return false
}

type SqlAssistantResp struct {
	Answer string `protobuf:"bytes,1,opt,name=Answer,proto3" json:"Answer,omitempty"`
}

func (m *SqlAssistantResp) Reset()      { *m = SqlAssistantResp{} }
func (*SqlAssistantResp) ProtoMessage() {}
func (*SqlAssistantResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{1}
}
func (m *SqlAssistantResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SqlAssistantResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SqlAssistantResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SqlAssistantResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SqlAssistantResp.Merge(m, src)
}
func (m *SqlAssistantResp) XXX_Size() int {
	return m.Size()
}
func (m *SqlAssistantResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SqlAssistantResp.DiscardUnknown(m)
}

var xxx_messageInfo_SqlAssistantResp proto.InternalMessageInfo

func (m *SqlAssistantResp) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

type DescribeDBTablesReq struct {
	Database   string   `protobuf:"bytes,1,opt,name=Database,proto3" json:"Database,omitempty"`
	Tables     []string `protobuf:"bytes,2,rep,name=Tables,proto3" json:"Tables,omitempty"`
	BizContext string   `protobuf:"bytes,3,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *DescribeDBTablesReq) Reset()      { *m = DescribeDBTablesReq{} }
func (*DescribeDBTablesReq) ProtoMessage() {}
func (*DescribeDBTablesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{2}
}
func (m *DescribeDBTablesReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDBTablesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDBTablesReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDBTablesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDBTablesReq.Merge(m, src)
}
func (m *DescribeDBTablesReq) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDBTablesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDBTablesReq.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDBTablesReq proto.InternalMessageInfo

func (m *DescribeDBTablesReq) GetDatabase() string {
	if m != nil {
		return m.Database
	}
	return ""
}

func (m *DescribeDBTablesReq) GetTables() []string {
	if m != nil {
		return m.Tables
	}
	return nil
}

func (m *DescribeDBTablesReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type DescribeDBTablesResp struct {
	DBID   string       `protobuf:"bytes,1,opt,name=DBID,proto3" json:"DBID,omitempty"`
	Tables []*TableMeta `protobuf:"bytes,2,rep,name=Tables,proto3" json:"Tables,omitempty"`
}

func (m *DescribeDBTablesResp) Reset()      { *m = DescribeDBTablesResp{} }
func (*DescribeDBTablesResp) ProtoMessage() {}
func (*DescribeDBTablesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{3}
}
func (m *DescribeDBTablesResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDBTablesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDBTablesResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDBTablesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDBTablesResp.Merge(m, src)
}
func (m *DescribeDBTablesResp) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDBTablesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDBTablesResp.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDBTablesResp proto.InternalMessageInfo

func (m *DescribeDBTablesResp) GetDBID() string {
	if m != nil {
		return m.DBID
	}
	return ""
}

func (m *DescribeDBTablesResp) GetTables() []*TableMeta {
	if m != nil {
		return m.Tables
	}
	return nil
}

type DBMetadata struct {
}

func (m *DBMetadata) Reset()      { *m = DBMetadata{} }
func (*DBMetadata) ProtoMessage() {}
func (*DBMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{4}
}
func (m *DBMetadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DBMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DBMetadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DBMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DBMetadata.Merge(m, src)
}
func (m *DBMetadata) XXX_Size() int {
	return m.Size()
}
func (m *DBMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_DBMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_DBMetadata proto.InternalMessageInfo

type TableMeta struct {
	Name        string        `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Description string        `protobuf:"bytes,2,opt,name=Description,proto3" json:"Description,omitempty"`
	Columns     []*ColumnMeta `protobuf:"bytes,3,rep,name=Columns,proto3" json:"Columns,omitempty"`
	Definition  string        `protobuf:"bytes,4,opt,name=Definition,proto3" json:"Definition,omitempty"`
}

func (m *TableMeta) Reset()      { *m = TableMeta{} }
func (*TableMeta) ProtoMessage() {}
func (*TableMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{5}
}
func (m *TableMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableMeta.Merge(m, src)
}
func (m *TableMeta) XXX_Size() int {
	return m.Size()
}
func (m *TableMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_TableMeta.DiscardUnknown(m)
}

var xxx_messageInfo_TableMeta proto.InternalMessageInfo

func (m *TableMeta) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TableMeta) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *TableMeta) GetColumns() []*ColumnMeta {
	if m != nil {
		return m.Columns
	}
	return nil
}

func (m *TableMeta) GetDefinition() string {
	if m != nil {
		return m.Definition
	}
	return ""
}

type ColumnMeta struct {
	Name        string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Type        string `protobuf:"bytes,2,opt,name=Type,proto3" json:"Type,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=Description,proto3" json:"Description,omitempty"`
	IsPrimary   bool   `protobuf:"varint,4,opt,name=IsPrimary,proto3" json:"IsPrimary,omitempty"`
}

func (m *ColumnMeta) Reset()      { *m = ColumnMeta{} }
func (*ColumnMeta) ProtoMessage() {}
func (*ColumnMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c5e21664cc4d496, []int{6}
}
func (m *ColumnMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ColumnMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ColumnMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ColumnMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ColumnMeta.Merge(m, src)
}
func (m *ColumnMeta) XXX_Size() int {
	return m.Size()
}
func (m *ColumnMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_ColumnMeta.DiscardUnknown(m)
}

var xxx_messageInfo_ColumnMeta proto.InternalMessageInfo

func (m *ColumnMeta) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ColumnMeta) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *ColumnMeta) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *ColumnMeta) GetIsPrimary() bool {
	if m != nil {
		return m.IsPrimary
	}
	return false
}

func init() {
	proto.RegisterEnum("shared.Action", Action_name, Action_value)
	proto.RegisterType((*SqlAssistantReq)(nil), "shared.SqlAssistantReq")
	proto.RegisterType((*SqlAssistantResp)(nil), "shared.SqlAssistantResp")
	proto.RegisterType((*DescribeDBTablesReq)(nil), "shared.DescribeDBTablesReq")
	proto.RegisterType((*DescribeDBTablesResp)(nil), "shared.DescribeDBTablesResp")
	proto.RegisterType((*DBMetadata)(nil), "shared.DBMetadata")
	proto.RegisterType((*TableMeta)(nil), "shared.TableMeta")
	proto.RegisterType((*ColumnMeta)(nil), "shared.ColumnMeta")
}

func init() { proto.RegisterFile("dbgpt.proto", fileDescriptor_9c5e21664cc4d496) }

var fileDescriptor_9c5e21664cc4d496 = []byte{
	// 469 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x53, 0xc1, 0x6e, 0xd3, 0x30,
	0x18, 0x8e, 0xd7, 0x2e, 0xb4, 0x7f, 0xa6, 0x6d, 0xfc, 0x4c, 0x53, 0x84, 0x90, 0x89, 0x72, 0x40,
	0x61, 0x42, 0x3d, 0x14, 0x5e, 0xa0, 0x5d, 0x10, 0xda, 0x01, 0x04, 0x61, 0x88, 0x23, 0x72, 0x5a,
	0xc3, 0x8c, 0xda, 0x24, 0xb3, 0x3d, 0xb1, 0xee, 0xc4, 0x1b, 0xc0, 0x63, 0xf0, 0x28, 0x9c, 0x50,
	0x8f, 0x3b, 0xd2, 0xf4, 0xc2, 0x71, 0x8f, 0x80, 0x6a, 0x27, 0xeb, 0xd6, 0xc1, 0x6e, 0xff, 0xf7,
	0xd9, 0xbf, 0xbf, 0xef, 0xf3, 0x6f, 0x83, 0x37, 0x4c, 0x3f, 0x15, 0xba, 0x53, 0xc8, 0x5c, 0xe7,
	0xe8, 0xaa, 0x23, 0x26, 0xf9, 0x30, 0xfc, 0x45, 0x60, 0xeb, 0xed, 0xf1, 0xa8, 0xa7, 0x94, 0x50,
	0x9a, 0x65, 0x3a, 0xe1, 0xc7, 0xb8, 0x03, 0xeb, 0x6f, 0x4e, 0xb8, 0x9c, 0xf8, 0x24, 0x20, 0x51,
	0x3b, 0xb1, 0x00, 0xef, 0x43, 0x2b, 0x66, 0x9a, 0xa5, 0x4c, 0x71, 0x7f, 0xcd, 0x2c, 0x5c, 0x62,
	0xdc, 0x05, 0xf7, 0x90, 0xa5, 0x23, 0xae, 0xfc, 0x46, 0xd0, 0x88, 0xda, 0x49, 0x85, 0xf0, 0x21,
	0x78, 0xa9, 0x38, 0xfb, 0x30, 0xc8, 0x33, 0xcd, 0x4f, 0xb5, 0xdf, 0x34, 0x6d, 0x90, 0x8a, 0xb3,
	0x7d, 0xcb, 0xe0, 0x23, 0x70, 0x7b, 0x03, 0x2d, 0xf2, 0xcc, 0x5f, 0x0f, 0x48, 0xb4, 0xd9, 0xdd,
	0xec, 0x58, 0x5f, 0x1d, 0xcb, 0x26, 0xd5, 0x2a, 0x46, 0xb0, 0xf5, 0x5e, 0xe8, 0xa3, 0xe7, 0xa7,
	0xc5, 0x88, 0x65, 0xcc, 0x34, 0xb8, 0x01, 0x89, 0x5a, 0xc9, 0x2a, 0x1d, 0xee, 0xc1, 0xf6, 0xf5,
	0x3c, 0xaa, 0x58, 0xd8, 0xeb, 0x65, 0xea, 0x0b, 0x97, 0x55, 0xa2, 0x0a, 0x85, 0x9f, 0xe1, 0x5e,
	0xcc, 0xd5, 0x40, 0x8a, 0x94, 0xc7, 0x7d, 0x6b, 0x79, 0x91, 0xff, 0x6a, 0x52, 0xf2, 0xdf, 0xa4,
	0x6b, 0xb7, 0x25, 0x6d, 0xac, 0x26, 0x0d, 0xdf, 0xc1, 0xce, 0x4d, 0x2d, 0x55, 0x20, 0x42, 0x33,
	0xee, 0x1f, 0xc4, 0x95, 0x90, 0xa9, 0xf1, 0xf1, 0x35, 0x11, 0xaf, 0x7b, 0xb7, 0xbe, 0x15, 0xc3,
	0xbe, 0xe4, 0x9a, 0xd5, 0xba, 0xe1, 0x06, 0x40, 0xdc, 0x5f, 0x30, 0x43, 0xa6, 0x59, 0xf8, 0x8d,
	0x40, 0xfb, 0x72, 0xcf, 0xe2, 0xe8, 0x57, 0x6c, 0x5c, 0x67, 0x30, 0x35, 0x06, 0xe0, 0x59, 0x1b,
	0x85, 0xb9, 0x44, 0x3b, 0xc8, 0xab, 0x14, 0x3e, 0x81, 0x3b, 0xfb, 0xf9, 0xe8, 0x64, 0x9c, 0xd9,
	0x61, 0x7a, 0x5d, 0xac, 0xd5, 0x2d, 0x6d, 0xe4, 0xeb, 0x2d, 0x48, 0x01, 0x62, 0xfe, 0x51, 0x64,
	0xc2, 0x1c, 0x57, 0x0d, 0x78, 0xc9, 0x84, 0x1a, 0x60, 0xd9, 0xf6, 0x4f, 0x47, 0x08, 0xcd, 0xc3,
	0x49, 0x51, 0xbf, 0x29, 0x53, 0xaf, 0xba, 0x6c, 0xdc, 0x74, 0xf9, 0x00, 0xda, 0x07, 0xea, 0xb5,
	0x14, 0x63, 0x26, 0x27, 0x46, 0xb6, 0x95, 0x2c, 0x89, 0xbd, 0xdd, 0xfa, 0x59, 0xe1, 0x06, 0xb4,
	0x5e, 0xf0, 0x8c, 0x4b, 0xa6, 0xf9, 0xb6, 0xd3, 0x7f, 0x36, 0x9d, 0x51, 0xe7, 0x7c, 0x46, 0x9d,
	0x8b, 0x19, 0x25, 0x5f, 0x4b, 0x4a, 0x7e, 0x94, 0x94, 0xfc, 0x2c, 0x29, 0x99, 0x96, 0x94, 0xfc,
	0x2e, 0x29, 0xf9, 0x53, 0x52, 0xe7, 0xa2, 0xa4, 0xe4, 0xfb, 0x9c, 0x3a, 0xd3, 0x39, 0x75, 0xce,
	0xe7, 0xd4, 0x49, 0x5d, 0xf3, 0x65, 0x9e, 0xfe, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x67, 0x69, 0x47,
	0x6f, 0x41, 0x03, 0x00, 0x00,
}

func (x Action) String() string {
	s, ok := Action_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *SqlAssistantReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SqlAssistantReq)
	if !ok {
		that2, ok := that.(SqlAssistantReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Query != that1.Query {
		return false
	}
	if this.Database != that1.Database {
		return false
	}
	if len(this.Tables) != len(that1.Tables) {
		return false
	}
	for i := range this.Tables {
		if this.Tables[i] != that1.Tables[i] {
			return false
		}
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.Action != that1.Action {
		return false
	}
	if this.WithExplanation != that1.WithExplanation {
		return false
	}
	return true
}
func (this *SqlAssistantResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SqlAssistantResp)
	if !ok {
		that2, ok := that.(SqlAssistantResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Answer != that1.Answer {
		return false
	}
	return true
}
func (this *DescribeDBTablesReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDBTablesReq)
	if !ok {
		that2, ok := that.(DescribeDBTablesReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Database != that1.Database {
		return false
	}
	if len(this.Tables) != len(that1.Tables) {
		return false
	}
	for i := range this.Tables {
		if this.Tables[i] != that1.Tables[i] {
			return false
		}
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *DescribeDBTablesResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDBTablesResp)
	if !ok {
		that2, ok := that.(DescribeDBTablesResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.DBID != that1.DBID {
		return false
	}
	if len(this.Tables) != len(that1.Tables) {
		return false
	}
	for i := range this.Tables {
		if !this.Tables[i].Equal(that1.Tables[i]) {
			return false
		}
	}
	return true
}
func (this *DBMetadata) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DBMetadata)
	if !ok {
		that2, ok := that.(DBMetadata)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *TableMeta) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TableMeta)
	if !ok {
		that2, ok := that.(TableMeta)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Description != that1.Description {
		return false
	}
	if len(this.Columns) != len(that1.Columns) {
		return false
	}
	for i := range this.Columns {
		if !this.Columns[i].Equal(that1.Columns[i]) {
			return false
		}
	}
	if this.Definition != that1.Definition {
		return false
	}
	return true
}
func (this *ColumnMeta) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ColumnMeta)
	if !ok {
		that2, ok := that.(ColumnMeta)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.Description != that1.Description {
		return false
	}
	if this.IsPrimary != that1.IsPrimary {
		return false
	}
	return true
}
func (this *SqlAssistantReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 10)
	s = append(s, "&shared.SqlAssistantReq{")
	s = append(s, "Query: "+fmt.Sprintf("%#v", this.Query)+",\n")
	s = append(s, "Database: "+fmt.Sprintf("%#v", this.Database)+",\n")
	s = append(s, "Tables: "+fmt.Sprintf("%#v", this.Tables)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "Action: "+fmt.Sprintf("%#v", this.Action)+",\n")
	s = append(s, "WithExplanation: "+fmt.Sprintf("%#v", this.WithExplanation)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SqlAssistantResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.SqlAssistantResp{")
	s = append(s, "Answer: "+fmt.Sprintf("%#v", this.Answer)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDBTablesReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.DescribeDBTablesReq{")
	s = append(s, "Database: "+fmt.Sprintf("%#v", this.Database)+",\n")
	s = append(s, "Tables: "+fmt.Sprintf("%#v", this.Tables)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDBTablesResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.DescribeDBTablesResp{")
	s = append(s, "DBID: "+fmt.Sprintf("%#v", this.DBID)+",\n")
	if this.Tables != nil {
		s = append(s, "Tables: "+fmt.Sprintf("%#v", this.Tables)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DBMetadata) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.DBMetadata{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TableMeta) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.TableMeta{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Description: "+fmt.Sprintf("%#v", this.Description)+",\n")
	if this.Columns != nil {
		s = append(s, "Columns: "+fmt.Sprintf("%#v", this.Columns)+",\n")
	}
	s = append(s, "Definition: "+fmt.Sprintf("%#v", this.Definition)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ColumnMeta) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.ColumnMeta{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "Description: "+fmt.Sprintf("%#v", this.Description)+",\n")
	s = append(s, "IsPrimary: "+fmt.Sprintf("%#v", this.IsPrimary)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringDbgpt(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *SqlAssistantReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SqlAssistantReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SqlAssistantReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WithExplanation {
		i--
		if m.WithExplanation {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.Action != 0 {
		i = encodeVarintDbgpt(dAtA, i, uint64(m.Action))
		i--
		dAtA[i] = 0x28
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Tables) > 0 {
		for iNdEx := len(m.Tables) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tables[iNdEx])
			copy(dAtA[i:], m.Tables[iNdEx])
			i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Tables[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Database) > 0 {
		i -= len(m.Database)
		copy(dAtA[i:], m.Database)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Database)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SqlAssistantResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SqlAssistantResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SqlAssistantResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Answer) > 0 {
		i -= len(m.Answer)
		copy(dAtA[i:], m.Answer)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Answer)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeDBTablesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDBTablesReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDBTablesReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Tables) > 0 {
		for iNdEx := len(m.Tables) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tables[iNdEx])
			copy(dAtA[i:], m.Tables[iNdEx])
			i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Tables[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Database) > 0 {
		i -= len(m.Database)
		copy(dAtA[i:], m.Database)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Database)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeDBTablesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDBTablesResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDBTablesResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Tables) > 0 {
		for iNdEx := len(m.Tables) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tables[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbgpt(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.DBID) > 0 {
		i -= len(m.DBID)
		copy(dAtA[i:], m.DBID)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.DBID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DBMetadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DBMetadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DBMetadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TableMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Definition) > 0 {
		i -= len(m.Definition)
		copy(dAtA[i:], m.Definition)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Definition)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Columns) > 0 {
		for iNdEx := len(m.Columns) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Columns[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbgpt(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Description) > 0 {
		i -= len(m.Description)
		copy(dAtA[i:], m.Description)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Description)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ColumnMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ColumnMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ColumnMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsPrimary {
		i--
		if m.IsPrimary {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.Description) > 0 {
		i -= len(m.Description)
		copy(dAtA[i:], m.Description)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Description)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDbgpt(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintDbgpt(dAtA []byte, offset int, v uint64) int {
	offset -= sovDbgpt(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SqlAssistantReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	l = len(m.Database)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if len(m.Tables) > 0 {
		for _, s := range m.Tables {
			l = len(s)
			n += 1 + l + sovDbgpt(uint64(l))
		}
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if m.Action != 0 {
		n += 1 + sovDbgpt(uint64(m.Action))
	}
	if m.WithExplanation {
		n += 2
	}
	return n
}

func (m *SqlAssistantResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Answer)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	return n
}

func (m *DescribeDBTablesReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Database)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if len(m.Tables) > 0 {
		for _, s := range m.Tables {
			l = len(s)
			n += 1 + l + sovDbgpt(uint64(l))
		}
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	return n
}

func (m *DescribeDBTablesResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DBID)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if len(m.Tables) > 0 {
		for _, e := range m.Tables {
			l = e.Size()
			n += 1 + l + sovDbgpt(uint64(l))
		}
	}
	return n
}

func (m *DBMetadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TableMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	l = len(m.Description)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if len(m.Columns) > 0 {
		for _, e := range m.Columns {
			l = e.Size()
			n += 1 + l + sovDbgpt(uint64(l))
		}
	}
	l = len(m.Definition)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	return n
}

func (m *ColumnMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	l = len(m.Description)
	if l > 0 {
		n += 1 + l + sovDbgpt(uint64(l))
	}
	if m.IsPrimary {
		n += 2
	}
	return n
}

func sovDbgpt(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDbgpt(x uint64) (n int) {
	return sovDbgpt(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *SqlAssistantReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SqlAssistantReq{`,
		`Query:` + fmt.Sprintf("%v", this.Query) + `,`,
		`Database:` + fmt.Sprintf("%v", this.Database) + `,`,
		`Tables:` + fmt.Sprintf("%v", this.Tables) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`Action:` + fmt.Sprintf("%v", this.Action) + `,`,
		`WithExplanation:` + fmt.Sprintf("%v", this.WithExplanation) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SqlAssistantResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SqlAssistantResp{`,
		`Answer:` + fmt.Sprintf("%v", this.Answer) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDBTablesReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeDBTablesReq{`,
		`Database:` + fmt.Sprintf("%v", this.Database) + `,`,
		`Tables:` + fmt.Sprintf("%v", this.Tables) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDBTablesResp) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTables := "[]*TableMeta{"
	for _, f := range this.Tables {
		repeatedStringForTables += strings.Replace(f.String(), "TableMeta", "TableMeta", 1) + ","
	}
	repeatedStringForTables += "}"
	s := strings.Join([]string{`&DescribeDBTablesResp{`,
		`DBID:` + fmt.Sprintf("%v", this.DBID) + `,`,
		`Tables:` + repeatedStringForTables + `,`,
		`}`,
	}, "")
	return s
}
func (this *DBMetadata) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DBMetadata{`,
		`}`,
	}, "")
	return s
}
func (this *TableMeta) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForColumns := "[]*ColumnMeta{"
	for _, f := range this.Columns {
		repeatedStringForColumns += strings.Replace(f.String(), "ColumnMeta", "ColumnMeta", 1) + ","
	}
	repeatedStringForColumns += "}"
	s := strings.Join([]string{`&TableMeta{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`Columns:` + repeatedStringForColumns + `,`,
		`Definition:` + fmt.Sprintf("%v", this.Definition) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ColumnMeta) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ColumnMeta{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`IsPrimary:` + fmt.Sprintf("%v", this.IsPrimary) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringDbgpt(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *SqlAssistantReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SqlAssistantReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SqlAssistantReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Database", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Database = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tables", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tables = append(m.Tables, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Action", wireType)
			}
			m.Action = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Action |= Action(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WithExplanation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithExplanation = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SqlAssistantResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SqlAssistantResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SqlAssistantResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Answer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Answer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDBTablesReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDBTablesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDBTablesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Database", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Database = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tables", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tables = append(m.Tables, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDBTablesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDBTablesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDBTablesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DBID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DBID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tables", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tables = append(m.Tables, &TableMeta{})
			if err := m.Tables[len(m.Tables)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DBMetadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DBMetadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DBMetadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Columns", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Columns = append(m.Columns, &ColumnMeta{})
			if err := m.Columns[len(m.Columns)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Definition", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Definition = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ColumnMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ColumnMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ColumnMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDbgpt
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDbgpt
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsPrimary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPrimary = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipDbgpt(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbgpt
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDbgpt(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDbgpt
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDbgpt
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDbgpt
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDbgpt
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDbgpt
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDbgpt        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDbgpt          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDbgpt = fmt.Errorf("proto: unexpected end of group")
)
